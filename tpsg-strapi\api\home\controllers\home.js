"use strict";

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */

process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0"

const https = require("https");

const wp_url = "https://toutpoursagloire.com/wp-json/etos/get-slugs"

module.exports = {
  async compare(ctx) {

    let strapiPosts = await strapi.query("post").find({_limit: -1});
    const strapiSlugs = strapiPosts.map(post => post.slug);

    // const response = await fetch( wp_url, { agent: agent });
    const response = await fetch(wp_url);
    if (response.ok) {
      const wpSlugs = await response.json();
      const wpLost = wpSlugs.filter(slug => !strapiSlugs.includes(slug));
      const strapiLost = strapiSlugs.filter(slug => !wpSlugs.includes(slug));
      return {
        "nb_posts_strapi": strapiSlugs.length,
        "nb_posts_wp": wpSlugs.length,
        "uniquement_wp": wpLost,
        "uniquement_strapi": strapiLost
      };
    } else {
      return "Check les logs, y'a eu un soucis";
    }
  }
};
