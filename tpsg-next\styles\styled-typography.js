import styled from "styled-components"
import { device } from "styles/device";

const fontRatios = {
  realSize: 0.6346,
  containerSize: 1.202,
  topSpace: 0.096,
  minBottomSpace: 0.2788,
  maxBottomSpace: 0.4711,
  baseLine: 0.731,
}


/**
 * Titre des principales pages du site
 */
const PageTitle = styled.h1`
  position: relative;
  font-weight: 500;
  color: #161616;
  font-size: 48px;
  margin-top:  calc(var(--spacing-l) - 48px * ${fontRatios.topSpace});
  margin-bottom: calc(var(--spacing-l) - 48px * ${fontRatios.minBottomSpace});
  
  @media ${device.tablet} {
    font-size: 72px;
    margin-top:  calc(var(--spacing-l) - 72px * ${fontRatios.topSpace});
    margin-bottom: calc(var(--spacing-l) - 72px * ${fontRatios.minBottomSpace});
  }
  @media ${device.desktop} {
    font-size: 104px;
    margin-top:  calc(var(--spacing-l) - 104px * ${fontRatios.topSpace});
    margin-bottom: calc(var(--spacing-l) - 104px * ${fontRatios.minBottomSpace});
  }
`

/**
 * Titre pour le ticket, timbre et diplôme
 */
const DisplayCardTitle = styled.h2`
  box-sizing: content-box;
  color: #ececec;
  margin-top: 0;
  margin-bottom: 0;
  font-size: clamp(24px, 1.125rem + 1vw, 32px);
  font-weight: 400;
  max-width: 600px;
  line-height: 105%;
  &:before {
    display: block;
    margin-bottom: 8px;
    content: '${props => props.label}';
    color: ${props => props.color};
    font-size: 18px;
  }
  @media ${device.tablet} {
    font-size: 32px;
    margin-right: 24px;
  }
  @media ${device.desktop} {
    font-weight: 400;
    margin-right: 48px;
    font-size: clamp(32px, 3.5vw, 48px);
  }
`;


/**
 * Titre des sections (principalement utilisé sur la page d'accueil)
 */
const SectionTitle = styled.h2`
  
  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);
  
  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;
  font-size: var(--title-size);
  color: ${props => props.light ? "var(--c-soft-cream)" : "var(--soft-dark)"};
  font-weight: 400;
  margin-top: 0;
  margin-bottom: calc( var(--title-size) * -${fontRatios.maxBottomSpace});
  
`;


/**
 * À partir d'ici on ne sait plus vraiment
 *
 *
 *
 *
 *
 *
 *
 */
const PostLittleTitle = styled.h2`
  font-size: 24px;
  font-weight: 500;
  line-height: 110%;
  margin-top: 0;
  margin-bottom: 16px;
  @media ${device.tablet} {
    font-size: 48px;
    margin-bottom: 16px;
    margin-top: 16px;
  }
`;

/**
 * PostCard
 */
const PostCardTitle = styled.h4`
  font-size: 20px;
  margin-top: 0;
  margin-bottom: 10px;
  @media ${device.tablet} {
    font-size: 30px;
  }
  span {
    position: relative;
    margin-left: 8px;
    display: inline-block;
    margin-bottom: -2px;
    height: 22px;
    width: 22px;
  }
`;
const PostCardDescription = styled.p`
  font-size: 16px;
  color: #888888;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 30px;
  margin-top: 0;
  margin-bottom: 10px;
  @media screen and (max-width:320px){ // Little screen only
    padding: 0;
  }
  @media ${device.tablet} {
    font-size:20px;
  }
`;
const PostCardDetails = styled.p`
  font-size: 14px;
  font-style: italic;
  font-family: "Lora", Charter, Times, "Times New Roman", serif;
  color: #7a7a7a;
  margin-top: 0;
  margin-bottom: 10px;
  @media ${device.tablet} {
    font-size: 17px;
  }
`;

/**
 * PostPage
 */
const PostTitle = styled.h1`
  font-size: 38px;
  font-weight: 500;
  line-height: 110%;
  margin-top: 0;
  margin-bottom: 16px;
  @media ${device.tablet} {
    font-size: 64px;
    margin-bottom: 24px;
    margin-top: 24px;
  }
  @media ${device.desktop} {
    margin-bottom: 24px;
    margin-top: 24px;
  }
`;
const PostType = styled.p`
  color: #161616;
  letter-spacing: 0.04em;
  margin-top: 48px;
  text-transform: uppercase;
  @media ${device.desktop} {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 22px;
    font-weight: 500;
    &:hover {
      color: var(--brand-color);
    }
  }
`;
const PostLead = styled.p`
  font-weight: 400;
  font-size: 26px;
  margin: 0;
  color: #161616;
  @media ${device.tablet} {
    font-size: 32px;
  }
`;

export {
  fontRatios,
  PageTitle,
  DisplayCardTitle,
  SectionTitle,

  PostCardTitle,
  PostCardDescription,
  PostCardDetails,

  PostLittleTitle,

  PostTitle,
  PostLead,
  PostType,
}
