"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[523],{4523:function(n,t,o){o.r(t),o.d(t,{default:function(){return CKForm}});var r=o(2729),i=o(5893),e=o(9521);function _templateObject(){let n=(0,r._)(["\n  position: relative;\n  width: 100%;\n  .form-title {\n    font-size: 42px;\n    margin-top: 0;\n    margin-bottom: 16px;\n  }\n  .form-desc {\n    margin-top: 24px;\n    margin-bottom: 24px;\n    font-weight: 400;\n    color: #161616;\n    font-size: 22px;\n  }\n  .formkit-input {\n    margin-bottom: 16px;\n    background-color: #F0F0F0 !important;\n    color: #161616 !important;\n    border-radius: 0 !important;\n    border: none !important;\n    width: 100% !important;\n    height: 52px;\n    font-size: 18px !important;\n    padding-left: 20px !important;\n    &::placeholder {\n      color: #888888 !important;\n    }\n  }\n  .formkit-submit {\n    margin-bottom: 16px;\n    color: white !important;\n    border: none !important;\n    border-radius: 0 !important;\n    background-color: #080808 !important;\n    width: 100%;\n    height: 52px;\n    font-size: 18px !important;\n    cursor: pointer;\n    &:hover {\n      background-color: var(--brand-color) !important;\n    }\n  }\n  div {\n    padding: 0 !important;\n  }\n"]);return _templateObject=function(){return n},n}function CKForm(n){let{title:t,desc:o,formString:r}=n;return r?(0,i.jsxs)(a,{children:[t&&(0,i.jsx)("h4",{className:"form-title",children:t}),o&&(0,i.jsx)("p",{className:"form-desc",children:o}),(0,i.jsx)("div",{dangerouslySetInnerHTML:{__html:"".concat(r)}})]}):(0,i.jsx)(i.Fragment,{})}let a=e.ZP.div.withConfig({componentId:"sc-4ad002d4-0"})(_templateObject())}}]);