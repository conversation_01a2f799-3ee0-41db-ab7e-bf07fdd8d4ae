import styled from "styled-components";
import paginate from "utils/paginate.utils";
import PaginationNumber from "./sticky-pagination/PaginationNumber";

export default function SSRPaginate({ nbHits, currentPage, baseUrl, options }) {
  const pagination = paginate(
    nbHits,
    currentPage || 1,
    options.postPerPage || 10,
    options.maxPages || 5
  );

  return (
    <PaginationWrapper className={"no-select"}>
      {pagination.startPage > 1 && (
        <>
          <PaginationNumber
            value={1}
            active={currentPage === 1}
            baseUrl={baseUrl}
          />
          <Dots>...</Dots>
        </>
      )}
      {pagination.pages.map((page, key) => (
        <PaginationNumber
          key={key}
          active={page === pagination.currentPage}
          value={page}
          baseUrl={baseUrl}
        />
      ))}
      {pagination.totalPages !== pagination.endPage && (
        <>
          <Dots>...</Dots>
          <PaginationNumber
            baseUrl={baseUrl}
            active={pagination.currentPage === pagination.endPage}
            value={pagination.totalPages}
          />
        </>
      )}
    </PaginationWrapper>
  );
}

const PaginationWrapper = styled.div`
  position: relative;
  width: 100%;
`;

const Dots = styled.span`
  color: #080808;
`;
