import client from "api/apollo-client";
import styled from "styled-components";
import HorizontalReversePostCard from "components/shared/Card/HorizontalReversePostCard";
import SSRPaginate from "components/shared/pagination/ssr-paginate";
import { gql } from "@apollo/client";
import { device } from "styles/device";
import { FilterTopicsString } from "utils/filterSearchString";
import { modulesAsObj } from "utils/components.utils";
import { useRouter } from "next/router";
import { useEffect } from "react";
import { topicsPostsfetcher } from "utils/fetcher";

const postPerPage = 15; // Number items per page

export default function PageMinistryRessources({ ministry, props, fallback }) {
  const nbHits = fallback?.posts?.totalHits || 0;
  const posts = fallback?.posts?.hits || [];
  let { page } = props;
  const router = useRouter();
  
  useEffect(() => {
    if (+page === 1) {
      router.replace(
        {
          pathname: `/categories/ministere/${ministry.slug}`,
        },
        undefined,
        { scroll: false }
      );
    } else if (+page > Math.ceil(nbHits / postPerPage)) {
      router.replace(
        {
          pathname: `/categories/ministere/${ministry.slug}/ressources`,
          query: { page: Math.ceil(nbHits / postPerPage) }
        },
        undefined,
        { scroll: false }
      );
    }
  },[ministry.slug, nbHits, page, router]);
  
  if (!ministry) return null;

  return (
    <Wrapper className={"site-padding"}>
      <h1>{ministry.name}</h1>
      <SectionPosts>
        <p className="label-type">Dernières ressources</p>
        <div className="posts-container ">
          <LeftContent>
            <ul className={"list-container"}>
              {posts.map((post, key) => {
                return (
                  <HorizontalReversePostCard
                    post={post}
                    key={`post-${key}`}
                    options={{
                      showLead: true,
                      showDate: true,
                      showAuthor: true
                    }}
                  />
                );
              })}
            </ul>
          </LeftContent>
        </div>
      </SectionPosts>
      <SSRPaginate
        nbHits={nbHits}
        baseUrl={`/categories/ministere/${ministry.slug}/ressources?page=`}
        currentPage={page}
        options={{
          postPerPage: postPerPage,
        }}
      />
    </Wrapper>
  );
}

export async function getServerSideProps({ query, params }) {

  let { page } = query;

  const ministry = await client.query({
    query: QUERY_MINISTRY,
    variables: { slug: params.ministry }
  }).then(response => {
    return response.data.topicGroups[0]
  })

  if (!ministry) {
    return {
      notFound: true,
    }
  }

  // Put all topic into an array
  let topics = [
    ...ministry.parent.topics,
    ...ministry.topics
  ];

  // Clear duplicate topics
  topics = topics.filter(
    (topic, index, self) =>
      self.findIndex((topic2) => topic2.id === topic.id) === index
  );

  //fetch posts
  const filterTopicsString = FilterTopicsString(topics);
  if (filterTopicsString.length === 0) {
    return {
      notFound: true,
    }
  }

  let posts = await topicsPostsfetcher({ page: +page }, filterTopicsString, postPerPage);

  return {
    props: {
      ministry,
      props: {
        page: +page
      },
      fallback: {
        posts,
      },
    },
  };

}

const QUERY_MINISTRY = gql`
  query Ministry($slug: String!) {
    topicGroups(where: { slug: $slug }) {
      slug
      name
      topics {
        id
        name
        slug
        postCount
      }
      parent {
        id
        name
        slug
        topics {
          id
          name
          postCount
        }
      }
      children {
        id
        slug
        topics {
          id
          name
          slug
          postCount
        }
      }
    }
  }
`;

const Wrapper = styled.div`
  padding-bottom: 50px;
`;

const SectionPosts = styled.section`
  margin-top: 96px;
  .posts-container {
    display: block;

    @media ${device.desktop} {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
  .label-type{
    font-size: 24px;
  }
`;

const LeftContent = styled.article`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }
`;
