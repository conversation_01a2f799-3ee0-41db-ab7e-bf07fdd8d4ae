import styled from "styled-components";
import { Fragment, memo } from "react";
import { dynamicSort } from "utils/list.utils";

const LetterSeparator = ({ word, prevWord }) => {
  if (word.charAt(0) !== prevWord.charAt(0)) {
    return (
      <li key={`s-${word.charAt(0)}`} className={"letter-separator"}>
        {word.charAt(0)}
      </li>
    );
  } else {
    return null;
  }
};

function ListAuthor({ authors, setFilter }) {
  authors.sort(dynamicSort("fullName"));

  const handleSelect = (topicName) => {
    setFilter("author", topicName);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <Wrapper className={"site-padding"}>
      {authors.map((a, i) => {
        return (
          <Fragment key={i}>
            {i !== 0 && (
              <LetterSeparator
                word={a.fullName}
                prevWord={authors[i - 1].fullName}
              />
            )}
            <li onClick={() => handleSelect(a.fullName)}>
              <span>{a.fullName}</span>
            </li>
          </Fragment>
        );
      })}
    </Wrapper>
  );
}

export default memo(ListAuthor);

const Wrapper = styled.ul`
  position: relative;
  z-index: 400;
  margin-top: 64px;
  width: calc(33.33% + var(--desktop-gap));
  column-width: 260px;

  li {
    margin: 4px 0 4px 0;
    font-size: 19px;
    font-weight: 400;
    line-height: 28px;
    list-style: none;
    color: var(--soft-white);

    span {
      cursor: pointer;
      margin-left: -10px;
      padding: 6px 10px 0 10px;
      transition: padding 300ms, margin-left 300ms;

      &:hover {
        background-color: #ec5119;
        margin-left: -16px;
        color: white;
        padding: 12px 16px 6px 16px;
        border-radius: 25px;
        font-weight: 500;
        z-index: 999;
      }
    }
  }

  .letter-separator {
    color: #ec5119;
    font-weight: 500;
    font-size: 64px;
    margin: 42px 0 24px 0;
    text-transform: uppercase;
  }
`;
