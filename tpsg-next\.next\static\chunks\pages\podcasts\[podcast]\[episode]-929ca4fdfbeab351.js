(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[827],{2279:function(n,t,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/podcasts/[podcast]/[episode]",function(){return e(562)}])},562:function(n,t,e){"use strict";e.r(t),e.d(t,{__N_SSG:function(){return c},default:function(){return PodcastEpisode}});var i=e(2729),o=e(5893),a=e(9521),p=e(7421),r=e(6768);function _templateObject(){let n=(0,i._)(["\n  padding-bottom: 70px;\n\n  header {\n    display: flex;\n    flex-direction: column;\n    margin-bottom: 48px;\n  }\n\n  .header-player-cover {\n    width: 100%;\n    aspect-ratio: 16 / 10;\n  }\n\n  .card-label {\n    display: none;\n    margin: 8px 8px 0 0;\n  }\n\n  .video-player {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n  }\n\n  .header-img-container {\n    position: relative;\n    width: 50%;\n    aspect-ratio: 16 / 10;\n  }\n\n  @media "," {\n    .card-label {\n      display: inline-block;\n    }\n\n    .header-player-cover {\n      margin-top: 48px;\n    }\n  }\n  @media "," {\n    header {\n      margin-top: 90px;\n      margin-bottom: 70px;\n      flex-direction: row;\n      justify-content: space-between;\n    }\n\n    .header-text-container {\n      position: relative;\n      width: 50%;\n      aspect-ratio: 16 / 10;\n      padding-top: 24px;\n      padding-left: 16px;\n    }\n\n    .header-player-cover {\n      margin-top: 0;\n      width: calc(50% - 32px);\n    }\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n\n  .right-content-sticky {\n    position: sticky;\n    top: 50px;\n  }\n\n  .podcast-platform {\n    display: flex;\n    flex-wrap: wrap;\n    margin-top: 64px;\n    width: 100%;\n    gap: 32px;\n  }\n\n  @media "," {\n    .podcast-platform {\n      margin-top: 16px;\n      gap: 16px;\n    }\n  }\n  @media "," {\n    border-left: 1px solid #dddddd;\n    width: 40%;\n    padding-left: 32px;\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  margin-top: 70px;\n  display: block;\n\n  @media "," {\n    display: flex;\n    flex-wrap: wrap;\n    justify-content: space-between;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n\n  @media "," {\n    width: 50%;\n  }\n"]);return _templateObject3=function(){return n},n}var c=!0;function PodcastEpisode(n){let{post:t,relatedPosts:e}=n;return(0,o.jsx)(r.Q4,{episode:t,preview:!1,relatedPosts:e})}a.ZP.div.withConfig({componentId:"sc-5c456b99-0"})(_templateObject(),p.U.tablet,p.U.desktop),a.ZP.div.withConfig({componentId:"sc-5c456b99-1"})(_templateObject1(),p.U.tablet,p.U.desktop),a.ZP.main.withConfig({componentId:"sc-5c456b99-2"})(_templateObject2(),p.U.desktop),a.ZP.article.withConfig({componentId:"sc-5c456b99-3"})(_templateObject3(),p.U.desktop)}},function(n){n.O(0,[755,764,962,676,291,915,211,621,768,774,888,179],function(){return n(n.s=2279)}),_N_E=n.O()}]);