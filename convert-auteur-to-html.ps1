$wordApp = New-Object -ComObject Word.Application
$wordApp.Visible = $false
$doc = $wordApp.Documents.Open("C:\rep\TPSG\faire apparaitre l'auteur des articles mis en avant.docx")
$htmlPath = "C:\rep\TPSG\faire apparaitre l'auteur des articles mis en avant.html"
$doc.SaveAs([ref]$htmlPath, [ref]10) # 10 est le code pour le format HTML
$doc.Close()
$wordApp.Quit()
Write-Host "Document converti en HTML avec succès!"
