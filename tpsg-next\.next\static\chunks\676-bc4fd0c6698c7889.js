(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[676],{4184:function(e,t){var r;/*!
	Copyright (c) 2018 <PERSON>.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/!function(){"use strict";var n={}.hasOwnProperty;function classNames(){for(var e=[],t=0;t<arguments.length;t++){var r=arguments[t];if(r){var o=typeof r;if("string"===o||"number"===o)e.push(r);else if(Array.isArray(r)){if(r.length){var a=classNames.apply(null,r);a&&e.push(a)}}else if("object"===o){if(r.toString!==Object.prototype.toString&&!r.toString.toString().includes("[native code]")){e.push(r.toString());continue}for(var i in r)n.call(r,i)&&r[i]&&e.push(i)}}}return e.join(" ")}e.exports?(classNames.default=classNames,e.exports=classNames):void 0!==(r=(function(){return classNames}).apply(t,[]))&&(e.exports=r)}()},9413:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{noSSR:function(){return noSSR},default:function(){return dynamic}});let n=r(8754),o=(r(7294),n._(r(132)));function convertModule(e){return{default:(null==e?void 0:e.default)||e}}function noSSR(e,t){return delete t.webpack,delete t.modules,e(t)}function dynamic(e,t){let r=o.default,n={loading:e=>{let{error:t,isLoading:r,pastDelay:n}=e;return null}};e instanceof Promise?n.loader=()=>e:"function"==typeof e?n.loader=e:"object"==typeof e&&(n={...n,...e}),n={...n,...t};let a=n.loader;return(n.loadableGenerated&&(n={...n,...n.loadableGenerated},delete n.loadableGenerated),"boolean"!=typeof n.ssr||n.ssr)?r({...n,loader:()=>null!=a?a().then(convertModule):Promise.resolve(convertModule(()=>null))}):(delete n.webpack,delete n.modules,noSSR(r,n))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7240:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"LoadableContext",{enumerable:!0,get:function(){return a}});let n=r(8754),o=n._(r(7294)),a=o.default.createContext(null)},132:function(e,t,r){"use strict";/**
@copyright (c) 2017-present James Kyle <<EMAIL>>
 MIT License
 Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:
 The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE
*/Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(8754),o=n._(r(7294)),a=r(7240),i=[],l=[],s=!1;function load(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then(e=>(r.loading=!1,r.loaded=e,e)).catch(e=>{throw r.loading=!1,r.error=e,e}),r}let LoadableSubscription=class LoadableSubscription{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}};function Loadable(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),n=null;function init(){if(!n){let t=new LoadableSubscription(e,r);n={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return n.promise()}if(!s){let e=r.webpack?r.webpack():r.modules;e&&l.push(t=>{for(let r of e)if(t.includes(r))return init()})}function LoadableComponent(e,t){!function(){init();let e=o.default.useContext(a.LoadableContext);e&&Array.isArray(r.modules)&&r.modules.forEach(t=>{e(t)})}();let i=o.default.useSyncExternalStore(n.subscribe,n.getCurrentValue,n.getCurrentValue);return o.default.useImperativeHandle(t,()=>({retry:n.retry}),[]),o.default.useMemo(()=>{var t;return i.loading||i.error?o.default.createElement(r.loading,{isLoading:i.loading,pastDelay:i.pastDelay,timedOut:i.timedOut,error:i.error,retry:n.retry}):i.loaded?o.default.createElement((t=i.loaded)&&t.default?t.default:t,e):null},[e,i])}return LoadableComponent.preload=()=>init(),LoadableComponent.displayName="LoadableComponent",o.default.forwardRef(LoadableComponent)}(load,e)}function flushInitializers(e,t){let r=[];for(;e.length;){let n=e.pop();r.push(n(t))}return Promise.all(r).then(()=>{if(e.length)return flushInitializers(e,t)})}Loadable.preloadAll=()=>new Promise((e,t)=>{flushInitializers(i).then(e,t)}),Loadable.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let res=()=>(s=!0,t());flushInitializers(l,e).then(res,res)})),window.__NEXT_PRELOADREADY=Loadable.preloadReady;let u=Loadable},5152:function(e,t,r){e.exports=r(9413)},6573:function(e,t,r){"use strict";var n=r(5456),o=r(6459),a=(0,r(9075).Z)("facebook",function(e,t){var r=t.quote,a=t.hashtag;return(0,n.Z)(e,"facebook.url"),"https://www.facebook.com/sharer/sharer.php"+(0,o.Z)({u:e,quote:r,hashtag:a})},function(e){return{quote:e.quote,hashtag:e.hashtag}},{windowWidth:550,windowHeight:400});t.Z=a},6616:function(e,t,r){"use strict";var n=r(5456),o=r(6459),a=(0,r(9075).Z)("twitter",function(e,t){var r=t.title,a=t.via,i=t.hashtags,l=void 0===i?[]:i,s=t.related,u=void 0===s?[]:s;return(0,n.Z)(e,"twitter.url"),(0,n.Z)(Array.isArray(l),"twitter.hashtags is not an array"),(0,n.Z)(Array.isArray(u),"twitter.related is not an array"),"https://twitter.com/share"+(0,o.Z)({url:e,text:r,via:a,hashtags:l.length>0?l.join(","):void 0,related:u.length>0?u.join(","):void 0})},function(e){return{hashtags:e.hashtags,title:e.title,via:e.via,related:e.related}},{windowWidth:550,windowHeight:400});t.Z=a},9075:function(e,t,r){"use strict";r.d(t,{Z:function(){return hocs_createShareButton}});var n,o=r(7294),a=r(4184),i=r.n(a),l=(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}),__assign=function(){return(__assign=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},__generator=function(e,t){var r,n,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function verb(a){return function(l){return function(a){if(r)throw TypeError("Generator is already executing.");for(;i;)try{if(r=1,n&&(o=2&a[0]?n.return:a[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,a[1])).done)return o;switch(n=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,n=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(e){a=[6,e],n=0}finally{r=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,l])}}},__rest=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r},s=function(e){function ShareButton(){var t=null!==e&&e.apply(this,arguments)||this;return t.openShareDialog=function(e){var r=t.props,n=r.onShareWindowClose,o=r.windowHeight,a=void 0===o?400:o,i=r.windowPosition,l=void 0===i?"windowCenter":i,s=r.windowWidth,u=void 0===s?550:s;!function(e,t,r){var n=t.height,o=t.width,a=__rest(t,["height","width"]),i=__assign({height:n,width:o,location:"no",toolbar:"no",status:"no",directories:"no",menubar:"no",scrollbars:"yes",resizable:"no",centerscreen:"yes",chrome:"yes"},a),l=window.open(e,"",Object.keys(i).map(function(e){return"".concat(e,"=").concat(i[e])}).join(", "));if(r)var s=window.setInterval(function(){try{(null===l||l.closed)&&(window.clearInterval(s),r(l))}catch(e){console.error(e)}},1e3)}(e,__assign({height:a,width:u},"windowCenter"===l?{left:window.outerWidth/2+(window.screenX||window.screenLeft||0)-u/2,top:window.outerHeight/2+(window.screenY||window.screenTop||0)-a/2}:{top:(window.screen.height-a)/2,left:(window.screen.width-u)/2}),n)},t.handleClick=function(e){var r,n,o;return r=void 0,n=void 0,o=function(){var t,r,n,o,a,i,l,s,u;return __generator(this,function(c){switch(c.label){case 0:var d;if(r=(t=this.props).beforeOnClick,n=t.disabled,o=t.networkLink,a=t.onClick,i=t.url,l=t.openShareDialogOnClick,s=o(i,t.opts),n)return[2];if(e.preventDefault(),!r||!((d=u=r())&&("object"==typeof d||"function"==typeof d)&&"function"==typeof d.then))return[3,2];return[4,u];case 1:c.sent(),c.label=2;case 2:return l&&this.openShareDialog(s),a&&a(e,s),[2]}})},new(n||(n=Promise))(function(e,a){function fulfilled(e){try{step(o.next(e))}catch(e){a(e)}}function rejected(e){try{step(o.throw(e))}catch(e){a(e)}}function step(t){var r;t.done?e(t.value):((r=t.value)instanceof n?r:new n(function(e){e(r)})).then(fulfilled,rejected)}step((o=o.apply(t,r||[])).next())})},t}return l(ShareButton,e),ShareButton.prototype.render=function(){var e=this.props,t=(e.beforeOnClick,e.children),r=e.className,n=e.disabled,a=e.disabledStyle,l=e.forwardedRef,s=(e.networkLink,e.networkName),u=(e.onShareWindowClose,e.openShareDialogOnClick,e.opts,e.resetButtonStyle),c=e.style,d=(e.url,e.windowHeight,e.windowPosition,e.windowWidth,__rest(e,["beforeOnClick","children","className","disabled","disabledStyle","forwardedRef","networkLink","networkName","onShareWindowClose","openShareDialogOnClick","opts","resetButtonStyle","style","url","windowHeight","windowPosition","windowWidth"])),f=i()("react-share__ShareButton",{"react-share__ShareButton--disabled":!!n,disabled:!!n},r),h=u?__assign(__assign({backgroundColor:"transparent",border:"none",padding:0,font:"inherit",color:"inherit",cursor:"pointer"},c),n&&a):__assign(__assign({},c),n&&a);return o.createElement("button",__assign({},d,{"aria-label":d["aria-label"]||s,className:f,onClick:this.handleClick,ref:l,style:h}),t)},ShareButton.defaultProps={disabledStyle:{opacity:.6},openShareDialogOnClick:!0,resetButtonStyle:!0},ShareButton}(o.Component),createShareButton_assign=function(){return(createShareButton_assign=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},hocs_createShareButton=function(e,t,r,n){function CreatedButton(a,i){var l=r(a),u=createShareButton_assign({},a);return Object.keys(l).forEach(function(e){delete u[e]}),o.createElement(s,createShareButton_assign({},n,u,{forwardedRef:i,networkName:e,networkLink:t,opts:r(a)}))}return CreatedButton.displayName="ShareButton-".concat(e),(0,o.forwardRef)(CreatedButton)}},5456:function(e,t,r){"use strict";r.d(t,{Z:function(){return assert}});var n,o=(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function __(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(__.prototype=t.prototype,new __)}),a=function(e){function AssertionError(t){var r=e.call(this,t)||this;return r.name="AssertionError",r}return o(AssertionError,e),AssertionError}(Error);function assert(e,t){if(!e)throw new a(t)}},6459:function(e,t,r){"use strict";function objectToGetParams(e){var t=Object.entries(e).filter(function(e){return null!=e[1]}).map(function(e){var t=e[0],r=e[1];return"".concat(encodeURIComponent(t),"=").concat(encodeURIComponent(String(r)))});return t.length>0?"?".concat(t.join("&")):""}r.d(t,{Z:function(){return objectToGetParams}})}}]);