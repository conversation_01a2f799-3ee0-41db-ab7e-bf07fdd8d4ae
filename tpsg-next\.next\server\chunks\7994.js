"use strict";exports.id=7994,exports.ids=[7994],exports.modules={7994:(e,i,n)=>{n.d(i,{Z:()=>BlogMenu});var t=n(997),r=n(635),o=n(1664),l=n.n(o),a=n(7518),s=n.n(a),p=n(6689),d=n(7467),c=n(2558);function RenderGroup({group:e,blogPath:i}){return(0,t.jsxs)(t.Fragment,{children:[t.jsx(b,{children:e.name}),t.jsx("ul",{children:e.items.map((e,n)=>t.jsx(RenderLink,{link:e,blogPath:i},n))})]})}function RenderLink({link:e,blogPath:i}){switch(e.type){case"external":return t.jsx("li",{children:t.jsx("a",{href:e.value,target:"_blank",rel:"noreferrer noopener",children:e.label})});case"internal":return t.jsx("li",{children:t.jsx(l(),{href:e.value,children:e.label})});case"filter":return t.jsx("li",{children:t.jsx(l(),{href:{pathname:`/blog/${i}/filtres`,query:e.value},children:e.label})});default:return t.jsx(t.Fragment,{})}}function BlogMenu({data:e}){let i=(0,r.DG)(e.menu),n=e.slug,[o,a]=(0,p.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[t.jsx(g,{isOpen:o,className:"blog-menu",children:(0,t.jsxs)(x,{isOpen:o,children:[(0,t.jsxs)(f,{children:[t.jsx("div",{className:"menu-blogger-picture",children:t.jsx(c.Z,{imageData:e.blogger.picture})}),(0,t.jsxs)("div",{children:[t.jsx(l(),{href:`/blog/${n}`,children:t.jsx("p",{className:"menu-blogger-name primary-hover",children:e.blogger.fullName})}),t.jsx("p",{className:"menu-blogger-label",children:"Blog"})]})]}),(0,t.jsxs)(h,{children:[i?.groups&&i.groups.map((e,i)=>t.jsx(RenderGroup,{group:e,blogPath:n},i)),i?.singles.length>0&&i.singles.map((e,i)=>t.jsx(RenderLink,{link:e,blogPath:n},i))]})]})}),t.jsx(m,{isOpen:o,onClick:()=>a(!o),children:t.jsx("p",{children:"→"})})]})}let g=s().div.withConfig({componentId:"sc-f4ce5f7f-0"})`
  position: fixed;
  bottom: -70px;
  left: -70px;
  height: calc(70vh + 140px);
  width: calc(100vw + 140px);
  padding: 48px 70px;
  background-color: rgba(${e=>e.isOpen?"236, 236, 236, 0.8":"236, 236, 236, 0.5"});
  backdrop-filter: blur(${e=>e.isOpen?"25px":"15px"});
  transform: translate3d(${e=>e.isOpen?"0,0,0":"-100vw,70vh,0"});
  border-top-right-radius: 70px;
  transition: all 450ms cubic-bezier(0.58, 0, 0.29, 0.91);
  z-index: 1800;

  @media ${d.U.desktop} {
    grid-row: 1/6;
    background-color: transparent;
    position: sticky;
    display: block;
    backdrop-filter: inherit;
    border-top-right-radius: 0;
    transform: none;
    top: 46px;
    left: 0;
    height: 100vh;
    width: 240px;
    padding: 40px 0 0 0;
    z-index: 50;
  }
`,f=s().div.withConfig({componentId:"sc-f4ce5f7f-1"})`
  display: flex;
  flex-direction: row;
  margin-bottom: 42px;
  .menu-blogger-picture {
    position: relative;
    width: 55px;
    height: 55px;
    border-radius: 50px;
    overflow: hidden;
  }
  .menu-blogger-name {
    margin: 4px 0 0 16px;
    font-family: "Stelvio", sans-serif;
    font-size: 24px;
    line-height: 105%;
    color: #161616;
    font-weight: 600;
  }
  .menu-blogger-label {
    margin: 4px 0 0 16px;
    font-family: "Novela", serif;
    font-style: italic;
    font-size: 20px;
    color: #888888;
    font-weight: 400;
  }
  
  @media ${d.U.desktop} {
    flex-direction: column;
    width: 50%;
    .menu-blogger-name {
      margin: 16px 0 0 0;
      font-family: "Stelvio", sans-serif;
      font-size: 32px;
      line-height: 105%;
      color: #161616;
      font-weight: 600;
    }
    .menu-blogger-label {
      margin: 4px 0 0 0;
    }
  }
`,x=s().div.withConfig({componentId:"sc-f4ce5f7f-2"})`
  
  padding-left: var(--mobile-gap);
  padding-right: var(--mobile-gap);
  
  @media ${d.U.tablet} {
    padding-left: var(--tablet-gap);
    padding-right: var(tablet-gap);
  }
  @media ${d.U.desktop} {
    transform: none;
    padding-left: 0;
    padding-right: 0;
    top: 0;
    left: 0;
  }
`,h=s().div.withConfig({componentId:"sc-f4ce5f7f-3"})`
  ul {
    display: table;
    padding: 0;
  }
  li {
    font-weight: 400;
    position: relative;
    font-family: "Stelvio", sans-serif;
    font-size: 20px;
    margin-top: 8px;
    list-style: none;
  }
  //transform: translate3d(${e=>e.isOpen?"0,0,0":"0, -100px, 0"});
  transition: all 800ms cubic-bezier(0.58, 0, 0.29, 0.91);

  @media ${d.U.desktop} {
    a {
      position: relative;
      height: 32px;
      display: table-row;
      line-height: 24px;
      border-radius: 32px;
      z-index: 900;
      color: black;

      &:hover {
        color: white;

        &:after {
          content: "";
          background-color: black;
          position: absolute;
          height: 100%;
          width: calc(100% + 24px);
          left: -12px;
          top: -7px;
          border-radius: 32px;
          z-index: -1;
        }
      }
    }
  }
`,m=s().div.withConfig({componentId:"sc-f4ce5f7f-4"})`
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  height: 70px;
  width: 70px;
  color: black;
  border-radius: 70px;
  p {
    padding-top: 12px;
    margin: 16px 16px 0 0;
    font-size: 32px;
    transform-origin: center;
    transform: rotate(${e=>e.isOpen?"-225deg":"-45deg"});
    transition: all 450ms cubic-bezier(0.58, -0.42, 0.29, 0.91);
  }
  z-index: 2000;
  @media ${d.U.desktop} {
    display: none;
  }
`,b=s().label.withConfig({componentId:"sc-f4ce5f7f-5"})`
  font-weight: 500;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: #888888;
`}};