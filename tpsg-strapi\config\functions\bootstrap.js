'use strict';

/**
 * An asynchronous bootstrap function that runs before
 * your application gets started.
 *
 * This gives you an opportunity to set up your data model,
 * run jobs, or perform some special logic.
 *
 * See more details here: https://strapi.io/documentation/developer-docs/latest/setup-deployment-guides/configurations.html#bootstrap
 */

module.exports = async () => {
  // Initialiser Meilisearch
  try {
    // Vérifier si l'index existe déjà
    const fetch = require('node-fetch');
    const meiliUrl = process.env.MEILI_HOST || 'http://127.0.0.1:7700';

    try {
      const response = await fetch(`${meiliUrl}/indexes`);
      const indexes = await response.json();

      const postIndexExists = indexes.results && indexes.results.some(index => index.uid === 'post');

      if (!postIndexExists) {
        console.log('Index "post" not found, creating it...');
        // Créer l'index sans utiliser la fonction createIndex qui cause l'erreur
        await fetch(`${meiliUrl}/indexes`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ uid: 'post' })
        });

        console.log('Index "post" created successfully');

        // Configurer les attributs filtrables
        await fetch(`${meiliUrl}/indexes/post/settings/filterable-attributes`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify([
            'title', 'topics', 'tags', 'author', 'type', 'blog', 'cs'
          ])
        });

        // Configurer les attributs triables
        await fetch(`${meiliUrl}/indexes/post/settings/sortable-attributes`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(['date'])
        });

        // Configurer les attributs de recherche
        await fetch(`${meiliUrl}/indexes/post/settings/searchable-attributes`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(['title', 'body', 'lead'])
        });
      } else {
        console.log('Index "post" already exists');
      }
    } catch (fetchError) {
      console.error('Error checking Meilisearch indexes:', fetchError);
    }

    console.log('Meilisearch initialization completed');
  } catch (error) {
    console.error('Error initializing Meilisearch:', error);
  }
};
