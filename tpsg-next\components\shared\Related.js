import styled from "styled-components";
import CondImage from "components/shared/condimage";
import { modulesAsObj } from "utils/components.utils";
import Link from "next/link";
import { device } from "styles/device";
import { getPostRoute, getPostSpeakers } from "utils/posts.utils";
import LargeRelatedCard from "./Card/LargeRelatedCard";
import VerticalCard from "./Card/VerticalCard";

const smallSection = ["Default", "vocation", "ministry"]

const mapSection = {
  "Default": "Thèmes",
  "vocation": "Vocation",
  "ministry": "Ministère",
}

const verticalCardOptions = {
  theme: "dark",
  showType: false
}

function RenderVerticalCard({ relatedItem, baseTopicNames }) {

  let labelText;

  if(baseTopicNames) {
    labelText = relatedItem.section === "Default" ?
      relatedItem.post.topics
        .filter(x => baseTopicNames.includes(x.name))
        .map(x => x.name)
        .splice(0, 2)
        .join(", ")
      :
      relatedItem.origin
  }

  const label = {
    text: labelText,
    link: ""
  }

  return(
    <VerticalCard
      post={relatedItem.post}
      options={verticalCardOptions}
      label={label}/>
  )
}

export default function Related({ items, baseTopicNames }) {

  if (!items) return null;

  const webinar = items.filter((x) => x.section === "webinar")[0];
  const smalls = items.filter((x) => smallSection.includes(x.section))
  // const formation = items.filter((x) => x.section === "formation")[0];

  return (
    <SectionWrapper>
      <SectionTitle>Ressources similaires</SectionTitle>
      { webinar && <LargeRelatedCard post={webinar.post}/> }
      <div className={"related-posts"}>
        { smalls && smalls.map((item, key) =>
          <RenderVerticalCard
            key={key}
            relatedItem={item}
            baseTopicNames={baseTopicNames}
          />
        )}
      </div>
    </SectionWrapper>
  )
}


const SectionWrapper = styled.section`
  position: relative;
  background-color: var(--c-dark-green);
  padding: 72px var(--border-space) 128px var(--border-space);
  
  .related-posts {
    margin-top: 126px;
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 24px;
  }
  
  @media ${device.tablet} {
    .related-posts {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  @media ${device.desktop} {
    .related-posts {
      grid-template-columns: repeat(4, 1fr);
    }
  }
`;

const SectionTitle = styled.h2`
  color: var(--c-soft-cream);
  font-family: Stelvio, sans-serif;
  font-size: 40px;
  font-weight: 500;
  line-height: 32px;
  width: 70%;
  margin-bottom: 40px;
  
  @media ${device.tablet} {
    width: 100%;
    white-space: nowrap;
  }
  @media ${device.desktop} {
    font-size: 40px;
  }
`;