const MODULE_NAMES = {
  "lead": "ComponentModuleLead",
  "webinar": "ComponentModuleWebinar",
  "podcast": "ComponentModulePodcast",
  "journey": "ComponentModuleEmailJourney",
  "formation": "ComponentModuleFormation",
  "seo": "ComponentModuleSeo"
}

function getModuleWithShortName(modules, shortName) {
  return modules.find(function (module) {
    return module.__typename === MODULE_NAMES[shortName]
  })
}


/**
 * Conversion d'un tableau de modules en objet.
 * Cette fonction est utilisée pour simplifier l'accès aux différents modules
 * associés aux posts / pages.
 * @param modules
 * @returns {{}}
 */
function modulesAsObj(modules) {

  if (!modules) return null;

  let modulesObj = {}
  modules.forEach(module => {
    switch (module.__typename) {
    case "ComponentModuleLead":
      modulesObj.lead = module;
      break;
    case "ComponentModuleWebinar":
      modulesObj.webinar = module;
      break;
    case "ComponentModuleEmailJourney":
      modulesObj.journey = module;
      break;
    case "ComponentModuleEvent":
      modulesObj.event = module;
      break;
    case "ComponentModuleFormation":
      modulesObj.formation = module;
      break;
    case "ComponentModuleSeo":
      modulesObj.seo = module;
      break;
    case "ComponentModulePodcast":
      modulesObj.podcast = module;
      break;
    }
  })
  return modulesObj;
}

function menuAsObj(menu) {
  if (!menu.length) return null
  let menuObj = {
    groups: [],
    singles: []
  }
  menu.forEach(item => {
    if (item.label.includes("/")) {
      let groupName = item.label.split("/")[0];
      if (!menuObj.groups.some(groupItem => groupItem.name === groupName)) {
        menuObj.groups.push({
          name: groupName,
          items: []
        })
      }
      for (const obj of menuObj.groups) {
        if (obj.name === groupName) {
          obj.items.push({
            label: item.label.split("/")[1],
            value: item.value,
            type: item.type
          });
          break;
        }
      }
    } else {
      menuObj.singles.push(item)
    }
  })
  return menuObj
}


/**
 *
 * @param post
 * @param {string} type doit être 'podcast' ou 'webinar'
 * @return {*|null}
 */
function getChannelSlug(post, type) {
  let { webinar } = getModuleWithShortName(post.modules, type);
  return webinar?.slug || null;
}

export {
  getChannelSlug,
  getModuleWithShortName,
  modulesAsObj,
  menuAsObj
}
