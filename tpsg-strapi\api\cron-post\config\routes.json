{"routes": [{"method": "GET", "path": "/cron-posts", "handler": "cron-post.find", "config": {"policies": []}}, {"method": "GET", "path": "/cron-posts/init", "handler": "cron-post.init", "config": {"policies": []}}, {"method": "GET", "path": "/cron-posts/count", "handler": "cron-post.count", "config": {"policies": []}}, {"method": "GET", "path": "/cron-posts/:id", "handler": "cron-post.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/cron-posts", "handler": "cron-post.create", "config": {"policies": []}}, {"method": "PUT", "path": "/cron-posts/:id", "handler": "cron-post.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/cron-posts/:id", "handler": "cron-post.delete", "config": {"policies": []}}]}