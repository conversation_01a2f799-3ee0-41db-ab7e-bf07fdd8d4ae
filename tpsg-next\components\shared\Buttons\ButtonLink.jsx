import Link from "next/link";
import styled from "styled-components";
import { isLinkExternal } from "utils/string.utils";
import styles from "./ButtonLink.module.css"

export default function ButtonLink({ text, url, type }) {

  if (isLinkExternal(url)) {
    return (
      <Button
        href={url}
        className={`button-link ${styles[type]}`}
        target="_blank"
        rel="noopener noreferrer">
        {text}
      </Button>
    )
  }
  return (
    <Link href={url}>
      <Button className={`button-link ${styles[type]}`}>{text}</Button>
    </Link>
  )
}

const Button = styled.a`
  margin: 0 16px 0 0;
  padding: 12px 18px 6px 18px;
  font-family: Stelvio,sans-serif;
  font-size: 18px;
  white-space: nowrap;
  cursor: pointer;
  
  &:hover {
    opacity: 0.72;
  }
`
