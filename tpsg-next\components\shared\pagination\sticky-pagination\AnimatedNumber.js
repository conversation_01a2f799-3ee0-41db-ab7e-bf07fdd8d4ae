import styled from "styled-components";
import { device } from "styles/device";

//TODO Reduce list size
export default function AnimatedNumber({ activePage, totalPage, gapSize = "100px", gapSizeMobile = "50px" }) {
  const pages = [...Array(totalPage).keys()]
  return (
    <Wrapper
      gapSize={gapSize} gapSizeMobile={gapSizeMobile}
    >
      <Numbers
        gap={-( activePage - 1 ) * gapSize.replace(/\D+/g, "")} gapSize={gapSize}
        gapMobile={-( activePage - 1 ) * gapSizeMobile.replace(/\D+/g, "")} gapSizeMobile={gapSizeMobile}
      >
        {pages.map((page) => ( <div className={"number-item"} key={page}>
          <p>{page + 1}</p>
        </div> ))}
      </Numbers>
    </Wrapper>
  )
}

const Wrapper = styled.div`
   overflow: hidden;
   width: ${props => 80 * props.gapSizeMobile / 100};
   @media ${device.tablet} {
      width: ${props => 80 * props.gapSize / 100};
   }
`

const Numbers = styled.div`
   align-items: center;
   display: flex;
   flex-direction: column;
   height: ${props => props.gapSizeMobile};
   p {
      padding-top: 3px;
      font-weight: 700;
      margin: 0;
      width: ${props => props.gapSizeMobile};
      height: ${props => props.gapSizeMobile};
   
      color: var(--soft-dark);
      font-size: ${props => props.gapSizeMobile};
   }
   
   transition: 1000ms cubic-bezier(1, 0.72, 0.15, 1.01);
   transform: translateY(${props => props.gapMobile}px);


   @media ${device.tablet} {
      height: ${props => props.gapSize};
      transform: translateY(${props => props.gap}px);
      p {
         width: ${props => props.gapSize};
         height: ${props => props.gapSize};
         font-size: ${props => props.gapSize};
      }
   }
`
