{"routes": [{"method": "GET", "path": "/topic-groups", "handler": "topic-group.find", "config": {"policies": []}}, {"method": "GET", "path": "/topic-groups/init", "handler": "topic-group.init", "config": {"policies": []}}, {"method": "GET", "path": "/topic-groups/count", "handler": "topic-group.count", "config": {"policies": []}}, {"method": "GET", "path": "/topic-groups/:id", "handler": "topic-group.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/topic-groups", "handler": "topic-group.create", "config": {"policies": []}}, {"method": "PUT", "path": "/topic-groups/:id", "handler": "topic-group.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/topic-groups/:id", "handler": "topic-group.delete", "config": {"policies": []}}]}