import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import styled from "styled-components";
import { topicSort } from "/utils/list.utils";
import { PageTitle } from "styles/styled-typography";
import { device } from "styles/device";

import SectionVocations from "components/shared/categories/SectionVocations";
import MainList from "components/categories/MainList";
import BigCta from "../../components/shared/atoms/Buttons/BigCta";
import { NextSeo } from "next-seo";


export default function Categories(props) {

  let {
    groups,
    topics
  } = props

  topics = topicSort(topics);

  return (
    <>
      <NextSeo
        title={"TPSG - Thèmes"}
        description={""}
      />
      <div className={"site-padding"}>
        <header>
          <PageTitle>Thèmes</PageTitle>
        </header>
        <VocationsWrapper>
          <div className={"list"}>
            <SectionVocations groups={groups} />
          </div>
          <div className={"description"}>
            <h2 className={"title"}>Vocations</h2>
            <p>Glorifier Dieu dans tous les aspects de notre vie c’est le glorifier dans chacune de nos vocations. Cela
              veut dire assumer les responsabilités qu’il nous confie là où il nous place: dans notre famille, dans
              notre église, dans notre vie sociale. Concrètement, glorifier Dieu c’est être le père, le frère et le
              collègue que Dieu m’appelle à être.</p>
          </div>
        </VocationsWrapper>
      </div>

      <section>
        <MainList topics={topics}/>
      </section>

      <SearchSection>
        {/* eslint-disable-next-line react/no-unescaped-entities */}
        <p className={"search-section-text"}>Vous n'avez pas trouvé ce que vous cherchiez? <br/>Essayez notre outil de recherche</p>
        <BigCta link={"/recherche"} text={"Rechercher"} theme={"dark"}/>
      </SearchSection>
    </>
  )
}

const VocationsWrapper = styled.section`
  position: relative;
  height: auto;
  margin-bottom: 48px;
  
  display: flex;
  flex-direction: column;
  
  .description {
    font-family: Switzer, sans-serif;
    margin-top: 64px;
    font-size: 16px;
  }
  
  @media ${device.desktop} {
    margin-bottom: 96px;
    flex-direction: row;
    .description {
      position: sticky;
      top: 80px;
      margin-top: 0;
      max-height: 200px;
      .title {
        margin-top: -8px;
      }
      margin-bottom: 14px;
      width: 33%;
      line-height: 23px;
      color: #161616;
    }
    .list {
      width: 66%;
      margin-right: 80px;
    }
  }
`;

const SearchSection = styled.section`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  background-color: #F9F1E6;
  
  .search-section-text {
    text-align: center;
    font-family: Stelvio, sans-serif;
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 32px;
  }
`;

export async function getStaticProps() {

  let groups = await client.query({
    query: QUERY_GROUPS
  }).then(response => {
    return response.data.topicGroups
  })

  let topics = await client.query({
    query: QUERY_TOPICS,
  }).then(response => {
    return response.data.topics
  })

  return {
    props: {
      groups: groups,
      topics: topics,
    },
    revalidate: 10
  }
}

const QUERY_GROUPS = gql`
    query Groups{
        topicGroups{
            name
            slug
            description
            cover{
                url
                height
                width
                provider
                formats
            }
            children {
                id
                name
                slug
                type
            }
            type
        }
    }
`

const QUERY_TOPICS = gql`
    query Topics{
        topics(limit: 200){
            name
            slug
            id
            postCount
            parent {
                id
                name
                slug
            }
        }
    }
`
