/**
 * Script pour réindexer les données dans Meilisearch
 *
 * Ce script :
 * 1. Supprime l'index existant
 * 2. Crée un nouvel index
 * 3. Configure les attributs filtrables, triables et recherchables
 * 4. Récupère tous les posts de Strapi
 * 5. Analyse et valide la structure des données
 * 6. Les indexe dans Meilisearch avec la structure correcte
 *
 * Pour exécuter ce script :
 * 1. Assurez-vous que Meilisearch et Strapi sont en cours d'exécution
 * 2. Exécutez la commande : node reindex-meilisearch.js
 */

const fetch = require('node-fetch');
const axios = require('axios');
const fs = require('fs');

const MEILI_URL = 'http://127.0.0.1:7700';
const STRAPI_URL = 'http://127.0.0.1:1337';
const INDEX_NAME = 'post';

// Statistiques pour l'analyse
const stats = {
  totalPosts: 0,
  postsWithoutLead: 0,
  postsWithoutBody: 0,
  postsWithoutAuthor: 0,
  postsWithoutImage: 0,
  postsWithoutTopics: 0,
  postsWithoutTags: 0,
  postsByType: {},
  podcastsWithoutPodcastModule: 0,
  podcastsWithoutPodcastSlug: 0,
  formationsWithoutFormationModule: 0,
  invalidRoutes: 0
};

// Fonction pour nettoyer le texte (supprimer markdown et HTML)
function cleanText(text) {
  if (!text) return '';
  // Supprimer le markdown
  let cleaned = text.replace(/(?:_|[*#])|\[(.*?)\]\(.*?\)/gm, "$1");
  // Supprimer le HTML
  cleaned = cleaned.replace(/`|<[^>]*(>|…)/gm, "");
  return cleaned;
}

// Fonction pour extraire un module spécifique
function extractModule(modules, componentName) {
  if (!modules || !Array.isArray(modules)) return null;
  return modules.find(module => module.__component === componentName);
}

// Fonction pour déterminer la route correcte selon le type de post
function determineRoute(post) {
  if (!post || !post.slug) {
    stats.invalidRoutes++;
    return '/';
  }

  // Si le post a déjà une route définie, la normaliser
  if (post.route) {
    // Éviter les doubles barres obliques
    if (post.route.startsWith('/')) {
      return post.route;
    } else {
      return '/' + post.route;
    }
  }

  const type = post.type || 'article';

  switch(type) {
    case 'podcast':
      const podcastModule = extractModule(post.modules, 'module.podcast');
      if (!podcastModule) {
        stats.podcastsWithoutPodcastModule++;
        return `/article/${post.slug}`;
      }
      if (!podcastModule.podcast || !podcastModule.podcast.slug) {
        stats.podcastsWithoutPodcastSlug++;
        return `/article/${post.slug}`;
      }
      return `/podcasts/${podcastModule.podcast.slug}/${post.slug}`;

    case 'parcours':
      return `/parcours-emails/${post.slug}`;

    case 'webinaire':
      return `/webinaires/${post.slug}`;

    case 'formation':
      const formationModule = extractModule(post.modules, 'module.formation');
      if (!formationModule) {
        stats.formationsWithoutFormationModule++;
        return `/article/${post.slug}`;
      }
      if (formationModule.link) {
        return formationModule.link;
      }
      return `/article/${post.slug}`;

    default:
      return `/article/${post.slug}`;
  }
}

// Fonction pour préparer un post pour Meilisearch
function preparePost(post) {
  // Incrémenter les statistiques
  stats.totalPosts++;

  // Compter par type
  const type = post.type || 'article';
  stats.postsByType[type] = (stats.postsByType[type] || 0) + 1;

  // Extraire le module lead s'il existe
  let lead = null;
  const leadModule = extractModule(post.modules, 'module.lead');
  if (leadModule && leadModule.content) {
    lead = cleanText(leadModule.content);
  } else {
    stats.postsWithoutLead++;
  }

  // Vérifier le corps du post
  if (!post.body) {
    stats.postsWithoutBody++;
  }

  // Vérifier l'auteur
  if (!post.author || !post.author.fullName) {
    stats.postsWithoutAuthor++;
  }

  // Vérifier l'image
  if (!post.image) {
    stats.postsWithoutImage++;
  }

  // Vérifier les topics
  if (!post.topics || post.topics.length === 0) {
    stats.postsWithoutTopics++;
  }

  // Vérifier les tags
  if (!post.tags || post.tags.length === 0) {
    stats.postsWithoutTags++;
  }

  // Déterminer la route
  const route = determineRoute(post);

  // Préparer le corps du post
  const body = cleanText(post.body || '');

  // Retourner l'objet formaté pour Meilisearch
  return {
    id: `post-${post.id}`,
    title: post.title || '',
    slug: post.slug || '',
    author: post.author?.fullName || null,
    tags: post.tags?.map(tag => tag.name) || [],
    topics: post.topics?.map(topic => topic.name) || [],
    type: type,
    lead: lead || null,
    body: body || null,
    date: post.published_at || new Date().toISOString(),
    blog: post.blog?.slug || null,
    image: post.image || null,
    route: route,
    cs: post.isCornerStone || false,
    // Ajouter totalHits pour compatibilité avec le code existant
    totalHits: 0
  };
}

async function reindexMeilisearch() {
  try {
    console.log('Début de la réindexation de Meilisearch...');

    // 1. Supprimer l'index existant s'il existe
    console.log('Vérification de l\'existence de l\'index...');
    const indexesResponse = await fetch(`${MEILI_URL}/indexes`);
    const indexes = await indexesResponse.json();

    const indexExists = indexes.results && indexes.results.some(index => index.uid === INDEX_NAME);

    if (indexExists) {
      console.log('Suppression de l\'index existant...');
      await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}`, {
        method: 'DELETE'
      });
      console.log('Index supprimé avec succès');
    }

    // 2. Créer un nouvel index
    console.log('Création d\'un nouvel index...');
    await fetch(`${MEILI_URL}/indexes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ uid: INDEX_NAME })
    });
    console.log('Nouvel index créé avec succès');

    // 3. Configurer les attributs filtrables, triables et recherchables
    console.log('Configuration des attributs filtrables...');
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'title', 'topics', 'tags', 'author', 'type', 'blog', 'cs'
      ])
    });

    console.log('Configuration des attributs triables...');
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['date'])
    });

    console.log('Configuration des attributs de recherche...');
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['title', 'body', 'lead'])
    });

    // 4. Récupérer tous les posts de Strapi
    console.log('Récupération des posts depuis Strapi...');
    const strapiResponse = await axios.get(`${STRAPI_URL}/posts?_limit=-1`);
    const posts = strapiResponse.data;
    console.log(`${posts.length} posts récupérés depuis Strapi`);

    // 5. Analyser les données
    console.log('Analyse des données...');
    const preparedPosts = posts.map(preparePost);

    // Générer un rapport d'analyse
    console.log('\n=== RAPPORT D\'ANALYSE DES DONNÉES ===');
    console.log(`Total des posts: ${stats.totalPosts}`);
    console.log('\nRépartition par type:');
    Object.entries(stats.postsByType).forEach(([type, count]) => {
      console.log(`- ${type}: ${count} (${Math.round(count/stats.totalPosts*100)}%)`);
    });

    console.log('\nProblèmes détectés:');
    console.log(`- Posts sans lead: ${stats.postsWithoutLead} (${Math.round(stats.postsWithoutLead/stats.totalPosts*100)}%)`);
    console.log(`- Posts sans corps: ${stats.postsWithoutBody} (${Math.round(stats.postsWithoutBody/stats.totalPosts*100)}%)`);
    console.log(`- Posts sans auteur: ${stats.postsWithoutAuthor} (${Math.round(stats.postsWithoutAuthor/stats.totalPosts*100)}%)`);
    console.log(`- Posts sans image: ${stats.postsWithoutImage} (${Math.round(stats.postsWithoutImage/stats.totalPosts*100)}%)`);
    console.log(`- Posts sans topics: ${stats.postsWithoutTopics} (${Math.round(stats.postsWithoutTopics/stats.totalPosts*100)}%)`);
    console.log(`- Posts sans tags: ${stats.postsWithoutTags} (${Math.round(stats.postsWithoutTags/stats.totalPosts*100)}%)`);
    console.log(`- Podcasts sans module podcast: ${stats.podcastsWithoutPodcastModule}`);
    console.log(`- Podcasts sans slug de podcast: ${stats.podcastsWithoutPodcastSlug}`);
    console.log(`- Formations sans module formation: ${stats.formationsWithoutFormationModule}`);
    console.log(`- Routes invalides: ${stats.invalidRoutes}`);

    // Enregistrer le rapport dans un fichier
    const reportDate = new Date().toISOString().replace(/:/g, '-').split('.')[0];
    const reportPath = `meilisearch-reindex-report-${reportDate}.json`;
    fs.writeFileSync(reportPath, JSON.stringify({
      date: new Date().toISOString(),
      stats,
      samplePosts: preparedPosts.slice(0, 5) // Inclure 5 exemples de posts préparés
    }, null, 2));
    console.log(`\nRapport détaillé enregistré dans ${reportPath}`);

    // 6. Indexer les posts dans Meilisearch
    console.log('\nIndexation des posts dans Meilisearch...');
    const indexResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/documents`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(preparedPosts)
    });

    const indexResult = await indexResponse.json();
    console.log('Indexation terminée !', indexResult);

    // 7. Vérifier que l'indexation a réussi
    console.log('\nVérification de l\'indexation...');
    const statsResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/stats`);
    const statsResult = await statsResponse.json();
    console.log(`Nombre de documents indexés: ${statsResult.numberOfDocuments}`);

    if (statsResult.numberOfDocuments !== stats.totalPosts) {
      console.warn(`ATTENTION: Le nombre de documents indexés (${statsResult.numberOfDocuments}) ne correspond pas au nombre de posts traités (${stats.totalPosts})`);
    } else {
      console.log('Nombre de documents indexés correspond au nombre de posts traités ✓');
    }

    console.log('\nRéindexation de Meilisearch terminée avec succès !');
    console.log('Vous pouvez maintenant redémarrer Next.js pour utiliser les données réindexées.');
  } catch (error) {
    console.error('Erreur lors de la réindexation :', error);
  }
}

reindexMeilisearch();
