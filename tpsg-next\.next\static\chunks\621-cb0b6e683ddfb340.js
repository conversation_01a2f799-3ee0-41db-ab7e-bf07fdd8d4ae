"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[621],{1809:function(e,n,t){var i=t(5152),r=t.n(i);let o=r()(()=>t.e(523).then(t.bind(t,4523)),{loadableGenerated:{webpack:()=>[4523]},ssr:!1});n.Z=o},6268:function(e,n,t){t.d(n,{Z:function(){return SvgDuotone}});var i=t(5893),r=t(4440);function SvgDuotone(e){let{hexLight:n,hexDark:t}=e,o=(0,r.o)(n),a=(0,r.o)(t);return(0,i.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"svg-filter",children:(0,i.jsxs)("filter",{id:"duotone-filter",children:[(0,i.jsx)("feColorMatrix",{type:"matrix",result:"grayscale",values:"1 0 0 0 0   1 0 0 0 0   1 0 0 0 0   0 0 0 1 0"}),(0,i.jsxs)("feComponentTransfer",{colorInterpolationFilters:"sRGB",result:"duotone",children:[(0,i.jsx)("feFuncR",{type:"table",tableValues:"".concat(a.r/255," ").concat(o.r/255)}),(0,i.jsx)("feFuncG",{type:"table",tableValues:"".concat(a.g/255," ").concat(o.g/255)}),(0,i.jsx)("feFuncB",{type:"table",tableValues:"".concat(a.b/255," ").concat(o.b/255)}),(0,i.jsx)("feFuncA",{type:"table",tableValues:"0 1"})]})]})})}},3617:function(e,n,t){t.d(n,{Z:function(){return Related}});var i=t(2729),r=t(5893),o=t(9521);t(1304),t(785);var a=t(1664),l=t.n(a),c=t(7421),s=t(4871),p=t(5675),d=t.n(p);function _templateObject(){let e=(0,i._)(['\n  position: relative;\n  display: flex;\n  .elt-speakers-pics {\n    display: flex;\n    flex-direction: row;\n  }\n  .elt-speakers-text {\n    color: var(--c-soft-cream);\n    font-family: Switzer, "Helvetica Neue", Helvetica, Arial, sans-serif;\n    font-size: 14px;\n    font-weight: 500;\n    margin: 0 0 0 19px;\n  }\n  .elt-speakers-label {\n    margin: -4px 0 4px 0;\n    opacity: 0.72;\n  }\n  .elt-speakers-names {\n    margin: 0;\n  }\n']);return _templateObject=function(){return e},e}function _templateObject1(){let e=(0,i._)(["\n  position: relative;\n  width: 34px;\n  height: 34px;\n  background-color: darkseagreen;\n  border: 1px solid white;\n  margin-right: -5px;\n  border-radius: 32px;\n  overflow: hidden;\n"]);return _templateObject1=function(){return e},e}function Speakers(e){let{speakers:n,options:t}=e;return(0,r.jsxs)(m,{children:[(0,r.jsx)("div",{className:"elt-speakers-pics",children:(0,r.jsx)(RenderPictures,{pics:n.pictures})}),(0,r.jsxs)("div",{className:"elt-speakers-text",children:[(0,r.jsx)("p",{className:"elt-speakers-label",children:"Orateur".concat(n.pictures.length?"s":"")}),(0,r.jsx)("p",{className:"elt-speakers-names",children:n.names})]})]})}function RenderPictures(e){let{pics:n}=e;return n.map((e,n)=>(0,r.jsx)(u,{children:(0,r.jsx)(d(),{src:e,sizes:"34px",fill:!0,alt:""})},n))}let m=o.ZP.div.withConfig({componentId:"sc-32c56f82-0"})(_templateObject()),u=o.ZP.div.withConfig({componentId:"sc-32c56f82-1"})(_templateObject1());var f=t(3071);function Author_templateObject(){let e=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  gap: 10px;\n\n  .elt-author-picture {\n    position: relative;\n    aspect-ratio: 1/1;\n    height: ",";\n    \n    overflow: hidden;\n    border-radius: 34px;\n    background-color: var(--c-soft-cream);\n  }\n  \n  .elt-author-text {\n    position: relative;\n    display: flex;\n    flex-direction: ",";\n    align-items: ",";\n\n    color: ",";\n    \n    font-family: Switzer, sans-serif;\n    font-size: 14px;\n    font-weight: 400;\n    \n    span {\n      margin-right: 8px;\n    }\n    \n    .elt-author-date {\n      color: ",";\n      text-transform: capitalize;\n    }\n  }\n"]);return Author_templateObject=function(){return e},e}function Author(e){let{date:n,author:t,options:i={size:"m",theme:"light",direction:"row",showName:!0,showDate:!0,showPicture:!0}}=e,o=null==t?void 0:t.fullName,a=(0,f.k)(null==t?void 0:t.picture),l=i.showDate&&i.showName&&"row"===i.direction?(0,r.jsx)("span",{className:"elt-author-dot",children:"•"}):null;return(0,r.jsxs)(h,{size:i.size,theme:i.theme,direction:i.direction,children:[i.showPicture&&(0,r.jsx)("div",{className:"elt-author-picture",children:a&&(0,r.jsx)(d(),{src:a,sizes:"28px",fill:!0,style:{objectFit:"cover"},alt:""})}),(0,r.jsxs)("div",{className:"elt-author-text",children:[i.showName&&(0,r.jsx)("span",{className:"elt-author-name",children:o}),l,i.showDate&&(0,r.jsx)("span",{className:"elt-author-date",children:n})]})]})}let h=o.ZP.div.withConfig({componentId:"sc-c290df6-0"})(Author_templateObject(),e=>"row"===e.direction?"28px":"34px",e=>e.direction,e=>"row"===e.direction?"center":"left",e=>"dark"===e.theme?"var(--c-soft-cream)":"#161616",e=>"row"===e.direction?"inherit":"#848484");var x=t(1261);function LargeRelatedCard_templateObject(){let e=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: column-reverse;\n  width: 100%;\n\n  .lrc-image {\n    position: relative;\n    min-width: 50%;\n    aspect-ratio: 16/9;\n    background-color: #F9F1E6;\n    background-image: url(/images/tpsg-logo.svg);\n    background-repeat: no-repeat;\n    background-position: center;\n  }\n  \n  border: 1px solid ",";\n  \n  .lrc-content {\n    display: flex;\n    height: 360px;\n    flex-direction: column;\n    justify-content: space-between;\n    padding: clamp(1.5rem, 0.112rem + 2.98vw, 2.5rem);\n  }\n  .lrc-text {\n    color: var(--c-soft-cream);\n  }\n  .lrc-type {\n    margin-top: 0;\n    text-transform: uppercase;\n    letter-spacing: 0.5px;\n    font-size: 14px;\n  }\n  .lrc-title {\n    position: relative;\n    color: ",";\n    font-family: Stelvio, sans-serif;\n    font-weight: 400;\n    font-size: clamp(1.75rem, 1.4029850746268657rem + 0.7462686567164178vw, 2rem);\n    line-height: clamp(2rem, 1.4794776119402986rem + 1.1194029850746268vw, 2.375rem);\n    margin-bottom: -4px;\n    margin-top: 6px;\n  }\n  .lrc-lead {\n    margin-top: 12px;\n    font-family: Switzer, sans-serif;\n    font-size: clamp(0.875rem, 0.53rem + 0.75vw, 1.125rem);\n    line-height: clamp(1.375rem, 0.8544776119402986rem + 1.1194029850746268vw, 1.75rem);\n    color: #989AA4;\n    display: -webkit-box;\n    -webkit-line-clamp: 4;\n    font-weight: 400;\n    -webkit-box-orient: vertical;\n    text-overflow: ellipsis;\n    overflow: hidden;\n  }\n\n  \n  @media "," {\n    flex-direction: row;\n    justify-content: space-between;\n    .lrc-content {\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n      padding: 40px;\n      min-height: 100%;\n      width: calc(50% - 24px);\n    }\n    .lrc-text {\n      font-family: Switzer, sans-serif;\n    }\n    .lrc-title {\n      font-family: Stelvio, sans-serif;\n      font-size: 32px;\n      line-height: 38px;\n      font-weight: 500;\n      margin-top: 24px;\n      margin-bottom: 0;\n    }\n    .lrc-lead {\n      font-size: 16px;\n      color: #9B9B9B;\n      margin-top: 14px;\n      margin-bottom: 40px;\n    }\n    .lrc-image {\n      position: relative;\n      min-width: 50%;\n      aspect-ratio: 16/9;\n    }\n    &:hover {\n      border-color: var(--brand-color);\n      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);\n      box-shadow: 0 0 0 1px var(--c-brand-lighter);\n      cursor: pointer;\n    }\n  }\n"]);return LargeRelatedCard_templateObject=function(){return e},e}function LargeRelatedCard(e){let{post:n}=e,t=(0,s.mj)(n),i=(0,s.pL)(n),o=(0,s.qt)(n);return(0,r.jsx)(x.Z,{link:o,children:(0,r.jsxs)(b,{theme:"dark",children:[(0,r.jsxs)("div",{className:"lrc-content",children:[(0,r.jsxs)("div",{className:"lrc-text",children:[(0,r.jsx)("p",{className:"lrc-type",children:n.type}),(0,r.jsx)("p",{className:"lrc-title",children:n.title}),(0,r.jsx)("p",{className:"lrc-lead",children:t})]}),(0,r.jsx)(Speakers,{speakers:i})]}),(0,r.jsx)("div",{className:"lrc-image",children:(0,r.jsx)(d(),{src:(0,f.k)(n.image),sizes:"30vw",fill:!0,style:g,alt:""})})]})})}let g={objectFit:"cover"},b=o.ZP.div.withConfig({componentId:"sc-887c087d-0"})(LargeRelatedCard_templateObject(),e=>"dark"===e.theme?"rgba(250,247,243,0.2)":"rgba(22,22,22,0.2)",e=>"dark"===e.theme?"#FFFFFF":"#161616",c.U.desktop);var j=t(4218);function VerticalCard_templateObject(){let e=(0,i._)(["\n  width: 100%;\n  border: 1px solid ",";\n\n  .vc-image {\n    position: relative;\n    height: 200px;\n    background-size: 80px;\n    background-image: url(/images/tpsg-logo.svg);\n    background-color: var(--c-soft-cream);\n    background-repeat: no-repeat;\n    background-position: center;\n  }\n\n  .vc-content {\n    padding: 26px 24px 24px 24px;\n  }\n\n  .vc-label {\n    margin-top: 0;\n    margin-bottom: 8px;\n    font-size: 16px;\n    font-weight: 400;\n    color: rgba(255, 255, 255, 0.72);\n  }\n\n  .vc-title {\n    margin-top: 0;\n    height: clamp(4.5rem, 2.65rem + 2.88vw, 5.25rem);\n    margin-bottom: 40px;\n\n    color: ",';\n\n    font-family: Switzer, "Helvetica Neue", Helvetica, sans-serif;\n    font-size: clamp(1.125rem, 0.82rem + 0.48vw, 1.25rem);\n    font-weight: ',";\n\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    text-overflow: ellipsis;\n    overflow: hidden;\n  }\n  \n  transition: all 300ms ease;\n\n  @media "," {\n    margin: 2px 2px;\n    &:hover {\n      transform: translateY(-8px);\n      cursor: pointer;\n      border: 1px solid var(--c-brand-lighter);\n      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);\n      box-shadow: 0 0 0 1px var(--c-brand-lighter);\n    }\n  }\n"]);return VerticalCard_templateObject=function(){return e},e}function VerticalCard(e){let{label:n,post:t,options:i={showType:!1,theme:"light"}}=e,o=(0,j.S$)(t.date),a=(0,f.k)(t.image),c=(0,s.qt)(t),p={showName:!0,showDate:!1,theme:i.theme,direction:"column",showPicture:!0};return(0,r.jsx)(l(),{href:c,children:(0,r.jsxs)(v,{theme:i.theme,children:[i.showType&&(0,r.jsx)("p",{children:t.type}),(0,r.jsx)("div",{className:"vc-image",children:(0,r.jsx)(d(),{src:a,alt:"",sizes:"15vw",fill:!0,style:w.postImage})}),(0,r.jsxs)("div",{className:"vc-content",children:[n&&(0,r.jsx)("p",{className:"vc-label",children:n.text}),(0,r.jsx)("p",{className:"vc-title",children:t.title}),(0,r.jsx)(Author,{author:t.author,date:o,options:p})]})]})})}let w={postImage:{objectFit:"cover"}},v=o.ZP.div.withConfig({componentId:"sc-99268a48-0"})(VerticalCard_templateObject(),e=>"dark"===e.theme?"rgba(250,247,243,0.2)":"rgba(22,22,22,0.2)",e=>"dark"===e.theme?"var(--c-soft-cream)":"#161616",e=>"dark"===e.theme?400:500,c.U.desktop);function Related_templateObject(){let e=(0,i._)(["\n  position: relative;\n  background-color: var(--c-dark-green);\n  padding: 72px var(--border-space) 128px var(--border-space);\n  \n  .related-posts {\n    margin-top: 126px;\n    display: grid;\n    grid-template-columns: repeat(1, 1fr);\n    grid-gap: 24px;\n  }\n  \n  @media "," {\n    .related-posts {\n      grid-template-columns: repeat(2, 1fr);\n    }\n  }\n  @media "," {\n    .related-posts {\n      grid-template-columns: repeat(4, 1fr);\n    }\n  }\n"]);return Related_templateObject=function(){return e},e}function Related_templateObject1(){let e=(0,i._)(["\n  color: var(--c-soft-cream);\n  font-family: Stelvio, sans-serif;\n  font-size: 40px;\n  font-weight: 500;\n  line-height: 32px;\n  width: 70%;\n  margin-bottom: 40px;\n  \n  @media "," {\n    width: 100%;\n    white-space: nowrap;\n  }\n  @media "," {\n    font-size: 40px;\n  }\n"]);return Related_templateObject1=function(){return e},e}let _=["Default","vocation","ministry"],k={theme:"dark",showType:!1};function RenderVerticalCard(e){let n,{relatedItem:t,baseTopicNames:i}=e;i&&(n="Default"===t.section?t.post.topics.filter(e=>i.includes(e.name)).map(e=>e.name).splice(0,2).join(", "):t.origin);let o={text:n,link:""};return(0,r.jsx)(VerticalCard,{post:t.post,options:k,label:o})}function Related(e){let{items:n,baseTopicNames:t}=e;if(!n)return null;let i=n.filter(e=>"webinar"===e.section)[0],o=n.filter(e=>_.includes(e.section));return(0,r.jsxs)(O,{children:[(0,r.jsx)(C,{children:"Ressources similaires"}),i&&(0,r.jsx)(LargeRelatedCard,{post:i.post}),(0,r.jsx)("div",{className:"related-posts",children:o&&o.map((e,n)=>(0,r.jsx)(RenderVerticalCard,{relatedItem:e,baseTopicNames:t},n))})]})}let O=o.ZP.section.withConfig({componentId:"sc-b49488c-0"})(Related_templateObject(),c.U.tablet,c.U.desktop),C=o.ZP.h2.withConfig({componentId:"sc-b49488c-1"})(Related_templateObject1(),c.U.tablet,c.U.desktop)},9340:function(e,n,t){t.d(n,{Z:function(){return b}});var i=t(2729),r=t(5893),o=t(9521),a=t(7421),l=t(1304);function _templateObject(){let e=(0,i._)(["\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  font-size: 16px;\n  min-width: 10%;\n  .subheader-text_label {\n    font-weight: 600;\n    margin-top: 4px;\n    margin-bottom: 0;\n  }\n  .subheader-text_content {\n    margin-top: 2px;\n    margin-bottom: 0;\n    font-weight: 400;\n    color: #484848;\n  }\n  @media "," {\n    font-size: 20px;\n  }\n  @media "," {\n    font-size: 22px;\n  }\n"]);return _templateObject=function(){return e},e}function Text(e){let{label:n,content:t,noClass:i,addClass:o}=e;return(0,r.jsxs)(c,{className:i?"":"subheader-item ".concat(o),children:[(0,r.jsx)("p",{className:"subheader-text_label",children:n}),(0,r.jsx)("p",{className:"subheader-text_content",children:t})]})}let c=o.ZP.div.withConfig({componentId:"sc-32a8df6a-0"})(_templateObject(),a.U.tablet,a.U.desktop);var s=t(1261);t(4871);var p=t(1510);function Authors_templateObject(){let e=(0,i._)(["\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n"]);return Authors_templateObject=function(){return e},e}function _templateObject1(){let e=(0,i._)(["\n  display: none;\n  flex-direction: row;\n  margin-right: 16px;\n  margin-left: 10px;\n\n  .author-picture {\n    position: relative;\n    height: 50px;\n    width: 50px;\n    border-radius: 25px;\n    margin-left: -10px;\n    overflow: hidden;\n    background-color: #161616;\n  }\n\n  @media "," {\n    display: flex;\n  }\n"]);return _templateObject1=function(){return e},e}let d=o.ZP.div.withConfig({componentId:"sc-bb91c198-0"})(Authors_templateObject()),m=o.ZP.div.withConfig({componentId:"sc-bb91c198-1"})(_templateObject1(),a.U.tablet);var u=t(9150);function Social_templateObject(){let e=(0,i._)(["\n  position: absolute;\n  right: 0;\n"]);return Social_templateObject=function(){return e},e}let f=o.ZP.div.withConfig({componentId:"sc-9fe33153-0"})(Social_templateObject());var h=t(1664),x=t.n(h);function LinkButton_templateObject(){let e=(0,i._)(["\n  position: absolute;\n  display: flex;\n  align-items: center;\n  right: 0;\n  top: 0;\n  height: 100%;\n\n  a {\n    margin: 0;\n    padding: 12px 18px 6px 18px;\n    color: #ffffff;\n    font-size: 20px;\n    line-height: 24px;\n    background-color: var(--brand-color);\n    /* No border radius as per Jean-Daniel's feedback */\n  }\n\n  &:hover {\n    opacity: 0.72;\n  }\n"]);return LinkButton_templateObject=function(){return e},e}let g=o.ZP.div.withConfig({componentId:"sc-1f2fe2ae-0"})(LinkButton_templateObject());function SubHeader_templateObject(){let e=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n\n  width: 100%;\n\n  padding-top: 16px;\n  padding-bottom: 16px;\n  border-top: 1px solid #dddddd;\n  border-bottom: 1px solid #dddddd;\n\n  @media "," {\n    padding-top: 24px;\n    padding-bottom: 24px;\n  }\n  .subheader-item {\n    padding-left: 16px;\n    margin-right: 48px;\n    border-left: 1px solid #dddddd;\n  }\n  .subheader-item:first-child {\n    padding-left: 0;\n    border-left: 0;\n  }\n  .subheader-item:last-child {\n    margin-right: 0;\n  }\n\n"]);return SubHeader_templateObject=function(){return e},e}let SubHeader=e=>{let{children:n}=e;return(0,r.jsx)(j,{className:"subheader",children:n})};SubHeader.Authors=function(e){let{label:n,authors:t,addClass:i}=e,o=(0,p.o)();if(!t)return(0,r.jsx)(r.Fragment,{});t.length-2>0&&t.length;let a=t.filter(e=>null==e?void 0:e.fullName).map(e=>e.fullName).splice(0,2).toString().replace(/\,/g,", ");a=a.length>0?a:"aucun ".concat(n," n'est renseign\xe9");let getAuthorLink=e=>{if(e.url)return e.url;if(e.slug){var n;let t=null===(n=o.blogs)||void 0===n?void 0:n.some(n=>n.slug===e.slug);if(t)return"/blog/".concat(e.slug)}return"/recherche?author=".concat(encodeURIComponent(e.fullName))},AuthorLink=e=>{let{author:n,children:t}=e,i=getAuthorLink(n);return(0,r.jsx)(s.Z,{link:i,children:t})},c=t.filter(e=>null==e?void 0:e.fullName).map((e,n)=>(0,r.jsx)(AuthorLink,{author:e,children:n>0?", "+e.fullName:e.fullName},n));return(0,r.jsxs)(d,{className:"subheader-item ".concat(i),children:[(0,r.jsx)(m,{children:t.filter(e=>null==e?void 0:e.picture).map((e,n)=>(0,r.jsx)(AuthorLink,{author:e,children:(0,r.jsx)("div",{className:"author-picture",children:(0,r.jsx)(l.Z,{imageData:e.picture})},"pic-".concat(n,"}"))},n))}),(0,r.jsx)(Text,{label:n,content:c,noClass:!0})]})},SubHeader.Text=Text,SubHeader.Social=function(e){let{url:n,addClass:t}=e;return(0,r.jsx)(f,{className:"subheader-item ".concat(t),children:(0,r.jsx)(u.Z,{url:n,inRow:!0})})},SubHeader.LinkButton=function(e){let{url:n,text:t}=e;return(0,r.jsx)(g,{children:(0,r.jsx)(x(),{href:n,children:t})})};var b=SubHeader;let j=o.ZP.div.withConfig({componentId:"sc-f4ab0496-0"})(SubHeader_templateObject(),a.U.tablet)},9150:function(e,n,t){t.d(n,{Z:function(){return SocialMedia}});var i=t(2729),r=t(5893),o=t(6616),a=t(6573),l=t(9521);function _templateObject(){let e=(0,i._)(["\n  display: flex;\n  flex-direction: ",";\n  width: ",";\n  height: ",";\n  \n  svg {\n    margin-bottom: ",";\n    margin-top: ",";\n    margin-right: 8px;\n    width: 32px;\n    height: 32px;\n  }\n  \n  .social-button {\n    &:hover {\n      * {\n        stroke: var(--c-brand-light);\n      }\n    }\n  }\n"]);return _templateObject=function(){return e},e}function SocialMedia(e){let{url:n,inRow:t}=e;return(0,r.jsxs)(c,{className:"social-buttons",inRow:t,children:[(0,r.jsx)(o.Z,{url:n,className:"social-button",children:(0,r.jsx)(TwitterIcon,{})}),(0,r.jsx)(a.Z,{url:n,className:"social-button",children:(0,r.jsx)(FacebookIcon,{})})]})}let c=l.ZP.div.withConfig({componentId:"sc-bc9d1278-0"})(_templateObject(),e=>e.inRow?"row":"column",e=>e.inRow?"auto":"52px",e=>e.inRow?"52px":"200px",e=>e.inRow?"auto":"8px",e=>e.inRow?"4px":"auto"),FacebookIcon=()=>(0,r.jsxs)("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M13.7038 24.5377C19.3802 24.5377 23.9819 19.936 23.9819 14.2596C23.9819 8.58321 19.3802 3.98157 13.7038 3.98157C8.02742 3.98157 3.42578 8.58321 3.42578 14.2596C3.42578 19.936 8.02742 24.5377 13.7038 24.5377Z",stroke:"#242424",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M17.9856 9.97705H16.2726C15.5912 9.97705 14.9376 10.2478 14.4557 10.7296C13.9738 11.2115 13.7031 11.8651 13.7031 12.5466V24.5376",stroke:"#242424",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M10.2773 15.9727H17.1294",stroke:"#242424",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),TwitterIcon=()=>(0,r.jsx)("svg",{width:"28",height:"29",viewBox:"0 0 28 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{d:"M13.7101 10.1847C13.7102 9.20074 14.0491 8.24682 14.6698 7.48331C15.2904 6.7198 16.155 6.19326 17.1182 5.99222C18.0814 5.79118 19.0845 5.92789 19.9587 6.37937C20.833 6.83085 21.5251 7.56957 21.9188 8.47133L25.7012 8.47137L22.2471 11.9255C22.0222 15.3997 20.4835 18.658 17.9435 21.0389C15.4035 23.4199 12.0526 24.7449 8.57112 24.7449C5.1451 24.7449 4.2886 23.4602 4.2886 23.4602C4.2886 23.4602 7.71462 22.1754 9.42762 19.6059C9.42762 19.6059 2.57559 16.1799 4.2886 6.75836C4.2886 6.75836 8.57112 11.0409 13.7087 11.8974L13.7101 10.1847Z",stroke:"#242424",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})},6368:function(e,n,t){t.d(n,{DZ:function(){return f},GN:function(){return p},My:function(){return u},NZ:function(){return s},V1:function(){return l},X0:function(){return m},bP:function(){return c},hQ:function(){return h},kz:function(){return d}});var i=t(2729),r=t(9521),o=t(7421);function _templateObject(){let e=(0,i._)(["\n  position: relative;\n  font-weight: 500;\n  color: #161616;\n  font-size: 48px;\n  margin-top:  calc(var(--spacing-l) - 48px * ",");\n  margin-bottom: calc(var(--spacing-l) - 48px * ",");\n  \n  @media "," {\n    font-size: 72px;\n    margin-top:  calc(var(--spacing-l) - 72px * ",");\n    margin-bottom: calc(var(--spacing-l) - 72px * ",");\n  }\n  @media "," {\n    font-size: 104px;\n    margin-top:  calc(var(--spacing-l) - 104px * ",");\n    margin-bottom: calc(var(--spacing-l) - 104px * ",");\n  }\n"]);return _templateObject=function(){return e},e}function _templateObject1(){let e=(0,i._)(["\n  box-sizing: content-box;\n  color: #ececec;\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: clamp(24px, 1.125rem + 1vw, 32px);\n  font-weight: 400;\n  max-width: 600px;\n  line-height: 105%;\n  &:before {\n    display: block;\n    margin-bottom: 8px;\n    content: '","';\n    color: ",";\n    font-size: 18px;\n  }\n  @media "," {\n    font-size: 32px;\n    margin-right: 24px;\n  }\n  @media "," {\n    font-weight: 400;\n    margin-right: 48px;\n    font-size: clamp(32px, 3.5vw, 48px);\n  }\n"]);return _templateObject1=function(){return e},e}function _templateObject2(){let e=(0,i._)(['\n  \n  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);\n  \n  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;\n  font-size: var(--title-size);\n  color: ',";\n  font-weight: 400;\n  margin-top: 0;\n  margin-bottom: calc( var(--title-size) * -",");\n  \n"]);return _templateObject2=function(){return e},e}function _templateObject3(){let e=(0,i._)(["\n  font-size: 24px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 48px;\n    margin-bottom: 16px;\n    margin-top: 16px;\n  }\n"]);return _templateObject3=function(){return e},e}function _templateObject4(){let e=(0,i._)(["\n  font-size: 20px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media "," {\n    font-size: 30px;\n  }\n  span {\n    position: relative;\n    margin-left: 8px;\n    display: inline-block;\n    margin-bottom: -2px;\n    height: 22px;\n    width: 22px;\n  }\n"]);return _templateObject4=function(){return e},e}function _templateObject5(){let e=(0,i._)(["\n  font-size: 16px;\n  color: #888888;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding-right: 30px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media screen and (max-width:320px){ // Little screen only\n    padding: 0;\n  }\n  @media "," {\n    font-size:20px;\n  }\n"]);return _templateObject5=function(){return e},e}function _templateObject6(){let e=(0,i._)(['\n  font-size: 14px;\n  font-style: italic;\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  color: #7a7a7a;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media '," {\n    font-size: 17px;\n  }\n"]);return _templateObject6=function(){return e},e}function _templateObject7(){let e=(0,i._)(["\n  font-size: 38px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 64px;\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n  @media "," {\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n"]);return _templateObject7=function(){return e},e}function _templateObject8(){let e=(0,i._)(["\n  color: #161616;\n  letter-spacing: 0.04em;\n  margin-top: 48px;\n  text-transform: uppercase;\n  @media "," {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 22px;\n    font-weight: 500;\n    &:hover {\n      color: var(--brand-color);\n    }\n  }\n"]);return _templateObject8=function(){return e},e}function _templateObject9(){let e=(0,i._)(["\n  font-weight: 400;\n  font-size: 26px;\n  margin: 0;\n  color: #161616;\n  @media "," {\n    font-size: 32px;\n  }\n"]);return _templateObject9=function(){return e},e}let a={topSpace:.096,minBottomSpace:.2788,maxBottomSpace:.4711},l=r.ZP.h1.withConfig({componentId:"sc-a3af5335-0"})(_templateObject(),a.topSpace,a.minBottomSpace,o.U.tablet,a.topSpace,a.minBottomSpace,o.U.desktop,a.topSpace,a.minBottomSpace),c=r.ZP.h2.withConfig({componentId:"sc-a3af5335-1"})(_templateObject1(),e=>e.label,e=>e.color,o.U.tablet,o.U.desktop),s=r.ZP.h2.withConfig({componentId:"sc-a3af5335-2"})(_templateObject2(),e=>e.light?"var(--c-soft-cream)":"var(--soft-dark)",a.maxBottomSpace),p=r.ZP.h2.withConfig({componentId:"sc-a3af5335-3"})(_templateObject3(),o.U.tablet),d=r.ZP.h4.withConfig({componentId:"sc-a3af5335-4"})(_templateObject4(),o.U.tablet),m=r.ZP.p.withConfig({componentId:"sc-a3af5335-5"})(_templateObject5(),o.U.tablet),u=r.ZP.p.withConfig({componentId:"sc-a3af5335-6"})(_templateObject6(),o.U.tablet),f=r.ZP.h1.withConfig({componentId:"sc-a3af5335-7"})(_templateObject7(),o.U.tablet,o.U.desktop),h=r.ZP.p.withConfig({componentId:"sc-a3af5335-8"})(_templateObject8(),o.U.desktop);r.ZP.p.withConfig({componentId:"sc-a3af5335-9"})(_templateObject9(),o.U.tablet)}}]);