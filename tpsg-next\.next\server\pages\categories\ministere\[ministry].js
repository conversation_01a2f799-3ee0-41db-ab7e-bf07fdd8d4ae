"use strict";(()=>{var e={};e.id=8835,e.ids=[8835,2888],e.modules={1580:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>m,default:()=>u,getServerSideProps:()=>g,getStaticPaths:()=>h,getStaticProps:()=>d,reportWebVitals:()=>x,routeModule:()=>P,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>w,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>f});var s=r(7093),a=r(5244),o=r(1323),n=r(779),l=r(4033),p=r(4089),c=e([p]);p=(c.then?(await c)():c)[0];let u=(0,o.l)(p,"default"),d=(0,o.l)(p,"getStaticProps"),h=(0,o.l)(p,"getStaticPaths"),g=(0,o.l)(p,"getServerSideProps"),m=(0,o.l)(p,"config"),x=(0,o.l)(p,"reportWebVitals"),f=(0,o.l)(p,"unstable_getStaticProps"),y=(0,o.l)(p,"unstable_getStaticPaths"),w=(0,o.l)(p,"unstable_getStaticParams"),S=(0,o.l)(p,"unstable_getServerProps"),b=(0,o.l)(p,"unstable_getServerSideProps"),P=new s.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/categories/ministere/[ministry]",pathname:"/categories/ministere/[ministry]",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:p});i()}catch(e){i(e)}})},4089:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{default:()=>PageMinistry,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var s=r(997),a=r(9114),o=r(1385),n=r(7518),l=r.n(n),p=r(8657),c=r(7467),u=r(1632),d=r(2325),h=r(9296),g=r(465),m=r(2333),x=r(7438),f=r(723),y=e([p]);function PageMinistry({ministry:e,fallback:t}){let r=t?.posts?.totalHits||0,i=t?.posts?.hits,a=t?.cornerStonesFeatured||[];return e?(0,s.jsxs)(j,{children:[s.jsx(x.Z,{category:e,type:"minist\xe8re"}),(0,s.jsxs)(v,{children:[s.jsx("section",{children:a[0]&&s.jsx(p.g4,{content:a[0]})}),(0,s.jsxs)(b,{className:"site-padding",children:[s.jsx("p",{className:"label-type",children:i?.length>0?"Derni\xe8res ressources":""}),(0,s.jsxs)("div",{className:"posts-container",children:[(0,s.jsxs)(P,{children:[s.jsx("ul",{className:"list-container",children:i?.map((e,t)=>s.jsx("li",{className:"post-card-li",children:s.jsx(d.Z,{post:e,options:{showLead:!0,showDate:!0,showAuthor:!0}})},`post-${t}`))}),s.jsx(g.Z,{nbHits:r,baseUrl:`/categories/ministere/${e.slug}/ressources?page=`,currentPage:1,options:{postPerPage:15}})]}),s.jsx(q,{children:(0,s.jsxs)("div",{className:"cornerstone-container",children:[a[1]&&s.jsx(h.Z,{post:a[1],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}}),a[2]&&s.jsx(h.Z,{post:a[2],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}})]})})]})]}),s.jsx("section",{children:a[3]&&s.jsx(p.g4,{content:a[3]})})]})]}):null}async function getStaticProps({params:e}){let t=await o.Z.query({query:S,variables:{slug:e.ministry}}).then(e=>e.data.topicGroups[0]);if(!t)return{notFound:!0};let r=[];t?.parent?.children&&r.push(...t.children.topics),t?.topics&&r.push(...t.topics),r=r.filter((e,t,r)=>r.findIndex(t=>t.id===e.id)===t);let i=(0,u.D)(r),s=[],a=[null,null,null,null];if(i.length>0){s=await (0,m.j)({},i,15);let e=i.length>0?" AND ":"",r=`cs=true ${e} (${i})`,n=await f.V.searchHighlight("",{filter:r,sort:["date:desc"],limit:4}),l=t?.featured?.filter(e=>!e.inColumn),p=t?.featured?.filter(e=>e.inColumn),enrichFeaturedWithAuthor=async e=>{if(!e)return null;let t={...e};if(e.postRef?.author?.fullName)return t.author=e.postRef.author.fullName,t;if(e.cta?.url&&!e.author)try{let r=e.cta.url.split("/"),i=r[r.length-1];if(i){let{data:e}=await o.Z.query({query:_,variables:{slug:i}});e?.posts?.length>0&&(t.author=e.posts[0].author?.fullName||null)}}catch(t){console.error("Erreur lors de la r\xe9cup\xe9ration de l'auteur pour:",e.title,t)}return t};a[0]=await enrichFeaturedWithAuthor(l[0])||(n?.hits[0]?n?.hits.shift():null),a[1]=await enrichFeaturedWithAuthor(p[0])||(n?.hits[0]?n?.hits.shift():null),a[2]=await enrichFeaturedWithAuthor(p[1])||(n?.hits[0]?n?.hits.shift():null),a[3]=await enrichFeaturedWithAuthor(l[1])||(n?.hits[0]?n?.hits.shift():null),s.hits=s?.hits?.filter(e=>!a?.find(t=>t?.route===e?.route||t?.cta?.url===e?.route))}return{props:{ministry:t,fallback:{posts:s,cornerStonesFeatured:a}},revalidate:10}}async function getStaticPaths(){let e=await o.Z.query({query:w,variables:{type:"ministere"}}).then(e=>e.data.topicGroups),t=e.map(e=>({params:{ministry:e.slug}}));return{paths:t,fallback:!0}}p=(y.then?(await y)():y)[0];let w=a.gql`
    query Ministries($type: String!){
        topicGroups(where: { type: $type }){
            slug
        }
    }
`,S=a.gql`
    query Ministries($slug: String!) {
        topicGroups(where: { slug: $slug} ){
            name
            slug
            description
            cover {
                formats
            }
            topics {
                id
                name
                postCount
            }
            children {
                id
                name
                slug
                topics {
                    id
                    name
                    postCount
                }
            }
            featured {
                title
                description
                inColumn
                image {
                    url
                    height
                    width
                    alternativeText
                    provider
                }
                cta {
                    name
                    url
                }
                color {
                    foreground
                    background
                }
                type
                postRef {
                    author {
                        fullName
                    }
                }
            }
        }
    }
`,b=l().section.withConfig({componentId:"sc-52e2ee4c-0"})`
  margin-top: 48px;
  .posts-container {
    display: block;
  }
  @media ${c.U.desktop} {
    margin-top: 96px;
    .posts-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
`,P=l().article.withConfig({componentId:"sc-52e2ee4c-1"})`
  width: 100%;
  margin-bottom: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }

  .post-card-li {
    list-style: none;
    padding-right: 0;
  }

  @media ${c.U.desktop} {
    width: 66.7%;
    margin-bottom: 164px;
    .post-card-li {
      padding-right: 142px;
    }
  }
`,q=l().div.withConfig({componentId:"sc-52e2ee4c-2"})`
  position: relative;
  width: 100%;

  .cornerstone-container {
    /* position: sticky;
    top: 60px; */
  }

  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }

  @media ${c.U.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${c.U.desktop} {
    width: 33.3%;
  }
`,j=l().div.withConfig({componentId:"sc-52e2ee4c-3"})`
  .label-type {
    font-size: 20px;
    font-family: Stelvio, sans-serif;
    margin: 0 0 16px 0;

    font-weight: 500;
    letter-spacing: 4%;
    line-height: 32px;
  }
`,v=l().div.withConfig({componentId:"sc-52e2ee4c-4"})`
  margin-top: 0;
`,_=a.gql`
  query GetPostAuthorBySlug($slug: String!) {
    posts(where: { slug: $slug }) {
      author {
        fullName
      }
    }
  }
`;i()}catch(e){i(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},9997:e=>{e.exports=require("meilisearch")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,9755,4033,779,7113,6453,1077,7620,8672,2832,8657,723,5745,4092,142,8074,9296,7438],()=>__webpack_exec__(1580));module.exports=r})();