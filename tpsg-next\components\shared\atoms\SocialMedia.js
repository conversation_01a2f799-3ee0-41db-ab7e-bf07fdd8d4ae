import {
  FacebookShare<PERSON>utton,
  TwitterShare<PERSON>utton,
  FacebookMessengerShareButton,
} from "react-share";
import styled from "styled-components";

export default function SocialMedia({ url, inRow }) {

  return (
    <Wrapper className={"social-buttons"} inRow={inRow}>
      <TwitterShareButton url={url} className={"social-button"}>
        <TwitterIcon/>
      </TwitterShareButton>
      <FacebookShareButton url={url} className={"social-button"}>
        <FacebookIcon/>
      </FacebookShareButton>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  display: flex;
  flex-direction: ${p => p.inRow ? "row" : "column"};
  width: ${p => p.inRow ? "auto" : "52px"};
  height: ${p => p.inRow ? "52px" : "200px"};
  
  svg {
    margin-bottom: ${p => p.inRow ? "auto" : "8px"};
    margin-top: ${p => p.inRow ? "4px" : "auto"};
    margin-right: 8px;
    width: 32px;
    height: 32px;
  }
  
  .social-button {
    &:hover {
      * {
        stroke: var(--c-brand-light);
      }
    }
  }
`;


const FacebookIcon = () => {
  return (
    <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13.7038 24.5377C19.3802 24.5377 23.9819 19.936 23.9819 14.2596C23.9819 8.58321 19.3802 3.98157 13.7038 3.98157C8.02742 3.98157 3.42578 8.58321 3.42578 14.2596C3.42578 19.936 8.02742 24.5377 13.7038 24.5377Z"
        stroke="#242424" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path
        d="M17.9856 9.97705H16.2726C15.5912 9.97705 14.9376 10.2478 14.4557 10.7296C13.9738 11.2115 13.7031 11.8651 13.7031 12.5466V24.5376"
        stroke="#242424" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M10.2773 15.9727H17.1294" stroke="#242424" strokeWidth="1.5" strokeLinecap="round"
        strokeLinejoin="round"/>
    </svg>

  )
}

const TwitterIcon = () => {
  return (
    <svg width="28" height="29" viewBox="0 0 28 29" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M13.7101 10.1847C13.7102 9.20074 14.0491 8.24682 14.6698 7.48331C15.2904 6.7198 16.155 6.19326 17.1182 5.99222C18.0814 5.79118 19.0845 5.92789 19.9587 6.37937C20.833 6.83085 21.5251 7.56957 21.9188 8.47133L25.7012 8.47137L22.2471 11.9255C22.0222 15.3997 20.4835 18.658 17.9435 21.0389C15.4035 23.4199 12.0526 24.7449 8.57112 24.7449C5.1451 24.7449 4.2886 23.4602 4.2886 23.4602C4.2886 23.4602 7.71462 22.1754 9.42762 19.6059C9.42762 19.6059 2.57559 16.1799 4.2886 6.75836C4.2886 6.75836 8.57112 11.0409 13.7087 11.8974L13.7101 10.1847Z"
        stroke="#242424" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  )
}

const MessengerIcon = () => {
  return (
    <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M4.86351 19.2982C3.58653 17.1455 3.13932 14.6007 3.60586 12.1416C4.07239 9.68254 5.42058 7.47835 7.39726 5.94296C9.37395 4.40757 11.8431 3.64658 14.3412 3.80291C16.8392 3.95923 19.1943 5.0221 20.9642 6.79194C22.734 8.56178 23.7969 10.9168 23.9532 13.4149C24.1096 15.9129 23.3486 18.3821 21.8132 20.3588C20.2778 22.3355 18.0736 23.6837 15.6146 24.1503C13.1555 24.6168 10.6107 24.1696 8.45801 22.8927L8.45804 22.8926L4.9082 23.9068C4.76133 23.9488 4.60591 23.9507 4.45805 23.9124C4.31018 23.874 4.17526 23.7969 4.06725 23.6889C3.95924 23.5809 3.88208 23.4459 3.84377 23.2981C3.80545 23.1502 3.80738 22.9948 3.84934 22.8479L4.86358 19.2981L4.86351 19.2982Z"
        stroke="#242424" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path d="M8.56445 15.7652L11.9905 12.3392L15.4165 15.7652L18.8425 12.3392" stroke="#242424" strokeWidth="1.5"
        strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  )
}

const InstagramIcon = () => {
  return (
    <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M14 18.375C16.4162 18.375 18.375 16.4162 18.375 14C18.375 11.5838 16.4162 9.625 14 9.625C11.5838 9.625 9.625 11.5838 9.625 14C9.625 16.4162 11.5838 18.375 14 18.375Z"
        stroke="#242424" strokeWidth="1.5" strokeMiterlimit="10"/>
      <path
        d="M18.8125 3.9375H9.1875C6.28801 3.9375 3.9375 6.28801 3.9375 9.1875V18.8125C3.9375 21.712 6.28801 24.0625 9.1875 24.0625H18.8125C21.712 24.0625 24.0625 21.712 24.0625 18.8125V9.1875C24.0625 6.28801 21.712 3.9375 18.8125 3.9375Z"
        stroke="#242424" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
      <path
        d="M19.6875 9.625C20.4124 9.625 21 9.03737 21 8.3125C21 7.58763 20.4124 7 19.6875 7C18.9626 7 18.375 7.58763 18.375 8.3125C18.375 9.03737 18.9626 9.625 19.6875 9.625Z"
        fill="#242424"/>
    </svg>
  )
}


