"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[21],{6303:function(n,t,e){e.d(t,{Z:function(){return GridCard}});var o=e(2729),i=e(5893);e(1664);var a=e(1304),r=e(9521),l=e(6368),c=e(5599),p=e(1261);function _templateObject(){let n=(0,o._)(["\n  position: absolute;\n  top: 16px;\n  right: 16px;\n  font-size: 18px;\n  background-color: #161616;\n  color: #f4f4f4;\n  padding: 12px 24px 6px 24px;\n  white-space: nowrap;\n  cursor: pointer;\n  box-shadow: 0 4px 13px rgba(0, 0, 0, 0.57);\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,o._)(["\n  position: relative;\n  margin-bottom: 48px;\n  .post-card-details-space{\n    margin-bottom: 20px;\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,o._)(["\n  position: relative;\n  display: block;\n  height: auto;\n  width: 100%;\n  aspect-ratio: 16/10;\n  background-color: gray;\n  overflow: hidden;\n  margin-bottom: 10px;\n  .post-image-container{\n    width: 100%;\n    height: 100%;\n    position: relative;\n  }\n"]);return _templateObject2=function(){return n},n}function GridCard(n){let{post:t}=n;return(null==t?void 0:t.link)?(0,i.jsxs)(u,{children:[(0,i.jsxs)(p.Z,{link:t.link,children:[(null==t?void 0:t.image)&&(0,i.jsx)(d,{children:(0,i.jsx)("div",{className:"post-image-container",children:(0,i.jsx)(a.Z,{imageData:t.image})})}),(0,i.jsxs)("div",{children:[(0,i.jsx)(l.My,{className:0===t.details.length?"post-card-details-space":"",children:t.details}),(0,i.jsx)(l.kz,{children:t.title}),(0,i.jsx)(l.X0,{children:t.lead?t.lead:""})]})]}),t.youtubeEmbed&&(0,i.jsx)(c.Z,{style:s,youtubeEmbed:t.youtubeEmbed,children:(0,i.jsx)(m,{children:"Voir l'aper\xe7u"})})]}):null}let s={position:"absolute",top:"0",right:"0"},m=r.ZP.div.withConfig({componentId:"sc-5a8dd6d4-0"})(_templateObject()),u=r.ZP.div.withConfig({componentId:"sc-5a8dd6d4-1"})(_templateObject1()),d=r.ZP.div.withConfig({componentId:"sc-5a8dd6d4-2"})(_templateObject2())},6268:function(n,t,e){e.d(t,{Z:function(){return SvgDuotone}});var o=e(5893),i=e(4440);function SvgDuotone(n){let{hexLight:t,hexDark:e}=n,a=(0,i.o)(t),r=(0,i.o)(e);return(0,o.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"svg-filter",children:(0,o.jsxs)("filter",{id:"duotone-filter",children:[(0,o.jsx)("feColorMatrix",{type:"matrix",result:"grayscale",values:"1 0 0 0 0   1 0 0 0 0   1 0 0 0 0   0 0 0 1 0"}),(0,o.jsxs)("feComponentTransfer",{colorInterpolationFilters:"sRGB",result:"duotone",children:[(0,o.jsx)("feFuncR",{type:"table",tableValues:"".concat(r.r/255," ").concat(a.r/255)}),(0,o.jsx)("feFuncG",{type:"table",tableValues:"".concat(r.g/255," ").concat(a.g/255)}),(0,o.jsx)("feFuncB",{type:"table",tableValues:"".concat(r.b/255," ").concat(a.b/255)}),(0,o.jsx)("feFuncA",{type:"table",tableValues:"0 1"})]})]})})}},5599:function(n,t,e){e.d(t,{Z:function(){return Preview}});var o=e(2729),i=e(5893),a=e(9521),r=e(7294),l=e(5158);function YoutubeEmbed(n){let{video:t,autoplay:e}=n,o=(0,l.gc)(t);return(0,i.jsx)("iframe",{src:"https://www.youtube.com/embed/"+o+"?&autoplay=".concat(e?1:0,"&mute=1&enablejsapi=1"),width:"100%",height:"100%",frameBorder:"0",allow:"fullscreen"})}function _templateObject(){let n=(0,o._)(["\n\n  .fullscreen-preview {\n    position: fixed;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n    z-index: 4000;\n    background-color: rgba(0, 0, 0, 0.9);\n    animation-duration: 600ms;\n    animation-name: showIn;\n  }\n\n  @keyframes showIn {\n    from {\n      background-color: transparent;\n    }\n    to {\n      background-color: rgba(0, 0, 0, 0.9);\n    }\n  }\n\n  .video-player {\n    position: relative;\n    width: 60%;\n    aspect-ratio: 16/10;\n  }\n\n  .close-button {\n    position: absolute;\n    border: 1px solid white;\n    border-radius: 48px;\n    top: 48px;\n    right: var(--border-space);\n    height: 32px;\n    width: 64px;\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    p {\n      color: white;\n      transform: scaleX(1.5);\n      font-weight: 400;\n      font-size: 18px;\n      line-height: 52px;\n      margin-top: 8px;\n      margin-bottom: 0;\n    }\n  }\n"]);return _templateObject=function(){return n},n}function Preview(n){let{youtubeEmbed:t,style:e,children:o}=n,[a,l]=(0,r.useState)(!1);return(0,i.jsxs)(c,{style:e,onClick:()=>l(!a),children:[a&&(0,i.jsxs)("div",{className:"fullscreen-preview",children:[(0,i.jsx)("div",{className:"video-player",children:(0,i.jsx)(YoutubeEmbed,{autoplay:!0,video:t})}),(0,i.jsx)("div",{className:"close-button",onClick:()=>l(!1),children:(0,i.jsx)("p",{children:"X"})})]}),(0,i.jsx)("div",{onClick:()=>l(!0),children:o})]})}let c=a.ZP.div.withConfig({componentId:"sc-281e56b5-0"})(_templateObject())},1304:function(n,t,e){e.d(t,{Z:function(){return CondImage}});var o=e(5893),i=e(9755),a=e.n(i),r=e(3071);function CondImage(n){let{imageData:t,preserveAspectRatio:e,addClass:i,priority:l=!1,sizes:c=null}=n,p=(0,r.k)(t);return p?e?(0,o.jsx)(a(),{src:p,layout:"intrinsic",height:t.height,width:t.width,alt:t.alternativeText||"",priority:l,className:"cond-image ".concat(i),sizes:c}):(0,o.jsx)(a(),{className:"cond-image ".concat(i),src:p,layout:"fill",objectFit:"cover",alt:t.alternativeText||"",priority:l,sizes:c},p):null}},5985:function(n,t,e){e.d(t,{Z:function(){return GridCardSection}});var o=e(2729),i=e(5893),a=e(9521),r=e(7421);function _templateObject(){let n=(0,o._)(["\n  margin-top: 96px;\n  width: 100%;\n  border-top: 1px solid black;\n\n  .page-section-title {\n    margin: 16px 0px 12px 0px;\n    font-size: 28px;\n  }\n  .all-post-card {\n    display: grid;\n    grid-template-columns: 1fr;\n    column-gap: 16px;\n  }\n\n  &:last-child {\n    margin-bottom: 96px;\n  }\n\n  @media "," {\n    .page-section-title {\n      margin: 32px 0px;\n      font-size: 46px;\n    }\n    .all-post-card {\n      grid-template-columns: repeat(2, 1fr);\n    }\n  }\n\n  @media "," {\n    .all-post-card {\n      grid-template-columns: repeat(3, 1fr);\n    }\n  }\n\n  @media "," {\n    .all-post-card {\n      grid-template-columns: repeat(4, 1fr);\n    }\n  }\n"]);return _templateObject=function(){return n},n}function GridCardSection(n){let{nameSection:t,children:e}=n;return(0,i.jsxs)(l,{id:t.normalize("NFD").replace(/[\u0300-\u036f]/g,"").toLowerCase().replace(/\s+/g,"-").replace(/[^\w-]/g,""),children:[(0,i.jsx)("h2",{className:"page-section-title",children:t}),(0,i.jsx)("div",{className:"all-post-card",children:e})]})}let l=a.ZP.section.withConfig({componentId:"sc-3dc537d8-0"})(_templateObject(),r.U.tablet,r.U.desktop,r.U.desktopXL)},6368:function(n,t,e){e.d(t,{DZ:function(){return f},GN:function(){return s},My:function(){return d},NZ:function(){return p},V1:function(){return l},X0:function(){return u},bP:function(){return c},hQ:function(){return b},kz:function(){return m}});var o=e(2729),i=e(9521),a=e(7421);function _templateObject(){let n=(0,o._)(["\n  position: relative;\n  font-weight: 500;\n  color: #161616;\n  font-size: 48px;\n  margin-top:  calc(var(--spacing-l) - 48px * ",");\n  margin-bottom: calc(var(--spacing-l) - 48px * ",");\n  \n  @media "," {\n    font-size: 72px;\n    margin-top:  calc(var(--spacing-l) - 72px * ",");\n    margin-bottom: calc(var(--spacing-l) - 72px * ",");\n  }\n  @media "," {\n    font-size: 104px;\n    margin-top:  calc(var(--spacing-l) - 104px * ",");\n    margin-bottom: calc(var(--spacing-l) - 104px * ",");\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,o._)(["\n  box-sizing: content-box;\n  color: #ececec;\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: clamp(24px, 1.125rem + 1vw, 32px);\n  font-weight: 400;\n  max-width: 600px;\n  line-height: 105%;\n  &:before {\n    display: block;\n    margin-bottom: 8px;\n    content: '","';\n    color: ",";\n    font-size: 18px;\n  }\n  @media "," {\n    font-size: 32px;\n    margin-right: 24px;\n  }\n  @media "," {\n    font-weight: 400;\n    margin-right: 48px;\n    font-size: clamp(32px, 3.5vw, 48px);\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,o._)(['\n  \n  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);\n  \n  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;\n  font-size: var(--title-size);\n  color: ',";\n  font-weight: 400;\n  margin-top: 0;\n  margin-bottom: calc( var(--title-size) * -",");\n  \n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,o._)(["\n  font-size: 24px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 48px;\n    margin-bottom: 16px;\n    margin-top: 16px;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,o._)(["\n  font-size: 20px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media "," {\n    font-size: 30px;\n  }\n  span {\n    position: relative;\n    margin-left: 8px;\n    display: inline-block;\n    margin-bottom: -2px;\n    height: 22px;\n    width: 22px;\n  }\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,o._)(["\n  font-size: 16px;\n  color: #888888;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding-right: 30px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media screen and (max-width:320px){ // Little screen only\n    padding: 0;\n  }\n  @media "," {\n    font-size:20px;\n  }\n"]);return _templateObject5=function(){return n},n}function _templateObject6(){let n=(0,o._)(['\n  font-size: 14px;\n  font-style: italic;\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  color: #7a7a7a;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media '," {\n    font-size: 17px;\n  }\n"]);return _templateObject6=function(){return n},n}function _templateObject7(){let n=(0,o._)(["\n  font-size: 38px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 64px;\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n  @media "," {\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n"]);return _templateObject7=function(){return n},n}function _templateObject8(){let n=(0,o._)(["\n  color: #161616;\n  letter-spacing: 0.04em;\n  margin-top: 48px;\n  text-transform: uppercase;\n  @media "," {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 22px;\n    font-weight: 500;\n    &:hover {\n      color: var(--brand-color);\n    }\n  }\n"]);return _templateObject8=function(){return n},n}function _templateObject9(){let n=(0,o._)(["\n  font-weight: 400;\n  font-size: 26px;\n  margin: 0;\n  color: #161616;\n  @media "," {\n    font-size: 32px;\n  }\n"]);return _templateObject9=function(){return n},n}let r={topSpace:.096,minBottomSpace:.2788,maxBottomSpace:.4711},l=i.ZP.h1.withConfig({componentId:"sc-a3af5335-0"})(_templateObject(),r.topSpace,r.minBottomSpace,a.U.tablet,r.topSpace,r.minBottomSpace,a.U.desktop,r.topSpace,r.minBottomSpace),c=i.ZP.h2.withConfig({componentId:"sc-a3af5335-1"})(_templateObject1(),n=>n.label,n=>n.color,a.U.tablet,a.U.desktop),p=i.ZP.h2.withConfig({componentId:"sc-a3af5335-2"})(_templateObject2(),n=>n.light?"var(--c-soft-cream)":"var(--soft-dark)",r.maxBottomSpace),s=i.ZP.h2.withConfig({componentId:"sc-a3af5335-3"})(_templateObject3(),a.U.tablet),m=i.ZP.h4.withConfig({componentId:"sc-a3af5335-4"})(_templateObject4(),a.U.tablet),u=i.ZP.p.withConfig({componentId:"sc-a3af5335-5"})(_templateObject5(),a.U.tablet),d=i.ZP.p.withConfig({componentId:"sc-a3af5335-6"})(_templateObject6(),a.U.tablet),f=i.ZP.h1.withConfig({componentId:"sc-a3af5335-7"})(_templateObject7(),a.U.tablet,a.U.desktop),b=i.ZP.p.withConfig({componentId:"sc-a3af5335-8"})(_templateObject8(),a.U.desktop);i.ZP.p.withConfig({componentId:"sc-a3af5335-9"})(_templateObject9(),a.U.tablet)},4440:function(n,t,e){function getDotColor(n){let t={formation:{front:"#262424",back:"#FFFFFF"},emailJourney:{front:"#AA2DD6",back:"#FFFFFF"},article:{front:"#000000",back:"#FFFFFF"},default:{front:"#000000",back:"#FFFFFF"}};return t[n]?t[n]:t.default}function hexToRgb(n){let t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(n);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null}e.d(t,{Q:function(){return getDotColor},o:function(){return hexToRgb}})},785:function(n,t,e){e.d(t,{DG:function(){return menuAsObj},MS:function(){return getModuleWithShortName},O9:function(){return getChannelSlug},fw:function(){return modulesAsObj}});let o={lead:"ComponentModuleLead",webinar:"ComponentModuleWebinar",podcast:"ComponentModulePodcast",journey:"ComponentModuleEmailJourney",formation:"ComponentModuleFormation",seo:"ComponentModuleSeo"};function getModuleWithShortName(n,t){return n.find(function(n){return n.__typename===o[t]})}function modulesAsObj(n){if(!n)return null;let t={};return n.forEach(n=>{switch(n.__typename){case"ComponentModuleLead":t.lead=n;break;case"ComponentModuleWebinar":t.webinar=n;break;case"ComponentModuleEmailJourney":t.journey=n;break;case"ComponentModuleEvent":t.event=n;break;case"ComponentModuleFormation":t.formation=n;break;case"ComponentModuleSeo":t.seo=n;break;case"ComponentModulePodcast":t.podcast=n}}),t}function menuAsObj(n){if(!n.length)return null;let t={groups:[],singles:[]};return n.forEach(n=>{if(n.label.includes("/")){let e=n.label.split("/")[0];for(let o of(t.groups.some(n=>n.name===e)||t.groups.push({name:e,items:[]}),t.groups))if(o.name===e){o.items.push({label:n.label.split("/")[1],value:n.value,type:n.type});break}}else t.singles.push(n)}),t}function getChannelSlug(n,t){let{webinar:e}=getModuleWithShortName(n.modules,t);return(null==e?void 0:e.slug)||null}}}]);