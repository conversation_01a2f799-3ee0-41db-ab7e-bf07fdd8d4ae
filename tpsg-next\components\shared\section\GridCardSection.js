import styled from "styled-components";
import { device } from "styles/device";

export default function GridCardSection({ nameSection, children }) {
  const toId = (name) =>
    name
      .normalize("NFD") // Normalize accented characters
      .replace(/[\u0300-\u036f]/g, "")
      .toLowerCase()
      .replace(/\s+/g, "-")
      .replace(/[^\w-]/g, "");

  return (
    <CardSection id={toId(nameSection)}>
      <h2 className="page-section-title">{nameSection}</h2>
      <div className="all-post-card">{children}</div>
    </CardSection>
  );
}

const CardSection = styled.section`
  margin-top: 96px;
  width: 100%;
  border-top: 1px solid black;

  .page-section-title {
    margin: 16px 0px 12px 0px;
    font-size: 28px;
  }
  .all-post-card {
    display: grid;
    grid-template-columns: 1fr;
    column-gap: 16px;
  }

  &:last-child {
    margin-bottom: 96px;
  }

  @media ${device.tablet} {
    .page-section-title {
      margin: 32px 0px;
      font-size: 46px;
    }
    .all-post-card {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media ${device.desktop} {
    .all-post-card {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media ${device.desktopXL} {
    .all-post-card {
      grid-template-columns: repeat(4, 1fr);
    }
  }
`;
