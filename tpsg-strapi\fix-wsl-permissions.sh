#!/bin/bash
# Script pour améliorer les permissions WSL sur les fichiers Windows

echo "Configuration des permissions WSL pour les fichiers Windows..."

# Créer ou modifier le fichier /etc/wsl.conf
cat << EOF | sudo tee /etc/wsl.conf
[automount]
enabled = true
options = "metadata,umask=22,fmask=11"
mountFsTab = false

[network]
generateHosts = true
generateResolvConf = true
EOF

echo "Configuration terminée. Vous devez redémarrer WSL pour appliquer les changements."
echo "Pour redémarrer WSL, exécutez la commande suivante dans PowerShell (en tant qu'administrateur):"
echo "wsl --shutdown"
echo "Puis relancez WSL."
