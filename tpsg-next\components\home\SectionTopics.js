import styled from "styled-components";
import { device } from "styles/device";
import DefaultPostCard from "components/shared/Card/DefaultCard";
import Link from "next/link";
import { slugify } from "../../utils/string.utils";

function RenderTopic({ topic }) {

  const { name, posts } = topic;

  return (
    <div className={"topic-wrapper"}>
      <Link href={`/categories/${slugify(name)}`}>
        <div className={"topic-head"}>
          <h2 className={"topic-title"}>{name}</h2>
        </div>
      </Link>
      {posts.slice().reverse().map((post, key) =>
        <DefaultPostCard
          key={key}
          post={post}
          options={{
            showTopics: true,
            showDate: false,
            showLead: false,
          }}/>
      )}
    </div>
  )
}

export default function SectionTopics({ data }) {
  return (
    <Section>
      {data.map((topic, key) => <RenderTopic key={key} topic={topic}/>)}
    </Section>
  )
}

const Section = styled.section`
  position: relative;
  grid-column: 1/13;
  grid-row: 1;

  .topic-head {
    position: relative;
    border-top: 1px solid #161616;
    border-bottom: 1px solid #161616;
    padding-top: 10px;
    padding-bottom: 2px;
    margin-bottom: 60px;
    margin-top: 90px;
    
    &:hover {
      * {
        color: var(--c-brand-light);
      }
    }
  }

  .topic-title {
    margin: 0;
    font-size: 22px;
    font-weight: 500;

    &::after {
      margin: 0;
      position: absolute;
      font-weight: 400;
      top: 10px;
      right: 0;
      content: '→';
      color: black;
      font-size: 22px;
    }
  }

  @media ${device.desktop} {
    grid-column: 1/7;

    .topic-head {
      width: 100%;
      border-top: 1px solid #161616;
      border-bottom: 1px solid #161616;
      padding-top: 14px;
      padding-bottom: 4px;
      margin-bottom: 60px;
      margin-top: 90px;
    }

    .topic-title {
      margin: 0;
      font-size: 32px;
      font-weight: 500;
      letter-spacing: 2%;
      color: #242424;

      &::after {
        margin: 0;
        position: absolute;
        font-weight: 400;
        top: 14px;
        right: 0;
        content: '→';
        color: black;
        font-size: 32px;
      }
    }

  }
`
