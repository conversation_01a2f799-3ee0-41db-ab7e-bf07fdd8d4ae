import styled from "styled-components";
import Link from "next/link";
import { device } from "styles/device";

export default function PaginationNumber({ value, active, baseUrl }) {
  if (!active) {
    return (
      <Link href={baseUrl + value}>
        <Number>
          {value}
        </Number>
      </Link>
    )
  } else {
    return (
      <Number active>
        {value}
      </Number>
    )
  }
}

const Number = styled.div`
  display: inline-block;
  height: 42px;
  width: 42px;
  font-size: 26px;
  padding-top: 10px;
  font-family: Stelvio, sans-serif;
  border-radius: 100px;
  text-align: center;
  color: #080808;
  background-color: inherit;
  border: 1px solid ${props => props.active ? "black" : "transparent"};

  &:hover {
    background-color: transparent;
    border: 1px solid black;
  }
  
  @media ${device.tablet} {
    margin: auto 10px;
  }
  @media ${device.desktop} {
    
  }
`;
