'use strict';

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */

const authorSlugs = [
    "matthieu-giralt",
    "florent-varak",
    "samuel-laurent",
    "dominique-angers",
    "benjamin-eggen",
    "jonathan-meyer",
    "raphael-charrier",
    "stephane-kapitaniuk"
]

module.exports = {
    async init() {
        for(const slug of authorSlugs){
            let author = await strapi.query("author").find({slug: slug});
            let newEntry = await strapi.query("blog").create({
                slug: slug,
                blogger: author[0].id
            });
        }
    }
};
