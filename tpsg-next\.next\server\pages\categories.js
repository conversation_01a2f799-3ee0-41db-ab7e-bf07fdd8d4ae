"use strict";(()=>{var e={};e.id=3161,e.ids=[3161,2888],e.modules={9540:(e,t,i)=>{i.r(t),i.d(t,{config:()=>M,default:()=>_,getServerSideProps:()=>I,getStaticPaths:()=>Z,getStaticProps:()=>z,reportWebVitals:()=>U,routeModule:()=>F,unstable_getServerProps:()=>R,unstable_getServerSideProps:()=>E,unstable_getStaticParams:()=>D,unstable_getStaticPaths:()=>T,unstable_getStaticProps:()=>G});var r={};i.r(r),i.d(r,{default:()=>Categories,getStaticProps:()=>getStaticProps});var s=i(7093),o=i(5244),n=i(1323),a=i(779),l=i(4033),c=i(997),p=i(9114),d=i(1385),x=i(7518),u=i.n(x),m=i(9667),h=i(6453),g=i(7467),C=i(2556);let renderChildren=e=>c.jsx("div",{className:"ministries",children:e?.map((e,t)=>c.jsx(C.Z,{image:e.cover,text:e.name,route:`/categories/${e.type}/${e.slug}`},t))});function SectionVocations({groups:e}){let t=e.filter(e=>e.children.length>0);return c.jsx(f,{children:c.jsx("div",{children:t?.map((e,t)=>c.jsxs("div",{children:[c.jsx(C.Z,{image:e.cover,text:e.name,route:`/categories/vocation/${e.slug}`}),e.children&&renderChildren(e.children)]},t))})})}let f=u().section.withConfig({componentId:"sc-156c3f87-0"})`
  .ministries {
    margin-left: 24px;
  }
  @media ${g.U.tablet} {
    .ministries {
      margin-left: 40px;
    }
  }
  @media ${g.U.desktop} {
    flex-direction: row;
  }
`;var v=i(1664),b=i.n(v),j=i(9785),w=i(6689);let NumbersSVG=()=>(0,c.jsxs)("svg",{width:"1400",height:"200",viewBox:"0 0 1400 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[c.jsx("path",{opacity:"0.01",fillRule:"evenodd",clipRule:"evenodd",d:"M0 0H1400V200H0V0Z",fill:"#D9D9D9"}),c.jsx("path",{d:"M104.18 14H119V186H94.9798V48.01L93.7504 47.7147C92.1206 50.9085 89.0265 53.3544 85.2076 55.2186C81.3947 57.0797 76.9229 58.3295 72.6351 59.1661C68.3516 60.002 64.2771 60.4206 61.2715 60.6299C59.7694 60.7346 58.5363 60.7868 57.6798 60.8129C57.4165 60.821 57.1889 60.8265 57 60.8303V39.134C58.3775 39.3073 59.5765 39.3072 60.7392 39.3072H60.7533C81.8748 39.3072 99.2009 31.1452 104.18 14Z",stroke:"#333333"}),c.jsx("path",{d:"M307.465 139.468L307.463 139.469L278.462 165.653L277.208 166.786H278.901H362V186H237V172.371L285.019 130.996C297.746 120.074 309.18 110.181 317.424 99.9347C325.68 89.674 330.78 79.0069 330.78 66.5286C330.78 55.7471 327.763 47.6555 322.003 42.2665C316.251 36.8842 307.865 34.2957 297.332 34.2957C284.372 34.2957 274.277 40.2097 268.059 49.4299C261.972 58.4548 259.623 70.6087 261.857 83.424H239.874C236.414 64.086 240.475 46.7423 250.577 34.2405C260.768 21.6292 277.168 13.8678 298.412 14.0017H298.416C316.323 14.0017 330.72 18.9621 340.633 27.8708C350.539 36.7725 356.037 49.6782 356.037 65.7188C356.037 80.6471 350.018 93.604 340.923 105.58C331.819 117.568 319.671 128.527 307.465 139.468Z",stroke:"#333333"}),c.jsx("path",{d:"M537.102 97.0419V97.042L535.845 97.7305L537.189 98.2275V98.2276L537.192 98.2286L537.205 98.2333L537.258 98.2539C537.306 98.2727 537.38 98.3014 537.477 98.3405C537.669 98.4186 537.955 98.5379 538.32 98.7003C539.051 99.0254 540.1 99.5225 541.359 100.209C543.88 101.582 547.24 103.708 550.599 106.714C557.311 112.722 564 122.229 564 136.304C564 155.287 555.789 167.667 543.516 175.334C531.199 183.027 514.745 186 498.278 186C478.288 186 462.801 180.794 452.282 170.906C441.89 161.137 436.255 146.711 436 127.948H459.82C460.039 140.679 462.84 150.207 469.02 156.568C475.311 163.041 484.973 166.106 498.542 166.106C509.253 166.106 519.455 165.252 526.986 161.09C530.77 158.998 533.883 156.07 536.043 152.01C538.2 147.955 539.388 142.809 539.388 136.304V136.293V136.282C539.186 130.293 537.774 125.571 535.424 121.856C533.073 118.14 529.809 115.474 525.971 113.559C518.325 109.746 508.342 108.887 498.56 108.622H498.551H498.542C493.519 108.622 489.209 109.096 485.187 109.594L485.435 87.4912C489.254 87.9632 493.536 88.1982 498.278 88.1982C507.556 88.1982 516.562 87.1411 523.264 83.2406C526.628 81.2824 529.413 78.6068 531.352 74.9985C533.29 71.3936 534.364 66.8934 534.364 61.3108V61.299V61.2872C533.96 50.117 529.846 43.2038 523.28 39.1257C516.773 35.0837 507.957 33.8935 498.278 33.8935C485.889 33.8935 477.269 36.8306 471.706 43.261C466.254 49.5621 463.857 59.0859 463.529 72.0526H439.706C440.074 52.7432 445.131 38.3144 454.775 28.6823C464.528 18.9415 479.078 14 498.542 14C516.598 14 531.577 17.2391 542.094 24.7437C552.572 32.2208 558.714 43.9999 558.976 61.3207V61.3253C559.235 73.0239 553.791 81.9435 548.24 87.9633C545.467 90.9719 542.675 93.246 540.577 94.7673C539.53 95.5275 538.656 96.0987 538.047 96.4788C537.742 96.6688 537.504 96.8108 537.342 96.9048C537.261 96.9518 537.2 96.9867 537.159 97.0096L537.115 97.0349L537.105 97.0407L537.102 97.0418V97.0419Z",stroke:"#333333"}),c.jsx("path",{d:"M721.05 146.213V145.563H720.4H627V119.491L714.655 14H745.325V125.308V125.958H745.975H772V145.563H745.975H745.325V146.213V186H721.05V146.213ZM721.05 40.8575V39.0562L719.9 40.4419L649.774 124.892L648.889 125.958H650.275H720.4H721.05V125.308V40.8575Z",stroke:"#333333"}),c.jsx("path",{d:"M862.967 82.4934L862.746 84.5929L864.106 82.9826V82.9824L864.107 82.9814L864.112 82.9747L864.139 82.9446C864.146 82.9364 864.155 82.9268 864.164 82.9161C864.186 82.8908 864.215 82.8584 864.251 82.8195C864.351 82.7082 864.504 82.5424 864.71 82.3301C865.122 81.9054 865.745 81.2942 866.582 80.5586C868.255 79.0873 870.781 77.1192 874.174 75.1492C880.96 71.2109 891.222 67.261 905.104 67.261C923.291 67.261 938 72.8944 948.161 82.9902C958.322 93.0854 964 107.708 964 125.819C964 144.186 957.792 159.214 946.791 169.652C935.788 180.093 919.93 185.998 900.53 186C873.755 185.461 857.857 176.001 848.533 163.814C839.393 151.87 836.501 137.215 836 125.514L860.03 129.618C861.376 151.351 878.851 165.672 900.793 165.672C912.25 165.672 921.986 161.975 928.861 155.283C935.74 148.587 939.701 138.948 939.701 127.171C939.701 115.126 935.673 105.216 928.662 98.3188C921.652 91.4227 911.713 87.5887 899.985 87.5887C884.341 87.5887 871.273 94.5556 864.865 106.987L837.647 102.566C837.661 102.438 837.676 102.292 837.694 102.128C837.768 101.457 837.874 100.483 838.011 99.2461C838.283 96.7735 838.673 93.2558 839.14 89.0421C840.075 80.6146 841.321 69.4031 842.567 58.2C843.813 46.997 845.059 35.8024 845.994 27.4086L847.123 17.2675L847.439 14.4268L847.487 14H953.493V33.2463H868.731H868.148L868.086 33.8285L862.967 82.4934Z",stroke:"#333333"}),c.jsx("path",{d:"M1237.5 34.7492V35.2492H1238H1334.27C1304.28 70.6968 1285.77 130.921 1284.97 185.993L1284.96 186.5H1285.47H1310.14H1310.64V186C1310.64 122.767 1329.65 72.393 1366.28 35.0996L1366.3 35.0799L1366.32 35.0582L1367.39 33.6931L1367.5 33.5571V33.3841V14V13.5H1367H1238H1237.5V14V34.7492Z",stroke:"#333333"}),c.jsx("path",{d:"M1063.85 88.4617H1064.15L1064.34 88.2332C1071.86 79.3816 1086.45 70.9725 1107.91 70.9725C1140.51 70.9725 1164 96.8831 1164 129.149C1164 163.443 1136.91 186 1103.7 186C1079.63 186 1062.87 175.836 1052.04 160.342C1041.19 144.817 1036.26 123.896 1036 102.381C1036 79.3952 1040.41 57.2838 1051.29 40.9384C1062.15 24.631 1079.49 14 1105.54 14C1129.69 14 1153.39 24.5765 1161.67 47.0575L1136.86 52.5961C1132.83 39.8889 1119.75 33.897 1106.86 33.897C1091.75 33.897 1080.61 40.0155 1073.26 49.6201C1065.93 59.2033 1062.41 72.2073 1062.41 85.9558V87.8107V88.4617H1063.05H1063.85ZM1064.78 129.149C1064.78 151.277 1081.74 166.103 1102.9 166.103C1113.6 166.103 1122.75 162.541 1129.22 156.11C1135.68 149.678 1139.44 140.427 1139.44 129.149C1139.44 117.607 1135.62 108.092 1129.02 101.461C1122.42 94.8306 1113.07 91.1345 1102.11 91.1345C1091.4 91.1345 1082.06 94.8326 1075.4 101.458C1068.73 108.087 1064.78 117.601 1064.78 129.149Z",stroke:"#333333"})]}),N1Theme=({topic:e,id:t})=>(0,c.jsxs)("div",{id:`theme-${t}`,className:"main-topic-wrapper",children:[c.jsx("h3",{children:e.name}),c.jsx(N2List,{topics:e.children})]}),N2List=({topics:e})=>c.jsx("div",{className:"topic-list-wrapper",children:c.jsx("div",{children:e.map((e,t)=>e.postCount>0&&c.jsx(N2Theme,{topic:e},t))})}),N2Theme=({topic:e})=>(0,c.jsxs)(c.Fragment,{children:[c.jsx("li",{className:"topic-lvl-2",children:(0,c.jsxs)(b(),{href:`/categories/${e.slug}`,children:[e.name," ",c.jsx("span",{children:e.postCount})]})}),e.children?.length>0&&c.jsx("ul",{children:e.children.map((e,t)=>e.postCount>0&&c.jsx("li",{className:"topic-lvl-3",children:(0,c.jsxs)(b(),{href:`/categories/${e.slug}`,children:[e.name," ",c.jsx("span",{children:e.postCount})]})},t))})]});function MainList({topics:e}){let[t,i]=(0,w.useState)(0),[r,s]=(0,j.useInView)(),[o,n]=(0,j.useInView)(),[a,l]=(0,j.useInView)(),[p,d]=(0,j.useInView)(),[x,u]=(0,j.useInView)(),[m,h]=(0,j.useInView)(),[g,C]=(0,j.useInView)(),getRef=e=>{switch(e){case 0:return r;case 1:return o;case 2:return a;case 3:return p;case 4:return x;case 5:return m;case 6:return g;default:return null}};return(0,w.useEffect)(()=>{let e=t;s&&(e=0),n&&(e=1),l&&(e=2),d&&(e=3),u&&(e=4),h&&(e=5),C&&(e=6),i(e)},[t,s,n,l,d,u,h,C]),(0,c.jsxs)(y,{children:[(0,c.jsxs)(V,{children:[c.jsx("div",{className:"anchor-list",children:c.jsx("ul",{children:e.map((e,t)=>c.jsx("li",{children:(0,c.jsxs)("a",{href:`#theme-${t}`,children:[c.jsx("span",{className:"list-number",children:`${t+1}`}),e.name]})},t))})}),c.jsx(H,{position:t,children:c.jsx("div",{className:"number-container",children:c.jsx(NumbersSVG,{})})})]}),c.jsx(L,{children:e.map((e,t)=>(0,c.jsxs)("div",{children:[c.jsx("div",{className:"section-marker",ref:getRef(t)}),c.jsx(N1Theme,{topic:e,id:t})]},t))})]})}let V=u().div.withConfig({componentId:"sc-ec7745a2-0"})`
  display: none;

  @media ${g.U.desktop} {
    display: initial;
    padding: 0;
    grid-column: 1 / span 4;
    top: 0;
    height: 80vh;
    position: -webkit-sticky;
    position: sticky;
  }

  @media ${g.U.desktop} {
    visibility: visible;
  }
  
  .anchor-list {
    position: relative;
    font-family: Stelvio, sans-serif;
    font-size: 20px;
    font-weight: 400;

    .list-number {
      display: inline-block;
      color: #f1f1e8;
      width: 40px;
    }

    ul {
      margin-top: 64px;
      padding: 0;
    }

    li {
      font-family: Switzer, sans-serif;
      list-style: none;
      color: rgba(249, 246, 241, 0.6);
      margin: 16px 0 0 0;
      padding: 0;
    }
  }
`,L=u().div.withConfig({componentId:"sc-ec7745a2-1"})`
  position: relative;
  color: rgba(249, 246, 241, 0.85);
  mix-blend-mode: exclusion;
  grid-column: 1 / span 12;

  @media ${g.U.desktop} {
    margin-top: 64px;
    padding: 0;
    grid-column: 5 / span 8;
  }

  h3 {
    font-size: 32px;
    margin-bottom: 16px;
    font-weight: 500;

    @media ${g.U.desktop} {
      font-size: clamp(24px, 3vw, 38px);
      max-width: 45%;
      margin-top: 32px;
    }
  }

  .section-marker {
    position: absolute;
    margin-top: 360px;
    height: 100%;
  }

  .main-topic-wrapper {
    scroll-margin-top: 64px;
    @media ${g.U.desktop} {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      border-top: 1px solid rgba(248, 248, 243, 0.18);
    }
  }

  .topic-list-wrapper {
    ul {
      margin: 0;
      padding: 0;
    }

    li {
      font-family: Switzer, sans-serif;
      list-style: none;

      span {
        float: right;
      }

      //border-bottom: 1px solid rgba(248, 248, 243, 0.2);
      &:hover {
        color: var(--c-brand-lighter)
      }
    }

    li:last-child {
      border: none;
      margin-bottom: 8px;
    }

    .topic-lvl-2 {
      padding-top: 24px;
      padding-bottom: 16px;
      font-size: 20px;
      font-weight: 600;
    }

    .topic-lvl-3 {
      padding: 12px 0;
      font-size: 18px;
      font-weight: 400;
      color: rgba(249, 246, 241, 0.9);
    }

    @media ${g.U.desktop} {
      width: calc(50% - 40px);
      margin: 16px 0;
    }
  }
`,H=u().div.withConfig({componentId:"sc-ec7745a2-2"})`
  position: absolute;
  width: 80%;
  aspect-ratio: 1/1;
  left: 25vw;
  bottom: -10%;
  z-index: -1;
  transition: all 450ms ease-in-out;
  border-radius: 100%;
  overflow: hidden;

  .number-container {
    position: relative;
    margin-top: 11%;
    margin-left: ${e=>(e.position,"11%")};
    height: 78%;
    width: 546%;
    left: ${e=>-(78*e.position)}%;
    box-sizing: border-box;
    transform-origin: right;
    transition-duration: 650ms;
    transition-timing-function: cubic-bezier(0.79, 0.43, 0.38, 0.99);
  }

  svg {
    position: relative;
    height: 100%;
    width: 100%;

    path {
      stroke: none;
      fill: #1C373C;
    }
  }
`,y=u().div.withConfig({componentId:"sc-ec7745a2-3"})`
  position: relative;
  padding: 64px var(--border-space) 128px var(--border-space);
  display: grid;
  background-color: var(--blue-dark);
  grid-template-columns: repeat(${e=>e.col?e.col:2}, 1fr);
  column-gap: ${e=>e.gutter?e.gutter:0}px;

  @media ${g.U.desktop} {
    grid-template-columns: repeat(${e=>e.col?e.col:12}, 1fr);
    column-gap: ${e=>e.gutter?64:0}px;
  }
`;var S=i(677),k=i(6641);function Categories(e){let{groups:t,topics:i}=e;return i=(0,m.Ay)(i),(0,c.jsxs)(c.Fragment,{children:[c.jsx(k.NextSeo,{title:"TPSG - Th\xe8mes",description:""}),(0,c.jsxs)("div",{className:"site-padding",children:[c.jsx("header",{children:c.jsx(h.V1,{children:"Th\xe8mes"})}),(0,c.jsxs)(q,{children:[c.jsx("div",{className:"list",children:c.jsx(SectionVocations,{groups:t})}),(0,c.jsxs)("div",{className:"description",children:[c.jsx("h2",{className:"title",children:"Vocations"}),c.jsx("p",{children:"Glorifier Dieu dans tous les aspects de notre vie c’est le glorifier dans chacune de nos vocations. Cela veut dire assumer les responsabilit\xe9s qu’il nous confie l\xe0 o\xf9 il nous place: dans notre famille, dans notre \xe9glise, dans notre vie sociale. Concr\xe8tement, glorifier Dieu c’est \xeatre le p\xe8re, le fr\xe8re et le coll\xe8gue que Dieu m’appelle \xe0 \xeatre."})]})]})]}),c.jsx("section",{children:c.jsx(MainList,{topics:i})}),(0,c.jsxs)($,{children:[(0,c.jsxs)("p",{className:"search-section-text",children:["Vous n'avez pas trouv\xe9 ce que vous cherchiez? ",c.jsx("br",{}),"Essayez notre outil de recherche"]}),c.jsx(S.Z,{link:"/recherche",text:"Rechercher",theme:"dark"})]})]})}let q=u().section.withConfig({componentId:"sc-b2cbc2b2-0"})`
  position: relative;
  height: auto;
  margin-bottom: 48px;
  
  display: flex;
  flex-direction: column;
  
  .description {
    font-family: Switzer, sans-serif;
    margin-top: 64px;
    font-size: 16px;
  }
  
  @media ${g.U.desktop} {
    margin-bottom: 96px;
    flex-direction: row;
    .description {
      position: sticky;
      top: 80px;
      margin-top: 0;
      max-height: 200px;
      .title {
        margin-top: -8px;
      }
      margin-bottom: 14px;
      width: 33%;
      line-height: 23px;
      color: #161616;
    }
    .list {
      width: 66%;
      margin-right: 80px;
    }
  }
`,$=u().section.withConfig({componentId:"sc-b2cbc2b2-1"})`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 600px;
  background-color: #F9F1E6;
  
  .search-section-text {
    text-align: center;
    font-family: Stelvio, sans-serif;
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 32px;
  }
`;async function getStaticProps(){return{props:{groups:await d.Z.query({query:N}).then(e=>e.data.topicGroups),topics:await d.Z.query({query:P}).then(e=>e.data.topics)},revalidate:10}}let N=p.gql`
    query Groups{
        topicGroups{
            name
            slug
            description
            cover{
                url
                height
                width
                provider
                formats
            }
            children {
                id
                name
                slug
                type
            }
            type
        }
    }
`,P=p.gql`
    query Topics{
        topics(limit: 200){
            name
            slug
            id
            postCount
            parent {
                id
                name
                slug
            }
        }
    }
`,_=(0,n.l)(r,"default"),z=(0,n.l)(r,"getStaticProps"),Z=(0,n.l)(r,"getStaticPaths"),I=(0,n.l)(r,"getServerSideProps"),M=(0,n.l)(r,"config"),U=(0,n.l)(r,"reportWebVitals"),G=(0,n.l)(r,"unstable_getStaticProps"),T=(0,n.l)(r,"unstable_getStaticPaths"),D=(0,n.l)(r,"unstable_getStaticParams"),R=(0,n.l)(r,"unstable_getServerProps"),E=(0,n.l)(r,"unstable_getServerSideProps"),F=new s.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/categories",pathname:"/categories",bundlePath:"",filename:""},components:{App:l.default,Document:a.default},userland:r})},9114:e=>{e.exports=require("@apollo/client")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},9785:e=>{e.exports=require("react-intersection-observer")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),i=t.X(0,[3181,5016,6859,8450,4033,779,6453,6920],()=>__webpack_exec__(9540));module.exports=i})();