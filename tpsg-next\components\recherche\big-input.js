import styled from "styled-components";
import { useEffect, useState } from "react";
import getMatchingFilter from "services/matchingFilter";
import { device } from "styles/device"

const BigInput = ({ listState, changeFilter, changeQueryTerms, autoFocus = false }) => {

  const [matchingFilter, setMatchingFilter] = useState("");
  const [inputValue, setInputValue] = useState(listState.queryTerms);
  const [rulerString, setRulerString] = useState("");
  const [rulerWidth, setRulerWidth] = useState(0);
  
  const handleTextChange = (text) => {
    setInputValue(text);
    if (text.length >= 3 && !listState.activeFilter) {
      let mf = getMatchingFilter(text);
      if (mf.value) {
        mf.displayValue = text + mf.value.slice(text.length)
      }
      setMatchingFilter(mf);
    } else {
      setMatchingFilter(null);
    }
  }

  const handleKeyDown = (e) => {
    // Tab is pressed
    if (e.keyCode === 9) {
      e.preventDefault();
      if (matchingFilter) {
        addFilter();
      }
    }
    // Backspace is pressed
    if (e.keyCode === 8 && inputValue.length === 0) {
      changeFilter(null, null);
      changeQueryTerms("");
    }
    if (e.key === "Enter") {
      changeQueryTerms(inputValue);
    }
  }


  const addFilter = () => {
    setInputValue("") // Reset du text de l'input
    changeQueryTerms("");
    changeFilter(matchingFilter.value, matchingFilter.type, true)
    setMatchingFilter(null) // Reset de l'autocomplete
  }

  const updateCaret = () => {
    let input = document.getElementById("input");
    setRulerString(
      inputValue
        .replaceAll(" ", "!")
        .slice(0, input.selectionStart));
  }

  useEffect(() => {
    updateCaret()
  }, [inputValue])

  useEffect(() => {
    let ruler = document.getElementById("ruler");
    if (ruler) {
      let width = ruler.offsetWidth;
      setRulerWidth(width);
    }
  }, [rulerString])

  return (
    <Wrapper>
      {matchingFilter?.displayValue &&
        <div className={"filter-label"} onClick={() => addFilter()}>AJOUTER: CLICK ou TAB</div>
      }
      <p id={"ruler"}>{rulerString} <Caret width={rulerWidth}/></p>
      <p>{matchingFilter?.displayValue || ""}</p>
      <input
        id={"input"}
        autoFocus={autoFocus}
        onKeyUp={(e) => updateCaret()}
        onKeyDown={(e) => handleKeyDown(e)}
        onClick={() => updateCaret()}
        onChange={(e) => handleTextChange(e.target.value)}
        placeholder={"Rechercher"}
        value={inputValue}/>
      <SearchSVG/>
    </Wrapper>
  )
}

export default BigInput;


const Caret = styled.span`
  position: absolute;
  top: 8px;
  left: 0;
  display: none;
  width: ${props => props.width}px;
  height: 80%;
  border-right-style: solid;
  border-right-color: #FF856A;
  border-right-width: ${props => props.width === 0 ? "3px" : "36px"};
  transition: width 300ms cubic-bezier(.55, .77, .17, .97);

  @media ${device.tablet} {
    //display: inline-block;
  }
`;

const Wrapper = styled.div`
  position: relative;
  margin-top: 16px;
  width: 100%;
  height: 48px;
  background-color: transparent;

  @media ${device.tablet} {
    height: 92px;
  }

  #ruler {
    position: absolute;
    font-family: Stelvio, Arial, sans-serif;
    font-weight: 500;
    font-size: 72px;
    height: 72px;
    color: transparent;
    padding-top: 8px;
  }

  input {
    position: absolute;
    top: 0;
    box-sizing: border-box;
    @media ${device.tablet} {
      //caret-color: transparent;
      font-size: 72px;
      height: auto;
    }
    padding-top: 0;
    margin-top: 0;
    padding-left: 0;
    width: 100%;
    font-family: Stelvio, Arial, sans-serif;
    font-weight: 500;
    font-size: 32px;
    //border: 1px solid green;
    border: none;
    caret-color: #F45D3C;
    background-color: transparent;
    z-index: 10;

    &:focus {
      outline: none;
      background-color: transparent;
    }

    &::placeholder {
      color: rgba(244, 93, 60, 0.25);
    }
  }

  // Permet de cacher le bas du caret 
  // (trop long avec la font Stelvio)
  &:after {
    content: '';
    display: inline-block;
    box-sizing: border-box;
    position: absolute;
    bottom: 0;
    left: 0;
    height: 12px;
    width: 100%;
    background-color: var(--soft-white);
    z-index: 20;

    @media ${device.tablet} {
      height: 20px;
    }
  }

  p {
    position: absolute;
    margin-top: 0;
    margin-bottom: 0;
    height: 48px;
    width: 100%; // à suprimer pour faire fonctionner le caret
    top: 0;
    left: 0;
    font-family: Stelvio, Arial, sans-serif;
    font-weight: 500;
    font-size: 32px;
    color: rgba(244, 93, 60, 0.5);
    z-index: 0;

    @media ${device.tablet} {
      font-size: 72px;
    }
  }

  .search-btn {
    position: absolute;
    top: 0;
    right: 4px;
    z-index: 100;

    svg {
      //background-color: rgba(138, 43, 226, 0.76);
      height: 24px;
      width: 24px;
    }

    @media ${device.tablet} {
      top: 12px;
      right: 10px;
      svg {
        height: 45px;
        width: 45px;
      }
    }
  }

  .filter-label {
    position: absolute;
    left: 0;
    padding: 4px 8px 4px 8px;
    color: #f4f4f4;
    background-color: black;
    top: -36px;
    font-size: 0.65rem;
    font-weight: 400;
    font-family: Arial, sans-serif;

    &:before {
      content: '';
      position: absolute;
      top: 100%;
      left: 0;
      transform: rotate(90deg);
      border-bottom: 12px solid transparent;
      border-left: 12px solid #121212;
    }

    @media ${device.tablet} {
      font-size: 0.75rem;
      top: -46px;
      padding: 5px 10px 5px 10px;
      &:before {
        border-bottom: 18px solid transparent;
        border-left: 18px solid #121212;
      }
    }
  }
`;

const SearchSVG = () => {
  return (
    <div className="search-btn">
      <svg width="50" height="50" viewBox="0 0 50 49" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M35.0234 36.1094L47.8259 48.912L49.2401 47.4977L36.4376 34.6952L35.0234 36.1094Z" fill="black"/>
        <circle cx="21.6367" cy="21.3087" r="20.1035" stroke="black" strokeWidth="2"/>
      </svg>
    </div>
  )
}
