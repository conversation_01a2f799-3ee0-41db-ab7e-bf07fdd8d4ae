import styled from "styled-components";
import { device } from "styles/device";
import { PageTitle } from "../../styles/styled-typography";
import { useState } from "react";
import Link from "next/link";

import {
  PodcastChretienne,
  PodcastMemento,
  PodcastPredications,
  PodcastPMM,
  Podcast1PVR,
} from "components/podcast/logos";

const podcasts = [
  {
    title: "Chrétienne",
    logo: PodcastChretienne(),
    colors: {
      logoDest: "#B08925",
      backDest: "#1C4450",
    },
    desc: "Le podcast qui aide les femmes à creuser toujours plus la bible pour mieux servir <PERSON>-<PERSON>. <PERSON><PERSON><PERSON> et <PERSON>z Thornton t'invitent chaque semaine à suivre leurs conversations autour de questions théologiques",
    slug: "chretienne",
    expand: 1.1,
  },
  {
    title: "Memento Mori",
    logo: PodcastMemento(),
    useImage: true,
    colors: {
      logoDest: "#f4f4f4",
      backDest: "#242424",
    },
    desc: "Memento Mori, c'est le podacast hebdomadaire des pasteurs Rap<PERSON>ël <PERSON> et <PERSON>, qui parle du présent en prenant la fin comme point de départ",
    slug: "memento-mori",
    expand: 1.25,
  },
  {
    title: "Parle moi maintenant",
    logo: PodcastPMM(),
    colors: {
      logoDest: "#f4f4f4",
      backDest: "#0E4FCD",
    },
    desc: "'Parle moi maintenant' c'est la prière que fait tout chrétien quand il ouvre sa Bible: il demande à Dieu de lui parler par l'Écriture. Mon désir, c'est que Dieu parle puissament à toutes les personnes qui suivront ce podcast. En plus d'une 'vérité centrale', des applications pratiques sont proposées.",
    slug: "parle-moi-maintenant",
    expand: 1.15,
  },
  {
    title: "Prédications TPSG",
    logo: PodcastPredications(),
    colors: {
      logoDest: "#F6F4F3",
      backDest: "#426755",
    },
    desc: "La plupart des blogueurs de TPSG sont également pasteurs. Aujourd'hui, tu peux toi aussi bénéficier de leurs enseignements grâce à notre nouveau podcast. Ces prédications, qui se veulent résolument textuelles et christocentriques, te feront redécouvrir le sens profond des Écritures et nourriront ta foi en Christ",
    slug: "predications-tpsg",
    expand: 1.15,
  },
  {
    title: "Un pasteur vous répond",
    logo: Podcast1PVR(),
    useImage: false,
    colors: {
      logoDest: "#E63E09",
      backDest: "#E6E1D8",
    },
    desc: "Le podcast de Florent Varak qui t'aide à mieux comprendre la bible une question à la fois. Si tu as une question à adresser à Florent, commence par regarder le sommaire ici: www.upvr.com. Si le sujet n'a pas encore été traité, pose ta question à <EMAIL>",
    slug: "un-pasteur-vous-repond",
    expand: 1.25,
  }
]

export default function Podcasts(){
  return (
    <PageWrapper>
      <PageTitle>Podcasts</PageTitle>
      <PodcastsBoxes>
        <PodcastBox podcast={podcasts[0]}/>
        <PodcastBox podcast={podcasts[1]}/>
        <PodcastBox podcast={podcasts[2]}/>
        <PodcastBox podcast={podcasts[3]}/>
        <PodcastBox podcast={podcasts[4]} last/>
        <VSpace/>
      </PodcastsBoxes>
    </PageWrapper>
  )
}

const PageWrapper = styled.div`
  margin-top: -196px;
  background-color: #f6f4f3;
  padding: 196px var(--border-space) 0 var(--border-space);
`;

const VSpace = styled.div`
  display: none;
  @media ${device.desktop} {
    display: inherit;
    width: calc(50% - 12px);
  }
  @media ${device.desktop} {
    display: inherit;
    width: calc(33.3% - 8px);
  }
`;

const PodcastBox = ({ podcast }) => {

  const [showDesc, setShowDesc] = useState(false);

  const handleMoreClick = (e) => {
    e.stopPropagation();
    setShowDesc(!showDesc);
  }

  return(
    <Link href={`podcasts/${podcast.slug}`} legacyBehavior={true}>
      <PodcastBoxWrapper
        backDest={podcast.colors.backDest}
        logoDest={podcast.colors.logoDest}
        useImage={podcast.useImage}
        expand={podcast.expand}
        showDesc={showDesc}>
        <p className={"desc"}>
          { podcast.desc && podcast.desc }
        </p>
        <div className={"logo"}>
          { podcast.logo && podcast.logo }
        </div>
        <p>{podcast.title}</p>
        <div
          onClick={(e) => handleMoreClick(e) }
          className={"more"}>
          <div className={"more-content"}>infos</div>
        </div>
      </PodcastBoxWrapper>
    </Link>
  )
}

const PodcastsBoxes = styled.div`
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  //margin-top: 128px;
  padding-bottom: 128px;
`

const PodcastBoxWrapper = styled.div`
  position: relative;
  width: 100%;
  aspect-ratio: 1/1;
  background-color: ${props => props.backDest};
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;

  .logo {
    transform: scale(${props => props.expand});
    filter: ${props => props.useImage ? "invert(1)" : "inherit"};
    display: ${props => props.showDesc ? "none" : "inherit"};
    transition: transform 350ms ease-in-out;
  }
  
  .desc {
    visibility: visible;
    position: absolute;
    top: ${props => props.showDesc ? "40px" : "-400px"};
    left: 0;
    margin-left: 40px;
    margin-right: 40px;
  }
  
  p {
    font-size: 16px;
    font-family: "Switzer", Arial, sans-serif;
    position: absolute;
    margin: 0;
    bottom: 40px;
    left: 40px;
    color: ${props => props.logoDest};
  }
  
  .more {
    position: absolute;
    bottom: 40px;
    right: 40px;
    height: 28px;
    //width: 26px;
    padding: 4px 12px 4px 12px;
    border-radius: 48px;
    background-color: #161616;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #f6f4f3;
    //padding-top: 6px;
    font-size: 16px;
    overflow: hidden;
    font-family: "Switzer", sans-serif;
    
    .more-content {
      margin-top: 0;
      top: ${props => props.showDesc ? -42 : 0}px;
      transition: margin-top 400ms ease-in-out;
    }
    
    &:after {
      content: "${props => props.showDesc ? "x" : "infos"}";
      position: absolute;
      top: ${props => props.showDesc ? 0 : 28}px;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 28px;
      width: 100%;
      border-radius: 13px;
      color: #161616;
      background-color: #f6f4f3;
      padding: 0;
      transition: top 400ms ease-in-out;
    }
  }

  svg {
    path {
      fill: ${props => props.logoDest};
    }
  }
  
  
  @media ${device.tablet} {
    width: calc(50% - 8px);

    &:hover {
      box-shadow: 0 0 32px ${props => `${props.backDest}80`};
      .more {
        &:hover {
          .more-content {
            margin-top: -42px;
          }
          &:after {
            top: 0;
          }
        }
      }
    }
  }
  @media ${device.desktop} {
    width: calc(33.3% - 8px);
  }
`;
