function slugify(str) {
  str = str.replace(/^\s+|\s+$/g, "");
  str = str.toLowerCase();
  let from = "ÁÄÂÀÃÅČÇĆĎÉĚËÈÊẼĔȆÍÌÎÏŇÑÓÖÒÔÕØŘŔŠŤÚŮÜÙÛÝŸŽáäâàãåčçćďéěëèêẽĕȇíìîïňñóöòôõøðřŕšťúůüùûýÿžþÞĐđßÆa·/_,:;";
  let to = "AAAAAACCCDEEEEEEEEIIIINNOOOOOORRSTUUUUUYYZaaaaaacccdeeeeeeeeiiiinnooooooorrstuuuuuyyzbBDdBAa------";
  for (let i = 0, l = from.length; i < l; i++) {
    str = str.replace(new RegExp(from.charAt(i), "g"), to.charAt(i));
  }
  // Remove invalid chars
  str = str.replace(/[^a-z0-9 -]/g, "")
    // Collapse whitespace and replace by -
    .replace(/\s+/g, "-")
    // Collapse dashes
    .replace(/-+/g, "-");
  return str;
}

/**
 * Retourne la chaine passée en paramètre sans markdown.
 * Seuls les noms des liens sont conservés, pas les url.
 */
function removeMarkdown(text) {
  return text.replace(/(?:_|[*#])|\[(.*?)\]\(.*?\)/gm, "$1");
}

/**
 * Retourne la chaine passée en paramètre sans balise html
 * en gardant le contenu.
 */
function removeHtml(str) {
  return str.replace(/`|<[^>]*(>|…)/gm, "");
}

/**
 * Retourne true si le lien passé redirige vers un site externe.
 * @param link {string}
 * @returns {boolean}
 */
function isLinkExternal(link) {
  return link.includes(".");
}

const getYouTubeVideoIdFromUrl = (url) => {
  url = url.split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/);
  return (url[2] !== undefined) ? url[2].split(/[^0-9a-z_\-]/i)[0] : url[0];
};

/**
  * Retourne la chaîne passée en paramètre sans le "\" de fin
  * Utiliser pour le body des articles
  */
function removeLastBackSlash(str) {
  return str.replace(/^\\/gm, "");
}

function noHyphen(text) {
  return text?.replace(/-/g, " ") || "";
}

function reverseWords(str) {
  const arr = str.split(" ");
  return [arr.pop(), [...arr].join(" ")].join(" ");
}

export {
  slugify,
  removeHtml,
  removeMarkdown,
  getYouTubeVideoIdFromUrl,
  isLinkExternal,
  removeLastBackSlash,
  noHyphen,
  reverseWords
}
