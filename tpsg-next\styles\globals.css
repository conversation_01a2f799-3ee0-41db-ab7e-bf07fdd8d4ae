@font-face {
  font-family: "Stelvio";
  src: url("../public/fonts/Stelvio/Regular.woff") format('woff');
  font-style: normal;
  font-weight: 400;
  font-display: swap;
}

@font-face {
  font-family: "Stelvio";
  src: url("../public/fonts/Stelvio/medium.woff") format('woff');
  font-style: normal;
  font-weight: 500;
  font-display: swap;
}

@font-face {
  font-family: "Stelvio";
  src: url("../public/fonts/Stelvio/Bold.woff") format('woff');
  font-style: normal;
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: "Stelvio Stylus";
  src: url("../public/fonts/Stelvio/Stylus.woff") format('woff');
  font-style: normal;
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: "Switzer";
  src: url("../public/fonts/Switzer/Regular.woff") format('woff');
  font-style: normal;
  font-weight: 400;
  font-display: swap;
}
@font-face {
  font-family: "Switzer";
  src: url("../public/fonts/Switzer/Medium.woff") format('woff');
  font-style: normal;
  font-weight: 500;
  font-display: swap;
}


a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}
