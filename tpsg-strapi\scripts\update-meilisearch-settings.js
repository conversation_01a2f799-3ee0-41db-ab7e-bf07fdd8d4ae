const { MeiliSearch } = require("meilisearch");

// Connexion à Meilisearch
const client = new MeiliSearch({
  host: process.env.MEILI_HOST || 'http://localhost:7700',
  apiKey: process.env.MEILI_ADMIN_KEY || '',
});

async function updateMeilisearchSettings() {
  try {
    console.log("Mise à jour des paramètres de l'index Meilisearch...");
    
    // Vérifier si l'index existe
    const indexes = await client.getIndexes();
    const postIndex = indexes.results.find(index => index.uid === "post");
    
    if (!postIndex) {
      console.error("L'index 'post' n'existe pas. Veuillez d'abord créer l'index.");
      return;
    }
    
    // Mettre à jour les paramètres de l'index
    await client.index("post").updateSettings({
      "filterableAttributes": [
        "title",
        "topics",
        "tags",
        "author",
        "type",
        "blog",
        "cs",
      ],
      "sortableAttributes": ["date"],
      "pagination": {
        "maxTotalHits": 4000
      },
      "searchableAttributes": [
        "title",
        "body",
        "lead"
      ]
    });
    
    console.log("Paramètres de l'index mis à jour avec succès !");
  } catch (error) {
    console.error("Erreur lors de la mise à jour des paramètres :", error);
  }
}

// Exécuter la fonction
updateMeilisearchSettings();
