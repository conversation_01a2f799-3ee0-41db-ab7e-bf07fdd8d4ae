{"kind": "collectionType", "collectionName": "podcasts", "info": {"name": "podcast", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "slug": {"type": "uid", "targetField": "name"}, "description": {"type": "text", "required": true}, "cover": {"model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}}, "speakers": {"collection": "author"}, "count": {"type": "integer"}, "platforms": {"type": "component", "repeatable": true, "component": "atom.platform"}, "averageDuration": {"type": "integer", "default": 0, "required": false, "min": 0, "max": 999}, "logoSmall": {"model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}}}}