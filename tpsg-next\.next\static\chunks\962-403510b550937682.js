"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[962],{2962:function(e,t,o){o.d(t,{PB:function(){return i}});var a=o(9008),r=o.n(a),n=o(7294);function _extends(){return(_extends=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(e[a]=o[a])}return e}).apply(this,arguments)}var p={templateTitle:"",noindex:!1,nofollow:!1,defaultOpenGraphImageWidth:0,defaultOpenGraphImageHeight:0,defaultOpenGraphVideoWidth:0,defaultOpenGraphVideoHeight:0,disableGooglebot:!1},buildOpenGraphMediaTags=function(e,t,o){void 0===t&&(t=[]);var a=void 0===o?{}:o,r=a.defaultWidth,p=a.defaultHeight;return t.reduce(function(t,o,a){return t.push(n.createElement("meta",{key:"og:"+e+":0"+a,property:"og:"+e,content:o.url})),o.alt&&t.push(n.createElement("meta",{key:"og:"+e+":alt0"+a,property:"og:"+e+":alt",content:o.alt})),o.secureUrl&&t.push(n.createElement("meta",{key:"og:"+e+":secure_url0"+a,property:"og:"+e+":secure_url",content:o.secureUrl.toString()})),o.type&&t.push(n.createElement("meta",{key:"og:"+e+":type0"+a,property:"og:"+e+":type",content:o.type.toString()})),o.width?t.push(n.createElement("meta",{key:"og:"+e+":width0"+a,property:"og:"+e+":width",content:o.width.toString()})):r&&t.push(n.createElement("meta",{key:"og:"+e+":width0"+a,property:"og:"+e+":width",content:r.toString()})),o.height?t.push(n.createElement("meta",{key:"og:"+e+":height"+a,property:"og:"+e+":height",content:o.height.toString()})):p&&t.push(n.createElement("meta",{key:"og:"+e+":height"+a,property:"og:"+e+":height",content:p.toString()})),t},[])},buildTags=function(e){var t,o,a,r,i,l=[];e.titleTemplate&&(p.templateTitle=e.titleTemplate);var h="";e.title?(h=e.title,p.templateTitle&&(h=p.templateTitle.replace(/%s/g,function(){return h}))):e.defaultTitle&&(h=e.defaultTitle),h&&l.push(n.createElement("title",{key:"title"},h));var c=e.noindex||p.noindex||e.dangerouslySetAllPagesToNoIndex,d=e.nofollow||p.nofollow||e.dangerouslySetAllPagesToNoFollow,s=e.disableGooglebot||p.disableGooglebot||e.dangerouslyDisableGooglebot,m="";if(e.robotsProps){var u=e.robotsProps,g=u.nosnippet,f=u.maxSnippet,y=u.maxImagePreview,G=u.maxVideoPreview,k=u.noarchive,b=u.noimageindex,v=u.notranslate,E=u.unavailableAfter;m=(g?",nosnippet":"")+(f?",max-snippet:"+f:"")+(y?",max-image-preview:"+y:"")+(k?",noarchive":"")+(E?",unavailable_after:"+E:"")+(b?",noimageindex":"")+(G?",max-video-preview:"+G:"")+(v?",notranslate":"")}if(e.dangerouslyDisableGooglebot&&(p.disableGooglebot=!0),c||d?(e.dangerouslySetAllPagesToNoIndex&&(p.noindex=!0),e.dangerouslySetAllPagesToNoFollow&&(p.nofollow=!0),l.push(n.createElement("meta",{key:"robots",name:"robots",content:(c?"noindex":"index")+","+(d?"nofollow":"follow")+m})),s||l.push(n.createElement("meta",{key:"googlebot",name:"googlebot",content:(c?"noindex":"index")+","+(d?"nofollow":"follow")+m}))):(l.push(n.createElement("meta",{key:"robots",name:"robots",content:"index,follow"+m})),s||l.push(n.createElement("meta",{key:"googlebot",name:"googlebot",content:"index,follow"+m}))),e.description&&l.push(n.createElement("meta",{key:"description",name:"description",content:e.description})),e.mobileAlternate&&l.push(n.createElement("link",{rel:"alternate",key:"mobileAlternate",media:e.mobileAlternate.media,href:e.mobileAlternate.href})),e.languageAlternates&&e.languageAlternates.length>0&&e.languageAlternates.forEach(function(e){l.push(n.createElement("link",{rel:"alternate",key:"languageAlternate-"+e.hrefLang,hrefLang:e.hrefLang,href:e.href}))}),e.twitter&&(e.twitter.cardType&&l.push(n.createElement("meta",{key:"twitter:card",name:"twitter:card",content:e.twitter.cardType})),e.twitter.site&&l.push(n.createElement("meta",{key:"twitter:site",name:"twitter:site",content:e.twitter.site})),e.twitter.handle&&l.push(n.createElement("meta",{key:"twitter:creator",name:"twitter:creator",content:e.twitter.handle}))),e.facebook&&e.facebook.appId&&l.push(n.createElement("meta",{key:"fb:app_id",property:"fb:app_id",content:e.facebook.appId})),(null!=(t=e.openGraph)&&t.title||e.title)&&l.push(n.createElement("meta",{key:"og:title",property:"og:title",content:(null==(r=e.openGraph)?void 0:r.title)||h})),(null!=(o=e.openGraph)&&o.description||e.description)&&l.push(n.createElement("meta",{key:"og:description",property:"og:description",content:(null==(i=e.openGraph)?void 0:i.description)||e.description})),e.openGraph){if((e.openGraph.url||e.canonical)&&l.push(n.createElement("meta",{key:"og:url",property:"og:url",content:e.openGraph.url||e.canonical})),e.openGraph.type){var w=e.openGraph.type.toLowerCase();l.push(n.createElement("meta",{key:"og:type",property:"og:type",content:w})),"profile"===w&&e.openGraph.profile?(e.openGraph.profile.firstName&&l.push(n.createElement("meta",{key:"profile:first_name",property:"profile:first_name",content:e.openGraph.profile.firstName})),e.openGraph.profile.lastName&&l.push(n.createElement("meta",{key:"profile:last_name",property:"profile:last_name",content:e.openGraph.profile.lastName})),e.openGraph.profile.username&&l.push(n.createElement("meta",{key:"profile:username",property:"profile:username",content:e.openGraph.profile.username})),e.openGraph.profile.gender&&l.push(n.createElement("meta",{key:"profile:gender",property:"profile:gender",content:e.openGraph.profile.gender}))):"book"===w&&e.openGraph.book?(e.openGraph.book.authors&&e.openGraph.book.authors.length&&e.openGraph.book.authors.forEach(function(e,t){l.push(n.createElement("meta",{key:"book:author:0"+t,property:"book:author",content:e}))}),e.openGraph.book.isbn&&l.push(n.createElement("meta",{key:"book:isbn",property:"book:isbn",content:e.openGraph.book.isbn})),e.openGraph.book.releaseDate&&l.push(n.createElement("meta",{key:"book:release_date",property:"book:release_date",content:e.openGraph.book.releaseDate})),e.openGraph.book.tags&&e.openGraph.book.tags.length&&e.openGraph.book.tags.forEach(function(e,t){l.push(n.createElement("meta",{key:"book:tag:0"+t,property:"book:tag",content:e}))})):"article"===w&&e.openGraph.article?(e.openGraph.article.publishedTime&&l.push(n.createElement("meta",{key:"article:published_time",property:"article:published_time",content:e.openGraph.article.publishedTime})),e.openGraph.article.modifiedTime&&l.push(n.createElement("meta",{key:"article:modified_time",property:"article:modified_time",content:e.openGraph.article.modifiedTime})),e.openGraph.article.expirationTime&&l.push(n.createElement("meta",{key:"article:expiration_time",property:"article:expiration_time",content:e.openGraph.article.expirationTime})),e.openGraph.article.authors&&e.openGraph.article.authors.length&&e.openGraph.article.authors.forEach(function(e,t){l.push(n.createElement("meta",{key:"article:author:0"+t,property:"article:author",content:e}))}),e.openGraph.article.section&&l.push(n.createElement("meta",{key:"article:section",property:"article:section",content:e.openGraph.article.section})),e.openGraph.article.tags&&e.openGraph.article.tags.length&&e.openGraph.article.tags.forEach(function(e,t){l.push(n.createElement("meta",{key:"article:tag:0"+t,property:"article:tag",content:e}))})):("video.movie"===w||"video.episode"===w||"video.tv_show"===w||"video.other"===w)&&e.openGraph.video&&(e.openGraph.video.actors&&e.openGraph.video.actors.length&&e.openGraph.video.actors.forEach(function(e,t){e.profile&&l.push(n.createElement("meta",{key:"video:actor:0"+t,property:"video:actor",content:e.profile})),e.role&&l.push(n.createElement("meta",{key:"video:actor:role:0"+t,property:"video:actor:role",content:e.role}))}),e.openGraph.video.directors&&e.openGraph.video.directors.length&&e.openGraph.video.directors.forEach(function(e,t){l.push(n.createElement("meta",{key:"video:director:0"+t,property:"video:director",content:e}))}),e.openGraph.video.writers&&e.openGraph.video.writers.length&&e.openGraph.video.writers.forEach(function(e,t){l.push(n.createElement("meta",{key:"video:writer:0"+t,property:"video:writer",content:e}))}),e.openGraph.video.duration&&l.push(n.createElement("meta",{key:"video:duration",property:"video:duration",content:e.openGraph.video.duration.toString()})),e.openGraph.video.releaseDate&&l.push(n.createElement("meta",{key:"video:release_date",property:"video:release_date",content:e.openGraph.video.releaseDate})),e.openGraph.video.tags&&e.openGraph.video.tags.length&&e.openGraph.video.tags.forEach(function(e,t){l.push(n.createElement("meta",{key:"video:tag:0"+t,property:"video:tag",content:e}))}),e.openGraph.video.series&&l.push(n.createElement("meta",{key:"video:series",property:"video:series",content:e.openGraph.video.series})))}e.defaultOpenGraphImageWidth&&(p.defaultOpenGraphImageWidth=e.defaultOpenGraphImageWidth),e.defaultOpenGraphImageHeight&&(p.defaultOpenGraphImageHeight=e.defaultOpenGraphImageHeight),e.openGraph.images&&e.openGraph.images.length&&l.push.apply(l,buildOpenGraphMediaTags("image",e.openGraph.images,{defaultWidth:p.defaultOpenGraphImageWidth,defaultHeight:p.defaultOpenGraphImageHeight})),e.defaultOpenGraphVideoWidth&&(p.defaultOpenGraphVideoWidth=e.defaultOpenGraphVideoWidth),e.defaultOpenGraphVideoHeight&&(p.defaultOpenGraphVideoHeight=e.defaultOpenGraphVideoHeight),e.openGraph.videos&&e.openGraph.videos.length&&l.push.apply(l,buildOpenGraphMediaTags("video",e.openGraph.videos,{defaultWidth:p.defaultOpenGraphVideoWidth,defaultHeight:p.defaultOpenGraphVideoHeight})),e.openGraph.locale&&l.push(n.createElement("meta",{key:"og:locale",property:"og:locale",content:e.openGraph.locale})),e.openGraph.site_name&&l.push(n.createElement("meta",{key:"og:site_name",property:"og:site_name",content:e.openGraph.site_name}))}return e.canonical&&l.push(n.createElement("link",{rel:"canonical",href:e.canonical,key:"canonical"})),e.additionalMetaTags&&e.additionalMetaTags.length>0&&e.additionalMetaTags.forEach(function(e){var t,o,a;l.push(n.createElement("meta",_extends({key:"meta:"+(null!=(t=null!=(o=null!=(a=e.keyOverride)?a:e.name)?o:e.property)?t:e.httpEquiv)},e)))}),null!=(a=e.additionalLinkTags)&&a.length&&e.additionalLinkTags.forEach(function(e){var t;l.push(n.createElement("link",_extends({key:"link"+(null!=(t=e.keyOverride)?t:e.href)+e.rel},e)))}),l},i=function(e){function NextSeo(){return e.apply(this,arguments)||this}return NextSeo.prototype=Object.create(e.prototype),NextSeo.prototype.constructor=NextSeo,NextSeo.__proto__=e,NextSeo.prototype.render=function(){var e=this.props,t=e.title,o=e.noindex,a=e.nofollow,p=e.robotsProps,i=e.description,l=e.canonical,h=e.openGraph,c=e.facebook,d=e.twitter,s=e.additionalMetaTags,m=e.titleTemplate,u=e.mobileAlternate,g=e.languageAlternates,f=e.additionalLinkTags,y=e.disableGooglebot;return n.createElement(r(),null,buildTags({title:t,noindex:void 0!==o&&o,nofollow:a,robotsProps:p,description:i,canonical:l,facebook:c,openGraph:h,additionalMetaTags:s,twitter:d,titleTemplate:m,mobileAlternate:u,languageAlternates:g,additionalLinkTags:f,disableGooglebot:y}))},NextSeo}(n.Component)}}]);