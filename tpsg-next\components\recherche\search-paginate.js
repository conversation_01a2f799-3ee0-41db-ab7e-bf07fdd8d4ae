import styled from "styled-components";
import { device } from "styles/device";
import paginate from "utils/paginate.utils";

const POST_PER_PAGE = 10

const SearchPaginate = ({ nbHits, currentPage, changePage }) => {

  const pagination = paginate(nbHits, currentPage, POST_PER_PAGE, 5);

  return (
    <Wrapper>
      {pagination.startPage > 1 &&
        <>
          <PaginationNumber
            onClick={() => changePage(1)}>
            {1}
          </PaginationNumber>
          <Dots>...</Dots>
        </>
      }
      {pagination.pages.map((page, key) => (
        <PaginationNumber
          key={key}
          onClick={() => changePage(page)}
          active={page === pagination.currentPage}>
          {page}
        </PaginationNumber>
      ))}
      {pagination.totalPages !== pagination.endPage &&
        <>
          <Dots>...</Dots>
          <PaginationNumber
            active={pagination.currentPage === pagination.endPage}
            onClick={() => changePage(pagination.totalPages)}>
            {pagination.totalPages}
          </PaginationNumber>
        </>
      }
    </Wrapper>
  )
}


export default SearchPaginate;

const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: end;
  height: 40px;
  width: 100%;
  color: white;
  font-size: 26px;
  margin: 72px auto;
  transform: scale(0.8);
  @media ${device.tablet} {
    transform: scale(1);
  }
`;

const Dots = styled.span`
  color: black;
`;

const PaginationNumber = styled.span`
  display: inline-block;
  height: 42px;
  width: 42px;
  margin: auto 10px;
  font-size: 26px;
  font-family: Stelvio, sans-serif;
  border-radius: 100px;
  padding-top: 10px;
  text-align: center;
  color: ${props => props.active ? "white" : "black"};
  background-color: ${props => props.active ? "#080808" : "none"};

  &:hover {
    background-color: transparent;
    cursor: pointer;
    border: 1.5px solid black;
  }
`;
