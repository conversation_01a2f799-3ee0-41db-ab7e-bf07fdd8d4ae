{"routes": [{"method": "GET", "path": "/old-topics/compare", "handler": "old-topics.compare", "config": {"policies": []}}, {"method": "GET", "path": "/old-topics", "handler": "old-topics.find", "config": {"policies": []}}, {"method": "GET", "path": "/old-topics/count", "handler": "old-topics.count", "config": {"policies": []}}, {"method": "GET", "path": "/old-topics/:id", "handler": "old-topics.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/old-topics", "handler": "old-topics.create", "config": {"policies": []}}, {"method": "PUT", "path": "/old-topics/:id", "handler": "old-topics.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/old-topics/:id", "handler": "old-topics.delete", "config": {"policies": []}}]}