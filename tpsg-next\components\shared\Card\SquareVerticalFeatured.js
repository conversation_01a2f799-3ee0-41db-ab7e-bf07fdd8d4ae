import styled from "styled-components";
import Link from "next/link";
import CondImage from "../condimage";
import { device } from "styles/device";
import CondLink from "../CondLink";

export default function SquareVerticalFeatured({ item }) {

  if (!item.cta?.url) return <></>;

  return (
    <Wrapper className={"svf-card"}>
      <CondLink link={item.cta.url}>
        <Content>
          <div className={"svf-hidden-image"}>
            <div className={"svf-blurred-image"}>
              <CondImage imageData={item.image} />
            </div>
          </div>
          <div className={"svf-image"}>
            <CondImage imageData={item.image} />
          </div>
          <h3 className={"svf-card-title"}>{item.title}</h3>
        </Content>
      </CondLink>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  position: relative;
  width: 100%;
  cursor: pointer;
`;

const Content = styled.div`
  background-color: #161616;
  padding: 24px;
  box-shadow: 0 7px 14px 3px rgba(0, 0, 0, 0.29);
  
  .svf-image {
    position: relative;
    width: 100%;
    aspect-ratio: 1/1;
  }
  .svf-blurred-image {
    position: relative;
  }
  .svf-card-title {
    font-size: 18px;
    font-weight: 500;
    line-height: 115%;
    color: #f4f4f4;
  }
  @media ${device.desktop} {
    .svf-card-title {
      
    }
  }
`