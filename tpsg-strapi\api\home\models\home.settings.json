{"kind": "singleType", "collectionName": "home", "info": {"name": "Home", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false}, "pluginOptions": {}, "attributes": {"articles": {"type": "component", "repeatable": false, "component": "section.post-set"}, "featured": {"type": "component", "repeatable": true, "component": "module.featured"}, "mission": {"type": "component", "repeatable": false, "component": "section.mission"}, "podcasts": {"type": "component", "repeatable": false, "component": "section.post-set"}, "webinars": {"type": "component", "repeatable": false, "component": "section.post-set"}, "formations": {"type": "component", "repeatable": false, "component": "section.post-set"}, "bloggers": {"type": "component", "repeatable": false, "component": "section.post-set"}, "quote": {"type": "component", "repeatable": false, "component": "section.quote"}, "topics": {"type": "component", "repeatable": true, "component": "section.post-set"}, "shop": {"type": "component", "repeatable": false, "component": "section.shop"}, "mostRead": {"type": "component", "repeatable": false, "component": "section.post-set"}, "config": {"type": "json"}, "newsletter": {"type": "text"}}}