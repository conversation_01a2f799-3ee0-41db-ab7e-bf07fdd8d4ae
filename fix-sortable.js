const { MeiliSearch } = require('meilisearch');

const client = new MeiliSearch({
  host: 'http://localhost:7700'
});

async function fixSortable() {
  try {
    console.log('Configuration des attributs triables...');
    
    const index = client.index('post');
    
    // Configurer les attributs triables
    await index.updateSortableAttributes(['date']);
    
    console.log('Attributs triables configurés avec succès');
    
    // Vérifier les attributs triables
    const settings = await index.getSortableAttributes();
    console.log('Attributs triables:', settings);
    
    console.log('Configuration terminée !');
  } catch (error) {
    console.error('Erreur lors de la configuration:', error);
  }
}

fixSortable();
