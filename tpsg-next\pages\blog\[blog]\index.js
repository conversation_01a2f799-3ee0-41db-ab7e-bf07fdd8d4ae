import client from "/api/apollo-client";
import { gql } from "@apollo/client";
import styled from "styled-components";
import BlogMenu from "components/blog/menu";
import { device } from "styles/device";
import { removeMarkdown } from "utils/string.utils";

import {
  FirstArticle,
  SquareVertical,
  SquareVerticalFeatured,
} from "components/shared/Card";

import ButtonLink from "components/shared/Buttons/ButtonLink";
import DynamicForm from "components/shared/ConvertkitForm/DynamicForm";
import AuthorBox from "components/shared/post/author-box";
import { Featured } from "components/shared";
import BlogHeader from "components/blog/BlogHeader";
import { AnimatedTextButton } from "../../../components/shared/Buttons";
import { NextSeo } from "next-seo";

const defaultCardOptions = {
  showAuthor: false,
  dotColors: { back: "#ffffff" },
};

export default function Blog({ blog, posts }) {
  if (!blog || posts.length < 10) return <></>;

  // Mises en avant pleine largeur
  const fullFeatured = blog.featured?.filter((f) => !f.inColumn);
  const topFeatured = fullFeatured.length > 0 ? fullFeatured[0] : null;
  const botFeatured = fullFeatured.length > 1 ? fullFeatured[1] : null;

  // Mises en avant en colonne
  const columnFeatured = blog.featured?.filter((f) => f.inColumn);
  const columnFeatured1 = columnFeatured.length > 0 ? columnFeatured[0] : null;
  const columnFeatured2 = columnFeatured.length > 1 ? columnFeatured[1] : null;

  return (
    <PageWrapper>
      <NextSeo
        title={`TPSG - Blog de ${blog.blogger.fullName}`}
        description={removeMarkdown(blog.blogger.about).slice(0, 160)}
      />
      <BlogHeader blog={blog} />
      <SectionPosts>
        <BlogMenu data={blog} />
        <PostsWrapper>
          <MainRow>
            <div className="main-element">
              <FirstArticle post={posts[0]} options={defaultCardOptions} />
            </div>
            <div className="sub-row">
              <SquareVertical post={posts[1]} options={defaultCardOptions} />
              <SquareVertical post={posts[2]} options={defaultCardOptions} />
              <SquareVertical post={posts[3]} options={defaultCardOptions} />
              <SquareVertical post={posts[4]} options={defaultCardOptions} />
            </div>
          </MainRow>
          {/* TODO: vérifier les espaaces entres les sections quand il n'y a pas de mise en avant*/}
          {/*{topFeatured &&*/}
          {/*  // TODO: À remplacer avec le composant par défaut. Ajouter une option sans bords.*/}
          {/*  <BlogFeatured backgroundColor={fullFeatured[0].color?.background || "orangered"}>*/}
          {/*    <div className={"blog-featured-image"}>*/}
          {/*      <CondImage imageData={fullFeatured[0].image} />*/}
          {/*    </div>*/}
          {/*    <div className={"blog-featured-text"} >*/}
          {/*      <p className={"blog-featured-type"}>{topFeatured.type || null}</p>*/}
          {/*      <h4 className={"blog-featured-title"}>{topFeatured.title}</h4>*/}
          {/*      <p className={"blog-featured-desc"}>{topFeatured.description}</p>*/}
          {/*      <div className={"blog-featured-buttons"}>*/}
          {/*        {topFeatured.cta?.url && (*/}
          {/*          <ButtonLink*/}
          {/*            text={topFeatured.cta.name}*/}
          {/*            type={topFeatured.cta.outline ? "soft-dark" : "glow-dark"}*/}
          {/*            url={topFeatured.cta.url}*/}
          {/*          />*/}
          {/*        )}*/}

          {/*      </div>*/}
          {/*    </div>*/}
          {/*  </BlogFeatured>*/}
          {/*}*/}
          {topFeatured && (
            <>
              <Shadow direction={"top"} />
              <Featured content={topFeatured} />
              <Shadow direction={"bot"} />
            </>
          )}
          <Spacer />
          {/* TODO: Y'a des trucs à modifier là ...*/}
          <CardRow>
            <SquareVertical post={posts[5]} options={defaultCardOptions} />
            <SquareVertical post={posts[6]} options={defaultCardOptions} />
            {columnFeatured1 && (
              <SquareVerticalFeatured item={columnFeatured1} />
            )}
            <SquareVertical post={posts[8]} options={defaultCardOptions} />
            <SquareVertical post={posts[9]} options={defaultCardOptions} />
            {columnFeatured2 && (
              <SquareVerticalFeatured item={columnFeatured2} />
            )}
          </CardRow>
        </PostsWrapper>
        <BtnAllPostContainer>
          <AnimatedTextButton
            theme={"dark"}
            text={"Tous mes articles"}
            link={`/blog/${blog.slug}/filtres`}
          />
        </BtnAllPostContainer>
      </SectionPosts>
      {botFeatured && (
        <>
          <Space />
          <Featured content={botFeatured} />
        </>
      )}
      <BlogBottom>
        <FormBox>
          <DynamicForm formString={blog.newsletter} title={"Ma newsletter"} />
        </FormBox>
        <AuthorBox author={blog.blogger} blog={blog} />
      </BlogBottom>
      {/*<Spacer />*/}
    </PageWrapper>
  );
}

const BlogBottom = styled.div`
  padding-left: var(--border-space);
  padding-right: var(--border-space);
  display: flex;
  flex-direction: column-reverse;
  margin-bottom: 128px;
  margin-top: 128px;

  .author-box {
    margin-bottom: 40px;
  }

  @media ${device.desktop} {
    flex-direction: row;
    .author-box {
      padding-left: 40px;
    }
  }
`;

const Space = styled.div`
  position: relative;
  height: 64px;
  @media ${device.desktop} {
    height: 164px;
  }
`;

const FormBox = styled.div`
  border-top: 1px solid rgba(0, 0, 0, 0.4);
  padding-right: 40px;
  padding-top: 32px;
  @media ${device.desktop} {
    min-width: 40%;
    border-right: 1px solid rgba(0, 0, 0, 0.4);
  }
`;

const Shadow = styled.div`
  position: relative;
  height: 40px;
  background: ${(p) =>
    p.direction === "bot"
      ? "linear-gradient(#FAF7F3, rgba(250, 247, 243, 0))"
      : "linear-gradient(rgba(250, 247, 243, 0), #FAF7F3)"};
  z-index: 100;
`;

const Spacer = styled.div`
  position: relative;
  height: 64px;
`;

const PageWrapper = styled.div`
  position: relative;
  background-color: var(--soft-white);
  @media ${device.desktop} {
    .blog-menu {
      margin-left: var(--border-space);
    }
  }
  @media ${device.desktopXL} {
    .blog-menu {
      margin-left: 114px;
    }
  }
`;

const SectionPosts = styled.div`
  position: relative;
`;

const PostsWrapper = styled.div`
  position: relative;
  top: 48px;
  left: 0;
  width: 100%;
  @media ${device.desktop} {
    margin-top: -100vh;
  }
`;

const BtnAllPostContainer = styled.div`
  position: relative;
  display: flex;
  margin-top: 48px;
  margin-left: var(--border-space);
  .button-link {
    width: calc(100% - var(--border-space));
    text-align: center;
  }
  @media ${device.tablet} {
    .button-link {
      width: auto;
      text-align: left;
    }
  }
  @media ${device.desktop} {
    margin-left: calc(25% + 24px);
  }
`;

const MainRow = styled.div`
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-right: var(--border-space);
  margin-left: var(--border-space);

  .main-element {
    display: flex;
    width: 100%;
  }

  .sub-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 18px;

    @media ${device.desktop} {
      display: flex;
      flex-direction: row;
      gap: 18px;
    }
  }

  @media ${device.tablet} {
    margin-left: calc(var(--border-space) - 24px);
  }
  @media ${device.desktop} {
    margin-left: 25%;
  }
`;

const CardRow = styled.div`
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-right: var(--border-space);

  .fa-card {
    position: relative;
    max-width: 100%;
    margin-left: 24px;
    margin-bottom: 40px;
  }
  .sv-card {
    margin-left: 24px;
    margin-bottom: 40px;
    max-width: calc(50% - 24px);
  }
  .svf-card {
    margin-left: 24px;
    margin-bottom: 40px;
    //max-width: calc(50% - 24px);
    max-width: 100%;
  }
  @media ${device.tablet} {
    margin-left: calc(var(--border-space) - 24px);
    .fa-card {
      width: calc(66.3% - 24px);
    }
    .sv-card {
      width: calc(33.3% - 24px);
    }
    .svf-card {
      width: calc(33.3% - 24px);
    }
  }
  @media ${device.desktop} {
    margin-left: 25%;
  }
`;

const BlogFeatured = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  background: transparent;
  margin-top: 126px;
  z-index: 300;

  .blog-featured-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16/10;
  }
  .blog-featured-text {
    background-color: ${(props) => props.backgroundColor};
    position: relative;
    padding: var(--border-space);
    width: 100%;
    min-height: 400px;
  }
  .blog-featured-title {
    font-size: 46px;
    margin-top: 24px;
    margin-bottom: 0;
  }
  .blog-featured-desc {
    font-size: 18px;
  }
  .blog-featured-type {
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 18px;
  }
  .blog-featured-buttons {
    margin-top: 40px;
    margin-bottom: 40px;
  }

  @media ${device.tablet} {
    flex-direction: row;
    .blog-featured-image {
      width: 50%;
      height: 100%;
      aspect-ratio: inherit;
    }
    .blog-featured-text {
      position: relative;
      padding: 52px;
      width: 50%;
      height: 100%;
    }
  }
  @media ${device.desktop} {
    padding-left: calc(25% + 24px);
    .blog-featured-image {
      width: calc(50% - 48px);
      min-height: 400px;
      max-height: 100%;
      height: inherit;
    }
    .blog-featured-text {
      width: calc(50% + 48px);
      height: 100%;
    }
  }
`;

export async function getStaticProps({ params }) {
  const blog = await client
    .query({
      query: QUERY_BLOG,
      variables: { slug: params.blog },
    })
    .then((response) => {
      return response.data.blogs[0];
    });

  if (!blog) {
    return { notFound: true };
  }
  // const featuredSlugs = blog.featured?.map((ft) => ft?.cta?.url?.split("/")[2])

  const featuredIds = blog.featured?.map((item) => item.postRef?.id);

  const posts = await client
    .query({
      query: QUERY_BLOG_POSTS,
      variables: {
        blogId: blog.id,
        featuredIds: featuredIds,
      },
    })
    .then((response) => {
      return response.data.posts;
    });

  return {
    props: {
      blog,
      posts,
    },
    revalidate: 10,
  };
}

export async function getStaticPaths() {
  const blogs = await client
    .query({
      query: QUERY_BLOG_SLUGS,
    })
    .then((response) => {
      return response.data.blogs;
    });

  return {
    paths: blogs.map((blog) => ({
      params: {
        blog: blog.slug,
      },
    })),
    fallback: true,
  };
}

const QUERY_BLOG = gql`
  query Blog($slug: String!) {
    blogs(where: { slug: $slug }) {
      id
      slug
      newsletter
      blogger {
        fullName
        picture {
          formats
          url
          provider
        }
        about
      }
      featured {
        title
        description
        type
        image {
          formats
          url
          provider
        }
        inColumn
        cta {
          name
          url
          outline
        }
        cta2 {
          name
          url
          outline
        }
        color {
          foreground
          background
        }
        postRef {
          id
        }
      }
      menu {
        label
        value
        type
      }
    }
  }
`;

const QUERY_BLOG_SLUGS = gql`
  query BlogSlugs {
    blogs {
      slug
    }
  }
`;

// 14 de limite : pour 10 posts +4 (marge de doublon supprimé pour les 4 cornerstones)
const QUERY_BLOG_POSTS = gql`
  query BlogPosts($blogId: ID!, $featuredIds: [String]) {
    posts(
      limit: 14
      where: { blog: $blogId, id_nin: $featuredIds }
      sort: "published_at:DESC"
    ) {
      title
      slug
      type
      image {
        url
        width
        height
        provider
        caption
        alternativeText
      }
      published_at
      modules {
        __typename
        ... on ComponentModulePodcast {
          podcast {
            name
            slug
          }
        }
      }
    }
  }
`;
