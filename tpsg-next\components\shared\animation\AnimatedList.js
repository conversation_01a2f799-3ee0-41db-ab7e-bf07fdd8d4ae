import { PaginateContext } from "context/PaginateContext";
import React, { createContext, useContext, useEffect, useState } from "react";
import styled from "styled-components";

/**
 * This component is designed to animate the lists on podcasts/[slug]
 * and webinaires pages. The animation is triggered when
 * animationState prop change.
 *
 * @param {Object} animationState
 * @param {number} animationState.nextPage
 * @param {number} animationStatxe.currentPage
 * @param {number} animationState.resourcesFetched
 * @param {JSX.Element} children
 * @return {JSX.Element}
 */
export default function AnimatedList({ animationTransition, setAnimationTransition, children }) {

  const [pageState, setPageState] = useContext(PaginateContext);
  const [animationClass, setAnimationClass] = useState("animation-init");
  
  useEffect(() => {
    function doneAnimation() {
      setAnimationTransition(prevState => ({
        ...prevState,
        transitioning: "done"
      }));
      setPageState(prevState => ({
        ...prevState,
        loading: false
      }));
    }
    let reverse = animationTransition.transitioning === "fetched" ? "-reverse" : "";

    if (animationTransition.transitioning === "init" && animationTransition.direction === null) {  // For First Page
      // console.log("animation -- start");
      setAnimationClass("backward-list-reverse");
      doneAnimation();
      // console.log("animation -- end");

    } else if (animationTransition.transitioning === "init" && animationTransition.direction !== null) { // First part of animation
      // console.log("animation -- start");

      document.getElementsByTagName("html")[0].style.scrollBehavior = "auto"; // disable smooth scroll
      document.getElementById("top-animated-list")?.scrollIntoView();
      document.getElementsByTagName("html")[0].style.scrollBehavior = null; // re enable smooth scroll

      setAnimationClass(animationTransition.direction < 0 ? `backward-list${reverse}` : `frontward-list${reverse}`);

    } else if (animationTransition.transitioning === "fetched" && animationTransition.direction !== null) { // Second part of animation with new list


      setAnimationClass(animationTransition.direction > 0 ? `backward-list${reverse}` : `frontward-list${reverse}`);
      doneAnimation();

      // console.log("animation -- end");
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [animationTransition,setAnimationTransition]);
  
  return (
    <Wrapper
      id="top-animated-list">
      <div className={`animation-list ${animationClass}`}>
        {children}
      </div>
    </Wrapper>
  );
}

const Wrapper = styled.div`
  width: 100%;
  overflow: hidden;
  scroll-behavior: auto !important;
  min-height: 100vh;
  @keyframes backward-list-animation { 
    from { 
      opacity: 1
    } to {
      transform: translateY(-300px);
      opacity: 0;
    } 
  }
  @keyframes frontward-list-animation { 
    from { 
      opacity: 1
    } to { 
      transform: translateY(300px);
      opacity: 0;
    } 
  }
  @keyframes backward-list-animation-reverse { 
    from { 
      opacity: 0;
      transform: translateY(-300px);
    } to { 
      opacity: 1;
      transform: translateY(0);
    } 
  }
  @keyframes frontward-list-animation-reverse { 
    from {
      opacity: 0;
      transform: translateY(300px);
    } to { 
      transform: translateY(0);
      opacity: 1;
    }
  }
  .backward-list {
    animation: backward-list-animation forwards 500ms;
  }
  .frontward-list {
    animation: frontward-list-animation forwards  500ms;
  }
  .backward-list-reverse {
    animation: backward-list-animation-reverse forwards 500ms;
  }
  .frontward-list-reverse {
    animation: frontward-list-animation-reverse forwards  500ms;
  }
  .animation-init{
    display: none
  }
`;
