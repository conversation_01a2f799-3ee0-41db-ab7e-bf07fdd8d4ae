{"kind": "collectionType", "collectionName": "posts", "info": {"name": "post", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "slug": {"type": "uid", "targetField": "title", "required": true}, "body": {"type": "richtext"}, "image": {"model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}}, "serie": {"via": "posts", "model": "serie"}, "tags": {"collection": "tag"}, "topics": {"collection": "topic"}, "author": {"model": "author"}, "modules": {"type": "dynamiczone", "components": ["module.event", "module.formation", "module.lead", "module.podcast", "module.seo", "module.webinar", "module.email-journey"]}, "old_topics": {"collection": "old-topics"}, "wp_created_at": {"type": "datetime"}, "blog": {"model": "blog"}, "isCornerStone": {"type": "boolean", "default": false}, "type": {"type": "enumeration", "enum": ["article", "formation", "parcours", "podcast", "webinaire"]}, "publish_at": {"type": "datetime"}, "wp_published_at": {"type": "datetime"}, "readingTime": {"type": "integer"}}}