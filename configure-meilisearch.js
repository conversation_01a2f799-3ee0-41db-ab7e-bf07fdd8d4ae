/**
 * <PERSON>ript pour configurer Meilisearch v0.27.0 localement
 * Ce script configure Meilisearch pour qu'il soit compatible avec le code existant
 * sans modifier le code de production
 */

const fetch = require('node-fetch');

// Configuration
const MEILI_URL = 'http://127.0.0.1:7700';  // Utiliser l'adresse IP locale explicite
const MEILI_KEY = '';
const INDEX_NAME = 'post';

async function configureMeilisearch() {
  console.log('Configuration de Meilisearch...');
  console.log(`URL: ${MEILI_URL}`);
  console.log(`Index: ${INDEX_NAME}`);

  try {
    // 1. Vérifier si l'index existe
    console.log('Vérification de l\'index...');
    const indexResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}`, {
      headers: MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {}
    });

    if (indexResponse.status === 404) {
      console.log('L\'index n\'existe pas, création...');
      await fetch(`${MEILI_URL}/indexes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {})
        },
        body: JSON.stringify({
          uid: INDEX_NAME,
          primaryKey: 'id'
        })
      });
      console.log('Index créé avec succès');
    } else {
      console.log('L\'index existe déjà');
    }

    // 2. Configurer les attributs filtrables
    console.log('Configuration des attributs filtrables...');
    const filterableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {})
      },
      body: JSON.stringify([
        'type', 'topics', 'tags', 'author', 'blog', 'cs'
      ])
    });
    console.log(`Attributs filtrables configurés: ${filterableResponse.status}`);

    // 3. Configurer les attributs triables
    console.log('Configuration des attributs triables...');
    const sortableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {})
      },
      body: JSON.stringify([
        'date'
      ])
    });
    console.log(`Attributs triables configurés: ${sortableResponse.status}`);

    // 4. Configurer les règles de classement
    console.log('Configuration des règles de classement...');
    const rankingResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/ranking-rules`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {})
      },
      body: JSON.stringify([
        'sort',
        'words',
        'typo',
        'proximity',
        'attribute',
        'exactness'
      ])
    });
    console.log(`Règles de classement configurées: ${rankingResponse.status}`);

    console.log('Configuration de Meilisearch terminée avec succès');
  } catch (error) {
    console.error('Erreur lors de la configuration de Meilisearch:', error);
  }
}

configureMeilisearch();
