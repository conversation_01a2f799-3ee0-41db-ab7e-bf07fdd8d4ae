# Guide d'utilisation de Meilisearch v0.27.0 avec TPSG

Ce guide explique comment utiliser Meilisearch v0.27.0 avec le projet TPSG sans modifier le code de production.

## Problème

Meilisearch v0.27.0 ne supporte pas les paramètres `page` et `hitsPerPage` pour la pagination, qui sont utilisés dans le code de production. À la place, il faut utiliser `offset` et `limit`.

## Solution

Nous avons créé un adaptateur qui convertit automatiquement les paramètres `page` et `hitsPerPage` en `offset` et `limit`, et qui adapte également la réponse de Meilisearch pour qu'elle soit compatible avec le code de production.

## Fichiers créés ou modifiés

1. `tpsg-next/api/use-meili-adapter.js` - Adaptateur pour Meilisearch v0.27.0
2. `tpsg-next/utils/meili-adapter.js` - Fonctions d'adaptation des paramètres et des réponses
3. `tpsg-next/pages/recherche.js` - Modifié pour utiliser l'adaptateur
4. `tpsg-next/pages/index.js` - Modifié pour utiliser l'adaptateur et afficher des diagnostics
5. `configure-meilisearch-v0.27.0.js` - Script pour configurer Meilisearch v0.27.0

## Comment utiliser cette solution

### 1. Configurer Meilisearch v0.27.0

Exécutez le script de configuration :

```bash
node configure-meilisearch-v0.27.0.js
```

Ce script configure Meilisearch v0.27.0 avec les paramètres nécessaires pour qu'il soit compatible avec le code de production.

### 2. Utiliser l'adaptateur dans votre code

Pour utiliser l'adaptateur dans votre code, remplacez :

```javascript
import { MeiliApi } from "api/meili-client";
```

par :

```javascript
import { MeiliApi } from "api/use-meili-adapter";
```

### 3. Vérifier les diagnostics

Des messages de diagnostic sont affichés dans la console du navigateur et dans un panneau de diagnostic visible sur la page d'accueil et la page de recherche en mode développement.

## Fonctionnement de l'adaptateur

L'adaptateur effectue les opérations suivantes :

1. Convertit les paramètres `page` et `hitsPerPage` en `offset` et `limit`
2. Adapte la réponse de Meilisearch pour qu'elle contienne les propriétés attendues par le code de production
3. Ajoute des logs de diagnostic pour faciliter le débogage

## Remarques importantes

- Cette solution est temporaire et ne modifie pas le code de production
- Elle permet de développer localement avec Meilisearch v0.27.0 sans avoir à modifier le code de production
- En production, le code original est utilisé avec une version de Meilisearch qui supporte les paramètres `page` et `hitsPerPage`

## Dépannage

Si vous rencontrez des problèmes, vérifiez les points suivants :

1. Assurez-vous que Meilisearch est en cours d'exécution
2. Vérifiez que le script de configuration a été exécuté avec succès
3. Consultez les messages de diagnostic dans la console du navigateur
4. Vérifiez que vous utilisez l'adaptateur dans votre code

## Retour à la configuration d'origine

Si vous souhaitez revenir à la configuration d'origine, il vous suffit de remplacer :

```javascript
import { MeiliApi } from "api/use-meili-adapter";
```

par :

```javascript
import { MeiliApi } from "api/meili-client";
```

dans les fichiers où vous avez utilisé l'adaptateur.
