import styled from "styled-components";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import CondImage from "components/shared/condimage";
import { device } from "styles/device";
import { isBlogger } from "utils/posts.utils";
import Link from "next/link";


export default function AuthorBox({ author, blog }) {

  if (!author) return null;
  // Supprime tout les backslashes restants de la bio.
  author.about = author.about?.replace(/\\/g, "") || "";

  // Déterminer le lien vers l'auteur
  const authorLink = blog ?
    `/blog/${blog.blogger?.slug}` :
    `/recherche?author=${author.fullName}`;

  const fullName = `${author.firstName || ""} ${author.lastName || ""}`.trim();
  
  return (
    <Wrapper className={"author-box"}>
      <Face>
        <a href={authorLink}>
          <CondImage imageData={author.picture}/>
        </a>
      </Face>
      <About>
        <p className={"abox-about__name"}>
          <a href={authorLink}>{fullName}</a> 
        </p>
        <ReactMarkdown
          rehypePlugins={[rehypeRaw]}>
          {author.about}
        </ReactMarkdown>
      </About>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  border-top: 1px solid rgba(0, 0, 0, 0.4);
  padding-top: 24px;
  
  @media ${device.tablet} {
    flex-direction: row;
    padding: 32px 0 0 0;
  }
`

const Face = styled.div`
  position: relative;
  flex-shrink: 0;
  margin: 0 32px 12px 0;
  height: 60px;
  width: 60px;
  border-radius: 60px;
  overflow: hidden;
  background-color: white;
`;

const About = styled.div`
  .abox-about__name {
    font-size: 26px;
    font-weight: 600;
  }
  p {
    font-size: 22px;
    line-height: 28px;
    font-weight: 400;
    margin: 12px 0 0 0;
  }
`
