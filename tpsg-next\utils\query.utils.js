/**
 * Convertie les paramètres venant d'une url en un objet
 * exploitable dans une requête à l'api de Meilisearch
 * @param urlParams
 * @param perPage nombre de post par page
 * @param {string} origin page dont est issue la requête
 * @return {filter, offset}
 */
const getMeiliParams = (urlParams, perPage, origin = "default") => {

  let filter = [];
  let offset = 0;

  // Traitement des paramètres de l'URL
  for (const param in urlParams) {
    switch (param) {
    case "type":
    case "author":
      if (origin === "recherche") {
        filter.push("" + param + " = \"" + urlParams[param] + "\"")
      }
      break
    case "topic":
    case "tag":
    case "serie":
      filter.push("" + param + "s = \"" + urlParams[param] + "\"")
      break
    case "blog":
      filter.push("blog = " + urlParams.blog)
      break
    case "pageTitle":
      filter = urlParams.pageTitle
      break
    case "page":
      offset = +urlParams.page > 0 ? ( +urlParams.page - 1 ) * perPage : 0
      break
    default:
      break
    }
  }
  return {
    filter: filter,
    limit: perPage,
    offset: offset,
  }
}

const getMeiliParamsV2 = (origin, urlParams, limit) => {
  let filter = [];
  let offset = 0;
  for (const paramName in urlParams) {
    if (acceptedParams[origin].includes(paramName)) {
      switch (paramName) {
      case "page":
        offset = +urlParams.page > 0 ? ( +urlParams.page - 1 ) * limit : 0;
        break;
      default:
        filter.push("" + completeName(paramName) + " = \"" + urlParams[paramName] + "\"")
      }
    }
  }
  return {
    filter,
    offset,
    limit
  }
}

/**
 * Convertie les paramètres venant d'une url en un objet
 * exploitable dans une requête à l'api de Meilisearch pour une pagination correct
 * @param urlParams
 * @param perPage nombre de post par page
 * @param {string} origin page dont est issue la requête
 * @return {filter, offset}
 */
const getMeiliParamsPaginate = (urlParams, perPage, origin = "default") => {

  let filter = [];
  let page = 1;
  for (const param in urlParams) {
    switch (param) {
    case "type":
    case "author":
      filter.push("" + param + " = \"" + urlParams[param] + "\"")
      break
    case "topic":
    case "tag":
    case "serie":
      filter.push("" + param + "s = \"" + urlParams[param] + "\"")
      break
    case "blog":
      filter.push("blog = " + urlParams.blog)
      break
    case "pageTitle":
      filter = urlParams.pageTitle
      break
    case "page":
      page = +urlParams.page > 1 ? +urlParams.page : 1
      break
    default:
      break
    }
  }

  return {
    filter: filter,
    page: page,
    hitsPerPage: perPage
  }
}

const acceptedParams = {
  "ministry": ["page"],
}

const completeName = (paramName) => {
  switch (paramName) {
  case "topic":
  case "tag":
  case "serie":
    return paramName + "s"
  default:
    return paramName;
  }
}

function removeUrlParams(url) {
  return url.split("?")[0];
}

export {
  getMeiliParams,
  getMeiliParamsV2,
  getMeiliParamsPaginate,
  removeUrlParams
}
