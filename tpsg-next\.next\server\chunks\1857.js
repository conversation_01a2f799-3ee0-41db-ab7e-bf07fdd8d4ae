"use strict";exports.id=1857,exports.ids=[1857],exports.modules={7178:(e,t,i)=>{i.d(t,{Z:()=>AnimatedList});var a=i(997),n=i(3229),o=i(6689),r=i(7518),s=i.n(r);function AnimatedList({animationTransition:e,setAnimationTransition:t,children:i}){let[r,s]=(0,o.useContext)(n.b),[d,c]=(0,o.useState)("animation-init");return(0,o.useEffect)(()=>{function doneAnimation(){t(e=>({...e,transitioning:"done"})),s(e=>({...e,loading:!1}))}let i="fetched"===e.transitioning?"-reverse":"";"init"===e.transitioning&&null===e.direction?(c("backward-list-reverse"),doneAnimation()):"init"===e.transitioning&&null!==e.direction?(document.getElementsByTagName("html")[0].style.scrollBehavior="auto",document.getElementById("top-animated-list")?.scrollIntoView(),document.getElementsByTagName("html")[0].style.scrollBehavior=null,c(e.direction<0?`backward-list${i}`:`frontward-list${i}`)):"fetched"===e.transitioning&&null!==e.direction&&(c(e.direction>0?`backward-list${i}`:`frontward-list${i}`),doneAnimation())},[e,t]),a.jsx(l,{id:"top-animated-list",children:a.jsx("div",{className:`animation-list ${d}`,children:i})})}let l=s().div.withConfig({componentId:"sc-add37874-0"})`
  width: 100%;
  overflow: hidden;
  scroll-behavior: auto !important;
  min-height: 100vh;
  @keyframes backward-list-animation { 
    from { 
      opacity: 1
    } to {
      transform: translateY(-300px);
      opacity: 0;
    } 
  }
  @keyframes frontward-list-animation { 
    from { 
      opacity: 1
    } to { 
      transform: translateY(300px);
      opacity: 0;
    } 
  }
  @keyframes backward-list-animation-reverse { 
    from { 
      opacity: 0;
      transform: translateY(-300px);
    } to { 
      opacity: 1;
      transform: translateY(0);
    } 
  }
  @keyframes frontward-list-animation-reverse { 
    from {
      opacity: 0;
      transform: translateY(300px);
    } to { 
      transform: translateY(0);
      opacity: 1;
    }
  }
  .backward-list {
    animation: backward-list-animation forwards 500ms;
  }
  .frontward-list {
    animation: frontward-list-animation forwards  500ms;
  }
  .backward-list-reverse {
    animation: backward-list-animation-reverse forwards 500ms;
  }
  .frontward-list-reverse {
    animation: frontward-list-animation-reverse forwards  500ms;
  }
  .animation-init{
    display: none
  }
`},2715:(e,t,i)=>{i.d(t,{Z:()=>StickyPagination});var a=i(997),n=i(7518),o=i.n(n),r=i(7467),s=i(6689),l=i(1664),d=i.n(l),c=i(3229);function NavButton({reverse:e,direction:t,url:i,shallow:n}){let[o,r,l,p]=(0,s.useContext)(c.b),m=t>=1?o.activePage>=o.maxPage:o.activePage<2;return a.jsx(g,{children:a.jsx(d(),{href:`${i}?page=${p+t}`,shallow:n,onClick:e=>{m||o.loading?e.preventDefault():e.ctrlKey||l(o.activePage+t)},className:"link-btn",replace:!0,children:a.jsx(f,{disabled:m,reverse:e,children:a.jsx(x,{disabled:m,children:a.jsx(ArrowSvg,{})})})})})}function NavigationButtons({url:e,shallow:t=!0}){return(0,a.jsxs)(p,{children:[a.jsx(NavButton,{reverse:!0,direction:-1,shallow:t,url:e}),a.jsx(NavButton,{reverse:!1,direction:1,shallow:t,url:e})]})}i(1163);let p=o().div.withConfig({componentId:"sc-cf6556c-0"})`
  display: flex;
`,g=o().div.withConfig({componentId:"sc-cf6556c-1"})`
  .link-btn {
    display: block;
    width: 42px;
    user-select: none;
    -webkit-user-drag: none;
  }
`,m=n.css`
  transform: rotate(-180deg);
`,h=n.css`
  opacity: 0.5;
  cursor: default;
`,f=o().div.withConfig({componentId:"sc-cf6556c-2"})`
  overflow: hidden;
  height: 42px;
  width: 42px;
  margin-right: 16px;
  border-radius: 100px;
  background-color: var(--soft-white);
  border: 1px solid var(--soft-dark);
  display: flex;
  align-items: center;
  justify-items: center;
  cursor: pointer;
  ${e=>e.disabled&&h};
  ${e=>e.reverse?m:null};
`,x=o().div.withConfig({componentId:"sc-cf6556c-3"})`
  margin: auto;
  height: 66%;
  width: 66%;

  svg {
    height: 100%;
    width: 100%;
    transform: scale(1);
    transition: 350ms;

    path {
      stroke: var(--soft-white);
      stroke-width: 0;
      transition: 350ms;
    }
  }
  ${e=>e.disabled?b:null};
`,b=n.css`
  svg {
    transform: rotate(45deg) scale(3.2);
    path {
      stroke-width: 1px;
    }
  }
`,ArrowSvg=()=>a.jsx("svg",{width:"100",height:"100",viewBox:"0 0 32 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:a.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M27.4626 7.30763C24.512 5.89173 22.0553 3.57515 20.4805 0.654073L21.7083 -1.04433e-06C23.6383 3.57991 27.0225 6.16306 31 7.09236L32 7.326L32 8.674L31 8.90764C27.0225 9.83694 23.6383 12.4201 21.7083 16L20.4805 15.3459C22.0553 12.4249 24.512 10.1083 27.4626 8.69236L9.78226e-07 8.69236L1.09928e-06 7.30763L27.4626 7.30763Z",fill:"black"})});function AnimatedNumber({activePage:e,totalPage:t,gapSize:i="100px",gapSizeMobile:n="50px"}){let o=[...Array(t).keys()];return a.jsx(u,{gapSize:i,gapSizeMobile:n,children:a.jsx(w,{gap:-(e-1)*i.replace(/\D+/g,""),gapSize:i,gapMobile:-(e-1)*n.replace(/\D+/g,""),gapSizeMobile:n,children:o.map(e=>a.jsx("div",{className:"number-item",children:a.jsx("p",{children:e+1})},e))})})}let u=o().div.withConfig({componentId:"sc-c4f5ad08-0"})`
   overflow: hidden;
   width: ${e=>80*e.gapSizeMobile/100};
   @media ${r.U.tablet} {
      width: ${e=>80*e.gapSize/100};
   }
`,w=o().div.withConfig({componentId:"sc-c4f5ad08-1"})`
   align-items: center;
   display: flex;
   flex-direction: column;
   height: ${e=>e.gapSizeMobile};
   p {
      padding-top: 3px;
      font-weight: 700;
      margin: 0;
      width: ${e=>e.gapSizeMobile};
      height: ${e=>e.gapSizeMobile};
   
      color: var(--soft-dark);
      font-size: ${e=>e.gapSizeMobile};
   }
   
   transition: 1000ms cubic-bezier(1, 0.72, 0.15, 1.01);
   transform: translateY(${e=>e.gapMobile}px);


   @media ${r.U.tablet} {
      height: ${e=>e.gapSize};
      transform: translateY(${e=>e.gap}px);
      p {
         width: ${e=>e.gapSize};
         height: ${e=>e.gapSize};
         font-size: ${e=>e.gapSize};
      }
   }
`;function PaginateSection({gapSize:e,gapSizeMobile:t,url:i=""}){let[n,o,r,l]=(0,s.useContext)(c.b);return(0,a.jsxs)(v,{children:[(0,a.jsxs)("header",{className:"pagestate-header",children:[a.jsx("p",{className:"podcast-secondary-text mobile-hide",children:`Page ${n.activePage}/${n.maxPage}`}),a.jsx(NavigationButtons,{url:i,shallow:!0})]}),a.jsx("main",{className:"pagestate-main",children:a.jsx(AnimatedNumber,{activePage:l,totalPage:n.maxPage,gapSize:e,gapSizeMobile:t})})]})}let v=o().section.withConfig({componentId:"sc-fb435624-0"})`
  max-width: 265px;
  user-select: none;
  .pagestate-header {
    display: flex;
    justify-content: space-between;
  }
  [class*="NavigationButtons__Circle"] {
    opacity: 0.8;
  }
  @media ${r.U.tablet} {
    [class*="NavigationButtons__Circle"] {
      opacity: 0.5;
    }
  }
`;var k=i(6453);function StickyPagination({url:e,title:t,children:i}){return(0,a.jsxs)(j,{children:[a.jsx("header",{children:a.jsx(k.GN,{children:t})}),a.jsx(y,{children:i}),a.jsx("footer",{className:"paginate-section-container",children:a.jsx(PaginateSection,{gapSize:"200px",gapSizeMobile:"42px",url:e})})]})}let y=o().main.withConfig({componentId:"sc-214d6b3-0"})`
    width: 100%;
    height: calc(100% - 350px);
    //overflow: auto;
`,j=o().div.withConfig({componentId:"sc-214d6b3-1"})`
  position: sticky;
  top: 0;
  padding-top: 20px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  flex-wrap: wrap;

  .paginate-section-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    position: fixed;
    margin-top: 0;
    bottom: 0;
    left: 0;
    height: 79px;
    background-color: rgba(244, 244, 244, 0.95);

    @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
      background-color: rgba(244, 244, 244, 0.8);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    section {
      display: flex;
      flex-direction: row-reverse;
    }
    .pagestate-header div[class*="NavigationButtons__Wrapper"] {
      margin-right: 24px;
      width: 140px;
      display: flex;
      justify-content: space-around;
    }
    .pagestate-main div[class*="AnimatedNumber__Wrapper"] {
      display: flex;
      justify-content: start;
    }
    .pagestate-main {
      margin-right: 17px;
    }

    &:before {
      content: "";
      position: absolute;
      right: 0;
      top: 0;
      height: 1px;
      width: 40%;
      border-top: 1px solid black;
    }
  }
  @media ${r.U.tablet} {
    @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
      -webkit-backdrop-filter: blur(0);
      backdrop-filter: blur(0);
    }

    padding-top: 40px;
    height: 100vh;
    p {
      display: block;
    }

    .paginate-section-container {
      position: absolute;
      bottom: 0;
      height: auto;
      background-color: transparent;
      margin-top: 50px;
      display: block;

      &:before {
        display: none;
      }

      section {
        display: block;
      }

      .pagestate-header div[class*="NavigationButtons__Wrapper"] {
        margin-right: 0;
      }

      .pagestate-main {
        margin-right: 0;
      }
    }
  }
`},3229:(e,t,i)=>{i.d(t,{b:()=>n});var a=i(6689);let n=(0,a.createContext)()}};