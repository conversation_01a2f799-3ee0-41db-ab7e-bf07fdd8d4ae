import styled from "styled-components";
import { device } from "../../styles/device";
import { withRealSrc } from "../../utils/image-utils";
import Image from "next/image";

export default function BlogHeader({ blog }) {

  const pictureUrl = withRealSrc(blog.blogger.picture);
  
  return (
    <Wrapper>
      <Picture>
        <Image
          fill
          sizes={"100px"}
          style={styles.picture}
          src={pictureUrl}
          alt={""}
        />
      </Picture>
      <div className={"text-content"}>
        <p className={"blog-header-name"}>{blog.blogger.fullName}</p>
        <p className={"blog-header-label"}>Blog</p>
      </div>
    </Wrapper>
  )
}

const styles = {
  picture: {
    objectFit: "cover"
  }
}

const Wrapper = styled.div`
  
  margin: 96px var(--border-space) 32px var(--border-space);
  
  display: flex;
  flex-direction: row;
  align-items: center;
  
  .text-content {
    margin-left: 16px;
  }
  
  .blog-header-name {
    font-family: Stelvio, sans-serif;
    font-size: 24px;
    line-height: 25px;
    font-weight: 500;
    margin: 0;
  }
  
  .blog-header-label {
    font-family: Lora, serif;
    font-style: italic;
    margin: 0;
    font-size: 17px;
    line-height: 20px;
  }
  
  @media ${device.desktop} {
    display: none;
  }
`;

const Picture = styled.div`
  position: relative;
  height: 54px;
  border-radius: 54px;
  background-color: #0070f3;
  overflow: hidden;
  aspect-ratio: 1/1;
`;