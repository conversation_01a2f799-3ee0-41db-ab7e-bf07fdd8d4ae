import styled from "styled-components";

export default function CKForm({ title, desc, formString }) {

  if(!formString) return <></>;

  return (
    <FormWrapper>
      {title && <h4 className={"form-title"}>{title}</h4>}
      {desc && <p className={"form-desc"}>{desc}</p>}
      <div dangerouslySetInnerHTML={{
        __html: `${formString}`
      }}/>
    </FormWrapper>
  )
}

const FormWrapper = styled.div`
  position: relative;
  width: 100%;
  .form-title {
    font-size: 42px;
    margin-top: 0;
    margin-bottom: 16px;
  }
  .form-desc {
    margin-top: 24px;
    margin-bottom: 24px;
    font-weight: 400;
    color: #161616;
    font-size: 22px;
  }
  .formkit-input {
    margin-bottom: 16px;
    background-color: #F0F0F0 !important;
    color: #161616 !important;
    border-radius: 0 !important;
    border: none !important;
    width: 100% !important;
    height: 52px;
    font-size: 18px !important;
    padding-left: 20px !important;
    &::placeholder {
      color: #888888 !important;
    }
  }
  .formkit-submit {
    margin-bottom: 16px;
    color: white !important;
    border: none !important;
    border-radius: 0 !important;
    background-color: #080808 !important;
    width: 100%;
    height: 52px;
    font-size: 18px !important;
    cursor: pointer;
    &:hover {
      background-color: var(--brand-color) !important;
    }
  }
  div {
    padding: 0 !important;
  }
`;