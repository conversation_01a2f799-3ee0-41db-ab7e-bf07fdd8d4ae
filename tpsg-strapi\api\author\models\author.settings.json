{"kind": "collectionType", "collectionName": "authors", "info": {"name": "author", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false}, "pluginOptions": {}, "attributes": {"firstName": {"type": "string"}, "lastName": {"type": "string"}, "fullName": {"type": "string", "required": true, "unique": true}, "about": {"type": "richtext"}, "picture": {"model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}}, "slug": {"type": "uid", "targetField": "fullName"}}}