"use strict";exports.id=8657,exports.ids=[8657],exports.modules={1728:(e,t,o)=>{o(997),o(1664);var i=o(7518),n=o.n(i);n().a.withConfig({componentId:"sc-555070bc-0"})`
    padding: 8px 16px;
    cursor: pointer;
    font-size: 12px;
    background-color: black;
    color: white;
`},2230:(e,t,o)=>{o(997),o(2832)},1779:(e,t,o)=>{o.d(t,{Z:()=><PERSON>ieWall});var i=o(997),n=o(7518),r=o.n(n),a=o(5515),l=o(6689),p=o(73);function CookieWall({style:e,children:t}){let[o,n]=(0,a.useCookies)(["preferences"]),[r,d]=(0,l.useState)(void 0);return(0,l.useEffect)(()=>{o?.preferences?.medias?d(!0):d(!1)},[o]),i.jsx(i.Fragment,{children:!0===r?t:(0,i.jsxs)(s,{style:{...e},children:[i.jsx("p",{className:"cw-text",children:"Nous n’avons pas d’autorisation de votre part pour l’utilisation de services tiers (YouTube, Spotify, SoundCloud, ConvertKit, …) depuis toutpoursagloire.com. Cette autorisation est n\xe9cessaire pour une exp\xe9rience compl\xe8te sur notre site. Vous pouvez les accepter en appuyant sur le bouton ci-dessous"}),i.jsx(p.Yz,{text:"Accepter",theme:"light",action:()=>void n("preferences",{...o.preferences,medias:!0},{sameSite:"strict",path:"/",maxAge:31536e3})})]})})}let s=r().div.withConfig({componentId:"sc-831a4ae6-0"})`
  position: relative;
  width: 100%;
  height: 100%;
  background-color: var(--c-dark-green);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: start;
  padding: 40px;

  .cw-text {
    margin-top: 0;
    font-family: Switzer, sans-serif;
    font-size: 16px;
    line-height: 24px;
    color: #f4f4f4;
  }
`},7895:(e,t,o)=>{o.d(t,{Z:()=>Featured});var i=o(997),n=o(7518),r=o.n(n),a=o(5675),l=o.n(a),p=o(7467),s=o(4130),d=o(677);function Featured({content:e}){let t;let{image:o,title:n,description:r,cta:a=null,cta2:p=null,color:g,type:f,postRef:x,route:m,_formatted:h}=e,u=["article","podcast","video","webinar","livre","formation"],b=u.includes(f?.toLowerCase()),getTypeFromRoute=e=>{if(!e)return"article";let t=e.split("/").filter(Boolean),o=t[0]?.toLowerCase();return u.includes(o)?o:"article"};if(!b&&f){let e=getTypeFromRoute(m);t=e.toUpperCase()}else if(f)t=f.toUpperCase();else{let e=getTypeFromRoute(m);t=e.toUpperCase()}let w=a?.url||(m?.startsWith("/")?m:"/"+m),v=p?.url||null;return(0,i.jsxs)(c,{backgroundColor:g?.background,children:[i.jsx("div",{className:"fw-featured-image",children:i.jsx(l(),{src:(0,s.k)(o),fill:!0,priority:!0,alt:"",sizes:"50vw"})}),(0,i.jsxs)("div",{className:"text-content",children:[i.jsx("p",{className:"fw-featured-type",children:t}),(0,i.jsxs)("div",{children:[i.jsx(PostAuthor,{post:x,content:e}),i.jsx("h3",{className:"fw-featured-title",children:n}),i.jsx("p",{className:"fw-featured-lead",children:r||h?.lead||h?.body}),(0,i.jsxs)("div",{className:"fw-featured-buttons",children:[w&&i.jsx(d.Z,{text:a?.name||"D\xe9couvrir",link:w,outline:a?.outline||!1,theme:"light"}),v&&i.jsx(d.Z,{text:p?.name||"D\xe9couvrir",link:v,outline:p?.outline||!1,theme:"light"})]})]})]})]})}let PostAuthor=({post:e,content:t})=>{let o=null;return(e?.author?.fullName?o=e.author.fullName:e?.author?o=e.author:t?.author&&(o=t.author),o)?i.jsx("div",{className:"fw-featured-author",children:o}):null},c=r().div.withConfig({componentId:"sc-daca621b-0"})`
  position: relative;
  padding: 0 var(--border-space);
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 24px;
  padding-bottom: 80px;
  width: 100%;
  min-height: 400px;
  background-color: ${e=>e.backgroundColor?e.backgroundColor:"var(--c-dark-green)"};
  z-index: 100;

  .fw-featured-author {
    font-family: "Lora", sans-serif;
    font-style: italic;
    letter-spacing: 1px;
    opacity: 0.4;
  }

  .fw-featured-image {
    position: relative;
    width: 100%;
    aspect-ratio: 1/1;
    grid-column: 1/5;

    img {
      object-fit: cover;
    }
  }

  .text-content {
    position: relative;
    grid-column: 1/5;
    color: ${e=>e.color?e.color:"var(--c-soft-cream)"};
    background-color: ${e=>e.backgroundColor?e.backgroundColor:"var(--c-dark-green)"};
  }

  .fw-featured-type {
    font-family: Switzer, Arial, sans-serif;
    opacity: 0.48;
    margin-top: 48px;
    margin-bottom: 56px;
    font-size: 16px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.02em;
  }

  .fw-featured-title {
    margin-top: 8px;
    margin-bottom: 0;
    font-size: 30px;
    font-weight: 500;
    line-height: 95%;
  }

  .fw-featured-lead {
    margin-top: 8px;
    font-size: 17px;
    margin-right: 32px;
  }

  .fw-featured-buttons {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  @media ${p.U.tablet} {
    flex-direction: row;
    min-height: 400px;
    padding-bottom: 96px;

    .fw-featured-image {
      position: relative;
      grid-column: 1/3;
    }

    .text-content {
      margin-top: 0;
      margin-left: 32px;
      grid-column: 3/5;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .fw-featured-type {
      font-size: 20px;
      margin-top: 46px;
    }

    .fw-featured-title {
      font-size: 46px;
      margin-top: 24px;
    }

    .fw-featured-lead {
      font-family: Switzer, sans-serif;
      font-size: 20px;
      font-weight: 400;
      opacity: 0.72;
      //font-size: 18px;
      margin-top: 24px;
    }

    .fw-featured-buttons {
      display: flex;
      flex-direction: row;
      gap: 24px;
    }
  }
`},5972:(e,t,o)=>{o.a(e,async(e,i)=>{try{o.d(t,{Z:()=>RenderMarkdown});var n=o(997),r=o(3135),a=o(6689),l=o(7518),p=o.n(l),s=o(1871),d=o(9526),c=o.n(d),g=o(7199),f=o(1779),x=o(7467),m=o(6809),h=e([r,s,m]);[r,s,m]=h.then?(await h)():h;let u={minHeight:300},codeComponent=({children:e})=>n.jsx(f.Z,{style:u,children:n.jsx(c(),{html:e})}),tableComponent=({children:e})=>n.jsx("div",{className:"table-container",children:n.jsx("table",{children:e})});function RenderMarkdown({content:e,className:t}){return e?n.jsx(a.Fragment,{children:n.jsx(b,{className:t,children:n.jsx(r.default,{rehypePlugins:[s.default,m.default],components:{code:codeComponent,table:tableComponent},children:(0,g.k5)(e)})})}):null}let b=p().div.withConfig({componentId:"sc-7da47e7e-0"})`
  font-family: "Lora", Charter, Times, "Times New Roman", serif;
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;

  img {
    max-width: 100%;
  }

  h5,
  h6 {
    font-size: 18px;
    line-height: 28px;
    font-weight: 500;
    margin-bottom: 0;
  }
  h1,
  h2,
  h3,
  h4 {
    font-family: Stelvio, sans-serif;
    font-weight: 600;
    margin-bottom: 0;
  }
  h1,
  h2 {
    font-size: 28px;
    line-height: 32px;
    margin-top: 32px;
    &:first-child {
      margin-top: 0;
    }
  }
  h3 {
    font-size: 24px;
    line-height: 28px;
    margin-top: 12px;
  }
  p {
    font-size: 18px;
    line-height: 28px;
    margin-top: 0;
    margin-bottom: 24px;
    color: #161616;
    sup {
      vertical-align: top;
      position: relative;
      top: -0.4em;
      margin-left: 4px;
      margin-right: 4px;
    }
  }
  blockquote {
    position: relative;
    margin: 0;
    padding: 8px 0 8px 36px;
    &::before {
      content: "“";
      display: inline-block;
      position: absolute;
      font-size: 54px;
      font-weight: bold;
      color: #363636;
      left: 0;
      top: 18px;
      width: 100px;
    }
  }
  blockquote p {
    font-size: 18px;
    line-height: 28px;
    font-style: italic;
  }
  em {
  }

  ul,
  ol {
    margin-top: 12px;
    padding-left: 24px;
    color: #161616;
  }
  li {
    line-height: 170%;
    margin: 0 0 8px 0;
    color: #161616;
  }
  a {
    color: #161616;
    text-decoration: underline;
  }
  a:hover {
    color: var(--brand-color);
  }
  hr {
    border: none;
    margin: 40px 0 40px 0;
    &::before {
      content: "***";
      display: block;
      letter-spacing: 10px;
      text-align: center;
      color: #161616;
    }
  }

  cite {
    &:before {
      content: "– ";
      font-weight: 600;
    }
  }

  @media ${x.U.tablet} {
    max-width: 720px;
    margin: 80px 0 0 0;
    h1,
    h2 {
      font-size: 32px;
      line-height: 36px;
      margin-top: 44px;
      margin-bottom: 6px;
    }
    h3,
    h4 {
      margin-top: 32px;
      margin-bottom: 0;
      font-size: 26px;
      line-height: 30px;
    }
    h4 {
      opacity: 0.6;
      font-weight: 500;
    }
    p {
      color: #323232;
      font-size: 20px;
      line-height: 32px;
      font-weight: 400;
      margin-bottom: 24px;
    }
    ul,
    ol {
      margin-top: 12px;
      padding-left: 24px;
      margin-bottom: 24px;
      font-size: 20px;
      line-height: 32px;
    }
    blockquote p {
      font-size: 20px;
      line-height: 32px;
      font-weight: 500;
      font-style: italic;
      margin-bottom: 0;
    }
    blockquote {
      margin-bottom: 24px;
    }

    .post-content-button {
      display: inline-block;
      margin-top: 16px;
      margin-bottom: 16px;
      padding: 10px 32px;
      border-radius: 40px;
      text-align: center;
      color: white;
      text-decoration: none;
      font-family: Switzer, sans-serif;
      font-weight: 400;
      background-color: var(--brand-color);
    }
  }
  .table-container {
    position: relative;
    overflow-x: auto;
    margin-bottom: 24px;
    width: 100%;
    background-image: linear-gradient(to right, white, white),
      linear-gradient(to right, white, white),
      linear-gradient(to right, rgba(0, 0, 20, 0.5), rgba(255, 255, 255, 0)),
      linear-gradient(to left, rgba(0, 0, 20, 0.5), rgba(255, 255, 255, 0));
    /* Shadows */
    /* Shadow covers */
    background-position: left center, right center, left center, right center;
    background-repeat: no-repeat;
    background-color: white;
    background-size: 20px 100%, 20px 100%, 10px 100%, 16px 100%;
    background-attachment: local, local, scroll, scroll;
  }

  table {
    font-family: Switzer, sans-serif;
    border: 1px solid #ccc;
    border-collapse: collapse;
    padding: 0;
    width: 100%;
    overflow-x: auto;
  }

  table caption {
    font-size: 1.5em;
    margin: 0.5em 0 0.75em;
  }

  table tr {
    background-color: rgba(248, 248, 248, 0.9);
    border: 1px solid #ddd;
    padding: 0.35em;
  }

  table th,
  table td {
    font-size: 0.85em;
    line-height: 1.4em;
    padding: 0.625em;
    text-align: left;
    min-width: 240px;
    color: rgba(0, 0, 0, 0.72);
  }

  table th {
    font-weight: 600;
    color: rgba(0, 0, 0, 9);
  }

  table th {
    font-size: 0.85em;
    //font-weight: 500;
    //text-transform: uppercase;
  }

  @media screen and (max-width: 600px) {
    .table-container {
      background: none;
    }
    table {
      border: 0;
    }

    table caption {
      font-size: 1.3em;
    }

    table thead {
      border: none;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px;
    }

    table tr {
      border-bottom: 3px solid #ddd;
      display: block;
      margin-bottom: 0.625em;
    }

    table td {
      border-bottom: 1px solid #ddd;
      display: block;
      font-size: 0.8em;
      text-align: left;
    }

    table td::before {
      /*
      * aria-label has no advantage, it won't be read inside a table
      content: attr(aria-label);
      */
      content: attr(data-label);
      float: left;
      font-weight: bold;
      text-transform: uppercase;
    }

    table td:last-child {
      border-bottom: 0;
    }
  }
`;i()}catch(e){i(e)}})},8657:(e,t,o)=>{o.a(e,async(e,i)=>{try{o.d(t,{g4:()=>r.Z,oJ:()=>n.Z,xr:()=>a.Z}),o(1728);var n=o(5972);o(2230);var r=o(7895),a=o(1779),l=e([n]);n=(l.then?(await l)():l)[0],i()}catch(e){i(e)}})}};