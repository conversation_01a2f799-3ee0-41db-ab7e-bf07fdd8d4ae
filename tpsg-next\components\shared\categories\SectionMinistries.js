import styled from "styled-components";
import { device } from "styles/device";
import ListLink from "../ListLink";

export default function SectionMinistries({ ministries }) {

  if (ministries?.length ===  0 ) return;

  return (
    <SectionMinistriesWrapper>
      <div><p className="label-type">Sous catégories associées</p></div>
      <div>
        {ministries?.map((ministry, key) => (
          <div key={key}>
            <ListLink
              image={ministry.cover}
              text={ministry.name}
              route={`/categories/${ministry.type}/${ministry.slug}`}
            />
          </div>
        ))}
      </div>
    </SectionMinistriesWrapper>
  );
}

const SectionMinistriesWrapper = styled.div`
  .ministries {
    margin-left: 38px;
  }
  @media ${device.tablet} {
    .ministries {
      margin-left: 50px;
    }
  }
  @media ${device.desktop} {
    flex-direction: row;
    .ministries {
      margin-left: 56px;
    }
  }
`;
