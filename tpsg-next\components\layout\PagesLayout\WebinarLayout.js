import { dateForHumans, hour } from "../../../utils/date.utils";
import { NextSeo } from "next-seo";
import { getPostRoute } from "../../../utils/posts.utils";
import { withRealSrc } from "../../../utils/image-utils";
import SvgDuotone from "../../shared/DuotoneFilter";
import { PostTitle, PostType } from "../../../styles/styled-typography";
import RoundedLabel from "../../shared/atoms/rounded-label";
import VideoPlayer from "../../shared/VideoPlayer";
import SubHeader from "../../shared/SubHeader/SubHeader";
import { RenderMarkdown } from "../../shared";
import RegisterBar from "../../webinars/RegisterBar";
import Related from "../../shared/Related";
import styled from "styled-components";
import { device } from "../../../styles/device";
import PreviewButton from "../../Preview/PreviewButton";
import Link from "next/link";


export default function WebinarLayout({ episode, preview, relatedPosts }) {

  if (!episode) return null


  const route = episode.route;
  const { lead, webinar, event, seo } = episode.modules;
  const date = dateForHumans(episode.published_at)
  const scheduled = event && new Date(event.date) > new Date();

  return (
    <>
      <NextSeo
        title={seo?.metaTitle || episode.title}
        description={seo?.metaDescription || null}
        openGraph={{
          title: seo?.metaTitle || episode.title,
          description: seo?.metaDescription || null,
          url: `https://toutpoursagloire.com${getPostRoute(episode)}`,
          images: [{
            url: withRealSrc(episode.image),
            alt: episode.image?.alternativeText || "",
          }],
        }}
        twitter={{
          site: "@t_p_s_g",
          cardType: "summary_large_image",
        }}
      />

      {preview && <PreviewButton url={route}/>}

      <PageWrapper className={"site-padding"}>

        <header>
          <div className={"header-text-container"}>
            <PostType><Link href="/webinaires">WEBINAIRE</Link></PostType>
            <PostTitle>{episode.title}</PostTitle>
            <div>
              {episode.topics && episode.topics.map((x, key) => <RoundedLabel key={key} text={x.name}/>)}
            </div>
          </div>
          <div className={"header-player-cover"}>
            <VideoPlayer
              video={!scheduled && webinar.embedVideo || null}
              image={episode.image}

            />
          </div>
        </header>
        <SubHeader>
          { !scheduled &&
            <SubHeader.Text
              label={"Date"}
              content={date}
              addClass={scheduled ? "mobile-hide_flex" : ""}/>
          }

          <SubHeader.Authors
            label={"Orateur(s)"}
            authors={webinar.speakers || false}
            addClass={scheduled ? "mobile-hide_flex" : ""}/>
          <SubHeader.Social
            url={`https://toutpoursagloire.com/webinaires/${episode.slug}`}
            addClass={"mobile-hide_flex"}/>
        </SubHeader>

        {/*{episode.modules.event && <SubscribeButton event={episode.modules.event}/>}*/}

        <article>
          {lead?.content && <section><RenderMarkdown content={lead.content}/></section>}
          <section>
            <RenderMarkdown content={episode.body}/>
          </section>
        </article>

        {scheduled &&
          <RegisterBar>
            <SubHeader>
              <SubHeader.Text label={"Date"} content={dateForHumans(event.date)}/>
              <SubHeader.Text label={"Début"} content={hour(event.date)}/>
              {/*<SubHeader.Authors authors={webinar.speakers || false} addClass={"tablet-hide_flex"}*/}
              {/*  label={"Orateur(s)"}/>*/}
              <SubHeader.LinkButton url={event.url} text={"JE M'INSCRIS"}/>
            </SubHeader>
          </RegisterBar>
        }
      </PageWrapper>
      <Related items={relatedPosts}/>
    </>
  )

}

const PageWrapper = styled.div`
  padding-bottom: 70px;

  header {
    display: flex;
    flex-direction: column;
    margin-bottom: 48px;
  }

  .header-player-cover {
    width: 100%;
    aspect-ratio: 16 / 10;
  }

  .card-label {
    display: none;
    margin: 8px 8px 0 0;
  }

  article {
    margin-top: 70px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .youtube-player-component {
    /* styles for webinaires */
  }

  @media ${device.tablet} {
    .card-label {
      display: inline-block;
    }

    .header-player-cover {
      margin-top: 48px;
    }
  }
  @media ${device.desktop} {
    header {
      margin-top: 90px;
      margin-bottom: 70px;
      flex-direction: row;
      justify-content: space-between;
    }

    .header-text-container {
      position: relative;
      width: 50%;
      aspect-ratio: 16 / 10;
      padding-top: 24px;
      padding-left: 16px;
    }

    .header-player-cover {
      margin-top: 0;
      width: calc(50% - 32px);
    }
  }
`;
