module.exports = {
  /** Met à jour la section des derniers articles
   *
   * @param   {object}    section   Section à mettre à jour
   * @param   {number}    limit     nombre de posts à récupérer
   * @param   {[number]}  nin       posts à exclure
   * @return  {Promise}   Ids des posts ajoutés au composant
   */
  updateHomeArticles: async ({section, limit, nin}) => {
    const params = {
      _limit: limit,
      _sort: "published_at:DESC",
      "type_eq": "article",
      published_at_null: false,
      id_nin: nin
    }
    const posts = await strapi.query('post').find(params);
    const Ids = posts.map((p) => p.id);
    await strapi
      .query('section.post-set')
      .update({id: section.id}, {posts: Ids});
    return Ids;
  },

  /**
   * Met à jour la section podcasts
   *
   * @param   {object}    section   Section à mettre à jour
   * @param   {[int]}     nin     Nombre de post dans la section
   * @return  {Promise}   Ids des posts ajoutés au composant
   */
  updateHomePodcasts: async ({section, nin, limit}) => {
    const params = {
      _limit: limit,
      _sort: "published_at:desc",
      "type_eq": "podcast",
      published_at_null: false,
      id_nin: nin
    }
    const posts = await strapi.query('post').find(params);
    const Ids = posts.map((p) => p.id);
    await strapi
      .query('section.post-set')
      .update({id: section.id}, {posts: Ids});
    return Ids;
  },

  /**
   * Met à jour la section bloggers
   *
   * @param   {object}    section   Section à mettre à jour
   * @param   {number}    limit     Nombre de post dans la section
   * @param   {[int]}     nin       ids des posts à exclure
   * @return  {Promise}   Ids des posts ajoutés au composant
   */
  updateHomeBloggers: async ({section, limit, nin}) => {

    const blogs = await strapi.query('blog').find({})

    const params = {
      _limit: 1,
      _sort: "published_at:desc",
      "type_eq": "article",
      published_at_null: false,
      id_nin: nin,
    }

    let blogPosts = [];
    for(const blog of blogs){
      params.blog = blog.id
      let data = await strapi.query('post').find(params).then(data => {
        return data[0]
      })
      if(data){
        blogPosts.push(data)
      }
    }

    /** Ajout des Ids posts les plus récents parmis tous les blogs*/
    const Ids = blogPosts.sort(function compare(p1, p2){
      return new Date(p2.published_at) - new Date(p1.published_at)
    }).map(p => p.id).slice(0,limit)

    await strapi
      .query('section.post-set')
      .update({id: section.id}, {posts: Ids})
    return Ids;
  },
  updateHomeWebinars: async ({section, nin, limit}) => {
    let posts = await strapi.query("post").find({
      _sort: "published_at:desc",
      type: "webinaire",
      published_at_null: false,
      id_nin: nin,
      _limit: limit
    })
    const Ids = posts.map((p) => p.id);

    await strapi
      .query('section.post-set')
      .update({id: section.id}, {posts: Ids, name: section.name})
    return Ids;
  },
  updateHomeFormations: async ({section, nin, limit}) => {
    let posts = await strapi.query("post").find({
      _sort: "published_at:desc",
      type: "formation",
      published_at_null: false,
      id_nin: nin,
      _limit: 1
    })
    const Ids = posts.map((p) => p.id);
    await strapi
      .query('section.post-set')
      .update({id: section.id}, {posts: Ids, name: section.name})
    return Ids;
  },

  /**
   * Met à jour une section de thème.
   *
   * @param   {object}    topic   Section à mettre à jour
   * @param   {[int]}     nin     Nombre de post dans la section
   * @return  {Promise}   Ids des posts ajoutés au composant
   */
  updateHomeTopics: async ({section, nin, limit}) => {
    /* le premier post servira à définir le thème du groupe de post */

    // why use _limit if you use findOne, dumbass?
    let firstPost = await strapi.query("post").find({
      _limit: 1,
      _sort: "published_at:desc",
      type: "article",
      published_at_null: false,
      id_nin: nin,
    });

    firstPost = firstPost[0];

    let Ids = [firstPost.id]      // quoi qu'il arrive, on ajoute le premier post
    let topicName = "undefined";  // correspondra au nom du thème de la section

    /* Si le post contient un thème, on ajoute les suivants */
    if(firstPost.topics.length > 0){
      const topicID = firstPost.topics[0].id;
      topicName = firstPost.topics[0].name;
      const nextPosts = await strapi
        .query('post')
        .find({
          _limit: limit > 1 ? limit - 1 : 2,
          type: "article",
          topics_in: topicID,
          published_at_null: false,
          id_nin: [firstPost.id, ...nin]
        })
      Ids = Ids.concat(nextPosts.map((post) => post.id))
    }
    await strapi
      .query('section.post-set')
      .update({ id: section.id }, { posts: Ids, name: topicName });
    return Ids;
  }
}
