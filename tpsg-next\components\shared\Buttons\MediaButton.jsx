import styled from "styled-components";

export default function MediaButton({ plateform, text }) {
  return (
    <>
      {getIcon(plateform) && (
        <Wrapper>
          {plateform && getIcon(plateform)}
          {text && <span>{text}</span>}
        </Wrapper>
      )}
    </>
  )
}

const getIcon = (name) => {
  switch (name) {
  case "apple":
    return <AppleIcon />
  case "spotify":
    return <SpotifyIcon />
  case "google":
    return <GoogleIcon />
  case "soundcloud":
    return <SoundcloudIcon />
  case "youtube":
    return <YoutubeIcon />
  default:
    return null
  }
}

const displayNames = {
  apple: "Apple podcast",
  google: "Google podcast",
  spotify: "Spotify",
  soundcloud: "Soundcloud",
  youtube: "Youtube"
}

const Wrapper = styled.button`
  
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 6px 6px 6px 6px;
  
  border: 1px solid #1E1E1D;
  background-color: transparent;
  box-sizing: border-box;
  border-radius: 100px;
  
  span {
    position: relative;
    font-family: Stelvio, sans-serif;
    white-space: nowrap;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    margin-bottom: -6px;
    margin-left: 6px;
    margin-right: 6px;
    color: #1E1E1D;
  }
  
  svg {
    height: 20px;
    width: 20px;
  }
  
  &:hover {
    background-color: #1E1E1D;
    span {
      color: #F8F8F3;
    }
    path {
      fill: #F8F8F3;
    }
  }

  cursor: pointer;
`;

const GoogleIcon = () => {
  return (
    <svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_221_586)">
        <path
          d="M11 0.499969C8.82441 0.499969 6.69767 1.14511 4.88873 2.3538C3.07979 3.5625 1.66989 5.28046 0.83733 7.29045C0.00476613 9.30044 -0.213071 11.5122 0.211367 13.646C0.635804 15.7798 1.68345 17.7398 3.22183 19.2781C4.76021 20.8165 6.72022 21.8642 8.85401 22.2886C10.9878 22.713 13.1995 22.4952 15.2095 21.6626C17.2195 20.8301 18.9375 19.4202 20.1462 17.6112C21.3549 15.8023 22 13.6756 22 11.5C21.9944 8.5843 20.8337 5.78966 18.772 3.72797C16.7103 1.66628 13.9157 0.50556 11 0.499969ZM11 19.9615C9.60898 19.9604 8.23967 19.6164 7.0133 18.9599C5.78693 18.3034 4.74132 17.3547 3.96903 16.1978C3.19674 15.0408 2.72159 13.7113 2.58563 12.327C2.44968 10.9426 2.65712 9.54606 3.18959 8.26098C3.72206 6.9759 4.56314 5.84191 5.63837 4.9594C6.71361 4.07688 7.98983 3.47306 9.35406 3.20138C10.7183 2.92971 12.1285 2.99855 13.4598 3.40183C14.7911 3.80511 16.0024 4.53037 16.9865 5.51343C17.1448 5.67206 17.2336 5.88697 17.2336 6.11103C17.2336 6.33509 17.1448 6.55 16.9865 6.70862C16.9087 6.78826 16.8158 6.85153 16.7131 6.89473C16.6105 6.93793 16.5003 6.96019 16.3889 6.96019C16.2776 6.96019 16.1674 6.93793 16.0647 6.89473C15.9621 6.85153 15.8692 6.78826 15.7913 6.70862C14.7094 5.62343 13.2917 4.93678 11.7695 4.76062C10.2473 4.58446 8.71025 4.92918 7.40905 5.73859C6.10786 6.54799 5.11925 7.77433 4.60441 9.21766C4.08956 10.661 4.07886 12.2361 4.57405 13.6863C5.06923 15.1365 6.04109 16.3762 7.33117 17.2032C8.62125 18.0302 10.1534 18.3958 11.6779 18.2403C13.2024 18.0848 14.6293 17.4175 15.7259 16.3471C16.8225 15.2767 17.5241 13.8664 17.7163 12.3461H11C10.7756 12.3461 10.5604 12.257 10.4017 12.0983C10.243 11.9396 10.1538 11.7244 10.1538 11.5C10.1538 11.2756 10.243 11.0603 10.4017 10.9016C10.5604 10.743 10.7756 10.6538 11 10.6538H18.6154C18.8398 10.6538 19.055 10.743 19.2137 10.9016C19.3724 11.0603 19.4615 11.2756 19.4615 11.5C19.4587 13.7432 18.5664 15.8938 16.9801 17.4801C15.3939 19.0663 13.2433 19.9587 11 19.9615Z"
          fill="black" />
      </g>
      <defs>
        <clipPath id="clip0_221_586">
          <rect width="22" height="22" fill="white" transform="translate(0 0.499969)" />
        </clipPath>
      </defs>
    </svg>
  )
}

const AppleIcon = () => {
  return (
    <svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M11.6769 3.82724C11.9433 3.14384 12.4092 2.5564 13.0139 2.14134C13.6187 1.72628 14.3343 1.5028 15.0678 1.49997C15.2607 1.49997 15.4457 1.57659 15.5821 1.71298C15.7185 1.84937 15.7951 2.03436 15.7951 2.22724C15.7951 2.42013 15.7185 2.60511 15.5821 2.7415C15.4457 2.87789 15.2607 2.95451 15.0678 2.95451C14.6269 2.95589 14.1967 3.09024 13.8334 3.34001C13.4701 3.58979 13.1906 3.94336 13.0315 4.35452C12.9781 4.49172 12.8843 4.60951 12.7626 4.69229C12.6409 4.77507 12.4969 4.81896 12.3496 4.81815C12.263 4.81629 12.1771 4.80095 12.0951 4.7727C12.0053 4.73849 11.9231 4.68681 11.8533 4.62064C11.7836 4.55447 11.7277 4.47513 11.6888 4.38721C11.6499 4.29929 11.6288 4.20453 11.6268 4.10841C11.6247 4.0123 11.6418 3.91673 11.6769 3.82724ZM19.9769 15.3C19.3957 15.008 18.9034 14.5656 18.5514 14.0187C18.1994 13.4718 18.0005 12.8405 17.9754 12.1906C17.9503 11.5407 18.1 10.896 18.4089 10.3236C18.7177 9.75122 19.1745 9.27216 19.7315 8.93633C19.8179 8.88149 19.8921 8.80942 19.9495 8.72463C20.0069 8.63984 20.0462 8.54414 20.0649 8.4435C20.0837 8.34286 20.0815 8.23943 20.0586 8.13966C20.0356 8.03989 19.9923 7.94592 19.9315 7.86361C18.9433 6.60456 17.5052 5.77787 15.9199 5.55746C14.3346 5.33704 12.7257 5.74008 11.4315 6.68179C10.5092 6.01095 9.41936 5.6083 8.28249 5.51839C7.14561 5.42847 6.00601 5.6548 4.98978 6.17233C3.97354 6.68986 3.12027 7.47841 2.52436 8.45077C1.92845 9.42312 1.61313 10.5414 1.61328 11.6818C1.63516 14.0794 2.3589 16.4181 3.6951 18.4091C5.00419 20.3454 6.66783 21.5 8.15874 21.5H14.7042C16.7496 21.5 19.0587 19.3272 20.3315 16.2272C20.4031 16.0568 20.4062 15.8652 20.3402 15.6925C20.2742 15.5198 20.144 15.3792 19.9769 15.3Z"
        fill="black" />
    </svg>
  )
}

const SpotifyIcon = () => {
  return (
    <svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_221_590)">
        <path
          d="M11 0.499969C8.82441 0.499969 6.69767 1.14511 4.88873 2.3538C3.07979 3.5625 1.66989 5.28046 0.83733 7.29045C0.00476613 9.30044 -0.213071 11.5122 0.211367 13.646C0.635804 15.7798 1.68345 17.7398 3.22183 19.2781C4.76021 20.8165 6.72022 21.8642 8.85401 22.2886C10.9878 22.713 13.1995 22.4952 15.2095 21.6626C17.2195 20.8301 18.9375 19.4202 20.1462 17.6112C21.3549 15.8023 22 13.6756 22 11.5C21.9944 8.5843 20.8337 5.78966 18.772 3.72797C16.7103 1.66628 13.9157 0.50556 11 0.499969ZM14.025 15.8048C13.9232 16.0031 13.7472 16.1533 13.5353 16.2226C13.3234 16.292 13.0926 16.2748 12.8933 16.175C12.3058 15.8804 11.6572 15.7282 11 15.7307C10.3381 15.7278 9.68523 15.8838 9.09616 16.1855C8.97685 16.241 8.84694 16.2698 8.71539 16.2702C8.52666 16.2681 8.34402 16.2031 8.19652 16.0853C8.04902 15.9676 7.94513 15.8039 7.90135 15.6203C7.85758 15.4367 7.87645 15.2437 7.95496 15.0721C8.03346 14.9005 8.1671 14.76 8.33462 14.673C9.16031 14.2535 10.0738 14.036 11 14.0384C11.9214 14.0375 12.8303 14.2512 13.6548 14.6625C13.8539 14.7663 14.0043 14.9443 14.0735 15.1579C14.1428 15.3716 14.1253 15.6038 14.025 15.8048ZM15.5904 12.8115C15.5401 12.9098 15.4708 12.9972 15.3866 13.0686C15.3023 13.1399 15.2048 13.1939 15.0995 13.2273C14.9943 13.2608 14.8835 13.273 14.7735 13.2634C14.6635 13.2537 14.5565 13.2224 14.4587 13.1711C13.3867 12.6267 12.201 12.3439 10.9987 12.3457C9.79644 12.3475 8.61166 12.634 7.54135 13.1817C7.41991 13.243 7.28602 13.2755 7.15 13.2769C6.99385 13.2772 6.84072 13.2338 6.70799 13.1516C6.57525 13.0693 6.46822 12.9515 6.39904 12.8115C6.34795 12.7123 6.31699 12.6039 6.30796 12.4927C6.29892 12.3814 6.31199 12.2695 6.34639 12.1633C6.3808 12.0571 6.43588 11.9588 6.50845 11.874C6.58102 11.7892 6.66965 11.7196 6.76923 11.6692C8.07771 11.0016 9.52576 10.6535 10.9947 10.6535C12.4637 10.6535 13.9117 11.0016 15.2202 11.6692C15.3198 11.7196 15.4084 11.7892 15.481 11.874C15.5535 11.9588 15.6086 12.0571 15.643 12.1633C15.6774 12.2695 15.6905 12.3814 15.6815 12.4927C15.6724 12.6039 15.6415 12.7123 15.5904 12.8115ZM17.1558 9.80766C17.1055 9.90597 17.0362 9.99333 16.952 10.0647C16.8677 10.1361 16.7701 10.1901 16.6649 10.2235C16.5597 10.2569 16.4488 10.2692 16.3388 10.2595C16.2288 10.2499 16.1218 10.2185 16.024 10.1673C14.4684 9.37332 12.7465 8.96007 11 8.96151C9.24852 8.95715 7.5217 9.37435 5.96539 10.1779C5.84812 10.2401 5.7174 10.2728 5.58462 10.273C5.39499 10.2734 5.21075 10.21 5.06146 10.0931C4.91216 9.97613 4.80647 9.81245 4.76134 9.62827C4.71621 9.44409 4.73426 9.25009 4.81258 9.07739C4.89091 8.90469 5.02498 8.76331 5.19327 8.67593C6.9877 7.75363 8.97586 7.27164 10.9934 7.2698C13.011 7.26796 15.0001 7.74632 16.7962 8.66535C16.8948 8.71668 16.9824 8.78696 17.0539 8.87217C17.1254 8.95738 17.1794 9.05585 17.2128 9.16195C17.2462 9.26804 17.2584 9.37968 17.2486 9.49048C17.2388 9.60128 17.2073 9.70906 17.1558 9.80766Z"
          fill="black" />
      </g>
      <defs>
        <clipPath id="clip0_221_590">
          <rect width="22" height="22" fill="white" transform="translate(0 0.499969)" />
        </clipPath>
      </defs>
    </svg>
  )
}

const SoundcloudIcon = () => {
  return (
    <svg width="22" height="23" viewBox="0 0 22 23" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M11.8164 6.32838V16.5661H18.5801C19.2293 16.4433 20.5277 15.766 20.5277 14.0396C20.5277 11.8815 18.5275 11.3814 16.8695 11.3814C16.8695 7.67587 13.5008 6.46875 11.8164 6.32838Z"
        fill="black" />
      <path fillRule="evenodd" clipRule="evenodd"
        d="M11.8166 18.0379C11.0037 18.0379 10.3447 17.379 10.3447 16.5661V6.32838C10.3447 5.91678 10.5171 5.52398 10.82 5.24528C11.1229 4.96658 11.5286 4.82744 11.9388 4.86163C12.9932 4.94949 14.535 5.35714 15.8528 6.33671C16.9472 7.1502 17.8744 8.35633 18.2086 10.011C18.8212 10.1093 19.4584 10.2926 20.0378 10.616C21.1782 11.2525 21.9997 12.3906 21.9997 14.0396C21.9997 16.7125 19.933 17.8081 18.8539 18.0123C18.7637 18.0293 18.6721 18.0379 18.5803 18.0379H11.8166ZM16.8696 11.3814C16.8696 8.58643 14.9531 7.21286 13.2884 6.64634C12.7461 6.46177 12.2305 6.36287 11.8166 6.32838V16.5661H18.5803C19.2295 16.4433 20.5278 15.766 20.5278 14.0396C20.5278 11.8815 18.5277 11.3814 16.8696 11.3814Z"
        fill="black" />
      <path fillRule="evenodd" clipRule="evenodd"
        d="M7.98538 5.61964C7.47733 5.61964 7.06548 6.03149 7.06548 6.53953L7.06548 17.2235C7.06548 17.7315 7.47733 18.1434 7.98538 18.1434C8.49342 18.1434 8.90527 17.7315 8.90527 17.2235L8.90527 6.53954C8.90527 6.0315 8.49342 5.61964 7.98538 5.61964Z"
        fill="black" />
      <path fillRule="evenodd" clipRule="evenodd"
        d="M4.45315 7.50483C3.9451 7.50483 3.53325 7.91668 3.53325 8.42473L3.53325 17.2234C3.53325 17.7315 3.9451 18.1433 4.45315 18.1433C4.96119 18.1433 5.37305 17.7315 5.37305 17.2234L5.37305 8.42473C5.37305 7.91668 4.9612 7.50483 4.45315 7.50483Z"
        fill="black" />
      <path fillRule="evenodd" clipRule="evenodd"
        d="M0.919947 10.9616C0.411902 10.9616 4.99609e-05 11.3734 4.99393e-05 11.8815L4.97117e-05 17.2235C4.96901e-05 17.7315 0.411902 18.1434 0.919947 18.1434C1.42799 18.1434 1.83984 17.7315 1.83984 17.2235L1.83984 11.8815C1.83984 11.3734 1.42799 10.9616 0.919947 10.9616Z"
        fill="black" />
    </svg>
  )
}

const YoutubeIcon = () => {
  return (
    <svg width="25" height="21" viewBox="0 0 25 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_243_539)">
        <path
          d="M23.8572 4.28545C23.7669 3.92336 23.5902 3.58856 23.3421 3.30973C23.0941 3.03091 22.7822 2.81639 22.4331 2.68456C19.0741 1.38813 13.7116 1.39795 13.417 1.39795C13.1223 1.39795 7.75985 1.38813 4.40092 2.68456C4.05181 2.81639 3.73987 3.03091 3.49184 3.30973C3.24381 3.58856 3.06709 3.92336 2.97681 4.28545C2.72146 5.25777 2.41699 7.04527 2.41699 10.0015C2.41699 12.9578 2.72146 14.7453 2.97681 15.7176C3.06709 16.0797 3.24381 16.4145 3.49184 16.6933C3.73987 16.9721 4.05181 17.1867 4.40092 17.3185C7.62235 18.5658 12.6706 18.6051 13.3482 18.6051H13.4857C14.1634 18.6051 19.2116 18.5658 22.4331 17.3185C22.7822 17.1867 23.0941 16.9721 23.3421 16.6933C23.5902 16.4145 23.7669 16.0797 23.8572 15.7176C24.1125 14.7453 24.417 12.9578 24.417 10.0015C24.417 7.04527 24.1125 5.25777 23.8572 4.28545ZM16.7759 10.3256L12.0616 13.4685C11.9991 13.5145 11.9232 13.5387 11.8456 13.5372C11.7805 13.5348 11.7168 13.518 11.659 13.4881C11.5962 13.4555 11.5437 13.4061 11.5073 13.3454C11.4709 13.2847 11.452 13.2151 11.4527 13.1444V6.85867C11.452 6.78791 11.4709 6.71833 11.5073 6.65765C11.5437 6.59698 11.5962 6.54757 11.659 6.51492C11.7216 6.4815 11.7921 6.46569 11.863 6.46914C11.9339 6.4726 12.0026 6.49521 12.0616 6.53456L16.7759 9.67741C16.8307 9.71192 16.8758 9.75974 16.9071 9.81642C16.9384 9.8731 16.9548 9.93678 16.9548 10.0015C16.9548 10.0663 16.9384 10.1299 16.9071 10.1866C16.8758 10.2433 16.8307 10.2911 16.7759 10.3256Z"
          fill="black" />
      </g>
      <defs>
        <clipPath id="clip0_243_539">
          <rect width="23.6364" height="20" fill="white" transform="translate(0.780273 0.00146484)" />
        </clipPath>
      </defs>
    </svg>
  )
}
