(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[835],{5918:function(n,t,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/categories/ministere/[ministry]",function(){return e(2906)}])},7326:function(n,t,e){"use strict";e.d(t,{Z:function(){return CornerStoneCard}});var i=e(2729),o=e(5893);e(1664);var r=e(9521),a=e(7421),l=e(6368);e(4871);var c=e(1304),s=e(1261);function _templateObject(){let n=(0,i._)(["\n  position: absolute;\n  width: 100%;\n  filter: blur(12px);\n  aspect-ratio: ",";\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  margin-bottom: 50px;\n  box-shadow: 0 7px 14px 3px rgba(0, 0, 0, 0.29);\n  a {\n    width: 100%;\n    display: flex;\n    justify-content: center;\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  width: 70%;\n  padding-left: 20px;\n  @media "," {\n    width: 100%;\n    padding-left: 0;\n  }\n  @media "," {\n    padding-left: 0;\n    width: 100%;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  position: relative;\n  width: 30%;\n  margin-bottom: 24px;\n  aspect-ratio: ",";\n\n\n  @media "," {\n    width: 100%;\n  }\n  @media "," {\n    width: 80%;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  color: white;\n  background-color: #0F0F0F;\n  padding: 20px 20px;\n\n  width: 100%;\n  display: flex;\n  flex-wrap: wrap;\n\n  .corner-stone-title {\n    padding-top: 8px;\n    font-weight: 400;\n    margin-top: 0;\n    margin-bottom: 8px;\n    font-size: 1.25rem;\n    font-size: clamp(1.25rem, 1.178635147190009rem + 0.35682426404995543vw, 1.5rem);\n    display: -webkit-box;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  @media "," {\n    .corner-stone-title {\n      -webkit-line-clamp:  2!important;\n    }\n  }\n  @media "," {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n"]);return _templateObject4=function(){return n},n}function CornerStoneCard(n){var t,e,i;let{post:r,options:a}=n,{showAuthor:h,showBlur:x,aspectRatio:g=1}=a,w=(null==r?void 0:null===(t=r.cta)||void 0===t?void 0:t.url)||(null==r?void 0:r.link)||((null==r?void 0:null===(e=r.route)||void 0===e?void 0:e.startsWith("/"))?r.route:"/"+(null==r?void 0:r.route));return w?(0,o.jsx)(d,{children:(0,o.jsx)(s.Z,{link:w,children:(0,o.jsxs)(f,{children:[(0,o.jsxs)(u,{aspectRatio:g,children:[x&&(0,o.jsx)(p,{aspectRatio:g,children:(0,o.jsx)(c.Z,{imageData:r.image})}),(0,o.jsx)(c.Z,{imageData:r.image})]}),(0,o.jsxs)(m,{children:[(0,o.jsx)("h2",{className:"corner-stone-title",children:null==r?void 0:r.title}),h&&r.author&&(0,o.jsx)(l.My,{children:(null===(i=r.author)||void 0===i?void 0:i.fullName)?r.author.fullName:r.author})]})]})})}):(0,o.jsx)(o.Fragment,{})}let p=r.ZP.div.withConfig({componentId:"sc-9da25472-0"})(_templateObject(),n=>n.aspectRatio),d=r.ZP.div.withConfig({componentId:"sc-9da25472-1"})(_templateObject1()),m=r.ZP.div.withConfig({componentId:"sc-9da25472-2"})(_templateObject2(),a.U.mini,a.U.desktop),u=r.ZP.div.withConfig({componentId:"sc-9da25472-3"})(_templateObject3(),n=>n.aspectRatio,a.U.desktop,a.U.mini),f=r.ZP.div.withConfig({componentId:"sc-9da25472-4"})(_templateObject4(),a.U.desktop,a.U.mini)},2929:function(n,t,e){"use strict";e.d(t,{Z:function(){return CategoriesHeader}});var i=e(2729),o=e(5893),r=e(9521),a=e(7421),l=e(1664),c=e.n(l);function _templateObject(){let n=(0,i._)(['\n  position: relative;\n  padding: var(--border-space);\n  padding-bottom: 80px;\n  padding-top: 60px;\n\n  .type {\n    font-size: clamp(1rem, 0.928635147190009rem + 0.35682426404995543vw, 1.25rem);\n    font-family: Stelvio, sans-serif;\n    margin: 0 0 24px 0;\n    color: #FFFFFF;\n    font-weight: 500;\n    letter-spacing: 1px;\n    text-transform: uppercase;\n  }\n  \n  .content{\n    position: relative;\n  }\n  \n  .header-color {\n    position: absolute;\n    overflow: hidden;\n    background-color: var(--brand-color) !important;\n    width: 100%;\n    height: calc(100% + 500px);\n    left: 0;\n    bottom: 0;\n  }\n  .content-container {\n    display: flex;\n    flex-wrap: wrap;\n  }\n  .content-container {\n    margin: 32px 0 0 0;\n    .description {\n      margin: 0;\n      font-size: 18px;\n      font-family: "Lora", serif;\n      margin: 0;\n      font-weight: 500;\n      letter-spacing: 0;\n      line-height: 24px;\n      color: white;\n    }\n    .title-container {\n      overflow-wrap: anywhere;\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: flex-start;\n      flex-direction: column;\n      align-items: flex-start;\n      .title {\n        font-size: clamp(2rem, 1.286rem + 1.905vw, 3rem);\n        line-height: clamp(2rem, 1.286rem + 1.905vw, 3rem);\n        font-family: Stelvio, sans-serif;\n        margin: 0 0 16px 0;\n        font-weight: 500;\n        letter-spacing: 0;\n        color: white; \n      }\n    }\n  }\n\n  @media '," {\n    .content-container {\n      .description-container {\n        width: 40%;\n      }\n      .title-container {\n        width: 60%;\n        padding-right: 50px;\n        .title {\n          margin: 0;\n        }\n      }\n    }\n  }\n"]);return _templateObject=function(){return n},n}function CategoriesHeader(n){let{category:t,type:e}=n;return(0,o.jsxs)(s,{children:[(0,o.jsx)("div",{className:"header-color"}),(0,o.jsxs)("div",{className:"content",children:[(0,o.jsx)(c(),{href:"/categories",className:"type",children:e+" /"}),(0,o.jsxs)("div",{className:"content-container",children:[(0,o.jsx)("div",{className:"title-container",children:(0,o.jsx)("h1",{className:"title",children:null==t?void 0:t.name})}),(0,o.jsx)("div",{className:"description-container",children:(0,o.jsx)("p",{className:"description",children:null==t?void 0:t.description})})]})]})]})}let s=r.ZP.header.withConfig({componentId:"sc-11b6a297-0"})(_templateObject(),a.U.tablet)},2906:function(n,t,e){"use strict";e.r(t),e.d(t,{__N_SSG:function(){return m},default:function(){return PageMinistry}});var i=e(2729),o=e(5893),r=e(9521),a=e(211),l=e(7421),c=e(4724),s=e(7326),p=e(3500),d=e(2929);function _templateObject(){let n=(0,i._)(["\n  margin-top: 48px;\n  .posts-container {\n    display: block;\n  }\n  @media "," {\n    margin-top: 96px;\n    .posts-container {\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: space-between;\n    }\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  width: 100%;\n  margin-bottom: 64px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n\n  .list-container {\n    padding: 0;\n    width: 100%;\n  }\n\n  .post-card-li {\n    list-style: none;\n    padding-right: 0;\n  }\n\n  @media "," {\n    width: 66.7%;\n    margin-bottom: 164px;\n    .post-card-li {\n      padding-right: 142px;\n    }\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n\n  .cornerstone-container {\n    /* position: sticky;\n    top: 60px; */\n  }\n\n  .podcast-platform {\n    display: flex;\n    flex-wrap: wrap;\n    margin-top: 64px;\n    width: 100%;\n    gap: 32px;\n  }\n\n  @media "," {\n    .podcast-platform {\n      margin-top: 16px;\n      gap: 16px;\n    }\n  }\n  @media "," {\n    width: 33.3%;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  .label-type {\n    font-size: 20px;\n    font-family: Stelvio, sans-serif;\n    margin: 0 0 16px 0;\n\n    font-weight: 500;\n    letter-spacing: 4%;\n    line-height: 32px;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  margin-top: 0;\n"]);return _templateObject4=function(){return n},n}var m=!0;function PageMinistry(n){var t,e;let{ministry:i,fallback:r}=n,l=(null==r?void 0:null===(t=r.posts)||void 0===t?void 0:t.totalHits)||0,m=null==r?void 0:null===(e=r.posts)||void 0===e?void 0:e.hits,w=(null==r?void 0:r.cornerStonesFeatured)||[];return i?(0,o.jsxs)(x,{children:[(0,o.jsx)(d.Z,{category:i,type:"minist\xe8re"}),(0,o.jsxs)(g,{children:[(0,o.jsx)("section",{children:w[0]&&(0,o.jsx)(a.g4,{content:w[0]})}),(0,o.jsxs)(u,{className:"site-padding",children:[(0,o.jsx)("p",{className:"label-type",children:(null==m?void 0:m.length)>0?"Derni\xe8res ressources":""}),(0,o.jsxs)("div",{className:"posts-container",children:[(0,o.jsxs)(f,{children:[(0,o.jsx)("ul",{className:"list-container",children:null==m?void 0:m.map((n,t)=>(0,o.jsx)("li",{className:"post-card-li",children:(0,o.jsx)(c.Z,{post:n,options:{showLead:!0,showDate:!0,showAuthor:!0}})},"post-".concat(t)))}),(0,o.jsx)(p.Z,{nbHits:l,baseUrl:"/categories/ministere/".concat(i.slug,"/ressources?page="),currentPage:1,options:{postPerPage:15}})]}),(0,o.jsx)(h,{children:(0,o.jsxs)("div",{className:"cornerstone-container",children:[w[1]&&(0,o.jsx)(s.Z,{post:w[1],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}}),w[2]&&(0,o.jsx)(s.Z,{post:w[2],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}})]})})]})]}),(0,o.jsx)("section",{children:w[3]&&(0,o.jsx)(a.g4,{content:w[3]})})]})]}):null}let u=r.ZP.section.withConfig({componentId:"sc-52e2ee4c-0"})(_templateObject(),l.U.desktop),f=r.ZP.article.withConfig({componentId:"sc-52e2ee4c-1"})(_templateObject1(),l.U.desktop),h=r.ZP.div.withConfig({componentId:"sc-52e2ee4c-2"})(_templateObject2(),l.U.tablet,l.U.desktop),x=r.ZP.div.withConfig({componentId:"sc-52e2ee4c-3"})(_templateObject3()),g=r.ZP.div.withConfig({componentId:"sc-52e2ee4c-4"})(_templateObject4())}},function(n){n.O(0,[755,764,291,915,211,749,774,888,179],function(){return n(n.s=5918)}),_N_E=n.O()}]);