module.exports = ({ env }) => ({
  graphql: {
    endpoint: '/graphql',
    shadowCRUD: true,
    playgroundAlways: false,
    depthLimit: 7,
    amountLimit: 2500,
    apolloServer: {
      tracing: false,
    },
  },
  meilisearch: {
    host: env('MEILI_HOST'),
    apiKey: env('MEILI_ADMIN_KEY'),
  },
  upload: {
    provider: 'aws-s3-enhanced',
    providerOptions: {
      accessKeyId: env('AWS_ACCESS_KEY_ID', 'FAKEACCESSKEY'),
      secretAccessKey: env('AWS_ACCESS_SECRET', 'FAKESECRETKEY'),
      region: env('AWS_REGION', 'FAKEREGION'),
      params: {
        Bucket: env('AWS_BUCKET', 'FAKEBUKET'),
      },
      customDomain: env('CDN_DOMAIN', 'FAKECDN'),
      endpoint: env('CUSTOM_S3_ENDPOINT', 'FAKEENDPOINT'), // For third-party S3-compatible storages
      prefix: null,
      quality: 80,
      webp: true,
      webpConfig: {},
      accessLevel: env('ACCESS_LEVEL', 'public-read'), // Default set to: 'public-read'
    }
  }
});
