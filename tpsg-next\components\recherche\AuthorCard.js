
import { useEffect, useState } from "react";
import { gql } from "@apollo/client";
import styled from "styled-components";
import client from "api/apollo-client";
import { device } from "styles/device";
import CondImage from "components/shared/condimage";
import ButtonLink from "components/shared/Buttons/ButtonLink";

export default function AuthorCard({ authorName }) {
  const [hasblog, setHasblog] = useState(false);
  const [authorData, setAuthorData] = useState();

  useEffect(() => {
    async function fetchAuthor() {
      let data = await client.query({
        query: QUERY_AUTHOR,
        variables: { authorName: authorName }
      }).then(response => {
        return response.data.authors
      });
      let bloggers = await client.query({
        query: QUERY_BLOGGERS_SLUG,
      }).then(response => {
        return response.data.blogs
      })
      setAuthorData(data[0])
      const authorHasBlogPos = bloggers.map(b => b.slug).indexOf(data[0]?.slug);
      if (authorHasBlogPos != -1) {
        setHasblog(true)
      }
    }
    fetchAuthor();
  }, [authorName]);

  if (authorData === undefined) {
    return <></>;
  }

  return (
    <AuthorCardWrapper>
      <AuthorImage>
        <div className={"author-picture"}>
          <CondImage imageData={authorData?.picture} />
        </div>
      </AuthorImage>
      <AuthorName>{authorData?.fullName?.trim()}</AuthorName>
      <AuthorAbout>{authorData?.about}</AuthorAbout>
      {hasblog && (
        <BtnContainer>
          <ButtonLink
            type={"soft-white"}
            text={"Voir le blog"}
            url={`/blog/${authorData?.slug}`}
          />
        </BtnContainer>
      )}
    </AuthorCardWrapper>
  );
}

const AuthorCardWrapper = styled.div`
  background-color: #111111;
  color: white;
  padding:39px;
  width: 100%;
`;
const AuthorImage = styled.div`
    display: none;
    flex-direction: row;
    margin-right: 16px;
    margin-left: 10px;
    margin-bottom: 16px;
    .author-picture {
      position: relative;
      height: 81px;
      width: 81px;
      border-radius: 80px;
      margin-left: -10px;
      overflow: hidden;
      background-color: #161616;
    }
    
    @media ${device.tablet} {
      display: flex;
    }
`;
const AuthorName = styled.h2`
  margin-bottom: 16px;
  color: #F1F1F1;
  font-family: Stelvio, sans-serif;
  font-size: 1.25rem;
  font-size: clamp(1.25rem, 1.178635147190009rem + 0.35682426404995543vw, 1.5rem);
`;
const AuthorAbout = styled.p`
  margin-bottom: 16px;
  color: #7A7A7A;
  font-family: Stelvio, sans-serif;
  font-size: 1rem;
  font-size: clamp(1rem, 0.928635147190009rem + 0.35682426404995543vw, 1.25rem);
  
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  
  @media ${device.tablet} {
    -webkit-line-clamp: 4 !important;
  }
  @media ${device.desktop} {
    -webkit-line-clamp: 5 !important;
  }
`;
const BtnContainer = styled.p`
  padding-top: 16px;
`;

const QUERY_AUTHOR = gql`
  query QueryAuthor($authorName: String!){
    authors(where: { fullName: $authorName} ){
      lastName
    	firstName
      fullName
      about
      slug
      picture {
        url
        width
        height
        provider
        alternativeText
        alternativeText
      }
    }
  }
`
const QUERY_BLOGGERS_SLUG = gql`
  query QueryBloggers {
    blogs {
      slug
    }
  }
`