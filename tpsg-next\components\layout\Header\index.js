import styled from "styled-components";
import { useEffect, useState } from "react";
import { HeaderProvider } from "context/HeaderContext";
import NavigationBar from "./NavigationBar";
import HeaderDropDown from "./HeaderDropDown";
import HeaderMenu from "./HeaderMenu";
import { useRouter } from "next/router";


export default function Header() {

  const { pathname } = useRouter();
  const [ invert, setInvert ] = useState(false);

  useEffect(() => {
    let p = pathname;
    if(p.includes("ressources")) {
      setInvert(false);
      return;
    }
    if(p.includes("/[page]") || p.includes("[vocation]") || p.includes("[ministry]") || p.includes("[]") ){
      setInvert(true);
    } else {
      setInvert(false);
    }
  }, [pathname]);

  return (
    <HeaderProvider>
      <Wrapper invert={invert}>
        <NavigationBar invert={invert}/>
        <HeaderDropDown/>
        <HeaderMenu/>
      </Wrapper>
    </HeaderProvider>
  )
}

const Wrapper = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  z-index: 1400;
  background-color: "transparent";
`;