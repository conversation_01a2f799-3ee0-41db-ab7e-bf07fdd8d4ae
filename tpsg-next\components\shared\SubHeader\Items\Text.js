import styled from "styled-components";
import { device } from "styles/device";

export default function Text({ label, content, noClass, addClass }) {
  return (
    <Wrapper className={noClass ? "" : `subheader-item ${addClass}`}>
      <p className={"subheader-text_label"}>{label}</p>
      <p className={"subheader-text_content"}>{content}</p>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  font-size: 16px;
  min-width: 10%;
  .subheader-text_label {
    font-weight: 600;
    margin-top: 4px;
    margin-bottom: 0;
  }
  .subheader-text_content {
    margin-top: 2px;
    margin-bottom: 0;
    font-weight: 400;
    color: #484848;
  }
  @media ${device.tablet} {
    font-size: 20px;
  }
  @media ${device.desktop} {
    font-size: 22px;
  }
`
