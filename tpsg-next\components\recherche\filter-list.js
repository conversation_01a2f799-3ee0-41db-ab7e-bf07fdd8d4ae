import styled from "styled-components";
import { Fragment } from "react";
import { device } from "styles/device";
import { dynamicSort } from "utils/list.utils";


const LetterSeparator = ({ word, prevWord }) => {
  if (word.charAt(0) !== prevWord.charAt(0)) {
    return (
      <li key={`s-${word.charAt(0)}`} className={"letter-separator"}>
        {word.charAt(0)}
      </li>
    );
  } else {
    return null;
  }
};

const FilterList = ({
  data,
  fieldName,
  changeFilter,
  haveDisplayName,
  separator = true }) => {

  const displayName = haveDisplayName ? "displayName" : fieldName;
  data.sort(dynamicSort(displayName));

  return (
    <Wrapper noCol={!separator}>
      {data.map((item, index) => (item.postCount == undefined || item?.postCount > 0) && (
        <Fragment key={index}>
          {index !== 0 && separator && (
            <LetterSeparator
              word={item[displayName]}
              prevWord={data[index - 1][displayName]}
            />
          )}
          <li
            onClick={() => changeFilter(item[fieldName])} key={index}>
            <span className={"list-filter"}>{item[displayName]}</span>
          </li>
        </Fragment>
      ))}
    </Wrapper>
  )
}

export default FilterList;

const Wrapper = styled.ul`
  position: relative;
  width: 100%;
  column-width: ${props => props.noCol ? "inherit" : "320px"};
  padding: 20px;
  margin-top: -100vh;
  z-index: 999;

  li {
    position: relative;
    width: auto;
    margin-bottom: 8px;
    margin-top: 0;
    list-style: none;
    font-family: "Stelvio Grotesk", Arial, sans-serif;
    font-size: 16px;
    color: #262626;
    height: 40px;
  }

  .list-filter {
    position: relative;
    margin-left: -12px;
    padding: 8px 12px 0 12px;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1; /* number of lines to show */
    line-clamp: 1;
    -webkit-box-orient: vertical;
    color: #f4f4f4;

    &:hover {
      opacity: 0.7;
    }
  }

  .letter-separator {
    position: relative;
    margin-left: -4px;
    font-family: "Stelvio Grotesk", Arial, sans-serif;
    font-weight: 500;
    font-size: 32px;
    height: 88px;
    color: rgba(255, 255, 255, 0.29);
  }

  @media ${device.tablet} {
    padding: 60px;
    li {
      font-size: 26px;
    }

    .letter-separator {
      font-size: 72px;
      margin-top: 56px;
    }
  }
`;
