module.exports = {
  webpack: (config, webpack) => {
    // Add your variable using the DefinePlugin
    config.plugins.push(
      new webpack.DefinePlugin({
        //All your custom ENVs that you want to use in frontend
        front_env: {
          CLIENT_URL: JSON.stringify(process.env.CLIENT_URL),
          CLIENT_PREVIEW_SECRET: JSON.stringify(process.env.CLIENT_PREVIEW_SECRET),
        },
      })
    );
    // Important: return the modified config
    return config;
  },
};
