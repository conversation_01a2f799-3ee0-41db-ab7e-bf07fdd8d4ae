export default function RefTagger() {
  return (
    <div dangerouslySetInnerHTML={{
      __html: `
            <script>
               var refTagger = {
                  settings: {
                     bibleVersion: "S21",
                     dropShadow: false,
                     socialSharing: [''],
                     tooltipStyle: 'dark',
                     customStyle : {
                        heading: {
                           backgroundColor : "#323232",
                           color : "#ffffff",
                           fontSize: "16px"
                        },
                        body: {
                           // fontFamily: "Stelvio",
                           fontSize: "16px",
                           color : "#d7d7db"
                        }
                     }
                  }
               };
               (function(d, t) {
               var n=d.querySelector("[nonce]");
               refTagger.settings.nonce = n && (n.nonce||n.getAttribute("nonce"));
               var g = d.createElement(t), s = d.getElementsByTagName(t)[0];
               g.src = "https://api.reftagger.com/v2/RefTagger.fr.js";
               g.nonce = refTagger.settings.nonce;
               s.parentNode.insertBefore(g, s);
               }(document, "script"));
            </script>
         `
    }}/>
  )
}
