/**
 * Script pour configurer Meilisearch v0.27.0 localement
 * Ce script configure Meilisearch pour qu'il soit compatible avec le code existant
 * sans modifier le code de production
 */

const fetch = require('node-fetch');

// Configuration
const MEILI_URL = 'http://127.0.0.1:7700';  // Utiliser l'adresse IP locale explicite
const MEILI_KEY = '';  // Laisser vide si pas de clé API
const INDEX_NAME = 'post';

async function configureMeilisearch() {
  console.log('Configuration de Meilisearch v0.27.0...');
  console.log(`URL: ${MEILI_URL}`);
  console.log(`Index: ${INDEX_NAME}`);

  try {
    // Vérifier si l'index existe
    console.log('\nVérification de l\'existence de l\'index...');
    const indexesResponse = await fetch(`${MEILI_URL}/indexes`);
    
    if (!indexesResponse.ok) {
      throw new Error(`Erreur lors de la récupération des index: ${indexesResponse.status} ${indexesResponse.statusText}`);
    }
    
    const indexes = await indexesResponse.json();
    
    const indexExists = indexes.results && indexes.results.some(index => index.uid === INDEX_NAME);
    
    if (!indexExists) {
      console.log('L\'index n\'existe pas, création de l\'index...');
      const createResponse = await fetch(`${MEILI_URL}/indexes`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          ...(MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {})
        },
        body: JSON.stringify({ uid: INDEX_NAME })
      });
      
      if (!createResponse.ok) {
        throw new Error(`Erreur lors de la création de l'index: ${createResponse.status} ${createResponse.statusText}`);
      }
      
      console.log('Index créé avec succès');
    } else {
      console.log('L\'index existe déjà');
    }
    
    // 1. Configurer les attributs filtrables
    console.log('\nConfiguration des attributs filtrables...');
    const filterableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        ...(MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {})
      },
      body: JSON.stringify([
        'title', 'topics', 'tags', 'author', 'type', 'blog', 'cs', 'series'
      ])
    });
    
    if (!filterableResponse.ok) {
      throw new Error(`Erreur lors de la configuration des attributs filtrables: ${filterableResponse.status} ${filterableResponse.statusText}`);
    }
    
    console.log(`Attributs filtrables configurés: ${filterableResponse.status}`);
    
    // 2. Configurer les attributs triables
    console.log('\nConfiguration des attributs triables...');
    const sortableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        ...(MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {})
      },
      body: JSON.stringify(['date'])
    });
    
    if (!sortableResponse.ok) {
      throw new Error(`Erreur lors de la configuration des attributs triables: ${sortableResponse.status} ${sortableResponse.statusText}`);
    }
    
    console.log(`Attributs triables configurés: ${sortableResponse.status}`);
    
    // 3. Configurer les attributs de recherche
    console.log('\nConfiguration des attributs de recherche...');
    const searchableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        ...(MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {})
      },
      body: JSON.stringify(['title', 'body', 'lead'])
    });
    
    if (!searchableResponse.ok) {
      throw new Error(`Erreur lors de la configuration des attributs de recherche: ${searchableResponse.status} ${searchableResponse.statusText}`);
    }
    
    console.log(`Attributs de recherche configurés: ${searchableResponse.status}`);
    
    // 4. Configurer les règles de classement
    console.log('\nConfiguration des règles de classement...');
    const rankingResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/ranking-rules`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        ...(MEILI_KEY ? { 'Authorization': `Bearer ${MEILI_KEY}` } : {})
      },
      body: JSON.stringify([
        'sort',
        'words',
        'typo',
        'proximity',
        'attribute',
        'exactness'
      ])
    });
    
    if (!rankingResponse.ok) {
      throw new Error(`Erreur lors de la configuration des règles de classement: ${rankingResponse.status} ${rankingResponse.statusText}`);
    }
    
    console.log(`Règles de classement configurées: ${rankingResponse.status}`);
    
    console.log('\nConfiguration de Meilisearch terminée avec succès');
    console.log('\nIMPORTANT:');
    console.log('Pour que les requêtes fonctionnent correctement, vous devez:');
    console.log('1. Utiliser uniquement les paramètres "offset" et "limit" pour la pagination');
    console.log('2. Ne pas inclure les paramètres "page" et "hitsPerPage" dans les requêtes');
    console.log('3. Redémarrer votre serveur Next.js pour appliquer les changements');
    
  } catch (error) {
    console.error('\nErreur lors de la configuration de Meilisearch:', error);
  }
}

configureMeilisearch();
