import styled from "styled-components";
import LongArrow from "components/svg/long-arrow";
import { device } from "styles/device";

/**
 *
 * @param direction
 * @param theme "light" or "dark"
 * @param onClickFunction (optionnel)
 * @return {JSX.Element}
 * @constructor
 */
export default function AnimatedArrowButton({ reverse, onClickFunction, theme, disabled }) {

  return (
    <ButtonWrapper
      theme={theme}
      reverse={reverse}
      disabled={disabled}
      onClick={(e) => onClickFunction(e)}>
      <LongArrow/>
      <LongArrow/>
    </ButtonWrapper>
  )
}


const ButtonWrapper = styled.button`
  padding: 0;
  position: relative;
  border-radius: 32px;
  background-color: transparent;
  border: 1px solid ${p => p.theme === "dark" ? "var(--c-soft-cream)" : "#161616"};

  color: ${p => p.theme === "dark" ? "var(--c-soft-cream)" : "#161616"};

  overflow: hidden;
  box-sizing: content-box;
  height: 32px;
  width: 48px;
  
  opacity: ${p => p.disabled ? 0.5 : 1};

  isolation: isolate;

  .icn-arrow {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    border-radius: 32px;
  }

  .icn-arrow:first-child {
    transform: rotate(${p => p.reverse ? "180deg" : "0"});

    * {
      fill: ${p => p.theme === "dark" ? "var(--c-soft-cream)" : "var(--soft-dark)"};
    }
  }

  .icn-arrow:last-child {
    position: absolute;
    top: 0;
    background-color: ${p => p.theme === "dark" ? "var(--c-soft-cream)" : "var(--soft-dark)"};
    transform: rotate(${p => p.reverse ? "180deg" : "0"}) translateX(-100%);

    transition: transform 350ms ease-out;

    * {
      fill: ${p => p.theme === "dark" ? "var(--soft-dark)" : "var(--c-soft-cream)"};
    }
  }

  &:active {
    border-color: var(--brand-color);
    * {
      fill: var(--brand-color);
    }
  }

  @media ${device.desktop} {
    &:active {
      border-color: ${p => p.theme === "dark" ? "var(--c-soft-cream)" : "var(--soft-dark)"};

      * {
        fill: black;
      }
    }

    &:hover {
      .icn-arrow:last-child {
        transform: rotate(${p => p.reverse ? "180deg" : "0"}) translateX(${p => !p.disabled ? "0" : "-100%"});
      }

      cursor: ${p => p.disabled ? "default" : "pointer" };
    }
  }
`;