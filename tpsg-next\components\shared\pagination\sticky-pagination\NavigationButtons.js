import styled, { css } from "styled-components";
import Link from "next/link";
import { PaginateContext } from "context/PaginateContext";
import { useContext, useState } from "react";
import { useRouter } from "next/router";

/**
 * Check si le bouton est sur une bordure pour un comportement différent
 * @param {string} direction direction du bouton (exemple: -1 = gauche ou 1 = droite)
 * @param {*} pageState
 * @returns {Boolean}
 */
function ifDisabled(direction, pageState) {
  // > 1 pour si on veut pouvoir avec un btn sauter de 2 page par 2 page
  if (direction >= 1) {
    return pageState.activePage >= pageState.maxPage;
  }
  return pageState.activePage < 2;
}

function NavButton({ reverse, direction, url, shallow }) {
  const [pageState, setPageState, setPage, nextNumber] =
    useContext(PaginateContext);

  // Test si le bouton est valide par rapport à la page actuel
  const disabled = ifDisabled(direction, pageState);

  // Désactive le link si le bouton est "disabled" (si le bouton est sur une bordure) ou au changement de page
  const handleClickLink = (e) => {
    if (disabled || pageState.loading) {
      // si le btn est désactivé ou la page est en chargement avec animation, annule l'event
      e.preventDefault();
    }else if (!e.ctrlKey) {
      // Garde la pagination actuel si ctrl+click
      setPage(pageState.activePage + direction)
    }
  };

  return (
    <ButtonWrapper>
      <Link
        href={`${url}?page=${nextNumber + direction}`}
        shallow={shallow}
        onClick={handleClickLink}
        className={"link-btn"}
        replace
      >
        <Circle
          disabled={disabled}
          reverse={reverse}
        >
          <Arrow disabled={disabled}>
            <ArrowSvg />
          </Arrow>
        </Circle>
      </Link>
    </ButtonWrapper>
  );
}

export default function NavigationButtons({ url, shallow = true }) {
  return (
    <Wrapper>
      <NavButton reverse={true} direction={-1} shallow={shallow} url={url} />
      <NavButton reverse={false} direction={1} shallow={shallow} url={url} />
    </Wrapper>
  );
}

const Wrapper = styled.div`
  display: flex;
`;
const ButtonWrapper = styled.div`
  .link-btn {
    display: block;
    width: 42px;
    user-select: none;
    -webkit-user-drag: none;
  }
`;
const Reverse = css`
  transform: rotate(-180deg);
`;
const CircleOff = css`
  opacity: 0.5;
  cursor: default;
`;
const Circle = styled.div`
  overflow: hidden;
  height: 42px;
  width: 42px;
  margin-right: 16px;
  border-radius: 100px;
  background-color: var(--soft-white);
  border: 1px solid var(--soft-dark);
  display: flex;
  align-items: center;
  justify-items: center;
  cursor: pointer;
  ${(props) => props.disabled && CircleOff};
  ${(props) => (props.reverse ? Reverse : null)};
`;

const Arrow = styled.div`
  margin: auto;
  height: 66%;
  width: 66%;

  svg {
    height: 100%;
    width: 100%;
    transform: scale(1);
    transition: 350ms;

    path {
      stroke: var(--soft-white);
      stroke-width: 0;
      transition: 350ms;
    }
  }
  ${(props) => (props.disabled ? ArrowOff : null)};
`;
const ArrowOff = css`
  svg {
    transform: rotate(45deg) scale(3.2);
    path {
      stroke-width: 1px;
    }
  }
`;

const ArrowSvg = () => {
  return (
    <svg
      width="100"
      height="100"
      viewBox="0 0 32 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M27.4626 7.30763C24.512 5.89173 22.0553 3.57515 20.4805 0.654073L21.7083 -1.04433e-06C23.6383 3.57991 27.0225 6.16306 31 7.09236L32 7.326L32 8.674L31 8.90764C27.0225 9.83694 23.6383 12.4201 21.7083 16L20.4805 15.3459C22.0553 12.4249 24.512 10.1083 27.4626 8.69236L9.78226e-07 8.69236L1.09928e-06 7.30763L27.4626 7.30763Z"
        fill="black"
      />
    </svg>
  );
};
