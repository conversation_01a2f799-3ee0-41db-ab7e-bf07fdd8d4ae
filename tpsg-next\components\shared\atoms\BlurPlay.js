import styled from "styled-components";
import { device } from "../../../styles/device";


export default function BlurPlay({ clickAction }) {
  return (
    <Wrapper onClick={clickAction}>
      <PlayButton>
        <Triangle/>
      </PlayButton>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`;

const PlayButton = styled.div`
  position: absolute;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 80px;
  width: 80px;
  border-radius: 60px;
  background-color: rgba(0, 0, 0, 0.8);

  &:hover {
    cursor: pointer;
    transform: scale(0.95);
  }

  transition: transform 350ms ease-in-out;

  @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
    background-color: rgba(0, 0, 0, 0.50);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }
  
  @media ${device.desktop} {
    height: 104px;
    width: 104px;
  }
`

const Triangle = styled.div`
  margin-left: 16px;
  width: 30px;
  height: 30px;
  background: white;
  clip-path: polygon(0 0, 0 100%, 80% 50%);
  @media ${device.desktop} {
    height: 36px;
    width: 36px;
  }
`;
