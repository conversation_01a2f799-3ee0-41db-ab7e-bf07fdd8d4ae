import styled from "styled-components";
import { useCookies } from "react-cookie";
import { Toggle } from "components/shared/inputs";
import { SmallButton } from "components/shared/Buttons";
import { device } from "styles/device";
import { useEffect, useState } from "react";
import { useFormData } from "hooks/useFormData";
import Router from "next/router";
import Link from "next/link";
import { useRouter } from "next/router";

export default function CookieBanner() {
  const router = useRouter();

  const [cookie, setCookie] = useCookies(["preferences"]);
  // const [showDetails, setShowDetails] = useState(false);
  const [showBanner, setShowBanner] = useState(false);

  const [formValues, handleFormValueChange] = useFormData({
    essentials: true,
    analytics: true,
    medias: true,
    set: true,
  });

  useEffect(() => {
    setShowBanner(!cookie.preferences?.set);
  }, [cookie]);

  function onValidation() {
    setCookie("preferences", formValues, {
      sameSite: "strict",
      path: "/",
      expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
    });
    Router.reload();
  }

  if (!showBanner) return <></>;

  if (router.asPath === "/cookies") return <></>;
  console.log(router.asPath);

  return (
    <StlCookieBannerContainer>
      <StlCookieBanner>
        <div className={"cb-top"}>
          <p className={"cb-title"}>Paramètre des cookies</p>
          <div className={"cb-entries"}>
            <div className={"cb-entry"}>
              <Toggle
                isChecked={true}
                formKey={"essentials"}
                handleValueChange={handleFormValueChange}
                label={"Nécessaires"}
              />
              <p className={"cb-entry-desc"}>
                Certains cookies sont nécessaires au fonctionnement minimal du
                site toutpoursagloire.com comme :
                <ul>
                  <li>
                    Vos préférences d’acceptation ou de rejet des cookies{" "}
                  </li>
                  <li>
                    Éventuellement des informations techniques pour le bon
                    affichage des pages
                  </li>
                </ul>
              </p>
            </div>
            <div>
              <Toggle
                isChecked={true}
                formKey={"analytics"}
                handleValueChange={handleFormValueChange}
                label={"Statistiques"}
              />
              <p className={"cb-entry-desc"}>
                Ces cookies sont utiles pour nous permettre de vous fournir des
                ressources (articles, podcasts, webinaires, …) toujours plus
                adaptées et pertinentes. Nous utilisons Google Analytics pour
                ça.
              </p>
            </div>
            <div>
              <Toggle
                isChecked={true}
                formKey={"medias"}
                handleValueChange={handleFormValueChange}
                label={"Provenance de tiers"}
              />
              <p className={"cb-entry-desc"}>
                Il s’agit du reste des cookies venant de services externes à
                toutpoursagloire.com mais pour autant utiles pour une expérience
                complète comme :
                <ul>
                  <li>
                    Les services multimédias pour les podcast et webinaires
                    (YouTube, Spotify, SoundCloud…)
                  </li>
                  <li>
                    Les services de communication pour les formulaires mails
                    (ConvertKit)
                  </li>
                  <li>
                    Les services de remontée de bug pour l&apos;amélioration
                    continue du site (Marker.io)
                  </li>
                </ul>
              </p>
            </div>
          </div>
        </div>
        <div className={"cb-bottom"}>
          <p className={"cb-details-btn"}>
            <Link href={"/cookies"}>Voir les détails</Link>
          </p>
          <SmallButton
            text={"Valider"}
            theme={"dark"}
            action={() => onValidation()}
          />
        </div>
      </StlCookieBanner>
    </StlCookieBannerContainer>
  );
}

const StlCookieBannerContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(8, 29, 33, 0.96);
`;

const StlCookieBanner = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  overflow: scroll;
  max-width: 500px;
  max-height: 66%;
  box-shadow: 0 4px 16px rgba(23, 22, 22, 0.2);
  background-color: var(--soft-white);

  p {
    font-family: Switzer, sans-serif;
    font-size: 14px;
    ul {
      margin-top: 8px;
      padding-left: 20px;
    }
  }

  .cb-top {
    padding: 32px;
  }

  .cb-title {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 18px;
    font-family: Switzer, sans-serif;
    font-weight: 600;
  }

  .cb-entries {
    margin-top: 32px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .cb-bottom {
    position: sticky;
    bottom: 0;
    width: 100%;
    padding: 32px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: var(--c-soft-cream);
  }

  .cb-details-btn {
    margin: 0;
    color: #242424;
    text-decoration: underline;
    font-family: Switzer, sans-serif;
    font-weight: 500;
    letter-spacing: 0.4px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      color: var(--brand-color);
    }
  }

  @media ${device.tablet} {
    min-width: 500px;
  }

  @media ${device.tablet} {
    position: relative;
    max-height: 100%;
  }
`;
