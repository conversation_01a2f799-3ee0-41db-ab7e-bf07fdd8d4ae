const fetch = require('node-fetch');

const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function fixSortable() {
  try {
    console.log('Configuration des attributs triables...');

    // Configurer les attributs triables
    const response = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['date'])
    });

    if (response.ok) {
      console.log('Attributs triables configurés avec succès');

      // Vérifier les attributs triables
      const settingsResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`);
      const settings = await settingsResponse.json();
      console.log('Attributs triables:', settings);
    } else {
      console.error('Erreur lors de la configuration des attributs triables:', await response.text());
    }

    console.log('Configuration terminée !');
  } catch (error) {
    console.error('Erreur lors de la configuration:', error);
  }
}

fixSortable();
