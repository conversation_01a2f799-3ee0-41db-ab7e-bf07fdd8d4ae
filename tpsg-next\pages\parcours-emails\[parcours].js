import { RenderMarkdown } from "../../components/shared";
import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import styled from "styled-components";
import { fragments, queries } from "api/gql-queries";
import Link from "next/link";

import { device } from "styles/device";
import { getModuleWithShortName, modulesAsObj } from "/utils/components.utils";

import SvgDuotone from "components/shared/DuotoneFilter";
import SubHeader from "components/shared/SubHeader/SubHeader";
import { dateForHumans } from "utils/date.utils";
import CondImage from "components/shared/condimage";
import RoundedLabel from "components/shared/atoms/rounded-label";
import { PostTitle, PostType } from "styles/styled-typography";
import Related from "components/shared/Related";
import DynamicForm from "components/shared/ConvertkitForm/DynamicForm";
import { NextSeo } from "next-seo";

export default function SingleParcours({ post, relatedPosts }) {
  if (!post) return null;

  const modules = modulesAsObj(post.modules);

  const date = dateForHumans(post.published_at);
  const lead = getModuleWithShortName(post.modules, "lead");

  return (
    <PageWrapper>
      <NextSeo
        title={modules?.seo?.metaTitle || null}
        description={modules?.seo?.metaDescription || null}
      />
      <div className={"site-padding"}>
        <SvgDuotone hexLight={"#f5e3df"} hexDark={"#62187d"} />
        <header>
          <div className={"header-text-container"}>
            <PostType>
              <Link href="/parcours-emails">PARCOURS E-MAILS</Link>
            </PostType>
            <PostTitle>{post.title}</PostTitle>
            <div className={"post-topics"}>
              {post.topics &&
                post.topics.map((x, key) => (
                  <RoundedLabel key={key} text={x.name} />
                ))}
            </div>
          </div>
          <div className={"header-img-container"}>
            <CondImage imageData={post.image} priority={true} />
          </div>
        </header>

        <SubHeader>
          <SubHeader.Text label={"Publié le"} content={date} addClass={""} />
          <SubHeader.Authors
            label={"Auteur(s)"}
            authors={[post.author] || false}
            addClass={""}
          />
          <SubHeader.Social
            url={`https://toutpoursagloire.com/parcours-emails/${post.slug}`}
            addClass={"mobile-hide_flex"}
          />
        </SubHeader>

        <MainContent>
          <LeftContent>
            {lead?.content && (
              <section>
                <RenderMarkdown content={lead.content} />
              </section>
            )}
            <section>
              <RenderMarkdown content={post.body} />
            </section>
          </LeftContent>
          <RightContent>
            {modules?.journey?.embedForm && (
              <DynamicForm
                title={"Inscription"}
                formString={modules.journey.embedForm}
              />
            )}
          </RightContent>
        </MainContent>
      </div>
      <Related items={relatedPosts} />
    </PageWrapper>
  );
}

export async function getStaticProps({ params }) {
  const post = await client
    .query({
      query: QUERY_JOURNEY,
      variables: { slug: params.parcours },
    })
    .then((response) => {
      return response.data.posts[0];
    });

  if (!post) {
    return { notFound: true };
  }

  const relatedPosts = await client
    .query({
      query: queries.QUERY_RELATED,
      variables: { id: post.id },
    })
    .then((response) => {
      return response.data.relatedPosts;
    });

  return {
    props: {
      post,
      relatedPosts: relatedPosts,
    },
    revalidate: 10,
  };
}

export async function getStaticPaths() {
  const { data } = await client.query({ query: QUERY_JOURNEY_SLUGS });
  return {
    paths: data.posts.map((post) => ({
      params: { parcours: post.slug },
    })),
    fallback: true,
  };
}

const QUERY_JOURNEY_SLUGS = gql`
  query JourneySlugs {
    posts(where: { type: "parcours" }, limit: 10) {
      slug
    }
  }
`;

const QUERY_JOURNEY = gql`
  ${fragments.CORE_POST_FIELDS}
  query Journey($slug: String!) {
    posts(where: { slug: $slug }) {
      ...CorePostFields
      modules {
        ... on ComponentModuleEmailJourney {
          __typename
          embedForm
        }
      }
    }
  }
`;

const MainContent = styled.main`
  margin-top: 70px;
  padding-bottom: 70px;
  display: block;

  @media ${device.desktop} {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start; /* Pour que les éléments commencent au haut */
    min-height: 100vh; /* Pour s'assurer que la hauteur est suffisante pour l'effet sticky */
  }
`;
const LeftContent = styled.article`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media ${device.desktop} {
    width: 50%;
  }
`;
const RightContent = styled.div`
  width: 100%;

  @media ${device.desktop} {
    padding-left: 40px;
    border-left: 1px solid #dddddd;
    width: 40%;
    position: sticky;
    top: 20px; /* Distance depuis le haut de l'écran */
    align-self: flex-start; /* Important pour sticky dans un conteneur flex */
    max-height: calc(100vh - 40px); /* Hauteur maximale pour éviter de dépasser l'écran */
    // overflow-y: auto; /* Permet le défilement si le formulaire est trop grand */
  }
`;

const PageWrapper = styled.div`
  header {
    display: flex;
    flex-direction: column;
    margin-bottom: 48px;
  }

  .card-label {
    display: none;
    margin: 8px 8px 0px 0px;
  }

  .header-img-container {
    position: relative;
    width: 100%;
    aspect-ratio: 16 / 10;
  }

  @media ${device.tablet} {
    .card-label {
      display: inline-block;
    }

    .header-img-container {
      margin-top: 48px;
    }
  }
  @media ${device.desktop} {
    header {
      margin-top: 90px;
      margin-bottom: 70px;
      flex-direction: row;
      justify-content: space-between;
    }

    .header-text-container {
      position: relative;
      width: 50%;
      aspect-ratio: 16 / 10;
      padding-top: 24px;
      padding-left: 16px;
    }

    .header-img-container {
      width: calc(50% - 32px);
      margin-top: 0px;
    }
  }
`;
