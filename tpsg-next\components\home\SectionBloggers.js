import styled from "styled-components";
import SectionHeader from "components/shared/section/SectionHeader";
import { withRealSrc } from "utils/image-utils";
import { dateForHumans } from "utils/date.utils";
import { sortByDate } from "utils/list.utils";
import SliderCard from "../test/card/SliderCard";
import Carousel from "../test/Carousel";
import { device } from "styles/device";
import { getPostRoute } from "utils/posts.utils";

export default function SectionBloggers({ data }) {

  const { posts } = data

  function RenderPost(post, index) {

    if(!post.blog) return null;

    const cardContent = {
      title: post.title,
      author: post.blog.blogger.fullName,
      authorLink: "/blog/" + post.blog.blogger.slug,
      image: withRealSrc(post.image),
      authorImage: withRealSrc(post.blog.blogger.picture),
      date: dateForHumans(post["published_at"]),
      route: getPostRoute(post)
    }
    const cardOptions = {
      theme: "light"
    }
    return (
      <SliderCard key={index} post={cardContent} options={cardOptions}/>
    );
  }

  return (
    <SectionWrapper>

      <SectionHeader
        supTitle={"Derniers articles de nos"}
        title={"Blogueurs"}
      />

      <Carousel options={{ theme: "light", seeMoreUrl: "/recherche?type=Article&page=1", seeMoreText: "Voir tout" }}>
        { posts.sort(sortByDate).map((post, index) => RenderPost(post, index) )}
      </Carousel>

    </SectionWrapper>
  )
}

const SectionWrapper = styled.section`
  padding: 56px 0;
  @media ${ device.tablet } {
    padding: 128px 0;
  }
  @media ${ device.desktop } {
    padding: 164px 0;
  }
  .section-header {
    margin-left: var(--border-space);
  }
`;
