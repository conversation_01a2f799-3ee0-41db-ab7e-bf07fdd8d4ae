import * as Card from "./Card"


// TODO: supprimer ce fichier au profit de Card/index.js
export default function CardPost({ post, cardType, haveIcon, showTopics }) {
  switch (cardType) {
  case "horizontal":
    return <></>
  case "vertical-square":
    return <Card.SquareVertical post={post} haveIcon={haveIcon}/>
  case "first-article":
    return <Card.FirstArticle post={post} haveIcon={haveIcon}/>
  default:
    return <Card.DefaultPostCard post={post} haveIcon={haveIcon} showTopics={showTopics}/>
  }
}
