"use strict";exports.id=4004,exports.ids=[4004],exports.modules={4004:(e,t,s)=>{s.d(t,{Z:()=>SvgDuotone});var l=s(997),r=s(5432);function SvgDuotone({hexLight:e,hexDark:t}){let s=(0,r.o)(e),o=(0,r.o)(t);return l.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"svg-filter",children:(0,l.jsxs)("filter",{id:"duotone-filter",children:[l.jsx("feColorMatrix",{type:"matrix",result:"grayscale",values:"1 0 0 0 0   1 0 0 0 0   1 0 0 0 0   0 0 0 1 0"}),(0,l.jsxs)("feComponentTransfer",{colorInterpolationFilters:"sRGB",result:"duotone",children:[l.jsx("feFuncR",{type:"table",tableValues:`${o.r/255} ${s.r/255}`}),l.jsx("feFuncG",{type:"table",tableValues:`${o.g/255} ${s.g/255}`}),l.jsx("feFuncB",{type:"table",tableValues:`${o.b/255} ${s.b/255}`}),l.jsx("feFuncA",{type:"table",tableValues:"0 1"})]})]})})}}};