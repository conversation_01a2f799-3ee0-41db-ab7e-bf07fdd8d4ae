import { getPostRoute } from "utils/posts.utils";
import Link from "next/link";
import styled from "styled-components";
import { device } from "styles/device"
import DynamicForm from "components/shared/ConvertkitForm/DynamicForm";

export default function SectionMostRead({ data, newsletter }) {
  const { name, posts } = data;
  return (
    <Section>
      <DynamicForm
        title={""}
        formString={newsletter}>
      </DynamicForm>
      <h2>{name}</h2>
      {posts.map((post, key) => {
        return (
          <Link key={key} href={getPostRoute(post)}>
            <MostReadItem>
              <p className={"author"}>{post.author.fullName}</p>
              <p className={"title"}>{post.title}</p>
            </MostReadItem>
          </Link>
        )
      })}
    </Section>
  )
}

const Section = styled.div`
  position: relative;
  margin-top: 60px;
  margin-bottom: 60px;
  grid-column: 1/12;
  height: auto;
  @media ${device.desktop} {
    grid-column: 9/13;
  }

  .ck-form-wrapper {
    position: relative;
    width: 100%;
    min-height: 400px;
    background-color: indianred;
  }
`;


const MostReadItem = styled.div`
  display: block;
  font-size: 22px;
  background-color: #161616;
  padding: 24px 24px 24px 24px;
  margin-bottom: 1px;

  .title {
    line-height: 115%;
    margin-top: 16px;
    margin-bottom: 0;
    color: #dcdcdc;
  }

  .author {
    margin: 0;
    font-family: "Lora", serif;
    font-style: italic;
    font-size: 15px;
    color: #888;
  }
`
