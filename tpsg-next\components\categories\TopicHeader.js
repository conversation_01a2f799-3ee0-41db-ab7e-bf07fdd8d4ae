import styled from "styled-components";
import { device } from "styles/device";
import Link from "next/link";

const mainTopicIndex = {
  "theologie-systematique": 0,
  "theoligie-biblique": 1,
  "theologie-pratique": 2,
  "theologie-historique": 3,
  "ethique": 4,
  "apologetique-et-vision-du-monde": 5,
  "ressources": 6
}

function getBreadcrumb(topic, type) {

  let parent;
  let grandParent;
  let baseRef = "/categories";

  if(topic.parent) {

    let parentSlug = topic.parent.slug;
    let parentName = topic.parent.name;

    if (topic.parent.parent) {

      let grandParentSlug = topic.parent.parent.slug;
      let grandParentName = topic.parent.parent.name;
      let grandParentRef = `${baseRef}/#theme-${mainTopicIndex[grandParentSlug] || "0"}`;
      let parentRef = `${baseRef}/${parentSlug}`;

      grandParent = (
        <Link
          href={grandParentRef}>
          {`${grandParentName} / `}
        </Link>
      )
      parent = (
        <Link href={`/categories/${topic.parent.slug}`}>
          {`${parentName} / `}
        </Link>
      )
    }
    else {
      let parentRef = `${baseRef}#theme-${mainTopicIndex[parentSlug] || "0"}`;
      parent = (
        <Link href={parentRef}>
          {`${parentName} / `}
        </Link>
      )
    }
  }

  return (
    <Breadcrumb>
      <Link href={"/categories"}>Thème / </Link>
      {grandParent && grandParent}
      {parent && parent}
    </Breadcrumb>
  )
}

export default function TopicHeader({ topic, topicName, type }) {

  return (
    <Wrapper>
      {topic ?
        getBreadcrumb(topic, type)
        :
        <Link href={"/categories"} className={"back-button"}>{type + " /" || "Thème /"}</Link>
      }
      <h1 className={"page-title"}>{topicName}</h1>
    </Wrapper>
  )
}

const Breadcrumb = styled.p`
  font-size: 17px;
  font-weight: 400;
  letter-spacing: 0.2px;
  margin-top: 24px;
  color: #080808;
  font-family: Switzer, sans-serif;
  a:hover {
    color: var(--c-brand-light);
  }
`;

const Wrapper = styled.header`
  margin-top: 50px;

  .back-button {
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 1px;
    color: #080808;

    &:hover {
      color: var(--c-brand-light);
    }
  }

  .page-title {
    margin-top: 24px;
    font-size: 48px;
    line-height: 100%;
    font-weight: 500;
    color: #080808;
  }

  @media ${device.desktop} {
    margin-top: 96px;
    .page-title {
      font-size: 104px;
    }
  }
`

