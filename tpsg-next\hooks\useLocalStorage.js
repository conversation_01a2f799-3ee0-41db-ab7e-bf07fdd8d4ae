import { useState } from "react";

export default function useLocalStorage(key, initialValue) {

  const [storedValue, setStoredValue] = useState(() => {
    if(typeof localStorage === "undefined") {
      return initialValue;
    }
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch(err) {
      console.log(err);
      return initialValue;
    }
  });

  const setValue = (value) => {
    try {
      // Allow value to be a function, so we have same API as useState
      const valueToStore = value instanceof Function ?
        value(storedValue) : value;
      // Save state
      setStoredValue(valueToStore);
      // Save to local storage
      if (typeof localStorage !== "undefined") {
        localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.log(error);
    }
  };

  return [storedValue, setValue];

}