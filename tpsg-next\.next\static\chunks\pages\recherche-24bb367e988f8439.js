(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[958],{851:function(t,n,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/recherche",function(){return e(816)}])},7001:function(t,n,e){"use strict";e.d(n,{V:function(){return o}});var i=e(5229);let a=new i.<PERSON>earch({host:"http://127.0.0.1:7700",apiKey:""}),r=a.index("post"),instantSearch=async t=>await r.search(t,{attributesToCrop:["body"],cropLength:200,limit:5}),search=async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1?arguments[1]:void 0;return await r.search(t,{attributesToRetrieve:["title","author","type","image","route","slug","date","cs","lead"],attributesToHighlight:["title"],...n})},searchHighlight=async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1?arguments[1]:void 0;return await r.search(t,{attributesToRetrieve:["title","author","type","image","route","slug","date","body","lead","topics","cs"],attributesToHighlight:["title","body","lead"],attributesToCrop:["body","lead"],cropLength:100,...n})},urls=async()=>await r.getDocuments({attributesToRetrieve:["route","date"],limit:9999}),o={instantSearch,search,searchHighlight,urls}},1026:function(t,n,e){"use strict";e.d(n,{Z:function(){return ButtonLink}});var i=e(2729),a=e(5893),r=e(1664),o=e.n(r),l=e(9521),s=e(5158),c=e(4355),p=e.n(c);function _templateObject(){let t=(0,i._)(["\n  margin: 0 16px 0 0;\n  padding: 12px 18px 6px 18px;\n  font-family: Stelvio,sans-serif;\n  font-size: 18px;\n  white-space: nowrap;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.72;\n  }\n"]);return _templateObject=function(){return t},t}function ButtonLink(t){let{text:n,url:e,type:i}=t;return(0,s.tm)(e)?(0,a.jsx)(d,{href:e,className:"button-link ".concat(p()[i]),target:"_blank",rel:"noopener noreferrer",children:n}):(0,a.jsx)(o(),{href:e,children:(0,a.jsx)(d,{className:"button-link ".concat(p()[i]),children:n})})}let d=l.ZP.a.withConfig({componentId:"sc-e62ceebf-0"})(_templateObject())},7326:function(t,n,e){"use strict";e.d(n,{Z:function(){return CornerStoneCard}});var i=e(2729),a=e(5893);e(1664);var r=e(9521),o=e(7421),l=e(6368);e(4871);var s=e(1304),c=e(1261);function _templateObject(){let t=(0,i._)(["\n  position: absolute;\n  width: 100%;\n  filter: blur(12px);\n  aspect-ratio: ",";\n"]);return _templateObject=function(){return t},t}function _templateObject1(){let t=(0,i._)(["\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  margin-bottom: 50px;\n  box-shadow: 0 7px 14px 3px rgba(0, 0, 0, 0.29);\n  a {\n    width: 100%;\n    display: flex;\n    justify-content: center;\n  }\n"]);return _templateObject1=function(){return t},t}function _templateObject2(){let t=(0,i._)(["\n  width: 70%;\n  padding-left: 20px;\n  @media "," {\n    width: 100%;\n    padding-left: 0;\n  }\n  @media "," {\n    padding-left: 0;\n    width: 100%;\n  }\n"]);return _templateObject2=function(){return t},t}function _templateObject3(){let t=(0,i._)(["\n  position: relative;\n  width: 30%;\n  margin-bottom: 24px;\n  aspect-ratio: ",";\n\n\n  @media "," {\n    width: 100%;\n  }\n  @media "," {\n    width: 80%;\n  }\n"]);return _templateObject3=function(){return t},t}function _templateObject4(){let t=(0,i._)(["\n  color: white;\n  background-color: #0F0F0F;\n  padding: 20px 20px;\n\n  width: 100%;\n  display: flex;\n  flex-wrap: wrap;\n\n  .corner-stone-title {\n    padding-top: 8px;\n    font-weight: 400;\n    margin-top: 0;\n    margin-bottom: 8px;\n    font-size: 1.25rem;\n    font-size: clamp(1.25rem, 1.178635147190009rem + 0.35682426404995543vw, 1.5rem);\n    display: -webkit-box;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  @media "," {\n    .corner-stone-title {\n      -webkit-line-clamp:  2!important;\n    }\n  }\n  @media "," {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n"]);return _templateObject4=function(){return t},t}function CornerStoneCard(t){var n,e,i;let{post:r,options:o}=t,{showAuthor:f,showBlur:g,aspectRatio:x=1}=o,b=(null==r?void 0:null===(n=r.cta)||void 0===n?void 0:n.url)||(null==r?void 0:r.link)||((null==r?void 0:null===(e=r.route)||void 0===e?void 0:e.startsWith("/"))?r.route:"/"+(null==r?void 0:r.route));return b?(0,a.jsx)(d,{children:(0,a.jsx)(c.Z,{link:b,children:(0,a.jsxs)(m,{children:[(0,a.jsxs)(h,{aspectRatio:x,children:[g&&(0,a.jsx)(p,{aspectRatio:x,children:(0,a.jsx)(s.Z,{imageData:r.image})}),(0,a.jsx)(s.Z,{imageData:r.image})]}),(0,a.jsxs)(u,{children:[(0,a.jsx)("h2",{className:"corner-stone-title",children:null==r?void 0:r.title}),f&&r.author&&(0,a.jsx)(l.My,{children:(null===(i=r.author)||void 0===i?void 0:i.fullName)?r.author.fullName:r.author})]})]})})}):(0,a.jsx)(a.Fragment,{})}let p=r.ZP.div.withConfig({componentId:"sc-9da25472-0"})(_templateObject(),t=>t.aspectRatio),d=r.ZP.div.withConfig({componentId:"sc-9da25472-1"})(_templateObject1()),u=r.ZP.div.withConfig({componentId:"sc-9da25472-2"})(_templateObject2(),o.U.mini,o.U.desktop),h=r.ZP.div.withConfig({componentId:"sc-9da25472-3"})(_templateObject3(),t=>t.aspectRatio,o.U.desktop,o.U.mini),m=r.ZP.div.withConfig({componentId:"sc-9da25472-4"})(_templateObject4(),o.U.desktop,o.U.mini)},9587:function(t,n,e){"use strict";e.d(n,{Z:function(){return HorizontalPostCard}});var i=e(2729),a=e(5893),r=e(4218),o=e(1664),l=e.n(o),s=e(9521),c=e(1304),p=e(7421),d=e(6368),u=e(4871),h=e(5158),m=e(279),f=e(3265),g=e(5675),x=e.n(g),b=e(1261);function _templateObject(){let t=(0,i._)(['\n  list-style: none;\n  position: relative;\n  width: 100%;\n\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n\n  &:after {\n    content: "";\n    display: block;\n    width: 100vw;\n    margin: 24px 0;\n    background-color: #40444444;\n    height: 1px;\n  }\n\n  .post-info {\n    width: 65%;\n    padding-right: 10px;\n  }\n  .post-image-container{\n    width: 30%;\n  }\n  .post-image {\n    width: 100%;\n    aspect-ratio: 1/1;\n    position: relative;\n  }\n  .card-topics{\n    position: relative;\n    display: flex;\n    flex-direction: row;\n    align-items: start;\n    justify-content: start;\n    flex-wrap: wrap;\n    margin-top: 16px;\n    height: 32px;\n    width: 100%;\n    overflow: hidden;\n    gap: 8px;\n  }\n  @media screen and (max-width: 320px) {\n    // Little screen only\n    .post-info {\n      width: 100%;\n    }\n  }\n  @media ',' {\n    &:after {\n      content: "";\n      display: block;\n      width: 100%;\n      margin: 37px 0 30px 0;\n\n      background-color: #40444444;\n      height: 1px;\n    }\n    .post-info {\n      width: 75%;\n      padding-right: 10px;\n    }\n    .post-image-container{\n      width: 20%;\n    }\n  }\n']);return _templateObject=function(){return t},t}function HorizontalPostCard(t){let{post:n,options:e}=t,i=!(0,f.a)({mediaQuery:p.U.tablet}),o="",s="",g=(0,u.qt)(n),w=(0,h.tm)(g);if((null==e?void 0:e.showDate)&&(o+=n.date?(0,r.S$)(n.date):(0,r.S$)(n.published_at)),(null==e?void 0:e.showAuthor)&&(null==n?void 0:n.author)){var C;o+=(""!==o?" - ":"")+((null===(C=n.author)||void 0===C?void 0:C.fullName)?n.author.fullName:n.author)}return(null==e?void 0:e.showLead)&&(s=(0,u.mj)(n)),(0,a.jsxs)(v,{children:[(0,a.jsxs)("div",{className:"post-info",children:[(0,a.jsx)(d.My,{children:o}),(0,a.jsxs)(b.Z,{link:g,children:[(0,a.jsxs)(d.kz,{className:"primary-hover",children:[n.title,w&&(0,a.jsx)("span",{children:(0,a.jsx)(x(),{src:"/images/icons/external.svg",alt:"external",fill:!0})})]}),!i&&s&&(0,a.jsx)(d.X0,{children:s})]}),e.showTopics&&(0,a.jsx)("div",{className:"card-topics",children:n.topics&&n.topics.map((t,n)=>(0,a.jsx)(m.Z,{text:t},n))})]}),(0,a.jsx)(l(),{href:g,passHref:!0,className:"post-image-container",children:(0,a.jsx)("div",{className:"post-image",children:(0,a.jsx)(c.Z,{imageData:n.image})})})]})}let v=s.ZP.li.withConfig({componentId:"sc-c8777780-0"})(_templateObject(),p.U.tablet)},3265:function(t,n,e){"use strict";e.d(n,{a:function(){return useMediaQuery}});var i=e(7294);let useMediaQuery=t=>{let{width:n,mediaQuery:e}=t,[a,r]=(0,i.useState)(!1),o=(0,i.useCallback)(t=>{t.matches?r(!0):r(!1)},[]);return(0,i.useEffect)(()=>{let t=window.matchMedia(n?"(max-width: ".concat(n,"px)"):e);return t.addEventListener("change",o),t.matches&&r(!0),()=>t.removeEventListener("change",o)},[]),a}},816:function(t,n,e){"use strict";e.r(n),e.d(n,{__N_SSP:function(){return E},default:function(){return SearchPage}});var i=e(2729),a=e(5893),r=e(7294),o=e(9521),l=e(7001),s=e(1163);let c=["podcast","formation","article","webinaire","parcours-email"];var p=e(7421);function _templateObject(){let t=(0,i._)(["\n  position: absolute;\n  top: 8px;\n  left: 0;\n  display: none;\n  width: ","px;\n  height: 80%;\n  border-right-style: solid;\n  border-right-color: #FF856A;\n  border-right-width: ",";\n  transition: width 300ms cubic-bezier(.55, .77, .17, .97);\n\n  @media "," {\n    //display: inline-block;\n  }\n"]);return _templateObject=function(){return t},t}function _templateObject1(){let t=(0,i._)(["\n  position: relative;\n  margin-top: 16px;\n  width: 100%;\n  height: 48px;\n  background-color: transparent;\n\n  @media "," {\n    height: 92px;\n  }\n\n  #ruler {\n    position: absolute;\n    font-family: Stelvio, Arial, sans-serif;\n    font-weight: 500;\n    font-size: 72px;\n    height: 72px;\n    color: transparent;\n    padding-top: 8px;\n  }\n\n  input {\n    position: absolute;\n    top: 0;\n    box-sizing: border-box;\n    @media "," {\n      //caret-color: transparent;\n      font-size: 72px;\n      height: auto;\n    }\n    padding-top: 0;\n    margin-top: 0;\n    padding-left: 0;\n    width: 100%;\n    font-family: Stelvio, Arial, sans-serif;\n    font-weight: 500;\n    font-size: 32px;\n    //border: 1px solid green;\n    border: none;\n    caret-color: #F45D3C;\n    background-color: transparent;\n    z-index: 10;\n\n    &:focus {\n      outline: none;\n      background-color: transparent;\n    }\n\n    &::placeholder {\n      color: rgba(244, 93, 60, 0.25);\n    }\n  }\n\n  // Permet de cacher le bas du caret \n  // (trop long avec la font Stelvio)\n  &:after {\n    content: '';\n    display: inline-block;\n    box-sizing: border-box;\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    height: 12px;\n    width: 100%;\n    background-color: var(--soft-white);\n    z-index: 20;\n\n    @media "," {\n      height: 20px;\n    }\n  }\n\n  p {\n    position: absolute;\n    margin-top: 0;\n    margin-bottom: 0;\n    height: 48px;\n    width: 100%; // \xe0 suprimer pour faire fonctionner le caret\n    top: 0;\n    left: 0;\n    font-family: Stelvio, Arial, sans-serif;\n    font-weight: 500;\n    font-size: 32px;\n    color: rgba(244, 93, 60, 0.5);\n    z-index: 0;\n\n    @media "," {\n      font-size: 72px;\n    }\n  }\n\n  .search-btn {\n    position: absolute;\n    top: 0;\n    right: 4px;\n    z-index: 100;\n\n    svg {\n      //background-color: rgba(138, 43, 226, 0.76);\n      height: 24px;\n      width: 24px;\n    }\n\n    @media "," {\n      top: 12px;\n      right: 10px;\n      svg {\n        height: 45px;\n        width: 45px;\n      }\n    }\n  }\n\n  .filter-label {\n    position: absolute;\n    left: 0;\n    padding: 4px 8px 4px 8px;\n    color: #f4f4f4;\n    background-color: black;\n    top: -36px;\n    font-size: 0.65rem;\n    font-weight: 400;\n    font-family: Arial, sans-serif;\n\n    &:before {\n      content: '';\n      position: absolute;\n      top: 100%;\n      left: 0;\n      transform: rotate(90deg);\n      border-bottom: 12px solid transparent;\n      border-left: 12px solid #121212;\n    }\n\n    @media "," {\n      font-size: 0.75rem;\n      top: -46px;\n      padding: 5px 10px 5px 10px;\n      &:before {\n        border-bottom: 18px solid transparent;\n        border-left: 18px solid #121212;\n      }\n    }\n  }\n"]);return _templateObject1=function(){return t},t}var big_input=t=>{let{listState:n,changeFilter:e,changeQueryTerms:i,autoFocus:o=!1}=t,[l,s]=(0,r.useState)(""),[p,h]=(0,r.useState)(n.queryTerms),[m,f]=(0,r.useState)(""),[g,x]=(0,r.useState)(0),handleTextChange=t=>{if(h(t),t.length>=3&&!n.activeFilter){var e;let n;let i=(n=null,(n=e.authors.filter(n=>{var e;return null===(e=n.fullName)||void 0===e?void 0:e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").startsWith(t.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,""))})).length>0?{type:"author",value:n[0].fullName}:(n=e.topics.filter(n=>{var e;return null===(e=n.name)||void 0===e?void 0:e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").startsWith(t.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,""))})).length>0?{type:"topics",value:n[0].name}:(n=c.filter(n=>n.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").startsWith(t.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"")))).length>0?{type:"type",value:n[0]}:n);i.value&&(i.displayValue=t+i.value.slice(t.length)),s(i)}else s(null)},handleKeyDown=t=>{9===t.keyCode&&(t.preventDefault(),l&&addFilter()),8===t.keyCode&&0===p.length&&(e(null,null),i("")),"Enter"===t.key&&i(p)},addFilter=()=>{h(""),i(""),e(l.value,l.type,!0),s(null)},updateCaret=()=>{let t=document.getElementById("input");f(p.replaceAll(" ","!").slice(0,t.selectionStart))};return(0,r.useEffect)(()=>{updateCaret()},[p]),(0,r.useEffect)(()=>{let t=document.getElementById("ruler");t&&x(t.offsetWidth)},[m]),(0,a.jsxs)(u,{children:[(null==l?void 0:l.displayValue)&&(0,a.jsx)("div",{className:"filter-label",onClick:()=>addFilter(),children:"AJOUTER: CLICK ou TAB"}),(0,a.jsxs)("p",{id:"ruler",children:[m," ",(0,a.jsx)(d,{width:g})]}),(0,a.jsx)("p",{children:(null==l?void 0:l.displayValue)||""}),(0,a.jsx)("input",{id:"input",autoFocus:o,onKeyUp:t=>updateCaret(),onKeyDown:t=>handleKeyDown(t),onClick:()=>updateCaret(),onChange:t=>handleTextChange(t.target.value),placeholder:"Rechercher",value:p}),(0,a.jsx)(SearchSVG,{})]})};let d=o.ZP.span.withConfig({componentId:"sc-939f8617-0"})(_templateObject(),t=>t.width,t=>0===t.width?"3px":"36px",p.U.tablet),u=o.ZP.div.withConfig({componentId:"sc-939f8617-1"})(_templateObject1(),p.U.tablet,p.U.tablet,p.U.tablet,p.U.tablet,p.U.tablet,p.U.tablet),SearchSVG=()=>(0,a.jsx)("div",{className:"search-btn",children:(0,a.jsxs)("svg",{width:"50",height:"50",viewBox:"0 0 50 49",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M35.0234 36.1094L47.8259 48.912L49.2401 47.4977L36.4376 34.6952L35.0234 36.1094Z",fill:"black"}),(0,a.jsx)("circle",{cx:"21.6367",cy:"21.3087",r:"20.1035",stroke:"black",strokeWidth:"2"})]})});function filter_bar_templateObject(){let t=(0,i._)(["\n  position: relative;\n  box-sizing: content-box;\n  height: 30px;\n  width: 100%;\n  overflow: hidden;\n  border-top: 1.5px solid black;\n  margin-top: -12px;\n\n  .removing {\n    transition-delay: 0ms;\n  }\n\n  .ready {\n    transform: translateX(-12px);\n  }\n\n  z-index: 20;\n\n  @media "," {\n    height: 52px;\n    margin-top: -16px;\n    border-top: 2px solid black;\n  }\n"]);return filter_bar_templateObject=function(){return t},t}function filter_bar_templateObject1(){let t=(0,i._)(["\n  position: absolute;\n  width: 100%;\n  top: ",";\n  left: 0;\n  transition: all 550ms cubic-bezier(1, 0.72, 0.15, 1.01);\n  transition-delay: 250ms;\n\n  @media "," {\n    top: ",";\n  }\n"]);return filter_bar_templateObject1=function(){return t},t}function _templateObject2(){let t=(0,i._)(["\n  position: relative;\n  width: 100%;\n  height: 30px;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  overflow: hidden;\n\n  @media "," {\n    height: 52px;\n  }\n"]);return _templateObject2=function(){return t},t}function _templateObject3(){let t=(0,i._)(["\n  position: relative;\n  width: calc(33% + 32px);\n  height: 100%;\n  padding-left: 16px;\n  margin-left: -16px;\n  border-left: 1.5px solid black;\n  cursor: pointer;\n\n  @media ",' {\n    border-left: 2px solid black;\n  }\n\n  &:hover {\n    svg {\n      path {\n        fill: orangered;\n      }\n    }\n  }\n\n  p {\n    position: relative;\n    margin-top: 8px;\n    font-size: 16px;\n    font-family: "Stelvio Grotesk", sans-serif;\n    font-weight: 500;\n  }\n\n  svg {\n    position: absolute;\n    right: 26px;\n    top: 10px;\n    transform: rotateX(',") scale(0.65);\n    transition: transform 350ms cubic-bezier(1, 0.72, 0.15, 1.01);\n  }\n\n  &:last-child {\n    svg {\n      right: 10px;\n    }\n  }\n\n  @media "," {\n    p {\n      margin-top: 16px;\n      font-size: 26px;\n    }\n\n    svg {\n      top: 22px;\n      right: 40px;\n      transform: rotateX(",") scale(1);\n    }\n\n    &:last-child {\n      svg {\n        right: 10px;\n      }\n    }\n  }\n"]);return _templateObject3=function(){return t},t}function _templateObject4(){let t=(0,i._)(['\n  position: absolute;\n  top: 30px;\n  width: 100%;\n  height: 30px;\n\n  &:hover {\n    .eraser {\n      transform: translateX(-16px);\n    }\n  }\n\n  .eraser {\n    position: absolute;\n    top: 2px;\n    left: 100%;\n    margin-left: 24px;\n    display: inline-block;\n    height: 100%;\n    width: calc(100% + 300px);\n    background-color: var(--soft-white);\n    transition: transform 200ms ease-out;\n\n    svg {\n      margin-top: -5px;\n      width: 30px;\n    }\n  }\n\n  .erase {\n    .eraser {\n      transition: transform 550ms cubic-bezier(1, 0.72, 0.15, 1.01);\n      transform: translateX(-100%);\n    }\n  }\n\n  p {\n    position: relative;\n    display: inline-block;\n    font-family: "Stelvio Grotesk", Arial, sans-serif;\n    font-weight: 500;\n    font-size: 16px;\n    margin-top: 8px;\n    cursor: pointer;\n\n    span {\n      font-weight: 700;\n    }\n  }\n\n  @media '," {\n    top: 52px;\n    height: 52px;\n\n    .eraser {\n      svg {\n        margin-top: 0;\n        width: auto;\n      }\n    }\n\n    p {\n      margin-top: 16px;\n      font-size: 26px;\n    }\n  }\n"]);return _templateObject4=function(){return t},t}let h={type:"Dans",author:"Par",topics:"Dans"};var filter_bar=t=>{let{listState:n,changeList:e,changeFilter:i}=t,[o,l]=(0,r.useState)(null),handleClick=t=>{let n=new Date().getTime();(!o||n-o>500)&&(l(n),e(t))};return(0,a.jsx)(m,{children:(0,a.jsxs)(f,{filterActive:n.activeFilter,className:n.filterOpen?"removing":"",children:[(0,a.jsxs)(g,{children:[(0,a.jsxs)(x,{active:"type"===n.activeList&&n.listOpen,onClick:()=>handleClick("type"),children:[(0,a.jsx)("p",{children:"Type"}),(0,a.jsx)(ChevArrow,{})]}),(0,a.jsxs)(x,{active:"author"===n.activeList&&n.listOpen,onClick:()=>handleClick("author"),children:[(0,a.jsx)("p",{children:"Auteur"}),(0,a.jsx)(ChevArrow,{})]}),(0,a.jsxs)(x,{active:"topic"===n.activeList&&n.listOpen,onClick:()=>handleClick("topics"),children:[(0,a.jsx)("p",{children:"Th\xe8me"}),(0,a.jsx)(ChevArrow,{})]})]}),n.activeFilter&&(0,a.jsx)(b,{onClick:()=>i(null,null),children:(0,a.jsxs)("p",{className:n.filterOpen?"":"erase",children:[(0,a.jsxs)("span",{children:[h[n.filterType],": "]}),n.activeFilter,(0,a.jsx)("span",{className:n.queryTerms.length?"eraser":"ready eraser",children:(0,a.jsx)(TabArrow,{})})]})})]})})};let m=o.ZP.div.withConfig({componentId:"sc-f3b5b2aa-0"})(filter_bar_templateObject(),p.U.tablet),f=o.ZP.div.withConfig({componentId:"sc-f3b5b2aa-1"})(filter_bar_templateObject1(),t=>t.filterActive?"-30px":"0",p.U.tablet,t=>t.filterActive?"-52px":"0"),g=o.ZP.div.withConfig({componentId:"sc-f3b5b2aa-2"})(_templateObject2(),p.U.tablet),x=o.ZP.div.withConfig({componentId:"sc-f3b5b2aa-3"})(_templateObject3(),p.U.tablet,t=>t.active?"180deg":0,p.U.tablet,t=>t.active?"180deg":0),b=o.ZP.div.withConfig({componentId:"sc-f3b5b2aa-4"})(_templateObject4(),p.U.tablet),TabArrow=()=>(0,a.jsxs)("svg",{width:"50",height:"20",viewBox:"0 0 50 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M2.51012 10.8755L2.10624 9.96067L2.14632 11.807L2.51012 10.8755ZM49.1318 9.87549L2.51012 9.87549L2.51012 11.8755L49.1318 11.8755L49.1318 9.87549ZM2.51012 10.8755C2.14632 11.807 2.14611 11.8069 2.14591 11.8068C2.14586 11.8068 2.14567 11.8067 2.14557 11.8067C2.14535 11.8066 2.14518 11.8065 2.14505 11.8065C2.14478 11.8064 2.14467 11.8063 2.14471 11.8063C2.1448 11.8064 2.1455 11.8066 2.14681 11.8072C2.14945 11.8082 2.15452 11.8103 2.16198 11.8133C2.1769 11.8194 2.20136 11.8295 2.23488 11.8438C2.30193 11.8724 2.40521 11.9178 2.54093 11.9811C2.81243 12.1078 3.21343 12.3063 3.71381 12.5867C4.71494 13.1477 6.11131 14.0351 7.66351 15.3288L8.94401 13.7925C7.28483 12.4096 5.78437 11.4544 4.69153 10.842C4.14492 10.5357 3.69964 10.3147 3.38661 10.1687C3.23007 10.0956 3.10652 10.0413 3.01981 10.0043C2.97645 9.98578 2.94229 9.97162 2.91782 9.96163C2.90558 9.95663 2.89577 9.95268 2.88843 9.94975C2.88476 9.94829 2.88171 9.94708 2.87929 9.94612C2.87808 9.94564 2.87703 9.94523 2.87613 9.94488C2.87568 9.9447 2.87527 9.94454 2.87491 9.94439C2.87472 9.94432 2.87447 9.94423 2.87438 9.94419C2.87414 9.9441 2.87392 9.94401 2.51012 10.8755ZM7.66351 15.3288C9.13958 16.5591 9.99185 17.8891 10.4743 18.9018C10.7157 19.4087 10.864 19.835 10.9506 20.1274C10.9939 20.2734 11.0216 20.3855 11.0378 20.4569C11.0459 20.4925 11.0511 20.518 11.0539 20.5323C11.0553 20.5394 11.0561 20.5438 11.0564 20.5453C11.0565 20.5461 11.0565 20.5461 11.0564 20.5454C11.0564 20.545 11.0563 20.5445 11.0561 20.5438C11.0561 20.5434 11.056 20.543 11.0559 20.5425C11.0559 20.5423 11.0558 20.5419 11.0558 20.5418C11.0557 20.5414 11.0557 20.541 12.0419 20.3755C13.0281 20.21 13.028 20.2095 13.0279 20.2091C13.0279 20.2089 13.0278 20.2085 13.0278 20.2082C13.0277 20.2075 13.0275 20.2068 13.0274 20.2061C13.0272 20.2046 13.0269 20.2029 13.0266 20.201C13.0259 20.1973 13.0251 20.1928 13.0241 20.1875C13.0222 20.1769 13.0197 20.1634 13.0164 20.1469C13.01 20.1139 13.0007 20.0693 12.9881 20.0139C12.963 19.9032 12.9245 19.7492 12.8682 19.5592C12.7557 19.1795 12.5716 18.6541 12.2799 18.0417C11.6961 16.8162 10.6793 15.2388 8.94401 13.7925L7.66351 15.3288ZM2.51012 10.8755C2.91399 11.7903 2.91412 11.7902 2.91427 11.7902C2.91434 11.7901 2.91451 11.7901 2.91464 11.79C2.91491 11.7899 2.91524 11.7897 2.91562 11.7896C2.91639 11.7892 2.91737 11.7888 2.91857 11.7883C2.92098 11.7872 2.92425 11.7857 2.92838 11.7839C2.93663 11.7802 2.94828 11.775 2.96319 11.7683C2.993 11.7548 3.03582 11.7353 3.09041 11.7099C3.19957 11.6593 3.35598 11.5855 3.54965 11.4905C3.93656 11.3007 4.47456 11.0254 5.08316 10.681C6.28637 10.0002 7.82289 9.01787 9.00335 7.86213L7.60417 6.43304C6.60801 7.40835 5.24771 8.28995 4.09825 8.94034C3.53052 9.26158 3.02822 9.51863 2.66892 9.69486C2.48949 9.78287 2.34631 9.85043 2.24916 9.89547C2.2006 9.91799 2.16359 9.93485 2.13935 9.94581C2.12723 9.95129 2.11831 9.95529 2.11274 9.95778C2.10996 9.95902 2.10801 9.95989 2.10692 9.96037C2.10637 9.96061 2.10604 9.96076 2.10593 9.96081C2.10587 9.96083 2.10587 9.96084 2.10592 9.96081C2.10595 9.9608 2.10603 9.96076 2.10604 9.96076C2.10613 9.96072 2.10624 9.96067 2.51012 10.8755ZM9.00335 7.86213C10.1921 6.69825 11.1815 5.15892 11.8604 3.95298C12.2042 3.3424 12.4772 2.80121 12.6649 2.41154C12.7588 2.21647 12.8316 2.05871 12.8816 1.94846C12.9065 1.89332 12.9258 1.85 12.9391 1.81979C12.9457 1.80468 12.9509 1.79284 12.9545 1.78443C12.9564 1.78022 12.9578 1.77687 12.9589 1.7744C12.9594 1.77316 12.9598 1.77214 12.9602 1.77135C12.9604 1.77095 12.9605 1.77061 12.9606 1.77032C12.9607 1.77018 12.9608 1.77 12.9608 1.76993C12.9609 1.76977 12.9609 1.76963 12.0419 1.37549C11.1228 0.981341 11.1229 0.981223 11.1229 0.98112C11.1229 0.981102 11.123 0.981013 11.123 0.98098C11.123 0.980916 11.123 0.980904 11.123 0.980948C11.123 0.981036 11.1228 0.981343 11.1226 0.981865C11.1222 0.982909 11.1213 0.984814 11.1201 0.987563C11.1177 0.99306 11.1139 1.00193 11.1086 1.014C11.0979 1.03816 11.0815 1.07516 11.0595 1.12374C11.0155 1.22093 10.9494 1.36428 10.8629 1.54385C10.6897 1.90347 10.4364 2.40574 10.1177 2.9718C9.4717 4.11914 8.59202 5.46587 7.60417 6.43304L9.00335 7.86213Z",fill:"black"}),(0,a.jsx)("line",{x1:"1.2666",y1:"1.18481",x2:"1.2666",y2:"20.5662",stroke:"black",strokeWidth:"2"})]}),ChevArrow=()=>(0,a.jsx)("svg",{width:"20",height:"12",viewBox:"0 0 20 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M10.2925 10.9345L9.37766 11.3384L11.224 11.2983L10.2925 10.9345ZM9.29248 9.23675L9.29248 10.9345L11.2925 10.9345L11.2925 9.23675L9.29248 9.23675ZM10.2925 10.9345C11.224 11.2983 11.2239 11.2985 11.2238 11.2987C11.2238 11.2988 11.2237 11.299 11.2237 11.2991C11.2236 11.2993 11.2235 11.2994 11.2235 11.2996C11.2234 11.2998 11.2233 11.3 11.2233 11.2999C11.2234 11.2998 11.2236 11.2991 11.2242 11.2978C11.2252 11.2952 11.2273 11.2901 11.2303 11.2826C11.2364 11.2677 11.2465 11.2433 11.2608 11.2097C11.2894 11.1427 11.3348 11.0394 11.3981 10.9037C11.5248 10.6322 11.7233 10.2312 12.0037 9.7308C12.5647 8.72967 13.4521 7.3333 14.7458 5.7811L13.2095 4.5006C11.8266 6.15978 10.8714 7.66024 10.259 8.75309C9.95265 9.2997 9.73174 9.74498 9.58568 10.058C9.51264 10.2146 9.45827 10.3381 9.42127 10.4248C9.40277 10.4682 9.38861 10.5023 9.37862 10.5268C9.37363 10.539 9.36967 10.5489 9.36674 10.5562C9.36528 10.5599 9.36407 10.5629 9.36311 10.5653C9.36264 10.5665 9.36222 10.5676 9.36187 10.5685C9.36169 10.5689 9.36153 10.5694 9.36139 10.5697C9.36132 10.5699 9.36122 10.5702 9.36118 10.5702C9.36109 10.5705 9.361 10.5707 10.2925 10.9345ZM14.7458 5.7811C15.9761 4.30503 17.3061 3.45276 18.3188 2.97033C18.8256 2.72891 19.252 2.58064 19.5444 2.49402C19.6904 2.45075 19.8025 2.42302 19.8739 2.40681C19.9095 2.39871 19.9349 2.39351 19.9493 2.3907C19.9564 2.38929 19.9608 2.38848 19.9623 2.38821C19.9631 2.38807 19.9631 2.38807 19.9624 2.3882C19.962 2.38826 19.9615 2.38835 19.9607 2.38848C19.9604 2.38854 19.96 2.38861 19.9595 2.38869C19.9593 2.38873 19.9589 2.38879 19.9588 2.38881C19.9584 2.38887 19.958 2.38894 19.7925 1.40274C19.627 0.416532 19.6265 0.416603 19.6261 0.416677C19.6259 0.416704 19.6255 0.41678 19.6252 0.416835C19.6245 0.416944 19.6238 0.417063 19.6231 0.41719C19.6216 0.417445 19.6199 0.417736 19.618 0.418064C19.6143 0.418719 19.6098 0.419522 19.6045 0.420481C19.5939 0.422398 19.5804 0.424941 19.5639 0.428177C19.5309 0.434647 19.4863 0.443896 19.4309 0.456473C19.3202 0.481619 19.1662 0.520122 18.9762 0.576414C18.5965 0.688915 18.0711 0.873037 17.4587 1.16472C16.2332 1.7485 14.6558 2.76529 13.2095 4.5006L14.7458 5.7811ZM10.2925 10.9345C11.2073 10.5306 11.2072 10.5305 11.2072 10.5303C11.2071 10.5303 11.2071 10.5301 11.207 10.53C11.2069 10.5297 11.2067 10.5294 11.2066 10.529C11.2062 10.5282 11.2058 10.5273 11.2053 10.526C11.2042 10.5236 11.2027 10.5204 11.2009 10.5162C11.1972 10.508 11.192 10.4963 11.1852 10.4814C11.1718 10.4516 11.1522 10.4088 11.1269 10.3542C11.0763 10.2451 11.0025 10.0886 10.9075 9.89497C10.7177 9.50806 10.4424 8.97006 10.098 8.36146C9.41721 7.15825 8.43486 5.62172 7.27912 4.44126L5.85003 5.84044C6.82534 6.8366 7.70694 8.1969 8.35733 9.34636C8.67857 9.9141 8.93562 10.4164 9.11185 10.7757C9.19986 10.9551 9.26743 11.0983 9.31247 11.1955C9.33498 11.244 9.35185 11.281 9.3628 11.3053C9.36828 11.3174 9.37228 11.3263 9.37477 11.3319C9.37601 11.3347 9.37688 11.3366 9.37736 11.3377C9.37761 11.3382 9.37775 11.3386 9.3778 11.3387C9.37783 11.3387 9.37783 11.3388 9.37781 11.3387C9.37779 11.3387 9.37776 11.3386 9.37775 11.3386C9.37771 11.3385 9.37766 11.3384 10.2925 10.9345ZM7.27912 4.44126C6.11524 3.25249 4.57592 2.26312 3.36998 1.58417C2.75939 1.24041 2.2182 0.967376 1.82853 0.779742C1.63346 0.685808 1.47571 0.612967 1.36545 0.563062C1.31031 0.538102 1.26699 0.518856 1.23678 0.505555C1.22167 0.498904 1.20983 0.493737 1.20142 0.490083C1.19721 0.488256 1.19386 0.486808 1.19139 0.48574C1.19016 0.485207 1.18914 0.484768 1.18834 0.484426C1.18794 0.484255 1.1876 0.484107 1.18731 0.483984C1.18717 0.483923 1.187 0.483848 1.18692 0.483817C1.18677 0.483749 1.18662 0.483687 0.792477 1.40274C0.398334 2.32179 0.398216 2.32173 0.398113 2.32169C0.398096 2.32168 0.398006 2.32164 0.397974 2.32163C0.397909 2.3216 0.397898 2.3216 0.397942 2.32162C0.398031 2.32165 0.398336 2.32179 0.398859 2.32201C0.399902 2.32246 0.401808 2.32329 0.404556 2.32448C0.410053 2.32687 0.418919 2.33073 0.430998 2.33605C0.455158 2.34669 0.492151 2.36311 0.540729 2.3851C0.63792 2.4291 0.781275 2.49525 0.960844 2.58172C1.32047 2.75488 1.82273 3.00826 2.38879 3.32695C3.53614 3.97291 4.88286 4.85259 5.85003 5.84044L7.27912 4.44126Z",fill:"black"})});var v=e(3365);function filter_list_templateObject(){let t=(0,i._)(["\n  position: relative;\n  width: 100%;\n  column-width: ",';\n  padding: 20px;\n  margin-top: -100vh;\n  z-index: 999;\n\n  li {\n    position: relative;\n    width: auto;\n    margin-bottom: 8px;\n    margin-top: 0;\n    list-style: none;\n    font-family: "Stelvio Grotesk", Arial, sans-serif;\n    font-size: 16px;\n    color: #262626;\n    height: 40px;\n  }\n\n  .list-filter {\n    position: relative;\n    margin-left: -12px;\n    padding: 8px 12px 0 12px;\n    border-radius: 50px;\n    cursor: pointer;\n    font-weight: 400;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 1; /* number of lines to show */\n    line-clamp: 1;\n    -webkit-box-orient: vertical;\n    color: #f4f4f4;\n\n    &:hover {\n      opacity: 0.7;\n    }\n  }\n\n  .letter-separator {\n    position: relative;\n    margin-left: -4px;\n    font-family: "Stelvio Grotesk", Arial, sans-serif;\n    font-weight: 500;\n    font-size: 32px;\n    height: 88px;\n    color: rgba(255, 255, 255, 0.29);\n  }\n\n  @media '," {\n    padding: 60px;\n    li {\n      font-size: 26px;\n    }\n\n    .letter-separator {\n      font-size: 72px;\n      margin-top: 56px;\n    }\n  }\n"]);return filter_list_templateObject=function(){return t},t}let LetterSeparator=t=>{let{word:n,prevWord:e}=t;return n.charAt(0)!==e.charAt(0)?(0,a.jsx)("li",{className:"letter-separator",children:n.charAt(0)},"s-".concat(n.charAt(0))):null};var filter_list=t=>{let{data:n,fieldName:e,changeFilter:i,haveDisplayName:o,separator:l=!0}=t,s=o?"displayName":e;return n.sort((0,v.IQ)(s)),(0,a.jsx)(w,{noCol:!l,children:n.map((t,o)=>(void 0==t.postCount||(null==t?void 0:t.postCount)>0)&&(0,a.jsxs)(r.Fragment,{children:[0!==o&&l&&(0,a.jsx)(LetterSeparator,{word:t[s],prevWord:n[o-1][s]}),(0,a.jsx)("li",{onClick:()=>i(t[e]),children:(0,a.jsx)("span",{className:"list-filter",children:t[s]})},o)]},o))})};let w=o.ZP.ul.withConfig({componentId:"sc-d0a18ee-0"})(filter_list_templateObject(),t=>t.noCol?"inherit":"320px",p.U.tablet);var C=e(1510);function search_tool_templateObject(){let t=(0,i._)(["\n  position: relative;\n  width: 100%;\n\n  div {\n    width: 100%;\n    //background-color: #EF4523;\n    //background-color: black;\n  }\n"]);return search_tool_templateObject=function(){return t},t}function search_tool_templateObject1(){let t=(0,i._)(["\n  position: sticky;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: ","vh;\n  transform-origin: top;\n  background-color: #161616;\n  //background-color: #E9EDFB;\n  //background-color: #F45D3C;\n  transform: ",";\n  transition: all 650ms cubic-bezier(1, 0.72, 0.15, 1.01);\n  z-index: 10;\n"]);return search_tool_templateObject1=function(){return t},t}function getInitialState(t){var n,e,i,a;return{activeList:(null==t?void 0:null===(n=t.filter)||void 0===n?void 0:n.type)?t.filter.type:"type",activeFilter:null==t?void 0:null===(e=t.filter)||void 0===e?void 0:e.value,filterType:null==t?void 0:null===(i=t.filter)||void 0===i?void 0:i.type,queryTerms:(null==t?void 0:t.terms)?t.terms:"",listOpen:!1,filterOpen:!!(null==t?void 0:null===(a=t.filter)||void 0===a?void 0:a.type),listVisible:!1}}var search_tool=t=>{let{setQuery:n,initQuery:e,autoFocus:i=!1}=t,o=(0,C.o)();o.types=[{name:"Podcast",displayName:"Podcasts"},{name:"Formation",displayName:"Formations"},{name:"Article",displayName:"Articles"},{name:"Webinaire",displayName:"Webinaires"},{name:"Parcours",displayName:"Parcours"}];let[l,s]=(0,r.useState)(getInitialState(e));return(0,r.useEffect)(()=>{l!==getInitialState(e)&&n({terms:l.queryTerms,filter:{value:l.activeFilter,type:l.filterType},page:0})},[l.activeFilter,l.queryTerms]),(0,r.useEffect)(()=>{setTimeout(()=>{s(t=>({...t,listVisible:l.listOpen}))},l.listOpen?350:0)},[l.listOpen]),(0,a.jsxs)(r.Fragment,{children:[(0,a.jsx)(big_input,{changeFilter:changeFilter,listState:l,changeQueryTerms:function(t){s(n=>({...n,queryTerms:t}))},autoFocus:i}),(0,a.jsx)(filter_bar,{setListState:s,listState:l,changeList:function(t){if(l.activeList===t&&l.listOpen){closeList();return}s(n=>({...n,activeList:t,listOpen:!0}))},changeFilter:changeFilter}),(0,a.jsxs)(_,{open:l.listOpen,children:[(0,a.jsx)(j,{open:l.listOpen}),(0,a.jsxs)("div",{className:l.listVisible?"lists visible":"lists",children:["author"===l.activeList&&l.listVisible&&(0,a.jsx)(filter_list,{separator:!0,changeFilter:changeFilter,haveDisplayName:!0,data:o.authors.map(t=>({displayName:t.lastName+" "+t.firstName,fullName:t.fullName})),fieldName:"fullName"}),"topics"===l.activeList&&l.listVisible&&(0,a.jsx)(filter_list,{separator:!0,changeFilter:changeFilter,data:o.topics,fieldName:"name"}),"type"===l.activeList&&l.listVisible&&(0,a.jsx)(filter_list,{separator:!1,changeFilter:changeFilter,data:o.types,haveDisplayName:!0,fieldName:"name"})]})]})]});function closeList(){window.scrollTo({top:0}),s(t=>({...t,listOpen:!1}))}function changeFilter(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l.activeList,e=arguments.length>2&&void 0!==arguments[2]&&arguments[2];t||s(t=>({...t,queryTerms:e||t.queryTerms,filterOpen:!1})),setTimeout(()=>{s(e=>({...e,activeFilter:t,filterType:n,filterOpen:!1!==t}))},t?0:250),closeList()}};let _=o.ZP.div.withConfig({componentId:"sc-137cca13-0"})(search_tool_templateObject()),j=o.ZP.div.withConfig({componentId:"sc-137cca13-1"})(search_tool_templateObject1(),t=>t.open?"100":"0",t=>t.open?"scaleY(1)":"scaleY(0)");var y=e(7481);function search_paginate_templateObject(){let t=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  justify-content: left;\n  align-items: end;\n  height: 40px;\n  width: 100%;\n  color: white;\n  font-size: 26px;\n  margin: 72px auto;\n  transform: scale(0.8);\n  @media "," {\n    transform: scale(1);\n  }\n"]);return search_paginate_templateObject=function(){return t},t}function search_paginate_templateObject1(){let t=(0,i._)(["\n  color: black;\n"]);return search_paginate_templateObject1=function(){return t},t}function search_paginate_templateObject2(){let t=(0,i._)(["\n  display: inline-block;\n  height: 42px;\n  width: 42px;\n  margin: auto 10px;\n  font-size: 26px;\n  font-family: Stelvio, sans-serif;\n  border-radius: 100px;\n  padding-top: 10px;\n  text-align: center;\n  color: ",";\n  background-color: ",";\n\n  &:hover {\n    background-color: transparent;\n    cursor: pointer;\n    border: 1.5px solid black;\n  }\n"]);return search_paginate_templateObject2=function(){return t},t}var search_paginate=t=>{let{nbHits:n,currentPage:e,changePage:i}=t,r=(0,y.Z)(n,e,10,5);return(0,a.jsxs)(O,{children:[r.startPage>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(z,{onClick:()=>i(1),children:1}),(0,a.jsx)(k,{children:"..."})]}),r.pages.map((t,n)=>(0,a.jsx)(z,{onClick:()=>i(t),active:t===r.currentPage,children:t},n)),r.totalPages!==r.endPage&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(k,{children:"..."}),(0,a.jsx)(z,{active:r.currentPage===r.endPage,onClick:()=>i(r.totalPages),children:r.totalPages})]})]})};let O=o.ZP.div.withConfig({componentId:"sc-dbe3a1d-0"})(search_paginate_templateObject(),p.U.tablet),k=o.ZP.span.withConfig({componentId:"sc-dbe3a1d-1"})(search_paginate_templateObject1()),z=o.ZP.span.withConfig({componentId:"sc-dbe3a1d-2"})(search_paginate_templateObject2(),t=>t.active?"white":"black",t=>t.active?"#080808":"none");var N=e(9587),L=e(7326),P=e(7283),Z=e(3847),S=e(1304),A=e(1026);function AuthorCard_templateObject(){let t=(0,i._)(["\n  background-color: #111111;\n  color: white;\n  padding:39px;\n  width: 100%;\n"]);return AuthorCard_templateObject=function(){return t},t}function AuthorCard_templateObject1(){let t=(0,i._)(["\n    display: none;\n    flex-direction: row;\n    margin-right: 16px;\n    margin-left: 10px;\n    margin-bottom: 16px;\n    .author-picture {\n      position: relative;\n      height: 81px;\n      width: 81px;\n      border-radius: 80px;\n      margin-left: -10px;\n      overflow: hidden;\n      background-color: #161616;\n    }\n    \n    @media "," {\n      display: flex;\n    }\n"]);return AuthorCard_templateObject1=function(){return t},t}function AuthorCard_templateObject2(){let t=(0,i._)(["\n  margin-bottom: 16px;\n  color: #F1F1F1;\n  font-family: Stelvio, sans-serif;\n  font-size: 1.25rem;\n  font-size: clamp(1.25rem, 1.178635147190009rem + 0.35682426404995543vw, 1.5rem);\n"]);return AuthorCard_templateObject2=function(){return t},t}function AuthorCard_templateObject3(){let t=(0,i._)(["\n  margin-bottom: 16px;\n  color: #7A7A7A;\n  font-family: Stelvio, sans-serif;\n  font-size: 1rem;\n  font-size: clamp(1rem, 0.928635147190009rem + 0.35682426404995543vw, 1.25rem);\n  \n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n\n  \n  @media "," {\n    -webkit-line-clamp: 4 !important;\n  }\n  @media "," {\n    -webkit-line-clamp: 5 !important;\n  }\n"]);return AuthorCard_templateObject3=function(){return t},t}function AuthorCard_templateObject4(){let t=(0,i._)(["\n  padding-top: 16px;\n"]);return AuthorCard_templateObject4=function(){return t},t}function _templateObject5(){let t=(0,i._)(["\n  query QueryAuthor($authorName: String!){\n    authors(where: { fullName: $authorName} ){\n      lastName\n    	firstName\n      fullName\n      about\n      slug\n      picture {\n        url\n        width\n        height\n        provider\n        alternativeText\n        alternativeText\n      }\n    }\n  }\n"]);return _templateObject5=function(){return t},t}function _templateObject6(){let t=(0,i._)(["\n  query QueryBloggers {\n    blogs {\n      slug\n    }\n  }\n"]);return _templateObject6=function(){return t},t}function AuthorCard(t){var n;let{authorName:e}=t,[i,o]=(0,r.useState)(!1),[l,s]=(0,r.useState)();return((0,r.useEffect)(()=>{(async function(){var t;let n=await Z.Z.query({query:B,variables:{authorName:e}}).then(t=>t.data.authors),i=await Z.Z.query({query:M}).then(t=>t.data.blogs);s(n[0]);let a=i.map(t=>t.slug).indexOf(null===(t=n[0])||void 0===t?void 0:t.slug);-1!=a&&o(!0)})()},[e]),void 0===l)?(0,a.jsx)(a.Fragment,{}):(0,a.jsxs)(F,{children:[(0,a.jsx)(I,{children:(0,a.jsx)("div",{className:"author-picture",children:(0,a.jsx)(S.Z,{imageData:null==l?void 0:l.picture})})}),(0,a.jsx)(U,{children:null==l?void 0:null===(n=l.fullName)||void 0===n?void 0:n.trim()}),(0,a.jsx)(T,{children:null==l?void 0:l.about}),i&&(0,a.jsx)(D,{children:(0,a.jsx)(A.Z,{type:"soft-white",text:"Voir le blog",url:"/blog/".concat(null==l?void 0:l.slug)})})]})}let F=o.ZP.div.withConfig({componentId:"sc-fbd4eae6-0"})(AuthorCard_templateObject()),I=o.ZP.div.withConfig({componentId:"sc-fbd4eae6-1"})(AuthorCard_templateObject1(),p.U.tablet),U=o.ZP.h2.withConfig({componentId:"sc-fbd4eae6-2"})(AuthorCard_templateObject2()),T=o.ZP.p.withConfig({componentId:"sc-fbd4eae6-3"})(AuthorCard_templateObject3(),p.U.tablet,p.U.desktop),D=o.ZP.p.withConfig({componentId:"sc-fbd4eae6-4"})(AuthorCard_templateObject4()),B=(0,P.Ps)(_templateObject5()),M=(0,P.Ps)(_templateObject6());function recherche_templateObject(){let t=(0,i._)(["\n  position: relative;\n  width: 100%;\n  padding-top: 48px;\n"]);return recherche_templateObject=function(){return t},t}function recherche_templateObject1(){let t=(0,i._)(['\n  display: flex;\n  flex-direction: row;\n  margin-top: 72px;\n  margin-bottom: 40px;\n  width: 100%;\n\n  h2 {\n    margin-top: 0;\n    font-size: 20px;\n    font-family: "Switzer", sans-serif;\n    margin-bottom: 0;\n    font-weight: 600;\n  }\n\n  @media '," {\n    //margin-bottom: 42px;\n  }\n"]);return recherche_templateObject1=function(){return t},t}function recherche_templateObject2(){let t=(0,i._)(["\n  display: flex;\n  justify-content: center;\n  .center-results {\n    width: 100%;\n  }\n  /* @media "," {\n    .center-results{\n      width: 85%;\n    }\n  } */\n"]);return recherche_templateObject2=function(){return t},t}function recherche_templateObject3(){let t=(0,i._)(["\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  .all-posts {\n    width: 100%;\n  }\n  .all-cs {\n    width: 100%;\n  }\n  .cornerstone-container {\n    /* position: sticky;\n    top: 20px; */\n    display: flex;\n    flex-wrap: wrap;\n    gap: 16px;\n    justify-content: center;\n    margin-bottom: 40px;\n  }\n  @media "," {\n    .all-posts {\n      width: 65.23%;\n      padding-right: 7.9%;\n      //border-right: 2px solid #DEDCD8;\n    }\n\n    .all-cs {\n      width: 30.16%;\n    }\n  }\n"]);return recherche_templateObject3=function(){return t},t}e(7915);var E=!0;function SearchPage(t){var n,e,i;let{initialQuery:o,initialResults:c,initialCornerStones:p}=t,d=(0,s.useRouter)(),[u,h]=(0,r.useState)(o),[m,f]=(0,r.useState)(c),[g,x]=(0,r.useState)(p),updateUrl=t=>{let n="";n+=t.terms?"?terms=".concat(t.terms):"",n+=t.filter.value&&t.filter.type?"".concat(n.length?"&":"?").concat(t.filter.type,"=").concat(t.filter.value):"",n+=t.page?"".concat(n.length?"&":"?","page=").concat(t.page):"";let e="/recherche".concat(n);d.push(e,void 0,{shallow:!0})};return(0,r.useEffect)(()=>{async function fetchData(){var t,n,e,i,a,r,o;let s,c,p,d,h;updateUrl(u);let m=(s=[],c="",p=4,(null===(n=u.filter)||void 0===n?void 0:n.type)&&(null===(e=u.filter)||void 0===e?void 0:e.value)?(s.push(""+(null==u?void 0:u.filter.type)+' = "'+(null==u?void 0:u.filter.value.trim())+'"'),(null==u?void 0:null===(i=u.filter)||void 0===i?void 0:i.type)!=="author"&&(null==u?void 0:null===(a=u.filter)||void 0===a?void 0:a.type)!=="author"?c=u.terms||null:p=3):c=u.terms||null,s.push("cs=true"),{terms:c,params:{filter:s,sort:["date:desc"],limit:p}}),g=await l.V.search(m.terms,m.params);x(g);let b=(d=[],h=+u.page,(null===(r=u.filter)||void 0===r?void 0:r.type)&&(null===(o=u.filter)||void 0===o?void 0:o.value)&&(d=[""+u.filter.type+' ="'+u.filter.value.trim()+'"']),{terms:u.terms||null,params:{filter:d,page:h,sort:["date:desc"],hitsPerPage:10}}),v=await l.V.searchHighlight(b.terms,b.params);v.hits=null==v?void 0:null===(t=v.hits)||void 0===t?void 0:t.filter(t=>{var n;return!(null==g?void 0:null===(n=g.hits)||void 0===n?void 0:n.find(n=>n.slug===t.slug))}),f(v)}1>+u.page||+u.page>1&&0===m.totalPages?h(t=>({...t,page:1})):+u.page>m.totalPages&&m.totalPages,u!==o&&fetchData()},[u]),(0,a.jsxs)(V,{className:"site-padding",children:[(0,a.jsx)(search_tool,{setQuery:h,initQuery:u,autoFocus:!0}),(0,a.jsx)(q,{children:(0,a.jsxs)("div",{className:"center-results",children:[(0,a.jsx)(H,{children:(0,a.jsxs)("h2",{children:[m.totalHits," R\xe9sultat",m.totalHits>1&&"s"]})}),(0,a.jsxs)(Q,{children:[(0,a.jsxs)("div",{className:"all-posts",children:[null==m?void 0:m.hits.map((t,n)=>{var e;return(0,a.jsx)(N.Z,{post:t,options:{showAuthor:!0,showDate:!0,showTopics:!0}},(null===(e=t.image)||void 0===e?void 0:e.src)+""+n)}),(null==m?void 0:null===(n=m.hits)||void 0===n?void 0:n.length)>0&&(0,a.jsx)(search_paginate,{nbHits:m.totalHits,currentPage:u.page||1,changePage:function(t){window.scrollTo({top:0}),h(n=>({...n,page:t}))}})]}),(0,a.jsx)("div",{className:"all-cs",children:(0,a.jsxs)("div",{className:"cornerstone-container",children:[(null===(e=u.filter)||void 0===e?void 0:e.type)==="author"&&(0,a.jsx)(AuthorCard,{authorName:u.filter.value}),null==g?void 0:null===(i=g.hits)||void 0===i?void 0:i.map((t,n)=>{var e;return t.link=null==t?void 0:null===(e=t.cta)||void 0===e?void 0:e.url,(0,a.jsx)(L.Z,{post:t,options:{showAuthor:!0}},n)})]})})]})]})})]})}let V=o.ZP.div.withConfig({componentId:"sc-441d5efd-0"})(recherche_templateObject()),H=o.ZP.div.withConfig({componentId:"sc-441d5efd-1"})(recherche_templateObject1(),p.U.tablet),q=o.ZP.div.withConfig({componentId:"sc-441d5efd-2"})(recherche_templateObject2(),p.U.desktop),Q=o.ZP.section.withConfig({componentId:"sc-441d5efd-3"})(recherche_templateObject3(),p.U.desktop)},6368:function(t,n,e){"use strict";e.d(n,{DZ:function(){return m},GN:function(){return p},My:function(){return h},NZ:function(){return c},V1:function(){return l},X0:function(){return u},bP:function(){return s},hQ:function(){return f},kz:function(){return d}});var i=e(2729),a=e(9521),r=e(7421);function _templateObject(){let t=(0,i._)(["\n  position: relative;\n  font-weight: 500;\n  color: #161616;\n  font-size: 48px;\n  margin-top:  calc(var(--spacing-l) - 48px * ",");\n  margin-bottom: calc(var(--spacing-l) - 48px * ",");\n  \n  @media "," {\n    font-size: 72px;\n    margin-top:  calc(var(--spacing-l) - 72px * ",");\n    margin-bottom: calc(var(--spacing-l) - 72px * ",");\n  }\n  @media "," {\n    font-size: 104px;\n    margin-top:  calc(var(--spacing-l) - 104px * ",");\n    margin-bottom: calc(var(--spacing-l) - 104px * ",");\n  }\n"]);return _templateObject=function(){return t},t}function _templateObject1(){let t=(0,i._)(["\n  box-sizing: content-box;\n  color: #ececec;\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: clamp(24px, 1.125rem + 1vw, 32px);\n  font-weight: 400;\n  max-width: 600px;\n  line-height: 105%;\n  &:before {\n    display: block;\n    margin-bottom: 8px;\n    content: '","';\n    color: ",";\n    font-size: 18px;\n  }\n  @media "," {\n    font-size: 32px;\n    margin-right: 24px;\n  }\n  @media "," {\n    font-weight: 400;\n    margin-right: 48px;\n    font-size: clamp(32px, 3.5vw, 48px);\n  }\n"]);return _templateObject1=function(){return t},t}function _templateObject2(){let t=(0,i._)(['\n  \n  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);\n  \n  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;\n  font-size: var(--title-size);\n  color: ',";\n  font-weight: 400;\n  margin-top: 0;\n  margin-bottom: calc( var(--title-size) * -",");\n  \n"]);return _templateObject2=function(){return t},t}function _templateObject3(){let t=(0,i._)(["\n  font-size: 24px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 48px;\n    margin-bottom: 16px;\n    margin-top: 16px;\n  }\n"]);return _templateObject3=function(){return t},t}function _templateObject4(){let t=(0,i._)(["\n  font-size: 20px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media "," {\n    font-size: 30px;\n  }\n  span {\n    position: relative;\n    margin-left: 8px;\n    display: inline-block;\n    margin-bottom: -2px;\n    height: 22px;\n    width: 22px;\n  }\n"]);return _templateObject4=function(){return t},t}function _templateObject5(){let t=(0,i._)(["\n  font-size: 16px;\n  color: #888888;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding-right: 30px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media screen and (max-width:320px){ // Little screen only\n    padding: 0;\n  }\n  @media "," {\n    font-size:20px;\n  }\n"]);return _templateObject5=function(){return t},t}function _templateObject6(){let t=(0,i._)(['\n  font-size: 14px;\n  font-style: italic;\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  color: #7a7a7a;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media '," {\n    font-size: 17px;\n  }\n"]);return _templateObject6=function(){return t},t}function _templateObject7(){let t=(0,i._)(["\n  font-size: 38px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 64px;\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n  @media "," {\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n"]);return _templateObject7=function(){return t},t}function _templateObject8(){let t=(0,i._)(["\n  color: #161616;\n  letter-spacing: 0.04em;\n  margin-top: 48px;\n  text-transform: uppercase;\n  @media "," {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 22px;\n    font-weight: 500;\n    &:hover {\n      color: var(--brand-color);\n    }\n  }\n"]);return _templateObject8=function(){return t},t}function _templateObject9(){let t=(0,i._)(["\n  font-weight: 400;\n  font-size: 26px;\n  margin: 0;\n  color: #161616;\n  @media "," {\n    font-size: 32px;\n  }\n"]);return _templateObject9=function(){return t},t}let o={topSpace:.096,minBottomSpace:.2788,maxBottomSpace:.4711},l=a.ZP.h1.withConfig({componentId:"sc-a3af5335-0"})(_templateObject(),o.topSpace,o.minBottomSpace,r.U.tablet,o.topSpace,o.minBottomSpace,r.U.desktop,o.topSpace,o.minBottomSpace),s=a.ZP.h2.withConfig({componentId:"sc-a3af5335-1"})(_templateObject1(),t=>t.label,t=>t.color,r.U.tablet,r.U.desktop),c=a.ZP.h2.withConfig({componentId:"sc-a3af5335-2"})(_templateObject2(),t=>t.light?"var(--c-soft-cream)":"var(--soft-dark)",o.maxBottomSpace),p=a.ZP.h2.withConfig({componentId:"sc-a3af5335-3"})(_templateObject3(),r.U.tablet),d=a.ZP.h4.withConfig({componentId:"sc-a3af5335-4"})(_templateObject4(),r.U.tablet),u=a.ZP.p.withConfig({componentId:"sc-a3af5335-5"})(_templateObject5(),r.U.tablet),h=a.ZP.p.withConfig({componentId:"sc-a3af5335-6"})(_templateObject6(),r.U.tablet),m=a.ZP.h1.withConfig({componentId:"sc-a3af5335-7"})(_templateObject7(),r.U.tablet,r.U.desktop),f=a.ZP.p.withConfig({componentId:"sc-a3af5335-8"})(_templateObject8(),r.U.desktop);a.ZP.p.withConfig({componentId:"sc-a3af5335-9"})(_templateObject9(),r.U.tablet)},7481:function(t,n,e){"use strict";function paginate(t,n,e,i){let a,r,o=Math.ceil(t/e);if(n<1?n=1:n>o&&(n=o),o<=i)a=1,r=o;else{let t=Math.floor(i/2),e=Math.ceil(i/2)-1;n<=t?(a=1,r=i):n+e>=o?(a=o-i+1,r=o):(a=n-t,r=n+e)}let l=(n-1)*e,s=Array.from(Array(r+1-a).keys()).map(t=>a+t);return{totalItems:t,currentPage:n,pageSize:e,totalPages:o,startPage:a,endPage:r,startIndex:l,endIndex:Math.min(l+e-1,t-1),pages:s}}e.d(n,{Z:function(){return paginate}})},4355:function(t){t.exports={"soft-white":"ButtonLink_soft-white__ep2DH","glow-white":"ButtonLink_glow-white__46aUW","soft-dark":"ButtonLink_soft-dark__NLNNQ","glow-dark":"ButtonLink_glow-dark__p2EZ7","rounded-glow-white":"ButtonLink_rounded-glow-white__XCQ_D ButtonLink_glow-white__46aUW","rounded-soft-white":"ButtonLink_rounded-soft-white__W4BQi ButtonLink_soft-white__ep2DH","rounded-glow-dark":"ButtonLink_rounded-glow-dark__D1hGV ButtonLink_glow-dark__p2EZ7","rounded-soft-dark":"ButtonLink_rounded-soft-dark__6gKMz ButtonLink_soft-dark__NLNNQ","orange-register":"ButtonLink_orange-register__aIov2"}}},function(t){t.O(0,[755,229,291,915,774,888,179],function(){return t(t.s=851)}),_N_E=t.O()}]);