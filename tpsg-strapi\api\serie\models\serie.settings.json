{"kind": "collectionType", "collectionName": "series", "info": {"name": "serie", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "text"}, "posts": {"via": "serie", "collection": "post"}}}