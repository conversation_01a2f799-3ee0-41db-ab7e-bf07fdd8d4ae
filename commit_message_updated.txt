Fix: Correction des erreurs de formatage, amélioration des styles CSS et résolution des problèmes de recherche et de compilation

Ce commit apporte plusieurs types de corrections:

1. Correction des erreurs de formatage:
   - Remplacement des guillemets simples par des guillemets doubles conformément aux règles ESLint
   - Ajout d'espaces appropriés dans les expressions JSX
   - Correction des problèmes d'indentation dans plusieurs fichiers
   - Échappement correct des caractères spéciaux (apostrophes)

2. Amélioration des styles CSS:
   - Ajout de règles CSS spécifiques dans globals.css pour garantir que tous les liens dans le contenu markdown respectent la charte graphique TPSG
   - Augmentation de la spécificité des sélecteurs CSS pour éviter les conflits de styles
   - Correction des styles des boutons d'inscription pour qu'ils ne soient plus orange par défaut

3. Correction des problèmes React:
   - Ajout des dépendances manquantes dans les hooks useEffect (UseMediaQuery.js)
   - Correction de l'utilisation de useRouter dans _app.js
   - Ajout des polices personnalisées dans _document.js
   - Résolution du problème de disparition des résultats de recherche

4. Amélioration de l'accessibilité:
   - Ajout d'attributs alt aux images
   - Remplacement des attributs obsolètes dans les SVG (xlinkHref par href)

5. Correction des problèmes de recherche et pagination:
   - Correction des composants search-tool.js et search-paginate.js pour la compatibilité avec Meilisearch v0.27.0
   - Mise à jour de utils/paginate.utils.js pour utiliser offset/limit au lieu de page
   - Correction de utils/query.utils.js pour adapter les requêtes au format attendu par Meilisearch
   - Résolution des problèmes d'affichage des résultats de recherche dans pages/recherche.js

6. Correction des problèmes de podcasts:
   - Résolution des erreurs dans pages/podcasts/[podcast]/[episode].js
   - Correction du formatage et de la gestion des données

7. Correction des problèmes de compilation:
   - Correction des problèmes de syntaxe pour respecter les normes ESLint
   - Correction des guillemets et de l'indentation dans utils/posts.utils.js et utils/string.utils.js
   - Amélioration de la gestion des liens externes dans CondLink.js
   - Correction des commentaires pour utiliser le format standard
   - Respect des conventions de code établies pour assurer la compatibilité avec la plateforme d'intégration
