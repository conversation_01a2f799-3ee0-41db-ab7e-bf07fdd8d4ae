"use strict";exports.id=6303,exports.ids=[6303],exports.modules={6073:(e,t,i)=>{i.d(t,{Z:()=>PreviewButton});var o=i(997),a=i(7518),n=i.n(a),r=i(6689);let l="https://toutpoursagloire.com";function PreviewButton({url:e}){let[t,i]=(0,r.useState)(""),copyUrl=()=>{navigator.clipboard.writeText(`${l}${e}`),i("Copi\xe9!"),setTimeout(function(){i("")},3e3)};return(0,o.jsxs)(d,{children:[o.jsx("p",{children:"Preview"}),(0,o.jsxs)(s,{onClick:()=>copyUrl(),children:[(0,o.jsxs)("span",{children:[l,e]}),o.jsx("div",{className:"clip-icon",children:o.jsx("svg",{width:"14",height:"16",viewBox:"0 0 14 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:o.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.14492 0.315878C4.24373 0.122287 4.44572 0 4.66667 0H7C7.22095 0 7.42294 0.122287 7.52175 0.315878L7.94385 1.14286H9.91667C10.8832 1.14286 11.6667 1.91037 11.6667 2.85714V4C11.6667 4.31559 11.4055 4.57143 11.0833 4.57143C10.7612 4.57143 10.5 4.31559 10.5 4V2.85714C10.5 2.54155 10.2388 2.28571 9.91667 2.28571H9.33333V4C9.33333 4.31559 9.07217 4.57143 8.75 4.57143H2.91667C2.5945 4.57143 2.33333 4.31559 2.33333 4V2.28571H1.75C1.42783 2.28571 1.16667 2.54155 1.16667 2.85714V13.1429C1.16667 13.4585 1.42783 13.7143 1.75 13.7143H4.08333C4.4055 13.7143 4.66667 13.9701 4.66667 14.2857C4.66667 14.6013 4.4055 14.8571 4.08333 14.8571H1.75C0.783507 14.8571 0 14.0897 0 13.1429V2.85714C0 1.91037 0.783501 1.14286 1.75 1.14286H3.72281L4.14492 0.315878ZM3.5 2.28571V3.42857H8.16667V2.28571H3.5ZM5.83333 7.42857C5.83333 6.48178 6.61682 5.71429 7.58333 5.71429H12.25C13.2165 5.71429 14 6.48178 14 7.42857V14.2857C14 15.2325 13.2165 16 12.25 16H7.58333C6.61682 16 5.83333 15.2325 5.83333 14.2857V7.42857Z",fill:"#080808"})})})]}),""!==t&&o.jsx("p",{className:"preview-message",children:t})]})}let d=n().div.withConfig({componentId:"sc-9d8da094-0"})`
		font-family: Switzer, sans-serif;
  position: fixed;
  bottom: 0;
  left: 0;
  height: 60px;
  width: 100%;
  background-color: #9fe7d3;
  border: 54px;
  display: flex;
  flex-display: row;
  align-items: center;
		padding: 0 30px;
  background-image: linear-gradient(45deg, #aaebda 25%, #9fe7d3 25%, #9fe7d3 50%, #aaebda 50%, #aaebda 75%, #9fe7d3 75%, #9fe7d3 100%);
  background-size: 33.94px 33.94px;		

  p {
    color: 080808;
    font-size: 18px;
    font-weight: 600;
		  margin: 0;
  }
		
		.preview-message {
				margin-left: 12px;
    background-color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
				font-weight: 400;
				height: 30px;
				line-height: 30px;
				padding: 0 18px;
				border-radius: 30px;
  }

  z-index: 1000;
`,s=n().div.withConfig({componentId:"sc-9d8da094-1"})`

  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 60px;
  padding: 0 18px;
  height: 30px;
  margin-left: 18px;

  span {
    font-size: 12px;
    font-weight: 500;
    color: rgba(8, 8, 8, 0.6);
		  letter-spacing: 0.2px;
  }

  .clip-icon {
    margin-left: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }

  &:hover {
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.7);
  }
`},1458:(e,t,i)=>{i.a(e,async(e,o)=>{try{i.d(t,{Z:()=>ArticleLayout});var a=i(997),n=i(6641),r=i(8672),l=i(2558),d=i(6382),s=i(3135),p=i(1871),c=i(3459),x=i(4631),g=i(8102),h=i(3646),m=i(7518),b=i.n(m),f=i(7467),u=i(4130),w=i(6073),j=e([s,p,x,h]);function ArticleLayout({post:e,preview:t,newsletter:i}){let{modules:o,route:m}=e,b=e.blog?`/blog/${e.blog.blogger.slug}`:`/recherche?author=${e.author.fullName}`;return(0,a.jsxs)(y,{children:[a.jsx(n.NextSeo,{title:o.seo?.metaTitle||e.title,description:o.seo?.metaDescription||null,openGraph:{title:o.seo?.metaTitle||e.title,description:o.seo?.metaDescription||null,url:`https://toutpoursagloire.com${m}`,type:"article",article:{publishedTime:e.published_at,tags:e.topics.map(e=>e.name)},images:[{url:(0,u.k)(e.image),alt:e.image?.alternativeText||""}]},twitter:{site:"@t_p_s_g",cardType:"summary_large_image"}}),t&&a.jsx(w.Z,{url:m}),(0,a.jsxs)(C,{children:[(0,a.jsxs)("div",{className:"article-head-text",children:[a.jsx("h1",{className:"article-title",children:e.title}),e.topics&&e.topics.map((e,t)=>a.jsx(r.Z,{text:e.name},t)),e.readingTime?(0,a.jsxs)("div",{className:"reading-time",children:[e.readingTime," min de lecture"]}):null]}),a.jsx("div",{className:"article-image",children:a.jsx("div",{className:"image-wrapper",children:a.jsx(l.Z,{imageData:e.image})})}),(0,a.jsxs)(z,{children:[a.jsx("a",{href:b,children:a.jsx("div",{className:"article-author-picture",children:a.jsx(l.Z,{imageData:e.author?.picture})})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"article-author-name",children:a.jsx("a",{href:b,children:e.author?.fullName||""})}),a.jsx("p",{className:"article-date",children:(0,d.S$)(e.published_at)})]})]}),a.jsx(Z,{children:a.jsx(s.default,{rehypePlugins:[p.default],children:o.lead?.content||""})}),a.jsx(c.Z,{url:`https://toutpoursagloire.com/article/${e.slug}`}),a.jsx(x.Z,{content:e.body})]}),(0,a.jsxs)(v,{children:[a.jsx(k,{children:a.jsx(g.Z,{formString:i,title:"Ma newsletter",desc:""})}),a.jsx(h.Z,{author:e.author,blog:e.blog})]})]})}[s,p,x,h]=j.then?(await j)():j;let y=b().div.withConfig({componentId:"sc-a9966f91-0"})`
  width: 100vw;
  padding: 0 var(--border-space);

  .preview-warning {
    position: fixed;
    bottom: 16px;
    left: 16px;
    margin: 0;
    display: inline-block;
    font-family: Switzer, sans-serif;
    font-size: 18px;
    padding: 10px 40px;
    border-radius: 100px;
    color: var(--soft-white);
    background-color: var(--brand-color);
    z-index: 9999;
    opacity: 0.86;
  }
`,v=b().div.withConfig({componentId:"sc-a9966f91-1"})`
  display: flex;
  flex-direction: column-reverse;
  margin-bottom: 128px;
  margin-top: 128px;

  .author-box {
    margin-bottom: 40px;
  }

  @media ${f.U.desktop} {
    flex-direction: row;
    margin-bottom: 126px;
    .author-box {
      padding-left: 40px;
    }
  }
`,k=b().div.withConfig({componentId:"sc-a9966f91-2"})`
  border-top: 1px solid rgba(0, 0, 0, 0.4);
  padding-right: 40px;
  padding-top: 32px;
  @media ${f.U.desktop} {
    min-width: 40%;
    border-right: 1px solid rgba(0, 0, 0, 0.4);
  }
`,C=b().div.withConfig({componentId:"sc-a9966f91-3"})`
  .card-label {
    display: none;
    margin-right: 8px;
  }

  .article-title {
    font-size: 38px;
    font-weight: 500;
    line-height: 110%;
    margin-top: 40px;
    margin-bottom: 14px;
  }

  .article-image {
    position: relative;
    width: 100%;
    height: 260px;
  }

  .reading-time {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 14px;
  }

  .social-buttons {
    display: none;
  }

  @media ${f.U.tablet} {
    display: grid;
    margin: 0 0 80px 0;
    grid-template-columns: repeat(8, 1fr);

    .article-head-text {
      grid-column: 1/5;
      align-self: center;
      grid-row: 1;
      padding: 40px 0 40px 0;
    }

    .card-label {
      display: inline-block;
    }

    .article-title {
      margin: 20px 0 10px 0;
      font-size: 56px;
      line-height: 110%;
    }

    .article-image {
      position: relative;
      grid-column: 6/9;
      width: calc(100%);
      height: unset;
      margin-top: 0;

      &:after {
        content: "";
        display: block;
        padding-bottom: 100%;
      }

      .image-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
      }
    }

    .reading-time {
      font-size: 16px;
      font-weight: 400;
      margin-top: 10px;
    }

    .md-post {
      grid-column: 2 / 7;
    }

    .social-buttons {
      display: block;
      position: sticky;
      top: 40px;
      margin-top: 80px;
    }

    .abox-facename {
      margin-top: 80px;
      grid-column: 2/4;
    }

    .abox-about {
      margin-top: 80px;
      grid-column: 4/7;
    }
  }
`,z=b().div.withConfig({componentId:"sc-a9966f91-4"})`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 40px;

  .article-author-picture {
    position: relative;
    visibility: visible;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    overflow: hidden;
    margin-right: 18px;
  }

  .article-author-name {
    font-size: 20px;
    margin-top: 8px;
    font-weight: 600;
    margin-bottom: 0;
  }

  .article-date {
    font-size: 16px;
    margin-top: 4px;
    margin-bottom: 8px;
    line-height: 100%;
  }

  .article-rt {
    position: relative;
    font-size: 16px;
    margin-bottom: 8px;
    line-height: 100%;

    span {
      font-size: 20px;
      margin-right: 2px;
    }
  }

  @media ${f.U.tablet} {
    display: flex;
    margin: 0;
    grid-column: 1/3;
    grid-row: 2;
    align-self: start;

    border-top: 1px solid #dcdcdc;
    border-bottom: none;
    padding-top: 80px;
    line-height: 120%;

    .article-author-name {
      margin: 12px 0 0 0;
      font-size: 22px;
      font-weight: 600;
      color: #323232;
      letter-spacing: 0.02em;
    }

    .article-date {
      margin-top: 8px;
      font-size: 20px;
      font-weight: 400;
      color: #323232;
      letter-spacing: 0.02em;
    }

    .article-rt {
      position: relative;
      font-size: 18px;

      span {
        display: inline-block;
        width: 28px;
        text-align: center;
        margin-right: 4px;
        color: white;
      }

      &:before {
        content: "";
        display: inline-block;
        position: absolute;
        z-index: -1;
        left: 0;
        top: -7px;
        background-color: #161616;
        height: 28px;
        width: 28px;
        border-radius: 22px;
      }
    }
  }
`,Z=b().div.withConfig({componentId:"sc-a9966f91-5"})`
  font-weight: 400;
  font-size: 26px;
  padding: 40px 0 24px 0;

  p {
    color: #161616;
    margin: 0;
  }

  @media ${f.U.tablet} {
    border-top: 1px solid #dcdcdc;
    border-left: 1px solid #dcdcdc;
    grid-column: 3/9;
    grid-row: 2;
    font-size: 32px;
    padding: 80px 0 40px 80px;
  }
`;o()}catch(e){o(e)}})},362:(e,t,i)=>{i.a(e,async(e,o)=>{try{i.d(t,{Z:()=>PodcastLayout});var a=i(997),n=i(6641),r=i(4130),l=i(1664),d=i.n(l),s=i(6453),p=i(8672),c=i(8657),x=i(4214),g=i(2350),h=i(3462),m=i(7518),b=i.n(m),f=i(7467),u=i(6382),w=i(3123),j=e([c]);function PodcastLayout({episode:e,relatedPosts:t}){if(!e)return a.jsx(a.Fragment,{});let i=(0,u.S$)(e.published_at),{route:o}=e,{lead:l,podcast:m,seo:b}=e.modules,f=m?.podcast?.platforms,j={fullName:m?.podcast.name||"unknown",picture:m?.podcast.logoSmall,url:`/podcasts/${m?.podcast.slug}`};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(y,{className:"site-padding",children:[a.jsx(n.NextSeo,{title:b?.metaTitle||e.title,description:b?.metaDescription||null,openGraph:{title:b?.metaTitle||e.title,description:b?.metaDescription||null,url:`https://toutpoursagloire.com${o}`,images:[{url:(0,r.k)(e.image),alt:e.image?.alternativeText||""}]},twitter:{site:"@t_p_s_g",cardType:"summary_large_image"}}),(0,a.jsxs)("header",{children:[(0,a.jsxs)("div",{className:"header-text-container",children:[a.jsx(d(),{href:`/podcasts/${m.podcast.slug}`,children:a.jsx(s.hQ,{children:m.podcast.name})}),a.jsx(s.DZ,{children:e.title}),a.jsx("div",{children:e.topics&&e.topics.map((e,t)=>a.jsx(p.Z,{text:e.name},t))})]}),a.jsx("div",{className:"header-player-cover",children:a.jsx(c.xr,{children:a.jsx(x.Z,{video:m.embedVideo,image:e.image})})})]}),(0,a.jsxs)(g.Z,{children:[a.jsx(g.Z.Text,{label:"Publi\xe9 le",content:i,addClass:""}),a.jsx(g.Z.Authors,{label:"Podcast",authors:[j],addClass:""}),a.jsx(g.Z.Social,{url:`https://toutpoursagloire.com/podcasts/${m?.podcast.slug}/${e.slug}`,addClass:"mobile-hide_flex"})]}),(0,a.jsxs)(k,{children:[(0,a.jsxs)(C,{children:[l?.content&&a.jsx("section",{children:a.jsx(c.oJ,{content:l.content})}),a.jsx("section",{children:a.jsx(c.oJ,{content:e.body})})]}),a.jsx(v,{children:(0,a.jsxs)("div",{className:"right-content-sticky",children:[m?.embedAudio&&a.jsx(c.oJ,{content:`<p>${m.embedAudio}</p>`}),a.jsx("div",{className:"podcast-platform",children:f&&f.map((e,t)=>a.jsx("a",{href:e.url,target:"_blank",rel:"noreferrer",children:a.jsx(h.Z,{plateform:e.name})},t))})]})})]})]}),a.jsx(w.Z,{items:t})]})}c=(j.then?(await j)():j)[0];let y=b().div.withConfig({componentId:"sc-4ca6bb54-0"})`
  padding-bottom: 70px;

  header {
    display: flex;
    flex-direction: column;
    margin-bottom: 48px;
  }

  .header-player-cover {
    width: 100%;
    aspect-ratio: 16 / 10;
  }

  .card-label {
    display: none;
    margin: 8px 8px 0 0;
  }

  .video-player {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }

  .header-img-container {
    position: relative;
    width: 50%;
    aspect-ratio: 16 / 10;
  }

  @media ${f.U.tablet} {
    .card-label {
      display: inline-block;
    }

    .header-player-cover {
      margin-top: 48px;
    }
  }
  @media ${f.U.desktop} {
    header {
      margin-top: 90px;
      margin-bottom: 70px;
      flex-direction: row;
      justify-content: space-between;
    }

    .header-text-container {
      position: relative;
      width: 50%;
      aspect-ratio: 16 / 10;
      padding-top: 24px;
      padding-left: 16px;
    }

    .header-player-cover {
      margin-top: 0;
      width: calc(50% - 32px);
    }
  }
`,v=b().div.withConfig({componentId:"sc-4ca6bb54-1"})`
  position: relative;
  width: 100%;

  .right-content-sticky {
    position: sticky;
    top: 50px;
  }

  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }

  @media ${f.U.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${f.U.desktop} {
    border-left: 1px solid #dddddd;
    width: 40%;
    padding-left: 32px;
  }
`,k=b().main.withConfig({componentId:"sc-4ca6bb54-2"})`
  margin-top: 70px;
  display: block;

  @media ${f.U.desktop} {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
`,C=b().article.withConfig({componentId:"sc-4ca6bb54-3"})`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media ${f.U.desktop} {
    width: 50%;
  }
`;o()}catch(e){o(e)}})},7069:(e,t,i)=>{i.a(e,async(e,o)=>{try{i.d(t,{Z:()=>WebinarLayout});var a=i(997),n=i(6382),r=i(6641),l=i(1077),d=i(4130);i(4004);var s=i(6453),p=i(8672),c=i(4214),x=i(2350),g=i(8657),h=i(9553),m=i(3123),b=i(7518),f=i.n(b),u=i(7467),w=i(6073),j=i(1664),y=i.n(j),v=e([g]);function WebinarLayout({episode:e,preview:t,relatedPosts:i}){if(!e)return null;let o=e.route,{lead:b,webinar:f,event:u,seo:j}=e.modules,v=(0,n.S$)(e.published_at),C=u&&new Date(u.date)>new Date;return(0,a.jsxs)(a.Fragment,{children:[a.jsx(r.NextSeo,{title:j?.metaTitle||e.title,description:j?.metaDescription||null,openGraph:{title:j?.metaTitle||e.title,description:j?.metaDescription||null,url:`https://toutpoursagloire.com${(0,l.qt)(e)}`,images:[{url:(0,d.k)(e.image),alt:e.image?.alternativeText||""}]},twitter:{site:"@t_p_s_g",cardType:"summary_large_image"}}),t&&a.jsx(w.Z,{url:o}),(0,a.jsxs)(k,{className:"site-padding",children:[(0,a.jsxs)("header",{children:[(0,a.jsxs)("div",{className:"header-text-container",children:[a.jsx(s.hQ,{children:a.jsx(y(),{href:"/webinaires",children:"WEBINAIRE"})}),a.jsx(s.DZ,{children:e.title}),a.jsx("div",{children:e.topics&&e.topics.map((e,t)=>a.jsx(p.Z,{text:e.name},t))})]}),a.jsx("div",{className:"header-player-cover",children:a.jsx(c.Z,{video:!C&&f.embedVideo||null,image:e.image})})]}),(0,a.jsxs)(x.Z,{children:[!C&&a.jsx(x.Z.Text,{label:"Date",content:v,addClass:C?"mobile-hide_flex":""}),a.jsx(x.Z.Authors,{label:"Orateur(s)",authors:f.speakers||!1,addClass:C?"mobile-hide_flex":""}),a.jsx(x.Z.Social,{url:`https://toutpoursagloire.com/webinaires/${e.slug}`,addClass:"mobile-hide_flex"})]}),(0,a.jsxs)("article",{children:[b?.content&&a.jsx("section",{children:a.jsx(g.oJ,{content:b.content})}),a.jsx("section",{children:a.jsx(g.oJ,{content:e.body})})]}),C&&a.jsx(h.Z,{children:(0,a.jsxs)(x.Z,{children:[a.jsx(x.Z.Text,{label:"Date",content:(0,n.S$)(u.date)}),a.jsx(x.Z.Text,{label:"D\xe9but",content:(0,n.xO)(u.date)}),a.jsx(x.Z.LinkButton,{url:u.url,text:"JE M'INSCRIS"})]})})]}),a.jsx(m.Z,{items:i})]})}g=(v.then?(await v)():v)[0];let k=f().div.withConfig({componentId:"sc-f925c478-0"})`
  padding-bottom: 70px;

  header {
    display: flex;
    flex-direction: column;
    margin-bottom: 48px;
  }

  .header-player-cover {
    width: 100%;
    aspect-ratio: 16 / 10;
  }

  .card-label {
    display: none;
    margin: 8px 8px 0 0;
  }

  article {
    margin-top: 70px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .youtube-player-component {
    /* styles for webinaires */
  }

  @media ${u.U.tablet} {
    .card-label {
      display: inline-block;
    }

    .header-player-cover {
      margin-top: 48px;
    }
  }
  @media ${u.U.desktop} {
    header {
      margin-top: 90px;
      margin-bottom: 70px;
      flex-direction: row;
      justify-content: space-between;
    }

    .header-text-container {
      position: relative;
      width: 50%;
      aspect-ratio: 16 / 10;
      padding-top: 24px;
      padding-left: 16px;
    }

    .header-player-cover {
      margin-top: 0;
      width: calc(50% - 32px);
    }
  }
`;o()}catch(e){o(e)}})},6303:(e,t,i)=>{i.a(e,async(e,o)=>{try{i.d(t,{Bv:()=>n.Z,KB:()=>r.Z,Q4:()=>a.Z});var a=i(362),n=i(1458),r=i(7069),l=e([a,n,r]);[a,n,r]=l.then?(await l)():l,o()}catch(e){o(e)}})},4214:(e,t,i)=>{i.d(t,{Z:()=>VideoPlayer});var o=i(997),a=i(9280),n=i(7518),r=i.n(n),l=i(6689),d=i(2558),s=i(7199);let YoutubeIframe=({video:e})=>{let t=(0,s.gc)(e);return o.jsx("iframe",{src:"https://www.youtube.com/embed/"+t+"?&autoplay=1&mute=1&enablejsapi=1",width:"100%",height:"100%",frameBorder:"0",allow:"fullscreen"})};function VideoPlayer({video:e,image:t}){let[i,n]=(0,l.useState)(!1),r=e&&e.length>0;return(0,o.jsxs)(p,{className:"youtube-player-component",hidePlayer:!r,children:[o.jsx(d.Z,{imageData:t}),r&&(0,o.jsxs)(o.Fragment,{children:[o.jsx(c,{children:"REPLAY"}),o.jsx(a.P8,{clickAction:()=>n(!0)}),i&&o.jsx(YoutubeIframe,{video:e})]})]})}let p=r().div.withConfig({componentId:"sc-aab69a3a-0"})`
  position: relative;

  box-shadow: ${e=>e.hidePlayer?"none":"0 16px 48px rgba(60, 60, 60, 0.3);"};
  
  width: 100%;
  height: 100%;
  
  iframe {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }
`,c=r().p.withConfig({componentId:"sc-aab69a3a-1"})`
  position: absolute;
  color: #f6f6f6;
  margin-top: 0;
  top: 22px;
  left: 22px;
  font-size: 22px;
  font-weight: 500;
`},4631:(e,t,i)=>{i.a(e,async(e,o)=>{try{i.d(t,{Z:()=>MDPost});var a=i(997),n=i(7518),r=i.n(n),l=i(3135),d=i(1871),s=i(6809),p=i(7467),c=i(7199),x=i(9526),g=i.n(x),h=e([l,d,s]);[l,d,s]=h.then?(await h)():h;let codeComponent=({children:e})=>a.jsx(g(),{html:e}),tableComponent=({children:e})=>a.jsx("div",{className:"table-container",children:a.jsx("table",{children:e})});function MDPost({content:e}){return a.jsx(m,{className:"md-post",children:a.jsx(l.default,{components:{code:codeComponent,table:tableComponent},rehypePlugins:[d.default,s.default],children:(0,c.k5)(e)})})}let m=r().div.withConfig({componentId:"sc-20e5a393-0"})`
  font-family: "Lora", Charter, Times, "Times New Roman", serif;
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;

  img {
    max-width: 100%;
  }

  h5, h6 {
    font-size: 18px;
    line-height: 28px;
    font-weight: 500;
    margin-bottom: 0;
  }

  h1,
  h2,
  h3,
  h4 {
    font-family: Stelvio, sans-serif;
    font-weight: 600;
    margin-bottom: 0;
  }

  h1,
  h2 {
    font-size: 28px;
    line-height: 32px;
    margin-top: 32px;

    &:first-child {
      margin-top: 0;
    }
  }

  h3 {
    font-size: 24px;
    line-height: 28px;
    margin-top: 12px;
  }

  p {
    font-size: 18px;
    line-height: 28px;
    margin-top: 0;
    margin-bottom: 24px;
    color: #161616;

    sup {
      vertical-align: top;
      position: relative;
      top: -0.4em;
      margin-left: 4px;
      margin-right: 4px;
    }
  }

  blockquote {
    position: relative;
    margin: 0;
    padding: 8px 0 8px 36px;

    &::before {
      content: "“";
      display: inline-block;
      position: absolute;
      font-size: 54px;
      font-weight: bold;
      color: #363636;
      left: 0;
      top: 18px;
      width: 100px;
    }
  }

  blockquote p {
    font-size: 18px;
    line-height: 28px;
    font-style: italic;
  }

  em {
  }

  ul, ol {
    margin-top: 12px;
    padding-left: 24px;
  }

  li {
    line-height: 170%;
    margin: 0 0 8px 0;
  }

  a {
    color: #161616;
    text-decoration: underline;
  }

  a:hover {
    color: var(--brand-color);
  }

  hr {
    border: none;
    margin: 40px 0 40px 0;

    &::before {
      content: "***";
      display: block;
      letter-spacing: 10px;
      text-align: center;
      color: #161616;
    }
  }

  cite {
    &:before {
      content: "– ";
      font-size: 600;
    }
  }

  @media ${p.U.tablet} {
    max-width: 720px;
    margin: 80px 0 0 0;
    h1, h2 {
      font-size: 32px;
      line-height: 36px;
      margin-top: 44px;
      margin-bottom: 6px;
    }

    h3, h4 {
      margin-top: 32px;
      margin-bottom: 0;
      font-size: 26px;
      line-height: 30px;
    }

    h4 {
      opacity: 0.6;
      font-weight: 500;
    }

    p {
      color: #323232;
      font-size: 20px;
      line-height: 32px;
      font-weight: 400;
      margin-bottom: 24px;
    }

    ul, ol {
      margin-top: 12px;
      padding-left: 24px;
      margin-bottom: 24px;
      font-size: 20px;
      line-height: 32px;
    }

    blockquote p {
      font-size: 20px;
      line-height: 32px;
      font-weight: 500;
      font-style: italic;
      margin-bottom: 0;
    }

    blockquote {
      margin-bottom: 24px;
    }

    .post-content-button {
      display: inline-block;
      margin-top: 16px;
      margin-bottom: 16px;
      padding: 10px 32px;
      border-radius: 40px;
      text-align: center;
      color: white;
      text-decoration: none;
      font-family: Switzer, sans-serif;
      font-weight: 400;
      background-color: var(--brand-color);
    }
  }

  .table-container {
    position: relative;
    overflow-x: auto;
    margin-bottom: 24px;
    width: 100%;
    background-image: linear-gradient(to right, white, white), linear-gradient(to right, white, white), linear-gradient(to right, rgba(0, 0, 20, .50), rgba(255, 255, 255, 0)), linear-gradient(to left, rgba(0, 0, 20, .50), rgba(255, 255, 255, 0));
    /* Shadows */
    /* Shadow covers */
    background-position: left center, right center, left center, right center;
    background-repeat: no-repeat;
    background-color: white;
    background-size: 20px 100%, 20px 100%, 10px 100%, 16px 100%;
    background-attachment: local, local, scroll, scroll;
  }

  table {
    font-family: Switzer, sans-serif;
    border: 1px solid #ccc;
    border-collapse: collapse;
    padding: 0;
    width: 100%;
    overflow-x: auto;
  }

  table caption {
    font-size: 1.5em;
    margin: .5em 0 .75em;
  }

  table tr {
    background-color: rgba(248, 248, 248, 0.9);
    border: 1px solid #ddd;
    padding: .35em;
  }

  table th,
  table td {
    font-size: 0.85em;
    line-height: 1.4em;
    padding: .625em;
    text-align: left;
    min-width: 240px;
    color: rgba(0, 0, 0, 0.72);
  }

  table th {
    font-weight: 600;
    color: rgba(0, 0, 0, 9);
  }

  table th {
    font-size: 0.85em;
    //font-weight: 500;
    //text-transform: uppercase;
  }

  @media screen and (max-width: 600px) {
    
    .table-container {
      background: none;
    }
    table {
      border: 0;
    }

    table caption {
      font-size: 1.3em;
    }

    table thead {
      border: none;
      clip: rect(0 0 0 0);
      height: 1px;
      margin: -1px;
      overflow: hidden;
      padding: 0;
      position: absolute;
      width: 1px;
    }

    table tr {
      border-bottom: 3px solid #ddd;
      display: block;
      margin-bottom: .625em;
    }

    table td {
      border-bottom: 1px solid #ddd;
      display: block;
      font-size: .8em;
      text-align: left;
    }

    table td::before {
      /*
      * aria-label has no advantage, it won't be read inside a table
      content: attr(aria-label);
      */
      content: attr(data-label);
      float: left;
      font-weight: bold;
      text-transform: uppercase;
    }

    table td:last-child {
      border-bottom: 0;
    }
  }
`;o()}catch(e){o(e)}})},9553:(e,t,i)=>{i.d(t,{Z:()=>RegisterBar});var o=i(997),a=i(7518),n=i.n(a);function RegisterBar({children:e}){return o.jsx(r,{children:o.jsx("div",{className:"register-content site-padding",children:e})})}i(7467);let r=n().div.withConfig({componentId:"sc-e7f5198c-0"})`
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  border-top: 1px #ffffff solid;
  background-color: rgba(241, 241, 241, 0.9);

  @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
    background-color: rgba(221, 221, 221, 0.45);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
  }

  z-index: 1000;

  .register-content {
    position: relative;
  }

  /* Ensure the SubHeader inside RegisterBar has proper alignment */
  .subheader {
    border-top: none;
    border-bottom: none;
    padding-top: 12px;
    padding-bottom: 12px;
  }
`}};