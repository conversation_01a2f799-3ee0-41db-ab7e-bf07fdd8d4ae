(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[888],{6489:function(n,r){"use strict";/*!
 * cookie
 * Copyright(c) 2012-2014 <PERSON>
 * Copyright(c) 2015 <PERSON>
 * MIT Licensed
 */r.Q=function(n,r){if("string"!=typeof n)throw TypeError("argument str must be a string");for(var o={},a=n.split(";"),u=(r||{}).decode||i,p=0;p<a.length;p++){var d=a[p],x=d.indexOf("=");if(!(x<0)){var w=d.substring(0,x).trim();if(void 0==o[w]){var k=d.substring(x+1,d.length).trim();'"'===k[0]&&(k=k.slice(1,-1)),o[w]=function(n,r){try{return r(n)}catch(r){return n}}(k,u)}}}return o},r.q=function(n,r,i){var u=i||{},p=u.encode||o;if("function"!=typeof p)throw TypeError("option encode is invalid");if(!a.test(n))throw TypeError("argument name is invalid");var d=p(r);if(d&&!a.test(d))throw TypeError("argument val is invalid");var x=n+"="+d;if(null!=u.maxAge){var w=u.maxAge-0;if(isNaN(w)||!isFinite(w))throw TypeError("option maxAge is invalid");x+="; Max-Age="+Math.floor(w)}if(u.domain){if(!a.test(u.domain))throw TypeError("option domain is invalid");x+="; Domain="+u.domain}if(u.path){if(!a.test(u.path))throw TypeError("option path is invalid");x+="; Path="+u.path}if(u.expires){if("function"!=typeof u.expires.toUTCString)throw TypeError("option expires is invalid");x+="; Expires="+u.expires.toUTCString()}if(u.httpOnly&&(x+="; HttpOnly"),u.secure&&(x+="; Secure"),u.sameSite)switch("string"==typeof u.sameSite?u.sameSite.toLowerCase():u.sameSite){case!0:case"strict":x+="; SameSite=Strict";break;case"lax":x+="; SameSite=Lax";break;case"none":x+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return x};var i=decodeURIComponent,o=encodeURIComponent,a=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/},7484:function(n){var r,i,o,a,u,p,d,x,w,k,E,C,D,I,R,V,K,W,G,J,et;n.exports=(r="millisecond",i="second",o="minute",a="hour",u="week",p="month",d="quarter",x="year",w="date",k="Invalid Date",E=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,C=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,D=function(n,r,i){var o=String(n);return!o||o.length>=r?n:""+Array(r+1-o.length).join(i)+n},(R={})[I="en"]={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(n){var r=["th","st","nd","rd"],i=n%100;return"["+n+(r[(i-20)%10]||r[i]||"th")+"]"}},V=function(n){return n instanceof J},K=function t(n,r,i){var o;if(!n)return I;if("string"==typeof n){var a=n.toLowerCase();R[a]&&(o=a),r&&(R[a]=r,o=a);var u=n.split("-");if(!o&&u.length>1)return t(u[0])}else{var p=n.name;R[p]=n,o=p}return!i&&o&&(I=o),o||!i&&I},W=function(n,r){if(V(n))return n.clone();var i="object"==typeof r?r:{};return i.date=n,i.args=arguments,new J(i)},(G={s:D,z:function(n){var r=-n.utcOffset(),i=Math.abs(r);return(r<=0?"+":"-")+D(Math.floor(i/60),2,"0")+":"+D(i%60,2,"0")},m:function t(n,r){if(n.date()<r.date())return-t(r,n);var i=12*(r.year()-n.year())+(r.month()-n.month()),o=n.clone().add(i,p),a=r-o<0,u=n.clone().add(i+(a?-1:1),p);return+(-(i+(r-o)/(a?o-u:u-o))||0)},a:function(n){return n<0?Math.ceil(n)||0:Math.floor(n)},p:function(n){return({M:p,y:x,w:u,d:"day",D:w,h:a,m:o,s:i,ms:r,Q:d})[n]||String(n||"").toLowerCase().replace(/s$/,"")},u:function(n){return void 0===n}}).l=K,G.i=V,G.w=function(n,r){return W(n,{locale:r.$L,utc:r.$u,x:r.$x,$offset:r.$offset})},et=(J=function(){function M(n){this.$L=K(n.locale,null,!0),this.parse(n)}var n=M.prototype;return n.parse=function(n){this.$d=function(n){var r=n.date,i=n.utc;if(null===r)return new Date(NaN);if(G.u(r))return new Date;if(r instanceof Date)return new Date(r);if("string"==typeof r&&!/Z$/i.test(r)){var o=r.match(E);if(o){var a=o[2]-1||0,u=(o[7]||"0").substring(0,3);return i?new Date(Date.UTC(o[1],a,o[3]||1,o[4]||0,o[5]||0,o[6]||0,u)):new Date(o[1],a,o[3]||1,o[4]||0,o[5]||0,o[6]||0,u)}}return new Date(r)}(n),this.$x=n.x||{},this.init()},n.init=function(){var n=this.$d;this.$y=n.getFullYear(),this.$M=n.getMonth(),this.$D=n.getDate(),this.$W=n.getDay(),this.$H=n.getHours(),this.$m=n.getMinutes(),this.$s=n.getSeconds(),this.$ms=n.getMilliseconds()},n.$utils=function(){return G},n.isValid=function(){return this.$d.toString()!==k},n.isSame=function(n,r){var i=W(n);return this.startOf(r)<=i&&i<=this.endOf(r)},n.isAfter=function(n,r){return W(n)<this.startOf(r)},n.isBefore=function(n,r){return this.endOf(r)<W(n)},n.$g=function(n,r,i){return G.u(n)?this[r]:this.set(i,n)},n.unix=function(){return Math.floor(this.valueOf()/1e3)},n.valueOf=function(){return this.$d.getTime()},n.startOf=function(n,r){var d=this,k=!!G.u(r)||r,E=G.p(n),l=function(n,r){var i=G.w(d.$u?Date.UTC(d.$y,r,n):new Date(d.$y,r,n),d);return k?i:i.endOf("day")},$=function(n,r){return G.w(d.toDate()[n].apply(d.toDate("s"),(k?[0,0,0,0]:[23,59,59,999]).slice(r)),d)},C=this.$W,D=this.$M,I=this.$D,R="set"+(this.$u?"UTC":"");switch(E){case x:return k?l(1,0):l(31,11);case p:return k?l(1,D):l(0,D+1);case u:var V=this.$locale().weekStart||0,K=(C<V?C+7:C)-V;return l(k?I-K:I+(6-K),D);case"day":case w:return $(R+"Hours",0);case a:return $(R+"Minutes",1);case o:return $(R+"Seconds",2);case i:return $(R+"Milliseconds",3);default:return this.clone()}},n.endOf=function(n){return this.startOf(n,!1)},n.$set=function(n,u){var d,k=G.p(n),E="set"+(this.$u?"UTC":""),C=((d={}).day=E+"Date",d[w]=E+"Date",d[p]=E+"Month",d[x]=E+"FullYear",d[a]=E+"Hours",d[o]=E+"Minutes",d[i]=E+"Seconds",d[r]=E+"Milliseconds",d)[k],D="day"===k?this.$D+(u-this.$W):u;if(k===p||k===x){var I=this.clone().set(w,1);I.$d[C](D),I.init(),this.$d=I.set(w,Math.min(this.$D,I.daysInMonth())).$d}else C&&this.$d[C](D);return this.init(),this},n.set=function(n,r){return this.clone().$set(n,r)},n.get=function(n){return this[G.p(n)]()},n.add=function(n,r){var d,w=this;n=Number(n);var k=G.p(r),y=function(r){var i=W(w);return G.w(i.date(i.date()+Math.round(r*n)),w)};if(k===p)return this.set(p,this.$M+n);if(k===x)return this.set(x,this.$y+n);if("day"===k)return y(1);if(k===u)return y(7);var E=((d={})[o]=6e4,d[a]=36e5,d[i]=1e3,d)[k]||1,C=this.$d.getTime()+n*E;return G.w(C,this)},n.subtract=function(n,r){return this.add(-1*n,r)},n.format=function(n){var r=this,i=this.$locale();if(!this.isValid())return i.invalidDate||k;var o=n||"YYYY-MM-DDTHH:mm:ssZ",a=G.z(this),u=this.$H,p=this.$m,d=this.$M,x=i.weekdays,w=i.months,h=function(n,i,a,u){return n&&(n[i]||n(r,o))||a[i].slice(0,u)},c=function(n){return G.s(u%12||12,n,"0")},E=i.meridiem||function(n,r,i){var o=n<12?"AM":"PM";return i?o.toLowerCase():o},D={YY:String(this.$y).slice(-2),YYYY:this.$y,M:d+1,MM:G.s(d+1,2,"0"),MMM:h(i.monthsShort,d,w,3),MMMM:h(w,d),D:this.$D,DD:G.s(this.$D,2,"0"),d:String(this.$W),dd:h(i.weekdaysMin,this.$W,x,2),ddd:h(i.weekdaysShort,this.$W,x,3),dddd:x[this.$W],H:String(u),HH:G.s(u,2,"0"),h:c(1),hh:c(2),a:E(u,p,!0),A:E(u,p,!1),m:String(p),mm:G.s(p,2,"0"),s:String(this.$s),ss:G.s(this.$s,2,"0"),SSS:G.s(this.$ms,3,"0"),Z:a};return o.replace(C,function(n,r){return r||D[n]||a.replace(":","")})},n.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},n.diff=function(n,r,w){var k,E=G.p(r),C=W(n),D=(C.utcOffset()-this.utcOffset())*6e4,I=this-C,R=G.m(this,C);return R=((k={})[x]=R/12,k[p]=R,k[d]=R/3,k[u]=(I-D)/6048e5,k.day=(I-D)/864e5,k[a]=I/36e5,k[o]=I/6e4,k[i]=I/1e3,k)[E]||I,w?R:G.a(R)},n.daysInMonth=function(){return this.endOf(p).$D},n.$locale=function(){return R[this.$L]},n.locale=function(n,r){if(!n)return this.$L;var i=this.clone(),o=K(n,r,!0);return o&&(i.$L=o),i},n.clone=function(){return G.w(this.$d,this)},n.toDate=function(){return new Date(this.valueOf())},n.toJSON=function(){return this.isValid()?this.toISOString():null},n.toISOString=function(){return this.$d.toISOString()},n.toString=function(){return this.$d.toUTCString()},M}()).prototype,W.prototype=et,[["$ms",r],["$s",i],["$m",o],["$H",a],["$W","day"],["$M",p],["$y",x],["$D",w]].forEach(function(n){et[n[1]]=function(r){return this.$g(r,n[0],n[1])}}),W.extend=function(n,r){return n.$i||(n(r,J,W),n.$i=!0),W},W.locale=K,W.isDayjs=V,W.unix=function(n){return W(1e3*n)},W.en=R[I],W.Ls=R,W.p={},W)},6023:function(n,r,i){var o,a;n.exports=(o=i(7484),a={name:"fr",weekdays:"dimanche_lundi_mardi_mercredi_jeudi_vendredi_samedi".split("_"),weekdaysShort:"dim._lun._mar._mer._jeu._ven._sam.".split("_"),weekdaysMin:"di_lu_ma_me_je_ve_sa".split("_"),months:"janvier_f\xe9vrier_mars_avril_mai_juin_juillet_ao\xfbt_septembre_octobre_novembre_d\xe9cembre".split("_"),monthsShort:"janv._f\xe9vr._mars_avr._mai_juin_juil._ao\xfbt_sept._oct._nov._d\xe9c.".split("_"),weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY HH:mm",LLLL:"dddd D MMMM YYYY HH:mm"},relativeTime:{future:"dans %s",past:"il y a %s",s:"quelques secondes",m:"une minute",mm:"%d minutes",h:"une heure",hh:"%d heures",d:"un jour",dd:"%d jours",M:"un mois",MM:"%d mois",y:"un an",yy:"%d ans"},ordinal:function(n){return""+n+(1===n?"er":"")}},(o&&"object"==typeof o&&"default"in o?o:{default:o}).default.locale(a,null,!0),a)},7283:function(n,r,i){"use strict";i.d(r,{Ps:function(){return gql}});var o,a,u,p,d,x=i(655);let w=/\r\n|[\n\r]/g;function getLocation(n,r){let i=0,o=1;for(let a of n.body.matchAll(w)){if("number"==typeof a.index||function(n,r){if(!n)throw Error(null!=r?r:"Unexpected invariant triggered.")}(!1),a.index>=r)break;i=a.index+a[0].length,o+=1}return{line:o,column:r+1-i}}function printSourceLocation(n,r){let i=n.locationOffset.column-1,o="".padStart(i)+n.body,a=r.line-1,u=n.locationOffset.line-1,p=r.line+u,d=1===r.line?i:0,x=r.column+d,w=`${n.name}:${p}:${x}
`,k=o.split(/\r\n|[\n\r]/g),E=k[a];if(E.length>120){let n=Math.floor(x/80),r=x%80,i=[];for(let n=0;n<E.length;n+=80)i.push(E.slice(n,n+80));return w+printPrefixedLines([[`${p} |`,i[0]],...i.slice(1,n+1).map(n=>["|",n]),["|","^".padStart(r)],["|",i[n+1]]])}return w+printPrefixedLines([[`${p-1} |`,k[a-1]],[`${p} |`,E],["|","^".padStart(x)],[`${p+1} |`,k[a+1]]])}function printPrefixedLines(n){let r=n.filter(([n,r])=>void 0!==r),i=Math.max(...r.map(([n])=>n.length));return r.map(([n,r])=>n.padStart(i)+(r?" "+r:"")).join("\n")}let GraphQLError=class GraphQLError extends Error{constructor(n,...r){var i,o,a,u;let{nodes:p,source:d,positions:x,path:w,originalError:k,extensions:E}=function(n){let r=n[0];return null==r||"kind"in r||"length"in r?{nodes:r,source:n[1],positions:n[2],path:n[3],originalError:n[4],extensions:n[5]}:r}(r);super(n),this.name="GraphQLError",this.path=null!=w?w:void 0,this.originalError=null!=k?k:void 0,this.nodes=undefinedIfEmpty(Array.isArray(p)?p:p?[p]:void 0);let C=undefinedIfEmpty(null===(i=this.nodes)||void 0===i?void 0:i.map(n=>n.loc).filter(n=>null!=n));this.source=null!=d?d:null==C?void 0:null===(o=C[0])||void 0===o?void 0:o.source,this.positions=null!=x?x:null==C?void 0:C.map(n=>n.start),this.locations=x&&d?x.map(n=>getLocation(d,n)):null==C?void 0:C.map(n=>getLocation(n.source,n.start));let D="object"==typeof(u=null==k?void 0:k.extensions)&&null!==u?null==k?void 0:k.extensions:void 0;this.extensions=null!==(a=null!=E?E:D)&&void 0!==a?a:Object.create(null),Object.defineProperties(this,{message:{writable:!0,enumerable:!0},name:{enumerable:!1},nodes:{enumerable:!1},source:{enumerable:!1},positions:{enumerable:!1},originalError:{enumerable:!1}}),null!=k&&k.stack?Object.defineProperty(this,"stack",{value:k.stack,writable:!0,configurable:!0}):Error.captureStackTrace?Error.captureStackTrace(this,GraphQLError):Object.defineProperty(this,"stack",{value:Error().stack,writable:!0,configurable:!0})}get[Symbol.toStringTag](){return"GraphQLError"}toString(){let n=this.message;if(this.nodes){for(let i of this.nodes)if(i.loc){var r;n+="\n\n"+printSourceLocation((r=i.loc).source,getLocation(r.source,r.start))}}else if(this.source&&this.locations)for(let r of this.locations)n+="\n\n"+printSourceLocation(this.source,r);return n}toJSON(){let n={message:this.message};return null!=this.locations&&(n.locations=this.locations),null!=this.path&&(n.path=this.path),null!=this.extensions&&Object.keys(this.extensions).length>0&&(n.extensions=this.extensions),n}};function undefinedIfEmpty(n){return void 0===n||0===n.length?void 0:n}function syntaxError(n,r,i){return new GraphQLError(`Syntax Error: ${i}`,{source:n,positions:[r]})}var k=i(2380);(o=p||(p={})).QUERY="QUERY",o.MUTATION="MUTATION",o.SUBSCRIPTION="SUBSCRIPTION",o.FIELD="FIELD",o.FRAGMENT_DEFINITION="FRAGMENT_DEFINITION",o.FRAGMENT_SPREAD="FRAGMENT_SPREAD",o.INLINE_FRAGMENT="INLINE_FRAGMENT",o.VARIABLE_DEFINITION="VARIABLE_DEFINITION",o.SCHEMA="SCHEMA",o.SCALAR="SCALAR",o.OBJECT="OBJECT",o.FIELD_DEFINITION="FIELD_DEFINITION",o.ARGUMENT_DEFINITION="ARGUMENT_DEFINITION",o.INTERFACE="INTERFACE",o.UNION="UNION",o.ENUM="ENUM",o.ENUM_VALUE="ENUM_VALUE",o.INPUT_OBJECT="INPUT_OBJECT",o.INPUT_FIELD_DEFINITION="INPUT_FIELD_DEFINITION";var E=i(7359),C=i(7392),D=i(8297);(a=d||(d={})).SOF="<SOF>",a.EOF="<EOF>",a.BANG="!",a.DOLLAR="$",a.AMP="&",a.PAREN_L="(",a.PAREN_R=")",a.SPREAD="...",a.COLON=":",a.EQUALS="=",a.AT="@",a.BRACKET_L="[",a.BRACKET_R="]",a.BRACE_L="{",a.PIPE="|",a.BRACE_R="}",a.NAME="Name",a.INT="Int",a.FLOAT="Float",a.STRING="String",a.BLOCK_STRING="BlockString",a.COMMENT="Comment";let Lexer=class Lexer{constructor(n){let r=new k.WU(d.SOF,0,0,0,0);this.source=n,this.lastToken=r,this.token=r,this.line=1,this.lineStart=0}get[Symbol.toStringTag](){return"Lexer"}advance(){this.lastToken=this.token;let n=this.token=this.lookahead();return n}lookahead(){let n=this.token;if(n.kind!==d.EOF)do if(n.next)n=n.next;else{let r=function(n,r){let i=n.source.body,o=i.length,a=r;for(;a<o;){let r=i.charCodeAt(a);switch(r){case 65279:case 9:case 32:case 44:++a;continue;case 10:++a,++n.line,n.lineStart=a;continue;case 13:10===i.charCodeAt(a+1)?a+=2:++a,++n.line,n.lineStart=a;continue;case 35:return function(n,r){let i=n.source.body,o=i.length,a=r+1;for(;a<o;){let n=i.charCodeAt(a);if(10===n||13===n)break;if(isUnicodeScalarValue(n))++a;else if(isSupplementaryCodePoint(i,a))a+=2;else break}return createToken(n,d.COMMENT,r,a,i.slice(r+1,a))}(n,a);case 33:return createToken(n,d.BANG,a,a+1);case 36:return createToken(n,d.DOLLAR,a,a+1);case 38:return createToken(n,d.AMP,a,a+1);case 40:return createToken(n,d.PAREN_L,a,a+1);case 41:return createToken(n,d.PAREN_R,a,a+1);case 46:if(46===i.charCodeAt(a+1)&&46===i.charCodeAt(a+2))return createToken(n,d.SPREAD,a,a+3);break;case 58:return createToken(n,d.COLON,a,a+1);case 61:return createToken(n,d.EQUALS,a,a+1);case 64:return createToken(n,d.AT,a,a+1);case 91:return createToken(n,d.BRACKET_L,a,a+1);case 93:return createToken(n,d.BRACKET_R,a,a+1);case 123:return createToken(n,d.BRACE_L,a,a+1);case 124:return createToken(n,d.PIPE,a,a+1);case 125:return createToken(n,d.BRACE_R,a,a+1);case 34:if(34===i.charCodeAt(a+1)&&34===i.charCodeAt(a+2))return function(n,r){let i=n.source.body,o=i.length,a=n.lineStart,u=r+3,p=u,x="",w=[];for(;u<o;){let o=i.charCodeAt(u);if(34===o&&34===i.charCodeAt(u+1)&&34===i.charCodeAt(u+2)){x+=i.slice(p,u),w.push(x);let o=createToken(n,d.BLOCK_STRING,r,u+3,(0,C.wv)(w).join("\n"));return n.line+=w.length-1,n.lineStart=a,o}if(92===o&&34===i.charCodeAt(u+1)&&34===i.charCodeAt(u+2)&&34===i.charCodeAt(u+3)){x+=i.slice(p,u),p=u+1,u+=4;continue}if(10===o||13===o){x+=i.slice(p,u),w.push(x),13===o&&10===i.charCodeAt(u+1)?u+=2:++u,x="",p=u,a=u;continue}if(isUnicodeScalarValue(o))++u;else if(isSupplementaryCodePoint(i,u))u+=2;else throw syntaxError(n.source,u,`Invalid character within String: ${printCodePointAt(n,u)}.`)}throw syntaxError(n.source,u,"Unterminated string.")}(n,a);return function(n,r){let i=n.source.body,o=i.length,a=r+1,u=a,p="";for(;a<o;){let o=i.charCodeAt(a);if(34===o)return p+=i.slice(u,a),createToken(n,d.STRING,r,a+1,p);if(92===o){p+=i.slice(u,a);let r=117===i.charCodeAt(a+1)?123===i.charCodeAt(a+2)?function(n,r){let i=n.source.body,o=0,a=3;for(;a<12;){let n=i.charCodeAt(r+a++);if(125===n){if(a<5||!isUnicodeScalarValue(o))break;return{value:String.fromCodePoint(o),size:a}}if((o=o<<4|readHexDigit(n))<0)break}throw syntaxError(n.source,r,`Invalid Unicode escape sequence: "${i.slice(r,r+a)}".`)}(n,a):function(n,r){let i=n.source.body,o=read16BitHexCode(i,r+2);if(isUnicodeScalarValue(o))return{value:String.fromCodePoint(o),size:6};if(isLeadingSurrogate(o)&&92===i.charCodeAt(r+6)&&117===i.charCodeAt(r+7)){let n=read16BitHexCode(i,r+8);if(isTrailingSurrogate(n))return{value:String.fromCodePoint(o,n),size:12}}throw syntaxError(n.source,r,`Invalid Unicode escape sequence: "${i.slice(r,r+6)}".`)}(n,a):function(n,r){let i=n.source.body,o=i.charCodeAt(r+1);switch(o){case 34:return{value:'"',size:2};case 92:return{value:"\\",size:2};case 47:return{value:"/",size:2};case 98:return{value:"\b",size:2};case 102:return{value:"\f",size:2};case 110:return{value:"\n",size:2};case 114:return{value:"\r",size:2};case 116:return{value:"	",size:2}}throw syntaxError(n.source,r,`Invalid character escape sequence: "${i.slice(r,r+2)}".`)}(n,a);p+=r.value,a+=r.size,u=a;continue}if(10===o||13===o)break;if(isUnicodeScalarValue(o))++a;else if(isSupplementaryCodePoint(i,a))a+=2;else throw syntaxError(n.source,a,`Invalid character within String: ${printCodePointAt(n,a)}.`)}throw syntaxError(n.source,a,"Unterminated string.")}(n,a)}if((0,D.X1)(r)||45===r)return function(n,r,i){let o=n.source.body,a=r,u=i,p=!1;if(45===u&&(u=o.charCodeAt(++a)),48===u){if(u=o.charCodeAt(++a),(0,D.X1)(u))throw syntaxError(n.source,a,`Invalid number, unexpected digit after 0: ${printCodePointAt(n,a)}.`)}else a=readDigits(n,a,u),u=o.charCodeAt(a);if(46===u&&(p=!0,u=o.charCodeAt(++a),a=readDigits(n,a,u),u=o.charCodeAt(a)),(69===u||101===u)&&(p=!0,(43===(u=o.charCodeAt(++a))||45===u)&&(u=o.charCodeAt(++a)),a=readDigits(n,a,u),u=o.charCodeAt(a)),46===u||(0,D.LQ)(u))throw syntaxError(n.source,a,`Invalid number, expected digit but got: ${printCodePointAt(n,a)}.`);return createToken(n,p?d.FLOAT:d.INT,r,a,o.slice(r,a))}(n,a,r);if((0,D.LQ)(r))return function(n,r){let i=n.source.body,o=i.length,a=r+1;for(;a<o;){let n=i.charCodeAt(a);if((0,D.HQ)(n))++a;else break}return createToken(n,d.NAME,r,a,i.slice(r,a))}(n,a);throw syntaxError(n.source,a,39===r?"Unexpected single quote character ('), did you mean to use a double quote (\")?":isUnicodeScalarValue(r)||isSupplementaryCodePoint(i,a)?`Unexpected character: ${printCodePointAt(n,a)}.`:`Invalid character: ${printCodePointAt(n,a)}.`)}return createToken(n,d.EOF,o,o)}(this,n.end);n.next=r,r.prev=n,n=r}while(n.kind===d.COMMENT);return n}};function isUnicodeScalarValue(n){return n>=0&&n<=55295||n>=57344&&n<=1114111}function isSupplementaryCodePoint(n,r){return isLeadingSurrogate(n.charCodeAt(r))&&isTrailingSurrogate(n.charCodeAt(r+1))}function isLeadingSurrogate(n){return n>=55296&&n<=56319}function isTrailingSurrogate(n){return n>=56320&&n<=57343}function printCodePointAt(n,r){let i=n.source.body.codePointAt(r);if(void 0===i)return d.EOF;if(i>=32&&i<=126){let n=String.fromCodePoint(i);return'"'===n?"'\"'":`"${n}"`}return"U+"+i.toString(16).toUpperCase().padStart(4,"0")}function createToken(n,r,i,o,a){let u=n.line,p=1+i-n.lineStart;return new k.WU(r,i,o,u,p,a)}function readDigits(n,r,i){if(!(0,D.X1)(i))throw syntaxError(n.source,r,`Invalid number, expected digit but got: ${printCodePointAt(n,r)}.`);let o=n.source.body,a=r+1;for(;(0,D.X1)(o.charCodeAt(a));)++a;return a}function read16BitHexCode(n,r){return readHexDigit(n.charCodeAt(r))<<12|readHexDigit(n.charCodeAt(r+1))<<8|readHexDigit(n.charCodeAt(r+2))<<4|readHexDigit(n.charCodeAt(r+3))}function readHexDigit(n){return n>=48&&n<=57?n-48:n>=65&&n<=70?n-55:n>=97&&n<=102?n-87:-1}var I=i(1270);let Parser=class Parser{constructor(n,r={}){let i=(0,I.T)(n)?n:new I.H(n);this._lexer=new Lexer(i),this._options=r,this._tokenCounter=0}parseName(){let n=this.expectToken(d.NAME);return this.node(n,{kind:E.h.NAME,value:n.value})}parseDocument(){return this.node(this._lexer.token,{kind:E.h.DOCUMENT,definitions:this.many(d.SOF,this.parseDefinition,d.EOF)})}parseDefinition(){if(this.peek(d.BRACE_L))return this.parseOperationDefinition();let n=this.peekDescription(),r=n?this._lexer.lookahead():this._lexer.token;if(r.kind===d.NAME){switch(r.value){case"schema":return this.parseSchemaDefinition();case"scalar":return this.parseScalarTypeDefinition();case"type":return this.parseObjectTypeDefinition();case"interface":return this.parseInterfaceTypeDefinition();case"union":return this.parseUnionTypeDefinition();case"enum":return this.parseEnumTypeDefinition();case"input":return this.parseInputObjectTypeDefinition();case"directive":return this.parseDirectiveDefinition()}if(n)throw syntaxError(this._lexer.source,this._lexer.token.start,"Unexpected description, descriptions are supported only on type definitions.");switch(r.value){case"query":case"mutation":case"subscription":return this.parseOperationDefinition();case"fragment":return this.parseFragmentDefinition();case"extend":return this.parseTypeSystemExtension()}}throw this.unexpected(r)}parseOperationDefinition(){let n;let r=this._lexer.token;if(this.peek(d.BRACE_L))return this.node(r,{kind:E.h.OPERATION_DEFINITION,operation:k.ku.QUERY,name:void 0,variableDefinitions:[],directives:[],selectionSet:this.parseSelectionSet()});let i=this.parseOperationType();return this.peek(d.NAME)&&(n=this.parseName()),this.node(r,{kind:E.h.OPERATION_DEFINITION,operation:i,name:n,variableDefinitions:this.parseVariableDefinitions(),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseOperationType(){let n=this.expectToken(d.NAME);switch(n.value){case"query":return k.ku.QUERY;case"mutation":return k.ku.MUTATION;case"subscription":return k.ku.SUBSCRIPTION}throw this.unexpected(n)}parseVariableDefinitions(){return this.optionalMany(d.PAREN_L,this.parseVariableDefinition,d.PAREN_R)}parseVariableDefinition(){return this.node(this._lexer.token,{kind:E.h.VARIABLE_DEFINITION,variable:this.parseVariable(),type:(this.expectToken(d.COLON),this.parseTypeReference()),defaultValue:this.expectOptionalToken(d.EQUALS)?this.parseConstValueLiteral():void 0,directives:this.parseConstDirectives()})}parseVariable(){let n=this._lexer.token;return this.expectToken(d.DOLLAR),this.node(n,{kind:E.h.VARIABLE,name:this.parseName()})}parseSelectionSet(){return this.node(this._lexer.token,{kind:E.h.SELECTION_SET,selections:this.many(d.BRACE_L,this.parseSelection,d.BRACE_R)})}parseSelection(){return this.peek(d.SPREAD)?this.parseFragment():this.parseField()}parseField(){let n,r;let i=this._lexer.token,o=this.parseName();return this.expectOptionalToken(d.COLON)?(n=o,r=this.parseName()):r=o,this.node(i,{kind:E.h.FIELD,alias:n,name:r,arguments:this.parseArguments(!1),directives:this.parseDirectives(!1),selectionSet:this.peek(d.BRACE_L)?this.parseSelectionSet():void 0})}parseArguments(n){let r=n?this.parseConstArgument:this.parseArgument;return this.optionalMany(d.PAREN_L,r,d.PAREN_R)}parseArgument(n=!1){let r=this._lexer.token,i=this.parseName();return this.expectToken(d.COLON),this.node(r,{kind:E.h.ARGUMENT,name:i,value:this.parseValueLiteral(n)})}parseConstArgument(){return this.parseArgument(!0)}parseFragment(){let n=this._lexer.token;this.expectToken(d.SPREAD);let r=this.expectOptionalKeyword("on");return!r&&this.peek(d.NAME)?this.node(n,{kind:E.h.FRAGMENT_SPREAD,name:this.parseFragmentName(),directives:this.parseDirectives(!1)}):this.node(n,{kind:E.h.INLINE_FRAGMENT,typeCondition:r?this.parseNamedType():void 0,directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentDefinition(){let n=this._lexer.token;return(this.expectKeyword("fragment"),!0===this._options.allowLegacyFragmentVariables)?this.node(n,{kind:E.h.FRAGMENT_DEFINITION,name:this.parseFragmentName(),variableDefinitions:this.parseVariableDefinitions(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()}):this.node(n,{kind:E.h.FRAGMENT_DEFINITION,name:this.parseFragmentName(),typeCondition:(this.expectKeyword("on"),this.parseNamedType()),directives:this.parseDirectives(!1),selectionSet:this.parseSelectionSet()})}parseFragmentName(){if("on"===this._lexer.token.value)throw this.unexpected();return this.parseName()}parseValueLiteral(n){let r=this._lexer.token;switch(r.kind){case d.BRACKET_L:return this.parseList(n);case d.BRACE_L:return this.parseObject(n);case d.INT:return this.advanceLexer(),this.node(r,{kind:E.h.INT,value:r.value});case d.FLOAT:return this.advanceLexer(),this.node(r,{kind:E.h.FLOAT,value:r.value});case d.STRING:case d.BLOCK_STRING:return this.parseStringLiteral();case d.NAME:switch(this.advanceLexer(),r.value){case"true":return this.node(r,{kind:E.h.BOOLEAN,value:!0});case"false":return this.node(r,{kind:E.h.BOOLEAN,value:!1});case"null":return this.node(r,{kind:E.h.NULL});default:return this.node(r,{kind:E.h.ENUM,value:r.value})}case d.DOLLAR:if(n){if(this.expectToken(d.DOLLAR),this._lexer.token.kind===d.NAME){let n=this._lexer.token.value;throw syntaxError(this._lexer.source,r.start,`Unexpected variable "$${n}" in constant value.`)}throw this.unexpected(r)}return this.parseVariable();default:throw this.unexpected()}}parseConstValueLiteral(){return this.parseValueLiteral(!0)}parseStringLiteral(){let n=this._lexer.token;return this.advanceLexer(),this.node(n,{kind:E.h.STRING,value:n.value,block:n.kind===d.BLOCK_STRING})}parseList(n){return this.node(this._lexer.token,{kind:E.h.LIST,values:this.any(d.BRACKET_L,()=>this.parseValueLiteral(n),d.BRACKET_R)})}parseObject(n){return this.node(this._lexer.token,{kind:E.h.OBJECT,fields:this.any(d.BRACE_L,()=>this.parseObjectField(n),d.BRACE_R)})}parseObjectField(n){let r=this._lexer.token,i=this.parseName();return this.expectToken(d.COLON),this.node(r,{kind:E.h.OBJECT_FIELD,name:i,value:this.parseValueLiteral(n)})}parseDirectives(n){let r=[];for(;this.peek(d.AT);)r.push(this.parseDirective(n));return r}parseConstDirectives(){return this.parseDirectives(!0)}parseDirective(n){let r=this._lexer.token;return this.expectToken(d.AT),this.node(r,{kind:E.h.DIRECTIVE,name:this.parseName(),arguments:this.parseArguments(n)})}parseTypeReference(){let n;let r=this._lexer.token;if(this.expectOptionalToken(d.BRACKET_L)){let i=this.parseTypeReference();this.expectToken(d.BRACKET_R),n=this.node(r,{kind:E.h.LIST_TYPE,type:i})}else n=this.parseNamedType();return this.expectOptionalToken(d.BANG)?this.node(r,{kind:E.h.NON_NULL_TYPE,type:n}):n}parseNamedType(){return this.node(this._lexer.token,{kind:E.h.NAMED_TYPE,name:this.parseName()})}peekDescription(){return this.peek(d.STRING)||this.peek(d.BLOCK_STRING)}parseDescription(){if(this.peekDescription())return this.parseStringLiteral()}parseSchemaDefinition(){let n=this._lexer.token,r=this.parseDescription();this.expectKeyword("schema");let i=this.parseConstDirectives(),o=this.many(d.BRACE_L,this.parseOperationTypeDefinition,d.BRACE_R);return this.node(n,{kind:E.h.SCHEMA_DEFINITION,description:r,directives:i,operationTypes:o})}parseOperationTypeDefinition(){let n=this._lexer.token,r=this.parseOperationType();this.expectToken(d.COLON);let i=this.parseNamedType();return this.node(n,{kind:E.h.OPERATION_TYPE_DEFINITION,operation:r,type:i})}parseScalarTypeDefinition(){let n=this._lexer.token,r=this.parseDescription();this.expectKeyword("scalar");let i=this.parseName(),o=this.parseConstDirectives();return this.node(n,{kind:E.h.SCALAR_TYPE_DEFINITION,description:r,name:i,directives:o})}parseObjectTypeDefinition(){let n=this._lexer.token,r=this.parseDescription();this.expectKeyword("type");let i=this.parseName(),o=this.parseImplementsInterfaces(),a=this.parseConstDirectives(),u=this.parseFieldsDefinition();return this.node(n,{kind:E.h.OBJECT_TYPE_DEFINITION,description:r,name:i,interfaces:o,directives:a,fields:u})}parseImplementsInterfaces(){return this.expectOptionalKeyword("implements")?this.delimitedMany(d.AMP,this.parseNamedType):[]}parseFieldsDefinition(){return this.optionalMany(d.BRACE_L,this.parseFieldDefinition,d.BRACE_R)}parseFieldDefinition(){let n=this._lexer.token,r=this.parseDescription(),i=this.parseName(),o=this.parseArgumentDefs();this.expectToken(d.COLON);let a=this.parseTypeReference(),u=this.parseConstDirectives();return this.node(n,{kind:E.h.FIELD_DEFINITION,description:r,name:i,arguments:o,type:a,directives:u})}parseArgumentDefs(){return this.optionalMany(d.PAREN_L,this.parseInputValueDef,d.PAREN_R)}parseInputValueDef(){let n;let r=this._lexer.token,i=this.parseDescription(),o=this.parseName();this.expectToken(d.COLON);let a=this.parseTypeReference();this.expectOptionalToken(d.EQUALS)&&(n=this.parseConstValueLiteral());let u=this.parseConstDirectives();return this.node(r,{kind:E.h.INPUT_VALUE_DEFINITION,description:i,name:o,type:a,defaultValue:n,directives:u})}parseInterfaceTypeDefinition(){let n=this._lexer.token,r=this.parseDescription();this.expectKeyword("interface");let i=this.parseName(),o=this.parseImplementsInterfaces(),a=this.parseConstDirectives(),u=this.parseFieldsDefinition();return this.node(n,{kind:E.h.INTERFACE_TYPE_DEFINITION,description:r,name:i,interfaces:o,directives:a,fields:u})}parseUnionTypeDefinition(){let n=this._lexer.token,r=this.parseDescription();this.expectKeyword("union");let i=this.parseName(),o=this.parseConstDirectives(),a=this.parseUnionMemberTypes();return this.node(n,{kind:E.h.UNION_TYPE_DEFINITION,description:r,name:i,directives:o,types:a})}parseUnionMemberTypes(){return this.expectOptionalToken(d.EQUALS)?this.delimitedMany(d.PIPE,this.parseNamedType):[]}parseEnumTypeDefinition(){let n=this._lexer.token,r=this.parseDescription();this.expectKeyword("enum");let i=this.parseName(),o=this.parseConstDirectives(),a=this.parseEnumValuesDefinition();return this.node(n,{kind:E.h.ENUM_TYPE_DEFINITION,description:r,name:i,directives:o,values:a})}parseEnumValuesDefinition(){return this.optionalMany(d.BRACE_L,this.parseEnumValueDefinition,d.BRACE_R)}parseEnumValueDefinition(){let n=this._lexer.token,r=this.parseDescription(),i=this.parseEnumValueName(),o=this.parseConstDirectives();return this.node(n,{kind:E.h.ENUM_VALUE_DEFINITION,description:r,name:i,directives:o})}parseEnumValueName(){if("true"===this._lexer.token.value||"false"===this._lexer.token.value||"null"===this._lexer.token.value)throw syntaxError(this._lexer.source,this._lexer.token.start,`${getTokenDesc(this._lexer.token)} is reserved and cannot be used for an enum value.`);return this.parseName()}parseInputObjectTypeDefinition(){let n=this._lexer.token,r=this.parseDescription();this.expectKeyword("input");let i=this.parseName(),o=this.parseConstDirectives(),a=this.parseInputFieldsDefinition();return this.node(n,{kind:E.h.INPUT_OBJECT_TYPE_DEFINITION,description:r,name:i,directives:o,fields:a})}parseInputFieldsDefinition(){return this.optionalMany(d.BRACE_L,this.parseInputValueDef,d.BRACE_R)}parseTypeSystemExtension(){let n=this._lexer.lookahead();if(n.kind===d.NAME)switch(n.value){case"schema":return this.parseSchemaExtension();case"scalar":return this.parseScalarTypeExtension();case"type":return this.parseObjectTypeExtension();case"interface":return this.parseInterfaceTypeExtension();case"union":return this.parseUnionTypeExtension();case"enum":return this.parseEnumTypeExtension();case"input":return this.parseInputObjectTypeExtension()}throw this.unexpected(n)}parseSchemaExtension(){let n=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("schema");let r=this.parseConstDirectives(),i=this.optionalMany(d.BRACE_L,this.parseOperationTypeDefinition,d.BRACE_R);if(0===r.length&&0===i.length)throw this.unexpected();return this.node(n,{kind:E.h.SCHEMA_EXTENSION,directives:r,operationTypes:i})}parseScalarTypeExtension(){let n=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("scalar");let r=this.parseName(),i=this.parseConstDirectives();if(0===i.length)throw this.unexpected();return this.node(n,{kind:E.h.SCALAR_TYPE_EXTENSION,name:r,directives:i})}parseObjectTypeExtension(){let n=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("type");let r=this.parseName(),i=this.parseImplementsInterfaces(),o=this.parseConstDirectives(),a=this.parseFieldsDefinition();if(0===i.length&&0===o.length&&0===a.length)throw this.unexpected();return this.node(n,{kind:E.h.OBJECT_TYPE_EXTENSION,name:r,interfaces:i,directives:o,fields:a})}parseInterfaceTypeExtension(){let n=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("interface");let r=this.parseName(),i=this.parseImplementsInterfaces(),o=this.parseConstDirectives(),a=this.parseFieldsDefinition();if(0===i.length&&0===o.length&&0===a.length)throw this.unexpected();return this.node(n,{kind:E.h.INTERFACE_TYPE_EXTENSION,name:r,interfaces:i,directives:o,fields:a})}parseUnionTypeExtension(){let n=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("union");let r=this.parseName(),i=this.parseConstDirectives(),o=this.parseUnionMemberTypes();if(0===i.length&&0===o.length)throw this.unexpected();return this.node(n,{kind:E.h.UNION_TYPE_EXTENSION,name:r,directives:i,types:o})}parseEnumTypeExtension(){let n=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("enum");let r=this.parseName(),i=this.parseConstDirectives(),o=this.parseEnumValuesDefinition();if(0===i.length&&0===o.length)throw this.unexpected();return this.node(n,{kind:E.h.ENUM_TYPE_EXTENSION,name:r,directives:i,values:o})}parseInputObjectTypeExtension(){let n=this._lexer.token;this.expectKeyword("extend"),this.expectKeyword("input");let r=this.parseName(),i=this.parseConstDirectives(),o=this.parseInputFieldsDefinition();if(0===i.length&&0===o.length)throw this.unexpected();return this.node(n,{kind:E.h.INPUT_OBJECT_TYPE_EXTENSION,name:r,directives:i,fields:o})}parseDirectiveDefinition(){let n=this._lexer.token,r=this.parseDescription();this.expectKeyword("directive"),this.expectToken(d.AT);let i=this.parseName(),o=this.parseArgumentDefs(),a=this.expectOptionalKeyword("repeatable");this.expectKeyword("on");let u=this.parseDirectiveLocations();return this.node(n,{kind:E.h.DIRECTIVE_DEFINITION,description:r,name:i,arguments:o,repeatable:a,locations:u})}parseDirectiveLocations(){return this.delimitedMany(d.PIPE,this.parseDirectiveLocation)}parseDirectiveLocation(){let n=this._lexer.token,r=this.parseName();if(Object.prototype.hasOwnProperty.call(p,r.value))return r;throw this.unexpected(n)}node(n,r){return!0!==this._options.noLocation&&(r.loc=new k.Ye(n,this._lexer.lastToken,this._lexer.source)),r}peek(n){return this._lexer.token.kind===n}expectToken(n){let r=this._lexer.token;if(r.kind===n)return this.advanceLexer(),r;throw syntaxError(this._lexer.source,r.start,`Expected ${getTokenKindDesc(n)}, found ${getTokenDesc(r)}.`)}expectOptionalToken(n){let r=this._lexer.token;return r.kind===n&&(this.advanceLexer(),!0)}expectKeyword(n){let r=this._lexer.token;if(r.kind===d.NAME&&r.value===n)this.advanceLexer();else throw syntaxError(this._lexer.source,r.start,`Expected "${n}", found ${getTokenDesc(r)}.`)}expectOptionalKeyword(n){let r=this._lexer.token;return r.kind===d.NAME&&r.value===n&&(this.advanceLexer(),!0)}unexpected(n){let r=null!=n?n:this._lexer.token;return syntaxError(this._lexer.source,r.start,`Unexpected ${getTokenDesc(r)}.`)}any(n,r,i){this.expectToken(n);let o=[];for(;!this.expectOptionalToken(i);)o.push(r.call(this));return o}optionalMany(n,r,i){if(this.expectOptionalToken(n)){let n=[];do n.push(r.call(this));while(!this.expectOptionalToken(i));return n}return[]}many(n,r,i){this.expectToken(n);let o=[];do o.push(r.call(this));while(!this.expectOptionalToken(i));return o}delimitedMany(n,r){this.expectOptionalToken(n);let i=[];do i.push(r.call(this));while(this.expectOptionalToken(n));return i}advanceLexer(){let{maxTokens:n}=this._options,r=this._lexer.advance();if(void 0!==n&&r.kind!==d.EOF&&(++this._tokenCounter,this._tokenCounter>n))throw syntaxError(this._lexer.source,r.start,`Document contains more that ${n} tokens. Parsing aborted.`)}};function getTokenDesc(n){let r=n.value;return getTokenKindDesc(n.kind)+(null!=r?` "${r}"`:"")}function getTokenKindDesc(n){return n===d.BANG||n===d.DOLLAR||n===d.AMP||n===d.PAREN_L||n===d.PAREN_R||n===d.SPREAD||n===d.COLON||n===d.EQUALS||n===d.AT||n===d.BRACKET_L||n===d.BRACKET_R||n===d.BRACE_L||n===d.PIPE||n===d.BRACE_R?`"${n}"`:n}var R=new Map,V=new Map,K=!0,W=!1;function normalize(n){return n.replace(/[\s,]+/g," ").trim()}function gql(n){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];"string"==typeof n&&(n=[n]);var o=n[0];return r.forEach(function(r,i){r&&"Document"===r.kind?o+=r.loc.source.body:o+=r,o+=n[i+1]}),function(n){var r=normalize(n);if(!R.has(r)){var i,o,a,u,p,d=function(n,r){let i=new Parser(n,r);return i.parseDocument()}(n,{experimentalFragmentVariables:W,allowLegacyFragmentVariables:W});if(!d||"Document"!==d.kind)throw Error("Not a valid GraphQL document.");R.set(r,(i=new Set,o=[],d.definitions.forEach(function(n){if("FragmentDefinition"===n.kind){var r,a=n.name.value,u=normalize((r=n.loc).source.body.substring(r.start,r.end)),p=V.get(a);p&&!p.has(u)?K&&console.warn("Warning: fragment with name "+a+" already exists.\ngraphql-tag enforces all fragment names across your application to be unique; read more about\nthis in the docs: http://dev.apollodata.com/core/fragments.html#unique-names"):p||V.set(a,p=new Set),p.add(u),i.has(u)||(i.add(u),o.push(n))}else o.push(n)}),a=(0,x.pi)((0,x.pi)({},d),{definitions:o}),(u=new Set(a.definitions)).forEach(function(n){n.loc&&delete n.loc,Object.keys(n).forEach(function(r){var i=n[r];i&&"object"==typeof i&&u.add(i)})}),(p=a.loc)&&(delete p.startToken,delete p.endToken),a))}return R.get(r)}(o)}var G={gql:gql,resetCaches:function(){R.clear(),V.clear()},disableFragmentWarnings:function(){K=!1},enableExperimentalFragmentVariables:function(){W=!0},disableExperimentalFragmentVariables:function(){W=!1}};(u=gql||(gql={})).gql=G.gql,u.resetCaches=G.resetCaches,u.disableFragmentWarnings=G.disableFragmentWarnings,u.enableExperimentalFragmentVariables=G.enableExperimentalFragmentVariables,u.disableExperimentalFragmentVariables=G.disableExperimentalFragmentVariables,gql.default=gql},8679:function(n,r,i){"use strict";var o=i(1296),a={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},u={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},p={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},d={};function getStatics(n){return o.isMemo(n)?p:d[n.$$typeof]||a}d[o.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},d[o.Memo]=p;var x=Object.defineProperty,w=Object.getOwnPropertyNames,k=Object.getOwnPropertySymbols,E=Object.getOwnPropertyDescriptor,C=Object.getPrototypeOf,D=Object.prototype;n.exports=function hoistNonReactStatics(n,r,i){if("string"!=typeof r){if(D){var o=C(r);o&&o!==D&&hoistNonReactStatics(n,o,i)}var a=w(r);k&&(a=a.concat(k(r)));for(var p=getStatics(n),d=getStatics(r),I=0;I<a.length;++I){var R=a[I];if(!u[R]&&!(i&&i[R])&&!(d&&d[R])&&!(p&&p[R])){var V=E(r,R);try{x(n,R,V)}catch(n){}}}}return n}},6103:function(n,r){"use strict";/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i="function"==typeof Symbol&&Symbol.for,o=i?Symbol.for("react.element"):60103,a=i?Symbol.for("react.portal"):60106,u=i?Symbol.for("react.fragment"):60107,p=i?Symbol.for("react.strict_mode"):60108,d=i?Symbol.for("react.profiler"):60114,x=i?Symbol.for("react.provider"):60109,w=i?Symbol.for("react.context"):60110,k=i?Symbol.for("react.async_mode"):60111,E=i?Symbol.for("react.concurrent_mode"):60111,C=i?Symbol.for("react.forward_ref"):60112,D=i?Symbol.for("react.suspense"):60113,I=i?Symbol.for("react.suspense_list"):60120,R=i?Symbol.for("react.memo"):60115,V=i?Symbol.for("react.lazy"):60116,K=i?Symbol.for("react.block"):60121,W=i?Symbol.for("react.fundamental"):60117,G=i?Symbol.for("react.responder"):60118,J=i?Symbol.for("react.scope"):60119;function z(n){if("object"==typeof n&&null!==n){var r=n.$$typeof;switch(r){case o:switch(n=n.type){case k:case E:case u:case d:case p:case D:return n;default:switch(n=n&&n.$$typeof){case w:case C:case V:case R:case x:return n;default:return r}}case a:return r}}}function A(n){return z(n)===E}r.AsyncMode=k,r.ConcurrentMode=E,r.ContextConsumer=w,r.ContextProvider=x,r.Element=o,r.ForwardRef=C,r.Fragment=u,r.Lazy=V,r.Memo=R,r.Portal=a,r.Profiler=d,r.StrictMode=p,r.Suspense=D,r.isAsyncMode=function(n){return A(n)||z(n)===k},r.isConcurrentMode=A,r.isContextConsumer=function(n){return z(n)===w},r.isContextProvider=function(n){return z(n)===x},r.isElement=function(n){return"object"==typeof n&&null!==n&&n.$$typeof===o},r.isForwardRef=function(n){return z(n)===C},r.isFragment=function(n){return z(n)===u},r.isLazy=function(n){return z(n)===V},r.isMemo=function(n){return z(n)===R},r.isPortal=function(n){return z(n)===a},r.isProfiler=function(n){return z(n)===d},r.isStrictMode=function(n){return z(n)===p},r.isSuspense=function(n){return z(n)===D},r.isValidElementType=function(n){return"string"==typeof n||"function"==typeof n||n===u||n===E||n===d||n===p||n===D||n===I||"object"==typeof n&&null!==n&&(n.$$typeof===V||n.$$typeof===R||n.$$typeof===x||n.$$typeof===w||n.$$typeof===C||n.$$typeof===W||n.$$typeof===G||n.$$typeof===J||n.$$typeof===K)},r.typeOf=z},1296:function(n,r,i){"use strict";n.exports=i(6103)},3454:function(n,r,i){"use strict";var o,a;n.exports=(null==(o=i.g.process)?void 0:o.env)&&"object"==typeof(null==(a=i.g.process)?void 0:a.env)?i.g.process:i(7663)},1118:function(n,r,i){(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return i(4722)}])},3847:function(n,r,i){"use strict";i.d(r,{Z:function(){return tB}});var o,a,u,p,d,x,w,k,E=i(655),C="Invariant Violation",D=Object.setPrototypeOf,I=void 0===D?function(n,r){return n.__proto__=r,n}:D,R=function(n){function InvariantError(r){void 0===r&&(r=C);var i=n.call(this,"number"==typeof r?C+": "+r+" (see https://github.com/apollographql/invariant-packages)":r)||this;return i.framesToPop=1,i.name=C,I(i,InvariantError.prototype),i}return(0,E.ZT)(InvariantError,n),InvariantError}(Error);function invariant(n,r){if(!n)throw new R(r)}var V=["debug","log","warn","error","silent"],K=V.indexOf("log");function wrapConsoleMethod(n){return function(){if(V.indexOf(n)>=K)return(console[n]||console.log).apply(console,arguments)}}function maybe(n){try{return n()}catch(n){}}(o=invariant||(invariant={})).debug=wrapConsoleMethod("debug"),o.log=wrapConsoleMethod("log"),o.warn=wrapConsoleMethod("warn"),o.error=wrapConsoleMethod("error");var W=maybe(function(){return globalThis})||maybe(function(){return window})||maybe(function(){return self})||maybe(function(){return global})||maybe(function(){return maybe.constructor("return this")()}),G="__DEV__",J=function(){try{return!!__DEV__}catch(n){return Object.defineProperty(W,G,{value:"production"!==maybe(function(){return"production"}),enumerable:!1,configurable:!0,writable:!0}),W[G]}}(),et=i(3454);function process_maybe(n){try{return n()}catch(n){}}var en=process_maybe(function(){return globalThis})||process_maybe(function(){return window})||process_maybe(function(){return self})||process_maybe(function(){return global})||process_maybe(function(){return process_maybe.constructor("return this")()}),er=!1;function _arrayLikeToArray(n,r){(null==r||r>n.length)&&(r=n.length);for(var i=0,o=Array(r);i<r;i++)o[i]=n[i];return o}function _defineProperties(n,r){for(var i=0;i<r.length;i++){var o=r[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(n,o.key,o)}}function _createClass(n,r,i){return r&&_defineProperties(n.prototype,r),i&&_defineProperties(n,i),Object.defineProperty(n,"prototype",{writable:!1}),n}!en||process_maybe(function(){return"production"})||process_maybe(function(){return et})||(Object.defineProperty(en,"process",{value:{env:{NODE_ENV:"production"}},configurable:!0,enumerable:!1,writable:!0}),er=!0),i(1270).H,er&&(delete en.process,er=!1),__DEV__?invariant("boolean"==typeof J,J):invariant("boolean"==typeof J,38);var hasSymbols=function(){return"function"==typeof Symbol},hasSymbol=function(n){return hasSymbols()&&!!Symbol[n]},getSymbol=function(n){return hasSymbol(n)?Symbol[n]:"@@"+n};hasSymbols()&&!hasSymbol("observable")&&(Symbol.observable=Symbol("observable"));var ei=getSymbol("iterator"),eo=getSymbol("observable"),es=getSymbol("species");function getMethod(n,r){var i=n[r];if(null!=i){if("function"!=typeof i)throw TypeError(i+" is not a function");return i}}function getSpecies(n){var r=n.constructor;return void 0!==r&&null===(r=r[es])&&(r=void 0),void 0!==r?r:eu}function hostReportError(n){hostReportError.log?hostReportError.log(n):setTimeout(function(){throw n})}function enqueue(n){Promise.resolve().then(function(){try{n()}catch(n){hostReportError(n)}})}function cleanupSubscription(n){var r=n._cleanup;if(void 0!==r){if(n._cleanup=void 0,!r)return;try{if("function"==typeof r)r();else{var i=getMethod(r,"unsubscribe");i&&i.call(r)}}catch(n){hostReportError(n)}}}function closeSubscription(n){n._observer=void 0,n._queue=void 0,n._state="closed"}function notifySubscription(n,r,i){n._state="running";var o=n._observer;try{var a=getMethod(o,r);switch(r){case"next":a&&a.call(o,i);break;case"error":if(closeSubscription(n),a)a.call(o,i);else throw i;break;case"complete":closeSubscription(n),a&&a.call(o)}}catch(n){hostReportError(n)}"closed"===n._state?cleanupSubscription(n):"running"===n._state&&(n._state="ready")}function onNotify(n,r,i){if("closed"!==n._state){if("buffering"===n._state){n._queue.push({type:r,value:i});return}if("ready"!==n._state){n._state="buffering",n._queue=[{type:r,value:i}],enqueue(function(){return function(n){var r=n._queue;if(r){n._queue=void 0,n._state="ready";for(var i=0;i<r.length&&(notifySubscription(n,r[i].type,r[i].value),"closed"!==n._state);++i);}}(n)});return}notifySubscription(n,r,i)}}var ec=function(){function Subscription(n,r){this._cleanup=void 0,this._observer=n,this._queue=void 0,this._state="initializing";var i=new el(this);try{this._cleanup=r.call(void 0,i)}catch(n){i.error(n)}"initializing"===this._state&&(this._state="ready")}return Subscription.prototype.unsubscribe=function(){"closed"!==this._state&&(closeSubscription(this),cleanupSubscription(this))},_createClass(Subscription,[{key:"closed",get:function(){return"closed"===this._state}}]),Subscription}(),el=function(){function SubscriptionObserver(n){this._subscription=n}var n=SubscriptionObserver.prototype;return n.next=function(n){onNotify(this._subscription,"next",n)},n.error=function(n){onNotify(this._subscription,"error",n)},n.complete=function(){onNotify(this._subscription,"complete")},_createClass(SubscriptionObserver,[{key:"closed",get:function(){return"closed"===this._subscription._state}}]),SubscriptionObserver}(),eu=function(){function Observable(n){if(!(this instanceof Observable))throw TypeError("Observable cannot be called as a function");if("function"!=typeof n)throw TypeError("Observable initializer must be a function");this._subscriber=n}var n=Observable.prototype;return n.subscribe=function(n){return("object"!=typeof n||null===n)&&(n={next:n,error:arguments[1],complete:arguments[2]}),new ec(n,this._subscriber)},n.forEach=function(n){var r=this;return new Promise(function(i,o){if("function"!=typeof n){o(TypeError(n+" is not a function"));return}function done(){a.unsubscribe(),i()}var a=r.subscribe({next:function(r){try{n(r,done)}catch(n){o(n),a.unsubscribe()}},error:o,complete:i})})},n.map=function(n){var r=this;if("function"!=typeof n)throw TypeError(n+" is not a function");return new(getSpecies(this))(function(i){return r.subscribe({next:function(r){try{r=n(r)}catch(n){return i.error(n)}i.next(r)},error:function(n){i.error(n)},complete:function(){i.complete()}})})},n.filter=function(n){var r=this;if("function"!=typeof n)throw TypeError(n+" is not a function");return new(getSpecies(this))(function(i){return r.subscribe({next:function(r){try{if(!n(r))return}catch(n){return i.error(n)}i.next(r)},error:function(n){i.error(n)},complete:function(){i.complete()}})})},n.reduce=function(n){var r=this;if("function"!=typeof n)throw TypeError(n+" is not a function");var i=getSpecies(this),o=arguments.length>1,a=!1,u=arguments[1],p=u;return new i(function(i){return r.subscribe({next:function(r){var u=!a;if(a=!0,!u||o)try{p=n(p,r)}catch(n){return i.error(n)}else p=r},error:function(n){i.error(n)},complete:function(){if(!a&&!o)return i.error(TypeError("Cannot reduce an empty sequence"));i.next(p),i.complete()}})})},n.concat=function(){for(var n=this,r=arguments.length,i=Array(r),o=0;o<r;o++)i[o]=arguments[o];var a=getSpecies(this);return new a(function(r){var o,u=0;return function startNext(n){o=n.subscribe({next:function(n){r.next(n)},error:function(n){r.error(n)},complete:function(){u===i.length?(o=void 0,r.complete()):startNext(a.from(i[u++]))}})}(n),function(){o&&(o.unsubscribe(),o=void 0)}})},n.flatMap=function(n){var r=this;if("function"!=typeof n)throw TypeError(n+" is not a function");var i=getSpecies(this);return new i(function(o){var a=[],u=r.subscribe({next:function(r){if(n)try{r=n(r)}catch(n){return o.error(n)}var u=i.from(r).subscribe({next:function(n){o.next(n)},error:function(n){o.error(n)},complete:function(){var n=a.indexOf(u);n>=0&&a.splice(n,1),completeIfDone()}});a.push(u)},error:function(n){o.error(n)},complete:function(){completeIfDone()}});function completeIfDone(){u.closed&&0===a.length&&o.complete()}return function(){a.forEach(function(n){return n.unsubscribe()}),u.unsubscribe()}})},n[eo]=function(){return this},Observable.from=function(n){var r="function"==typeof this?this:Observable;if(null==n)throw TypeError(n+" is not an object");var i=getMethod(n,eo);if(i){var o=i.call(n);if(Object(o)!==o)throw TypeError(o+" is not an object");return o instanceof eu&&o.constructor===r?o:new r(function(n){return o.subscribe(n)})}if(hasSymbol("iterator")&&(i=getMethod(n,ei)))return new r(function(r){enqueue(function(){if(!r.closed){for(var o,a=function(n,r){var i="undefined"!=typeof Symbol&&n[Symbol.iterator]||n["@@iterator"];if(i)return(i=i.call(n)).next.bind(i);if(Array.isArray(n)||(i=function(n,r){if(n){if("string"==typeof n)return _arrayLikeToArray(n,r);var i=Object.prototype.toString.call(n).slice(8,-1);if("Object"===i&&n.constructor&&(i=n.constructor.name),"Map"===i||"Set"===i)return Array.from(n);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return _arrayLikeToArray(n,r)}}(n))){i&&(n=i);var o=0;return function(){return o>=n.length?{done:!0}:{done:!1,value:n[o++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(i.call(n));!(o=a()).done;){var u=o.value;if(r.next(u),r.closed)return}r.complete()}})});if(Array.isArray(n))return new r(function(r){enqueue(function(){if(!r.closed){for(var i=0;i<n.length;++i)if(r.next(n[i]),r.closed)return;r.complete()}})});throw TypeError(n+" is not observable")},Observable.of=function(){for(var n=arguments.length,r=Array(n),i=0;i<n;i++)r[i]=arguments[i];return new("function"==typeof this?this:Observable)(function(n){enqueue(function(){if(!n.closed){for(var i=0;i<r.length;++i)if(n.next(r[i]),n.closed)return;n.complete()}})})},_createClass(Observable,null,[{key:es,get:function(){return this}}]),Observable}();function isNonNullObject(n){return null!==n&&"object"==typeof n}function getFragmentQueryDocument(n,r){var i=r,o=[];return n.definitions.forEach(function(n){if("OperationDefinition"===n.kind)throw __DEV__?new R("Found a ".concat(n.operation," operation").concat(n.name?" named '".concat(n.name.value,"'"):"",". ")+"No operations are allowed when using a fragment as a query. Only fragments are allowed."):new R(43);"FragmentDefinition"===n.kind&&o.push(n)}),void 0===i&&(__DEV__?invariant(1===o.length,"Found ".concat(o.length," fragments. `fragmentName` must be provided when there is not exactly 1 fragment.")):invariant(1===o.length,44),i=o[0].name.value),(0,E.pi)((0,E.pi)({},n),{definitions:(0,E.ev)([{kind:"OperationDefinition",operation:"query",selectionSet:{kind:"SelectionSet",selections:[{kind:"FragmentSpread",name:{kind:"Name",value:i}}]}}],n.definitions,!0)})}function createFragmentMap(n){void 0===n&&(n=[]);var r={};return n.forEach(function(n){r[n.name.value]=n}),r}function getFragmentFromSelection(n,r){switch(n.kind){case"InlineFragment":return n;case"FragmentSpread":var i=n.name.value;if("function"==typeof r)return r(i);var o=r&&r[i];return __DEV__?invariant(o,"No fragment named ".concat(i)):invariant(o,45),o||null;default:return null}}function makeReference(n){return{__ref:String(n)}}function isReference(n){return!!(n&&"object"==typeof n&&"string"==typeof n.__ref)}function valueToObjectRepresentation(n,r,i,o){if("IntValue"===i.kind||"FloatValue"===i.kind)n[r.value]=Number(i.value);else if("BooleanValue"===i.kind||"StringValue"===i.kind)n[r.value]=i.value;else if("ObjectValue"===i.kind){var a={};i.fields.map(function(n){return valueToObjectRepresentation(a,n.name,n.value,o)}),n[r.value]=a}else if("Variable"===i.kind){var u=(o||{})[i.name.value];n[r.value]=u}else if("ListValue"===i.kind)n[r.value]=i.values.map(function(n){var i={};return valueToObjectRepresentation(i,r,n,o),i[r.value]});else if("EnumValue"===i.kind)n[r.value]=i.value;else if("NullValue"===i.kind)n[r.value]=null;else throw __DEV__?new R('The inline argument "'.concat(r.value,'" of kind "').concat(i.kind,'"')+"is not supported. Use variables instead of inline arguments to overcome this limitation."):new R(54)}hasSymbols()&&Object.defineProperty(eu,Symbol("extensions"),{value:{symbol:eo,hostReportError:hostReportError},configurable:!0});var ep=["connection","include","skip","client","rest","export"],ef=Object.assign(function(n,r,i){if(r&&i&&i.connection&&i.connection.key){if(!i.connection.filter||!(i.connection.filter.length>0))return i.connection.key;var o=i.connection.filter?i.connection.filter:[];o.sort();var a={};return o.forEach(function(n){a[n]=r[n]}),"".concat(i.connection.key,"(").concat(stringify(a),")")}var u=n;if(r){var p=stringify(r);u+="(".concat(p,")")}return i&&Object.keys(i).forEach(function(n){-1===ep.indexOf(n)&&(i[n]&&Object.keys(i[n]).length?u+="@".concat(n,"(").concat(stringify(i[n]),")"):u+="@".concat(n))}),u},{setStringify:function(n){var r=stringify;return stringify=n,r}}),stringify=function(n){return JSON.stringify(n,stringifyReplacer)};function stringifyReplacer(n,r){return isNonNullObject(r)&&!Array.isArray(r)&&(r=Object.keys(r).sort().reduce(function(n,i){return n[i]=r[i],n},{})),r}function argumentsObjectFromField(n,r){if(n.arguments&&n.arguments.length){var i={};return n.arguments.forEach(function(n){return valueToObjectRepresentation(i,n.name,n.value,r)}),i}return null}function resultKeyNameFromField(n){return n.alias?n.alias.value:n.name.value}function getTypenameFromResult(n,r,i){if("string"==typeof n.__typename)return n.__typename;for(var o=0,a=r.selections;o<a.length;o++){var u=a[o];if(storeUtils_isField(u)){if("__typename"===u.name.value)return n[resultKeyNameFromField(u)]}else{var p=getTypenameFromResult(n,getFragmentFromSelection(u,i).selectionSet,i);if("string"==typeof p)return p}}}function storeUtils_isField(n){return"Field"===n.kind}function isInlineFragment(n){return"InlineFragment"===n.kind}function checkDocument(n){__DEV__?invariant(n&&"Document"===n.kind,'Expecting a parsed GraphQL document. Perhaps you need to wrap the query string in a "gql" tag? http://docs.apollostack.com/apollo-client/core.html#gql'):invariant(n&&"Document"===n.kind,46);var r=n.definitions.filter(function(n){return"FragmentDefinition"!==n.kind}).map(function(n){if("OperationDefinition"!==n.kind)throw __DEV__?new R('Schema type definitions not allowed in queries. Found: "'.concat(n.kind,'"')):new R(47);return n});return __DEV__?invariant(r.length<=1,"Ambiguous GraphQL document: contains ".concat(r.length," operations")):invariant(r.length<=1,48),n}function getOperationDefinition(n){return checkDocument(n),n.definitions.filter(function(n){return"OperationDefinition"===n.kind})[0]}function getOperationName(n){return n.definitions.filter(function(n){return"OperationDefinition"===n.kind&&n.name}).map(function(n){return n.name.value})[0]||null}function getFragmentDefinitions(n){return n.definitions.filter(function(n){return"FragmentDefinition"===n.kind})}function getQueryDefinition(n){var r=getOperationDefinition(n);return __DEV__?invariant(r&&"query"===r.operation,"Must contain a query definition."):invariant(r&&"query"===r.operation,49),r}function getMainDefinition(n){checkDocument(n);for(var r,i=0,o=n.definitions;i<o.length;i++){var a=o[i];if("OperationDefinition"===a.kind){var u=a.operation;if("query"===u||"mutation"===u||"subscription"===u)return a}"FragmentDefinition"!==a.kind||r||(r=a)}if(r)return r;throw __DEV__?new R("Expected a parsed GraphQL query with a query, mutation, subscription, or a fragment."):new R(53)}function getDefaultValues(n){var r=Object.create(null),i=n&&n.variableDefinitions;return i&&i.length&&i.forEach(function(n){n.defaultValue&&valueToObjectRepresentation(r,n.variable.name,n.defaultValue)}),r}function passthrough(n,r){return r?r(n):eu.of()}function toLink(n){return"function"==typeof n?new eh(n):n}function isTerminating(n){return n.request.length<=1}var ed=function(n){function LinkError(r,i){var o=n.call(this,r)||this;return o.link=i,o}return(0,E.ZT)(LinkError,n),LinkError}(Error),eh=function(){function ApolloLink(n){n&&(this.request=n)}return ApolloLink.empty=function(){return new ApolloLink(function(){return eu.of()})},ApolloLink.from=function(n){return 0===n.length?ApolloLink.empty():n.map(toLink).reduce(function(n,r){return n.concat(r)})},ApolloLink.split=function(n,r,i){var o=toLink(r),a=toLink(i||new ApolloLink(passthrough));return new ApolloLink(isTerminating(o)&&isTerminating(a)?function(r){return n(r)?o.request(r)||eu.of():a.request(r)||eu.of()}:function(r,i){return n(r)?o.request(r,i)||eu.of():a.request(r,i)||eu.of()})},ApolloLink.execute=function(n,r){var i,o,a,u;return n.request((a=r.context,(o={variables:(i=function(n){for(var r=["query","operationName","variables","extensions","context"],i=0,o=Object.keys(n);i<o.length;i++){var a=o[i];if(0>r.indexOf(a))throw __DEV__?new R("illegal argument: ".concat(a)):new R(26)}return n}(r)).variables||{},extensions:i.extensions||{},operationName:i.operationName,query:i.query}).operationName||(o.operationName="string"!=typeof o.query?getOperationName(o.query)||void 0:""),u=(0,E.pi)({},a),Object.defineProperty(o,"setContext",{enumerable:!1,value:function(n){u="function"==typeof n?(0,E.pi)((0,E.pi)({},u),n(u)):(0,E.pi)((0,E.pi)({},u),n)}}),Object.defineProperty(o,"getContext",{enumerable:!1,value:function(){return(0,E.pi)({},u)}}),o))||eu.of()},ApolloLink.concat=function(n,r){var i=toLink(n);if(isTerminating(i))return __DEV__&&invariant.warn(new ed("You are calling concat on a terminating link, which will have no effect",i)),i;var o=toLink(r);return new ApolloLink(isTerminating(o)?function(n){return i.request(n,function(n){return o.request(n)||eu.of()})||eu.of()}:function(n,r){return i.request(n,function(n){return o.request(n,r)||eu.of()})||eu.of()})},ApolloLink.prototype.split=function(n,r,i){return this.concat(ApolloLink.split(n,r,i||new ApolloLink(passthrough)))},ApolloLink.prototype.concat=function(n){return ApolloLink.concat(this,n)},ApolloLink.prototype.request=function(n,r){throw __DEV__?new R("request is not implemented"):new R(21)},ApolloLink.prototype.onError=function(n,r){if(r&&r.error)return r.error(n),!1;throw n},ApolloLink.prototype.setOnError=function(n){return this.onError=n,this},ApolloLink}(),em=eh.execute,ey=i(7826),ev=i(5821),eg=i(2380),eb=i(7359);let e_=Object.freeze({});function visitor_visit(n,r,i=eg.h8){let o,a,u;let p=new Map;for(let n of Object.values(eb.h))p.set(n,function(n,r){let i=n[r];return"object"==typeof i?i:"function"==typeof i?{enter:i,leave:void 0}:{enter:n.enter,leave:n.leave}}(r,n));let d=Array.isArray(n),x=[n],w=-1,k=[],E=n,C=[],D=[];do{var I,R,V;let n;w++;let K=w===x.length,W=K&&0!==k.length;if(K){if(a=0===D.length?void 0:C[C.length-1],E=u,u=D.pop(),W){if(d){E=E.slice();let n=0;for(let[r,i]of k){let o=r-n;null===i?(E.splice(o,1),n++):E[o]=i}}else for(let[n,r]of(E=Object.defineProperties({},Object.getOwnPropertyDescriptors(E)),k))E[n]=r}w=o.index,x=o.keys,k=o.edits,d=o.inArray,o=o.prev}else if(u){if(null==(E=u[a=d?w:x[w]]))continue;C.push(a)}if(!Array.isArray(E)){(0,eg.UG)(E)||(0,ey.a)(!1,`Invalid AST Node: ${(0,ev.X)(E)}.`);let i=K?null===(I=p.get(E.kind))||void 0===I?void 0:I.leave:null===(R=p.get(E.kind))||void 0===R?void 0:R.enter;if((n=null==i?void 0:i.call(r,E,a,u,C,D))===e_)break;if(!1===n){if(!K){C.pop();continue}}else if(void 0!==n&&(k.push([a,n]),!K)){if((0,eg.UG)(n))E=n;else{C.pop();continue}}}void 0===n&&W&&k.push([a,E]),K?C.pop():(o={inArray:d,index:w,keys:x,edits:k,prev:o},x=(d=Array.isArray(E))?E:null!==(V=i[E.kind])&&void 0!==V?V:[],w=-1,k=[],u&&D.push(u),u=E)}while(void 0!==o);return 0!==k.length?k[k.length-1][1]:n}function shouldInclude(n,r){var i,o=n.directives;return!o||!o.length||(i=[],o&&o.length&&o.forEach(function(n){var r;if(!("skip"!==(r=n.name.value)&&"include"!==r)){var o=n.arguments,a=n.name.value;__DEV__?invariant(o&&1===o.length,"Incorrect number of arguments for the @".concat(a," directive.")):invariant(o&&1===o.length,40);var u=o[0];__DEV__?invariant(u.name&&"if"===u.name.value,"Invalid argument for the @".concat(a," directive.")):invariant(u.name&&"if"===u.name.value,41);var p=u.value;__DEV__?invariant(p&&("Variable"===p.kind||"BooleanValue"===p.kind),"Argument for the @".concat(a," directive must be a variable or a boolean value.")):invariant(p&&("Variable"===p.kind||"BooleanValue"===p.kind),42),i.push({directive:n,ifArgument:u})}}),i).every(function(n){var i=n.directive,o=n.ifArgument,a=!1;return"Variable"===o.value.kind?(a=r&&r[o.value.name.value],__DEV__?invariant(void 0!==a,"Invalid variable referenced in @".concat(i.name.value," directive.")):invariant(void 0!==a,39)):a=o.value.value,"skip"===i.name.value?!a:a})}function hasDirectives(n,r,i){var o=new Set(n),a=o.size;return visitor_visit(r,{Directive:function(n){if(o.delete(n.name.value)&&(!i||!o.size))return e_}}),i?!o.size:o.size<a}var serializeFetchParameter=function(n,r){var i;try{i=JSON.stringify(n)}catch(n){var o=__DEV__?new R("Network request failed. ".concat(r," is not serializable: ").concat(n.message)):new R(23);throw o.parseError=n,o}return i},ex="function"==typeof WeakMap&&"ReactNative"!==maybe(function(){return navigator.product}),ew="function"==typeof WeakSet,ek="function"==typeof Symbol&&"function"==typeof Symbol.for,eS=ek&&Symbol.asyncIterator;function readerIterator(n){var r={next:function(){return n.read()}};return eS&&(r[Symbol.asyncIterator]=function(){return this}),r}maybe(function(){return window.document.createElement}),maybe(function(){return navigator.userAgent.indexOf("jsdom")>=0});var throwServerError=function(n,r,i){var o=Error(i);throw o.name="ServerError",o.response=n,o.statusCode=n.status,o.result=r,o},eO=Object.prototype.hasOwnProperty;function parseJsonBody(n,r){n.status>=300&&throwServerError(n,function(){try{return JSON.parse(r)}catch(n){return r}}(),"Response not successful: Received status code ".concat(n.status));try{return JSON.parse(r)}catch(i){throw i.name="ServerParseError",i.response=n,i.statusCode=n.status,i.bodyText=r,i}}function handleError(n,r){var i,o;"AbortError"!==n.name&&(n.result&&n.result.errors&&n.result.data&&(null===(i=r.next)||void 0===i||i.call(r,n.result)),null===(o=r.error)||void 0===o||o.call(r,n))}var checkFetcher=function(n){if(!n&&"undefined"==typeof fetch)throw __DEV__?new R("\n\"fetch\" has not been found globally and no fetcher has been configured. To fix this, install a fetch package (like https://www.npmjs.com/package/cross-fetch), instantiate the fetcher, and pass it into your HttpLink constructor. For example:\n\nimport fetch from 'cross-fetch';\nimport { ApolloClient, HttpLink } from '@apollo/client';\nconst client = new ApolloClient({\n  link: new HttpLink({ uri: '/graphql', fetch })\n});\n    "):new R(22)},eE=i(7392);let eC=/[\x00-\x1f\x22\x5c\x7f-\x9f]/g;function escapedReplacer(n){return ej[n.charCodeAt(0)]}let ej=["\\u0000","\\u0001","\\u0002","\\u0003","\\u0004","\\u0005","\\u0006","\\u0007","\\b","\\t","\\n","\\u000B","\\f","\\r","\\u000E","\\u000F","\\u0010","\\u0011","\\u0012","\\u0013","\\u0014","\\u0015","\\u0016","\\u0017","\\u0018","\\u0019","\\u001A","\\u001B","\\u001C","\\u001D","\\u001E","\\u001F","","",'\\"',"","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\\\","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","\\u007F","\\u0080","\\u0081","\\u0082","\\u0083","\\u0084","\\u0085","\\u0086","\\u0087","\\u0088","\\u0089","\\u008A","\\u008B","\\u008C","\\u008D","\\u008E","\\u008F","\\u0090","\\u0091","\\u0092","\\u0093","\\u0094","\\u0095","\\u0096","\\u0097","\\u0098","\\u0099","\\u009A","\\u009B","\\u009C","\\u009D","\\u009E","\\u009F"];function print(n){return visitor_visit(n,eD)}let eD={Name:{leave:n=>n.value},Variable:{leave:n=>"$"+n.name},Document:{leave:n=>join(n.definitions,"\n\n")},OperationDefinition:{leave(n){let r=wrap("(",join(n.variableDefinitions,", "),")"),i=join([n.operation,join([n.name,r]),join(n.directives," ")]," ");return("query"===i?"":i+" ")+n.selectionSet}},VariableDefinition:{leave:({variable:n,type:r,defaultValue:i,directives:o})=>n+": "+r+wrap(" = ",i)+wrap(" ",join(o," "))},SelectionSet:{leave:({selections:n})=>block(n)},Field:{leave({alias:n,name:r,arguments:i,directives:o,selectionSet:a}){let u=wrap("",n,": ")+r,p=u+wrap("(",join(i,", "),")");return p.length>80&&(p=u+wrap("(\n",indent(join(i,"\n")),"\n)")),join([p,join(o," "),a]," ")}},Argument:{leave:({name:n,value:r})=>n+": "+r},FragmentSpread:{leave:({name:n,directives:r})=>"..."+n+wrap(" ",join(r," "))},InlineFragment:{leave:({typeCondition:n,directives:r,selectionSet:i})=>join(["...",wrap("on ",n),join(r," "),i]," ")},FragmentDefinition:{leave:({name:n,typeCondition:r,variableDefinitions:i,directives:o,selectionSet:a})=>`fragment ${n}${wrap("(",join(i,", "),")")} on ${r} ${wrap("",join(o," ")," ")}`+a},IntValue:{leave:({value:n})=>n},FloatValue:{leave:({value:n})=>n},StringValue:{leave:({value:n,block:r})=>r?(0,eE.LZ)(n):`"${n.replace(eC,escapedReplacer)}"`},BooleanValue:{leave:({value:n})=>n?"true":"false"},NullValue:{leave:()=>"null"},EnumValue:{leave:({value:n})=>n},ListValue:{leave:({values:n})=>"["+join(n,", ")+"]"},ObjectValue:{leave:({fields:n})=>"{"+join(n,", ")+"}"},ObjectField:{leave:({name:n,value:r})=>n+": "+r},Directive:{leave:({name:n,arguments:r})=>"@"+n+wrap("(",join(r,", "),")")},NamedType:{leave:({name:n})=>n},ListType:{leave:({type:n})=>"["+n+"]"},NonNullType:{leave:({type:n})=>n+"!"},SchemaDefinition:{leave:({description:n,directives:r,operationTypes:i})=>wrap("",n,"\n")+join(["schema",join(r," "),block(i)]," ")},OperationTypeDefinition:{leave:({operation:n,type:r})=>n+": "+r},ScalarTypeDefinition:{leave:({description:n,name:r,directives:i})=>wrap("",n,"\n")+join(["scalar",r,join(i," ")]," ")},ObjectTypeDefinition:{leave:({description:n,name:r,interfaces:i,directives:o,fields:a})=>wrap("",n,"\n")+join(["type",r,wrap("implements ",join(i," & ")),join(o," "),block(a)]," ")},FieldDefinition:{leave:({description:n,name:r,arguments:i,type:o,directives:a})=>wrap("",n,"\n")+r+(hasMultilineItems(i)?wrap("(\n",indent(join(i,"\n")),"\n)"):wrap("(",join(i,", "),")"))+": "+o+wrap(" ",join(a," "))},InputValueDefinition:{leave:({description:n,name:r,type:i,defaultValue:o,directives:a})=>wrap("",n,"\n")+join([r+": "+i,wrap("= ",o),join(a," ")]," ")},InterfaceTypeDefinition:{leave:({description:n,name:r,interfaces:i,directives:o,fields:a})=>wrap("",n,"\n")+join(["interface",r,wrap("implements ",join(i," & ")),join(o," "),block(a)]," ")},UnionTypeDefinition:{leave:({description:n,name:r,directives:i,types:o})=>wrap("",n,"\n")+join(["union",r,join(i," "),wrap("= ",join(o," | "))]," ")},EnumTypeDefinition:{leave:({description:n,name:r,directives:i,values:o})=>wrap("",n,"\n")+join(["enum",r,join(i," "),block(o)]," ")},EnumValueDefinition:{leave:({description:n,name:r,directives:i})=>wrap("",n,"\n")+join([r,join(i," ")]," ")},InputObjectTypeDefinition:{leave:({description:n,name:r,directives:i,fields:o})=>wrap("",n,"\n")+join(["input",r,join(i," "),block(o)]," ")},DirectiveDefinition:{leave:({description:n,name:r,arguments:i,repeatable:o,locations:a})=>wrap("",n,"\n")+"directive @"+r+(hasMultilineItems(i)?wrap("(\n",indent(join(i,"\n")),"\n)"):wrap("(",join(i,", "),")"))+(o?" repeatable":"")+" on "+join(a," | ")},SchemaExtension:{leave:({directives:n,operationTypes:r})=>join(["extend schema",join(n," "),block(r)]," ")},ScalarTypeExtension:{leave:({name:n,directives:r})=>join(["extend scalar",n,join(r," ")]," ")},ObjectTypeExtension:{leave:({name:n,interfaces:r,directives:i,fields:o})=>join(["extend type",n,wrap("implements ",join(r," & ")),join(i," "),block(o)]," ")},InterfaceTypeExtension:{leave:({name:n,interfaces:r,directives:i,fields:o})=>join(["extend interface",n,wrap("implements ",join(r," & ")),join(i," "),block(o)]," ")},UnionTypeExtension:{leave:({name:n,directives:r,types:i})=>join(["extend union",n,join(r," "),wrap("= ",join(i," | "))]," ")},EnumTypeExtension:{leave:({name:n,directives:r,values:i})=>join(["extend enum",n,join(r," "),block(i)]," ")},InputObjectTypeExtension:{leave:({name:n,directives:r,fields:i})=>join(["extend input",n,join(r," "),block(i)]," ")}};function join(n,r=""){var i;return null!==(i=null==n?void 0:n.filter(n=>n).join(r))&&void 0!==i?i:""}function block(n){return wrap("{\n",indent(join(n,"\n")),"\n}")}function wrap(n,r,i=""){return null!=r&&""!==r?n+r+i:""}function indent(n){return wrap("  ",n.replace(/\n/g,"\n  "))}function hasMultilineItems(n){var r;return null!==(r=null==n?void 0:n.some(n=>n.includes("\n")))&&void 0!==r&&r}var eT={http:{includeQuery:!0,includeExtensions:!1,preserveHeaderCase:!1},headers:{accept:"*/*","content-type":"application/json"},options:{method:"POST"}},defaultPrinter=function(n,r){return r(n)},createSignalIfSupported=function(){if("undefined"==typeof AbortController)return{controller:!1,signal:!1};var n=new AbortController,r=n.signal;return{controller:n,signal:r}};function fromError(n){return new eu(function(r){r.error(n)})}var eI=maybe(function(){return fetch}),createHttpLink=function(n){void 0===n&&(n={});var r=n.uri,i=void 0===r?"/graphql":r,o=n.fetch,a=n.print,u=void 0===a?defaultPrinter:a,p=n.includeExtensions,d=n.preserveHeaderCase,x=n.useGETForQueries,w=n.includeUnusedVariables,k=void 0!==w&&w,C=(0,E._T)(n,["uri","fetch","print","includeExtensions","preserveHeaderCase","useGETForQueries","includeUnusedVariables"]);__DEV__&&checkFetcher(o||eI);var D={http:{includeExtensions:p,preserveHeaderCase:d},options:C.fetchOptions,credentials:C.credentials,headers:C.headers};return new eh(function(n){var r,a,p,d=(r=n,a=i,r.getContext().uri||("function"==typeof a?a(r):a||"/graphql")),w=n.getContext(),C={};if(w.clientAwareness){var I=w.clientAwareness,R=I.name,V=I.version;R&&(C["apollographql-client-name"]=R),V&&(C["apollographql-client-version"]=V)}var K=(0,E.pi)((0,E.pi)({},C),w.headers),W=function(n,r){for(var i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];var a={},u={};i.forEach(function(n){a=(0,E.pi)((0,E.pi)((0,E.pi)({},a),n.options),{headers:(0,E.pi)((0,E.pi)({},a.headers),n.headers)}),n.credentials&&(a.credentials=n.credentials),u=(0,E.pi)((0,E.pi)({},u),n.http)}),a.headers=function(n,r){if(!r){var i=Object.create(null);return Object.keys(Object(n)).forEach(function(r){i[r.toLowerCase()]=n[r]}),i}var o=Object.create(null);Object.keys(Object(n)).forEach(function(r){o[r.toLowerCase()]={originalName:r,value:n[r]}});var a=Object.create(null);return Object.keys(o).forEach(function(n){a[o[n].originalName]=o[n].value}),a}(a.headers,u.preserveHeaderCase);var p=n.operationName,d=n.extensions,x=n.variables,w=n.query,k={operationName:p,variables:x};return u.includeExtensions&&(k.extensions=d),u.includeQuery&&(k.query=r(w,print)),{options:a,body:k}}(n,u,eT,D,{http:w.http,options:w.fetchOptions,credentials:w.credentials,headers:K}),G=W.options,J=W.body;if(J.variables&&!k){var et=new Set(Object.keys(J.variables));visitor_visit(n.query,{Variable:function(n,r,i){i&&"VariableDefinition"!==i.kind&&et.delete(n.name.value)}}),et.size&&(J.variables=(0,E.pi)({},J.variables),et.forEach(function(n){delete J.variables[n]}))}if(!G.signal){var en=createSignalIfSupported(),er=en.controller,ei=en.signal;(p=er)&&(G.signal=ei)}if(x&&!n.query.definitions.some(function(n){return"OperationDefinition"===n.kind&&"mutation"===n.operation})&&(G.method="GET"),hasDirectives(["defer"],n.query)&&(G.headers.accept="multipart/mixed; deferSpec=20220824, application/json"),"GET"===G.method){var eo=function(n,r){var i=[],addQueryParam=function(n,r){i.push("".concat(n,"=").concat(encodeURIComponent(r)))};if("query"in r&&addQueryParam("query",r.query),r.operationName&&addQueryParam("operationName",r.operationName),r.variables){var o=void 0;try{o=serializeFetchParameter(r.variables,"Variables map")}catch(n){return{parseError:n}}addQueryParam("variables",o)}if(r.extensions){var a=void 0;try{a=serializeFetchParameter(r.extensions,"Extensions map")}catch(n){return{parseError:n}}addQueryParam("extensions",a)}var u="",p=n,d=n.indexOf("#");-1!==d&&(u=n.substr(d),p=n.substr(0,d));var x=-1===p.indexOf("?")?"?":"&";return{newURI:p+x+i.join("&")+u}}(d,J),es=eo.newURI,ec=eo.parseError;if(ec)return fromError(ec);d=es}else try{G.body=serializeFetchParameter(J,"Payload")}catch(n){return fromError(n)}return new eu(function(r){return(o||maybe(function(){return fetch})||eI)(d,G).then(function(i){n.setContext({response:i});var o,a=null===(o=i.headers)||void 0===o?void 0:o.get("content-type");return null!==a&&/^multipart\/mixed/i.test(a)?function(n,r){var i,o,a;return(0,E.mG)(this,void 0,void 0,function(){var u,p,d,x,w,k,C,D,I,R,V,K,W,G,J,et,en,er,ei;return(0,E.Jh)(this,function(E){switch(E.label){case 0:if(void 0===TextDecoder)throw Error("TextDecoder must be defined in the environment: please import a polyfill.");u=new TextDecoder("utf-8"),p=null===(i=n.headers)||void 0===i?void 0:i.get("content-type"),d="boundary=",x=(null==p?void 0:p.includes(d))?null==p?void 0:p.substring((null==p?void 0:p.indexOf(d))+d.length).replace(/['"]/g,"").replace(/\;(.*)/gm,"").trim():"-",w="--".concat(x),k="",C=function(n){var r,i,o,a,u,p,d=n;if(n.body&&(d=n.body),r=d,eS&&r[Symbol.asyncIterator])return o=d[Symbol.asyncIterator](),(i={next:function(){return o.next()}})[Symbol.asyncIterator]=function(){return this},i;if(d.getReader)return readerIterator(d.getReader());if(d.stream)return readerIterator(d.stream().getReader());if(d.arrayBuffer)return a=d.arrayBuffer(),u=!1,p={next:function(){return u?Promise.resolve({value:void 0,done:!0}):(u=!0,new Promise(function(n,r){a.then(function(r){n({value:r,done:!1})}).catch(r)}))}},eS&&(p[Symbol.asyncIterator]=function(){return this}),p;if(d.pipe)return function(n){var r=null,i=null,o=!1,a=[],u=[];function onData(n){if(!i){if(u.length){var r=u.shift();if(Array.isArray(r)&&r[0])return r[0]({value:n,done:!1})}a.push(n)}}function onError(n){i=n,u.slice().forEach(function(r){r[1](n)}),r&&r()}function onEnd(){o=!0,u.slice().forEach(function(n){n[0]({value:void 0,done:!0})}),r&&r()}r=function(){r=null,n.removeListener("data",onData),n.removeListener("error",onError),n.removeListener("end",onEnd),n.removeListener("finish",onEnd),n.removeListener("close",onEnd)},n.on("data",onData),n.on("error",onError),n.on("end",onEnd),n.on("finish",onEnd),n.on("close",onEnd);var p={next:function(){return new Promise(function(n,r){return i?r(i):a.length?n({value:a.shift(),done:!1}):o?n({value:void 0,done:!0}):void u.push([n,r])})}};return eS&&(p[Symbol.asyncIterator]=function(){return this}),p}(d);throw Error("Unknown body type for responseIterator. Please pass a streamable response.")}(n),D=!0,E.label=1;case 1:if(!D)return[3,3];return[4,C.next()];case 2:for(R=(I=E.sent()).value,V=I.done,K="string"==typeof R?R:u.decode(R),D=!V,k+=K,W=k.indexOf(w);W>-1;){if(G=void 0,G=(ei=[k.slice(0,W),k.slice(W+w.length)])[0],k=ei[1],G.trim()){if(J=G.indexOf("\r\n\r\n"),(et=function(n){var r={};return n.split("\n").forEach(function(n){var i=n.indexOf(":");if(i>-1){var o=n.slice(0,i).trim().toLowerCase(),a=n.slice(i+1).trim();r[o]=a}}),r}(G.slice(0,J))["content-type"])&&-1===et.toLowerCase().indexOf("application/json"))throw Error("Unsupported patch content type: application/json is required.");en=G.slice(J);try{er=parseJsonBody(n,en.replace("\r\n","")),(Object.keys(er).length>1||"data"in er||"incremental"in er||"errors"in er)&&(null===(o=r.next)||void 0===o||o.call(r,er))}catch(n){handleError(n,r)}}W=k.indexOf(w)}return[3,1];case 3:return null===(a=r.complete)||void 0===a||a.call(r),[2]}})})}(i,r):function(n,r,i){var o;(o=n).text().then(function(n){return parseJsonBody(o,n)}).then(function(n){return o.status>=300&&throwServerError(o,n,"Response not successful: Received status code ".concat(o.status)),Array.isArray(n)||eO.call(n,"data")||eO.call(n,"errors")||throwServerError(o,n,"Server response was missing for query '".concat(Array.isArray(r)?r.map(function(n){return n.operationName}):r.operationName,"'.")),n}).then(function(n){var r,o;null===(r=i.next)||void 0===r||r.call(i,n),null===(o=i.complete)||void 0===o||o.call(i)}).catch(function(n){return handleError(n,i)})}(i,n,r)}).catch(function(n){return handleError(n,r)}),function(){p&&p.abort()}})})},eN=function(n){function HttpLink(r){void 0===r&&(r={});var i=n.call(this,createHttpLink(r).request)||this;return i.options=r,i}return(0,E.ZT)(HttpLink,n),HttpLink}(eh),eA=Object.prototype,eF=eA.toString,eP=eA.hasOwnProperty,eM=Function.prototype.toString,eR=new Map;function equal(n,r){try{return function check(n,r){if(n===r)return!0;var i=eF.call(n);if(i!==eF.call(r))return!1;switch(i){case"[object Array]":if(n.length!==r.length)break;case"[object Object]":if(previouslyCompared(n,r))return!0;var o=definedKeys(n),a=definedKeys(r),u=o.length;if(u!==a.length)break;for(var p=0;p<u;++p)if(!eP.call(r,o[p]))return!1;for(var p=0;p<u;++p){var d=o[p];if(!check(n[d],r[d]))return!1}return!0;case"[object Error]":return n.name===r.name&&n.message===r.message;case"[object Number]":if(n!=n)return r!=r;case"[object Boolean]":case"[object Date]":return+n==+r;case"[object RegExp]":case"[object String]":return n=="".concat(r);case"[object Map]":case"[object Set]":if(n.size!==r.size)break;if(previouslyCompared(n,r))return!0;for(var x=n.entries(),w="[object Map]"===i;;){var k=x.next();if(k.done)break;var E=k.value,C=E[0],D=E[1];if(!r.has(C)||w&&!check(D,r.get(C)))return!1}return!0;case"[object Uint16Array]":case"[object Uint8Array]":case"[object Uint32Array]":case"[object Int32Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object ArrayBuffer]":n=new Uint8Array(n),r=new Uint8Array(r);case"[object DataView]":var I=n.byteLength;if(I===r.byteLength)for(;I--&&n[I]===r[I];);return -1===I;case"[object AsyncFunction]":case"[object GeneratorFunction]":case"[object AsyncGeneratorFunction]":case"[object Function]":var R,V=eM.call(n);if(V!==eM.call(r))break;return!((R=V.length-eL.length)>=0)||V.indexOf(eL,R)!==R}return!1}(n,r)}finally{eR.clear()}}function definedKeys(n){return Object.keys(n).filter(isDefinedKey,n)}function isDefinedKey(n){return void 0!==this[n]}var eL="{ [native code] }";function previouslyCompared(n,r){var i=eR.get(n);if(i){if(i.has(r))return!0}else eR.set(n,i=new Set);return i.add(r),!1}var defaultMakeData=function(){return Object.create(null)},eq=Array.prototype,eV=eq.forEach,eQ=eq.slice,ez=function(){function Trie(n,r){void 0===n&&(n=!0),void 0===r&&(r=defaultMakeData),this.weakness=n,this.makeData=r}return Trie.prototype.lookup=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return this.lookupArray(n)},Trie.prototype.lookupArray=function(n){var r=this;return eV.call(n,function(n){return r=r.getChildTrie(n)}),r.data||(r.data=this.makeData(eQ.call(n)))},Trie.prototype.getChildTrie=function(n){var r=this.weakness&&function(n){switch(typeof n){case"object":if(null===n)break;case"function":return!0}return!1}(n)?this.weak||(this.weak=new WeakMap):this.strong||(this.strong=new Map),i=r.get(n);return i||r.set(n,i=new Trie(this.weakness,this.makeData)),i},Trie}();function compact(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=Object.create(null);return n.forEach(function(n){n&&Object.keys(n).forEach(function(r){var o=n[r];void 0!==o&&(i[r]=o)})}),i}var eB=Object.prototype.hasOwnProperty;function mergeDeep(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return mergeDeepArray(n)}function mergeDeepArray(n){var r=n[0]||{},i=n.length;if(i>1)for(var o=new eU,a=1;a<i;++a)r=o.merge(r,n[a]);return r}var defaultReconciler=function(n,r,i){return this.merge(n[i],r[i])},eU=function(){function DeepMerger(n){void 0===n&&(n=defaultReconciler),this.reconciler=n,this.isObject=isNonNullObject,this.pastCopies=new Set}return DeepMerger.prototype.merge=function(n,r){for(var i=this,o=[],a=2;a<arguments.length;a++)o[a-2]=arguments[a];return isNonNullObject(r)&&isNonNullObject(n)?(Object.keys(r).forEach(function(a){if(eB.call(n,a)){var u=n[a];if(r[a]!==u){var p=i.reconciler.apply(i,(0,E.ev)([n,r,a],o,!1));p!==u&&((n=i.shallowCopyForMerge(n))[a]=p)}}else(n=i.shallowCopyForMerge(n))[a]=r[a]}),n):r},DeepMerger.prototype.shallowCopyForMerge=function(n){return isNonNullObject(n)&&!this.pastCopies.has(n)&&(n=Array.isArray(n)?n.slice(0):(0,E.pi)({__proto__:Object.getPrototypeOf(n)},n),this.pastCopies.add(n)),n},DeepMerger}(),e$=Object.prototype.hasOwnProperty;function isNullish(n){return null==n}var eH=Array.isArray;function defaultDataIdFromObject(n,r){var i=n.__typename,o=n.id,a=n._id;if("string"==typeof i&&(r&&(r.keyObject=isNullish(o)?isNullish(a)?void 0:{_id:a}:{id:o}),isNullish(o)&&!isNullish(a)&&(o=a),!isNullish(o)))return"".concat(i,":").concat("number"==typeof o||"string"==typeof o?o:JSON.stringify(o))}var eK={dataIdFromObject:defaultDataIdFromObject,addTypename:!0,resultCaching:!0,canonizeResults:!1};function shouldCanonizeResults(n){var r=n.canonizeResults;return void 0===r?eK.canonizeResults:r}var eW=/^[_a-z][_0-9a-z]*/i;function fieldNameFromStoreName(n){var r=n.match(eW);return r?r[0]:n}function storeValueIsStoreObject(n){return isNonNullObject(n)&&!isReference(n)&&!eH(n)}function extractFragmentContext(n,r){var i=createFragmentMap(getFragmentDefinitions(n));return{fragmentMap:i,lookupFragment:function(n){var o=i[n];return!o&&r&&(o=r.lookup(n)),o||null}}}var eG=function(){function ObjectCanon(){this.known=new(ew?WeakSet:Set),this.pool=new ez(ex),this.passes=new WeakMap,this.keysByJSON=new Map,this.empty=this.admit({})}return ObjectCanon.prototype.isKnown=function(n){return isNonNullObject(n)&&this.known.has(n)},ObjectCanon.prototype.pass=function(n){if(isNonNullObject(n)){var r=isNonNullObject(n)?eH(n)?n.slice(0):(0,E.pi)({__proto__:Object.getPrototypeOf(n)},n):n;return this.passes.set(r,n),r}return n},ObjectCanon.prototype.admit=function(n){var r=this;if(isNonNullObject(n)){var i=this.passes.get(n);if(i)return i;switch(Object.getPrototypeOf(n)){case Array.prototype:if(this.known.has(n))break;var o=n.map(this.admit,this),a=this.pool.lookupArray(o);return!a.array&&(this.known.add(a.array=o),__DEV__&&Object.freeze(o)),a.array;case null:case Object.prototype:if(this.known.has(n))break;var u=Object.getPrototypeOf(n),p=[u],d=this.sortedKeys(n);p.push(d.json);var x=p.length;d.sorted.forEach(function(i){p.push(r.admit(n[i]))});var a=this.pool.lookupArray(p);if(!a.object){var w=a.object=Object.create(u);this.known.add(w),d.sorted.forEach(function(n,r){w[n]=p[x+r]}),__DEV__&&Object.freeze(w)}return a.object}}return n},ObjectCanon.prototype.sortedKeys=function(n){var r=Object.keys(n),i=this.pool.lookupArray(r);if(!i.keys){r.sort();var o=JSON.stringify(r);(i.keys=this.keysByJSON.get(o))||this.keysByJSON.set(o,i.keys={sorted:r,json:o})}return i.keys},ObjectCanon}(),eY=Object.assign(function(n){if(isNonNullObject(n)){void 0===d&&resetCanonicalStringify();var r=d.admit(n),i=x.get(r);return void 0===i&&x.set(r,i=JSON.stringify(r)),i}return JSON.stringify(n)},{reset:resetCanonicalStringify});function resetCanonicalStringify(){d=new eG,x=new(ex?WeakMap:Map)}function asyncMap(n,r,i){return new eu(function(o){var a=o.next,u=o.error,p=o.complete,d=0,x=!1,w={then:function(n){return new Promise(function(r){return r(n())})}};function makeCallback(n,r){return n?function(r){++d;var both=function(){return n(r)};w=w.then(both,both).then(function(n){--d,a&&a.call(o,n),x&&k.complete()},function(n){throw--d,n}).catch(function(n){u&&u.call(o,n)})}:function(n){return r&&r.call(o,n)}}var k={next:makeCallback(r,a),error:makeCallback(i,u),complete:function(){x=!0,!d&&p&&p.call(o)}},E=n.subscribe(k);return function(){return E.unsubscribe()}})}function graphQLResultHasError(n){return n.errors&&n.errors.length>0||!1}function filterInPlace(n,r,i){var o=0;return n.forEach(function(i,a){r.call(this,i,a,n)&&(n[o++]=i)},i),n.length=o,n}var eZ={kind:"Field",name:{kind:"Name",value:"__typename"}};function nullIfDocIsEmpty(n){var r;return!function isEmpty(n,r){return!n||n.selectionSet.selections.every(function(n){return"FragmentSpread"===n.kind&&isEmpty(r[n.name.value],r)})}(getOperationDefinition(n)||(__DEV__?invariant("Document"===n.kind,'Expecting a parsed GraphQL document. Perhaps you need to wrap the query string in a "gql" tag? http://docs.apollostack.com/apollo-client/core.html#gql'):invariant("Document"===n.kind,50),__DEV__?invariant(n.definitions.length<=1,"Fragment must have exactly one definition."):invariant(n.definitions.length<=1,51),r=n.definitions[0],__DEV__?invariant("FragmentDefinition"===r.kind,"Must be a fragment definition."):invariant("FragmentDefinition"===r.kind,52),r),createFragmentMap(getFragmentDefinitions(n)))?n:null}function getDirectiveMatcher(n){return function(r){return n.some(function(n){return n.name&&n.name===r.name.value||n.test&&n.test(r)})}}function removeDirectivesFromDocument(n,r){var i,o,a,u=Object.create(null),p=[],d=Object.create(null),x=[],w=nullIfDocIsEmpty(visitor_visit(r,{Variable:{enter:function(n,r,i){"VariableDefinition"!==i.kind&&(u[n.name.value]=!0)}},Field:{enter:function(r){if(n&&r.directives&&n.some(function(n){return n.remove})&&r.directives&&r.directives.some(getDirectiveMatcher(n)))return r.arguments&&r.arguments.forEach(function(n){"Variable"===n.value.kind&&p.push({name:n.value.name.value})}),r.selectionSet&&(function getAllFragmentSpreadsFromSelectionSet(n){var r=[];return n.selections.forEach(function(n){(storeUtils_isField(n)||isInlineFragment(n))&&n.selectionSet?getAllFragmentSpreadsFromSelectionSet(n.selectionSet).forEach(function(n){return r.push(n)}):"FragmentSpread"===n.kind&&r.push(n)}),r})(r.selectionSet).forEach(function(n){x.push({name:n.name.value})}),null}},FragmentSpread:{enter:function(n){d[n.name.value]=!0}},Directive:{enter:function(r){if(getDirectiveMatcher(n)(r))return null}}}));return w&&filterInPlace(p,function(n){return!!n.name&&!u[n.name]}).length&&(i=p,o=w,a=function(n){return i.some(function(r){return n.value&&"Variable"===n.value.kind&&n.value.name&&(r.name===n.value.name.value||r.test&&r.test(n))})},w=nullIfDocIsEmpty(visitor_visit(o,{OperationDefinition:{enter:function(n){return(0,E.pi)((0,E.pi)({},n),{variableDefinitions:n.variableDefinitions?n.variableDefinitions.filter(function(n){return!i.some(function(r){return r.name===n.variable.name.value})}):[]})}},Field:{enter:function(n){if(i.some(function(n){return n.remove})){var r=0;if(n.arguments&&n.arguments.forEach(function(n){a(n)&&(r+=1)}),1===r)return null}}},Argument:{enter:function(n){if(a(n))return null}}}))),w&&filterInPlace(x,function(n){return!!n.name&&!d[n.name]}).length&&(w=function(n,r){function enter(r){if(n.some(function(n){return n.name===r.name.value}))return null}return nullIfDocIsEmpty(visitor_visit(r,{FragmentSpread:{enter:enter},FragmentDefinition:{enter:enter}}))}(x,w)),w}var eJ=Object.assign(function(n){return visitor_visit(n,{SelectionSet:{enter:function(n,r,i){if(!i||"OperationDefinition"!==i.kind){var o=n.selections;if(!(!o||o.some(function(n){return storeUtils_isField(n)&&("__typename"===n.name.value||0===n.name.value.lastIndexOf("__",0))}))&&!(storeUtils_isField(i)&&i.directives&&i.directives.some(function(n){return"export"===n.name.value})))return(0,E.pi)((0,E.pi)({},n),{selections:(0,E.ev)((0,E.ev)([],o,!0),[eZ],!1)})}}}})},{added:function(n){return n===eZ}}),eX={test:function(n){var r="connection"===n.name.value;return r&&(!n.arguments||!n.arguments.some(function(n){return"key"===n.name.value}))&&__DEV__&&invariant.warn("Removing an @connection directive even though it does not have a key. You may want to use the key parameter to specify a store key."),r}},e0=new Map;function makeUniqueId(n){var r=e0.get(n)||1;return e0.set(n,r+1),"".concat(n,":").concat(r,":").concat(Math.random().toString(36).slice(2))}function iterateObserversSafely(n,r,i){var o=[];n.forEach(function(n){return n[r]&&o.push(n)}),o.forEach(function(n){return n[r](i)})}function fixObservableSubclass(n){function set(r){Object.defineProperty(n,r,{value:eu})}return ek&&Symbol.species&&set(Symbol.species),set("@@species"),n}function isPromiseLike(n){return n&&"function"==typeof n.then}var e1=function(n){function Concast(r){var i=n.call(this,function(n){return i.addObserver(n),function(){return i.removeObserver(n)}})||this;return i.observers=new Set,i.promise=new Promise(function(n,r){i.resolve=n,i.reject=r}),i.handlers={next:function(n){null!==i.sub&&(i.latest=["next",n],i.notify("next",n),iterateObserversSafely(i.observers,"next",n))},error:function(n){var r=i.sub;null!==r&&(r&&setTimeout(function(){return r.unsubscribe()}),i.sub=null,i.latest=["error",n],i.reject(n),i.notify("error",n),iterateObserversSafely(i.observers,"error",n))},complete:function(){var n=i.sub;if(null!==n){var r=i.sources.shift();r?isPromiseLike(r)?r.then(function(n){return i.sub=n.subscribe(i.handlers)}):i.sub=r.subscribe(i.handlers):(n&&setTimeout(function(){return n.unsubscribe()}),i.sub=null,i.latest&&"next"===i.latest[0]?i.resolve(i.latest[1]):i.resolve(),i.notify("complete"),iterateObserversSafely(i.observers,"complete"))}}},i.nextResultListeners=new Set,i.cancel=function(n){i.reject(n),i.sources=[],i.handlers.complete()},i.promise.catch(function(n){}),"function"==typeof r&&(r=[new eu(r)]),isPromiseLike(r)?r.then(function(n){return i.start(n)},i.handlers.error):i.start(r),i}return(0,E.ZT)(Concast,n),Concast.prototype.start=function(n){void 0===this.sub&&(this.sources=Array.from(n),this.handlers.complete())},Concast.prototype.deliverLastMessage=function(n){if(this.latest){var r=this.latest[0],i=n[r];i&&i.call(n,this.latest[1]),null===this.sub&&"next"===r&&n.complete&&n.complete()}},Concast.prototype.addObserver=function(n){this.observers.has(n)||(this.deliverLastMessage(n),this.observers.add(n))},Concast.prototype.removeObserver=function(n){this.observers.delete(n)&&this.observers.size<1&&this.handlers.complete()},Concast.prototype.notify=function(n,r){var i=this.nextResultListeners;i.size&&(this.nextResultListeners=new Set,i.forEach(function(i){return i(n,r)}))},Concast.prototype.beforeNext=function(n){var r=!1;this.nextResultListeners.add(function(i,o){r||(r=!0,n(i,o))})},Concast}(eu);fixObservableSubclass(e1);var e2=Object.prototype.toString;function cloneDeepHelper(n,r){switch(e2.call(n)){case"[object Array]":if((r=r||new Map).has(n))return r.get(n);var i=n.slice(0);return r.set(n,i),i.forEach(function(n,o){i[o]=cloneDeepHelper(n,r)}),i;case"[object Object]":if((r=r||new Map).has(n))return r.get(n);var o=Object.create(Object.getPrototypeOf(n));return r.set(n,o),Object.keys(n).forEach(function(i){o[i]=cloneDeepHelper(n[i],r)}),o;default:return n}}function isNonEmptyArray(n){return Array.isArray(n)&&n.length>0}var generateErrorMessage=function(n){var r="";return(isNonEmptyArray(n.graphQLErrors)||isNonEmptyArray(n.clientErrors))&&(n.graphQLErrors||[]).concat(n.clientErrors||[]).forEach(function(n){var i=n?n.message:"Error message not found.";r+="".concat(i,"\n")}),n.networkError&&(r+="".concat(n.networkError.message,"\n")),r=r.replace(/\n$/,"")},e4=function(n){function ApolloError(r){var i=r.graphQLErrors,o=r.clientErrors,a=r.networkError,u=r.errorMessage,p=r.extraInfo,d=n.call(this,u)||this;return d.graphQLErrors=i||[],d.clientErrors=o||[],d.networkError=a||null,d.message=u||generateErrorMessage(d),d.extraInfo=p,d.__proto__=ApolloError.prototype,d}return(0,E.ZT)(ApolloError,n),ApolloError}(Error);function isNetworkRequestInFlight(n){return!!n&&n<7}(a=w||(w={}))[a.loading=1]="loading",a[a.setVariables=2]="setVariables",a[a.fetchMore=3]="fetchMore",a[a.refetch=4]="refetch",a[a.poll=6]="poll",a[a.ready=7]="ready",a[a.error=8]="error";var e3=Object.assign,e5=Object.hasOwnProperty,e8=function(n){function ObservableQuery(r){var i=r.queryManager,o=r.queryInfo,a=r.options,u=n.call(this,function(n){try{var r=n._subscription._observer;r&&!r.error&&(r.error=defaultSubscriptionObserverErrorCallback)}catch(n){}var i=!u.observers.size;u.observers.add(n);var o=u.last;return o&&o.error?n.error&&n.error(o.error):o&&o.result&&n.next&&n.next(o.result),i&&u.reobserve().catch(function(){}),function(){u.observers.delete(n)&&!u.observers.size&&u.tearDownQuery()}})||this;u.observers=new Set,u.subscriptions=new Set,u.queryInfo=o,u.queryManager=i,u.isTornDown=!1;var p=i.defaultOptions.watchQuery,d=(void 0===p?{}:p).fetchPolicy,x=void 0===d?"cache-first":d,w=a.fetchPolicy,k=void 0===w?x:w,C=a.initialFetchPolicy,D=void 0===C?"standby"===k?x:k:C;u.options=(0,E.pi)((0,E.pi)({},a),{initialFetchPolicy:D,fetchPolicy:k}),u.queryId=o.queryId||i.generateQueryId();var I=getOperationDefinition(u.query);return u.queryName=I&&I.name&&I.name.value,u}return(0,E.ZT)(ObservableQuery,n),Object.defineProperty(ObservableQuery.prototype,"query",{get:function(){return this.queryManager.transform(this.options.query).document},enumerable:!1,configurable:!0}),Object.defineProperty(ObservableQuery.prototype,"variables",{get:function(){return this.options.variables},enumerable:!1,configurable:!0}),ObservableQuery.prototype.result=function(){var n=this;return new Promise(function(r,i){var o={next:function(i){r(i),n.observers.delete(o),n.observers.size||n.queryManager.removeQuery(n.queryId),setTimeout(function(){a.unsubscribe()},0)},error:i},a=n.subscribe(o)})},ObservableQuery.prototype.getCurrentResult=function(n){void 0===n&&(n=!0);var r=this.getLastResult(!0),i=this.queryInfo.networkStatus||r&&r.networkStatus||w.ready,o=(0,E.pi)((0,E.pi)({},r),{loading:isNetworkRequestInFlight(i),networkStatus:i}),a=this.options.fetchPolicy,u=void 0===a?"cache-first":a;if("network-only"===u||"no-cache"===u||"standby"===u||this.queryManager.transform(this.options.query).hasForcedResolvers);else{var p=this.queryInfo.getDiff();(p.complete||this.options.returnPartialData)&&(o.data=p.result),equal(o.data,{})&&(o.data=void 0),p.complete?(delete o.partial,p.complete&&o.networkStatus===w.loading&&("cache-first"===u||"cache-only"===u)&&(o.networkStatus=w.ready,o.loading=!1)):o.partial=!0,!__DEV__||p.complete||this.options.partialRefetch||o.loading||o.data||o.error||logMissingFieldErrors(p.missing)}return n&&this.updateLastResult(o),o},ObservableQuery.prototype.isDifferentFromLastResult=function(n,r){return!this.last||!equal(this.last.result,n)||r&&!equal(this.last.variables,r)},ObservableQuery.prototype.getLast=function(n,r){var i=this.last;if(i&&i[n]&&(!r||equal(i.variables,this.variables)))return i[n]},ObservableQuery.prototype.getLastResult=function(n){return this.getLast("result",n)},ObservableQuery.prototype.getLastError=function(n){return this.getLast("error",n)},ObservableQuery.prototype.resetLastResults=function(){delete this.last,this.isTornDown=!1},ObservableQuery.prototype.resetQueryStoreErrors=function(){this.queryManager.resetErrors(this.queryId)},ObservableQuery.prototype.refetch=function(n){var r,i={pollInterval:0},o=this.options.fetchPolicy;if("cache-and-network"===o?i.fetchPolicy=o:"no-cache"===o?i.fetchPolicy="no-cache":i.fetchPolicy="network-only",__DEV__&&n&&e5.call(n,"variables")){var a=getQueryDefinition(this.query),u=a.variableDefinitions;(!u||!u.some(function(n){return"variables"===n.variable.name.value}))&&__DEV__&&invariant.warn("Called refetch(".concat(JSON.stringify(n),") for query ").concat((null===(r=a.name)||void 0===r?void 0:r.value)||JSON.stringify(a),", which does not declare a $variables variable.\nDid you mean to call refetch(variables) instead of refetch({ variables })?"))}return n&&!equal(this.options.variables,n)&&(i.variables=this.options.variables=(0,E.pi)((0,E.pi)({},this.options.variables),n)),this.queryInfo.resetLastWrite(),this.reobserve(i,w.refetch)},ObservableQuery.prototype.fetchMore=function(n){var r=this,i=(0,E.pi)((0,E.pi)({},n.query?n:(0,E.pi)((0,E.pi)((0,E.pi)((0,E.pi)({},this.options),{query:this.query}),n),{variables:(0,E.pi)((0,E.pi)({},this.options.variables),n.variables)})),{fetchPolicy:"no-cache"}),o=this.queryManager.generateQueryId(),a=this.queryInfo,u=a.networkStatus;a.networkStatus=w.fetchMore,i.notifyOnNetworkStatusChange&&this.observe();var p=new Set;return this.queryManager.fetchQuery(o,i,w.fetchMore).then(function(d){return r.queryManager.removeQuery(o),a.networkStatus===w.fetchMore&&(a.networkStatus=u),r.queryManager.cache.batch({update:function(o){var a=n.updateQuery;a?o.updateQuery({query:r.query,variables:r.variables,returnPartialData:!0,optimistic:!1},function(n){return a(n,{fetchMoreResult:d.data,variables:i.variables})}):o.writeQuery({query:i.query,variables:i.variables,data:d.data})},onWatchUpdated:function(n){p.add(n.query)}}),d}).finally(function(){p.has(r.query)||reobserveCacheFirst(r)})},ObservableQuery.prototype.subscribeToMore=function(n){var r=this,i=this.queryManager.startGraphQLSubscription({query:n.document,variables:n.variables,context:n.context}).subscribe({next:function(i){var o=n.updateQuery;o&&r.updateQuery(function(n,r){return o(n,{subscriptionData:i,variables:r.variables})})},error:function(r){if(n.onError){n.onError(r);return}__DEV__&&invariant.error("Unhandled GraphQL subscription error",r)}});return this.subscriptions.add(i),function(){r.subscriptions.delete(i)&&i.unsubscribe()}},ObservableQuery.prototype.setOptions=function(n){return this.reobserve(n)},ObservableQuery.prototype.setVariables=function(n){return equal(this.variables,n)?this.observers.size?this.result():Promise.resolve():(this.options.variables=n,this.observers.size)?this.reobserve({fetchPolicy:this.options.initialFetchPolicy,variables:n},w.setVariables):Promise.resolve()},ObservableQuery.prototype.updateQuery=function(n){var r=this.queryManager,i=n(r.cache.diff({query:this.options.query,variables:this.variables,returnPartialData:!0,optimistic:!1}).result,{variables:this.variables});i&&(r.cache.writeQuery({query:this.options.query,data:i,variables:this.variables}),r.broadcastQueries())},ObservableQuery.prototype.startPolling=function(n){this.options.pollInterval=n,this.updatePolling()},ObservableQuery.prototype.stopPolling=function(){this.options.pollInterval=0,this.updatePolling()},ObservableQuery.prototype.applyNextFetchPolicy=function(n,r){if(r.nextFetchPolicy){var i=r.fetchPolicy,o=void 0===i?"cache-first":i,a=r.initialFetchPolicy,u=void 0===a?o:a;"standby"===o||("function"==typeof r.nextFetchPolicy?r.fetchPolicy=r.nextFetchPolicy(o,{reason:n,options:r,observable:this,initialFetchPolicy:u}):"variables-changed"===n?r.fetchPolicy=u:r.fetchPolicy=r.nextFetchPolicy)}return r.fetchPolicy},ObservableQuery.prototype.fetch=function(n,r){return this.queryManager.setObservableQuery(this),this.queryManager.fetchQueryObservable(this.queryId,n,r)},ObservableQuery.prototype.updatePolling=function(){var n=this;if(!this.queryManager.ssrMode){var r=this.pollingInfo,i=this.options.pollInterval;if(!i){r&&(clearTimeout(r.timeout),delete this.pollingInfo);return}if(!r||r.interval!==i){__DEV__?invariant(i,"Attempted to start a polling query without a polling interval."):invariant(i,12),(r||(this.pollingInfo={})).interval=i;var maybeFetch=function(){n.pollingInfo&&(isNetworkRequestInFlight(n.queryInfo.networkStatus)?poll():n.reobserve({fetchPolicy:"no-cache"===n.options.initialFetchPolicy?"no-cache":"network-only"},w.poll).then(poll,poll))},poll=function(){var r=n.pollingInfo;r&&(clearTimeout(r.timeout),r.timeout=setTimeout(maybeFetch,r.interval))};poll()}}},ObservableQuery.prototype.updateLastResult=function(n,r){return void 0===r&&(r=this.variables),this.last=(0,E.pi)((0,E.pi)({},this.last),{result:this.queryManager.assumeImmutableResults?n:cloneDeepHelper(n),variables:r}),isNonEmptyArray(n.errors)||delete this.last.error,this.last},ObservableQuery.prototype.reobserve=function(n,r){var i=this;this.isTornDown=!1;var o=r===w.refetch||r===w.fetchMore||r===w.poll,a=this.options.variables,u=this.options.fetchPolicy,p=compact(this.options,n||{}),d=o?p:e3(this.options,p);!o&&(this.updatePolling(),n&&n.variables&&!equal(n.variables,a)&&"standby"!==d.fetchPolicy&&d.fetchPolicy===u&&(this.applyNextFetchPolicy("variables-changed",d),void 0===r&&(r=w.setVariables)));var x=d.variables&&(0,E.pi)({},d.variables),k=this.fetch(d,r),C={next:function(n){i.reportResult(n,x)},error:function(n){i.reportError(n,x)}};return o||(this.concast&&this.observer&&this.concast.removeObserver(this.observer),this.concast=k,this.observer=C),k.addObserver(C),k.promise},ObservableQuery.prototype.observe=function(){this.reportResult(this.getCurrentResult(!1),this.variables)},ObservableQuery.prototype.reportResult=function(n,r){var i=this.getLastError();(i||this.isDifferentFromLastResult(n,r))&&((i||!n.partial||this.options.returnPartialData)&&this.updateLastResult(n,r),iterateObserversSafely(this.observers,"next",n))},ObservableQuery.prototype.reportError=function(n,r){var i=(0,E.pi)((0,E.pi)({},this.getLastResult()),{error:n,errors:n.graphQLErrors,networkStatus:w.error,loading:!1});this.updateLastResult(i,r),iterateObserversSafely(this.observers,"error",this.last.error=n)},ObservableQuery.prototype.hasObservers=function(){return this.observers.size>0},ObservableQuery.prototype.tearDownQuery=function(){this.isTornDown||(this.concast&&this.observer&&(this.concast.removeObserver(this.observer),delete this.concast,delete this.observer),this.stopPolling(),this.subscriptions.forEach(function(n){return n.unsubscribe()}),this.subscriptions.clear(),this.queryManager.stopQuery(this.queryId),this.observers.clear(),this.isTornDown=!0)},ObservableQuery}(eu);function reobserveCacheFirst(n){var r=n.options,i=r.fetchPolicy,o=r.nextFetchPolicy;return"cache-and-network"===i||"network-only"===i?n.reobserve({fetchPolicy:"cache-first",nextFetchPolicy:function(){return(this.nextFetchPolicy=o,"function"==typeof o)?o.apply(this,arguments):i}}):n.reobserve()}function defaultSubscriptionObserverErrorCallback(n){__DEV__&&invariant.error("Unhandled error",n.message,n.stack)}function logMissingFieldErrors(n){__DEV__&&n&&__DEV__&&invariant.debug("Missing cache result fields: ".concat(JSON.stringify(n)),n)}fixObservableSubclass(e8);var e9=null,e6={},e7=1,tt="@wry/context:Slot",tn=Array,tr=tn[tt]||function(){var n=function(){function Slot(){this.id=["slot",e7++,Date.now(),Math.random().toString(36).slice(2)].join(":")}return Slot.prototype.hasValue=function(){for(var n=e9;n;n=n.parent)if(this.id in n.slots){var r=n.slots[this.id];if(r===e6)break;return n!==e9&&(e9.slots[this.id]=r),!0}return e9&&(e9.slots[this.id]=e6),!1},Slot.prototype.getValue=function(){if(this.hasValue())return e9.slots[this.id]},Slot.prototype.withValue=function(n,r,i,o){var a,u=((a={__proto__:null})[this.id]=n,a),p=e9;e9={parent:p,slots:u};try{return r.apply(o,i)}finally{e9=p}},Slot.bind=function(n){var r=e9;return function(){var i=e9;try{return e9=r,n.apply(this,arguments)}finally{e9=i}}},Slot.noContext=function(n,r,i){if(!e9)return n.apply(i,r);var o=e9;try{return e9=null,n.apply(i,r)}finally{e9=o}},Slot}();try{Object.defineProperty(tn,tt,{value:tn[tt]=n,enumerable:!1,writable:!1,configurable:!1})}finally{return n}}();function defaultDispose(){}tr.bind,tr.noContext;var ti=function(){function Cache(n,r){void 0===n&&(n=1/0),void 0===r&&(r=defaultDispose),this.max=n,this.dispose=r,this.map=new Map,this.newest=null,this.oldest=null}return Cache.prototype.has=function(n){return this.map.has(n)},Cache.prototype.get=function(n){var r=this.getNode(n);return r&&r.value},Cache.prototype.getNode=function(n){var r=this.map.get(n);if(r&&r!==this.newest){var i=r.older,o=r.newer;o&&(o.older=i),i&&(i.newer=o),r.older=this.newest,r.older.newer=r,r.newer=null,this.newest=r,r===this.oldest&&(this.oldest=o)}return r},Cache.prototype.set=function(n,r){var i=this.getNode(n);return i?i.value=r:(i={key:n,value:r,newer:null,older:this.newest},this.newest&&(this.newest.newer=i),this.newest=i,this.oldest=this.oldest||i,this.map.set(n,i),i.value)},Cache.prototype.clean=function(){for(;this.oldest&&this.map.size>this.max;)this.delete(this.oldest.key)},Cache.prototype.delete=function(n){var r=this.map.get(n);return!!r&&(r===this.newest&&(this.newest=r.older),r===this.oldest&&(this.oldest=r.newer),r.newer&&(r.newer.older=r.older),r.older&&(r.older.newer=r.newer),this.map.delete(n),this.dispose(r.value,n),!0)},Cache}(),to=new tr,ta=Object.prototype.hasOwnProperty,ts=void 0===(k=Array.from)?function(n){var r=[];return n.forEach(function(n){return r.push(n)}),r}:k;function maybeUnsubscribe(n){var r=n.unsubscribe;"function"==typeof r&&(n.unsubscribe=void 0,r())}var tc=[];function assert(n,r){if(!n)throw Error(r||"assertion failure")}function valueGet(n){switch(n.length){case 0:throw Error("unknown value");case 1:return n[0];case 2:throw n[1]}}var tl=function(){function Entry(n){this.fn=n,this.parents=new Set,this.childValues=new Map,this.dirtyChildren=null,this.dirty=!0,this.recomputing=!1,this.value=[],this.deps=null,++Entry.count}return Entry.prototype.peek=function(){if(1===this.value.length&&!mightBeDirty(this))return rememberParent(this),this.value[0]},Entry.prototype.recompute=function(n){var r;return assert(!this.recomputing,"already recomputing"),rememberParent(this),mightBeDirty(this)&&(forgetChildren(this),to.withValue(this,recomputeNewValue,[this,n]),function(n,r){if("function"==typeof n.subscribe)try{maybeUnsubscribe(n),n.unsubscribe=n.subscribe.apply(null,r)}catch(r){return n.setDirty(),!1}return!0}(this,n)&&(this.dirty=!1,mightBeDirty(this)||(r=this,eachParent(r,reportCleanChild)))),valueGet(this.value)},Entry.prototype.setDirty=function(){this.dirty||(this.dirty=!0,this.value.length=0,eachParent(this,reportDirtyChild),maybeUnsubscribe(this))},Entry.prototype.dispose=function(){var n=this;this.setDirty(),forgetChildren(this),eachParent(this,function(r,i){r.setDirty(),forgetChild(r,n)})},Entry.prototype.forget=function(){this.dispose()},Entry.prototype.dependOn=function(n){n.add(this),this.deps||(this.deps=tc.pop()||new Set),this.deps.add(n)},Entry.prototype.forgetDeps=function(){var n=this;this.deps&&(ts(this.deps).forEach(function(r){return r.delete(n)}),this.deps.clear(),tc.push(this.deps),this.deps=null)},Entry.count=0,Entry}();function rememberParent(n){var r=to.getValue();if(r)return n.parents.add(r),r.childValues.has(n)||r.childValues.set(n,[]),mightBeDirty(n)?reportDirtyChild(r,n):reportCleanChild(r,n),r}function recomputeNewValue(n,r){n.recomputing=!0,n.value.length=0;try{n.value[0]=n.fn.apply(null,r)}catch(r){n.value[1]=r}n.recomputing=!1}function mightBeDirty(n){return n.dirty||!!(n.dirtyChildren&&n.dirtyChildren.size)}function eachParent(n,r){var i=n.parents.size;if(i)for(var o=ts(n.parents),a=0;a<i;++a)r(o[a],n)}function reportDirtyChild(n,r){assert(n.childValues.has(r)),assert(mightBeDirty(r));var i=!mightBeDirty(n);if(n.dirtyChildren){if(n.dirtyChildren.has(r))return}else n.dirtyChildren=tc.pop()||new Set;n.dirtyChildren.add(r),i&&eachParent(n,reportDirtyChild)}function reportCleanChild(n,r){assert(n.childValues.has(r)),assert(!mightBeDirty(r));var i,o,a=n.childValues.get(r);0===a.length?n.childValues.set(r,r.value.slice(0)):(i=r.value,(o=a.length)>0&&o===i.length&&a[o-1]===i[o-1]||n.setDirty()),removeDirtyChild(n,r),mightBeDirty(n)||eachParent(n,reportCleanChild)}function removeDirtyChild(n,r){var i=n.dirtyChildren;i&&(i.delete(r),0===i.size&&(tc.length<100&&tc.push(i),n.dirtyChildren=null))}function forgetChildren(n){n.childValues.size>0&&n.childValues.forEach(function(r,i){forgetChild(n,i)}),n.forgetDeps(),assert(null===n.dirtyChildren)}function forgetChild(n,r){r.parents.delete(n),n.childValues.delete(r),removeDirtyChild(n,r)}var tu={setDirty:!0,dispose:!0,forget:!0};function dep(n){var r=new Map,i=n&&n.subscribe;function depend(n){var o=to.getValue();if(o){var a=r.get(n);a||r.set(n,a=new Set),o.dependOn(a),"function"==typeof i&&(maybeUnsubscribe(a),a.unsubscribe=i(n))}}return depend.dirty=function(n,i){var o=r.get(n);if(o){var a=i&&ta.call(tu,i)?i:"setDirty";ts(o).forEach(function(n){return n[a]()}),r.delete(n),maybeUnsubscribe(o)}},depend}function makeDefaultMakeCacheKeyFunction(){var n=new ez("function"==typeof WeakMap);return function(){return n.lookupArray(arguments)}}makeDefaultMakeCacheKeyFunction();var tp=new Set;function bundle_esm_wrap(n,r){void 0===r&&(r=Object.create(null));var i=new ti(r.max||65536,function(n){return n.dispose()}),o=r.keyArgs,a=r.makeCacheKey||makeDefaultMakeCacheKeyFunction(),optimistic=function(){var u=a.apply(null,o?o.apply(null,arguments):arguments);if(void 0===u)return n.apply(null,arguments);var p=i.get(u);p||(i.set(u,p=new tl(n)),p.subscribe=r.subscribe,p.forget=function(){return i.delete(u)});var d=p.recompute(Array.prototype.slice.call(arguments));return i.set(u,p),tp.add(i),to.hasValue()||(tp.forEach(function(n){return n.clean()}),tp.clear()),d};function dirtyKey(n){var r=i.get(n);r&&r.setDirty()}function peekKey(n){var r=i.get(n);if(r)return r.peek()}function forgetKey(n){return i.delete(n)}return Object.defineProperty(optimistic,"size",{get:function(){return i.map.size},configurable:!1,enumerable:!1}),optimistic.dirtyKey=dirtyKey,optimistic.dirty=function(){dirtyKey(a.apply(null,arguments))},optimistic.peekKey=peekKey,optimistic.peek=function(){return peekKey(a.apply(null,arguments))},optimistic.forgetKey=forgetKey,optimistic.forget=function(){return forgetKey(a.apply(null,arguments))},optimistic.makeCacheKey=a,optimistic.getKey=o?function(){return a.apply(null,o.apply(null,arguments))}:a,Object.freeze(optimistic)}var tf=null,td={},th=1;function context_esm_maybe(n){try{return n()}catch(n){}}var tm="@wry/context:Slot",ty=context_esm_maybe(function(){return globalThis})||context_esm_maybe(function(){return i.g})||Object.create(null),tv=ty[tm]||Array[tm]||function(n){try{Object.defineProperty(ty,tm,{value:n,enumerable:!1,writable:!1,configurable:!0})}finally{return n}}(function(){function Slot(){this.id=["slot",th++,Date.now(),Math.random().toString(36).slice(2)].join(":")}return Slot.prototype.hasValue=function(){for(var n=tf;n;n=n.parent)if(this.id in n.slots){var r=n.slots[this.id];if(r===td)break;return n!==tf&&(tf.slots[this.id]=r),!0}return tf&&(tf.slots[this.id]=td),!1},Slot.prototype.getValue=function(){if(this.hasValue())return tf.slots[this.id]},Slot.prototype.withValue=function(n,r,i,o){var a,u=((a={__proto__:null})[this.id]=n,a),p=tf;tf={parent:p,slots:u};try{return r.apply(o,i)}finally{tf=p}},Slot.bind=function(n){var r=tf;return function(){var i=tf;try{return tf=r,n.apply(this,arguments)}finally{tf=i}}},Slot.noContext=function(n,r,i){if(!tf)return n.apply(i,r);var o=tf;try{return tf=null,n.apply(i,r)}finally{tf=o}},Slot}());tv.bind,tv.noContext;var tg=new tv,tb=new WeakMap;function getCacheInfo(n){var r=tb.get(n);return r||tb.set(n,r={vars:new Set,dep:dep()}),r}function forgetCache(n){getCacheInfo(n).vars.forEach(function(r){return r.forgetCache(n)})}function makeVar(n){var r=new Set,i=new Set,rv=function(a){if(arguments.length>0){if(n!==a){n=a,r.forEach(function(n){getCacheInfo(n).dep.dirty(rv),n.broadcastWatches&&n.broadcastWatches()});var u=Array.from(i);i.clear(),u.forEach(function(r){return r(n)})}}else{var p=tg.getValue();p&&(o(p),getCacheInfo(p).dep(rv))}return n};rv.onNextChange=function(n){return i.add(n),function(){i.delete(n)}};var o=rv.attachCache=function(n){return r.add(n),getCacheInfo(n).vars.add(rv),rv};return rv.forgetCache=function(n){return r.delete(n)},rv}var t_=function(){function LocalState(n){var r=n.cache,i=n.client,o=n.resolvers,a=n.fragmentMatcher;this.cache=r,i&&(this.client=i),o&&this.addResolvers(o),a&&this.setFragmentMatcher(a)}return LocalState.prototype.addResolvers=function(n){var r=this;this.resolvers=this.resolvers||{},Array.isArray(n)?n.forEach(function(n){r.resolvers=mergeDeep(r.resolvers,n)}):this.resolvers=mergeDeep(this.resolvers,n)},LocalState.prototype.setResolvers=function(n){this.resolvers={},this.addResolvers(n)},LocalState.prototype.getResolvers=function(){return this.resolvers||{}},LocalState.prototype.runResolvers=function(n){var r=n.document,i=n.remoteResult,o=n.context,a=n.variables,u=n.onlyRunForcedResolvers,p=void 0!==u&&u;return(0,E.mG)(this,void 0,void 0,function(){return(0,E.Jh)(this,function(n){return r?[2,this.resolveDocument(r,i.data,o,a,this.fragmentMatcher,p).then(function(n){return(0,E.pi)((0,E.pi)({},i),{data:n.result})})]:[2,i]})})},LocalState.prototype.setFragmentMatcher=function(n){this.fragmentMatcher=n},LocalState.prototype.getFragmentMatcher=function(){return this.fragmentMatcher},LocalState.prototype.clientQuery=function(n){return hasDirectives(["client"],n)&&this.resolvers?n:null},LocalState.prototype.serverQuery=function(n){var r;return checkDocument(n),(r=removeDirectivesFromDocument([{test:function(n){return"client"===n.name.value},remove:!0}],n))&&(r=visitor_visit(r,{FragmentDefinition:{enter:function(n){if(n.selectionSet&&n.selectionSet.selections.every(function(n){return storeUtils_isField(n)&&"__typename"===n.name.value}))return null}}})),r},LocalState.prototype.prepareContext=function(n){var r=this.cache;return(0,E.pi)((0,E.pi)({},n),{cache:r,getCacheKey:function(n){return r.identify(n)}})},LocalState.prototype.addExportedVariables=function(n,r,i){return void 0===r&&(r={}),void 0===i&&(i={}),(0,E.mG)(this,void 0,void 0,function(){return(0,E.Jh)(this,function(o){return n?[2,this.resolveDocument(n,this.buildRootValueFromCache(n,r)||{},this.prepareContext(i),r).then(function(n){return(0,E.pi)((0,E.pi)({},r),n.exportedVariables)})]:[2,(0,E.pi)({},r)]})})},LocalState.prototype.shouldForceResolvers=function(n){var r=!1;return visitor_visit(n,{Directive:{enter:function(n){if("client"===n.name.value&&n.arguments&&(r=n.arguments.some(function(n){return"always"===n.name.value&&"BooleanValue"===n.value.kind&&!0===n.value.value})))return e_}}}),r},LocalState.prototype.buildRootValueFromCache=function(n,r){return this.cache.diff({query:"query"===getMainDefinition(n).operation?n:visitor_visit(n,{OperationDefinition:{enter:function(n){return(0,E.pi)((0,E.pi)({},n),{operation:"query"})}}}),variables:r,returnPartialData:!0,optimistic:!1}).result},LocalState.prototype.resolveDocument=function(n,r,i,o,a,u){return void 0===i&&(i={}),void 0===o&&(o={}),void 0===a&&(a=function(){return!0}),void 0===u&&(u=!1),(0,E.mG)(this,void 0,void 0,function(){var p,d,x,w,k,C,D,I;return(0,E.Jh)(this,function(R){return p=getMainDefinition(n),d=createFragmentMap(getFragmentDefinitions(n)),w=(x=p.operation)?x.charAt(0).toUpperCase()+x.slice(1):"Query",k=this,C=k.cache,D=k.client,I={fragmentMap:d,context:(0,E.pi)((0,E.pi)({},i),{cache:C,client:D}),variables:o,fragmentMatcher:a,defaultOperationType:w,exportedVariables:{},onlyRunForcedResolvers:u},[2,this.resolveSelectionSet(p.selectionSet,r,I).then(function(n){return{result:n,exportedVariables:I.exportedVariables}})]})})},LocalState.prototype.resolveSelectionSet=function(n,r,i){return(0,E.mG)(this,void 0,void 0,function(){var o,a,u,p,d,x=this;return(0,E.Jh)(this,function(w){return o=i.fragmentMap,a=i.context,u=i.variables,p=[r],d=function(n){return(0,E.mG)(x,void 0,void 0,function(){var d,x;return(0,E.Jh)(this,function(w){return shouldInclude(n,u)?storeUtils_isField(n)?[2,this.resolveField(n,r,i).then(function(r){var i;void 0!==r&&p.push(((i={})[resultKeyNameFromField(n)]=r,i))})]:(isInlineFragment(n)?d=n:(d=o[n.name.value],__DEV__?invariant(d,"No fragment named ".concat(n.name.value)):invariant(d,11)),d&&d.typeCondition&&(x=d.typeCondition.name.value,i.fragmentMatcher(r,x,a)))?[2,this.resolveSelectionSet(d.selectionSet,r,i).then(function(n){p.push(n)})]:[2]:[2]})})},[2,Promise.all(n.selections.map(d)).then(function(){return mergeDeepArray(p)})]})})},LocalState.prototype.resolveField=function(n,r,i){return(0,E.mG)(this,void 0,void 0,function(){var o,a,u,p,d,x,w,k,C,D=this;return(0,E.Jh)(this,function(E){return o=i.variables,p=(a=n.name.value)!==(u=resultKeyNameFromField(n)),x=Promise.resolve(d=r[u]||r[a]),(!i.onlyRunForcedResolvers||this.shouldForceResolvers(n))&&(w=r.__typename||i.defaultOperationType,(k=this.resolvers&&this.resolvers[w])&&(C=k[p?a:u])&&(x=Promise.resolve(tg.withValue(this.cache,C,[r,argumentsObjectFromField(n,o),i.context,{field:n,fragmentMap:i.fragmentMap}])))),[2,x.then(function(r){return(void 0===r&&(r=d),n.directives&&n.directives.forEach(function(n){"export"===n.name.value&&n.arguments&&n.arguments.forEach(function(n){"as"===n.name.value&&"StringValue"===n.value.kind&&(i.exportedVariables[n.value.value]=r)})}),n.selectionSet&&null!=r)?Array.isArray(r)?D.resolveSubSelectedArray(n,r,i):n.selectionSet?D.resolveSelectionSet(n.selectionSet,r,i):void 0:r})]})})},LocalState.prototype.resolveSubSelectedArray=function(n,r,i){var o=this;return Promise.all(r.map(function(r){return null===r?null:Array.isArray(r)?o.resolveSubSelectedArray(n,r,i):n.selectionSet?o.resolveSelectionSet(n.selectionSet,r,i):void 0}))},LocalState}(),tx=new(ex?WeakMap:Map);function wrapDestructiveCacheMethod(n,r){var i=n[r];"function"==typeof i&&(n[r]=function(){return tx.set(n,(tx.get(n)+1)%1e15),i.apply(this,arguments)})}function cancelNotifyTimeout(n){n.notifyTimeout&&(clearTimeout(n.notifyTimeout),n.notifyTimeout=void 0)}var tw=function(){function QueryInfo(n,r){void 0===r&&(r=n.generateQueryId()),this.queryId=r,this.listeners=new Set,this.document=null,this.lastRequestId=1,this.subscriptions=new Set,this.stopped=!1,this.dirty=!1,this.observableQuery=null;var i=this.cache=n.cache;tx.has(i)||(tx.set(i,0),wrapDestructiveCacheMethod(i,"evict"),wrapDestructiveCacheMethod(i,"modify"),wrapDestructiveCacheMethod(i,"reset"))}return QueryInfo.prototype.init=function(n){var r=n.networkStatus||w.loading;return this.variables&&this.networkStatus!==w.loading&&!equal(this.variables,n.variables)&&(r=w.setVariables),equal(n.variables,this.variables)||(this.lastDiff=void 0),Object.assign(this,{document:n.document,variables:n.variables,networkError:null,graphQLErrors:this.graphQLErrors||[],networkStatus:r}),n.observableQuery&&this.setObservableQuery(n.observableQuery),n.lastRequestId&&(this.lastRequestId=n.lastRequestId),this},QueryInfo.prototype.reset=function(){cancelNotifyTimeout(this),this.dirty=!1},QueryInfo.prototype.getDiff=function(n){void 0===n&&(n=this.variables);var r=this.getDiffOptions(n);if(this.lastDiff&&equal(r,this.lastDiff.options))return this.lastDiff.diff;this.updateWatch(this.variables=n);var i=this.observableQuery;if(i&&"no-cache"===i.options.fetchPolicy)return{complete:!1};var o=this.cache.diff(r);return this.updateLastDiff(o,r),o},QueryInfo.prototype.updateLastDiff=function(n,r){this.lastDiff=n?{diff:n,options:r||this.getDiffOptions()}:void 0},QueryInfo.prototype.getDiffOptions=function(n){var r;return void 0===n&&(n=this.variables),{query:this.document,variables:n,returnPartialData:!0,optimistic:!0,canonizeResults:null===(r=this.observableQuery)||void 0===r?void 0:r.options.canonizeResults}},QueryInfo.prototype.setDiff=function(n){var r=this,i=this.lastDiff&&this.lastDiff.diff;this.updateLastDiff(n),this.dirty||equal(i&&i.result,n&&n.result)||(this.dirty=!0,this.notifyTimeout||(this.notifyTimeout=setTimeout(function(){return r.notify()},0)))},QueryInfo.prototype.setObservableQuery=function(n){var r=this;n!==this.observableQuery&&(this.oqListener&&this.listeners.delete(this.oqListener),this.observableQuery=n,n?(n.queryInfo=this,this.listeners.add(this.oqListener=function(){r.getDiff().fromOptimisticTransaction?n.observe():reobserveCacheFirst(n)})):delete this.oqListener)},QueryInfo.prototype.notify=function(){var n=this;cancelNotifyTimeout(this),this.shouldNotify()&&this.listeners.forEach(function(r){return r(n)}),this.dirty=!1},QueryInfo.prototype.shouldNotify=function(){if(!this.dirty||!this.listeners.size)return!1;if(isNetworkRequestInFlight(this.networkStatus)&&this.observableQuery){var n=this.observableQuery.options.fetchPolicy;if("cache-only"!==n&&"cache-and-network"!==n)return!1}return!0},QueryInfo.prototype.stop=function(){if(!this.stopped){this.stopped=!0,this.reset(),this.cancel(),this.cancel=QueryInfo.prototype.cancel,this.subscriptions.forEach(function(n){return n.unsubscribe()});var n=this.observableQuery;n&&n.stopPolling()}},QueryInfo.prototype.cancel=function(){},QueryInfo.prototype.updateWatch=function(n){var r=this;void 0===n&&(n=this.variables);var i=this.observableQuery;if(!i||"no-cache"!==i.options.fetchPolicy){var o=(0,E.pi)((0,E.pi)({},this.getDiffOptions(n)),{watcher:this,callback:function(n){return r.setDiff(n)}});this.lastWatch&&equal(o,this.lastWatch)||(this.cancel(),this.cancel=this.cache.watch(this.lastWatch=o))}},QueryInfo.prototype.resetLastWrite=function(){this.lastWrite=void 0},QueryInfo.prototype.shouldWrite=function(n,r){var i=this.lastWrite;return!(i&&i.dmCount===tx.get(this.cache)&&equal(r,i.variables)&&equal(n.data,i.result.data))},QueryInfo.prototype.markResult=function(n,r,i,o){var a=this,u=isNonEmptyArray(n.errors)?n.errors.slice(0):[];if(this.reset(),"incremental"in n&&isNonEmptyArray(n.incremental)){var p=this.getDiff().result,d=new eU;n.incremental.forEach(function(n){for(var r=n.data,i=n.path,o=n.errors,a=i.length-1;a>=0;--a){var x=i[a],w=isNaN(+x)?{}:[];w[x]=r,r=w}o&&u.push.apply(u,o),p=d.merge(p,r)}),n.data=p}this.graphQLErrors=u,"no-cache"===i.fetchPolicy?this.updateLastDiff({result:n.data,complete:!0},this.getDiffOptions(i.variables)):0!==o&&(shouldWriteResult(n,i.errorPolicy)?this.cache.performTransaction(function(u){if(a.shouldWrite(n,i.variables))u.writeQuery({query:r,data:n.data,variables:i.variables,overwrite:1===o}),a.lastWrite={result:n,variables:i.variables,dmCount:tx.get(a.cache)};else if(a.lastDiff&&a.lastDiff.diff.complete){n.data=a.lastDiff.diff.result;return}var p=a.getDiffOptions(i.variables),d=u.diff(p);a.stopped||a.updateWatch(i.variables),a.updateLastDiff(d,p),d.complete&&(n.data=d.result)}):this.lastWrite=void 0)},QueryInfo.prototype.markReady=function(){return this.networkError=null,this.networkStatus=w.ready},QueryInfo.prototype.markError=function(n){return this.networkStatus=w.error,this.lastWrite=void 0,this.reset(),n.graphQLErrors&&(this.graphQLErrors=n.graphQLErrors),n.networkError&&(this.networkError=n.networkError),n},QueryInfo}();function shouldWriteResult(n,r){void 0===r&&(r="none");var i="ignore"===r||"all"===r,o=!graphQLResultHasError(n);return!o&&i&&n.data&&(o=!0),o}var tk=Object.prototype.hasOwnProperty,tS=function(){function QueryManager(n){var r=n.cache,i=n.link,o=n.defaultOptions,a=n.queryDeduplication,u=n.onBroadcast,p=n.ssrMode,d=n.clientAwareness,x=n.localState,w=n.assumeImmutableResults;this.clientAwareness={},this.queries=new Map,this.fetchCancelFns=new Map,this.transformCache=new(ex?WeakMap:Map),this.queryIdCounter=1,this.requestIdCounter=1,this.mutationIdCounter=1,this.inFlightLinkObservables=new Map,this.cache=r,this.link=i,this.defaultOptions=o||Object.create(null),this.queryDeduplication=void 0!==a&&a,this.clientAwareness=void 0===d?{}:d,this.localState=x||new t_({cache:r}),this.ssrMode=void 0!==p&&p,this.assumeImmutableResults=!!w,(this.onBroadcast=u)&&(this.mutationStore=Object.create(null))}return QueryManager.prototype.stop=function(){var n=this;this.queries.forEach(function(r,i){n.stopQueryNoBroadcast(i)}),this.cancelPendingFetches(__DEV__?new R("QueryManager stopped while query was in flight"):new R(13))},QueryManager.prototype.cancelPendingFetches=function(n){this.fetchCancelFns.forEach(function(r){return r(n)}),this.fetchCancelFns.clear()},QueryManager.prototype.mutate=function(n){var r,i,o=n.mutation,a=n.variables,u=n.optimisticResponse,p=n.updateQueries,d=n.refetchQueries,x=void 0===d?[]:d,w=n.awaitRefetchQueries,k=void 0!==w&&w,C=n.update,D=n.onQueryUpdated,I=n.fetchPolicy,R=void 0===I?(null===(r=this.defaultOptions.mutate)||void 0===r?void 0:r.fetchPolicy)||"network-only":I,V=n.errorPolicy,K=void 0===V?(null===(i=this.defaultOptions.mutate)||void 0===i?void 0:i.errorPolicy)||"none":V,W=n.keepRootFields,G=n.context;return(0,E.mG)(this,void 0,void 0,function(){var n,r,i,d,w,I;return(0,E.Jh)(this,function(V){switch(V.label){case 0:if(__DEV__?invariant(o,"mutation option is required. You must specify your GraphQL document in the mutation option."):invariant(o,14),__DEV__?invariant("network-only"===R||"no-cache"===R,"Mutations support only 'network-only' or 'no-cache' fetchPolicy strings. The default `network-only` behavior automatically writes mutation results to the cache. Passing `no-cache` skips the cache write."):invariant("network-only"===R||"no-cache"===R,15),n=this.generateMutationId(),i=(r=this.transform(o)).document,d=r.hasClientExports,o=this.cache.transformForLink(i),a=this.getVariables(o,a),!d)return[3,2];return[4,this.localState.addExportedVariables(o,a,G)];case 1:a=V.sent(),V.label=2;case 2:return w=this.mutationStore&&(this.mutationStore[n]={mutation:o,variables:a,loading:!0,error:null}),u&&this.markMutationOptimistic(u,{mutationId:n,document:o,variables:a,fetchPolicy:R,errorPolicy:K,context:G,updateQueries:p,update:C,keepRootFields:W}),this.broadcastQueries(),I=this,[2,new Promise(function(r,i){return asyncMap(I.getObservableFromLink(o,(0,E.pi)((0,E.pi)({},G),{optimisticResponse:u}),a,!1),function(r){if(graphQLResultHasError(r)&&"none"===K)throw new e4({graphQLErrors:r.errors});w&&(w.loading=!1,w.error=null);var i=(0,E.pi)({},r);return"function"==typeof x&&(x=x(i)),"ignore"===K&&graphQLResultHasError(i)&&delete i.errors,I.markMutationResult({mutationId:n,result:i,document:o,variables:a,fetchPolicy:R,errorPolicy:K,context:G,update:C,updateQueries:p,awaitRefetchQueries:k,refetchQueries:x,removeOptimistic:u?n:void 0,onQueryUpdated:D,keepRootFields:W})}).subscribe({next:function(n){I.broadcastQueries(),r(n)},error:function(r){w&&(w.loading=!1,w.error=r),u&&I.cache.removeOptimistic(n),I.broadcastQueries(),i(r instanceof e4?r:new e4({networkError:r}))}})})]}})})},QueryManager.prototype.markMutationResult=function(n,r){var i=this;void 0===r&&(r=this.cache);var o=n.result,a=[],u="no-cache"===n.fetchPolicy;if(!u&&shouldWriteResult(o,n.errorPolicy)){a.push({result:o.data,dataId:"ROOT_MUTATION",query:n.document,variables:n.variables});var p=n.updateQueries;p&&this.queries.forEach(function(n,u){var d=n.observableQuery,x=d&&d.queryName;if(x&&tk.call(p,x)){var w=p[x],k=i.queries.get(u),E=k.document,C=k.variables,D=r.diff({query:E,variables:C,returnPartialData:!0,optimistic:!1}),I=D.result;if(D.complete&&I){var R=w(I,{mutationResult:o,queryName:E&&getOperationName(E)||void 0,queryVariables:C});R&&a.push({result:R,dataId:"ROOT_QUERY",query:E,variables:C})}}})}if(a.length>0||n.refetchQueries||n.update||n.onQueryUpdated||n.removeOptimistic){var d=[];if(this.refetchQueries({updateCache:function(r){u||a.forEach(function(n){return r.write(n)});var p=n.update;if(p){if(!u){var d=r.diff({id:"ROOT_MUTATION",query:i.transform(n.document).asQuery,variables:n.variables,optimistic:!1,returnPartialData:!0});d.complete&&!o.incremental&&(o=(0,E.pi)((0,E.pi)({},o),{data:d.result}))}p(r,o,{context:n.context,variables:n.variables})}u||n.keepRootFields||r.modify({id:"ROOT_MUTATION",fields:function(n,r){var i=r.fieldName,o=r.DELETE;return"__typename"===i?n:o}})},include:n.refetchQueries,optimistic:!1,removeOptimistic:n.removeOptimistic,onQueryUpdated:n.onQueryUpdated||null}).forEach(function(n){return d.push(n)}),n.awaitRefetchQueries||n.onQueryUpdated)return Promise.all(d).then(function(){return o})}return Promise.resolve(o)},QueryManager.prototype.markMutationOptimistic=function(n,r){var i=this,o="function"==typeof n?n(r.variables):n;return this.cache.recordOptimisticTransaction(function(n){try{i.markMutationResult((0,E.pi)((0,E.pi)({},r),{result:{data:o}}),n)}catch(n){__DEV__&&invariant.error(n)}},r.mutationId)},QueryManager.prototype.fetchQuery=function(n,r,i){return this.fetchQueryObservable(n,r,i).promise},QueryManager.prototype.getQueryStore=function(){var n=Object.create(null);return this.queries.forEach(function(r,i){n[i]={variables:r.variables,networkStatus:r.networkStatus,networkError:r.networkError,graphQLErrors:r.graphQLErrors}}),n},QueryManager.prototype.resetErrors=function(n){var r=this.queries.get(n);r&&(r.networkError=void 0,r.graphQLErrors=[])},QueryManager.prototype.transform=function(n){var r=this.transformCache;if(!r.has(n)){var i=this.cache.transformDocument(n),o=removeDirectivesFromDocument([eX],checkDocument(i)),a=this.localState.clientQuery(i),u=o&&this.localState.serverQuery(o),p={document:i,hasClientExports:i&&hasDirectives(["client","export"],i,!0),hasForcedResolvers:this.localState.shouldForceResolvers(i),clientQuery:a,serverQuery:u,defaultVars:getDefaultValues(getOperationDefinition(i)),asQuery:(0,E.pi)((0,E.pi)({},i),{definitions:i.definitions.map(function(n){return"OperationDefinition"===n.kind&&"query"!==n.operation?(0,E.pi)((0,E.pi)({},n),{operation:"query"}):n})})},add=function(n){n&&!r.has(n)&&r.set(n,p)};add(n),add(i),add(a),add(u)}return r.get(n)},QueryManager.prototype.getVariables=function(n,r){return(0,E.pi)((0,E.pi)({},this.transform(n).defaultVars),r)},QueryManager.prototype.watchQuery=function(n){void 0===(n=(0,E.pi)((0,E.pi)({},n),{variables:this.getVariables(n.query,n.variables)})).notifyOnNetworkStatusChange&&(n.notifyOnNetworkStatusChange=!1);var r=new tw(this),i=new e8({queryManager:this,queryInfo:r,options:n});return this.queries.set(i.queryId,r),r.init({document:i.query,observableQuery:i,variables:i.variables}),i},QueryManager.prototype.query=function(n,r){var i=this;return void 0===r&&(r=this.generateQueryId()),__DEV__?invariant(n.query,"query option is required. You must specify your GraphQL document in the query option."):invariant(n.query,16),__DEV__?invariant("Document"===n.query.kind,'You must wrap the query string in a "gql" tag.'):invariant("Document"===n.query.kind,17),__DEV__?invariant(!n.returnPartialData,"returnPartialData option only supported on watchQuery."):invariant(!n.returnPartialData,18),__DEV__?invariant(!n.pollInterval,"pollInterval option only supported on watchQuery."):invariant(!n.pollInterval,19),this.fetchQuery(r,n).finally(function(){return i.stopQuery(r)})},QueryManager.prototype.generateQueryId=function(){return String(this.queryIdCounter++)},QueryManager.prototype.generateRequestId=function(){return this.requestIdCounter++},QueryManager.prototype.generateMutationId=function(){return String(this.mutationIdCounter++)},QueryManager.prototype.stopQueryInStore=function(n){this.stopQueryInStoreNoBroadcast(n),this.broadcastQueries()},QueryManager.prototype.stopQueryInStoreNoBroadcast=function(n){var r=this.queries.get(n);r&&r.stop()},QueryManager.prototype.clearStore=function(n){return void 0===n&&(n={discardWatches:!0}),this.cancelPendingFetches(__DEV__?new R("Store reset while query was in flight (not completed in link chain)"):new R(20)),this.queries.forEach(function(n){n.observableQuery?n.networkStatus=w.loading:n.stop()}),this.mutationStore&&(this.mutationStore=Object.create(null)),this.cache.reset(n)},QueryManager.prototype.getObservableQueries=function(n){var r=this;void 0===n&&(n="active");var i=new Map,o=new Map,a=new Set;return Array.isArray(n)&&n.forEach(function(n){"string"==typeof n?o.set(n,!1):isNonNullObject(n)&&"Document"===n.kind&&Array.isArray(n.definitions)?o.set(r.transform(n).document,!1):isNonNullObject(n)&&n.query&&a.add(n)}),this.queries.forEach(function(r,a){var u=r.observableQuery,p=r.document;if(u){if("all"===n){i.set(a,u);return}var d=u.queryName;if("standby"===u.options.fetchPolicy||"active"===n&&!u.hasObservers())return;("active"===n||d&&o.has(d)||p&&o.has(p))&&(i.set(a,u),d&&o.set(d,!0),p&&o.set(p,!0))}}),a.size&&a.forEach(function(n){var o=makeUniqueId("legacyOneTimeQuery"),a=r.getQuery(o).init({document:n.query,variables:n.variables}),u=new e8({queryManager:r,queryInfo:a,options:(0,E.pi)((0,E.pi)({},n),{fetchPolicy:"network-only"})});invariant(u.queryId===o),a.setObservableQuery(u),i.set(o,u)}),__DEV__&&o.size&&o.forEach(function(n,r){!n&&__DEV__&&invariant.warn("Unknown query ".concat("string"==typeof r?"named ":"").concat(JSON.stringify(r,null,2)," requested in refetchQueries options.include array"))}),i},QueryManager.prototype.reFetchObservableQueries=function(n){var r=this;void 0===n&&(n=!1);var i=[];return this.getObservableQueries(n?"all":"active").forEach(function(o,a){var u=o.options.fetchPolicy;o.resetLastResults(),(n||"standby"!==u&&"cache-only"!==u)&&i.push(o.refetch()),r.getQuery(a).setDiff(null)}),this.broadcastQueries(),Promise.all(i)},QueryManager.prototype.setObservableQuery=function(n){this.getQuery(n.queryId).setObservableQuery(n)},QueryManager.prototype.startGraphQLSubscription=function(n){var r=this,i=n.query,o=n.fetchPolicy,a=n.errorPolicy,u=n.variables,p=n.context,d=void 0===p?{}:p;i=this.transform(i).document,u=this.getVariables(i,u);var makeObservable=function(n){return r.getObservableFromLink(i,d,n).map(function(u){if("no-cache"!==o&&(shouldWriteResult(u,a)&&r.cache.write({query:i,result:u.data,dataId:"ROOT_SUBSCRIPTION",variables:n}),r.broadcastQueries()),graphQLResultHasError(u))throw new e4({graphQLErrors:u.errors});return u})};if(this.transform(i).hasClientExports){var x=this.localState.addExportedVariables(i,u,d).then(makeObservable);return new eu(function(n){var r=null;return x.then(function(i){return r=i.subscribe(n)},n.error),function(){return r&&r.unsubscribe()}})}return makeObservable(u)},QueryManager.prototype.stopQuery=function(n){this.stopQueryNoBroadcast(n),this.broadcastQueries()},QueryManager.prototype.stopQueryNoBroadcast=function(n){this.stopQueryInStoreNoBroadcast(n),this.removeQuery(n)},QueryManager.prototype.removeQuery=function(n){this.fetchCancelFns.delete(n),this.queries.has(n)&&(this.getQuery(n).stop(),this.queries.delete(n))},QueryManager.prototype.broadcastQueries=function(){this.onBroadcast&&this.onBroadcast(),this.queries.forEach(function(n){return n.notify()})},QueryManager.prototype.getLocalState=function(){return this.localState},QueryManager.prototype.getObservableFromLink=function(n,r,i,o){var a,u,p=this;void 0===o&&(o=null!==(a=null==r?void 0:r.queryDeduplication)&&void 0!==a?a:this.queryDeduplication);var d=this.transform(n).serverQuery;if(d){var x=this.inFlightLinkObservables,w=this.link,k={query:d,variables:i,operationName:getOperationName(d)||void 0,context:this.prepareContext((0,E.pi)((0,E.pi)({},r),{forceFetch:!o}))};if(r=k.context,o){var C=x.get(d)||new Map;x.set(d,C);var D=eY(i);if(!(u=C.get(D))){var I=new e1([em(w,k)]);C.set(D,u=I),I.beforeNext(function(){C.delete(D)&&C.size<1&&x.delete(d)})}}else u=new e1([em(w,k)])}else u=new e1([eu.of({data:{}})]),r=this.prepareContext(r);var R=this.transform(n).clientQuery;return R&&(u=asyncMap(u,function(n){return p.localState.runResolvers({document:R,remoteResult:n,context:r,variables:i})})),u},QueryManager.prototype.getResultsFromLink=function(n,r,i){var o=n.lastRequestId=this.generateRequestId();i=cloneDeepHelper(i);var a=this.cache.transformForLink(this.transform(n.document).document);return asyncMap(this.getObservableFromLink(a,i.context,i.variables),function(u){var p=isNonEmptyArray(u.errors)?u.errors.slice(0):[];"incremental"in u&&isNonEmptyArray(u.incremental)&&u.incremental.forEach(function(n){n.errors&&p.push.apply(p,n.errors)});var d=isNonEmptyArray(p);if(o>=n.lastRequestId){if(d&&"none"===i.errorPolicy)throw n.markError(new e4({graphQLErrors:p}));n.markResult(u,a,i,r),n.markReady()}var x={data:u.data,loading:!1,networkStatus:w.ready};return d&&"ignore"!==i.errorPolicy&&(x.errors=p,x.networkStatus=w.error),x},function(r){var i=r.hasOwnProperty("graphQLErrors")?r:new e4({networkError:r});throw o>=n.lastRequestId&&n.markError(i),i})},QueryManager.prototype.fetchQueryObservable=function(n,r,i){var o=this;void 0===i&&(i=w.loading);var a=this.transform(r.query).document,u=this.getVariables(a,r.variables),p=this.getQuery(n),d=this.defaultOptions.watchQuery,x=r.fetchPolicy,k=void 0===x?d&&d.fetchPolicy||"cache-first":x,E=r.errorPolicy,C=void 0===E?d&&d.errorPolicy||"none":E,D=r.returnPartialData,I=r.notifyOnNetworkStatusChange,R=r.context,V=Object.assign({},r,{query:a,variables:u,fetchPolicy:k,errorPolicy:C,returnPartialData:void 0!==D&&D,notifyOnNetworkStatusChange:void 0!==I&&I,context:void 0===R?{}:R}),fromVariables=function(n){V.variables=n;var a=o.fetchQueryByPolicy(p,V,i);return"standby"!==V.fetchPolicy&&a.length>0&&p.observableQuery&&p.observableQuery.applyNextFetchPolicy("after-fetch",r),a},cleanupCancelFn=function(){return o.fetchCancelFns.delete(n)};this.fetchCancelFns.set(n,function(n){cleanupCancelFn(),setTimeout(function(){return K.cancel(n)})});var K=new e1(this.transform(V.query).hasClientExports?this.localState.addExportedVariables(V.query,V.variables,V.context).then(fromVariables):fromVariables(V.variables));return K.promise.then(cleanupCancelFn,cleanupCancelFn),K},QueryManager.prototype.refetchQueries=function(n){var r=this,i=n.updateCache,o=n.include,a=n.optimistic,u=void 0!==a&&a,p=n.removeOptimistic,d=void 0===p?u?makeUniqueId("refetchQueries"):void 0:p,x=n.onQueryUpdated,w=new Map;o&&this.getObservableQueries(o).forEach(function(n,i){w.set(i,{oq:n,lastDiff:r.getQuery(i).getDiff()})});var k=new Map;return i&&this.cache.batch({update:i,optimistic:u&&d||!1,removeOptimistic:d,onWatchUpdated:function(n,r,i){var o=n.watcher instanceof tw&&n.watcher.observableQuery;if(o){if(x){w.delete(o.queryId);var a=x(o,r,i);return!0===a&&(a=o.refetch()),!1!==a&&k.set(o,a),a}null!==x&&w.set(o.queryId,{oq:o,lastDiff:i,diff:r})}}}),w.size&&w.forEach(function(n,i){var o,a=n.oq,u=n.lastDiff,p=n.diff;if(x){if(!p){var d=a.queryInfo;d.reset(),p=d.getDiff()}o=x(a,p,u)}x&&!0!==o||(o=a.refetch()),!1!==o&&k.set(a,o),i.indexOf("legacyOneTimeQuery")>=0&&r.stopQueryNoBroadcast(i)}),d&&this.cache.removeOptimistic(d),k},QueryManager.prototype.fetchQueryByPolicy=function(n,r,i){var o=this,a=r.query,u=r.variables,p=r.fetchPolicy,d=r.refetchWritePolicy,x=r.errorPolicy,k=r.returnPartialData,C=r.context,D=r.notifyOnNetworkStatusChange,I=n.networkStatus;n.init({document:this.transform(a).document,variables:u,networkStatus:i});var readCache=function(){return n.getDiff(u)},resultsFromCache=function(r,i){void 0===i&&(i=n.networkStatus||w.loading);var p=r.result;!__DEV__||k||equal(p,{})||logMissingFieldErrors(r.missing);var fromData=function(n){return eu.of((0,E.pi)({data:n,loading:isNetworkRequestInFlight(i),networkStatus:i},r.complete?null:{partial:!0}))};return p&&o.transform(a).hasForcedResolvers?o.localState.runResolvers({document:a,remoteResult:{data:p},context:C,variables:u,onlyRunForcedResolvers:!0}).then(function(n){return fromData(n.data||void 0)}):fromData(p)},R="no-cache"===p?0:i===w.refetch&&"merge"!==d?1:2,resultsFromLink=function(){return o.getResultsFromLink(n,R,{variables:u,context:C,fetchPolicy:p,errorPolicy:x})},V=D&&"number"==typeof I&&I!==i&&isNetworkRequestInFlight(i);switch(p){default:case"cache-first":var K=readCache();if(K.complete)return[resultsFromCache(K,n.markReady())];if(k||V)return[resultsFromCache(K),resultsFromLink()];return[resultsFromLink()];case"cache-and-network":var K=readCache();if(K.complete||k||V)return[resultsFromCache(K),resultsFromLink()];return[resultsFromLink()];case"cache-only":return[resultsFromCache(readCache(),n.markReady())];case"network-only":if(V)return[resultsFromCache(readCache()),resultsFromLink()];return[resultsFromLink()];case"no-cache":if(V)return[resultsFromCache(n.getDiff()),resultsFromLink()];return[resultsFromLink()];case"standby":return[]}},QueryManager.prototype.getQuery=function(n){return n&&!this.queries.has(n)&&this.queries.set(n,new tw(this,n)),this.queries.get(n)},QueryManager.prototype.prepareContext=function(n){void 0===n&&(n={});var r=this.localState.prepareContext(n);return(0,E.pi)((0,E.pi)({},r),{clientAwareness:this.clientAwareness})},QueryManager}();function mergeOptions(n,r){return compact(n,r,r.variables&&{variables:(0,E.pi)((0,E.pi)({},n&&n.variables),r.variables)})}var tO=!1,tE=function(){function ApolloClient(n){var r=this;this.resetStoreCallbacks=[],this.clearStoreCallbacks=[];var i=n.uri,o=n.credentials,a=n.headers,u=n.cache,p=n.ssrMode,d=void 0!==p&&p,x=n.ssrForceFetchDelay,w=void 0===x?0:x,k=n.connectToDevTools,E=void 0===k?"object"==typeof window&&!window.__APOLLO_CLIENT__&&__DEV__:k,C=n.queryDeduplication,D=void 0===C||C,I=n.defaultOptions,V=n.assumeImmutableResults,K=n.resolvers,W=n.typeDefs,G=n.fragmentMatcher,J=n.name,et=n.version,en=n.link;if(en||(en=i?new eN({uri:i,credentials:o,headers:a}):eh.empty()),!u)throw __DEV__?new R("To initialize Apollo Client, you must specify a 'cache' property in the options object. \nFor more information, please visit: https://go.apollo.dev/c/docs"):new R(9);if(this.link=en,this.cache=u,this.disableNetworkFetches=d||w>0,this.queryDeduplication=D,this.defaultOptions=I||Object.create(null),this.typeDefs=W,w&&setTimeout(function(){return r.disableNetworkFetches=!1},w),this.watchQuery=this.watchQuery.bind(this),this.query=this.query.bind(this),this.mutate=this.mutate.bind(this),this.resetStore=this.resetStore.bind(this),this.reFetchObservableQueries=this.reFetchObservableQueries.bind(this),E&&"object"==typeof window&&(window.__APOLLO_CLIENT__=this),!tO&&__DEV__&&(tO=!0,"undefined"!=typeof window&&window.document&&window.top===window.self&&!window.__APOLLO_DEVTOOLS_GLOBAL_HOOK__)){var er=window.navigator,ei=er&&er.userAgent,eo=void 0;"string"==typeof ei&&(ei.indexOf("Chrome/")>-1?eo="https://chrome.google.com/webstore/detail/apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm":ei.indexOf("Firefox/")>-1&&(eo="https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/")),eo&&__DEV__&&invariant.log("Download the Apollo DevTools for a better development experience: "+eo)}this.version="3.7.1",this.localState=new t_({cache:u,client:this,resolvers:K,fragmentMatcher:G}),this.queryManager=new tS({cache:this.cache,link:this.link,defaultOptions:this.defaultOptions,queryDeduplication:D,ssrMode:d,clientAwareness:{name:J,version:et},localState:this.localState,assumeImmutableResults:void 0!==V&&V,onBroadcast:E?function(){r.devToolsHookCb&&r.devToolsHookCb({action:{},state:{queries:r.queryManager.getQueryStore(),mutations:r.queryManager.mutationStore||{}},dataWithOptimisticResults:r.cache.extract(!0)})}:void 0})}return ApolloClient.prototype.stop=function(){this.queryManager.stop()},ApolloClient.prototype.watchQuery=function(n){return this.defaultOptions.watchQuery&&(n=mergeOptions(this.defaultOptions.watchQuery,n)),this.disableNetworkFetches&&("network-only"===n.fetchPolicy||"cache-and-network"===n.fetchPolicy)&&(n=(0,E.pi)((0,E.pi)({},n),{fetchPolicy:"cache-first"})),this.queryManager.watchQuery(n)},ApolloClient.prototype.query=function(n){return this.defaultOptions.query&&(n=mergeOptions(this.defaultOptions.query,n)),__DEV__?invariant("cache-and-network"!==n.fetchPolicy,"The cache-and-network fetchPolicy does not work with client.query, because client.query can only return a single result. Please use client.watchQuery to receive multiple results from the cache and the network, or consider using a different fetchPolicy, such as cache-first or network-only."):invariant("cache-and-network"!==n.fetchPolicy,10),this.disableNetworkFetches&&"network-only"===n.fetchPolicy&&(n=(0,E.pi)((0,E.pi)({},n),{fetchPolicy:"cache-first"})),this.queryManager.query(n)},ApolloClient.prototype.mutate=function(n){return this.defaultOptions.mutate&&(n=mergeOptions(this.defaultOptions.mutate,n)),this.queryManager.mutate(n)},ApolloClient.prototype.subscribe=function(n){return this.queryManager.startGraphQLSubscription(n)},ApolloClient.prototype.readQuery=function(n,r){return void 0===r&&(r=!1),this.cache.readQuery(n,r)},ApolloClient.prototype.readFragment=function(n,r){return void 0===r&&(r=!1),this.cache.readFragment(n,r)},ApolloClient.prototype.writeQuery=function(n){this.cache.writeQuery(n),this.queryManager.broadcastQueries()},ApolloClient.prototype.writeFragment=function(n){this.cache.writeFragment(n),this.queryManager.broadcastQueries()},ApolloClient.prototype.__actionHookForDevTools=function(n){this.devToolsHookCb=n},ApolloClient.prototype.__requestRaw=function(n){return em(this.link,n)},ApolloClient.prototype.resetStore=function(){var n=this;return Promise.resolve().then(function(){return n.queryManager.clearStore({discardWatches:!1})}).then(function(){return Promise.all(n.resetStoreCallbacks.map(function(n){return n()}))}).then(function(){return n.reFetchObservableQueries()})},ApolloClient.prototype.clearStore=function(){var n=this;return Promise.resolve().then(function(){return n.queryManager.clearStore({discardWatches:!0})}).then(function(){return Promise.all(n.clearStoreCallbacks.map(function(n){return n()}))})},ApolloClient.prototype.onResetStore=function(n){var r=this;return this.resetStoreCallbacks.push(n),function(){r.resetStoreCallbacks=r.resetStoreCallbacks.filter(function(r){return r!==n})}},ApolloClient.prototype.onClearStore=function(n){var r=this;return this.clearStoreCallbacks.push(n),function(){r.clearStoreCallbacks=r.clearStoreCallbacks.filter(function(r){return r!==n})}},ApolloClient.prototype.reFetchObservableQueries=function(n){return this.queryManager.reFetchObservableQueries(n)},ApolloClient.prototype.refetchQueries=function(n){var r=this.queryManager.refetchQueries(n),i=[],o=[];r.forEach(function(n,r){i.push(r),o.push(n)});var a=Promise.all(o);return a.queries=i,a.results=o,a.catch(function(n){__DEV__&&invariant.debug("In client.refetchQueries, Promise.all promise rejected with error ".concat(n))}),a},ApolloClient.prototype.getObservableQueries=function(n){return void 0===n&&(n="active"),this.queryManager.getObservableQueries(n)},ApolloClient.prototype.extract=function(n){return this.cache.extract(n)},ApolloClient.prototype.restore=function(n){return this.cache.restore(n)},ApolloClient.prototype.addResolvers=function(n){this.localState.addResolvers(n)},ApolloClient.prototype.setResolvers=function(n){this.localState.setResolvers(n)},ApolloClient.prototype.getResolvers=function(){return this.localState.getResolvers()},ApolloClient.prototype.setLocalStateFragmentMatcher=function(n){this.localState.setFragmentMatcher(n)},ApolloClient.prototype.setLink=function(n){this.link=this.queryManager.link=n},ApolloClient}(),tC=function(){function ApolloCache(){this.getFragmentDoc=bundle_esm_wrap(getFragmentQueryDocument)}return ApolloCache.prototype.batch=function(n){var r,i=this,o="string"==typeof n.optimistic?n.optimistic:!1===n.optimistic?null:void 0;return this.performTransaction(function(){return r=n.update(i)},o),r},ApolloCache.prototype.recordOptimisticTransaction=function(n,r){this.performTransaction(n,r)},ApolloCache.prototype.transformDocument=function(n){return n},ApolloCache.prototype.transformForLink=function(n){return n},ApolloCache.prototype.identify=function(n){},ApolloCache.prototype.gc=function(){return[]},ApolloCache.prototype.modify=function(n){return!1},ApolloCache.prototype.readQuery=function(n,r){return void 0===r&&(r=!!n.optimistic),this.read((0,E.pi)((0,E.pi)({},n),{rootId:n.id||"ROOT_QUERY",optimistic:r}))},ApolloCache.prototype.readFragment=function(n,r){return void 0===r&&(r=!!n.optimistic),this.read((0,E.pi)((0,E.pi)({},n),{query:this.getFragmentDoc(n.fragment,n.fragmentName),rootId:n.id,optimistic:r}))},ApolloCache.prototype.writeQuery=function(n){var r=n.id,i=n.data,o=(0,E._T)(n,["id","data"]);return this.write(Object.assign(o,{dataId:r||"ROOT_QUERY",result:i}))},ApolloCache.prototype.writeFragment=function(n){var r=n.id,i=n.data,o=n.fragment,a=n.fragmentName,u=(0,E._T)(n,["id","data","fragment","fragmentName"]);return this.write(Object.assign(u,{query:this.getFragmentDoc(o,a),dataId:r,result:i}))},ApolloCache.prototype.updateQuery=function(n,r){return this.batch({update:function(i){var o=i.readQuery(n),a=r(o);return null==a?o:(i.writeQuery((0,E.pi)((0,E.pi)({},n),{data:a})),a)}})},ApolloCache.prototype.updateFragment=function(n,r){return this.batch({update:function(i){var o=i.readFragment(n),a=r(o);return null==a?o:(i.writeFragment((0,E.pi)((0,E.pi)({},n),{data:a})),a)}})},ApolloCache}(),tj=function(n){function MissingFieldError(r,i,o,a){var u,p=n.call(this,r)||this;if(p.message=r,p.path=i,p.query=o,p.variables=a,Array.isArray(p.path)){p.missing=p.message;for(var d=p.path.length-1;d>=0;--d)p.missing=((u={})[p.path[d]]=p.missing,u)}else p.missing=p.path;return p.__proto__=MissingFieldError.prototype,p}return(0,E.ZT)(MissingFieldError,n),MissingFieldError}(Error);function maybeDeepFreeze(n){if(__DEV__){var r;(r=new Set([n])).forEach(function(n){isNonNullObject(n)&&function(n){if(__DEV__&&!Object.isFrozen(n))try{Object.freeze(n)}catch(n){if(n instanceof TypeError)return null;throw n}return n}(n)===n&&Object.getOwnPropertyNames(n).forEach(function(i){isNonNullObject(n[i])&&r.add(n[i])})})}return n}var tD=Object.create(null),delModifier=function(){return tD},tT=Object.create(null),tI=function(){function EntityStore(n,r){var i=this;this.policies=n,this.group=r,this.data=Object.create(null),this.rootIds=Object.create(null),this.refs=Object.create(null),this.getFieldValue=function(n,r){return maybeDeepFreeze(isReference(n)?i.get(n.__ref,r):n&&n[r])},this.canRead=function(n){return isReference(n)?i.has(n.__ref):"object"==typeof n},this.toReference=function(n,r){if("string"==typeof n)return makeReference(n);if(isReference(n))return n;var o=i.policies.identify(n)[0];if(o){var a=makeReference(o);return r&&i.merge(o,n),a}}}return EntityStore.prototype.toObject=function(){return(0,E.pi)({},this.data)},EntityStore.prototype.has=function(n){return void 0!==this.lookup(n,!0)},EntityStore.prototype.get=function(n,r){if(this.group.depend(n,r),e$.call(this.data,n)){var i=this.data[n];if(i&&e$.call(i,r))return i[r]}return"__typename"===r&&e$.call(this.policies.rootTypenamesById,n)?this.policies.rootTypenamesById[n]:this instanceof tA?this.parent.get(n,r):void 0},EntityStore.prototype.lookup=function(n,r){return(r&&this.group.depend(n,"__exists"),e$.call(this.data,n))?this.data[n]:this instanceof tA?this.parent.lookup(n,r):this.policies.rootTypenamesById[n]?Object.create(null):void 0},EntityStore.prototype.merge=function(n,r){var i,o=this;isReference(n)&&(n=n.__ref),isReference(r)&&(r=r.__ref);var a="string"==typeof n?this.lookup(i=n):n,u="string"==typeof r?this.lookup(i=r):r;if(u){__DEV__?invariant("string"==typeof i,"store.merge expects a string ID"):invariant("string"==typeof i,1);var p=new eU(storeObjectReconciler).merge(a,u);if(this.data[i]=p,p!==a&&(delete this.refs[i],this.group.caching)){var d=Object.create(null);a||(d.__exists=1),Object.keys(u).forEach(function(n){if(!a||a[n]!==p[n]){d[n]=1;var r=fieldNameFromStoreName(n);r===n||o.policies.hasKeyArgs(p.__typename,r)||(d[r]=1),void 0!==p[n]||o instanceof tA||delete p[n]}}),d.__typename&&!(a&&a.__typename)&&this.policies.rootTypenamesById[i]===p.__typename&&delete d.__typename,Object.keys(d).forEach(function(n){return o.group.dirty(i,n)})}}},EntityStore.prototype.modify=function(n,r){var i=this,o=this.lookup(n);if(o){var a=Object.create(null),u=!1,p=!0,d={DELETE:tD,INVALIDATE:tT,isReference:isReference,toReference:this.toReference,canRead:this.canRead,readField:function(r,o){return i.policies.readField("string"==typeof r?{fieldName:r,from:o||makeReference(n)}:r,{store:i})}};if(Object.keys(o).forEach(function(x){var w=fieldNameFromStoreName(x),k=o[x];if(void 0!==k){var C="function"==typeof r?r:r[x]||r[w];if(C){var D=C===delModifier?tD:C(maybeDeepFreeze(k),(0,E.pi)((0,E.pi)({},d),{fieldName:w,storeFieldName:x,storage:i.getStorage(n,x)}));D===tT?i.group.dirty(n,x):(D===tD&&(D=void 0),D!==k&&(a[x]=D,u=!0,k=D))}void 0!==k&&(p=!1)}}),u)return this.merge(n,a),p&&(this instanceof tA?this.data[n]=void 0:delete this.data[n],this.group.dirty(n,"__exists")),!0}return!1},EntityStore.prototype.delete=function(n,r,i){var o,a=this.lookup(n);if(a){var u=this.getFieldValue(a,"__typename"),p=r&&i?this.policies.getStoreFieldName({typename:u,fieldName:r,args:i}):r;return this.modify(n,p?((o={})[p]=delModifier,o):delModifier)}return!1},EntityStore.prototype.evict=function(n,r){var i=!1;return n.id&&(e$.call(this.data,n.id)&&(i=this.delete(n.id,n.fieldName,n.args)),this instanceof tA&&this!==r&&(i=this.parent.evict(n,r)||i),(n.fieldName||i)&&this.group.dirty(n.id,n.fieldName||"__exists")),i},EntityStore.prototype.clear=function(){this.replace(null)},EntityStore.prototype.extract=function(){var n=this,r=this.toObject(),i=[];return this.getRootIdSet().forEach(function(r){e$.call(n.policies.rootTypenamesById,r)||i.push(r)}),i.length&&(r.__META={extraRootIds:i.sort()}),r},EntityStore.prototype.replace=function(n){var r=this;if(Object.keys(this.data).forEach(function(i){n&&e$.call(n,i)||r.delete(i)}),n){var i=n.__META,o=(0,E._T)(n,["__META"]);Object.keys(o).forEach(function(n){r.merge(n,o[n])}),i&&i.extraRootIds.forEach(this.retain,this)}},EntityStore.prototype.retain=function(n){return this.rootIds[n]=(this.rootIds[n]||0)+1},EntityStore.prototype.release=function(n){if(this.rootIds[n]>0){var r=--this.rootIds[n];return r||delete this.rootIds[n],r}return 0},EntityStore.prototype.getRootIdSet=function(n){return void 0===n&&(n=new Set),Object.keys(this.rootIds).forEach(n.add,n),this instanceof tA?this.parent.getRootIdSet(n):Object.keys(this.policies.rootTypenamesById).forEach(n.add,n),n},EntityStore.prototype.gc=function(){var n=this,r=this.getRootIdSet(),i=this.toObject();r.forEach(function(o){e$.call(i,o)&&(Object.keys(n.findChildRefIds(o)).forEach(r.add,r),delete i[o])});var o=Object.keys(i);if(o.length){for(var a=this;a instanceof tA;)a=a.parent;o.forEach(function(n){return a.delete(n)})}return o},EntityStore.prototype.findChildRefIds=function(n){if(!e$.call(this.refs,n)){var r=this.refs[n]=Object.create(null),i=this.data[n];if(!i)return r;var o=new Set([i]);o.forEach(function(n){isReference(n)&&(r[n.__ref]=!0),isNonNullObject(n)&&Object.keys(n).forEach(function(r){var i=n[r];isNonNullObject(i)&&o.add(i)})})}return this.refs[n]},EntityStore.prototype.makeCacheKey=function(){return this.group.keyMaker.lookupArray(arguments)},EntityStore}(),tN=function(){function CacheGroup(n,r){void 0===r&&(r=null),this.caching=n,this.parent=r,this.d=null,this.resetCaching()}return CacheGroup.prototype.resetCaching=function(){this.d=this.caching?dep():null,this.keyMaker=new ez(ex)},CacheGroup.prototype.depend=function(n,r){if(this.d){this.d(r+"#"+n);var i=fieldNameFromStoreName(r);i!==r&&this.d(i+"#"+n),this.parent&&this.parent.depend(n,r)}},CacheGroup.prototype.dirty=function(n,r){this.d&&this.d.dirty(r+"#"+n,"__exists"===r?"forget":"setDirty")},CacheGroup}();function maybeDependOnExistenceOfEntity(n,r){supportsResultCaching(n)&&n.group.depend(r,"__exists")}p=function(n){function Root(r){var i=r.policies,o=r.resultCaching,a=r.seed,u=n.call(this,i,new tN(void 0===o||o))||this;return u.stump=new tF(u),u.storageTrie=new ez(ex),a&&u.replace(a),u}return(0,E.ZT)(Root,n),Root.prototype.addLayer=function(n,r){return this.stump.addLayer(n,r)},Root.prototype.removeLayer=function(){return this},Root.prototype.getStorage=function(){return this.storageTrie.lookupArray(arguments)},Root}(u=tI||(tI={})),u.Root=p;var tA=function(n){function Layer(r,i,o,a){var u=n.call(this,i.policies,a)||this;return u.id=r,u.parent=i,u.replay=o,u.group=a,o(u),u}return(0,E.ZT)(Layer,n),Layer.prototype.addLayer=function(n,r){return new Layer(n,this,r,this.group)},Layer.prototype.removeLayer=function(n){var r=this,i=this.parent.removeLayer(n);return n===this.id?(this.group.caching&&Object.keys(this.data).forEach(function(n){var o=r.data[n],a=i.lookup(n);a?o?o!==a&&Object.keys(o).forEach(function(i){equal(o[i],a[i])||r.group.dirty(n,i)}):(r.group.dirty(n,"__exists"),Object.keys(a).forEach(function(i){r.group.dirty(n,i)})):r.delete(n)}),i):i===this.parent?this:i.addLayer(this.id,this.replay)},Layer.prototype.toObject=function(){return(0,E.pi)((0,E.pi)({},this.parent.toObject()),this.data)},Layer.prototype.findChildRefIds=function(r){var i=this.parent.findChildRefIds(r);return e$.call(this.data,r)?(0,E.pi)((0,E.pi)({},i),n.prototype.findChildRefIds.call(this,r)):i},Layer.prototype.getStorage=function(){for(var n=this.parent;n.parent;)n=n.parent;return n.getStorage.apply(n,arguments)},Layer}(tI),tF=function(n){function Stump(r){return n.call(this,"EntityStore.Stump",r,function(){},new tN(r.group.caching,r.group))||this}return(0,E.ZT)(Stump,n),Stump.prototype.removeLayer=function(){return this},Stump.prototype.merge=function(){return this.parent.merge.apply(this.parent,arguments)},Stump}(tA);function storeObjectReconciler(n,r,i){var o=n[i],a=r[i];return equal(o,a)?o:a}function supportsResultCaching(n){return!!(n instanceof tI&&n.group.caching)}function execSelectionSetKeyArgs(n){return[n.selectionSet,n.objectOrReference,n.context,n.context.canonizeResults]}var tP=function(){function StoreReader(n){var r=this;this.knownResults=new(ex?WeakMap:Map),this.config=compact(n,{addTypename:!1!==n.addTypename,canonizeResults:shouldCanonizeResults(n)}),this.canon=n.canon||new eG,this.executeSelectionSet=bundle_esm_wrap(function(n){var i,o=n.context.canonizeResults,a=execSelectionSetKeyArgs(n);a[3]=!o;var u=(i=r.executeSelectionSet).peek.apply(i,a);return u?o?(0,E.pi)((0,E.pi)({},u),{result:r.canon.admit(u.result)}):u:(maybeDependOnExistenceOfEntity(n.context.store,n.enclosingRef.__ref),r.execSelectionSetImpl(n))},{max:this.config.resultCacheMaxSize,keyArgs:execSelectionSetKeyArgs,makeCacheKey:function(n,r,i,o){if(supportsResultCaching(i.store))return i.store.makeCacheKey(n,isReference(r)?r.__ref:r,i.varString,o)}}),this.executeSubSelectedArray=bundle_esm_wrap(function(n){return maybeDependOnExistenceOfEntity(n.context.store,n.enclosingRef.__ref),r.execSubSelectedArrayImpl(n)},{max:this.config.resultCacheMaxSize,makeCacheKey:function(n){var r=n.field,i=n.array,o=n.context;if(supportsResultCaching(o.store))return o.store.makeCacheKey(r,i,o.varString)}})}return StoreReader.prototype.resetCanon=function(){this.canon=new eG},StoreReader.prototype.diffQueryAgainstStore=function(n){var r,i=n.store,o=n.query,a=n.rootId,u=void 0===a?"ROOT_QUERY":a,p=n.variables,d=n.returnPartialData,x=n.canonizeResults,w=void 0===x?this.config.canonizeResults:x,k=this.config.cache.policies;p=(0,E.pi)((0,E.pi)({},getDefaultValues(getQueryDefinition(o))),p);var C=makeReference(u),D=this.executeSelectionSet({selectionSet:getMainDefinition(o).selectionSet,objectOrReference:C,enclosingRef:C,context:(0,E.pi)({store:i,query:o,policies:k,variables:p,varString:eY(p),canonizeResults:w},extractFragmentContext(o,this.config.fragments))});if(D.missing&&(r=[new tj(function(n){try{JSON.stringify(n,function(n,r){if("string"==typeof r)throw r;return r})}catch(n){return n}}(D.missing),D.missing,o,p)],!(void 0===d||d)))throw r[0];return{result:D.result,complete:!r,missing:r}},StoreReader.prototype.isFresh=function(n,r,i,o){if(supportsResultCaching(o.store)&&this.knownResults.get(n)===i){var a=this.executeSelectionSet.peek(i,r,o,this.canon.isKnown(n));if(a&&n===a.result)return!0}return!1},StoreReader.prototype.execSelectionSetImpl=function(n){var r,i=this,o=n.selectionSet,a=n.objectOrReference,u=n.enclosingRef,p=n.context;if(isReference(a)&&!p.policies.rootTypenamesById[a.__ref]&&!p.store.has(a.__ref))return{result:this.canon.empty,missing:"Dangling reference to missing ".concat(a.__ref," object")};var d=p.variables,x=p.policies,w=p.store.getFieldValue(a,"__typename"),k=[],E=new eU;function handleMissing(n,i){var o;return n.missing&&(r=E.merge(r,((o={})[i]=n.missing,o))),n.result}this.config.addTypename&&"string"==typeof w&&!x.rootIdsByTypename[w]&&k.push({__typename:w});var C=new Set(o.selections);C.forEach(function(n){var o,D;if(shouldInclude(n,d)){if(storeUtils_isField(n)){var I=x.readField({fieldName:n.name.value,field:n,variables:p.variables,from:a},p),V=resultKeyNameFromField(n);void 0===I?eJ.added(n)||(r=E.merge(r,((o={})[V]="Can't find field '".concat(n.name.value,"' on ").concat(isReference(a)?a.__ref+" object":"object "+JSON.stringify(a,null,2)),o))):eH(I)?I=handleMissing(i.executeSubSelectedArray({field:n,array:I,enclosingRef:u,context:p}),V):n.selectionSet?null!=I&&(I=handleMissing(i.executeSelectionSet({selectionSet:n.selectionSet,objectOrReference:I,enclosingRef:isReference(I)?I:u,context:p}),V)):p.canonizeResults&&(I=i.canon.pass(I)),void 0!==I&&k.push(((D={})[V]=I,D))}else{var K=getFragmentFromSelection(n,p.lookupFragment);if(!K&&n.kind===eb.h.FRAGMENT_SPREAD)throw __DEV__?new R("No fragment named ".concat(n.name.value)):new R(5);K&&x.fragmentMatches(K,w)&&K.selectionSet.selections.forEach(C.add,C)}}});var D={result:mergeDeepArray(k),missing:r},I=p.canonizeResults?this.canon.admit(D):maybeDeepFreeze(D);return I.result&&this.knownResults.set(I.result,o),I},StoreReader.prototype.execSubSelectedArrayImpl=function(n){var r,i=this,o=n.field,a=n.array,u=n.enclosingRef,p=n.context,d=new eU;function handleMissing(n,i){var o;return n.missing&&(r=d.merge(r,((o={})[i]=n.missing,o))),n.result}return o.selectionSet&&(a=a.filter(p.store.canRead)),a=a.map(function(n,r){return null===n?null:eH(n)?handleMissing(i.executeSubSelectedArray({field:o,array:n,enclosingRef:u,context:p}),r):o.selectionSet?handleMissing(i.executeSelectionSet({selectionSet:o.selectionSet,objectOrReference:n,enclosingRef:isReference(n)?n:u,context:p}),r):(__DEV__&&function(n,r,i){if(!r.selectionSet){var o=new Set([i]);o.forEach(function(i){isNonNullObject(i)&&(__DEV__?invariant(!isReference(i),"Missing selection set for object of type ".concat(isReference(i)?n.get(i.__ref,"__typename"):i&&i.__typename," returned for query field ").concat(r.name.value)):invariant(!isReference(i),6),Object.values(i).forEach(o.add,o))})}}(p.store,o,n),n)}),{result:p.canonizeResults?this.canon.admit(a):a,missing:r}},StoreReader}(),tM=Object.create(null);function lookupSpecifierInfo(n){var r=JSON.stringify(n);return tM[r]||(tM[r]=Object.create(null))}function keyFieldsFnFromSpecifier(n){var r=lookupSpecifierInfo(n);return r.keyFieldsFn||(r.keyFieldsFn=function(r,i){var extract=function(n,r){return i.readField(r,n)},o=i.keyObject=collectSpecifierPaths(n,function(n){var o=extractKeyPath(i.storeObject,n,extract);return void 0===o&&r!==i.storeObject&&e$.call(r,n[0])&&(o=extractKeyPath(r,n,extractKey)),__DEV__?invariant(void 0!==o,"Missing field '".concat(n.join("."),"' while extracting keyFields from ").concat(JSON.stringify(r))):invariant(void 0!==o,2),o});return"".concat(i.typename,":").concat(JSON.stringify(o))})}function keyArgsFnFromSpecifier(n){var r=lookupSpecifierInfo(n);return r.keyArgsFn||(r.keyArgsFn=function(r,i){var o=i.field,a=i.variables,u=i.fieldName,p=JSON.stringify(collectSpecifierPaths(n,function(n){var i=n[0],u=i.charAt(0);if("@"===u){if(o&&isNonEmptyArray(o.directives)){var p=i.slice(1),d=o.directives.find(function(n){return n.name.value===p}),x=d&&argumentsObjectFromField(d,a);return x&&extractKeyPath(x,n.slice(1))}return}if("$"===u){var w=i.slice(1);if(a&&e$.call(a,w)){var k=n.slice(0);return k[0]=w,extractKeyPath(a,k)}return}if(r)return extractKeyPath(r,n)}));return(r||"{}"!==p)&&(u+=":"+p),u})}function collectSpecifierPaths(n,r){var i=new eU;return(function getSpecifierPaths(n){var r=lookupSpecifierInfo(n);if(!r.paths){var i=r.paths=[],o=[];n.forEach(function(r,a){eH(r)?(getSpecifierPaths(r).forEach(function(n){return i.push(o.concat(n))}),o.length=0):(o.push(r),eH(n[a+1])||(i.push(o.slice(0)),o.length=0))})}return r.paths})(n).reduce(function(n,o){var a,u=r(o);if(void 0!==u){for(var p=o.length-1;p>=0;--p)(a={})[o[p]]=u,u=a;n=i.merge(n,u)}return n},Object.create(null))}function extractKey(n,r){return n[r]}function extractKeyPath(n,r,i){return i=i||extractKey,function normalize(n){return isNonNullObject(n)?eH(n)?n.map(normalize):collectSpecifierPaths(Object.keys(n).sort(),function(r){return extractKeyPath(n,r)}):n}(r.reduce(function reducer(n,r){return eH(n)?n.map(function(n){return reducer(n,r)}):n&&i(n,r)},n))}function argsFromFieldSpecifier(n){return void 0!==n.args?n.args:n.field?argumentsObjectFromField(n.field,n.variables):null}ef.setStringify(eY);var nullKeyFieldsFn=function(){},simpleKeyArgsFn=function(n,r){return r.fieldName},mergeTrueFn=function(n,r,i){return(0,i.mergeObjects)(n,r)},mergeFalseFn=function(n,r){return r},tR=function(){function Policies(n){this.config=n,this.typePolicies=Object.create(null),this.toBeAdded=Object.create(null),this.supertypeMap=new Map,this.fuzzySubtypes=new Map,this.rootIdsByTypename=Object.create(null),this.rootTypenamesById=Object.create(null),this.usingPossibleTypes=!1,this.config=(0,E.pi)({dataIdFromObject:defaultDataIdFromObject},n),this.cache=this.config.cache,this.setRootTypename("Query"),this.setRootTypename("Mutation"),this.setRootTypename("Subscription"),n.possibleTypes&&this.addPossibleTypes(n.possibleTypes),n.typePolicies&&this.addTypePolicies(n.typePolicies)}return Policies.prototype.identify=function(n,r){var i,o,a=this,u=r&&(r.typename||(null===(i=r.storeObject)||void 0===i?void 0:i.__typename))||n.__typename;if(u===this.rootTypenamesById.ROOT_QUERY)return["ROOT_QUERY"];for(var p=r&&r.storeObject||n,d=(0,E.pi)((0,E.pi)({},r),{typename:u,storeObject:p,readField:r&&r.readField||function(){var n=normalizeReadFieldOptions(arguments,p);return a.readField(n,{store:a.cache.data,variables:n.variables})}}),x=u&&this.getTypePolicy(u),w=x&&x.keyFn||this.config.dataIdFromObject;w;){var k=w(n,d);if(eH(k))w=keyFieldsFnFromSpecifier(k);else{o=k;break}}return o=o?String(o):void 0,d.keyObject?[o,d.keyObject]:[o]},Policies.prototype.addTypePolicies=function(n){var r=this;Object.keys(n).forEach(function(i){var o=n[i],a=o.queryType,u=o.mutationType,p=o.subscriptionType,d=(0,E._T)(o,["queryType","mutationType","subscriptionType"]);a&&r.setRootTypename("Query",i),u&&r.setRootTypename("Mutation",i),p&&r.setRootTypename("Subscription",i),e$.call(r.toBeAdded,i)?r.toBeAdded[i].push(d):r.toBeAdded[i]=[d]})},Policies.prototype.updateTypePolicy=function(n,r){var i=this,o=this.getTypePolicy(n),a=r.keyFields,u=r.fields;function setMerge(n,r){n.merge="function"==typeof r?r:!0===r?mergeTrueFn:!1===r?mergeFalseFn:n.merge}setMerge(o,r.merge),o.keyFn=!1===a?nullKeyFieldsFn:eH(a)?keyFieldsFnFromSpecifier(a):"function"==typeof a?a:o.keyFn,u&&Object.keys(u).forEach(function(r){var o=i.getFieldPolicy(n,r,!0),a=u[r];if("function"==typeof a)o.read=a;else{var p=a.keyArgs,d=a.read,x=a.merge;o.keyFn=!1===p?simpleKeyArgsFn:eH(p)?keyArgsFnFromSpecifier(p):"function"==typeof p?p:o.keyFn,"function"==typeof d&&(o.read=d),setMerge(o,x)}o.read&&o.merge&&(o.keyFn=o.keyFn||simpleKeyArgsFn)})},Policies.prototype.setRootTypename=function(n,r){void 0===r&&(r=n);var i="ROOT_"+n.toUpperCase(),o=this.rootTypenamesById[i];r!==o&&(__DEV__?invariant(!o||o===n,"Cannot change root ".concat(n," __typename more than once")):invariant(!o||o===n,3),o&&delete this.rootIdsByTypename[o],this.rootIdsByTypename[r]=i,this.rootTypenamesById[i]=r)},Policies.prototype.addPossibleTypes=function(n){var r=this;this.usingPossibleTypes=!0,Object.keys(n).forEach(function(i){r.getSupertypeSet(i,!0),n[i].forEach(function(n){r.getSupertypeSet(n,!0).add(i);var o=n.match(eW);o&&o[0]===n||r.fuzzySubtypes.set(n,new RegExp(n))})})},Policies.prototype.getTypePolicy=function(n){var r=this;if(!e$.call(this.typePolicies,n)){var i=this.typePolicies[n]=Object.create(null);i.fields=Object.create(null);var o=this.supertypeMap.get(n);o&&o.size&&o.forEach(function(n){var o=r.getTypePolicy(n),a=o.fields;Object.assign(i,(0,E._T)(o,["fields"])),Object.assign(i.fields,a)})}var a=this.toBeAdded[n];return a&&a.length&&a.splice(0).forEach(function(i){r.updateTypePolicy(n,i)}),this.typePolicies[n]},Policies.prototype.getFieldPolicy=function(n,r,i){if(n){var o=this.getTypePolicy(n).fields;return o[r]||i&&(o[r]=Object.create(null))}},Policies.prototype.getSupertypeSet=function(n,r){var i=this.supertypeMap.get(n);return!i&&r&&this.supertypeMap.set(n,i=new Set),i},Policies.prototype.fragmentMatches=function(n,r,i,o){var a=this;if(!n.typeCondition)return!0;if(!r)return!1;var u=n.typeCondition.name.value;if(r===u)return!0;if(this.usingPossibleTypes&&this.supertypeMap.has(u))for(var p=this.getSupertypeSet(r,!0),d=[p],maybeEnqueue_1=function(n){var r=a.getSupertypeSet(n,!1);r&&r.size&&0>d.indexOf(r)&&d.push(r)},x=!!(i&&this.fuzzySubtypes.size),w=!1,k=0;k<d.length;++k){var E=d[k];if(E.has(u))return p.has(u)||(w&&__DEV__&&invariant.warn("Inferring subtype ".concat(r," of supertype ").concat(u)),p.add(u)),!0;E.forEach(maybeEnqueue_1),x&&k===d.length-1&&function selectionSetMatchesResult(n,r,i){return!!isNonNullObject(r)&&(eH(r)?r.every(function(r){return selectionSetMatchesResult(n,r,i)}):n.selections.every(function(n){if(storeUtils_isField(n)&&shouldInclude(n,i)){var o=resultKeyNameFromField(n);return e$.call(r,o)&&(!n.selectionSet||selectionSetMatchesResult(n.selectionSet,r[o],i))}return!0}))}(n.selectionSet,i,o)&&(x=!1,w=!0,this.fuzzySubtypes.forEach(function(n,i){var o=r.match(n);o&&o[0]===r&&maybeEnqueue_1(i)}))}return!1},Policies.prototype.hasKeyArgs=function(n,r){var i=this.getFieldPolicy(n,r,!1);return!!(i&&i.keyFn)},Policies.prototype.getStoreFieldName=function(n){var r,i,o,a,u,p=n.typename,d=n.fieldName,x=this.getFieldPolicy(p,d,!1),w=x&&x.keyFn;if(w&&p)for(var k={typename:p,fieldName:d,field:n.field||null,variables:n.variables},E=argsFromFieldSpecifier(n);w;){var C=w(E,k);if(eH(C))w=keyArgsFnFromSpecifier(C);else{u=C||d;break}}return void 0===u&&(u=n.field?(r=n.field,i=n.variables,o=null,r.directives&&(o={},r.directives.forEach(function(n){o[n.name.value]={},n.arguments&&n.arguments.forEach(function(r){var a=r.name,u=r.value;return valueToObjectRepresentation(o[n.name.value],a,u,i)})})),a=null,r.arguments&&r.arguments.length&&(a={},r.arguments.forEach(function(n){var r=n.name,o=n.value;return valueToObjectRepresentation(a,r,o,i)})),ef(r.name.value,a,o)):ef(d,argsFromFieldSpecifier(n))),!1===u?d:d===fieldNameFromStoreName(u)?u:d+":"+u},Policies.prototype.readField=function(n,r){var i=n.from;if(i&&(n.field||n.fieldName)){if(void 0===n.typename){var o=r.store.getFieldValue(i,"__typename");o&&(n.typename=o)}var a=this.getStoreFieldName(n),u=fieldNameFromStoreName(a),p=r.store.getFieldValue(i,a),d=this.getFieldPolicy(n.typename,u,!1),x=d&&d.read;if(x){var w=makeFieldFunctionOptions(this,i,n,r,r.store.getStorage(isReference(i)?i.__ref:i,a));return tg.withValue(this.cache,x,[p,w])}return p}},Policies.prototype.getReadFunction=function(n,r){var i=this.getFieldPolicy(n,r,!1);return i&&i.read},Policies.prototype.getMergeFunction=function(n,r,i){var o=this.getFieldPolicy(n,r,!1),a=o&&o.merge;return!a&&i&&(a=(o=this.getTypePolicy(i))&&o.merge),a},Policies.prototype.runMergeFunction=function(n,r,i,o,a){var u=i.field,p=i.typename,d=i.merge;return d===mergeTrueFn?makeMergeObjectsFunction(o.store)(n,r):d===mergeFalseFn?r:(o.overwrite&&(n=void 0),d(n,r,makeFieldFunctionOptions(this,void 0,{typename:p,fieldName:u.name.value,field:u,variables:o.variables},o,a||Object.create(null))))},Policies}();function makeFieldFunctionOptions(n,r,i,o,a){var u=n.getStoreFieldName(i),p=fieldNameFromStoreName(u),d=i.variables||o.variables,x=o.store,w=x.toReference,k=x.canRead;return{args:argsFromFieldSpecifier(i),field:i.field||null,fieldName:p,storeFieldName:u,variables:d,isReference:isReference,toReference:w,storage:a,cache:n.cache,canRead:k,readField:function(){return n.readField(normalizeReadFieldOptions(arguments,r,d),o)},mergeObjects:makeMergeObjectsFunction(o.store)}}function normalizeReadFieldOptions(n,r,i){var o,a,u,p=n[0],d=n[1],x=n.length;return"string"==typeof p?u={fieldName:p,from:x>1?d:r}:(u=(0,E.pi)({},p),e$.call(u,"from")||(u.from=r)),__DEV__&&void 0===u.from&&__DEV__&&invariant.warn("Undefined 'from' passed to readField with arguments ".concat((o=Array.from(n),a=makeUniqueId("stringifyForDisplay"),JSON.stringify(o,function(n,r){return void 0===r?a:r}).split(JSON.stringify(a)).join("<undefined>")))),void 0===u.variables&&(u.variables=i),u}function makeMergeObjectsFunction(n){return function(r,i){if(eH(r)||eH(i))throw __DEV__?new R("Cannot automatically merge arrays"):new R(4);if(isNonNullObject(r)&&isNonNullObject(i)){var o=n.getFieldValue(r,"__typename"),a=n.getFieldValue(i,"__typename");if(o&&a&&o!==a)return i;if(isReference(r)&&storeValueIsStoreObject(i))return n.merge(r.__ref,i),r;if(storeValueIsStoreObject(r)&&isReference(i))return n.merge(r,i.__ref),i;if(storeValueIsStoreObject(r)&&storeValueIsStoreObject(i))return(0,E.pi)((0,E.pi)({},r),i)}return i}}function getContextFlavor(n,r,i){var o="".concat(r).concat(i),a=n.flavors.get(o);return a||n.flavors.set(o,a=n.clientOnly===r&&n.deferred===i?n:(0,E.pi)((0,E.pi)({},n),{clientOnly:r,deferred:i})),a}var tL=function(){function StoreWriter(n,r,i){this.cache=n,this.reader=r,this.fragments=i}return StoreWriter.prototype.writeToStore=function(n,r){var i=this,o=r.query,a=r.result,u=r.dataId,p=r.variables,d=r.overwrite,x=getOperationDefinition(o),w=new eU;p=(0,E.pi)((0,E.pi)({},getDefaultValues(x)),p);var k=(0,E.pi)((0,E.pi)({store:n,written:Object.create(null),merge:function(n,r){return w.merge(n,r)},variables:p,varString:eY(p)},extractFragmentContext(o,this.fragments)),{overwrite:!!d,incomingById:new Map,clientOnly:!1,deferred:!1,flavors:new Map}),C=this.processSelectionSet({result:a||Object.create(null),dataId:u,selectionSet:x.selectionSet,mergeTree:{map:new Map},context:k});if(!isReference(C))throw __DEV__?new R("Could not identify object ".concat(JSON.stringify(a))):new R(7);return k.incomingById.forEach(function(r,o){var a=r.storeObject,u=r.mergeTree,p=r.fieldNodeSet,d=makeReference(o);if(u&&u.map.size){var x=i.applyMerges(u,d,a,k);if(isReference(x))return;a=x}if(__DEV__&&!k.overwrite){var w=Object.create(null);p.forEach(function(n){n.selectionSet&&(w[n.name.value]=!0)});var hasMergeFunction_1=function(n){var r=u&&u.map.get(n);return!!(r&&r.info&&r.info.merge)};Object.keys(a).forEach(function(n){!0!==w[fieldNameFromStoreName(n)]||hasMergeFunction_1(n)||function(n,r,i,o){var getChild=function(n){var r=o.getFieldValue(n,i);return"object"==typeof r&&r},a=getChild(n);if(a){var u=getChild(r);if(!(!u||isReference(a)||equal(a,u)||Object.keys(a).every(function(n){return void 0!==o.getFieldValue(u,n)}))){var p=o.getFieldValue(n,"__typename")||o.getFieldValue(r,"__typename"),d=fieldNameFromStoreName(i),x="".concat(p,".").concat(d);if(!tV.has(x)){tV.add(x);var w=[];eH(a)||eH(u)||[a,u].forEach(function(n){var r=o.getFieldValue(n,"__typename");"string"!=typeof r||w.includes(r)||w.push(r)}),__DEV__&&invariant.warn("Cache data may be lost when replacing the ".concat(d," field of a ").concat(p," object.\n\nTo address this problem (which is not a bug in Apollo Client), ").concat(w.length?"either ensure all objects of type "+w.join(" and ")+" have an ID or a custom merge function, or ":"","define a custom merge function for the ").concat(x," field, so InMemoryCache can safely merge these objects:\n\n  existing: ").concat(JSON.stringify(a).slice(0,1e3),"\n  incoming: ").concat(JSON.stringify(u).slice(0,1e3),"\n\nFor more information about these options, please refer to the documentation:\n\n  * Ensuring entity objects have IDs: https://go.apollo.dev/c/generating-unique-identifiers\n  * Defining custom merge functions: https://go.apollo.dev/c/merging-non-normalized-objects\n"))}}}}(d,a,n,k.store)})}n.merge(o,a)}),n.retain(C.__ref),C},StoreWriter.prototype.processSelectionSet=function(n){var r=this,i=n.dataId,o=n.result,a=n.selectionSet,u=n.context,p=n.mergeTree,d=this.cache.policies,x=Object.create(null),w=i&&d.rootTypenamesById[i]||getTypenameFromResult(o,a,u.fragmentMap)||i&&u.store.get(i,"__typename");"string"==typeof w&&(x.__typename=w);var readField=function(){var n=normalizeReadFieldOptions(arguments,x,u.variables);if(isReference(n.from)){var r=u.incomingById.get(n.from.__ref);if(r){var i=d.readField((0,E.pi)((0,E.pi)({},n),{from:r.storeObject}),u);if(void 0!==i)return i}}return d.readField(n,u)},k=new Set;this.flattenFields(a,o,u,w).forEach(function(n,i){var a,u=o[resultKeyNameFromField(i)];if(k.add(i),void 0!==u){var E=d.getStoreFieldName({typename:w,fieldName:i.name.value,field:i,variables:n.variables}),C=getChildMergeTree(p,E),D=r.processFieldValue(u,i,i.selectionSet?getContextFlavor(n,!1,!1):n,C),I=void 0;i.selectionSet&&(isReference(D)||storeValueIsStoreObject(D))&&(I=readField("__typename",D));var R=d.getMergeFunction(w,i.name.value,I);R?C.info={field:i,typename:w,merge:R}:maybeRecycleChildMergeTree(p,E),x=n.merge(x,((a={})[E]=D,a))}else __DEV__&&!n.clientOnly&&!n.deferred&&!eJ.added(i)&&!d.getReadFunction(w,i.name.value)&&__DEV__&&invariant.error("Missing field '".concat(resultKeyNameFromField(i),"' while writing result ").concat(JSON.stringify(o,null,2)).substring(0,1e3))});try{var C=d.identify(o,{typename:w,selectionSet:a,fragmentMap:u.fragmentMap,storeObject:x,readField:readField}),D=C[0],I=C[1];i=i||D,I&&(x=u.merge(x,I))}catch(n){if(!i)throw n}if("string"==typeof i){var R=makeReference(i),V=u.written[i]||(u.written[i]=[]);if(V.indexOf(a)>=0||(V.push(a),this.reader&&this.reader.isFresh(o,R,a,u)))return R;var K=u.incomingById.get(i);return K?(K.storeObject=u.merge(K.storeObject,x),K.mergeTree=function mergeMergeTrees(n,r){if(n===r||!r||mergeTreeIsEmpty(r))return n;if(!n||mergeTreeIsEmpty(n))return r;var i=n.info&&r.info?(0,E.pi)((0,E.pi)({},n.info),r.info):n.info||r.info,o=n.map.size&&r.map.size,a={info:i,map:o?new Map:n.map.size?n.map:r.map};if(o){var u=new Set(r.map.keys());n.map.forEach(function(n,i){a.map.set(i,mergeMergeTrees(n,r.map.get(i))),u.delete(i)}),u.forEach(function(i){a.map.set(i,mergeMergeTrees(r.map.get(i),n.map.get(i)))})}return a}(K.mergeTree,p),k.forEach(function(n){return K.fieldNodeSet.add(n)})):u.incomingById.set(i,{storeObject:x,mergeTree:mergeTreeIsEmpty(p)?void 0:p,fieldNodeSet:k}),R}return x},StoreWriter.prototype.processFieldValue=function(n,r,i,o){var a=this;return r.selectionSet&&null!==n?eH(n)?n.map(function(n,u){var p=a.processFieldValue(n,r,i,getChildMergeTree(o,u));return maybeRecycleChildMergeTree(o,u),p}):this.processSelectionSet({result:n,selectionSet:r.selectionSet,context:i,mergeTree:o}):__DEV__?cloneDeepHelper(n):n},StoreWriter.prototype.flattenFields=function(n,r,i,o){void 0===o&&(o=getTypenameFromResult(r,n,i.fragmentMap));var a=new Map,u=this.cache.policies,p=new ez(!1);return function flatten(n,d){var x=p.lookup(n,d.clientOnly,d.deferred);x.visited||(x.visited=!0,n.selections.forEach(function(n){if(shouldInclude(n,i.variables)){var p=d.clientOnly,x=d.deferred;if(!(p&&x)&&isNonEmptyArray(n.directives)&&n.directives.forEach(function(n){var r=n.name.value;if("client"===r&&(p=!0),"defer"===r){var o=argumentsObjectFromField(n,i.variables);o&&!1===o.if||(x=!0)}}),storeUtils_isField(n)){var w=a.get(n);w&&(p=p&&w.clientOnly,x=x&&w.deferred),a.set(n,getContextFlavor(i,p,x))}else{var k=getFragmentFromSelection(n,i.lookupFragment);if(!k&&n.kind===eb.h.FRAGMENT_SPREAD)throw __DEV__?new R("No fragment named ".concat(n.name.value)):new R(8);k&&u.fragmentMatches(k,o,r,i.variables)&&flatten(k.selectionSet,getContextFlavor(i,p,x))}}}))}(n,i),a},StoreWriter.prototype.applyMerges=function(n,r,i,o,a){var u=this;if(n.map.size&&!isReference(i)){var p,d,x=!eH(i)&&(isReference(r)||storeValueIsStoreObject(r))?r:void 0,w=i;x&&!a&&(a=[isReference(x)?x.__ref:x]);var getValue_1=function(n,r){return eH(n)?"number"==typeof r?n[r]:void 0:o.store.getFieldValue(n,String(r))};n.map.forEach(function(n,r){var i=getValue_1(x,r),p=getValue_1(w,r);if(void 0!==p){a&&a.push(r);var k=u.applyMerges(n,i,p,o,a);k!==p&&(d=d||new Map).set(r,k),a&&invariant(a.pop()===r)}}),d&&(i=eH(w)?w.slice(0):(0,E.pi)({},w),d.forEach(function(n,r){i[r]=n}))}return n.info?this.cache.policies.runMergeFunction(r,i,n.info,o,a&&(p=o.store).getStorage.apply(p,a)):i},StoreWriter}(),tq=[];function getChildMergeTree(n,r){var i=n.map;return i.has(r)||i.set(r,tq.pop()||{map:new Map}),i.get(r)}function mergeTreeIsEmpty(n){return!n||!(n.info||n.map.size)}function maybeRecycleChildMergeTree(n,r){var i=n.map,o=i.get(r);o&&mergeTreeIsEmpty(o)&&(tq.push(o),i.delete(r))}var tV=new Set,tQ=function(n){function InMemoryCache(r){void 0===r&&(r={});var i=n.call(this)||this;return i.watches=new Set,i.typenameDocumentCache=new Map,i.makeVar=makeVar,i.txCount=0,i.config=compact(eK,r),i.addTypename=!!i.config.addTypename,i.policies=new tR({cache:i,dataIdFromObject:i.config.dataIdFromObject,possibleTypes:i.config.possibleTypes,typePolicies:i.config.typePolicies}),i.init(),i}return(0,E.ZT)(InMemoryCache,n),InMemoryCache.prototype.init=function(){var n=this.data=new tI.Root({policies:this.policies,resultCaching:this.config.resultCaching});this.optimisticData=n.stump,this.resetResultCache()},InMemoryCache.prototype.resetResultCache=function(n){var r=this,i=this.storeReader,o=this.config.fragments;this.storeWriter=new tL(this,this.storeReader=new tP({cache:this,addTypename:this.addTypename,resultCacheMaxSize:this.config.resultCacheMaxSize,canonizeResults:shouldCanonizeResults(this.config),canon:n?void 0:i&&i.canon,fragments:o}),o),this.maybeBroadcastWatch=bundle_esm_wrap(function(n,i){return r.broadcastWatch(n,i)},{max:this.config.resultCacheMaxSize,makeCacheKey:function(n){var i=n.optimistic?r.optimisticData:r.data;if(supportsResultCaching(i)){var o=n.optimistic,a=n.id,u=n.variables;return i.makeCacheKey(n.query,n.callback,eY({optimistic:o,id:a,variables:u}))}}}),new Set([this.data.group,this.optimisticData.group]).forEach(function(n){return n.resetCaching()})},InMemoryCache.prototype.restore=function(n){return this.init(),n&&this.data.replace(n),this},InMemoryCache.prototype.extract=function(n){return void 0===n&&(n=!1),(n?this.optimisticData:this.data).extract()},InMemoryCache.prototype.read=function(n){var r=n.returnPartialData;try{return this.storeReader.diffQueryAgainstStore((0,E.pi)((0,E.pi)({},n),{store:n.optimistic?this.optimisticData:this.data,config:this.config,returnPartialData:void 0!==r&&r})).result||null}catch(n){if(n instanceof tj)return null;throw n}},InMemoryCache.prototype.write=function(n){try{return++this.txCount,this.storeWriter.writeToStore(this.data,n)}finally{--this.txCount||!1===n.broadcast||this.broadcastWatches()}},InMemoryCache.prototype.modify=function(n){if(e$.call(n,"id")&&!n.id)return!1;var r=n.optimistic?this.optimisticData:this.data;try{return++this.txCount,r.modify(n.id||"ROOT_QUERY",n.fields)}finally{--this.txCount||!1===n.broadcast||this.broadcastWatches()}},InMemoryCache.prototype.diff=function(n){return this.storeReader.diffQueryAgainstStore((0,E.pi)((0,E.pi)({},n),{store:n.optimistic?this.optimisticData:this.data,rootId:n.id||"ROOT_QUERY",config:this.config}))},InMemoryCache.prototype.watch=function(n){var r=this;return this.watches.size||function(n){getCacheInfo(n).vars.forEach(function(r){return r.attachCache(n)})}(this),this.watches.add(n),n.immediate&&this.maybeBroadcastWatch(n),function(){r.watches.delete(n)&&!r.watches.size&&forgetCache(r),r.maybeBroadcastWatch.forget(n)}},InMemoryCache.prototype.gc=function(n){eY.reset();var r=this.optimisticData.gc();return n&&!this.txCount&&(n.resetResultCache?this.resetResultCache(n.resetResultIdentities):n.resetResultIdentities&&this.storeReader.resetCanon()),r},InMemoryCache.prototype.retain=function(n,r){return(r?this.optimisticData:this.data).retain(n)},InMemoryCache.prototype.release=function(n,r){return(r?this.optimisticData:this.data).release(n)},InMemoryCache.prototype.identify=function(n){if(isReference(n))return n.__ref;try{return this.policies.identify(n)[0]}catch(n){__DEV__&&invariant.warn(n)}},InMemoryCache.prototype.evict=function(n){if(!n.id){if(e$.call(n,"id"))return!1;n=(0,E.pi)((0,E.pi)({},n),{id:"ROOT_QUERY"})}try{return++this.txCount,this.optimisticData.evict(n,this.data)}finally{--this.txCount||!1===n.broadcast||this.broadcastWatches()}},InMemoryCache.prototype.reset=function(n){var r=this;return this.init(),eY.reset(),n&&n.discardWatches?(this.watches.forEach(function(n){return r.maybeBroadcastWatch.forget(n)}),this.watches.clear(),forgetCache(this)):this.broadcastWatches(),Promise.resolve()},InMemoryCache.prototype.removeOptimistic=function(n){var r=this.optimisticData.removeLayer(n);r!==this.optimisticData&&(this.optimisticData=r,this.broadcastWatches())},InMemoryCache.prototype.batch=function(n){var r,i=this,o=n.update,a=n.optimistic,u=void 0===a||a,p=n.removeOptimistic,d=n.onWatchUpdated,perform=function(n){var a=i.data,u=i.optimisticData;++i.txCount,n&&(i.data=i.optimisticData=n);try{return r=o(i)}finally{--i.txCount,i.data=a,i.optimisticData=u}},x=new Set;return d&&!this.txCount&&this.broadcastWatches((0,E.pi)((0,E.pi)({},n),{onWatchUpdated:function(n){return x.add(n),!1}})),"string"==typeof u?this.optimisticData=this.optimisticData.addLayer(u,perform):!1===u?perform(this.data):perform(),"string"==typeof p&&(this.optimisticData=this.optimisticData.removeLayer(p)),d&&x.size?(this.broadcastWatches((0,E.pi)((0,E.pi)({},n),{onWatchUpdated:function(n,r){var i=d.call(this,n,r);return!1!==i&&x.delete(n),i}})),x.size&&x.forEach(function(n){return i.maybeBroadcastWatch.dirty(n)})):this.broadcastWatches(n),r},InMemoryCache.prototype.performTransaction=function(n,r){return this.batch({update:n,optimistic:r||null!==r})},InMemoryCache.prototype.transformDocument=function(n){if(this.addTypename){var r=this.typenameDocumentCache.get(n);return r||(r=eJ(n),this.typenameDocumentCache.set(n,r),this.typenameDocumentCache.set(r,r)),r}return n},InMemoryCache.prototype.transformForLink=function(n){var r=this.config.fragments;return r?r.transform(n):n},InMemoryCache.prototype.broadcastWatches=function(n){var r=this;this.txCount||this.watches.forEach(function(i){return r.maybeBroadcastWatch(i,n)})},InMemoryCache.prototype.broadcastWatch=function(n,r){var i=n.lastDiff,o=this.diff(n);(!r||(n.optimistic&&"string"==typeof r.optimistic&&(o.fromOptimisticTransaction=!0),!r.onWatchUpdated||!1!==r.onWatchUpdated.call(this,n,o,i)))&&(i&&equal(i.result,o.result)||n.callback(n.lastDiff=o,i))},InMemoryCache}(tC);let tz=new tE({uri:"http://127.0.0.1:1337/graphql",cache:new tQ({addTypename:!1}),defaultOptions:{query:{fetchPolicy:"no-cache"}}});var tB=tz},4629:function(n,r,i){"use strict";i.d(r,{Z:function(){return AnimatedTextButton}});var o=i(2729),a=i(5893),u=i(9521),p=i(1261);function _templateObject(){let n=(0,o._)(["\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0 24px 0 24px;\n  background-color: transparent;\n  border: 1px solid ",";\n  border-radius: 100px;\n  font-family: Switzer, serif;\n  font-size: 16px;\n  cursor: pointer;\n  overflow: hidden;\n  isolation: isolate;\n  line-height: 32px;\n\n  p {\n    color: ",";\n    margin: 0;\n    height: 100%;\n    transition: transform 350ms ease-out;\n  }\n  \n  &:after {\n    position: absolute;\n    content: '","';\n    color: ",";\n    background-color: ",";\n    border-radius: 100px;\n    top: 100%;\n    height: 100%;\n    width: 100%;\n    transition: transform 350ms ease-out;\n  }\n  \n  &:hover {\n    p {\n      transform: translateY(-100%);\n    }\n    &:after {\n      transform: translateY(-100%);\n    }\n  }\n"]);return _templateObject=function(){return n},n}function AnimatedTextButton(n){let{text:r,link:i,onClickFunction:o,theme:u}=n;function Button(){return(0,a.jsx)(d,{className:"animated-text-button",text:r,onClick:o?()=>o():null,light:"light"===u,children:(0,a.jsx)("p",{children:r})})}return i?(0,a.jsx)(p.Z,{link:i,children:(0,a.jsx)(Button,{})}):(0,a.jsx)(Button,{})}let d=u.ZP.button.withConfig({componentId:"sc-9eb99ee0-0"})(_templateObject(),n=>n.light?"var(--c-soft-cream)":"black",n=>n.light?"var(--c-soft-cream)":"black",n=>n.text,n=>n.light?"#081921":"var(--c-soft-cream)",n=>n.light?"var(--c-soft-cream)":"black")},4256:function(n,r,i){"use strict";i.d(r,{Z_:function(){return AnimatedArrowButton},Ty:function(){return w.Z},Yz:function(){return SmallButton}});var o=i(2729),a=i(5893),u=i(9521);function _templateObject(){let n=(0,o._)(["\n  padding: 6px 20px;\n  font-family: Switzer, sans-serif;\n  font-weight: 400;\n  border-radius: 30px;\n  cursor: pointer;\n  letter-spacing: 0.4px;\n  font-size: 16px;\n  \n  &.light {\n    color: black;\n    background-color: white;\n  }\n  \n  &.dark {\n    color: white;\n    background-color: var(--c-dark-green);\n  }\n"]);return _templateObject=function(){return n},n}function SmallButton(n){let{text:r,action:i,theme:o="light"}=n;return(0,a.jsx)(p,{onClick:i,className:o,children:r})}let p=u.ZP.div.withConfig({componentId:"sc-1dc5b33c-0"})(_templateObject());var long_arrow=()=>(0,a.jsx)("div",{className:"icn-arrow",children:(0,a.jsx)("svg",{width:"24",height:"8",viewBox:"0 0 24 8",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{d:"M17.7277 7.80001C18.0477 7.11734 18.3571 6.52001 18.6557 6.00801C18.9757 5.49601 19.2851 5.06935 19.5837 4.72801H0.927734V3.38401H19.5837C19.2851 3.02134 18.9757 2.58401 18.6557 2.07201C18.3571 1.56001 18.0477 0.973345 17.7277 0.312012H18.8477C20.1917 1.86935 21.5997 3.02134 23.0717 3.76801V4.34401C21.5997 5.06934 20.1917 6.22134 18.8477 7.80001H17.7277Z",fill:"#F6F4F3"})})}),d=i(7421);function AnimatedArrowButton_templateObject(){let n=(0,o._)(["\n  padding: 0;\n  position: relative;\n  border-radius: 32px;\n  background-color: transparent;\n  border: 1px solid ",";\n\n  color: ",";\n\n  overflow: hidden;\n  box-sizing: content-box;\n  height: 32px;\n  width: 48px;\n  \n  opacity: ",";\n\n  isolation: isolate;\n\n  .icn-arrow {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 100%;\n    width: 100%;\n    border-radius: 32px;\n  }\n\n  .icn-arrow:first-child {\n    transform: rotate(",");\n\n    * {\n      fill: ",";\n    }\n  }\n\n  .icn-arrow:last-child {\n    position: absolute;\n    top: 0;\n    background-color: ",";\n    transform: rotate(",") translateX(-100%);\n\n    transition: transform 350ms ease-out;\n\n    * {\n      fill: ",";\n    }\n  }\n\n  &:active {\n    border-color: var(--brand-color);\n    * {\n      fill: var(--brand-color);\n    }\n  }\n\n  @media "," {\n    &:active {\n      border-color: ",";\n\n      * {\n        fill: black;\n      }\n    }\n\n    &:hover {\n      .icn-arrow:last-child {\n        transform: rotate(",") translateX(",");\n      }\n\n      cursor: ",";\n    }\n  }\n"]);return AnimatedArrowButton_templateObject=function(){return n},n}function AnimatedArrowButton(n){let{reverse:r,onClickFunction:i,theme:o,disabled:u}=n;return(0,a.jsxs)(x,{theme:o,reverse:r,disabled:u,onClick:n=>i(n),children:[(0,a.jsx)(long_arrow,{}),(0,a.jsx)(long_arrow,{})]})}let x=u.ZP.button.withConfig({componentId:"sc-e439b357-0"})(AnimatedArrowButton_templateObject(),n=>"dark"===n.theme?"var(--c-soft-cream)":"#161616",n=>"dark"===n.theme?"var(--c-soft-cream)":"#161616",n=>n.disabled?.5:1,n=>n.reverse?"180deg":"0",n=>"dark"===n.theme?"var(--c-soft-cream)":"var(--soft-dark)",n=>"dark"===n.theme?"var(--c-soft-cream)":"var(--soft-dark)",n=>n.reverse?"180deg":"0",n=>"dark"===n.theme?"var(--soft-dark)":"var(--c-soft-cream)",d.U.desktop,n=>"dark"===n.theme?"var(--c-soft-cream)":"var(--soft-dark)",n=>n.reverse?"180deg":"0",n=>n.disabled?"-100%":"0",n=>n.disabled?"default":"pointer");var w=i(4629)},1261:function(n,r,i){"use strict";i.d(r,{Z:function(){return CondLink}});var o=i(5893),a=i(5158),u=i(1664),p=i.n(u);function CondLink(n){let{link:r,children:i}=n;return(0,a.tm)(r)?(0,o.jsx)("a",{target:"_blank",rel:"noreferrer",href:r,children:i}):(0,o.jsx)(p(),{href:r,children:i})}},2053:function(n,r,i){"use strict";i.d(r,{Z:function(){return BigCta}});var o=i(2729),a=i(5893),u=i(9521),p=i(1664),d=i.n(p),x=i(5158),w=i(7421);function _templateObject(){let n=(0,o._)(["\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 10px 40px;\n  border-radius: 100px;\n  border: 1px solid ",";\n  width: ",";\n\n  /* Corrects font smoothing for webkit */\n  -webkit-font-smoothing: inherit;\n  -moz-osx-font-smoothing: inherit;\n\n  /* Corrects inability to style clickable `input` types in iOS */\n  -webkit-appearance: none;\n  \n  background-color: ",";\n  \n  p {\n    margin: 0;\n    font-size: 18px;\n    font-weight: 500;\n    font-family: Switzer, sans-serif;\n    color: ",";\n  }\n  \n  cursor: pointer;\n  \n  &:hover {\n    background-color: var(--c-brand-lighter);\n    P {\n      color: var(--c-soft-cream);\n    }\n  }\n  \n  @media "," {\n    width: inherit;\n  }\n"],["\r\n  position: relative;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 10px 40px;\r\n  border-radius: 100px;\r\n  border: 1px solid ",";\r\n  width: ",";\r\n\r\n  /* Corrects font smoothing for webkit */\r\n  -webkit-font-smoothing: inherit;\r\n  -moz-osx-font-smoothing: inherit;\r\n\r\n  /* Corrects inability to style clickable \\`input\\` types in iOS */\r\n  -webkit-appearance: none;\r\n  \r\n  background-color: ",";\r\n  \r\n  p {\r\n    margin: 0;\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    font-family: Switzer, sans-serif;\r\n    color: ",";\r\n  }\r\n  \r\n  cursor: pointer;\r\n  \r\n  &:hover {\r\n    background-color: var(--c-brand-lighter);\r\n    P {\r\n      color: var(--c-soft-cream);\r\n    }\r\n  }\r\n  \r\n  @media "," {\r\n    width: inherit;\r\n  }\r\n"]);return _templateObject=function(){return n},n}function BigCta(n){let{text:r,link:i,onClickFunction:o,theme:u,outline:p=!1,fullWidth:w=!0}=n,E=(0,a.jsx)(k,{fullWidth:w,text:r,theme:u,textColor:p?"dark"===u?"var(--c-dark-green)":"var(--c-soft-cream)":"dark"===u?"var(--c-soft-cream)":"var(--c-dark-green)",backgroundColor:p?"transparent":"dark"===u?"var(--c-dark-green)":"var(--c-soft-cream)",className:"cta-big",outline:p,children:(0,a.jsx)("p",{children:r})});return(0,x.tm)(i)?(0,a.jsx)("a",{href:i,rel:"noopener noreferrer",onClick:o||null,target:"_blank",children:E}):(0,a.jsx)(d(),{href:i,onClick:o||null,children:E})}let k=u.ZP.button.withConfig({componentId:"sc-a61d8ff1-0"})(_templateObject(),n=>n.outline?n.textColor:"transparent",n=>n.fullWidth?"100%":"auto",n=>n.backgroundColor,n=>n.textColor,w.U.tablet)},9246:function(n,r,i){"use strict";var o=i(5893);r.Z=()=>(0,o.jsx)("svg",{width:"20",height:"11",viewBox:"0 0 20 11",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,o.jsx)("path",{d:"M19 1L10 10L1 1",stroke:"black",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})})},1510:function(n,r,i){"use strict";i.d(r,{F:function(){return CoreDataProvider},o:function(){return useCoreData}});var o=i(5893),a=i(7294);let u=(0,a.createContext)(null);function CoreDataProvider(n){let{children:r}=n,[i,p]=(0,a.useState)({authors:[],blogs:[],topics:[],podcasts:[],topicGroups:[]});return(0,a.useEffect)(()=>{let fetchCoreData=async()=>{let n=await fetch("/api/coredata"),r=await n.json();p(r)};fetchCoreData().catch(console.error)},[]),i&&(0,o.jsx)(u.Provider,{value:i,children:r})}function useCoreData(){let n=(0,a.useContext)(u);if(null===n)throw Error("useCoreData must be used within a CoreDataProvider");return n}},628:function(n,r){"use strict";var i,o;Object.defineProperty(r,"__esModule",{value:!0}),function(n,r){for(var i in r)Object.defineProperty(n,i,{enumerable:!0,get:r[i]})}(r,{PrefetchKind:function(){return i},ACTION_REFRESH:function(){return a},ACTION_NAVIGATE:function(){return u},ACTION_RESTORE:function(){return p},ACTION_SERVER_PATCH:function(){return d},ACTION_PREFETCH:function(){return x},ACTION_FAST_REFRESH:function(){return w},ACTION_SERVER_ACTION:function(){return k}});let a="refresh",u="navigate",p="restore",d="server-patch",x="prefetch",w="fast-refresh",k="server-action";(o=i||(i={})).AUTO="auto",o.FULL="full",o.TEMPORARY="temporary",("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),n.exports=r.default)},7567:function(n,r,i){"use strict";function getDomainLocale(n,r,i,o){return!1}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getDomainLocale",{enumerable:!0,get:function(){return getDomainLocale}}),i(2955),("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),n.exports=r.default)},2715:function(n,r,i){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"Image",{enumerable:!0,get:function(){return R}});let o=i(8754),a=i(1757),u=a._(i(7294)),p=o._(i(3935)),d=o._(i(46)),x=i(3560),w=i(7599),k=i(4955);i(9941);let E=i(637),C=o._(i(7791)),D={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function handleLoading(n,r,i,o,a,u){let p=null==n?void 0:n.src;if(!n||n["data-loaded-src"]===p)return;n["data-loaded-src"]=p;let d="decode"in n?n.decode():Promise.resolve();d.catch(()=>{}).then(()=>{if(n.parentElement&&n.isConnected){if("empty"!==r&&a(!0),null==i?void 0:i.current){let r=new Event("load");Object.defineProperty(r,"target",{writable:!1,value:n});let o=!1,a=!1;i.current({...r,nativeEvent:r,currentTarget:n,target:n,isDefaultPrevented:()=>o,isPropagationStopped:()=>a,persist:()=>{},preventDefault:()=>{o=!0,r.preventDefault()},stopPropagation:()=>{a=!0,r.stopPropagation()}})}(null==o?void 0:o.current)&&o.current(n)}})}function getDynamicProps(n){let[r,i]=u.version.split("."),o=parseInt(r,10),a=parseInt(i,10);return o>18||18===o&&a>=3?{fetchPriority:n}:{fetchpriority:n}}let I=(0,u.forwardRef)((n,r)=>{let{src:i,srcSet:o,sizes:a,height:p,width:d,decoding:x,className:w,style:k,fetchPriority:E,placeholder:C,loading:D,unoptimized:I,fill:R,onLoadRef:V,onLoadingCompleteRef:K,setBlurComplete:W,setShowAltText:G,onLoad:J,onError:et,...en}=n;return u.default.createElement("img",{...en,...getDynamicProps(E),loading:D,width:d,height:p,decoding:x,"data-nimg":R?"fill":"1",className:w,style:k,sizes:a,srcSet:o,src:i,ref:(0,u.useCallback)(n=>{r&&("function"==typeof r?r(n):"object"==typeof r&&(r.current=n)),n&&(et&&(n.src=n.src),n.complete&&handleLoading(n,C,V,K,W,I))},[i,C,V,K,W,et,I,r]),onLoad:n=>{let r=n.currentTarget;handleLoading(r,C,V,K,W,I)},onError:n=>{G(!0),"empty"!==C&&W(!0),et&&et(n)}})});function ImagePreload(n){let{isAppRouter:r,imgAttributes:i}=n,o={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...getDynamicProps(i.fetchPriority)};return r&&p.default.preload?(p.default.preload(i.src,o),null):u.default.createElement(d.default,null,u.default.createElement("link",{key:"__nimg-"+i.src+i.srcSet+i.sizes,rel:"preload",href:i.srcSet?void 0:i.src,...o}))}let R=(0,u.forwardRef)((n,r)=>{let i=(0,u.useContext)(E.RouterContext),o=(0,u.useContext)(k.ImageConfigContext),a=(0,u.useMemo)(()=>{let n=D||o||w.imageConfigDefault,r=[...n.deviceSizes,...n.imageSizes].sort((n,r)=>n-r),i=n.deviceSizes.sort((n,r)=>n-r);return{...n,allSizes:r,deviceSizes:i}},[o]),{onLoad:p,onLoadingComplete:d}=n,R=(0,u.useRef)(p);(0,u.useEffect)(()=>{R.current=p},[p]);let V=(0,u.useRef)(d);(0,u.useEffect)(()=>{V.current=d},[d]);let[K,W]=(0,u.useState)(!1),[G,J]=(0,u.useState)(!1),{props:et,meta:en}=(0,x.getImgProps)(n,{defaultLoader:C.default,imgConf:a,blurComplete:K,showAltText:G});return u.default.createElement(u.default.Fragment,null,u.default.createElement(I,{...et,unoptimized:en.unoptimized,placeholder:en.placeholder,fill:en.fill,onLoadRef:R,onLoadingCompleteRef:V,setBlurComplete:W,setShowAltText:J,ref:r}),en.priority?u.default.createElement(ImagePreload,{isAppRouter:!i,imgAttributes:et}):null)});("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),n.exports=r.default)},4520:function(n,r,i){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return W}});let o=i(8754),a=o._(i(7294)),u=i(4785),p=i(4131),d=i(3833),x=i(1306),w=i(2048),k=i(637),E=i(1158),C=i(5352),D=i(7567),I=i(5864),R=i(628),V=new Set;function prefetch(n,r,i,o,a,u){if(!u&&!(0,p.isLocalURL)(r))return;if(!o.bypassPrefetchedCheck){let a=void 0!==o.locale?o.locale:"locale"in n?n.locale:void 0,u=r+"%"+i+"%"+a;if(V.has(u))return;V.add(u)}let d=u?n.prefetch(r,a):n.prefetch(r,i,o);Promise.resolve(d).catch(n=>{})}function formatStringOrUrl(n){return"string"==typeof n?n:(0,d.formatUrl)(n)}let K=a.default.forwardRef(function(n,r){let i,o;let{href:d,as:V,children:K,prefetch:W=null,passHref:G,replace:J,shallow:et,scroll:en,locale:er,onClick:ei,onMouseEnter:eo,onTouchStart:es,legacyBehavior:ec=!1,...el}=n;i=K,ec&&("string"==typeof i||"number"==typeof i)&&(i=a.default.createElement("a",null,i));let eu=a.default.useContext(k.RouterContext),ep=a.default.useContext(E.AppRouterContext),ef=null!=eu?eu:ep,ed=!eu,eh=!1!==W,em=null===W?R.PrefetchKind.AUTO:R.PrefetchKind.FULL,{href:ey,as:ev}=a.default.useMemo(()=>{if(!eu){let n=formatStringOrUrl(d);return{href:n,as:V?formatStringOrUrl(V):n}}let[n,r]=(0,u.resolveHref)(eu,d,!0);return{href:n,as:V?(0,u.resolveHref)(eu,V):r||n}},[eu,d,V]),eg=a.default.useRef(ey),eb=a.default.useRef(ev);ec&&(o=a.default.Children.only(i));let e_=ec?o&&"object"==typeof o&&o.ref:r,[ex,ew,ek]=(0,C.useIntersection)({rootMargin:"200px"}),eS=a.default.useCallback(n=>{(eb.current!==ev||eg.current!==ey)&&(ek(),eb.current=ev,eg.current=ey),ex(n),e_&&("function"==typeof e_?e_(n):"object"==typeof e_&&(e_.current=n))},[ev,e_,ey,ek,ex]);a.default.useEffect(()=>{ef&&ew&&eh&&prefetch(ef,ey,ev,{locale:er},{kind:em},ed)},[ev,ey,ew,er,eh,null==eu?void 0:eu.locale,ef,ed,em]);let eO={ref:eS,onClick(n){ec||"function"!=typeof ei||ei(n),ec&&o.props&&"function"==typeof o.props.onClick&&o.props.onClick(n),ef&&!n.defaultPrevented&&function(n,r,i,o,u,d,x,w,k,E){let{nodeName:C}=n.currentTarget,D="A"===C.toUpperCase();if(D&&(function(n){let r=n.currentTarget,i=r.getAttribute("target");return i&&"_self"!==i||n.metaKey||n.ctrlKey||n.shiftKey||n.altKey||n.nativeEvent&&2===n.nativeEvent.which}(n)||!k&&!(0,p.isLocalURL)(i)))return;n.preventDefault();let navigate=()=>{let n=null==x||x;"beforePopState"in r?r[u?"replace":"push"](i,o,{shallow:d,locale:w,scroll:n}):r[u?"replace":"push"](o||i,{forceOptimisticNavigation:!E,scroll:n})};k?a.default.startTransition(navigate):navigate()}(n,ef,ey,ev,J,et,en,er,ed,eh)},onMouseEnter(n){ec||"function"!=typeof eo||eo(n),ec&&o.props&&"function"==typeof o.props.onMouseEnter&&o.props.onMouseEnter(n),ef&&(eh||!ed)&&prefetch(ef,ey,ev,{locale:er,priority:!0,bypassPrefetchedCheck:!0},{kind:em},ed)},onTouchStart(n){ec||"function"!=typeof es||es(n),ec&&o.props&&"function"==typeof o.props.onTouchStart&&o.props.onTouchStart(n),ef&&(eh||!ed)&&prefetch(ef,ey,ev,{locale:er,priority:!0,bypassPrefetchedCheck:!0},{kind:em},ed)}};if((0,x.isAbsoluteUrl)(ev))eO.href=ev;else if(!ec||G||"a"===o.type&&!("href"in o.props)){let n=void 0!==er?er:null==eu?void 0:eu.locale,r=(null==eu?void 0:eu.isLocaleDomain)&&(0,D.getDomainLocale)(ev,n,null==eu?void 0:eu.locales,null==eu?void 0:eu.domainLocales);eO.href=r||(0,I.addBasePath)((0,w.addLocale)(ev,n,null==eu?void 0:eu.defaultLocale))}return ec?a.default.cloneElement(o,eO):a.default.createElement("a",{...el,...eO},i)}),W=K;("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),n.exports=r.default)},5352:function(n,r,i){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useIntersection",{enumerable:!0,get:function(){return useIntersection}});let o=i(7294),a=i(3767),u="function"==typeof IntersectionObserver,p=new Map,d=[];function useIntersection(n){let{rootRef:r,rootMargin:i,disabled:x}=n,w=x||!u,[k,E]=(0,o.useState)(!1),C=(0,o.useRef)(null),D=(0,o.useCallback)(n=>{C.current=n},[]);(0,o.useEffect)(()=>{if(u){if(w||k)return;let n=C.current;if(n&&n.tagName){let o=function(n,r,i){let{id:o,observer:a,elements:u}=function(n){let r;let i={root:n.root||null,margin:n.rootMargin||""},o=d.find(n=>n.root===i.root&&n.margin===i.margin);if(o&&(r=p.get(o)))return r;let a=new Map,u=new IntersectionObserver(n=>{n.forEach(n=>{let r=a.get(n.target),i=n.isIntersecting||n.intersectionRatio>0;r&&i&&r(i)})},n);return r={id:i,observer:u,elements:a},d.push(i),p.set(i,r),r}(i);return u.set(n,r),a.observe(n),function(){if(u.delete(n),a.unobserve(n),0===u.size){a.disconnect(),p.delete(o);let n=d.findIndex(n=>n.root===o.root&&n.margin===o.margin);n>-1&&d.splice(n,1)}}}(n,n=>n&&E(n),{root:null==r?void 0:r.current,rootMargin:i});return o}}else if(!k){let n=(0,a.requestIdleCallback)(()=>E(!0));return()=>(0,a.cancelIdleCallback)(n)}},[w,i,r,k,C.current]);let I=(0,o.useCallback)(()=>{E(!1)},[]);return[D,k,I]}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),n.exports=r.default)},3560:function(n,r,i){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getImgProps",{enumerable:!0,get:function(){return getImgProps}}),i(9941);let o=i(1235),a=i(7599);function isStaticRequire(n){return void 0!==n.default}function getInt(n){return void 0===n?n:"number"==typeof n?Number.isFinite(n)?n:NaN:"string"==typeof n&&/^[0-9]+$/.test(n)?parseInt(n,10):NaN}function getImgProps(n,r){var i;let u,p,d,{src:x,sizes:w,unoptimized:k=!1,priority:E=!1,loading:C,className:D,quality:I,width:R,height:V,fill:K=!1,style:W,onLoad:G,onLoadingComplete:J,placeholder:et="empty",blurDataURL:en,fetchPriority:er,layout:ei,objectFit:eo,objectPosition:es,lazyBoundary:ec,lazyRoot:el,...eu}=n,{imgConf:ep,showAltText:ef,blurComplete:ed,defaultLoader:eh}=r,em=ep||a.imageConfigDefault;if("allSizes"in em)u=em;else{let n=[...em.deviceSizes,...em.imageSizes].sort((n,r)=>n-r),r=em.deviceSizes.sort((n,r)=>n-r);u={...em,allSizes:n,deviceSizes:r}}let ey=eu.loader||eh;delete eu.loader,delete eu.srcSet;let ev="__next_img_default"in ey;if(ev){if("custom"===u.loader)throw Error('Image with src "'+x+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let n=ey;ey=r=>{let{config:i,...o}=r;return n(o)}}if(ei){"fill"===ei&&(K=!0);let n={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[ei];n&&(W={...W,...n});let r={responsive:"100vw",fill:"100vw"}[ei];r&&!w&&(w=r)}let eg="",eb=getInt(R),e_=getInt(V);if("object"==typeof(i=x)&&(isStaticRequire(i)||void 0!==i.src)){let n=isStaticRequire(x)?x.default:x;if(!n.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(n));if(!n.height||!n.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(n));if(p=n.blurWidth,d=n.blurHeight,en=en||n.blurDataURL,eg=n.src,!K){if(eb||e_){if(eb&&!e_){let r=eb/n.width;e_=Math.round(n.height*r)}else if(!eb&&e_){let r=e_/n.height;eb=Math.round(n.width*r)}}else eb=n.width,e_=n.height}}let ex=!E&&("lazy"===C||void 0===C);(!(x="string"==typeof x?x:eg)||x.startsWith("data:")||x.startsWith("blob:"))&&(k=!0,ex=!1),u.unoptimized&&(k=!0),ev&&x.endsWith(".svg")&&!u.dangerouslyAllowSVG&&(k=!0),E&&(er="high");let ew=getInt(I),ek=Object.assign(K?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:eo,objectPosition:es}:{},ef?{}:{color:"transparent"},W),eS=ed||"empty"===et?null:"blur"===et?'url("data:image/svg+xml;charset=utf-8,'+(0,o.getImageBlurSvg)({widthInt:eb,heightInt:e_,blurWidth:p,blurHeight:d,blurDataURL:en||"",objectFit:ek.objectFit})+'")':'url("'+et+'")',eO=eS?{backgroundSize:ek.objectFit||"cover",backgroundPosition:ek.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:eS}:{},eE=function(n){let{config:r,src:i,unoptimized:o,width:a,quality:u,sizes:p,loader:d}=n;if(o)return{src:i,srcSet:void 0,sizes:void 0};let{widths:x,kind:w}=function(n,r,i){let{deviceSizes:o,allSizes:a}=n;if(i){let n=/(^|\s)(1?\d?\d)vw/g,r=[];for(let o;o=n.exec(i);o)r.push(parseInt(o[2]));if(r.length){let n=.01*Math.min(...r);return{widths:a.filter(r=>r>=o[0]*n),kind:"w"}}return{widths:a,kind:"w"}}if("number"!=typeof r)return{widths:o,kind:"w"};let u=[...new Set([r,2*r].map(n=>a.find(r=>r>=n)||a[a.length-1]))];return{widths:u,kind:"x"}}(r,a,p),k=x.length-1;return{sizes:p||"w"!==w?p:"100vw",srcSet:x.map((n,o)=>d({config:r,src:i,quality:u,width:n})+" "+("w"===w?n:o+1)+w).join(", "),src:d({config:r,src:i,quality:u,width:x[k]})}}({config:u,src:x,unoptimized:k,width:eb,quality:ew,sizes:w,loader:ey}),eC={...eu,loading:ex?"lazy":C,fetchPriority:er,width:eb,height:e_,decoding:"async",className:D,style:{...ek,...eO},sizes:eE.sizes,srcSet:eE.srcSet,src:eE.src},ej={unoptimized:k,priority:E,placeholder:et,fill:K};return{props:eC,meta:ej}}},1235:function(n,r){"use strict";function getImageBlurSvg(n){let{widthInt:r,heightInt:i,blurWidth:o,blurHeight:a,blurDataURL:u,objectFit:p}=n,d=o?40*o:r,x=a?40*a:i,w=d&&x?"viewBox='0 0 "+d+" "+x+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+w+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(w?"none":"contain"===p?"xMidYMid":"cover"===p?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+u+"'/%3E%3C/svg%3E"}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getImageBlurSvg",{enumerable:!0,get:function(){return getImageBlurSvg}})},2647:function(n,r,i){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(n,r){for(var i in r)Object.defineProperty(n,i,{enumerable:!0,get:r[i]})}(r,{unstable_getImgProps:function(){return unstable_getImgProps},default:function(){return x}});let o=i(8754),a=i(3560),u=i(9941),p=i(2715),d=o._(i(7791)),unstable_getImgProps=n=>{(0,u.warnOnce)("Warning: unstable_getImgProps() is experimental and may change or be removed at any time. Use at your own risk.");let{props:r}=(0,a.getImgProps)(n,{defaultLoader:d.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[n,i]of Object.entries(r))void 0===i&&delete r[n];return{props:r}},x=p.Image},7791:function(n,r){"use strict";function defaultLoader(n){let{config:r,src:i,width:o,quality:a}=n;return r.path+"?url="+encodeURIComponent(i)+"&w="+o+"&q="+(a||75)}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return i}}),defaultLoader.__next_img_default=!0;let i=defaultLoader},4722:function(n,r,i){"use strict";i.r(r),i.d(r,{default:function(){return _app}});var o,a=i(5893),u=i(1510),p=i(2729),d=i(9521),x=i(7294);let w=(0,x.createContext)({}),HeaderProvider=n=>{let{children:r}=n,[i,o]=(0,x.useState)({showMenu:!1,dropDownOpen:!1,dropDownKey:"blogs"});return(0,a.jsx)(w.Provider,{value:{headerState:i,toggleMenu:function(){let n=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=document.body;r.classList.toggle("no-scroll",!i.showMenu),n?(o({...i,showMenu:!1}),r.classList.toggle("no-scroll",!1)):o({...i,showMenu:!i.showMenu})},onDropDownButtonClick:n=>{i.dropDownKey!==n?o(r=>({...r,dropDownOpen:!0,dropDownKey:n})):o(r=>({...r,dropDownOpen:!i.dropDownOpen,dropDownKey:n}))},onDropDownClickOutside:()=>{o(n=>({...n,dropDownOpen:!1}))}},children:r})};var k=i(1664),E=i.n(k),C=i(5675),D=i.n(C);function _templateObject(){let n=(0,p._)(["\n  position: relative;\n  height: 25px;\n  width: calc(25px * 2.09);\n  margin-left: var(--border-space);\n"]);return _templateObject=function(){return n},n}function Logo(n){let{white:r}=n,{toggleMenu:i}=(0,x.useContext)(w);return(0,a.jsx)(E(),{href:"/",children:(0,a.jsx)(I,{onClick:()=>i(!0),children:(0,a.jsx)(D(),{src:!0===r?"/images/tpsg-logo-white.svg":"/images/tpsg-logo.svg",alt:"LOGO_TPSG",sizes:"100px",fill:!0})})})}let I=d.ZP.div.withConfig({componentId:"sc-4bff2d1c-0"})(_templateObject());var R=i(7421);function MenuButtons_templateObject(){let n=(0,p._)(["\n  position: relative;\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  height: 101%;\n  \n  padding-right: var(--border-space);\n  padding-left: 32px;\n  flex-direction: row;\n  gap: 8px;\n  \n  //border-bottom: ",";\n  transition-delay: 300ms;\n  \n  svg {\n    path {\n      fill: ",";\n      transition: fill 450ms ease-in-out;\n    }\n  }\n  \n  .menu-icon {\n    display: flex;\n    flex-direction: column;\n    align-items: end;\n    gap: ","px;\n    transition: all 250ms ease-in-out;\n    width: 32px;\n    hr {\n      transform-origin: right;\n      color: ",";\n      border: 1.2px solid ",";\n      margin: 0;\n      transition: all 250ms ease-in-out;\n    }\n    hr:nth-child(1) {\n      width: ","px;\n      transform: ",";\n    }\n    hr:nth-child(2){\n      width: ","px;\n      opacity: ",";\n    }\n    hr:nth-child(3){\n      width: 24px;\n      transform: ",";\n    }\n  }\n  \n  @media "," {\n    //margin-right: 15px;\n  }\n"]);return MenuButtons_templateObject=function(){return n},n}function _templateObject1(){let n=(0,p._)(["\n  outline: none;\n  border: none;\n  display: block;\n  background-color: transparent;\n  cursor: pointer;\n  height: 48px;\n  width: 48px;\n  \n  &:hover {\n    * {\n      path {\n        fill: var(--brand-color);\n      }\n    }\n    * {\n      hr {\n        color: var(--brand-color);\n        border-color: var(--brand-color);\n      }\n    }\n  }\n"]);return _templateObject1=function(){return n},n}function MenuButtons(n){let{invert:r}=n,{headerState:i,toggleMenu:o,onDropDownClickOutside:u}=(0,x.useContext)(w);return(0,a.jsxs)(V,{invert:r,menuOpen:i.showMenu,children:[(0,a.jsx)(E(),{href:"/recherche",children:(0,a.jsx)(K,{onClick:()=>{o(!0),u()},children:(0,a.jsx)(SearchIcon,{})})}),(0,a.jsx)(K,{onClick:()=>{o(),u()},children:(0,a.jsx)(MenuIcon,{})})]})}let V=d.ZP.div.withConfig({componentId:"sc-fcd954af-0"})(MenuButtons_templateObject(),n=>n.menuOpen?"1px solid #39474D":"1px solid transparent",n=>n.menuOpen||n.invert?"var(--c-cream)":"black",n=>n.menuOpen?5:3,n=>n.menuOpen||n.invert?"var(--c-cream)":"black",n=>n.menuOpen||n.invert?"var(--c-cream)":"black",n=>n.menuOpen?24:27,n=>n.menuOpen?"rotate(-35deg)":"rotate(0)",n=>n.menuOpen?0:21,n=>n.menuOpen?0:1,n=>n.menuOpen?"rotate(35deg)":"rotate(0)",R.U.desktop),K=d.ZP.button.withConfig({componentId:"sc-fcd954af-1"})(_templateObject1()),SearchIcon=()=>(0,a.jsx)("svg",{width:"20",height:"19",viewBox:"0 0 20 19",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.2104 8.31546C15.2104 11.7473 12.4283 14.5294 8.99641 14.5294C5.56455 14.5294 2.78247 11.7473 2.78247 8.31546C2.78247 4.8836 5.56455 2.10152 8.99641 2.10152C12.4283 2.10152 15.2104 4.8836 15.2104 8.31546ZM14.0588 14.7844C12.6637 15.8776 10.9062 16.5294 8.99641 16.5294C4.45998 16.5294 0.782471 12.8519 0.782471 8.31546C0.782471 3.77903 4.45998 0.101524 8.99641 0.101524C13.5328 0.101524 17.2104 3.77903 17.2104 8.31546C17.2104 10.2213 16.5612 11.9756 15.4721 13.3693L19.6182 17.5154L18.204 18.9296L14.0588 14.7844Z",fill:"#161616"})}),MenuIcon=()=>(0,a.jsxs)("div",{className:"menu-icon",children:[(0,a.jsx)("hr",{}),(0,a.jsx)("hr",{}),(0,a.jsx)("hr",{})]});function NavigationBar_templateObject(){let n=(0,p._)(["\n  display: flex;\n  width: 100%;\n  height: 80px;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-between;\n  border-bottom: 1px solid rgba(0,0,0,0.20);\n\n  .animated-buttons-line {\n    position: absolute;\n    right: 0;\n    top: 70px;\n    z-index: 2100;\n    border: ",";\n    width: 100%;\n    transition: all 600ms ease-in-out;\n\n    @media "," {\n      right: 15px;\n      width: ",";\n    }\n  }\n  \n  .animated-background {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n    background-color: var(--blue-dark);\n    transform: ",";\n    transition: transform 450ms ease-in-out;\n    z-index: 2100;\n\n    @media "," {\n      display: none;\n    }\n  }\n  \n\n"]);return NavigationBar_templateObject=function(){return n},n}function NavigationBar_templateObject1(){let n=(0,p._)(['\n\n  position: relative;\n  height: 100%;\n  display: flex;\n  align-items: center;\n\n  font-family: "Switzer", "Helvetica Neue", Helvetica, sans-serif;\n  font-size: 16px;\n  font-weight: 500;\n\n  ul li {\n    list-style: none;\n    display: none;\n    @media '," {\n      display: inline;\n    }\n  }\n  li {\n    margin-left: 32px;\n    cursor: pointer;\n    color: ",";\n  }\n  \n  .highlight-top-menu {\n    color: ",";\n  }\n  \n  .nav-v-separator {\n    height: 100%;\n    margin: 0 0 0 32px;\n    width: 0;\n    border-left: 1px solid rgba(0,0,0,0.2);\n  }\n  \n"]);return NavigationBar_templateObject1=function(){return n},n}function NavigationBar(n){let{invert:r}=n,i=(0,x.useRef)(null),{headerState:o,onDropDownButtonClick:u,onDropDownClickOutside:p}=(0,x.useContext)(w);return(0,x.useEffect)(()=>{let handleClickOutside=n=>{i.current&&!i.current.contains(n.target)&&o.dropDownOpen&&(p(),n.stopPropagation())};return document&&document.addEventListener("click",handleClickOutside,!0),()=>{document&&document.removeEventListener("click",handleClickOutside,!0)}},[o]),(0,a.jsxs)(W,{menuOpen:o.showMenu,invert:r,children:[(0,a.jsx)("hr",{className:"animated-buttons-line"}),(0,a.jsx)(Logo,{white:r}),(0,a.jsxs)(G,{invert:r,ref:i,children:[(0,a.jsxs)("ul",{children:[(0,a.jsx)("li",{onClick:()=>u("blogs"),children:"Blogs"}),(0,a.jsx)("li",{onClick:()=>p(),children:(0,a.jsx)(E(),{href:"/categories",children:"Th\xe8mes"})}),(0,a.jsx)("li",{onClick:()=>p(),children:(0,a.jsx)(E(),{href:"/formations",children:"Formations"})}),(0,a.jsx)("li",{onClick:()=>u("podcasts"),children:"Podcasts"}),(0,a.jsx)("li",{onClick:()=>p(),children:(0,a.jsx)(E(),{href:"/webinaires",children:"Webinaires"})}),(0,a.jsx)("li",{onClick:()=>p(),className:"highlight-top-menu",children:(0,a.jsx)(E(),{href:"/soutenir",children:"Soutenir"})})]}),(0,a.jsx)("div",{className:"nav-v-separator"}),(0,a.jsx)(MenuButtons,{invert:r})]}),(0,a.jsx)("div",{className:"animated-background"})]})}let W=d.ZP.div.withConfig({componentId:"sc-bc67f75e-0"})(NavigationBar_templateObject(),n=>n.menuOpen?"1px solid #1C2E33":"1px solid transparent",R.U.desktop,n=>n.menuOpen?"calc(var(--border-space) + 98px)":"0",n=>n.menuOpen?"translateY(0)":"translateY(-100%)",R.U.desktop),G=d.ZP.div.withConfig({componentId:"sc-bc67f75e-1"})(NavigationBar_templateObject1(),R.U.desktop,n=>n.invert?"var(--soft-white)":"black",n=>n.invert?"var(--soft-white)":"var(--brand-color)");var J=i(3365);function HeaderDropDown_templateObject(){let n=(0,p._)(["\n  position: relative;\n  margin-top: -1px;\n  width: 100%;\n  height: ","px;\n  //background: linear-gradient(56.8deg, #081921 18.37%, rgba(8, 25, 33, 0.8) 100.63%);\n  background-color: var(--blue-dark);\n  transition: all 350ms ease-out;\n  overflow: hidden;\n  z-index: 2;\n\n  //backdrop-filter: blur(12px);\n  //\n  //-webkit-backface-visibility: hidden; // Safari shit here\n  //-moz-backface-visibility: hidden; // Safari shit here\n  //-webkit-transform: translate3d(0, 0, 0); // Safari shit here\n  //-moz-transform: translate3d(0, 0, 0); // Safari shit here\n\n  .dd-podcasts {\n    margin: 0 0 0 calc(100% - (445px + var(--border-space)));\n    &:before {\n      top: ","px;\n    }\n    li {\n      opacity: ",";\n    }\n  }\n\n  .dd-blogs {\n    margin: 0 0 0 calc(100% - (727px + var(--border-space)));\n    &:before {\n      top: ","px;\n    }\n    li {\n      opacity: ",';\n    }\n  }\n\n  ul {\n    position: absolute;\n    padding: 28px 0;\n\n    &:before {\n      position: absolute;\n      left: 5px;\n      content: "";\n      display: block;\n      width: 20px;\n      height: 20px;\n      background-color: var(--soft-white);\n      transform: rotate(45deg);\n      transition: all 350ms ease-out;\n    }\n  }\n\n  li {\n    font-family: "Switzer", sans-serif;\n    font-size: 16px;\n    margin-top: 14px;\n    color: #F9F1E6;\n    list-style: none;\n    transition: opacity 250ms ease-out;\n\n    &:hover {\n      color: var(--c-brand-lighter);\n      cursor: pointer;\n    }\n  }\n\n']);return HeaderDropDown_templateObject=function(){return n},n}function HeaderDropDown(){let{blogs:n,podcasts:r}=(0,u.o)(),{headerState:i}=(0,x.useContext)(w),o={blogs:n.map(n=>({name:n.blogger.fullName,slug:n.slug,lastName:n.blogger.lastName})).sort((0,J.IQ)("lastName")),podcasts:r.sort((0,J.IQ)("name"))};return o.blogs&&o.podcasts?(0,a.jsxs)(et,{show:i.dropDownOpen,blog:"blogs"===i.dropDownKey,podcast:"podcasts"===i.dropDownKey,podcastHeight:34*o.podcasts.length+80,blogHeight:34*o.blogs.length+80,children:[(0,a.jsx)("ul",{className:"dd-blogs",children:o.blogs.map((n,r)=>(0,a.jsx)("li",{children:(0,a.jsx)(E(),{href:"/blog/".concat(n.slug),children:n.name})},r))}),(0,a.jsx)("ul",{className:"dd-podcasts",children:o.podcasts.map((n,r)=>(0,a.jsx)("li",{children:(0,a.jsx)(E(),{href:"/podcasts/".concat(n.slug),children:n.name})},r))})]}):null}let et=d.ZP.div.withConfig({componentId:"sc-ff9b963e-0"})(HeaderDropDown_templateObject(),n=>n.show?n.podcast?n.podcastHeight:n.blogHeight:0,n=>n.show&&n.podcast?-10:-24,n=>n.blog?0:1,n=>n.show&&n.blog?-10:-24,n=>n.blog?1:0);function Blogs_templateObject(){let n=(0,p._)(['\n  grid-row: 2;\n  grid-column: 1/3;\n  margin-bottom: 64px;\n  padding: 0;\n\n  :before {\n    display: block;\n    position: relative;\n    content: attr(aria-label);\n    font-family: "Switzer", serif;\n    text-transform: uppercase;\n    font-size: 14px;\n    font-weight: 400;\n    margin-bottom: 24px;\n    color: var(--c-cream-A80);\n  }\n\n  li {\n    list-style: none;\n    margin-top: -1px;\n    border-top: 1px solid #1C2E33;\n    border-bottom: 1px solid #1C2E33;\n  }\n\n  p {\n    font-family: "Switzer", serif;\n    font-size: 16px;\n    line-height: 24px;\n    font-weight: 400;\n    letter-spacing: 0.5px;\n    color: var(--c-cream);\n\n    &:before {\n      margin-right: 24px;\n      left: 0;\n      top: 0;\n      content: "";\n      display: inline-block;\n      width: 14px;\n      height: 14px;\n      background-color: var(--c-cream-A20);\n\n      border-radius: 14px;\n    }\n\n    &:hover {\n      cursor: pointer;\n\n      &:before {\n        background-color: #fa7050;\n      }\n    }\n  }\n\n  @media '," {\n    width: 110%;\n    li {\n      width: 110%;\n    }\n  }\n"]);return Blogs_templateObject=function(){return n},n}function Blogs(n){let{title:r,data:i}=n,{toggleMenu:o}=(0,x.useContext)(w);return(0,a.jsx)(en,{"aria-label":r,children:i.sort((0,J.IQ)("lastName")).map((n,r)=>(0,a.jsx)("li",{children:(0,a.jsx)(E(),{href:n.route,children:(0,a.jsx)("p",{onClick:()=>o(),children:n.name})})},r))})}let en=d.ZP.ul.withConfig({componentId:"sc-918972e8-0"})(Blogs_templateObject(),R.U.desktop);var er=i(9246);function Vocations_templateObject(){let n=(0,p._)(['\n  grid-row: 1;\n  grid-column: 1/3;\n  margin-bottom: 64px;\n  padding: 0;\n  list-style: none;\n\n  :before{\n    display: block;\n    position: relative;\n    content:attr(aria-label);\n    font-family: "Switzer", serif;\n    text-transform: uppercase;\n    font-size: 14px;\n    letter-spacing: 0.5px;\n    font-weight: 400;\n    margin-bottom: 24px;\n    color: var(--c-cream-A40);\n  }\n\n  .li {\n    list-style: none;\n  }\n']);return Vocations_templateObject=function(){return n},n}function Vocations_templateObject1(){let n=(0,p._)(['\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-between;\n  margin-top: -1px;\n  border-top: 1px solid #1C2E33;\n  border-bottom: 1px solid #1C2E33;\n\n  p {\n    font-family: "Switzer", "Helvetica Neue", Helvetica, sans-serif;\n    font-size: 16px;\n    line-height: 24px;\n    font-weight: 400;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    color: var(--c-cream);\n\n    &:before {\n      margin-right: 24px;\n      left: 0;\n      top: 0;\n      content: "";\n      display: inline-block;\n      width: 14px;\n      height: 14px;\n      background-color: var(--c-cream-A20);\n      border-radius: 14px;\n    }\n\n    &:hover {\n      cursor: pointer;\n\n      &:before {\n        background-color: #fa7051;\n      }\n    }\n  }\n\n  .chevron-icon {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    height: 48px;\n    width: 32px;\n    border-radius: 26px;\n\n    svg {\n      width: 14px;\n      height: 14px;\n\n      path {\n        stroke: var(--c-cream);\n      }\n    }\n\n    &:hover {\n      cursor: pointer;\n\n      path {\n        stroke: #fa7051;\n        stroke-width: 2.4;\n      }\n    }\n  }\n  \n  @media '," {\n    width: 110%;\n  }\n"]);return Vocations_templateObject1=function(){return n},n}function _templateObject2(){let n=(0,p._)(["\n  height: ","px;\n  transition: 350ms ease-in-out;\n  padding-left: 38px;\n  overflow: hidden;\n  list-style: none;\n  @media "," {\n    width: 110%;\n  }\n"]);return _templateObject2=function(){return n},n}function Vocations_Blogs(n){let{title:r,data:i,row:o}=n,[u,p]=(0,x.useState)(""),{toggleMenu:d}=(0,x.useContext)(w);return(0,a.jsx)(ei,{"aria-label":r,row:o,children:i.map((n,r)=>(0,a.jsxs)("li",{className:"parent-list",children:[(0,a.jsxs)(eo,{children:[(0,a.jsx)(E(),{href:n.route,children:(0,a.jsx)("p",{onClick:()=>d(),children:n.name})}),(0,a.jsx)("div",{onClick:()=>p(n.name===u?null:n.name),className:"chevron-icon",children:(0,a.jsx)(er.Z,{})})]}),(0,a.jsx)(es,{className:"hidden-list",itemCount:n.children.length,show:u!=="".concat(n.name),children:n.children.map((n,r)=>(0,a.jsx)("li",{children:(0,a.jsx)(eo,{children:(0,a.jsx)(E(),{href:n.route,children:(0,a.jsx)("p",{onClick:()=>d(),children:n.name})})})},"hidden-list-"+r))})]},r))})}let ei=d.ZP.ul.withConfig({componentId:"sc-421560ba-0"})(Vocations_templateObject()),eo=d.ZP.div.withConfig({componentId:"sc-421560ba-1"})(Vocations_templateObject1(),R.U.desktop),es=d.ZP.ul.withConfig({componentId:"sc-421560ba-2"})(_templateObject2(),n=>n.show?0:57*n.itemCount,R.U.desktop);var ec=i(4629);function HeaderMenu_templateObject(){let n=(0,p._)(["\n  display: flex;\n  flex-direction: column;\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  visibility: ",";\n  transition-delay: ",";\n  z-index: 2000;\n\n\n  // Hide Scrollbar\n  &::-webkit-scrollbar {\n    display: none; /* Safari and Chrome */\n  }\n\n  -ms-overflow-style: none; /* IE and Edge */\n  scrollbar-width: none; /* Firefox */\n  overflow-y: scroll;\n\n  .dark-filter {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 100%;\n    width: 100%;\n    background-color: orangered;\n    opacity: ",";\n    transition: opacity 800ms;\n  }\n"]);return HeaderMenu_templateObject=function(){return n},n}function HeaderMenu_templateObject1(){let n=(0,p._)(["\n  position: relative;\n  background-color: var(--blue-dark);\n  width: 100vw;\n  height: 100vh;\n  padding: 126px var(--border-space) 64px 0;\n  clip-path: inset(0 0 "," 0);\n  transition: clip-path 600ms cubic-bezier(0.93, 0.03, 0.23, 0.84);\n\n  @media "," {\n    position: absolute;\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    grid-auto-rows: max-content;\n    column-gap: 24px;\n    padding: 126px 0;\n    height: 100vh;\n    width: 50%;\n    left: 0;\n    top: 0;\n    clip-path: inset(0 "," 0 0);\n\n    .menu-buttons {\n      grid-column: 1/4;\n      margin-top: 64px;\n    }\n  }\n\n  .menu-buttons {\n    position: relative;\n    display: flex;\n    flex-direction: row-reverse;\n    gap: 16px;\n  }\n"]);return HeaderMenu_templateObject1=function(){return n},n}function HeaderMenu_templateObject2(){let n=(0,p._)(["\n  position: relative;\n  grid-column: 2/4;\n  height: 100%;\n  ul {\n    padding: 0;\n  }\n"]);return HeaderMenu_templateObject2=function(){return n},n}function _templateObject3(){let n=(0,p._)(["\n  position: relative;\n  width: 100%;\n  text-align: right;\n  list-style: none;\n  font-size: 32px;\n  margin-top: 20px;\n  font-weight: 400;\n  color: var(--c-cream);\n  white-space: nowrap;\n\n  @media "," {\n    font-size: 40px;\n    margin-bottom: 16px;\n    &:hover {\n      color: var(--c-brand-lighter);\n      cursor: pointer;\n    }\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,p._)(["\n  position: relative;\n  width: 100%;\n  background-color: var(--blue-dark);\n  padding: 64px var(--border-space);\n  clip-path: inset("," 0 0 0);\n  transition: clip-path 600ms cubic-bezier(0.93, 0.03, 0.23, 0.84);\n\n  @media "," {\n    position: absolute;\n    right: 0;\n    top: 0;\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    padding: 126px var(--border-space) 126px 0;\n    height: 100vh;\n    width: 51%;\n    overflow-y: scroll;\n    clip-path: inset(0 0 0 ",");\n  }\n"]);return _templateObject4=function(){return n},n}let el=[{name:"Articles",route:"/recherche"},{name:"Th\xe8mes",route:"/categories"},{name:"Formations",route:"/formations"},{name:"Parcours e-mails",route:"/parcours-emails"},{name:"Podcasts",route:"/podcasts"},{name:"Webinaires",route:"/webinaires"}],setChildren=n=>n.map(n=>({route:"/categories/".concat(n.type,"/").concat(n.slug),name:n.name}));function HeaderMenu(){let{headerState:n,toggleMenu:r}=(0,x.useContext)(w),{blogs:i,topicGroups:o}=(0,u.o)(),p=o.filter(n=>{var r;return(null===(r=n.children)||void 0===r?void 0:r.length)>0}).map(n=>({route:"/categories/".concat(n.type,"/").concat(n.slug),name:n.name,children:setChildren(n.children)})),d=i.map(n=>({route:"/blog/".concat(n.slug),name:n.blogger.fullName,lastName:n.blogger.lastName}));return(0,a.jsxs)(eu,{isOpen:n.showMenu,children:[(0,a.jsx)("div",{className:"dark-filter"}),(0,a.jsxs)(ep,{isOpen:n.showMenu,children:[(0,a.jsx)(ef,{children:(0,a.jsx)("ul",{children:el.map((n,i)=>(0,a.jsx)(E(),{href:n.route,children:(0,a.jsx)(ed,{onClick:()=>r(),children:n.name})},i))})}),(0,a.jsxs)("div",{className:"menu-buttons",children:[(0,a.jsx)(ec.Z,{theme:"light",text:"Nous soutenir",link:"/soutenir",onClickFunction:()=>r()}),(0,a.jsx)(ec.Z,{theme:"light",text:"\xc0 propos",link:"/a-propos",onClickFunction:()=>r()})]})]}),(0,a.jsxs)(eh,{isOpen:n.showMenu,children:[(0,a.jsx)(Vocations_Blogs,{title:"vocations",data:p}),(0,a.jsx)(Blogs,{title:"blogs",data:d})]})]})}let eu=d.ZP.div.withConfig({componentId:"sc-f4dd8104-0"})(HeaderMenu_templateObject(),n=>n.isOpen?"visible":"hidden",n=>n.isOpen?"0ms":"600ms",n=>n.isOpen?"1":"0"),ep=d.ZP.div.withConfig({componentId:"sc-f4dd8104-1"})(HeaderMenu_templateObject1(),n=>n.isOpen?"0%":"100%",R.U.desktop,n=>n.isOpen?"0%":"100%"),ef=d.ZP.div.withConfig({componentId:"sc-f4dd8104-2"})(HeaderMenu_templateObject2()),ed=d.ZP.li.withConfig({componentId:"sc-f4dd8104-3"})(_templateObject3(),R.U.tablet),eh=d.ZP.div.withConfig({componentId:"sc-f4dd8104-4"})(_templateObject4(),n=>n.isOpen?"0%":"100%",R.U.desktop,n=>n.isOpen?"0%":"100%");var em=i(1163),ey=i.n(em);function Header_templateObject(){let n=(0,p._)(['\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  z-index: 1400;\n  background-color: "transparent";\n']);return Header_templateObject=function(){return n},n}function Header(){let{pathname:n}=(0,em.useRouter)(),[r,i]=(0,x.useState)(!1);return(0,x.useEffect)(()=>{if(n.includes("ressources")){i(!1);return}n.includes("/[page]")||n.includes("[vocation]")||n.includes("[ministry]")||n.includes("[]")?i(!0):i(!1)},[n]),(0,a.jsx)(HeaderProvider,{children:(0,a.jsxs)(ev,{invert:r,children:[(0,a.jsx)(NavigationBar,{invert:r}),(0,a.jsx)(HeaderDropDown,{}),(0,a.jsx)(HeaderMenu,{})]})})}let ev=d.ZP.div.withConfig({componentId:"sc-6f0c4967-0"})(Header_templateObject());function Footer_templateObject(){let n=(0,p._)(['\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  column-gap: 16px;\n\n  padding: 40px var(--border-space) 120px var(--border-space);\n\n  background-color: var(--c-dark-green);\n  color: #E8E8E5;\n\n  .footer-mission {\n    grid-column: 1 / span 2;\n    grid-row: 1;\n    font-size: 36px;\n    line-height: 36px;\n    margin: 0 0 96px 0;\n  }\n\n  a {\n    display: block;\n    font-family: sans-serif, "Helvetica Neue", Helvetica, Arial;\n    margin: 0 0 8px 0;\n    line-height: 24px;\n    opacity: 0.8;\n    font-weight: 400;\n  }\n\n  a:hover {\n    text-decoration: underline;\n  }\n\n  hr {\n    border-color: transparent;\n    height: 24px;\n  }\n\n  @media '," {\n    grid-template-columns: repeat(8, 1fr);\n    padding-top: 80px;\n    padding-bottom: 80px;\n    .footer-mission {\n      grid-column: 1 / span 6;\n      grid-row: 1;\n      font-size: 48px;\n      line-height: 48px;\n      margin: 0;\n    }\n\n    .footer-links {\n      grid-column: 11 / span 2;\n    }\n  }\n\n  @media "," {\n    grid-template-columns: repeat(12, 1fr);\n    padding-top: 80px;\n    padding-bottom: 80px;\n    .footer-mission {\n      grid-column: 1 / span 6;\n      font-size: 48px;\n      line-height: 48px;\n    }\n\n    .footer-links {\n      grid-column: 11 / span 2;\n    }\n  }\n"]);return Footer_templateObject=function(){return n},n}function Footer(){return(0,a.jsxs)(eg,{children:[(0,a.jsx)("p",{className:"footer-mission",children:"Nous existons pour vous aider \xe0 voir comme Dieu voit pour vivre comme Dieu veut"}),(0,a.jsxs)("div",{className:"footer-links",children:[(0,a.jsx)(E(),{href:"/contact",children:"Contact"}),(0,a.jsx)(E(),{href:"/soutenir",children:"Nous soutenir"}),(0,a.jsx)("hr",{}),(0,a.jsx)(E(),{href:"/a-propos",children:"\xc0 propos"}),(0,a.jsx)(E(),{href:"/ce-que-nous-croyons",children:"Ce que nous croyons"}),(0,a.jsx)(E(),{href:"/equipe-tpsg",children:"\xc9quipe"}),(0,a.jsx)("hr",{}),(0,a.jsx)(E(),{href:"/formations#evenements-tpsg",children:"Camp TPSG"}),(0,a.jsx)(E(),{href:"https://toutpoursagloire.myspreadshop.fr/",children:"Shop"}),(0,a.jsx)("hr",{}),(0,a.jsx)(E(),{href:"/mentions-legales",children:"Mentions l\xe9gales"})]})]})}let eg=d.ZP.footer.withConfig({componentId:"sc-3177e1d6-0"})(Footer_templateObject(),R.U.tablet,R.U.desktop);var eb=i(3847),e_=i(7283);function gql_queries_templateObject(){let n=(0,p._)(["\n  fragment postTypeModules on Post {\n    modules {\n      ... on ComponentModulePodcast {\n        podcast {\n          slug\n          name\n        }\n      }\n      ... on ComponentModuleFormation {\n        __typename\n        speakers {\n          fullName\n        }\n        link\n        youtubeEmbed\n      }\n    }\n  }\n"]);return gql_queries_templateObject=function(){return n},n}function gql_queries_templateObject1(){let n=(0,p._)(["\n    fragment CorePostFields on Post {\n      id\n      title\n      body\n      slug\n      type\n      readingTime\n      author {\n        fullName\n        firstName\n        lastName\n        about\n        slug\n        picture {\n          url\n          width\n          height\n          provider\n          alternativeText\n        }\n      }\n      published_at\n      image {\n        url\n        provider\n        alternativeText\n        caption\n        width\n        height\n      }\n      serie {\n        id\n        name\n      }\n      topics {\n        name\n        slug\n      }\n      blog {\n        blogger {\n          fullName\n          slug\n        }\n      }\n      modules {\n        __typename\n        ... on ComponentModuleLead {\n          content\n        }\n        ... on ComponentModuleSeo {\n          metaDescription\n          metaTitle\n        }\n      }\n    }\n  "]);return gql_queries_templateObject1=function(){return n},n}function gql_queries_templateObject2(){let n=(0,p._)(["\n  fragment fullPostFragment on Post {\n    id\n    title\n    slug\n    type\n    published_at\n    body\n    author {\n      fullName\n      picture {\n        url\n        provider\n      }\n    }\n    image {\n      url\n      height\n      width\n      alternativeText\n      provider\n    }\n    topics {\n      name\n    }\n    modules {\n      ... on ComponentModuleWebinar {\n        __typename\n        webinar {\n          slug\n          name\n        }\n        embedVideo\n        speakers {\n          fullName\n          firstName\n          lastName\n          picture {\n            url\n            provider\n            size\n          }\n        }\n      }\n      ... on ComponentModulePodcast {\n        __typename\n        podcast {\n          slug\n          name\n        }\n        embedAudio\n        embedVideo\n      }\n      ... on ComponentModuleLead {\n        __typename\n        content\n      }\n    }\n  }\n"]);return gql_queries_templateObject2=function(){return n},n}function gql_queries_templateObject3(){let n=(0,p._)(["\n    query MainTopic($slug: String!) {\n      topics(where: { slug: $slug }) {\n        name\n        slug\n        id\n        postCount\n        description\n        parent {\n          slug\n          name\n          parent {\n            slug\n            name\n          }\n        }\n      }\n    }\n  "]);return gql_queries_templateObject3=function(){return n},n}function gql_queries_templateObject4(){let n=(0,p._)(["\n    query TopicGroup($slug: String!) {\n      topicGroups(where: { slug: $slug }) {\n        name\n        description\n        cover {\n          formats\n        }\n        topics {\n          id\n          postCount\n        }\n        featured {\n          title\n          description\n          inColumn\n          image {\n            url\n            width\n            height\n            provider\n            caption\n            alternativeText\n          }\n        }\n      }\n    }\n  "]);return gql_queries_templateObject4=function(){return n},n}function _templateObject5(){let n=(0,p._)(["\n    query Posts($topicIds: [ID], $offset: Int!) {\n      posts(\n        limit: 20\n        start: $offset\n        where: { topics: { id_in: $topicIds } }\n      ) {\n        title\n        slug\n        published_at\n        image {\n          url\n          height\n          width\n          provider\n          caption\n          alternativeText\n        }\n        author {\n          fullName\n        }\n        topics {\n          slug\n        }\n        modules {\n          __typename\n          ... on ComponentModulePodcast {\n            podcast {\n              name\n              slug\n            }\n          }\n        }\n        type\n      }\n    }\n  "]);return _templateObject5=function(){return n},n}function _templateObject6(){let n=(0,p._)(["\n    query GetRelated($id: ID!) {\n      relatedPosts(id: $id) {\n        section\n        score\n        origin\n        post {\n          id\n          title\n          slug\n          type\n          published_at\n          author {\n            fullName\n            picture {\n              url\n              provider\n            }\n          }\n          image {\n            url\n            height\n            width\n            alternativeText\n            provider\n          }\n          topics {\n            name\n          }\n          modules {\n            __typename\n            ... on ComponentModuleWebinar {\n              webinar {\n                slug\n                name\n              }\n              speakers {\n                fullName\n                firstName\n                lastName\n                picture {\n                  url\n                  provider\n                  size\n                }\n              }\n            }\n            ... on ComponentModulePodcast {\n              podcast {\n                slug\n                name\n              }\n            }\n            ... on ComponentModuleLead {\n              content\n            }\n          }\n        }\n      }\n    }\n  "]);return _templateObject6=function(){return n},n}function _templateObject7(){let n=(0,p._)(["\n    ","\n    query BlogPosts($blog: ID!, $limit: Int!, $sort: String!) {\n      posts(limit: $limit, where: { blog: $blog }, sort: $sort) {\n        ...fullPostFragment\n      }\n    }\n  "]);return _templateObject7=function(){return n},n}function _templateObject8(){let n=(0,p._)(["\n    ","\n    query Posts($limit: Int!, $sort: String!) {\n      posts(limit: $limit, sort: $sort) {\n        ...fullPostFragment\n      }\n    }\n  "]);return _templateObject8=function(){return n},n}function _templateObject9(){let n=(0,p._)(["\n  fragment CorePostSet on Post {\n    title\n    slug\n    type\n    published_at\n    topics {\n      name\n    }\n    image {\n      url\n      width\n      height\n      alternativeText\n      provider\n    }\n    author {\n      fullName\n      picture {\n        url\n        formats\n      }\n    }\n    modules {\n      __typename\n      ... on ComponentModulePodcast {\n        podcast {\n          slug\n          name\n        }\n      }\n      ... on ComponentModuleFormation {\n        speakers {\n          fullName\n        }\n        link\n        youtubeEmbed\n      }\n    }\n  }\n"]);return _templateObject9=function(){return n},n}function _templateObject10(){let n=(0,p._)(['\n  query Popups {\n    popups(sort: "published_at:desc") {\n      id\n      title\n      body\n      image {\n        url\n        provider\n      }\n      startDate\n      endDate\n      published_at\n      button {\n        name\n        url\n      }\n    }\n  }\n']);return _templateObject10=function(){return n},n}(0,e_.Ps)(gql_queries_templateObject()),(0,e_.Ps)(gql_queries_templateObject1());let ex=(0,e_.Ps)(gql_queries_templateObject2());(0,e_.Ps)(gql_queries_templateObject3()),(0,e_.Ps)(gql_queries_templateObject4()),(0,e_.Ps)(_templateObject5()),(0,e_.Ps)(_templateObject6()),(0,e_.Ps)(_templateObject7(),ex),(0,e_.Ps)(_templateObject8(),ex),(0,e_.Ps)(_templateObject9());let ew=(0,e_.Ps)(_templateObject10());var ek=i(4218),eS=i(2053),eO=i(3071);function ButtonClose_templateObject(){let n=(0,p._)(["\n		\n		height: 28px;\n		border-radius: 16px;\n		padding: 1px 22px 0 18px;\n		background-color: var(--c-soft-cream);\n		\n		cursor: pointer;\n		\n		p {\n				margin: 0;\n				font-size: 24px;\n				line-height: 32px;\n				transform: rotate(45deg);\n				color: black;\n		}\n		\n		@media "," {\n				&:hover {\n						background-color: var(--c-brand-lighter);\n						p {\n								color: var(--c-soft-cream);\n						}\n				}\n		}\n"]);return ButtonClose_templateObject=function(){return n},n}function ButtonClose(n){let{handleClick:r}=n;return(0,a.jsx)(eE,{onClick:r,children:(0,a.jsx)("p",{children:"+"})})}let eE=d.ZP.div.withConfig({componentId:"sc-44a20ba0-0"})(ButtonClose_templateObject(),R.U.desktop);function Popup_templateObject(){let n=(0,p._)(["\n  position: fixed;\n  bottom: 0;\n  right: 0;\n  padding: 24px;\n  width: calc(100% - 32px);\n  max-height: 100%;\n  margin: 16px;\n  background: linear-gradient(85.13deg, #081D21 15.79%, #1d2b30 104.33%);\n  z-index: 9999;\n\n  .pu-content {\n    position: relative;\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .pu-image-container {\n    position: relative;\n    width: 100%;\n    max-height: 40vh;\n    aspect-ratio: 16/9;\n    margin-top: 66px;\n    /* background-color: var(--c-soft-cream); */\n  }\n\n  .pu-inner-border {\n    position: relative;\n    height: 100%;\n    width: 100%;\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    align-items: center;\n    background-image: none;\n  }\n\n  .pu-right {\n    position: relative;\n    margin-top: 42px;\n    width: 100%;\n    display: flex;\n    align-self: start;\n    flex-direction: column;\n    justify-content: space-between;\n    color: var(--c-soft-cream);\n  }\n\n  .pu-title {\n    font-family: Stelvio, sans-serif;\n    color: var(--c-soft-cream);\n    font-size: 28px;\n    line-height: 32px;\n    margin: 0;\n  }\n\n  .pu-body {\n    font-family: Switzer, sans-serif;\n    margin-top: 6px;\n    font-weight: 400;\n    opacity: 0.9;\n    color: var(--c-soft-cream);\n    font-size: 14px;\n  }\n\n  button {\n    justify-self: flex-end;\n    min-width: 80px;\n    min-height: 40px;\n    margin-right: 24px;\n    margin-top: 42px;\n  }\n\n  .pu-close {\n    position: absolute;\n    top: 24px;\n    right: 24px;\n  }\n\n  @media "," {\n    top: 0;\n    left: 0;\n    width: calc(100% - 2 * (var(--border-space) - 24px));\n    margin: calc(var(--border-space) - 24px);\n\n    .pu-inner-border {\n      background-image: url(\"data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333C41FF' stroke-width='2' stroke-dasharray='4%2c 4' stroke-dashoffset='38' stroke-linecap='butt'/%3e%3c/svg%3e\");\n    }\n\n    .pu-image-container {\n      width: 50%;\n      margin-top: 0;\n      max-height: inherit;\n    }\n\n    .pu-content {\n      margin: 0 8%;\n      width: 100%;\n      flex-direction: row;\n      align-items: center;\n    }\n\n    .pu-title {\n      font-size: 32px;\n      line-height: 36px;\n    }\n\n    .pu-right {\n      width: 50%;\n      margin-top: 0;\n      margin-left: 42px;\n      aspect-ratio: 16/9;\n    }\n\n    .pu-body {\n      margin-top: 24px;\n      font-size: 16px;\n    }\n\n    .pu-button {\n      margin-top: 0;\n    }\n\n    .pu-close {\n      top: 42px;\n      right: 42px;\n    }\n\n  }\n"]);return Popup_templateObject=function(){return n},n}function Popup(){var n;let[r,i]=function(n,r){let[i,o]=(0,x.useState)(()=>{if("undefined"==typeof localStorage)return r;try{let i=localStorage.getItem(n);return i?JSON.parse(i):r}catch(n){return console.log(n),r}});return[i,r=>{try{let a=r instanceof Function?r(i):r;o(a),"undefined"!=typeof localStorage&&localStorage.setItem(n,JSON.stringify(a))}catch(n){console.log(n)}}]}("tpsg-pu",null),[o,u]=(0,x.useState)(!1);function closePopup(){let n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];i({...r,lastView:new Date,engaged:n}),u(!1)}if((0,x.useEffect)(()=>{(async function(){let n=await eb.Z.query({query:ew}).then(n=>{var r;return null==n?void 0:null===(r=n.data)||void 0===r?void 0:r.popups}),o=new Date,a=null==n?void 0:n.filter(n=>o<new Date(n.endDate))[0];(null==a?void 0:a.id)&&a.id!==(null==r?void 0:r.id)&&i({...a,lastView:null,engaged:!1}),u(function(n){if(!n)return!1;let r=new Date,i=(null==n?void 0:n.startDate)?new Date(null==n?void 0:n.startDate):r,o=new Date(null==n?void 0:n.endDate),a=(null==n?void 0:n.lastView)&&new Date(null==n?void 0:n.lastView);return!(null==n?void 0:n.engaged)&&r<o&&r>=i&&(!a||(0,ek.LG)(a,r)>=1)}(r))})()},[]),o)return(0,a.jsxs)(ej,{children:[(0,a.jsx)("div",{className:"pu-inner-border",children:(0,a.jsxs)("div",{className:"pu-content",children:[(0,a.jsx)("div",{className:"pu-image-container",children:(null===(n=r.image)||void 0===n?void 0:n.url)&&(0,a.jsx)(D(),{fill:!0,sizes:"500px",style:eC,src:(0,eO.k)(r.image),alt:""})}),(0,a.jsxs)("div",{className:"pu-right",children:[(0,a.jsxs)("div",{className:"pu-text",children:[(0,a.jsx)("p",{className:"pu-title",children:r.title}),(0,a.jsx)("p",{className:"pu-body",children:r.body})]}),(0,a.jsx)(eS.Z,{onClickFunction:()=>closePopup(!0),text:r.button.name,link:r.button.url})]})]})}),(0,a.jsx)("div",{className:"pu-close",children:(0,a.jsx)(ButtonClose,{handleClick:()=>closePopup()})})]})}let eC={objectFit:"contain"},ej=d.ZP.div.withConfig({componentId:"sc-c883c5c9-0"})(Popup_templateObject(),R.U.desktop);var eD=i(708);function toggle_templateObject(){let n=(0,p._)(["\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  gap: 8px;\n\n  .toggle-button {\n    height: 22px;\n    width: 38px;\n    border: 1.5px solid var(--c-dark-green);\n    background-color: ",";\n    border-radius: 21px;\n    cursor: pointer;\n  }\n  \n  .inner-dot {\n    height: 15px;\n    width: 15px;\n    background-color: ",";\n    border-radius: 15px;\n    margin-top: 2px;\n    margin-left: 2px;\n    transform: translateX(",");\n    transition: all 350ms;\n  }\n  \n  .toggle-label {\n    margin: 0;\n    padding-top: 4px;\n    font-size: 16px;\n    line-height: 21px;\n    font-weight: 500;\n    letter-spacing: 0.4px;\n  }\n"]);return toggle_templateObject=function(){return n},n}function Toggle(n){let{isChecked:r,formKey:i,handleValueChange:o,label:u}=n,[p,d]=(0,x.useState)(r);return(0,a.jsxs)(eT,{checked:p,children:[(0,a.jsx)("div",{className:"toggle-button",onClick:()=>void(o(i,!p),d(!p)),children:(0,a.jsx)("div",{className:"inner-dot"})}),(0,a.jsx)("p",{className:"toggle-label",children:u})]})}let eT=d.ZP.div.withConfig({componentId:"sc-d9158d9d-0"})(toggle_templateObject(),n=>n.checked?"var(--c-dark-green)":"transparent",n=>n.checked?"white":"var(--c-dark-green)",n=>n.checked?"100%":"0%");var eI=i(4256);let useFormData=n=>{let[r,i]=(0,x.useState)({...n});return[r,(n,o)=>{i({...r,[n]:o})},i]};function CookieBanner_templateObject(){let n=(0,p._)(["\n  position: fixed;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n  z-index: 9999;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background-color: rgba(8, 29, 33, 0.96);\n"]);return CookieBanner_templateObject=function(){return n},n}function CookieBanner_templateObject1(){let n=(0,p._)(["\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  overflow: scroll;\n  max-width: 500px;\n  max-height: 66%;\n  box-shadow: 0 4px 16px rgba(23, 22, 22, 0.2);\n  background-color: var(--soft-white);\n\n  p {\n    font-family: Switzer, sans-serif;\n    font-size: 14px;\n    ul {\n      margin-top: 8px;\n      padding-left: 20px;\n    }\n  }\n\n  .cb-top {\n    padding: 32px;\n  }\n\n  .cb-title {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 18px;\n    font-family: Switzer, sans-serif;\n    font-weight: 600;\n  }\n\n  .cb-entries {\n    margin-top: 32px;\n    display: flex;\n    flex-direction: column;\n    gap: 24px;\n  }\n\n  .cb-bottom {\n    position: sticky;\n    bottom: 0;\n    width: 100%;\n    padding: 32px;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    justify-content: space-between;\n    background-color: var(--c-soft-cream);\n  }\n\n  .cb-details-btn {\n    margin: 0;\n    color: #242424;\n    text-decoration: underline;\n    font-family: Switzer, sans-serif;\n    font-weight: 500;\n    letter-spacing: 0.4px;\n    font-size: 14px;\n    cursor: pointer;\n\n    &:hover {\n      color: var(--brand-color);\n    }\n  }\n\n  @media "," {\n    min-width: 500px;\n  }\n\n  @media "," {\n    position: relative;\n    max-height: 100%;\n  }\n"]);return CookieBanner_templateObject1=function(){return n},n}function CookieBanner(){let n=(0,em.useRouter)(),[r,i]=(0,eD.Z)(["preferences"]),[o,u]=(0,x.useState)(!1),[p,d]=useFormData({essentials:!0,analytics:!0,medias:!0,set:!0});return((0,x.useEffect)(()=>{var n;u(!(null===(n=r.preferences)||void 0===n?void 0:n.set))},[r]),o&&"/cookies"!==n.asPath)?(console.log(n.asPath),(0,a.jsx)(eN,{children:(0,a.jsxs)(eA,{children:[(0,a.jsxs)("div",{className:"cb-top",children:[(0,a.jsx)("p",{className:"cb-title",children:"Param\xe8tre des cookies"}),(0,a.jsxs)("div",{className:"cb-entries",children:[(0,a.jsxs)("div",{className:"cb-entry",children:[(0,a.jsx)(Toggle,{isChecked:!0,formKey:"essentials",handleValueChange:d,label:"N\xe9cessaires"}),(0,a.jsxs)("p",{className:"cb-entry-desc",children:["Certains cookies sont n\xe9cessaires au fonctionnement minimal du site toutpoursagloire.com comme :",(0,a.jsxs)("ul",{children:[(0,a.jsxs)("li",{children:["Vos pr\xe9f\xe9rences d’acceptation ou de rejet des cookies"," "]}),(0,a.jsx)("li",{children:"\xc9ventuellement des informations techniques pour le bon affichage des pages"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(Toggle,{isChecked:!0,formKey:"analytics",handleValueChange:d,label:"Statistiques"}),(0,a.jsx)("p",{className:"cb-entry-desc",children:"Ces cookies sont utiles pour nous permettre de vous fournir des ressources (articles, podcasts, webinaires, …) toujours plus adapt\xe9es et pertinentes. Nous utilisons Google Analytics pour \xe7a."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(Toggle,{isChecked:!0,formKey:"medias",handleValueChange:d,label:"Provenance de tiers"}),(0,a.jsxs)("p",{className:"cb-entry-desc",children:["Il s’agit du reste des cookies venant de services externes \xe0 toutpoursagloire.com mais pour autant utiles pour une exp\xe9rience compl\xe8te comme :",(0,a.jsxs)("ul",{children:[(0,a.jsx)("li",{children:"Les services multim\xe9dias pour les podcast et webinaires (YouTube, Spotify, SoundCloud…)"}),(0,a.jsx)("li",{children:"Les services de communication pour les formulaires mails (ConvertKit)"}),(0,a.jsx)("li",{children:"Les services de remont\xe9e de bug pour l'am\xe9lioration continue du site (Marker.io)"})]})]})]})]})]}),(0,a.jsxs)("div",{className:"cb-bottom",children:[(0,a.jsx)("p",{className:"cb-details-btn",children:(0,a.jsx)(E(),{href:"/cookies",children:"Voir les d\xe9tails"})}),(0,a.jsx)(eI.Yz,{text:"Valider",theme:"dark",action:()=>void(i("preferences",p,{sameSite:"strict",path:"/",expires:new Date(Date.now()+31536e6)}),ey().reload())})]})]})})):(0,a.jsx)(a.Fragment,{})}let eN=d.ZP.div.withConfig({componentId:"sc-dbd6c5f7-0"})(CookieBanner_templateObject()),eA=d.ZP.div.withConfig({componentId:"sc-dbd6c5f7-1"})(CookieBanner_templateObject1(),R.U.tablet,R.U.tablet);function Layout(n){let{children:r}=n;return(0,a.jsxs)(u.F,{children:[(0,a.jsx)(Header,{}),r,(0,a.jsx)(Footer,{}),(0,a.jsx)(CookieBanner,{}),(0,a.jsx)(Popup,{})]})}i(4470);var eF=i(9008),eP=i.n(eF),eM=i(4298),eR=i.n(eM),eL=i(5885),eq=i(9347),eV=(o=function(n,r){return(o=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var i in r)r.hasOwnProperty(i)&&(n[i]=r[i])})(n,r)},function(n,r){function __(){this.constructor=n}o(n,r),n.prototype=null===r?Object.create(r):(__.prototype=r.prototype,new __)}),eQ=function(n){function CookiesProvider(r){var i=n.call(this,r)||this;return r.cookies?i.cookies=r.cookies:i.cookies=new eL.Z,i}return eV(CookiesProvider,n),CookiesProvider.prototype.render=function(){return x.createElement(eq.zt,{value:this.cookies},this.props.children)},CookiesProvider}(x.Component),_app=function(n){let{Component:r,pageProps:i}=n,getParams=(n,r)=>{let i=r.reduce((r,i)=>(n[i]&&""!==n[i]&&"page"===i&&1!==Number(n.page)&&r.push("".concat(i,"=").concat(n[i])),r),[]);return i.length?"?".concat(i.join("&")):""};return(0,a.jsx)(eQ,{children:(0,a.jsxs)(Layout,{children:[(0,a.jsxs)(eP(),{children:[(0,a.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0"}),(0,a.jsx)("link",{rel:"canonical",href:(()=>{let{pathname:n,query:r}=(0,em.useRouter)(),i="";switch(n){case"/article/[article]":i="/article/".concat(r.article);break;case"/blog/[blog]":i="/blog/".concat(r.blog);break;case"/blog/[blog]/filtres":i="/blog/".concat(r.blog,"/filtres").concat(getParams(r,["topic","type","page"]));break;case"/categories":i="/categories";break;case"/categories/[topic]":i="/categories/".concat(r.topic);break;case"/categories/[topic]/ressources":i="/categories/".concat(r.topic,"/ressources").concat(getParams(r,["page"]));break;case"/categories/ministere/[ministry]":i="/categories/ministere/".concat(r.ministry);break;case"/categories/ministere/[ministry]/ressources":i="/categories/ministere/".concat(r.ministry,"/ressources").concat(getParams(r,["page"]));break;case"/categories/vocation/[vocation]":i="/categories/vocation/".concat(r.vocation);break;case"/categories/vocation/[vocation]/ressources":i="/categories/vocation/".concat(r.vocation,"/ressources").concat(getParams(r,["page"]));break;case"/formations":i="/formations";break;case"/formations/[formation]":i="/formations/".concat(r.formation);break;case"/parcours-emails":i="/parcours-emails";break;case"/parcours-emails/[parcours]":i="/parcours-emails/".concat(r.parcours);break;case"/podcasts":i="/podcasts";break;case"/podcasts/[podcast]":i="/podcasts/".concat(r.podcast).concat(getParams(r,["page"]));break;case"/podcasts/[podcast]/[episode]":i="/podcasts/".concat(r.podcast,"/").concat(r.episode);break;case"/webinaires":i="/webinaires".concat(getParams(r,["page"]));break;case"/webinaires/[episode]":i="/webinaires/".concat(r.episode).concat(getParams(r,["page"]));break;case"/[page]":i="/".concat(r.page);break;default:i=""}return new URL(i,"https://toutpoursagloire.com").href})()}),(0,a.jsx)("link",{rel:"icon",href:"/favicon.png"})]}),(0,a.jsx)(r,{...i}),function(){var n,r;let[i,o]=(0,eD.Z)(["preferences"]);return(0,a.jsxs)(a.Fragment,{children:[(null===(n=i.preferences)||void 0===n?void 0:n.analytics)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(eR(),{async:!0,src:"https://www.googletagmanager.com/gtag/js?id=".concat(""),id:"ga-url-script"}),(0,a.jsx)(eR(),{id:"ga-analytics-script",children:'window.dataLayer = window.dataLayer || [];\n              function gtag(){ dataLayer.push(arguments); }\n              gtag("js", new Date());\n              gtag("config", "'.concat("",'");\n            ')})]}),(null===(r=i.preferences)||void 0===r?void 0:r.medias)&&(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(eR(),{id:"marker-io-config",children:'window.markerConfig = {\n                project: "647f03e6a572cb7307800759",\n                source: "snippet" };'})})]})}()]})})}},7421:function(n,r,i){"use strict";i.d(r,{U:function(){return a}});let o={mini:"320px",tablet:"744px",desktop:"1024px",desktopXL:"1441px"},a={mini:"(max-width: ".concat(o.mini,")"),tablet:"(min-width: ".concat(o.tablet,")"),desktop:"(min-width: ".concat(o.desktop,")"),desktopXL:"(min-width: ".concat(o.desktopXL,")")}},4218:function(n,r,i){"use strict";i.d(r,{LG:function(){return dateDiffInDays},S$:function(){return dateForHumans},xO:function(){return hour}});var o=i(7484),a=i.n(o);function dateForHumans(n){return a()(n).locale("fr").format("DD MMM YYYY")}function hour(n){return a()(n).locale("fr").format("HH:mm")}function dateDiffInDays(n,r){let i=toUTC(n),o=toUTC(r);return Math.floor(Math.abs(o-i)/864e5)}function toUTC(n){return Date.UTC(n.getUTCFullYear(),n.getUTCMonth(),n.getUTCDate(),n.getUTCHours(),n.getUTCMinutes(),n.getUTCSeconds())}i(6023),a().locale("fr")},3071:function(n,r,i){"use strict";function withRealSrc(n){return(null==n?void 0:n.provider)==="local"?"http://localhost:1337"+n.url:(null==n?void 0:n.url)?n.url:void 0}i.d(r,{k:function(){return withRealSrc}})},3365:function(n,r,i){"use strict";function topicSort(n){let r=[],i=n.map(n=>({...n,children:null}));return i.filter(n=>null==n.parent||n.id===i[0].id).map(n=>{i.filter(r=>{var i;return(null===(i=r.parent)||void 0===i?void 0:i.id)===n.id}).map(r=>{i.filter(n=>{var i;return(null===(i=n.parent)||void 0===i?void 0:i.id)===r.id}).map(n=>{r.children=r.children?r.children:[],r.children.push(n)}),r.children&&r.children.sort(dynamicSort("name")),n.children=n.children?n.children:[],n.children.push(r)}),n.children&&n.children.sort(dynamicSort("name")),r.push(n)}),r}i.d(r,{Ay:function(){return topicSort},IQ:function(){return dynamicSort},df:function(){return sortByDate}});let dynamicSort=n=>{let r=1;return"-"===n[0]&&(r=-1,n=n.substr(1)),function(i,o){return(i[n]<o[n]?-1:i[n]>o[n]?1:0)*r}};function sortByDate(n,r){return new Date(r.published_at)-new Date(n.published_at)}},5158:function(n,r,i){"use strict";function slugify(n){n=(n=n.replace(/^\s+|\s+$/g,"")).toLowerCase();let r="\xc1\xc4\xc2\xc0\xc3\xc5Č\xc7ĆĎ\xc9Ě\xcb\xc8\xcaẼĔȆ\xcd\xcc\xce\xcfŇ\xd1\xd3\xd6\xd2\xd4\xd5\xd8ŘŔŠŤ\xdaŮ\xdc\xd9\xdb\xddŸŽ\xe1\xe4\xe2\xe0\xe3\xe5č\xe7ćď\xe9ě\xeb\xe8\xeaẽĕȇ\xed\xec\xee\xefň\xf1\xf3\xf6\xf2\xf4\xf5\xf8\xf0řŕšť\xfaů\xfc\xf9\xfb\xfd\xffž\xfe\xdeĐđ\xdf\xc6a\xb7/_,:;";for(let i=0,o=r.length;i<o;i++)n=n.replace(RegExp(r.charAt(i),"g"),"AAAAAACCCDEEEEEEEEIIIINNOOOOOORRSTUUUUUYYZaaaaaacccdeeeeeeeeiiiinnooooooorrstuuuuuyyzbBDdBAa------".charAt(i));return n=n.replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-")}function removeMarkdown(n){return n.replace(/(?:_|[*#])|\[(.*?)\]\(.*?\)/gm,"$1")}function removeHtml(n){return n.replace(/`|<[^>]*(>|…)/gm,"")}function isLinkExternal(n){return n.includes(".")}i.d(r,{Gq:function(){return removeHtml},Kd:function(){return removeMarkdown},gc:function(){return getYouTubeVideoIdFromUrl},k5:function(){return removeLastBackSlash},lV:function(){return slugify},tm:function(){return isLinkExternal}});let getYouTubeVideoIdFromUrl=n=>void 0!==(n=n.split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/))[2]?n[2].split(/[^0-9a-z_\-]/i)[0]:n[0];function removeLastBackSlash(n){return n.replace(/^\\/gm,"")}},4470:function(){},7663:function(n){!function(){var r={229:function(n){var r,i,o,a=n.exports={};function defaultSetTimout(){throw Error("setTimeout has not been defined")}function defaultClearTimeout(){throw Error("clearTimeout has not been defined")}function runTimeout(n){if(r===setTimeout)return setTimeout(n,0);if((r===defaultSetTimout||!r)&&setTimeout)return r=setTimeout,setTimeout(n,0);try{return r(n,0)}catch(i){try{return r.call(null,n,0)}catch(i){return r.call(this,n,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(n){r=defaultSetTimout}try{i="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(n){i=defaultClearTimeout}}();var u=[],p=!1,d=-1;function cleanUpNextTick(){p&&o&&(p=!1,o.length?u=o.concat(u):d=-1,u.length&&drainQueue())}function drainQueue(){if(!p){var n=runTimeout(cleanUpNextTick);p=!0;for(var r=u.length;r;){for(o=u,u=[];++d<r;)o&&o[d].run();d=-1,r=u.length}o=null,p=!1,function(n){if(i===clearTimeout)return clearTimeout(n);if((i===defaultClearTimeout||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(n);try{i(n)}catch(r){try{return i.call(null,n)}catch(r){return i.call(this,n)}}}(n)}}function Item(n,r){this.fun=n,this.array=r}function noop(){}a.nextTick=function(n){var r=Array(arguments.length-1);if(arguments.length>1)for(var i=1;i<arguments.length;i++)r[i-1]=arguments[i];u.push(new Item(n,r)),1!==u.length||p||runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},a.title="browser",a.browser=!0,a.env={},a.argv=[],a.version="",a.versions={},a.on=noop,a.addListener=noop,a.once=noop,a.off=noop,a.removeListener=noop,a.removeAllListeners=noop,a.emit=noop,a.prependListener=noop,a.prependOnceListener=noop,a.listeners=function(n){return[]},a.binding=function(n){throw Error("process.binding is not supported")},a.cwd=function(){return"/"},a.chdir=function(n){throw Error("process.chdir is not supported")},a.umask=function(){return 0}}},i={};function __nccwpck_require__(n){var o=i[n];if(void 0!==o)return o.exports;var a=i[n]={exports:{}},u=!0;try{r[n](a,a.exports,__nccwpck_require__),u=!1}finally{u&&delete i[n]}return a.exports}__nccwpck_require__.ab="//";var o=__nccwpck_require__(229);n.exports=o}()},9008:function(n,r,i){n.exports=i(46)},5675:function(n,r,i){n.exports=i(2647)},1664:function(n,r,i){n.exports=i(4520)},1163:function(n,r,i){n.exports=i(4751)},4298:function(n,r,i){n.exports=i(2288)},9347:function(n,r,i){"use strict";i.d(r,{zt:function(){return p},ZP:function(){return d}});var o=i(7294),a=i(5885).Z,u=o.createContext(new a),p=u.Provider;u.Consumer;var d=u},708:function(n,r,i){"use strict";i.d(r,{Z:function(){return useCookies}});var o=i(7294),a=i(9347);function useCookies(n){var r=(0,o.useContext)(a.ZP);if(!r)throw Error("Missing <CookiesProvider>");var i=r.getAll(),u=(0,o.useState)(i),p=u[0],d=u[1],x=(0,o.useRef)(p);return"undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement&&(0,o.useLayoutEffect)(function(){function onChange(){var i=r.getAll();(function(n,r,i){if(!n)return!0;for(var o=0;o<n.length;o++){var a=n[o];if(r[a]!==i[a])return!0}return!1})(n||null,i,x.current)&&d(i),x.current=i}return r.addChangeListener(onChange),function(){r.removeChangeListener(onChange)}},[r]),[p,(0,o.useMemo)(function(){return r.set.bind(r)},[r]),(0,o.useMemo)(function(){return r.remove.bind(r)},[r])]}},9921:function(n,r){"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i,o=Symbol.for("react.element"),a=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),p=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),x=Symbol.for("react.provider"),w=Symbol.for("react.context"),k=Symbol.for("react.server_context"),E=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),D=Symbol.for("react.suspense_list"),I=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),V=Symbol.for("react.offscreen");i=Symbol.for("react.module.reference"),r.isValidElementType=function(n){return"string"==typeof n||"function"==typeof n||n===u||n===d||n===p||n===C||n===D||n===V||"object"==typeof n&&null!==n&&(n.$$typeof===R||n.$$typeof===I||n.$$typeof===x||n.$$typeof===w||n.$$typeof===E||n.$$typeof===i||void 0!==n.getModuleId)},r.typeOf=function(n){if("object"==typeof n&&null!==n){var r=n.$$typeof;switch(r){case o:switch(n=n.type){case u:case d:case p:case C:case D:return n;default:switch(n=n&&n.$$typeof){case k:case w:case E:case R:case I:case x:return n;default:return r}}case a:return r}}}},9864:function(n,r,i){"use strict";n.exports=i(9921)},6774:function(n){n.exports=function(n,r,i,o){var a=i?i.call(o,n,r):void 0;if(void 0!==a)return!!a;if(n===r)return!0;if("object"!=typeof n||!n||"object"!=typeof r||!r)return!1;var u=Object.keys(n),p=Object.keys(r);if(u.length!==p.length)return!1;for(var d=Object.prototype.hasOwnProperty.bind(r),x=0;x<u.length;x++){var w=u[x];if(!d(w))return!1;var k=n[w],E=r[w];if(!1===(a=i?i.call(o,k,E,w):void 0)||void 0===a&&k!==E)return!1}return!0}},9521:function(n,r,i){"use strict";i.d(r,{iv:function(){return Ce},ZP:function(){return eI},F4:function(){return Ue}});var o,a,u,p=i(9864),d=i(7294),x=i(6774),w=i.n(x),stylis_browser_esm=function(n){function X(n,r,i){var o=r.trim().split(p);r=o;var a=o.length,u=n.length;switch(u){case 0:case 1:var d=0;for(n=0===u?"":n[0]+" ";d<a;++d)r[d]=Z(n,r[d],i).trim();break;default:var x=d=0;for(r=[];d<a;++d)for(var w=0;w<u;++w)r[x++]=Z(n[w]+" ",o[d],i).trim()}return r}function Z(n,r,i){var o=r.charCodeAt(0);switch(33>o&&(o=(r=r.trim()).charCodeAt(0)),o){case 38:return r.replace(d,"$1"+n.trim());case 58:return n.trim()+r.replace(d,"$1"+n.trim());default:if(0<1*i&&0<r.indexOf("\f"))return r.replace(d,(58===n.charCodeAt(0)?"":"$1")+n.trim())}return n+r}function P(n,r,i,p){var d=n+";",x=2*r+3*i+4*p;if(944===x){n=d.indexOf(":",9)+1;var w=d.substring(n,d.length-1).trim();return w=d.substring(0,n).trim()+w+";",1===et||2===et&&L(w,1)?"-webkit-"+w+w:w}if(0===et||2===et&&!L(d,1))return d;switch(x){case 1015:return 97===d.charCodeAt(10)?"-webkit-"+d+d:d;case 951:return 116===d.charCodeAt(3)?"-webkit-"+d+d:d;case 963:return 110===d.charCodeAt(5)?"-webkit-"+d+d:d;case 1009:if(100!==d.charCodeAt(4))break;case 969:case 942:return"-webkit-"+d+d;case 978:return"-webkit-"+d+"-moz-"+d+d;case 1019:case 983:return"-webkit-"+d+"-moz-"+d+"-ms-"+d+d;case 883:if(45===d.charCodeAt(8))return"-webkit-"+d+d;if(0<d.indexOf("image-set(",11))return d.replace(K,"$1-webkit-$2")+d;break;case 932:if(45===d.charCodeAt(4))switch(d.charCodeAt(5)){case 103:return"-webkit-box-"+d.replace("-grow","")+"-webkit-"+d+"-ms-"+d.replace("grow","positive")+d;case 115:return"-webkit-"+d+"-ms-"+d.replace("shrink","negative")+d;case 98:return"-webkit-"+d+"-ms-"+d.replace("basis","preferred-size")+d}return"-webkit-"+d+"-ms-"+d+d;case 964:return"-webkit-"+d+"-ms-flex-"+d+d;case 1023:if(99!==d.charCodeAt(8))break;return"-webkit-box-pack"+(w=d.substring(d.indexOf(":",15)).replace("flex-","").replace("space-between","justify"))+"-webkit-"+d+"-ms-flex-pack"+w+d;case 1005:return a.test(d)?d.replace(o,":-webkit-")+d.replace(o,":-moz-")+d:d;case 1e3:switch(r=(w=d.substring(13).trim()).indexOf("-")+1,w.charCodeAt(0)+w.charCodeAt(r)){case 226:w=d.replace(E,"tb");break;case 232:w=d.replace(E,"tb-rl");break;case 220:w=d.replace(E,"lr");break;default:return d}return"-webkit-"+d+"-ms-"+w+d;case 1017:if(-1===d.indexOf("sticky",9))break;case 975:switch(r=(d=n).length-10,x=(w=(33===d.charCodeAt(r)?d.substring(0,r):d).substring(n.indexOf(":",7)+1).trim()).charCodeAt(0)+(0|w.charCodeAt(7))){case 203:if(111>w.charCodeAt(8))break;case 115:d=d.replace(w,"-webkit-"+w)+";"+d;break;case 207:case 102:d=d.replace(w,"-webkit-"+(102<x?"inline-":"")+"box")+";"+d.replace(w,"-webkit-"+w)+";"+d.replace(w,"-ms-"+w+"box")+";"+d}return d+";";case 938:if(45===d.charCodeAt(5))switch(d.charCodeAt(6)){case 105:return w=d.replace("-items",""),"-webkit-"+d+"-webkit-box-"+w+"-ms-flex-"+w+d;case 115:return"-webkit-"+d+"-ms-flex-item-"+d.replace(I,"")+d;default:return"-webkit-"+d+"-ms-flex-line-pack"+d.replace("align-content","").replace(I,"")+d}break;case 973:case 989:if(45!==d.charCodeAt(3)||122===d.charCodeAt(4))break;case 931:case 953:if(!0===V.test(n))return 115===(w=n.substring(n.indexOf(":")+1)).charCodeAt(0)?P(n.replace("stretch","fill-available"),r,i,p).replace(":fill-available",":stretch"):d.replace(w,"-webkit-"+w)+d.replace(w,"-moz-"+w.replace("fill-",""))+d;break;case 962:if(d="-webkit-"+d+(102===d.charCodeAt(5)?"-ms-"+d:"")+d,211===i+p&&105===d.charCodeAt(13)&&0<d.indexOf("transform",10))return d.substring(0,d.indexOf(";",27)+1).replace(u,"$1-webkit-$2")+d}return d}function L(n,r){var i=n.indexOf(1===r?":":"{"),o=n.substring(0,3!==r?i:10);return i=n.substring(i+1,n.length-1),eo(2!==r?o:o.replace(R,"$1"),i,r)}function ea(n,r){var i=P(r,r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2));return i!==r+";"?i.replace(D," or ($1)").substring(4):"("+r+")"}function H(n,r,i,o,a,u,p,d,x,w){for(var k,E=0,C=r;E<ei;++E)switch(k=er[E].call(B,n,C,i,o,a,u,p,d,x,w)){case void 0:case!1:case!0:case null:break;default:C=k}if(C!==r)return C}function U(n){return void 0!==(n=n.prefix)&&(eo=null,n?"function"!=typeof n?et=1:(et=2,eo=n):et=0),U}function B(n,o){var a=n;if(33>a.charCodeAt(0)&&(a=a.trim()),a=[a],0<ei){var u=H(-1,o,a,a,G,W,0,0,0,0);void 0!==u&&"string"==typeof u&&(o=u)}var p=function M(n,o,a,u,p){for(var d,E,D,I,R,V=0,K=0,er=0,eo=0,ec=0,el=0,eu=D=d=0,ep=0,ef=0,ed=0,eh=0,em=a.length,ey=em-1,ev="",eg="",eb="",e_="";ep<em;){if(E=a.charCodeAt(ep),ep===ey&&0!==K+eo+er+V&&(0!==K&&(E=47===K?10:47),eo=er=V=0,em++,ey++),0===K+eo+er+V){if(ep===ey&&(0<ef&&(ev=ev.replace(i,"")),0<ev.trim().length)){switch(E){case 32:case 9:case 59:case 13:case 10:break;default:ev+=a.charAt(ep)}E=59}switch(E){case 123:for(d=(ev=ev.trim()).charCodeAt(0),D=1,eh=++ep;ep<em;){switch(E=a.charCodeAt(ep)){case 123:D++;break;case 125:D--;break;case 47:switch(E=a.charCodeAt(ep+1)){case 42:case 47:e:{for(eu=ep+1;eu<ey;++eu)switch(a.charCodeAt(eu)){case 47:if(42===E&&42===a.charCodeAt(eu-1)&&ep+2!==eu){ep=eu+1;break e}break;case 10:if(47===E){ep=eu+1;break e}}ep=eu}}break;case 91:E++;case 40:E++;case 34:case 39:for(;ep++<ey&&a.charCodeAt(ep)!==E;);}if(0===D)break;ep++}if(D=a.substring(eh,ep),0===d&&(d=(ev=ev.replace(r,"").trim()).charCodeAt(0)),64===d){switch(0<ef&&(ev=ev.replace(i,"")),E=ev.charCodeAt(1)){case 100:case 109:case 115:case 45:ef=o;break;default:ef=en}if(eh=(D=M(o,ef,D,E,p+1)).length,0<ei&&(R=H(3,D,ef=X(en,ev,ed),o,G,W,eh,E,p,u),ev=ef.join(""),void 0!==R&&0===(eh=(D=R.trim()).length)&&(E=0,D="")),0<eh)switch(E){case 115:ev=ev.replace(C,ea);case 100:case 109:case 45:D=ev+"{"+D+"}";break;case 107:D=(ev=ev.replace(x,"$1 $2"))+"{"+D+"}",D=1===et||2===et&&L("@"+D,3)?"@-webkit-"+D+"@"+D:"@"+D;break;default:D=ev+D,112===u&&(eg+=D,D="")}else D=""}else D=M(o,X(o,ev,ed),D,u,p+1);eb+=D,D=ed=ef=eu=d=0,ev="",E=a.charCodeAt(++ep);break;case 125:case 59:if(1<(eh=(ev=(0<ef?ev.replace(i,""):ev).trim()).length))switch(0===eu&&(45===(d=ev.charCodeAt(0))||96<d&&123>d)&&(eh=(ev=ev.replace(" ",":")).length),0<ei&&void 0!==(R=H(1,ev,o,n,G,W,eg.length,u,p,u))&&0===(eh=(ev=R.trim()).length)&&(ev="\x00\x00"),d=ev.charCodeAt(0),E=ev.charCodeAt(1),d){case 0:break;case 64:if(105===E||99===E){e_+=ev+a.charAt(ep);break}default:58!==ev.charCodeAt(eh-1)&&(eg+=P(ev,d,E,ev.charCodeAt(2)))}ed=ef=eu=d=0,ev="",E=a.charCodeAt(++ep)}}switch(E){case 13:case 10:47===K?K=0:0===1+d&&107!==u&&0<ev.length&&(ef=1,ev+="\x00"),0<ei*es&&H(0,ev,o,n,G,W,eg.length,u,p,u),W=1,G++;break;case 59:case 125:if(0===K+eo+er+V){W++;break}default:switch(W++,I=a.charAt(ep),E){case 9:case 32:if(0===eo+V+K)switch(ec){case 44:case 58:case 9:case 32:I="";break;default:32!==E&&(I=" ")}break;case 0:I="\\0";break;case 12:I="\\f";break;case 11:I="\\v";break;case 38:0===eo+K+V&&(ef=ed=1,I="\f"+I);break;case 108:if(0===eo+K+V+J&&0<eu)switch(ep-eu){case 2:112===ec&&58===a.charCodeAt(ep-3)&&(J=ec);case 8:111===el&&(J=el)}break;case 58:0===eo+K+V&&(eu=ep);break;case 44:0===K+er+eo+V&&(ef=1,I+="\r");break;case 34:case 39:0===K&&(eo=eo===E?0:0===eo?E:eo);break;case 91:0===eo+K+er&&V++;break;case 93:0===eo+K+er&&V--;break;case 41:0===eo+K+V&&er--;break;case 40:0===eo+K+V&&(0===d&&(2*ec+3*el==533||(d=1)),er++);break;case 64:0===K+er+eo+V+eu+D&&(D=1);break;case 42:case 47:if(!(0<eo+V+er))switch(K){case 0:switch(2*E+3*a.charCodeAt(ep+1)){case 235:K=47;break;case 220:eh=ep,K=42}break;case 42:47===E&&42===ec&&eh+2!==ep&&(33===a.charCodeAt(eh+2)&&(eg+=a.substring(eh,ep+1)),I="",K=0)}}0===K&&(ev+=I)}el=ec,ec=E,ep++}if(0<(eh=eg.length)){if(ef=o,0<ei&&void 0!==(R=H(2,eg,ef,n,G,W,eh,u,p,u))&&0===(eg=R).length)return e_+eg+eb;if(eg=ef.join(",")+"{"+eg+"}",0!=et*J){switch(2!==et||L(eg,2)||(J=0),J){case 111:eg=eg.replace(k,":-moz-$1")+eg;break;case 112:eg=eg.replace(w,"::-webkit-input-$1")+eg.replace(w,"::-moz-$1")+eg.replace(w,":-ms-input-$1")+eg}J=0}}return e_+eg+eb}(en,a,o,0,0);return 0<ei&&void 0!==(u=H(-2,p,a,a,G,W,p.length,0,0,0))&&(p=u),J=0,W=G=1,p}var r=/^\0+/g,i=/[\0\r\f]/g,o=/: */g,a=/zoo|gra/,u=/([,: ])(transform)/g,p=/,\r+?/g,d=/([\t\r\n ])*\f?&/g,x=/@(k\w+)\s*(\S*)\s*/,w=/::(place)/g,k=/:(read-only)/g,E=/[svh]\w+-[tblr]{2}/,C=/\(\s*(.*)\s*\)/g,D=/([\s\S]*?);/g,I=/-self|flex-/g,R=/[^]*?(:[rp][el]a[\w-]+)[^]*/,V=/stretch|:\s*\w+\-(?:conte|avail)/,K=/([^-])(image-set\()/,W=1,G=1,J=0,et=1,en=[],er=[],ei=0,eo=null,es=0;return B.use=function T(n){switch(n){case void 0:case null:ei=er.length=0;break;default:if("function"==typeof n)er[ei++]=n;else if("object"==typeof n)for(var r=0,i=n.length;r<i;++r)T(n[r]);else es=0|!!n}return T},B.set=U,void 0!==n&&U(n),B},k={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},E=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,C=(o=Object.create(null),function(n){return void 0===o[n]&&(o[n]=E.test(n)||111===n.charCodeAt(0)&&110===n.charCodeAt(1)&&91>n.charCodeAt(2)),o[n]}),D=i(8679),I=i.n(D),R=i(3454);function v(){return(v=Object.assign||function(n){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(n[o]=i[o])}return n}).apply(this,arguments)}var g=function(n,r){for(var i=[n[0]],o=0,a=r.length;o<a;o+=1)i.push(r[o],n[o+1]);return i},S=function(n){return null!==n&&"object"==typeof n&&"[object Object]"===(n.toString?n.toString():Object.prototype.toString.call(n))&&!(0,p.typeOf)(n)},V=Object.freeze([]),K=Object.freeze({});function b(n){return"function"==typeof n}function _(n){return n.displayName||n.name||"Component"}function N(n){return n&&"string"==typeof n.styledComponentId}var W=void 0!==R&&(R.env.REACT_APP_SC_ATTR||R.env.SC_ATTR)||"data-styled",G="undefined"!=typeof window&&"HTMLElement"in window,J=!!("boolean"==typeof SC_DISABLE_SPEEDY?SC_DISABLE_SPEEDY:void 0!==R&&void 0!==R.env.REACT_APP_SC_DISABLE_SPEEDY&&""!==R.env.REACT_APP_SC_DISABLE_SPEEDY?"false"!==R.env.REACT_APP_SC_DISABLE_SPEEDY&&R.env.REACT_APP_SC_DISABLE_SPEEDY:void 0!==R&&void 0!==R.env.SC_DISABLE_SPEEDY&&""!==R.env.SC_DISABLE_SPEEDY&&"false"!==R.env.SC_DISABLE_SPEEDY&&R.env.SC_DISABLE_SPEEDY);function j(n){for(var r=arguments.length,i=Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];throw Error("An error occurred. See https://git.io/JUIaE#"+n+" for more information."+(i.length>0?" Args: "+i.join(", "):""))}var et=function(){function e(n){this.groupSizes=new Uint32Array(512),this.length=512,this.tag=n}var n=e.prototype;return n.indexOfGroup=function(n){for(var r=0,i=0;i<n;i++)r+=this.groupSizes[i];return r},n.insertRules=function(n,r){if(n>=this.groupSizes.length){for(var i=this.groupSizes,o=i.length,a=o;n>=a;)(a<<=1)<0&&j(16,""+n);this.groupSizes=new Uint32Array(a),this.groupSizes.set(i),this.length=a;for(var u=o;u<a;u++)this.groupSizes[u]=0}for(var p=this.indexOfGroup(n+1),d=0,x=r.length;d<x;d++)this.tag.insertRule(p,r[d])&&(this.groupSizes[n]++,p++)},n.clearGroup=function(n){if(n<this.length){var r=this.groupSizes[n],i=this.indexOfGroup(n),o=i+r;this.groupSizes[n]=0;for(var a=i;a<o;a++)this.tag.deleteRule(i)}},n.getGroup=function(n){var r="";if(n>=this.length||0===this.groupSizes[n])return r;for(var i=this.groupSizes[n],o=this.indexOfGroup(n),a=o+i,u=o;u<a;u++)r+=this.tag.getRule(u)+"/*!sc*/\n";return r},e}(),en=new Map,er=new Map,ei=1,B=function(n){if(en.has(n))return en.get(n);for(;er.has(ei);)ei++;var r=ei++;return en.set(n,r),er.set(r,n),r},M=function(n,r){r>=ei&&(ei=r+1),en.set(n,r),er.set(r,n)},eo="style["+W+'][data-styled-version="5.3.6"]',es=RegExp("^"+W+'\\.g(\\d+)\\[id="([\\w\\d-]+)"\\].*?"([^"]*)'),F=function(n,r,i){for(var o,a=i.split(","),u=0,p=a.length;u<p;u++)(o=a[u])&&n.registerName(r,o)},Y=function(n,r){for(var i=(r.textContent||"").split("/*!sc*/\n"),o=[],a=0,u=i.length;a<u;a++){var p=i[a].trim();if(p){var d=p.match(es);if(d){var x=0|parseInt(d[1],10),w=d[2];0!==x&&(M(w,x),F(n,w,d[3]),n.getTag().insertRules(x,o)),o.length=0}else o.push(p)}}},q=function(){return i.nc},H=function(n){var r=document.head,i=n||r,o=document.createElement("style"),a=function(n){for(var r=n.childNodes,i=r.length;i>=0;i--){var o=r[i];if(o&&1===o.nodeType&&o.hasAttribute(W))return o}}(i),u=void 0!==a?a.nextSibling:null;o.setAttribute(W,"active"),o.setAttribute("data-styled-version","5.3.6");var p=q();return p&&o.setAttribute("nonce",p),i.insertBefore(o,u),o},ec=function(){function e(n){var r=this.element=H(n);r.appendChild(document.createTextNode("")),this.sheet=function(n){if(n.sheet)return n.sheet;for(var r=document.styleSheets,i=0,o=r.length;i<o;i++){var a=r[i];if(a.ownerNode===n)return a}j(17)}(r),this.length=0}var n=e.prototype;return n.insertRule=function(n,r){try{return this.sheet.insertRule(r,n),this.length++,!0}catch(n){return!1}},n.deleteRule=function(n){this.sheet.deleteRule(n),this.length--},n.getRule=function(n){var r=this.sheet.cssRules[n];return void 0!==r&&"string"==typeof r.cssText?r.cssText:""},e}(),el=function(){function e(n){var r=this.element=H(n);this.nodes=r.childNodes,this.length=0}var n=e.prototype;return n.insertRule=function(n,r){if(n<=this.length&&n>=0){var i=document.createTextNode(r),o=this.nodes[n];return this.element.insertBefore(i,o||null),this.length++,!0}return!1},n.deleteRule=function(n){this.element.removeChild(this.nodes[n]),this.length--},n.getRule=function(n){return n<this.length?this.nodes[n].textContent:""},e}(),eu=function(){function e(n){this.rules=[],this.length=0}var n=e.prototype;return n.insertRule=function(n,r){return n<=this.length&&(this.rules.splice(n,0,r),this.length++,!0)},n.deleteRule=function(n){this.rules.splice(n,1),this.length--},n.getRule=function(n){return n<this.length?this.rules[n]:""},e}(),ep=G,ef={isServer:!G,useCSSOMInjection:!J},ed=function(){function e(n,r,i){void 0===n&&(n=K),void 0===r&&(r={}),this.options=v({},ef,{},n),this.gs=r,this.names=new Map(i),this.server=!!n.isServer,!this.server&&G&&ep&&(ep=!1,function(n){for(var r=document.querySelectorAll(eo),i=0,o=r.length;i<o;i++){var a=r[i];a&&"active"!==a.getAttribute(W)&&(Y(n,a),a.parentNode&&a.parentNode.removeChild(a))}}(this))}e.registerId=function(n){return B(n)};var n=e.prototype;return n.reconstructWithOptions=function(n,r){return void 0===r&&(r=!0),new e(v({},this.options,{},n),this.gs,r&&this.names||void 0)},n.allocateGSInstance=function(n){return this.gs[n]=(this.gs[n]||0)+1},n.getTag=function(){var n,r,i,o,a;return this.tag||(this.tag=(i=(r=this.options).isServer,o=r.useCSSOMInjection,a=r.target,n=i?new eu(a):o?new ec(a):new el(a),new et(n)))},n.hasNameForId=function(n,r){return this.names.has(n)&&this.names.get(n).has(r)},n.registerName=function(n,r){if(B(n),this.names.has(n))this.names.get(n).add(r);else{var i=new Set;i.add(r),this.names.set(n,i)}},n.insertRules=function(n,r,i){this.registerName(n,r),this.getTag().insertRules(B(n),i)},n.clearNames=function(n){this.names.has(n)&&this.names.get(n).clear()},n.clearRules=function(n){this.getTag().clearGroup(B(n)),this.clearNames(n)},n.clearTag=function(){this.tag=void 0},n.toString=function(){return function(n){for(var r=n.getTag(),i=r.length,o="",a=0;a<i;a++){var u,p=(u=a,er.get(u));if(void 0!==p){var d=n.names.get(p),x=r.getGroup(a);if(d&&x&&d.size){var w=W+".g"+a+'[id="'+p+'"]',k="";void 0!==d&&d.forEach(function(n){n.length>0&&(k+=n+",")}),o+=""+x+w+'{content:"'+k+'"}/*!sc*/\n'}}}return o}(this)},e}(),eh=/(a)(d)/gi,Q=function(n){return String.fromCharCode(n+(n>25?39:97))};function ee(n){var r,i="";for(r=Math.abs(n);r>52;r=r/52|0)i=Q(r%52)+i;return(Q(r%52)+i).replace(eh,"$1-$2")}var te=function(n,r){for(var i=r.length;i;)n=33*n^r.charCodeAt(--i);return n},ne=function(n){return te(5381,n)};function re(n){for(var r=0;r<n.length;r+=1){var i=n[r];if(b(i)&&!N(i))return!1}return!0}var em=ne("5.3.6"),ey=function(){function e(n,r,i){this.rules=n,this.staticRulesId="",this.isStatic=(void 0===i||i.isStatic)&&re(n),this.componentId=r,this.baseHash=te(em,r),this.baseStyle=i,ed.registerId(r)}return e.prototype.generateAndInjectStyles=function(n,r,i){var o=this.componentId,a=[];if(this.baseStyle&&a.push(this.baseStyle.generateAndInjectStyles(n,r,i)),this.isStatic&&!i.hash){if(this.staticRulesId&&r.hasNameForId(o,this.staticRulesId))a.push(this.staticRulesId);else{var u=Ne(this.rules,n,r,i).join(""),p=ee(te(this.baseHash,u)>>>0);if(!r.hasNameForId(o,p)){var d=i(u,"."+p,void 0,o);r.insertRules(o,p,d)}a.push(p),this.staticRulesId=p}}else{for(var x=this.rules.length,w=te(this.baseHash,i.hash),k="",E=0;E<x;E++){var C=this.rules[E];if("string"==typeof C)k+=C;else if(C){var D=Ne(C,n,r,i),I=Array.isArray(D)?D.join(""):D;w=te(w,I+E),k+=I}}if(k){var R=ee(w>>>0);if(!r.hasNameForId(o,R)){var V=i(k,"."+R,void 0,o);r.insertRules(o,R,V)}a.push(R)}}return a.join(" ")},e}(),ev=/^\s*\/\/.*$/gm,eg=[":","[",".","#"];function ce(n){var r,i,o,a,u=void 0===n?K:n,p=u.options,d=void 0===p?K:p,x=u.plugins,w=void 0===x?V:x,k=new stylis_browser_esm(d),E=[],C=function(n){function t(r){if(r)try{n(r+"}")}catch(n){}}return function(r,i,o,a,u,p,d,x,w,k){switch(r){case 1:if(0===w&&64===i.charCodeAt(0))return n(i+";"),"";break;case 2:if(0===x)return i+"/*|*/";break;case 3:switch(x){case 102:case 112:return n(o[0]+i),"";default:return i+(0===k?"/*|*/":"")}case -2:i.split("/*|*/}").forEach(t)}}}(function(n){E.push(n)}),f=function(n,o,u){return 0===o&&-1!==eg.indexOf(u[i.length])||u.match(a)?n:"."+r};function m(n,u,p,d){void 0===d&&(d="&");var x=n.replace(ev,""),w=u&&p?p+" "+u+" { "+x+" }":x;return r=d,o=RegExp("\\"+(i=u)+"\\b","g"),a=RegExp("(\\"+i+"\\b){2,}"),k(p||!u?"":u,w)}return k.use([].concat(w,[function(n,r,a){2===n&&a.length&&a[0].lastIndexOf(i)>0&&(a[0]=a[0].replace(o,f))},C,function(n){if(-2===n){var r=E;return E=[],r}}])),m.hash=w.length?w.reduce(function(n,r){return r.name||j(15),te(n,r.name)},5381).toString():"",m}var eb=d.createContext(),e_=(eb.Consumer,d.createContext()),ex=(e_.Consumer,new ed),ew=ce();function fe(){return(0,d.useContext)(eb)||ex}function ye(n){var r=(0,d.useState)(n.stylisPlugins),i=r[0],o=r[1],a=fe(),u=(0,d.useMemo)(function(){var r=a;return n.sheet?r=n.sheet:n.target&&(r=r.reconstructWithOptions({target:n.target},!1)),n.disableCSSOMInjection&&(r=r.reconstructWithOptions({useCSSOMInjection:!1})),r},[n.disableCSSOMInjection,n.sheet,n.target]),p=(0,d.useMemo)(function(){return ce({options:{prefix:!n.disableVendorPrefixes},plugins:i})},[n.disableVendorPrefixes,i]);return(0,d.useEffect)(function(){w()(i,n.stylisPlugins)||o(n.stylisPlugins)},[n.stylisPlugins]),d.createElement(eb.Provider,{value:u},d.createElement(e_.Provider,{value:p},n.children))}var ek=function(){function e(n,r){var i=this;this.inject=function(n,r){void 0===r&&(r=ew);var o=i.name+r.hash;n.hasNameForId(i.id,o)||n.insertRules(i.id,o,r(i.rules,o,"@keyframes"))},this.toString=function(){return j(12,String(i.name))},this.name=n,this.id="sc-keyframes-"+n,this.rules=r}return e.prototype.getName=function(n){return void 0===n&&(n=ew),this.name+n.hash},e}(),eS=/([A-Z])/,eO=/([A-Z])/g,eE=/^ms-/,Ee=function(n){return"-"+n.toLowerCase()};function be(n){return eS.test(n)?n.replace(eO,Ee).replace(eE,"-ms-"):n}var _e=function(n){return null==n||!1===n||""===n};function Ne(n,r,i,o){if(Array.isArray(n)){for(var a,u=[],p=0,d=n.length;p<d;p+=1)""!==(a=Ne(n[p],r,i,o))&&(Array.isArray(a)?u.push.apply(u,a):u.push(a));return u}return _e(n)?"":N(n)?"."+n.styledComponentId:b(n)?"function"!=typeof n||n.prototype&&n.prototype.isReactComponent||!r?n:Ne(n(r),r,i,o):n instanceof ek?i?(n.inject(i,o),n.getName(o)):n:S(n)?function e(n,r){var i,o=[];for(var a in n)n.hasOwnProperty(a)&&!_e(n[a])&&(Array.isArray(n[a])&&n[a].isCss||b(n[a])?o.push(be(a)+":",n[a],";"):S(n[a])?o.push.apply(o,e(n[a],a)):o.push(be(a)+": "+(null==(i=n[a])||"boolean"==typeof i||""===i?"":"number"!=typeof i||0===i||a in k?String(i).trim():i+"px")+";"));return r?[r+" {"].concat(o,["}"]):o}(n):n.toString()}var Ae=function(n){return Array.isArray(n)&&(n.isCss=!0),n};function Ce(n){for(var r=arguments.length,i=Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];return b(n)||S(n)?Ae(Ne(g(V,[n].concat(i)))):0===i.length&&1===n.length&&"string"==typeof n[0]?n:Ae(Ne(g(n,i)))}var eC=/[!"#$%&'()*+,./:;<=>?@[\\\]^`{|}~-]+/g,ej=/(^-|-$)/g;function Te(n){return n.replace(eC,"-").replace(ej,"")}var xe=function(n){return ee(ne(n)>>>0)};function ke(n){return"string"==typeof n}var Ve=function(n){return"function"==typeof n||"object"==typeof n&&null!==n&&!Array.isArray(n)},eD=d.createContext();eD.Consumer;var eT={},He=function(n){return function e(n,r,i){if(void 0===i&&(i=K),!(0,p.isValidElementType)(r))return j(1,String(r));var s=function(){return n(r,i,Ce.apply(void 0,arguments))};return s.withConfig=function(o){return e(n,r,v({},i,{},o))},s.attrs=function(o){return e(n,r,v({},i,{attrs:Array.prototype.concat(i.attrs,o).filter(Boolean)}))},s}(function qe(n,r,i){var o=N(n),a=!ke(n),u=r.attrs,p=void 0===u?V:u,x=r.componentId,w=void 0===x?(G=r.displayName,J=r.parentComponentId,eT[et="string"!=typeof G?"sc":Te(G)]=(eT[et]||0)+1,en=et+"-"+xe("5.3.6"+et+eT[et]),J?J+"-"+en:en):x,k=r.displayName,E=void 0===k?ke(n)?"styled."+n:"Styled("+_(n)+")":k,D=r.displayName&&r.componentId?Te(r.displayName)+"-"+r.componentId:r.componentId||w,R=o&&n.attrs?Array.prototype.concat(n.attrs,p).filter(Boolean):p,W=r.shouldForwardProp;o&&n.shouldForwardProp&&(W=r.shouldForwardProp?function(i,o,a){return n.shouldForwardProp(i,o,a)&&r.shouldForwardProp(i,o,a)}:n.shouldForwardProp);var G,J,et,en,er,ei=new ey(i,D,o?n.componentStyle:void 0),eo=ei.isStatic&&0===p.length,O=function(n,r){return function(n,r,i,o){var a,u,p,x,w,k,E,D=n.attrs,I=n.componentStyle,R=n.defaultProps,V=n.foldedComponentIds,W=n.shouldForwardProp,G=n.styledComponentId,J=n.target,et=(a=(0,d.useContext)(eD),void 0===(u=R)&&(u=K),void 0===(p=r.theme!==u.theme&&r.theme||a||u.theme||K)&&(p=K),x=v({},r,{theme:p}),w={},D.forEach(function(n){var r,i,o,a=n;for(r in b(a)&&(a=a(x)),a)x[r]=w[r]="className"===r?(i=w[r],o=a[r],i&&o?i+" "+o:i||o):a[r]}),[x,w]),en=et[0],er=et[1],ei=(k=fe(),E=(0,d.useContext)(e_)||ew,o?I.generateAndInjectStyles(K,k,E):I.generateAndInjectStyles(en,k,E)),eo=er.$as||r.$as||er.as||r.as||J,es=ke(eo),ec=er!==r?v({},r,{},er):r,el={};for(var eu in ec)"$"!==eu[0]&&"as"!==eu&&("forwardedAs"===eu?el.as=ec[eu]:(W?W(eu,C,eo):!es||C(eu))&&(el[eu]=ec[eu]));return r.style&&er.style!==r.style&&(el.style=v({},r.style,{},er.style)),el.className=Array.prototype.concat(V,G,ei!==G?ei:null,r.className,er.className).filter(Boolean).join(" "),el.ref=i,(0,d.createElement)(eo,el)}(er,n,r,eo)};return O.displayName=E,(er=d.forwardRef(O)).attrs=R,er.componentStyle=ei,er.displayName=E,er.shouldForwardProp=W,er.foldedComponentIds=o?Array.prototype.concat(n.foldedComponentIds,n.styledComponentId):V,er.styledComponentId=D,er.target=o?n.target:n,er.withComponent=function(n){var o=r.componentId,a=function(n,r){if(null==n)return{};var i,o,a={},u=Object.keys(n);for(o=0;o<u.length;o++)r.indexOf(i=u[o])>=0||(a[i]=n[i]);return a}(r,["componentId"]),u=o&&o+"-"+(ke(n)?n:Te(_(n)));return qe(n,v({},a,{attrs:R,componentId:u}),i)},Object.defineProperty(er,"defaultProps",{get:function(){return this._foldedDefaultProps},set:function(r){this._foldedDefaultProps=o?function Me(n){for(var r=arguments.length,i=Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];for(var a=0;a<i.length;a++){var u=i[a];if(Ve(u))for(var p in u)"__proto__"!==p&&"constructor"!==p&&"prototype"!==p&&function(n,r,i){var o=n[i];Ve(r)&&Ve(o)?Me(o,r):n[i]=r}(n,u[p],p)}return n}({},n.defaultProps,r):r}}),er.toString=function(){return"."+er.styledComponentId},a&&I()(er,n,{attrs:!0,componentStyle:!0,displayName:!0,foldedComponentIds:!0,shouldForwardProp:!0,styledComponentId:!0,target:!0,withComponent:!0}),er},n)};function Ue(n){for(var r=arguments.length,i=Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];var a=Ce.apply(void 0,[n].concat(i)).join(""),u=xe(a);return new ek(u,a)}["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","marker","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","textPath","tspan"].forEach(function(n){He[n]=He(n)}),(a=(function(n,r){this.rules=n,this.componentId=r,this.isStatic=re(n),ed.registerId(this.componentId+1)}).prototype).createStyles=function(n,r,i,o){var a=o(Ne(this.rules,r,i,o).join(""),""),u=this.componentId+n;i.insertRules(u,u,a)},a.removeStyles=function(n,r){r.clearRules(this.componentId+n)},a.renderStyles=function(n,r,i,o){n>2&&ed.registerId(this.componentId+n),this.removeStyles(n,i),this.createStyles(n,r,i,o)},(u=(function(){var n=this;this._emitSheetCSS=function(){var r=n.instance.toString();if(!r)return"";var i=q();return"<style "+[i&&'nonce="'+i+'"',W+'="true"','data-styled-version="5.3.6"'].filter(Boolean).join(" ")+">"+r+"</style>"},this.getStyleTags=function(){return n.sealed?j(2):n._emitSheetCSS()},this.getStyleElement=function(){if(n.sealed)return j(2);var r,i=((r={})[W]="",r["data-styled-version"]="5.3.6",r.dangerouslySetInnerHTML={__html:n.instance.toString()},r),o=q();return o&&(i.nonce=o),[d.createElement("style",v({},i,{key:"sc-0-0"}))]},this.seal=function(){n.sealed=!0},this.instance=new ed({isServer:!0}),this.sealed=!1}).prototype).collectStyles=function(n){return this.sealed?j(2):d.createElement(ye,{sheet:this.instance},n)},u.interleaveWithNodeStream=function(n){return j(3)};var eI=He},655:function(n,r,i){"use strict";i.d(r,{Jh:function(){return __generator},ZT:function(){return __extends},_T:function(){return __rest},ev:function(){return __spreadArray},mG:function(){return __awaiter},pi:function(){return __assign}});var extendStatics=function(n,r){return(extendStatics=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(n[i]=r[i])})(n,r)};function __extends(n,r){if("function"!=typeof r&&null!==r)throw TypeError("Class extends value "+String(r)+" is not a constructor or null");function __(){this.constructor=n}extendStatics(n,r),n.prototype=null===r?Object.create(r):(__.prototype=r.prototype,new __)}var __assign=function(){return(__assign=Object.assign||function(n){for(var r,i=1,o=arguments.length;i<o;i++)for(var a in r=arguments[i])Object.prototype.hasOwnProperty.call(r,a)&&(n[a]=r[a]);return n}).apply(this,arguments)};function __rest(n,r){var i={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&0>r.indexOf(o)&&(i[o]=n[o]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols)for(var a=0,o=Object.getOwnPropertySymbols(n);a<o.length;a++)0>r.indexOf(o[a])&&Object.prototype.propertyIsEnumerable.call(n,o[a])&&(i[o[a]]=n[o[a]]);return i}function __awaiter(n,r,i,o){return new(i||(i=Promise))(function(a,u){function fulfilled(n){try{step(o.next(n))}catch(n){u(n)}}function rejected(n){try{step(o.throw(n))}catch(n){u(n)}}function step(n){var r;n.done?a(n.value):((r=n.value)instanceof i?r:new i(function(n){n(r)})).then(fulfilled,rejected)}step((o=o.apply(n,r||[])).next())})}function __generator(n,r){var i,o,a,u,p={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return u={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function verb(d){return function(x){return function(d){if(i)throw TypeError("Generator is already executing.");for(;u&&(u=0,d[0]&&(p=0)),p;)try{if(i=1,o&&(a=2&d[0]?o.return:d[0]?o.throw||((a=o.return)&&a.call(o),0):o.next)&&!(a=a.call(o,d[1])).done)return a;switch(o=0,a&&(d=[2&d[0],a.value]),d[0]){case 0:case 1:a=d;break;case 4:return p.label++,{value:d[1],done:!1};case 5:p.label++,o=d[1],d=[0];continue;case 7:d=p.ops.pop(),p.trys.pop();continue;default:if(!(a=(a=p.trys).length>0&&a[a.length-1])&&(6===d[0]||2===d[0])){p=0;continue}if(3===d[0]&&(!a||d[1]>a[0]&&d[1]<a[3])){p.label=d[1];break}if(6===d[0]&&p.label<a[1]){p.label=a[1],a=d;break}if(a&&p.label<a[2]){p.label=a[2],p.ops.push(d);break}a[2]&&p.ops.pop(),p.trys.pop();continue}d=r.call(n,p)}catch(n){d=[6,n],o=0}finally{i=a=0}if(5&d[0])throw d[1];return{value:d[0]?d[1]:void 0,done:!0}}([d,x])}}}function __spreadArray(n,r,i){if(i||2==arguments.length)for(var o,a=0,u=r.length;a<u;a++)!o&&a in r||(o||(o=Array.prototype.slice.call(r,0,a)),o[a]=r[a]);return n.concat(o||Array.prototype.slice.call(r))}},5885:function(n,r,i){"use strict";i.d(r,{Z:function(){return a}});var o=i(6489);function readCookie(n,r){void 0===r&&(r={});var i,o=n&&"j"===n[0]&&":"===n[1]?n.substr(2):n;if(void 0===(i=r.doNotParse)&&(i=!o||"{"!==o[0]&&"["!==o[0]&&'"'!==o[0]),!i)try{return JSON.parse(o)}catch(n){}return n}var __assign=function(){return(__assign=Object.assign||function(n){for(var r,i=1,o=arguments.length;i<o;i++)for(var a in r=arguments[i])Object.prototype.hasOwnProperty.call(r,a)&&(n[a]=r[a]);return n}).apply(this,arguments)},a=function(){function Cookies(n,r){var i=this;this.changeListeners=[],this.HAS_DOCUMENT_COOKIE=!1,this.cookies="string"==typeof n?o.Q(n,r):"object"==typeof n&&null!==n?n:{},new Promise(function(){i.HAS_DOCUMENT_COOKIE="object"==typeof document&&"string"==typeof document.cookie}).catch(function(){})}return Cookies.prototype._updateBrowserValues=function(n){this.HAS_DOCUMENT_COOKIE&&(this.cookies=o.Q(document.cookie,n))},Cookies.prototype._emitChange=function(n){for(var r=0;r<this.changeListeners.length;++r)this.changeListeners[r](n)},Cookies.prototype.get=function(n,r,i){return void 0===r&&(r={}),this._updateBrowserValues(i),readCookie(this.cookies[n],r)},Cookies.prototype.getAll=function(n,r){void 0===n&&(n={}),this._updateBrowserValues(r);var i={};for(var o in this.cookies)i[o]=readCookie(this.cookies[o],n);return i},Cookies.prototype.set=function(n,r,i){var a;"object"==typeof r&&(r=JSON.stringify(r)),this.cookies=__assign(__assign({},this.cookies),((a={})[n]=r,a)),this.HAS_DOCUMENT_COOKIE&&(document.cookie=o.q(n,r,i)),this._emitChange({name:n,value:r,options:i})},Cookies.prototype.remove=function(n,r){var i=r=__assign(__assign({},r),{expires:new Date(1970,1,1,0,0,1),maxAge:0});this.cookies=__assign({},this.cookies),delete this.cookies[n],this.HAS_DOCUMENT_COOKIE&&(document.cookie=o.q(n,"",i)),this._emitChange({name:n,value:void 0,options:r})},Cookies.prototype.addChangeListener=function(n){this.changeListeners.push(n)},Cookies.prototype.removeChangeListener=function(n){var r=this.changeListeners.indexOf(n);r>=0&&this.changeListeners.splice(r,1)},Cookies}()},2729:function(n,r,i){"use strict";function _tagged_template_literal(n,r){return r||(r=n.slice(0)),Object.freeze(Object.defineProperties(n,{raw:{value:Object.freeze(r)}}))}i.d(r,{_:function(){return _tagged_template_literal}})},7826:function(n,r,i){"use strict";function devAssert(n,r){if(!n)throw Error(r)}i.d(r,{a:function(){return devAssert}})},5821:function(n,r,i){"use strict";function inspect(n){return function formatValue(n,r){switch(typeof n){case"string":return JSON.stringify(n);case"function":return n.name?`[function ${n.name}]`:"[function]";case"object":return function(n,r){if(null===n)return"null";if(r.includes(n))return"[Circular]";let i=[...r,n];if("function"==typeof n.toJSON){let r=n.toJSON();if(r!==n)return"string"==typeof r?r:formatValue(r,i)}else if(Array.isArray(n))return function(n,r){if(0===n.length)return"[]";if(r.length>2)return"[Array]";let i=Math.min(10,n.length),o=n.length-i,a=[];for(let o=0;o<i;++o)a.push(formatValue(n[o],r));return 1===o?a.push("... 1 more item"):o>1&&a.push(`... ${o} more items`),"["+a.join(", ")+"]"}(n,i);return function(n,r){let i=Object.entries(n);if(0===i.length)return"{}";if(r.length>2)return"["+function(n){let r=Object.prototype.toString.call(n).replace(/^\[object /,"").replace(/]$/,"");if("Object"===r&&"function"==typeof n.constructor){let r=n.constructor.name;if("string"==typeof r&&""!==r)return r}return r}(n)+"]";let o=i.map(([n,i])=>n+": "+formatValue(i,r));return"{ "+o.join(", ")+" }"}(n,i)}(n,r);default:return String(n)}}(n,[])}i.d(r,{X:function(){return inspect}})},2380:function(n,r,i){"use strict";var o,a;i.d(r,{UG:function(){return isNode},WU:function(){return Token},Ye:function(){return Location},h8:function(){return u},ku:function(){return o}});let Location=class Location{constructor(n,r,i){this.start=n.start,this.end=r.end,this.startToken=n,this.endToken=r,this.source=i}get[Symbol.toStringTag](){return"Location"}toJSON(){return{start:this.start,end:this.end}}};let Token=class Token{constructor(n,r,i,o,a,u){this.kind=n,this.start=r,this.end=i,this.line=o,this.column=a,this.value=u,this.prev=null,this.next=null}get[Symbol.toStringTag](){return"Token"}toJSON(){return{kind:this.kind,value:this.value,line:this.line,column:this.column}}};let u={Name:[],Document:["definitions"],OperationDefinition:["name","variableDefinitions","directives","selectionSet"],VariableDefinition:["variable","type","defaultValue","directives"],Variable:["name"],SelectionSet:["selections"],Field:["alias","name","arguments","directives","selectionSet"],Argument:["name","value"],FragmentSpread:["name","directives"],InlineFragment:["typeCondition","directives","selectionSet"],FragmentDefinition:["name","variableDefinitions","typeCondition","directives","selectionSet"],IntValue:[],FloatValue:[],StringValue:[],BooleanValue:[],NullValue:[],EnumValue:[],ListValue:["values"],ObjectValue:["fields"],ObjectField:["name","value"],Directive:["name","arguments"],NamedType:["name"],ListType:["type"],NonNullType:["type"],SchemaDefinition:["description","directives","operationTypes"],OperationTypeDefinition:["type"],ScalarTypeDefinition:["description","name","directives"],ObjectTypeDefinition:["description","name","interfaces","directives","fields"],FieldDefinition:["description","name","arguments","type","directives"],InputValueDefinition:["description","name","type","defaultValue","directives"],InterfaceTypeDefinition:["description","name","interfaces","directives","fields"],UnionTypeDefinition:["description","name","directives","types"],EnumTypeDefinition:["description","name","directives","values"],EnumValueDefinition:["description","name","directives"],InputObjectTypeDefinition:["description","name","directives","fields"],DirectiveDefinition:["description","name","arguments","locations"],SchemaExtension:["directives","operationTypes"],ScalarTypeExtension:["name","directives"],ObjectTypeExtension:["name","interfaces","directives","fields"],InterfaceTypeExtension:["name","interfaces","directives","fields"],UnionTypeExtension:["name","directives","types"],EnumTypeExtension:["name","directives","values"],InputObjectTypeExtension:["name","directives","fields"]},p=new Set(Object.keys(u));function isNode(n){let r=null==n?void 0:n.kind;return"string"==typeof r&&p.has(r)}(a=o||(o={})).QUERY="query",a.MUTATION="mutation",a.SUBSCRIPTION="subscription"},7392:function(n,r,i){"use strict";i.d(r,{LZ:function(){return printBlockString},wv:function(){return dedentBlockStringLines}});var o=i(8297);function dedentBlockStringLines(n){var r,i;let a=Number.MAX_SAFE_INTEGER,u=null,p=-1;for(let r=0;r<n.length;++r){let d=n[r],x=function(n){let r=0;for(;r<n.length&&(0,o.FD)(n.charCodeAt(r));)++r;return r}(d);x!==d.length&&(u=null!==(i=u)&&void 0!==i?i:r,p=r,0!==r&&x<a&&(a=x))}return n.map((n,r)=>0===r?n:n.slice(a)).slice(null!==(r=u)&&void 0!==r?r:0,p+1)}function printBlockString(n,r){let i=n.replace(/"""/g,'\\"""'),a=i.split(/\r\n|[\n\r]/g),u=1===a.length,p=a.length>1&&a.slice(1).every(n=>0===n.length||(0,o.FD)(n.charCodeAt(0))),d=i.endsWith('\\"""'),x=n.endsWith('"')&&!d,w=n.endsWith("\\"),k=x||w,E=!(null!=r&&r.minimize)&&(!u||n.length>70||k||p||d),C="",D=u&&(0,o.FD)(n.charCodeAt(0));return(E&&!D||p)&&(C+="\n"),C+=i,(E||k)&&(C+="\n"),'"""'+C+'"""'}},8297:function(n,r,i){"use strict";function isWhiteSpace(n){return 9===n||32===n}function isDigit(n){return n>=48&&n<=57}function isLetter(n){return n>=97&&n<=122||n>=65&&n<=90}function isNameStart(n){return isLetter(n)||95===n}function isNameContinue(n){return isLetter(n)||isDigit(n)||95===n}i.d(r,{FD:function(){return isWhiteSpace},HQ:function(){return isNameContinue},LQ:function(){return isNameStart},X1:function(){return isDigit}})},7359:function(n,r,i){"use strict";var o,a;i.d(r,{h:function(){return o}}),(a=o||(o={})).NAME="Name",a.DOCUMENT="Document",a.OPERATION_DEFINITION="OperationDefinition",a.VARIABLE_DEFINITION="VariableDefinition",a.SELECTION_SET="SelectionSet",a.FIELD="Field",a.ARGUMENT="Argument",a.FRAGMENT_SPREAD="FragmentSpread",a.INLINE_FRAGMENT="InlineFragment",a.FRAGMENT_DEFINITION="FragmentDefinition",a.VARIABLE="Variable",a.INT="IntValue",a.FLOAT="FloatValue",a.STRING="StringValue",a.BOOLEAN="BooleanValue",a.NULL="NullValue",a.ENUM="EnumValue",a.LIST="ListValue",a.OBJECT="ObjectValue",a.OBJECT_FIELD="ObjectField",a.DIRECTIVE="Directive",a.NAMED_TYPE="NamedType",a.LIST_TYPE="ListType",a.NON_NULL_TYPE="NonNullType",a.SCHEMA_DEFINITION="SchemaDefinition",a.OPERATION_TYPE_DEFINITION="OperationTypeDefinition",a.SCALAR_TYPE_DEFINITION="ScalarTypeDefinition",a.OBJECT_TYPE_DEFINITION="ObjectTypeDefinition",a.FIELD_DEFINITION="FieldDefinition",a.INPUT_VALUE_DEFINITION="InputValueDefinition",a.INTERFACE_TYPE_DEFINITION="InterfaceTypeDefinition",a.UNION_TYPE_DEFINITION="UnionTypeDefinition",a.ENUM_TYPE_DEFINITION="EnumTypeDefinition",a.ENUM_VALUE_DEFINITION="EnumValueDefinition",a.INPUT_OBJECT_TYPE_DEFINITION="InputObjectTypeDefinition",a.DIRECTIVE_DEFINITION="DirectiveDefinition",a.SCHEMA_EXTENSION="SchemaExtension",a.SCALAR_TYPE_EXTENSION="ScalarTypeExtension",a.OBJECT_TYPE_EXTENSION="ObjectTypeExtension",a.INTERFACE_TYPE_EXTENSION="InterfaceTypeExtension",a.UNION_TYPE_EXTENSION="UnionTypeExtension",a.ENUM_TYPE_EXTENSION="EnumTypeExtension",a.INPUT_OBJECT_TYPE_EXTENSION="InputObjectTypeExtension"},1270:function(n,r,i){"use strict";i.d(r,{H:function(){return Source},T:function(){return isSource}});var o=i(7826),a=i(5821);let Source=class Source{constructor(n,r="GraphQL request",i={line:1,column:1}){"string"==typeof n||(0,o.a)(!1,`Body must be a string. Received: ${(0,a.X)(n)}.`),this.body=n,this.name=r,this.locationOffset=i,this.locationOffset.line>0||(0,o.a)(!1,"line in locationOffset is 1-indexed and must be positive."),this.locationOffset.column>0||(0,o.a)(!1,"column in locationOffset is 1-indexed and must be positive.")}get[Symbol.toStringTag](){return"Source"}};function isSource(n){return n instanceof Source}}},function(n){var __webpack_exec__=function(r){return n(n.s=r)};n.O(0,[774,179],function(){return __webpack_exec__(1118),__webpack_exec__(4751)}),_N_E=n.O()}]);