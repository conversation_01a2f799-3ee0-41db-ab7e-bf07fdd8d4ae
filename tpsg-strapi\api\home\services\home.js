'use strict';

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-services)
 * to customize this service
 */

module.exports = {
  /**
   * build: met à jour les différentes sections de la home page.
   * @returns {boolean} 0: échec mise à jour / 1: success
   */
  updateSections: async (data) => {
    /**
     * Permet de stocker les posts présents sur la Home. On s'en servira pour éviter
     * les doublons en l'utilisant comme paramètre dans les requêtes successives.
     */
    let nin = [];

    /**
     * Récupération des données de la Home page dans le cas où la mise à jour n'a pas
     * été faite depuis la page elle-même.
     */
    if (!data) {
      data = await strapi.query("home").findOne();
    }

    const { limits } = data.config


    const {
      articles,
      podcasts,
      bloggers,
      topics,
      webinars,
      formations,
      featured
    } = data;

    if(featured.length > 0) {
      featured.forEach(item => {
        if(item.postRef) {
          nin = nin.concat(item.postRef.id);
        }
      })
    }
    if(articles){
      let articlesIDs = await strapi.config.functions['updates'].updateHomeArticles({
        section: articles,
        limit: limits?.articles || 10,
        nin: nin
      });
      nin = nin.concat(articlesIDs);
    }
    if(podcasts){
      let podcastsIDs = await strapi.config.functions['updates'].updateHomePodcasts({
        section: podcasts,
        limit: limits?.podcasts || 4,
        nin: nin
      });
      nin = nin.concat(podcastsIDs);
    }
    if(bloggers){
      let bloggersIDs = await strapi.config.functions['updates'].updateHomeBloggers({
        section: bloggers,
        limit: limits?.bloggers || 4,
        nin: nin,
      });
      nin = nin.concat(bloggersIDs)
    }
    if(topics){
      for(const topic of topics){
        let topicsIDs = await strapi.config.functions['updates'].updateHomeTopics({
          section: topic,
          limit: limits?.topics || 3,
          nin: nin,
        });
        nin = nin.concat(topicsIDs)
      }
    }
    if(webinars){
      let webinarIds = await strapi.config.functions['updates'].updateHomeWebinars({
        section: webinars,
        limit: limits?.webinars || 1,
        nin: nin
      })
      nin = nin.concat(webinarIds);
    }
    if(formations){
      let webinarIds = await strapi.config.functions['updates'].updateHomeFormations({
        section: formations,
        limit: limits?.webinars || 1,
        nin: nin
      })
      nin = nin.concat(webinarIds);
    }
  }
};
