input FileInfoInput {
  name: String
  alternativeText: String
  caption: String
}

type UsersPermissionsMe {
  id: ID!
  username: String!
  email: String!
  confirmed: Boolean
  blocked: Boolean
  role: UsersPermissionsMeRole
}

type UsersPermissionsMeRole {
  id: ID!
  name: String!
  description: String
  type: String
}

input UsersPermissionsRegisterInput {
  username: String!
  email: String!
  password: String!
}

input UsersPermissionsLoginInput {
  identifier: String!
  password: String!
  provider: String = "local"
}

type UsersPermissionsLoginPayload {
  jwt: String
  user: UsersPermissionsMe!
}

type UserPermissionsPasswordPayload {
  ok: Boolean!
}

type Related {
  post: Post!
  score: Int!
  section: String!
  origin: String!
}

type Author {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  firstName: String
  lastName: String
  fullName: String!
  about: String
  picture: UploadFile
  slug: String
}

type AuthorConnection {
  values: [Author]
  groupBy: AuthorGroupBy
  aggregate: AuthorAggregator
}

type AuthorAggregator {
  count: Int
  totalCount: Int
}

type AuthorGroupBy {
  id: [AuthorConnectionId]
  created_at: [AuthorConnectionCreated_at]
  updated_at: [AuthorConnectionUpdated_at]
  firstName: [AuthorConnectionFirstName]
  lastName: [AuthorConnectionLastName]
  fullName: [AuthorConnectionFullName]
  about: [AuthorConnectionAbout]
  picture: [AuthorConnectionPicture]
  slug: [AuthorConnectionSlug]
}

type AuthorConnectionId {
  key: ID
  connection: AuthorConnection
}

type AuthorConnectionCreated_at {
  key: DateTime
  connection: AuthorConnection
}

type AuthorConnectionUpdated_at {
  key: DateTime
  connection: AuthorConnection
}

type AuthorConnectionFirstName {
  key: String
  connection: AuthorConnection
}

type AuthorConnectionLastName {
  key: String
  connection: AuthorConnection
}

type AuthorConnectionFullName {
  key: String
  connection: AuthorConnection
}

type AuthorConnectionAbout {
  key: String
  connection: AuthorConnection
}

type AuthorConnectionPicture {
  key: ID
  connection: AuthorConnection
}

type AuthorConnectionSlug {
  key: String
  connection: AuthorConnection
}

input AuthorInput {
  firstName: String
  lastName: String
  fullName: String!
  about: String
  picture: ID
  slug: String
  created_by: ID
  updated_by: ID
}

input editAuthorInput {
  firstName: String
  lastName: String
  fullName: String
  about: String
  picture: ID
  slug: String
  created_by: ID
  updated_by: ID
}

input createAuthorInput {
  data: AuthorInput
}

type createAuthorPayload {
  author: Author
}

input updateAuthorInput {
  where: InputID
  data: editAuthorInput
}

type updateAuthorPayload {
  author: Author
}

input deleteAuthorInput {
  where: InputID
}

type deleteAuthorPayload {
  author: Author
}

type Blog {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  blogger: Author
  slug: String
  featured: [ComponentModuleFeatured]
  menu: [ComponentNavigationNavUrl]
  newsletter: String
  author_user: AdminUser
}

type BlogConnection {
  values: [Blog]
  groupBy: BlogGroupBy
  aggregate: BlogAggregator
}

type BlogAggregator {
  count: Int
  totalCount: Int
}

type BlogGroupBy {
  id: [BlogConnectionId]
  created_at: [BlogConnectionCreated_at]
  updated_at: [BlogConnectionUpdated_at]
  blogger: [BlogConnectionBlogger]
  slug: [BlogConnectionSlug]
  newsletter: [BlogConnectionNewsletter]
  author_user: [BlogConnectionAuthor_user]
}

type BlogConnectionId {
  key: ID
  connection: BlogConnection
}

type BlogConnectionCreated_at {
  key: DateTime
  connection: BlogConnection
}

type BlogConnectionUpdated_at {
  key: DateTime
  connection: BlogConnection
}

type BlogConnectionBlogger {
  key: ID
  connection: BlogConnection
}

type BlogConnectionSlug {
  key: String
  connection: BlogConnection
}

type BlogConnectionNewsletter {
  key: String
  connection: BlogConnection
}

type BlogConnectionAuthor_user {
  key: ID
  connection: BlogConnection
}

input BlogInput {
  blogger: ID
  slug: String
  featured: [ComponentModuleFeaturedInput]
  menu: [ComponentNavigationNavUrlInput]
  newsletter: String
  author_user: ID
  created_by: ID
  updated_by: ID
}

input editBlogInput {
  blogger: ID
  slug: String
  featured: [editComponentModuleFeaturedInput]
  menu: [editComponentNavigationNavUrlInput]
  newsletter: String
  author_user: ID
  created_by: ID
  updated_by: ID
}

input createBlogInput {
  data: BlogInput
}

type createBlogPayload {
  blog: Blog
}

input updateBlogInput {
  where: InputID
  data: editBlogInput
}

type updateBlogPayload {
  blog: Blog
}

input deleteBlogInput {
  where: InputID
}

type deleteBlogPayload {
  blog: Blog
}

enum ENUM_CRONPOST_TRIGGER {
  update
  create
  delete
}

type CronPost {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  postTitle: String
  postId: Long
  trigger: ENUM_CRONPOST_TRIGGER
}

type CronPostConnection {
  values: [CronPost]
  groupBy: CronPostGroupBy
  aggregate: CronPostAggregator
}

type CronPostAggregator {
  count: Int
  totalCount: Int
}

type CronPostGroupBy {
  id: [CronPostConnectionId]
  created_at: [CronPostConnectionCreated_at]
  updated_at: [CronPostConnectionUpdated_at]
  postTitle: [CronPostConnectionPostTitle]
  postId: [CronPostConnectionPostId]
  trigger: [CronPostConnectionTrigger]
}

type CronPostConnectionId {
  key: ID
  connection: CronPostConnection
}

type CronPostConnectionCreated_at {
  key: DateTime
  connection: CronPostConnection
}

type CronPostConnectionUpdated_at {
  key: DateTime
  connection: CronPostConnection
}

type CronPostConnectionPostTitle {
  key: String
  connection: CronPostConnection
}

type CronPostConnectionPostId {
  key: ID
  connection: CronPostConnection
}

type CronPostConnectionTrigger {
  key: String
  connection: CronPostConnection
}

input CronPostInput {
  postTitle: String
  postId: Long
  trigger: ENUM_CRONPOST_TRIGGER
  created_by: ID
  updated_by: ID
}

input editCronPostInput {
  postTitle: String
  postId: Long
  trigger: ENUM_CRONPOST_TRIGGER
  created_by: ID
  updated_by: ID
}

input createCronPostInput {
  data: CronPostInput
}

type createCronPostPayload {
  cronPost: CronPost
}

input updateCronPostInput {
  where: InputID
  data: editCronPostInput
}

type updateCronPostPayload {
  cronPost: CronPost
}

input deleteCronPostInput {
  where: InputID
}

type deleteCronPostPayload {
  cronPost: CronPost
}

union FormationsSectionsDynamicZone = ComponentSectionPostSet

"""Input type for dynamic zone sections of Formations"""
scalar FormationsSectionsDynamicZoneInput

type Formations {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  sections: [FormationsSectionsDynamicZone]
}

input FormationInput {
  sections: [FormationsSectionsDynamicZoneInput!]
  created_by: ID
  updated_by: ID
}

input editFormationInput {
  sections: [FormationsSectionsDynamicZoneInput!]
  created_by: ID
  updated_by: ID
}

input updateFormationInput {
  data: editFormationInput
}

type updateFormationPayload {
  formation: Formations
}

type deleteFormationPayload {
  formation: Formations
}

type Home {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  articles: ComponentSectionPostSet
  featured: [ComponentModuleFeatured]
  mission: ComponentSectionMission
  podcasts: ComponentSectionPostSet
  webinars: ComponentSectionPostSet
  formations: ComponentSectionPostSet
  bloggers: ComponentSectionPostSet
  quote: ComponentSectionQuote
  topics: [ComponentSectionPostSet]
  shop: ComponentSectionShop
  mostRead: ComponentSectionPostSet
  config: JSON
  newsletter: String
}

input HomeInput {
  articles: ComponentSectionPostSetInput
  featured: [ComponentModuleFeaturedInput]
  mission: ComponentSectionMissionInput
  podcasts: ComponentSectionPostSetInput
  webinars: ComponentSectionPostSetInput
  formations: ComponentSectionPostSetInput
  bloggers: ComponentSectionPostSetInput
  quote: ComponentSectionQuoteInput
  topics: [ComponentSectionPostSetInput]
  shop: ComponentSectionShopInput
  mostRead: ComponentSectionPostSetInput
  config: JSON
  newsletter: String
  created_by: ID
  updated_by: ID
}

input editHomeInput {
  articles: editComponentSectionPostSetInput
  featured: [editComponentModuleFeaturedInput]
  mission: editComponentSectionMissionInput
  podcasts: editComponentSectionPostSetInput
  webinars: editComponentSectionPostSetInput
  formations: editComponentSectionPostSetInput
  bloggers: editComponentSectionPostSetInput
  quote: editComponentSectionQuoteInput
  topics: [editComponentSectionPostSetInput]
  shop: editComponentSectionShopInput
  mostRead: editComponentSectionPostSetInput
  config: JSON
  newsletter: String
  created_by: ID
  updated_by: ID
}

input updateHomeInput {
  data: editHomeInput
}

type updateHomePayload {
  home: Home
}

type deleteHomePayload {
  home: Home
}

type OldTopics {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  name: String
  slug: String
  topic: Topic
}

type OldTopicsConnection {
  values: [OldTopics]
  groupBy: OldTopicsGroupBy
  aggregate: OldTopicsAggregator
}

type OldTopicsAggregator {
  count: Int
  totalCount: Int
}

type OldTopicsGroupBy {
  id: [OldTopicsConnectionId]
  created_at: [OldTopicsConnectionCreated_at]
  updated_at: [OldTopicsConnectionUpdated_at]
  name: [OldTopicsConnectionName]
  slug: [OldTopicsConnectionSlug]
  topic: [OldTopicsConnectionTopic]
}

type OldTopicsConnectionId {
  key: ID
  connection: OldTopicsConnection
}

type OldTopicsConnectionCreated_at {
  key: DateTime
  connection: OldTopicsConnection
}

type OldTopicsConnectionUpdated_at {
  key: DateTime
  connection: OldTopicsConnection
}

type OldTopicsConnectionName {
  key: String
  connection: OldTopicsConnection
}

type OldTopicsConnectionSlug {
  key: String
  connection: OldTopicsConnection
}

type OldTopicsConnectionTopic {
  key: ID
  connection: OldTopicsConnection
}

input OldTopicInput {
  name: String
  slug: String
  topic: ID
  created_by: ID
  updated_by: ID
}

input editOldTopicInput {
  name: String
  slug: String
  topic: ID
  created_by: ID
  updated_by: ID
}

input createOldTopicInput {
  data: OldTopicInput
}

type createOldTopicPayload {
  oldTopic: OldTopics
}

input updateOldTopicInput {
  where: InputID
  data: editOldTopicInput
}

type updateOldTopicPayload {
  oldTopic: OldTopics
}

input deleteOldTopicInput {
  where: InputID
}

type deleteOldTopicPayload {
  oldTopic: OldTopics
}

union PageBlocksDynamicZone = ComponentBlockText

"""Input type for dynamic zone blocks of Page"""
scalar PageBlocksDynamicZoneInput

type Page {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  blocks: [PageBlocksDynamicZone]
  title: String
  slug: String!
  cover: UploadFile
  metas: ComponentModuleSeo
  published_at: DateTime
}

type PageConnection {
  values: [Page]
  groupBy: PageGroupBy
  aggregate: PageAggregator
}

type PageAggregator {
  count: Int
  totalCount: Int
}

type PageGroupBy {
  id: [PageConnectionId]
  created_at: [PageConnectionCreated_at]
  updated_at: [PageConnectionUpdated_at]
  title: [PageConnectionTitle]
  slug: [PageConnectionSlug]
  cover: [PageConnectionCover]
  metas: [PageConnectionMetas]
  published_at: [PageConnectionPublished_at]
}

type PageConnectionId {
  key: ID
  connection: PageConnection
}

type PageConnectionCreated_at {
  key: DateTime
  connection: PageConnection
}

type PageConnectionUpdated_at {
  key: DateTime
  connection: PageConnection
}

type PageConnectionTitle {
  key: String
  connection: PageConnection
}

type PageConnectionSlug {
  key: String
  connection: PageConnection
}

type PageConnectionCover {
  key: ID
  connection: PageConnection
}

type PageConnectionMetas {
  key: ID
  connection: PageConnection
}

type PageConnectionPublished_at {
  key: DateTime
  connection: PageConnection
}

input PageInput {
  blocks: [PageBlocksDynamicZoneInput!]
  title: String
  slug: String!
  cover: ID
  metas: ComponentModuleSeoInput
  published_at: DateTime
  created_by: ID
  updated_by: ID
}

input editPageInput {
  blocks: [PageBlocksDynamicZoneInput!]
  title: String
  slug: String
  cover: ID
  metas: editComponentModuleSeoInput
  published_at: DateTime
  created_by: ID
  updated_by: ID
}

input createPageInput {
  data: PageInput
}

type createPagePayload {
  page: Page
}

input updatePageInput {
  where: InputID
  data: editPageInput
}

type updatePagePayload {
  page: Page
}

input deletePageInput {
  where: InputID
}

type deletePagePayload {
  page: Page
}

union ParcoursEmailsSectionsDynamicZone = ComponentSectionPostSet

"""Input type for dynamic zone sections of ParcoursEmails"""
scalar ParcoursEmailsSectionsDynamicZoneInput

type ParcoursEmails {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  sections: [ParcoursEmailsSectionsDynamicZone]
}

input ParcoursEmailInput {
  sections: [ParcoursEmailsSectionsDynamicZoneInput!]
  created_by: ID
  updated_by: ID
}

input editParcoursEmailInput {
  sections: [ParcoursEmailsSectionsDynamicZoneInput!]
  created_by: ID
  updated_by: ID
}

input updateParcoursEmailInput {
  data: editParcoursEmailInput
}

type updateParcoursEmailPayload {
  parcoursEmail: ParcoursEmails
}

type deleteParcoursEmailPayload {
  parcoursEmail: ParcoursEmails
}

type Podcast {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  name: String!
  slug: String
  description: String!
  cover: UploadFile
  count: Int
  platforms: [ComponentAtomPlatform]
  averageDuration: Int
  logoSmall: UploadFile
  speakers(sort: String, limit: Int, start: Int, where: JSON): [Author]
}

type PodcastConnection {
  values: [Podcast]
  groupBy: PodcastGroupBy
  aggregate: PodcastAggregator
}

type PodcastAggregator {
  count: Int
  totalCount: Int
  sum: PodcastAggregatorSum
  avg: PodcastAggregatorAvg
  min: PodcastAggregatorMin
  max: PodcastAggregatorMax
}

type PodcastAggregatorSum {
  count: Float
  averageDuration: Float
}

type PodcastAggregatorAvg {
  count: Float
  averageDuration: Float
}

type PodcastAggregatorMin {
  count: Float
  averageDuration: Float
}

type PodcastAggregatorMax {
  count: Float
  averageDuration: Float
}

type PodcastGroupBy {
  id: [PodcastConnectionId]
  created_at: [PodcastConnectionCreated_at]
  updated_at: [PodcastConnectionUpdated_at]
  name: [PodcastConnectionName]
  slug: [PodcastConnectionSlug]
  description: [PodcastConnectionDescription]
  cover: [PodcastConnectionCover]
  count: [PodcastConnectionCount]
  averageDuration: [PodcastConnectionAverageDuration]
  logoSmall: [PodcastConnectionLogoSmall]
}

type PodcastConnectionId {
  key: ID
  connection: PodcastConnection
}

type PodcastConnectionCreated_at {
  key: DateTime
  connection: PodcastConnection
}

type PodcastConnectionUpdated_at {
  key: DateTime
  connection: PodcastConnection
}

type PodcastConnectionName {
  key: String
  connection: PodcastConnection
}

type PodcastConnectionSlug {
  key: String
  connection: PodcastConnection
}

type PodcastConnectionDescription {
  key: String
  connection: PodcastConnection
}

type PodcastConnectionCover {
  key: ID
  connection: PodcastConnection
}

type PodcastConnectionCount {
  key: Int
  connection: PodcastConnection
}

type PodcastConnectionAverageDuration {
  key: Int
  connection: PodcastConnection
}

type PodcastConnectionLogoSmall {
  key: ID
  connection: PodcastConnection
}

input PodcastInput {
  name: String!
  slug: String
  description: String!
  cover: ID
  speakers: [ID]
  count: Int
  platforms: [ComponentAtomPlatformInput]
  averageDuration: Int
  logoSmall: ID
  created_by: ID
  updated_by: ID
}

input editPodcastInput {
  name: String
  slug: String
  description: String
  cover: ID
  speakers: [ID]
  count: Int
  platforms: [editComponentAtomPlatformInput]
  averageDuration: Int
  logoSmall: ID
  created_by: ID
  updated_by: ID
}

input createPodcastInput {
  data: PodcastInput
}

type createPodcastPayload {
  podcast: Podcast
}

input updatePodcastInput {
  where: InputID
  data: editPodcastInput
}

type updatePodcastPayload {
  podcast: Podcast
}

input deletePodcastInput {
  where: InputID
}

type deletePodcastPayload {
  podcast: Podcast
}

type Popup {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  title: String
  body: String
  image: UploadFile
  endDate: DateTime
  startDate: DateTime
  button: ComponentAtomButton
  published_at: DateTime
}

type PopupConnection {
  values: [Popup]
  groupBy: PopupGroupBy
  aggregate: PopupAggregator
}

type PopupAggregator {
  count: Int
  totalCount: Int
}

type PopupGroupBy {
  id: [PopupConnectionId]
  created_at: [PopupConnectionCreated_at]
  updated_at: [PopupConnectionUpdated_at]
  title: [PopupConnectionTitle]
  body: [PopupConnectionBody]
  image: [PopupConnectionImage]
  endDate: [PopupConnectionEndDate]
  startDate: [PopupConnectionStartDate]
  button: [PopupConnectionButton]
  published_at: [PopupConnectionPublished_at]
}

type PopupConnectionId {
  key: ID
  connection: PopupConnection
}

type PopupConnectionCreated_at {
  key: DateTime
  connection: PopupConnection
}

type PopupConnectionUpdated_at {
  key: DateTime
  connection: PopupConnection
}

type PopupConnectionTitle {
  key: String
  connection: PopupConnection
}

type PopupConnectionBody {
  key: String
  connection: PopupConnection
}

type PopupConnectionImage {
  key: ID
  connection: PopupConnection
}

type PopupConnectionEndDate {
  key: DateTime
  connection: PopupConnection
}

type PopupConnectionStartDate {
  key: DateTime
  connection: PopupConnection
}

type PopupConnectionButton {
  key: ID
  connection: PopupConnection
}

type PopupConnectionPublished_at {
  key: DateTime
  connection: PopupConnection
}

input PopupInput {
  title: String
  body: String
  image: ID
  endDate: DateTime
  startDate: DateTime
  button: ComponentAtomButtonInput
  published_at: DateTime
  created_by: ID
  updated_by: ID
}

input editPopupInput {
  title: String
  body: String
  image: ID
  endDate: DateTime
  startDate: DateTime
  button: editComponentAtomButtonInput
  published_at: DateTime
  created_by: ID
  updated_by: ID
}

input createPopupInput {
  data: PopupInput
}

type createPopupPayload {
  popup: Popup
}

input updatePopupInput {
  where: InputID
  data: editPopupInput
}

type updatePopupPayload {
  popup: Popup
}

input deletePopupInput {
  where: InputID
}

type deletePopupPayload {
  popup: Popup
}

enum ENUM_POST_TYPE {
  article
  formation
  parcours
  podcast
  webinaire
}

union PostModulesDynamicZone = ComponentModuleEvent | ComponentModuleFormation | ComponentModuleLead | ComponentModulePodcast | ComponentModuleSeo | ComponentModuleWebinar | ComponentModuleEmailJourney

"""Input type for dynamic zone modules of Post"""
scalar PostModulesDynamicZoneInput

type Post {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  title: String
  slug: String!
  body: String
  image: UploadFile
  serie: Serie
  author: Author
  modules: [PostModulesDynamicZone]
  wp_created_at: DateTime
  blog: Blog
  isCornerStone: Boolean
  type: ENUM_POST_TYPE
  publish_at: DateTime
  wp_published_at: DateTime
  readingTime: Int
  published_at: DateTime
  tags(sort: String, limit: Int, start: Int, where: JSON): [Tag]
  topics(sort: String, limit: Int, start: Int, where: JSON): [Topic]
  old_topics(sort: String, limit: Int, start: Int, where: JSON): [OldTopics]
}

type PostConnection {
  values: [Post]
  groupBy: PostGroupBy
  aggregate: PostAggregator
}

type PostAggregator {
  count: Int
  totalCount: Int
  sum: PostAggregatorSum
  avg: PostAggregatorAvg
  min: PostAggregatorMin
  max: PostAggregatorMax
}

type PostAggregatorSum {
  readingTime: Float
}

type PostAggregatorAvg {
  readingTime: Float
}

type PostAggregatorMin {
  readingTime: Float
}

type PostAggregatorMax {
  readingTime: Float
}

type PostGroupBy {
  id: [PostConnectionId]
  created_at: [PostConnectionCreated_at]
  updated_at: [PostConnectionUpdated_at]
  title: [PostConnectionTitle]
  slug: [PostConnectionSlug]
  body: [PostConnectionBody]
  image: [PostConnectionImage]
  serie: [PostConnectionSerie]
  author: [PostConnectionAuthor]
  wp_created_at: [PostConnectionWp_created_at]
  blog: [PostConnectionBlog]
  isCornerStone: [PostConnectionIsCornerStone]
  type: [PostConnectionType]
  publish_at: [PostConnectionPublish_at]
  wp_published_at: [PostConnectionWp_published_at]
  readingTime: [PostConnectionReadingTime]
  published_at: [PostConnectionPublished_at]
}

type PostConnectionId {
  key: ID
  connection: PostConnection
}

type PostConnectionCreated_at {
  key: DateTime
  connection: PostConnection
}

type PostConnectionUpdated_at {
  key: DateTime
  connection: PostConnection
}

type PostConnectionTitle {
  key: String
  connection: PostConnection
}

type PostConnectionSlug {
  key: String
  connection: PostConnection
}

type PostConnectionBody {
  key: String
  connection: PostConnection
}

type PostConnectionImage {
  key: ID
  connection: PostConnection
}

type PostConnectionSerie {
  key: ID
  connection: PostConnection
}

type PostConnectionAuthor {
  key: ID
  connection: PostConnection
}

type PostConnectionWp_created_at {
  key: DateTime
  connection: PostConnection
}

type PostConnectionBlog {
  key: ID
  connection: PostConnection
}

type PostConnectionIsCornerStone {
  key: Boolean
  connection: PostConnection
}

type PostConnectionType {
  key: String
  connection: PostConnection
}

type PostConnectionPublish_at {
  key: DateTime
  connection: PostConnection
}

type PostConnectionWp_published_at {
  key: DateTime
  connection: PostConnection
}

type PostConnectionReadingTime {
  key: Int
  connection: PostConnection
}

type PostConnectionPublished_at {
  key: DateTime
  connection: PostConnection
}

input PostInput {
  title: String
  slug: String!
  body: String
  image: ID
  serie: ID
  tags: [ID]
  topics: [ID]
  author: ID
  modules: [PostModulesDynamicZoneInput!]
  old_topics: [ID]
  wp_created_at: DateTime
  blog: ID
  isCornerStone: Boolean
  type: ENUM_POST_TYPE
  publish_at: DateTime
  wp_published_at: DateTime
  readingTime: Int
  published_at: DateTime
  created_by: ID
  updated_by: ID
}

input editPostInput {
  title: String
  slug: String
  body: String
  image: ID
  serie: ID
  tags: [ID]
  topics: [ID]
  author: ID
  modules: [PostModulesDynamicZoneInput!]
  old_topics: [ID]
  wp_created_at: DateTime
  blog: ID
  isCornerStone: Boolean
  type: ENUM_POST_TYPE
  publish_at: DateTime
  wp_published_at: DateTime
  readingTime: Int
  published_at: DateTime
  created_by: ID
  updated_by: ID
}

input createPostInput {
  data: PostInput
}

type createPostPayload {
  post: Post
}

input updatePostInput {
  where: InputID
  data: editPostInput
}

type updatePostPayload {
  post: Post
}

input deletePostInput {
  where: InputID
}

type deletePostPayload {
  post: Post
}

type Serie {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  name: String!
  slug: String!
  description: String
  posts(sort: String, limit: Int, start: Int, where: JSON): [Post]
}

type SerieConnection {
  values: [Serie]
  groupBy: SerieGroupBy
  aggregate: SerieAggregator
}

type SerieAggregator {
  count: Int
  totalCount: Int
}

type SerieGroupBy {
  id: [SerieConnectionId]
  created_at: [SerieConnectionCreated_at]
  updated_at: [SerieConnectionUpdated_at]
  name: [SerieConnectionName]
  slug: [SerieConnectionSlug]
  description: [SerieConnectionDescription]
}

type SerieConnectionId {
  key: ID
  connection: SerieConnection
}

type SerieConnectionCreated_at {
  key: DateTime
  connection: SerieConnection
}

type SerieConnectionUpdated_at {
  key: DateTime
  connection: SerieConnection
}

type SerieConnectionName {
  key: String
  connection: SerieConnection
}

type SerieConnectionSlug {
  key: String
  connection: SerieConnection
}

type SerieConnectionDescription {
  key: String
  connection: SerieConnection
}

input SerieInput {
  name: String!
  slug: String!
  description: String
  posts: [ID]
  created_by: ID
  updated_by: ID
}

input editSerieInput {
  name: String
  slug: String
  description: String
  posts: [ID]
  created_by: ID
  updated_by: ID
}

input createSerieInput {
  data: SerieInput
}

type createSeriePayload {
  serie: Serie
}

input updateSerieInput {
  where: InputID
  data: editSerieInput
}

type updateSeriePayload {
  serie: Serie
}

input deleteSerieInput {
  where: InputID
}

type deleteSeriePayload {
  serie: Serie
}

type Tag {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  name: String!
  slug: String
}

type TagConnection {
  values: [Tag]
  groupBy: TagGroupBy
  aggregate: TagAggregator
}

type TagAggregator {
  count: Int
  totalCount: Int
}

type TagGroupBy {
  id: [TagConnectionId]
  created_at: [TagConnectionCreated_at]
  updated_at: [TagConnectionUpdated_at]
  name: [TagConnectionName]
  slug: [TagConnectionSlug]
}

type TagConnectionId {
  key: ID
  connection: TagConnection
}

type TagConnectionCreated_at {
  key: DateTime
  connection: TagConnection
}

type TagConnectionUpdated_at {
  key: DateTime
  connection: TagConnection
}

type TagConnectionName {
  key: String
  connection: TagConnection
}

type TagConnectionSlug {
  key: String
  connection: TagConnection
}

input TagInput {
  name: String!
  slug: String
  created_by: ID
  updated_by: ID
}

input editTagInput {
  name: String
  slug: String
  created_by: ID
  updated_by: ID
}

input createTagInput {
  data: TagInput
}

type createTagPayload {
  tag: Tag
}

input updateTagInput {
  where: InputID
  data: editTagInput
}

type updateTagPayload {
  tag: Tag
}

input deleteTagInput {
  where: InputID
}

type deleteTagPayload {
  tag: Tag
}

enum ENUM_TOPICGROUP_TYPE {
  ministere
  vocation
}

type TopicGroup {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  name: String!
  slug: String!
  description: String
  cover: UploadFile
  type: ENUM_TOPICGROUP_TYPE!
  featured: [ComponentModuleFeatured]
  parent: TopicGroup
  topics(sort: String, limit: Int, start: Int, where: JSON): [Topic]
  children(sort: String, limit: Int, start: Int, where: JSON): [TopicGroup]
}

type TopicGroupConnection {
  values: [TopicGroup]
  groupBy: TopicGroupGroupBy
  aggregate: TopicGroupAggregator
}

type TopicGroupAggregator {
  count: Int
  totalCount: Int
}

type TopicGroupGroupBy {
  id: [TopicGroupConnectionId]
  created_at: [TopicGroupConnectionCreated_at]
  updated_at: [TopicGroupConnectionUpdated_at]
  name: [TopicGroupConnectionName]
  slug: [TopicGroupConnectionSlug]
  description: [TopicGroupConnectionDescription]
  cover: [TopicGroupConnectionCover]
  type: [TopicGroupConnectionType]
  parent: [TopicGroupConnectionParent]
}

type TopicGroupConnectionId {
  key: ID
  connection: TopicGroupConnection
}

type TopicGroupConnectionCreated_at {
  key: DateTime
  connection: TopicGroupConnection
}

type TopicGroupConnectionUpdated_at {
  key: DateTime
  connection: TopicGroupConnection
}

type TopicGroupConnectionName {
  key: String
  connection: TopicGroupConnection
}

type TopicGroupConnectionSlug {
  key: String
  connection: TopicGroupConnection
}

type TopicGroupConnectionDescription {
  key: String
  connection: TopicGroupConnection
}

type TopicGroupConnectionCover {
  key: ID
  connection: TopicGroupConnection
}

type TopicGroupConnectionType {
  key: String
  connection: TopicGroupConnection
}

type TopicGroupConnectionParent {
  key: ID
  connection: TopicGroupConnection
}

input TopicGroupInput {
  name: String!
  slug: String!
  description: String
  cover: ID
  topics: [ID]
  type: ENUM_TOPICGROUP_TYPE!
  featured: [ComponentModuleFeaturedInput]
  children: [ID]
  parent: ID
  created_by: ID
  updated_by: ID
}

input editTopicGroupInput {
  name: String
  slug: String
  description: String
  cover: ID
  topics: [ID]
  type: ENUM_TOPICGROUP_TYPE
  featured: [editComponentModuleFeaturedInput]
  children: [ID]
  parent: ID
  created_by: ID
  updated_by: ID
}

input createTopicGroupInput {
  data: TopicGroupInput
}

type createTopicGroupPayload {
  topicGroup: TopicGroup
}

input updateTopicGroupInput {
  where: InputID
  data: editTopicGroupInput
}

type updateTopicGroupPayload {
  topicGroup: TopicGroup
}

input deleteTopicGroupInput {
  where: InputID
}

type deleteTopicGroupPayload {
  topicGroup: TopicGroup
}

type Topic {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  name: String!
  slug: String!
  parent: Topic
  description: String
  parentSlug: String
  postCount: Int
}

type TopicConnection {
  values: [Topic]
  groupBy: TopicGroupBy
  aggregate: TopicAggregator
}

type TopicAggregator {
  count: Int
  totalCount: Int
  sum: TopicAggregatorSum
  avg: TopicAggregatorAvg
  min: TopicAggregatorMin
  max: TopicAggregatorMax
}

type TopicAggregatorSum {
  postCount: Float
}

type TopicAggregatorAvg {
  postCount: Float
}

type TopicAggregatorMin {
  postCount: Float
}

type TopicAggregatorMax {
  postCount: Float
}

type TopicGroupBy {
  id: [TopicConnectionId]
  created_at: [TopicConnectionCreated_at]
  updated_at: [TopicConnectionUpdated_at]
  name: [TopicConnectionName]
  slug: [TopicConnectionSlug]
  parent: [TopicConnectionParent]
  description: [TopicConnectionDescription]
  parentSlug: [TopicConnectionParentSlug]
  postCount: [TopicConnectionPostCount]
}

type TopicConnectionId {
  key: ID
  connection: TopicConnection
}

type TopicConnectionCreated_at {
  key: DateTime
  connection: TopicConnection
}

type TopicConnectionUpdated_at {
  key: DateTime
  connection: TopicConnection
}

type TopicConnectionName {
  key: String
  connection: TopicConnection
}

type TopicConnectionSlug {
  key: String
  connection: TopicConnection
}

type TopicConnectionParent {
  key: ID
  connection: TopicConnection
}

type TopicConnectionDescription {
  key: String
  connection: TopicConnection
}

type TopicConnectionParentSlug {
  key: String
  connection: TopicConnection
}

type TopicConnectionPostCount {
  key: Int
  connection: TopicConnection
}

input TopicInput {
  name: String!
  slug: String!
  parent: ID
  description: String
  parentSlug: String
  postCount: Int
  created_by: ID
  updated_by: ID
}

input editTopicInput {
  name: String
  slug: String
  parent: ID
  description: String
  parentSlug: String
  postCount: Int
  created_by: ID
  updated_by: ID
}

input createTopicInput {
  data: TopicInput
}

type createTopicPayload {
  topic: Topic
}

input updateTopicInput {
  where: InputID
  data: editTopicInput
}

type updateTopicPayload {
  topic: Topic
}

input deleteTopicInput {
  where: InputID
}

type deleteTopicPayload {
  topic: Topic
}

type Webinar {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  name: String!
  description: String!
  cover: UploadFile
  slug: String
  count: Int
}

type WebinarConnection {
  values: [Webinar]
  groupBy: WebinarGroupBy
  aggregate: WebinarAggregator
}

type WebinarAggregator {
  count: Int
  totalCount: Int
  sum: WebinarAggregatorSum
  avg: WebinarAggregatorAvg
  min: WebinarAggregatorMin
  max: WebinarAggregatorMax
}

type WebinarAggregatorSum {
  count: Float
}

type WebinarAggregatorAvg {
  count: Float
}

type WebinarAggregatorMin {
  count: Float
}

type WebinarAggregatorMax {
  count: Float
}

type WebinarGroupBy {
  id: [WebinarConnectionId]
  created_at: [WebinarConnectionCreated_at]
  updated_at: [WebinarConnectionUpdated_at]
  name: [WebinarConnectionName]
  description: [WebinarConnectionDescription]
  cover: [WebinarConnectionCover]
  slug: [WebinarConnectionSlug]
  count: [WebinarConnectionCount]
}

type WebinarConnectionId {
  key: ID
  connection: WebinarConnection
}

type WebinarConnectionCreated_at {
  key: DateTime
  connection: WebinarConnection
}

type WebinarConnectionUpdated_at {
  key: DateTime
  connection: WebinarConnection
}

type WebinarConnectionName {
  key: String
  connection: WebinarConnection
}

type WebinarConnectionDescription {
  key: String
  connection: WebinarConnection
}

type WebinarConnectionCover {
  key: ID
  connection: WebinarConnection
}

type WebinarConnectionSlug {
  key: String
  connection: WebinarConnection
}

type WebinarConnectionCount {
  key: Int
  connection: WebinarConnection
}

input WebinarInput {
  name: String!
  description: String!
  cover: ID
  slug: String
  count: Int
  created_by: ID
  updated_by: ID
}

input editWebinarInput {
  name: String
  description: String
  cover: ID
  slug: String
  count: Int
  created_by: ID
  updated_by: ID
}

input createWebinarInput {
  data: WebinarInput
}

type createWebinarPayload {
  webinar: Webinar
}

input updateWebinarInput {
  where: InputID
  data: editWebinarInput
}

type updateWebinarPayload {
  webinar: Webinar
}

input deleteWebinarInput {
  where: InputID
}

type deleteWebinarPayload {
  webinar: Webinar
}

type I18NLocale {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  name: String
  code: String
}

input LocaleInput {
  name: String
  code: String
  created_by: ID
  updated_by: ID
}

input editLocaleInput {
  name: String
  code: String
  created_by: ID
  updated_by: ID
}

type UploadFile {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  name: String!
  alternativeText: String
  caption: String
  width: Int
  height: Int
  formats: JSON
  hash: String!
  ext: String
  mime: String!
  size: Float!
  url: String!
  previewUrl: String
  provider: String!
  provider_metadata: JSON
  related(sort: String, limit: Int, start: Int, where: JSON): [Morph]
}

type UploadFileConnection {
  values: [UploadFile]
  groupBy: UploadFileGroupBy
  aggregate: UploadFileAggregator
}

type UploadFileAggregator {
  count: Int
  totalCount: Int
  sum: UploadFileAggregatorSum
  avg: UploadFileAggregatorAvg
  min: UploadFileAggregatorMin
  max: UploadFileAggregatorMax
}

type UploadFileAggregatorSum {
  width: Float
  height: Float
  size: Float
}

type UploadFileAggregatorAvg {
  width: Float
  height: Float
  size: Float
}

type UploadFileAggregatorMin {
  width: Float
  height: Float
  size: Float
}

type UploadFileAggregatorMax {
  width: Float
  height: Float
  size: Float
}

type UploadFileGroupBy {
  id: [UploadFileConnectionId]
  created_at: [UploadFileConnectionCreated_at]
  updated_at: [UploadFileConnectionUpdated_at]
  name: [UploadFileConnectionName]
  alternativeText: [UploadFileConnectionAlternativeText]
  caption: [UploadFileConnectionCaption]
  width: [UploadFileConnectionWidth]
  height: [UploadFileConnectionHeight]
  formats: [UploadFileConnectionFormats]
  hash: [UploadFileConnectionHash]
  ext: [UploadFileConnectionExt]
  mime: [UploadFileConnectionMime]
  size: [UploadFileConnectionSize]
  url: [UploadFileConnectionUrl]
  previewUrl: [UploadFileConnectionPreviewUrl]
  provider: [UploadFileConnectionProvider]
  provider_metadata: [UploadFileConnectionProvider_metadata]
}

type UploadFileConnectionId {
  key: ID
  connection: UploadFileConnection
}

type UploadFileConnectionCreated_at {
  key: DateTime
  connection: UploadFileConnection
}

type UploadFileConnectionUpdated_at {
  key: DateTime
  connection: UploadFileConnection
}

type UploadFileConnectionName {
  key: String
  connection: UploadFileConnection
}

type UploadFileConnectionAlternativeText {
  key: String
  connection: UploadFileConnection
}

type UploadFileConnectionCaption {
  key: String
  connection: UploadFileConnection
}

type UploadFileConnectionWidth {
  key: Int
  connection: UploadFileConnection
}

type UploadFileConnectionHeight {
  key: Int
  connection: UploadFileConnection
}

type UploadFileConnectionFormats {
  key: JSON
  connection: UploadFileConnection
}

type UploadFileConnectionHash {
  key: String
  connection: UploadFileConnection
}

type UploadFileConnectionExt {
  key: String
  connection: UploadFileConnection
}

type UploadFileConnectionMime {
  key: String
  connection: UploadFileConnection
}

type UploadFileConnectionSize {
  key: Float
  connection: UploadFileConnection
}

type UploadFileConnectionUrl {
  key: String
  connection: UploadFileConnection
}

type UploadFileConnectionPreviewUrl {
  key: String
  connection: UploadFileConnection
}

type UploadFileConnectionProvider {
  key: String
  connection: UploadFileConnection
}

type UploadFileConnectionProvider_metadata {
  key: JSON
  connection: UploadFileConnection
}

input FileInput {
  name: String!
  alternativeText: String
  caption: String
  width: Int
  height: Int
  formats: JSON
  hash: String!
  ext: String
  mime: String!
  size: Float!
  url: String!
  previewUrl: String
  provider: String!
  provider_metadata: JSON
  related: [ID]
  created_by: ID
  updated_by: ID
}

input editFileInput {
  name: String
  alternativeText: String
  caption: String
  width: Int
  height: Int
  formats: JSON
  hash: String
  ext: String
  mime: String
  size: Float
  url: String
  previewUrl: String
  provider: String
  provider_metadata: JSON
  related: [ID]
  created_by: ID
  updated_by: ID
}

input deleteFileInput {
  where: InputID
}

type deleteFilePayload {
  file: UploadFile
}

type UsersPermissionsPermission {
  id: ID!
  type: String!
  controller: String!
  action: String!
  enabled: Boolean!
  policy: String
  role: UsersPermissionsRole
}

type UsersPermissionsRole {
  id: ID!
  name: String!
  description: String
  type: String
  permissions(sort: String, limit: Int, start: Int, where: JSON): [UsersPermissionsPermission]
  users(sort: String, limit: Int, start: Int, where: JSON): [UsersPermissionsUser]
}

type UsersPermissionsRoleConnection {
  values: [UsersPermissionsRole]
  groupBy: UsersPermissionsRoleGroupBy
  aggregate: UsersPermissionsRoleAggregator
}

type UsersPermissionsRoleAggregator {
  count: Int
  totalCount: Int
}

type UsersPermissionsRoleGroupBy {
  id: [UsersPermissionsRoleConnectionId]
  name: [UsersPermissionsRoleConnectionName]
  description: [UsersPermissionsRoleConnectionDescription]
  type: [UsersPermissionsRoleConnectionType]
}

type UsersPermissionsRoleConnectionId {
  key: ID
  connection: UsersPermissionsRoleConnection
}

type UsersPermissionsRoleConnectionName {
  key: String
  connection: UsersPermissionsRoleConnection
}

type UsersPermissionsRoleConnectionDescription {
  key: String
  connection: UsersPermissionsRoleConnection
}

type UsersPermissionsRoleConnectionType {
  key: String
  connection: UsersPermissionsRoleConnection
}

input RoleInput {
  name: String!
  description: String
  type: String
  permissions: [ID]
  users: [ID]
  created_by: ID
  updated_by: ID
}

input editRoleInput {
  name: String
  description: String
  type: String
  permissions: [ID]
  users: [ID]
  created_by: ID
  updated_by: ID
}

input createRoleInput {
  data: RoleInput
}

type createRolePayload {
  role: UsersPermissionsRole
}

input updateRoleInput {
  where: InputID
  data: editRoleInput
}

type updateRolePayload {
  role: UsersPermissionsRole
}

input deleteRoleInput {
  where: InputID
}

type deleteRolePayload {
  role: UsersPermissionsRole
}

type UsersPermissionsUser {
  id: ID!
  created_at: DateTime!
  updated_at: DateTime!
  username: String!
  email: String!
  provider: String
  confirmed: Boolean
  blocked: Boolean
  role: UsersPermissionsRole
}

type UsersPermissionsUserConnection {
  values: [UsersPermissionsUser]
  groupBy: UsersPermissionsUserGroupBy
  aggregate: UsersPermissionsUserAggregator
}

type UsersPermissionsUserAggregator {
  count: Int
  totalCount: Int
}

type UsersPermissionsUserGroupBy {
  id: [UsersPermissionsUserConnectionId]
  created_at: [UsersPermissionsUserConnectionCreated_at]
  updated_at: [UsersPermissionsUserConnectionUpdated_at]
  username: [UsersPermissionsUserConnectionUsername]
  email: [UsersPermissionsUserConnectionEmail]
  provider: [UsersPermissionsUserConnectionProvider]
  confirmed: [UsersPermissionsUserConnectionConfirmed]
  blocked: [UsersPermissionsUserConnectionBlocked]
  role: [UsersPermissionsUserConnectionRole]
}

type UsersPermissionsUserConnectionId {
  key: ID
  connection: UsersPermissionsUserConnection
}

type UsersPermissionsUserConnectionCreated_at {
  key: DateTime
  connection: UsersPermissionsUserConnection
}

type UsersPermissionsUserConnectionUpdated_at {
  key: DateTime
  connection: UsersPermissionsUserConnection
}

type UsersPermissionsUserConnectionUsername {
  key: String
  connection: UsersPermissionsUserConnection
}

type UsersPermissionsUserConnectionEmail {
  key: String
  connection: UsersPermissionsUserConnection
}

type UsersPermissionsUserConnectionProvider {
  key: String
  connection: UsersPermissionsUserConnection
}

type UsersPermissionsUserConnectionConfirmed {
  key: Boolean
  connection: UsersPermissionsUserConnection
}

type UsersPermissionsUserConnectionBlocked {
  key: Boolean
  connection: UsersPermissionsUserConnection
}

type UsersPermissionsUserConnectionRole {
  key: ID
  connection: UsersPermissionsUserConnection
}

input UserInput {
  username: String!
  email: String!
  provider: String
  password: String
  resetPasswordToken: String
  confirmationToken: String
  confirmed: Boolean
  blocked: Boolean
  role: ID
  created_by: ID
  updated_by: ID
}

input editUserInput {
  username: String
  email: String
  provider: String
  password: String
  resetPasswordToken: String
  confirmationToken: String
  confirmed: Boolean
  blocked: Boolean
  role: ID
  created_by: ID
  updated_by: ID
}

input createUserInput {
  data: UserInput
}

type createUserPayload {
  user: UsersPermissionsUser
}

input updateUserInput {
  where: InputID
  data: editUserInput
}

type updateUserPayload {
  user: UsersPermissionsUser
}

input deleteUserInput {
  where: InputID
}

type deleteUserPayload {
  user: UsersPermissionsUser
}

type ComponentAtomButton {
  id: ID!
  name: String
  url: String
  outline: Boolean
}

input ComponentAtomButtonInput {
  name: String
  url: String
  outline: Boolean
}

input editComponentAtomButtonInput {
  id: ID
  name: String
  url: String
  outline: Boolean
}

type ComponentAtomColor {
  id: ID!
  foreground: String
  background: String
}

input ComponentAtomColorInput {
  foreground: String
  background: String
}

input editComponentAtomColorInput {
  id: ID
  foreground: String
  background: String
}

enum ENUM_COMPONENTATOMPLATFORM_NAME {
  rss
  spotify
  soundcloud
  apple
  google
  youtube
}

type ComponentAtomPlatform {
  id: ID!
  url: String
  name: ENUM_COMPONENTATOMPLATFORM_NAME
}

input ComponentAtomPlatformInput {
  url: String
  name: ENUM_COMPONENTATOMPLATFORM_NAME
}

input editComponentAtomPlatformInput {
  id: ID
  url: String
  name: ENUM_COMPONENTATOMPLATFORM_NAME
}

type ComponentBlockText {
  id: ID!
  content: String
}

input ComponentBlockTextInput {
  content: String
}

input editComponentBlockTextInput {
  id: ID
  content: String
}

type ComponentModuleEmailJourney {
  id: ID!
  embedForm: String
}

input ComponentModuleEmailJourneyInput {
  embedForm: String
}

input editComponentModuleEmailJourneyInput {
  id: ID
  embedForm: String
}

type ComponentModuleEvent {
  id: ID!
  date: DateTime!
  url: String
}

input ComponentModuleEventInput {
  date: DateTime!
  url: String
}

input editComponentModuleEventInput {
  id: ID
  date: DateTime
  url: String
}

type ComponentModuleFeatured {
  id: ID!
  title: String!
  description: String
  image: UploadFile
  inColumn: Boolean
  cta: ComponentAtomButton
  color: ComponentAtomColor
  type: String
  cta2: ComponentAtomButton
  postRef: Post
}

input ComponentModuleFeaturedInput {
  title: String!
  description: String
  image: ID
  inColumn: Boolean
  cta: ComponentAtomButtonInput
  color: ComponentAtomColorInput
  type: String
  cta2: ComponentAtomButtonInput
  postRef: ID
}

input editComponentModuleFeaturedInput {
  id: ID
  title: String
  description: String
  image: ID
  inColumn: Boolean
  cta: editComponentAtomButtonInput
  color: editComponentAtomColorInput
  type: String
  cta2: editComponentAtomButtonInput
  postRef: ID
}

type ComponentModuleFormation {
  id: ID!
  link: String
  youtubeEmbed: String
  speakers(sort: String, limit: Int, start: Int, where: JSON): [Author]
}

input ComponentModuleFormationInput {
  speakers: [ID]
  link: String
  youtubeEmbed: String
}

input editComponentModuleFormationInput {
  id: ID
  speakers: [ID]
  link: String
  youtubeEmbed: String
}

type ComponentModuleLead {
  id: ID!
  content: String!
}

input ComponentModuleLeadInput {
  content: String!
}

input editComponentModuleLeadInput {
  id: ID
  content: String
}

type ComponentModulePodcast {
  id: ID!
  embedVideo: String
  embedAudio: String
  podcast: Podcast
}

input ComponentModulePodcastInput {
  embedVideo: String
  embedAudio: String
  podcast: ID
}

input editComponentModulePodcastInput {
  id: ID
  embedVideo: String
  embedAudio: String
  podcast: ID
}

type ComponentModuleSeo {
  id: ID!
  metaTitle: String
  metaDescription: String
  preventIndexing: Boolean
}

input ComponentModuleSeoInput {
  metaTitle: String
  metaDescription: String
  preventIndexing: Boolean
}

input editComponentModuleSeoInput {
  id: ID
  metaTitle: String
  metaDescription: String
  preventIndexing: Boolean
}

type ComponentModuleWebinar {
  id: ID!
  embedVideo: String
  webinar: Webinar
  speakers(sort: String, limit: Int, start: Int, where: JSON): [Author]
}

input ComponentModuleWebinarInput {
  embedVideo: String
  webinar: ID
  speakers: [ID]
}

input editComponentModuleWebinarInput {
  id: ID
  embedVideo: String
  webinar: ID
  speakers: [ID]
}

enum ENUM_COMPONENTNAVIGATIONNAVURL_TYPE {
  external
  internal
  filter
}

type ComponentNavigationNavUrl {
  id: ID!
  label: String
  value: String
  type: ENUM_COMPONENTNAVIGATIONNAVURL_TYPE
}

input ComponentNavigationNavUrlInput {
  label: String
  value: String
  type: ENUM_COMPONENTNAVIGATIONNAVURL_TYPE
}

input editComponentNavigationNavUrlInput {
  id: ID
  label: String
  value: String
  type: ENUM_COMPONENTNAVIGATIONNAVURL_TYPE
}

type ComponentSectionMission {
  id: ID!
  partOne: String
  partTwo: String
}

input ComponentSectionMissionInput {
  partOne: String
  partTwo: String
}

input editComponentSectionMissionInput {
  id: ID
  partOne: String
  partTwo: String
}

type ComponentSectionPostSet {
  id: ID!
  name: String
  posts(sort: String, limit: Int, start: Int, where: JSON): [Post]
}

input ComponentSectionPostSetInput {
  name: String
  posts: [ID]
}

input editComponentSectionPostSetInput {
  id: ID
  name: String
  posts: [ID]
}

type ComponentSectionQuote {
  id: ID!
  author: String!
  text: String
}

input ComponentSectionQuoteInput {
  author: String!
  text: String
}

input editComponentSectionQuoteInput {
  id: ID
  author: String
  text: String
}

type ComponentSectionShop {
  id: ID!
  name: String
  text: String
  image: UploadFile
}

input ComponentSectionShopInput {
  name: String
  text: String
  image: ID
}

input editComponentSectionShopInput {
  id: ID
  name: String
  text: String
  image: ID
}

union Morph = UsersPermissionsMe | UsersPermissionsMeRole | UsersPermissionsLoginPayload | UserPermissionsPasswordPayload | Related | Author | AuthorConnection | AuthorAggregator | AuthorGroupBy | AuthorConnectionId | AuthorConnectionCreated_at | AuthorConnectionUpdated_at | AuthorConnectionFirstName | AuthorConnectionLastName | AuthorConnectionFullName | AuthorConnectionAbout | AuthorConnectionPicture | AuthorConnectionSlug | createAuthorPayload | updateAuthorPayload | deleteAuthorPayload | Blog | BlogConnection | BlogAggregator | BlogGroupBy | BlogConnectionId | BlogConnectionCreated_at | BlogConnectionUpdated_at | BlogConnectionBlogger | BlogConnectionSlug | BlogConnectionNewsletter | BlogConnectionAuthor_user | createBlogPayload | updateBlogPayload | deleteBlogPayload | CronPost | CronPostConnection | CronPostAggregator | CronPostGroupBy | CronPostConnectionId | CronPostConnectionCreated_at | CronPostConnectionUpdated_at | CronPostConnectionPostTitle | CronPostConnectionPostId | CronPostConnectionTrigger | createCronPostPayload | updateCronPostPayload | deleteCronPostPayload | Formations | updateFormationPayload | deleteFormationPayload | Home | updateHomePayload | deleteHomePayload | OldTopics | OldTopicsConnection | OldTopicsAggregator | OldTopicsGroupBy | OldTopicsConnectionId | OldTopicsConnectionCreated_at | OldTopicsConnectionUpdated_at | OldTopicsConnectionName | OldTopicsConnectionSlug | OldTopicsConnectionTopic | createOldTopicPayload | updateOldTopicPayload | deleteOldTopicPayload | Page | PageConnection | PageAggregator | PageGroupBy | PageConnectionId | PageConnectionCreated_at | PageConnectionUpdated_at | PageConnectionTitle | PageConnectionSlug | PageConnectionCover | PageConnectionMetas | PageConnectionPublished_at | createPagePayload | updatePagePayload | deletePagePayload | ParcoursEmails | updateParcoursEmailPayload | deleteParcoursEmailPayload | Podcast | PodcastConnection | PodcastAggregator | PodcastAggregatorSum | PodcastAggregatorAvg | PodcastAggregatorMin | PodcastAggregatorMax | PodcastGroupBy | PodcastConnectionId | PodcastConnectionCreated_at | PodcastConnectionUpdated_at | PodcastConnectionName | PodcastConnectionSlug | PodcastConnectionDescription | PodcastConnectionCover | PodcastConnectionCount | PodcastConnectionAverageDuration | PodcastConnectionLogoSmall | createPodcastPayload | updatePodcastPayload | deletePodcastPayload | Popup | PopupConnection | PopupAggregator | PopupGroupBy | PopupConnectionId | PopupConnectionCreated_at | PopupConnectionUpdated_at | PopupConnectionTitle | PopupConnectionBody | PopupConnectionImage | PopupConnectionEndDate | PopupConnectionStartDate | PopupConnectionButton | PopupConnectionPublished_at | createPopupPayload | updatePopupPayload | deletePopupPayload | Post | PostConnection | PostAggregator | PostAggregatorSum | PostAggregatorAvg | PostAggregatorMin | PostAggregatorMax | PostGroupBy | PostConnectionId | PostConnectionCreated_at | PostConnectionUpdated_at | PostConnectionTitle | PostConnectionSlug | PostConnectionBody | PostConnectionImage | PostConnectionSerie | PostConnectionAuthor | PostConnectionWp_created_at | PostConnectionBlog | PostConnectionIsCornerStone | PostConnectionType | PostConnectionPublish_at | PostConnectionWp_published_at | PostConnectionReadingTime | PostConnectionPublished_at | createPostPayload | updatePostPayload | deletePostPayload | Serie | SerieConnection | SerieAggregator | SerieGroupBy | SerieConnectionId | SerieConnectionCreated_at | SerieConnectionUpdated_at | SerieConnectionName | SerieConnectionSlug | SerieConnectionDescription | createSeriePayload | updateSeriePayload | deleteSeriePayload | Tag | TagConnection | TagAggregator | TagGroupBy | TagConnectionId | TagConnectionCreated_at | TagConnectionUpdated_at | TagConnectionName | TagConnectionSlug | createTagPayload | updateTagPayload | deleteTagPayload | TopicGroup | TopicGroupConnection | TopicGroupAggregator | TopicGroupGroupBy | TopicGroupConnectionId | TopicGroupConnectionCreated_at | TopicGroupConnectionUpdated_at | TopicGroupConnectionName | TopicGroupConnectionSlug | TopicGroupConnectionDescription | TopicGroupConnectionCover | TopicGroupConnectionType | TopicGroupConnectionParent | createTopicGroupPayload | updateTopicGroupPayload | deleteTopicGroupPayload | Topic | TopicConnection | TopicAggregator | TopicAggregatorSum | TopicAggregatorAvg | TopicAggregatorMin | TopicAggregatorMax | TopicGroupBy | TopicConnectionId | TopicConnectionCreated_at | TopicConnectionUpdated_at | TopicConnectionName | TopicConnectionSlug | TopicConnectionParent | TopicConnectionDescription | TopicConnectionParentSlug | TopicConnectionPostCount | createTopicPayload | updateTopicPayload | deleteTopicPayload | Webinar | WebinarConnection | WebinarAggregator | WebinarAggregatorSum | WebinarAggregatorAvg | WebinarAggregatorMin | WebinarAggregatorMax | WebinarGroupBy | WebinarConnectionId | WebinarConnectionCreated_at | WebinarConnectionUpdated_at | WebinarConnectionName | WebinarConnectionDescription | WebinarConnectionCover | WebinarConnectionSlug | WebinarConnectionCount | createWebinarPayload | updateWebinarPayload | deleteWebinarPayload | I18NLocale | UploadFile | UploadFileConnection | UploadFileAggregator | UploadFileAggregatorSum | UploadFileAggregatorAvg | UploadFileAggregatorMin | UploadFileAggregatorMax | UploadFileGroupBy | UploadFileConnectionId | UploadFileConnectionCreated_at | UploadFileConnectionUpdated_at | UploadFileConnectionName | UploadFileConnectionAlternativeText | UploadFileConnectionCaption | UploadFileConnectionWidth | UploadFileConnectionHeight | UploadFileConnectionFormats | UploadFileConnectionHash | UploadFileConnectionExt | UploadFileConnectionMime | UploadFileConnectionSize | UploadFileConnectionUrl | UploadFileConnectionPreviewUrl | UploadFileConnectionProvider | UploadFileConnectionProvider_metadata | deleteFilePayload | UsersPermissionsPermission | UsersPermissionsRole | UsersPermissionsRoleConnection | UsersPermissionsRoleAggregator | UsersPermissionsRoleGroupBy | UsersPermissionsRoleConnectionId | UsersPermissionsRoleConnectionName | UsersPermissionsRoleConnectionDescription | UsersPermissionsRoleConnectionType | createRolePayload | updateRolePayload | deleteRolePayload | UsersPermissionsUser | UsersPermissionsUserConnection | UsersPermissionsUserAggregator | UsersPermissionsUserGroupBy | UsersPermissionsUserConnectionId | UsersPermissionsUserConnectionCreated_at | UsersPermissionsUserConnectionUpdated_at | UsersPermissionsUserConnectionUsername | UsersPermissionsUserConnectionEmail | UsersPermissionsUserConnectionProvider | UsersPermissionsUserConnectionConfirmed | UsersPermissionsUserConnectionBlocked | UsersPermissionsUserConnectionRole | createUserPayload | updateUserPayload | deleteUserPayload | ComponentAtomButton | ComponentAtomColor | ComponentAtomPlatform | ComponentBlockText | ComponentModuleEmailJourney | ComponentModuleEvent | ComponentModuleFeatured | ComponentModuleFormation | ComponentModuleLead | ComponentModulePodcast | ComponentModuleSeo | ComponentModuleWebinar | ComponentNavigationNavUrl | ComponentSectionMission | ComponentSectionPostSet | ComponentSectionQuote | ComponentSectionShop

input InputID {
  id: ID!
}

enum PublicationState {
  LIVE
  PREVIEW
}

type AdminUser {
  id: ID!
  username: String
  firstname: String!
  lastname: String!
}

type Query {
  author(id: ID!, publicationState: PublicationState): Author
  authors(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Author]
  authorsConnection(sort: String, limit: Int, start: Int, where: JSON): AuthorConnection
  blog(id: ID!, publicationState: PublicationState): Blog
  blogs(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Blog]
  blogsConnection(sort: String, limit: Int, start: Int, where: JSON): BlogConnection
  cronPost(id: ID!, publicationState: PublicationState): CronPost
  cronPosts(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [CronPost]
  cronPostsConnection(sort: String, limit: Int, start: Int, where: JSON): CronPostConnection
  formation(publicationState: PublicationState): Formations
  home(publicationState: PublicationState): Home
  oldTopic(id: ID!, publicationState: PublicationState): OldTopics
  oldTopics(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [OldTopics]
  oldTopicsConnection(sort: String, limit: Int, start: Int, where: JSON): OldTopicsConnection
  page(id: ID!, publicationState: PublicationState): Page
  pages(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Page]
  pagesConnection(sort: String, limit: Int, start: Int, where: JSON): PageConnection
  parcoursEmail(publicationState: PublicationState): ParcoursEmails
  podcast(id: ID!, publicationState: PublicationState): Podcast
  podcasts(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Podcast]
  podcastsConnection(sort: String, limit: Int, start: Int, where: JSON): PodcastConnection
  popup(id: ID!, publicationState: PublicationState): Popup
  popups(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Popup]
  popupsConnection(sort: String, limit: Int, start: Int, where: JSON): PopupConnection
  post(id: ID!, publicationState: PublicationState): Post
  posts(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Post]
  postsConnection(sort: String, limit: Int, start: Int, where: JSON): PostConnection
  serie(id: ID!, publicationState: PublicationState): Serie
  series(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Serie]
  seriesConnection(sort: String, limit: Int, start: Int, where: JSON): SerieConnection
  tag(id: ID!, publicationState: PublicationState): Tag
  tags(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Tag]
  tagsConnection(sort: String, limit: Int, start: Int, where: JSON): TagConnection
  topicGroup(id: ID!, publicationState: PublicationState): TopicGroup
  topicGroups(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [TopicGroup]
  topicGroupsConnection(sort: String, limit: Int, start: Int, where: JSON): TopicGroupConnection
  topic(id: ID!, publicationState: PublicationState): Topic
  topics(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Topic]
  topicsConnection(sort: String, limit: Int, start: Int, where: JSON): TopicConnection
  webinar(id: ID!, publicationState: PublicationState): Webinar
  webinars(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [Webinar]
  webinarsConnection(sort: String, limit: Int, start: Int, where: JSON): WebinarConnection
  files(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [UploadFile]
  filesConnection(sort: String, limit: Int, start: Int, where: JSON): UploadFileConnection
  role(id: ID!, publicationState: PublicationState): UsersPermissionsRole

  """
  Retrieve all the existing roles. You can't apply filters on this query.
  """
  roles(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [UsersPermissionsRole]
  rolesConnection(sort: String, limit: Int, start: Int, where: JSON): UsersPermissionsRoleConnection
  user(id: ID!, publicationState: PublicationState): UsersPermissionsUser
  users(sort: String, limit: Int, start: Int, where: JSON, publicationState: PublicationState): [UsersPermissionsUser]
  usersConnection(sort: String, limit: Int, start: Int, where: JSON): UsersPermissionsUserConnection
  me: UsersPermissionsMe
  relatedPosts(id: ID, limit: Int): [Related]!
  topicChildren(id: ID, limit: Int): [Topic]!
}

type Mutation {
  createAuthor(input: createAuthorInput): createAuthorPayload
  updateAuthor(input: updateAuthorInput): updateAuthorPayload
  deleteAuthor(input: deleteAuthorInput): deleteAuthorPayload
  createBlog(input: createBlogInput): createBlogPayload
  updateBlog(input: updateBlogInput): updateBlogPayload
  deleteBlog(input: deleteBlogInput): deleteBlogPayload
  createCronPost(input: createCronPostInput): createCronPostPayload
  updateCronPost(input: updateCronPostInput): updateCronPostPayload
  deleteCronPost(input: deleteCronPostInput): deleteCronPostPayload
  updateFormation(input: updateFormationInput): updateFormationPayload
  deleteFormation: deleteFormationPayload
  updateHome(input: updateHomeInput): updateHomePayload
  deleteHome: deleteHomePayload
  createOldTopic(input: createOldTopicInput): createOldTopicPayload
  updateOldTopic(input: updateOldTopicInput): updateOldTopicPayload
  deleteOldTopic(input: deleteOldTopicInput): deleteOldTopicPayload
  createPage(input: createPageInput): createPagePayload
  updatePage(input: updatePageInput): updatePagePayload
  deletePage(input: deletePageInput): deletePagePayload
  updateParcoursEmail(input: updateParcoursEmailInput): updateParcoursEmailPayload
  deleteParcoursEmail: deleteParcoursEmailPayload
  createPodcast(input: createPodcastInput): createPodcastPayload
  updatePodcast(input: updatePodcastInput): updatePodcastPayload
  deletePodcast(input: deletePodcastInput): deletePodcastPayload
  createPopup(input: createPopupInput): createPopupPayload
  updatePopup(input: updatePopupInput): updatePopupPayload
  deletePopup(input: deletePopupInput): deletePopupPayload
  createPost(input: createPostInput): createPostPayload
  updatePost(input: updatePostInput): updatePostPayload
  deletePost(input: deletePostInput): deletePostPayload
  createSerie(input: createSerieInput): createSeriePayload
  updateSerie(input: updateSerieInput): updateSeriePayload
  deleteSerie(input: deleteSerieInput): deleteSeriePayload
  createTag(input: createTagInput): createTagPayload
  updateTag(input: updateTagInput): updateTagPayload
  deleteTag(input: deleteTagInput): deleteTagPayload
  createTopicGroup(input: createTopicGroupInput): createTopicGroupPayload
  updateTopicGroup(input: updateTopicGroupInput): updateTopicGroupPayload
  deleteTopicGroup(input: deleteTopicGroupInput): deleteTopicGroupPayload
  createTopic(input: createTopicInput): createTopicPayload
  updateTopic(input: updateTopicInput): updateTopicPayload
  deleteTopic(input: deleteTopicInput): deleteTopicPayload
  createWebinar(input: createWebinarInput): createWebinarPayload
  updateWebinar(input: updateWebinarInput): updateWebinarPayload
  deleteWebinar(input: deleteWebinarInput): deleteWebinarPayload

  """Delete one file"""
  deleteFile(input: deleteFileInput): deleteFilePayload

  """Create a new role"""
  createRole(input: createRoleInput): createRolePayload

  """Update an existing role"""
  updateRole(input: updateRoleInput): updateRolePayload

  """Delete an existing role"""
  deleteRole(input: deleteRoleInput): deleteRolePayload

  """Create a new user"""
  createUser(input: createUserInput): createUserPayload

  """Update an existing user"""
  updateUser(input: updateUserInput): updateUserPayload

  """Delete an existing user"""
  deleteUser(input: deleteUserInput): deleteUserPayload
  upload(refId: ID, ref: String, field: String, source: String, info: FileInfoInput, file: Upload!): UploadFile!
  multipleUpload(refId: ID, ref: String, field: String, source: String, files: [Upload]!): [UploadFile]!
  updateFileInfo(id: ID!, info: FileInfoInput!): UploadFile!
  login(input: UsersPermissionsLoginInput!): UsersPermissionsLoginPayload!
  register(input: UsersPermissionsRegisterInput!): UsersPermissionsLoginPayload!
  forgotPassword(email: String!): UserPermissionsPasswordPayload
  resetPassword(password: String!, passwordConfirmation: String!, code: String!): UsersPermissionsLoginPayload
  emailConfirmation(confirmation: String!): UsersPermissionsLoginPayload
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar DateTime

"""A time string with format: HH:mm:ss.SSS"""
scalar Time

"""
A date string, such as 2007-12-03, compliant with the `full-date` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar Date

"""The `Long` scalar type represents 52-bit integers"""
scalar Long

"""The `Upload` scalar type represents a file upload."""
scalar Upload
