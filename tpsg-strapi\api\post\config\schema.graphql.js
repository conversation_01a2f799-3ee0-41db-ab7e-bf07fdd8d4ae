module.exports = {
    query: `relatedPosts(id: ID, limit: Int): [Related]!`,
    definition: `
        type Related {
            post: Post!,
            score: Int!,
            section: String!,
            origin: String!
        }
    `,
    resolver: {
        Query: {
            relatedPosts: {
                description: 'retrieve related posts',
                resolver: `application::post.post.relatedPosts`
            }
        },
    },
};