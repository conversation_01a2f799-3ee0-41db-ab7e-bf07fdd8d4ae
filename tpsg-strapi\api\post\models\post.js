"use strict";
const TurndownService = require('turndown');
// const { sanitizeEntity } = require('strapi-utils');
const {initPostCount} = require("../../topic/controllers/topic")

module.exports = {
  lifecycles: {
    beforeCreate: async (data) => {

      console.log("RESQUEST DATA:", data)
      // Validating Podcast Data

      if(data.modules && data.modules.length > 0) {
        for(const module of data.modules) {
          if(module.__component === "module.podcast"){
            if(!module.podcast){
              
              throw strapi.errors.badRequest('Validation error. When creating a post with the podcast module, the podcast field is obligatory')
            }
          }
        }
      }

      /**
       * Si la création provient de l'export wordpress,
       * le texte de l'article est converti en markdown
       */
      if(data.import) {
        let turndownService = new TurndownService({
          headingStyle: 'atx',
          hr: '---',
          bulletListMarker: '-',
        });
        turndownService.keep(['iframe', 'sup', 'sub', 'script', 'quote', 'cite', 'form']);
        data.body = turndownService.turndown(data.body);

      } else {
        const { type, tags } = await strapi.services["post"].getMetas(data);
        data.type = type;
        data.tags = tags;
      }
      /**
       * Vérifie si le créateur de la ressource est un blogueur.
       * Si oui, on ajoute le blog du blogueur dans le post.
       */
      let blog = await strapi.query("blog")
        .findOne({ author_user: data.created_by });

      console.log("blog: ", blog);

      if(blog) {
        data.blog = blog.id;
      }
    },

    afterCreate: async (result, data) => {
      /**
       *  Modification de la date de création du post pour la faire correspondre
       *  à celle de wordpress
       *  TODO: code à supprimer une fois le site en production
       */
      if(result.wp_created_at && result.wp_published_at){
        const knex = strapi.connections.default;
        const query = await knex('posts').update({
          created_at: new Date(Date.parse(result.wp_created_at)),
          published_at: new Date(Date.parse(result.wp_published_at)),
        }).where('id', result.id);
      }
      /**
       * Ajoute le post dans la table des tâches crons.
       * TODO: Activer lors la mise en production finale.
       */
      if(!data?.noTrigger && !data?.import) {
        await strapi.services['cron-post'].create({
          postId: result.id,
          postTitle: result.title,
          trigger: 'create'
        })
      }

      /**
       * Si un blog a été défini, on force le créateur du post à être l'utilisateur associé au blog.
       */
      if(result.blog && result.blog?.author_user) {
        const knex = strapi.connections.default;
        await knex('posts').update({
          created_by: result.blog.author_user
        }).where('id', result.id);
      }

      /**
       * TODO: Code à activer après la mise en production finale.
       */
      // update topics postCount attribute
      // result.topics.forEach(topic => {
      //   strapi.services['topic'].updatePostCount(topic)
      // })
    },

    beforeUpdate: async (params, data) => {

    

      /**
       * Vérifie si la mise à jour est déclenchée par un click sur le bouton de
       * publication. Danc ce cas, on ne modifie pas le type et les tags de l'article
       * étant donné que les données récupérées ne contiennent pas les modules attachés
       * au post.
       */
      const keys = Object.keys(data);

      if(keys.length === 1 && keys[0] === 'published_at' ) {
        console.log("should not change type");
        return;
      }

      /**
       * Si l'update provient de l'export wordpress,
       * le texte de l'article est converti en markdown
       */
      if(data.import) {
        let turndownService = new TurndownService({
          headingStyle: 'atx',
          hr: '---',
          bulletListMarker: '-',
        });
        turndownService.keep(['iframe', 'sup', 'sub', 'script', 'quote', 'cite', 'form']);
        data.body = turndownService.turndown(data.body);
      }
      /**
       * Si des modifications ne proviennent pas de l'export,
       * on vérifie le type de l'article et les tags suivant
       * les modules.
       */
      if(!data.noTrigger && !data.import){
        const { type, tags } = await strapi.services["post"].getMetas(data);
        data.type = type;
        data.tags = tags;
       

        // TODO: Remplacer false. Ne devrais se déclencher que si les
        //  topics ont changés.
        // if(false){
        //   data.topics.forEach(topic => {
        //     strapi.services['topic'].updatePostCount(topic)
        //   })
        // }
      }

    },

    afterUpdate: async (result, postId, data) => {
      await initPostCount()
      /**
       *  Mise à jour de la page d'accueil.
       */
      if(!data.import && data.published_at) {
        strapi.services['home'].updateSections();
      }

      /**
       * Si un blog a été défini, on force le créateur du post à être l'utilisateur associé au blog.
       */
      if(result.blog && result.blog?.author_user) {
        const knex = strapi.connections.default;
        await knex('posts').update({
          created_by: result.blog.author_user
        }).where('id', result.id);
      }

      /**
       * Ajoute le post dans la table des tâches cron pour être mis à jour par meilisearch.
       */
      await strapi.services['cron-post'].create({
        postId: result.id,
        postTitle: result.title,
        trigger: 'update'
      })
    },

    afterDelete: async (data) => {
      await initPostCount()
      if(data.topics) {
        data.topics.forEach(topic => {
          strapi.services['topic'].updatePostCount(topic)
        })
      }
      /**
       * Ajoute le/les post dans la table des tâches cron.
       * (On s'assure d'avoir un tableau de post à supprimer
       * même si on ne reçoit qu'un seul post).
       */
      const postArray = Array.isArray(data) ? data : [data];

      for(let index in postArray ) {
        await strapi.services['cron-post'].create({
          postId: postArray[index].id,
          postTitle: postArray[index].title,
          trigger: 'delete'
        })
      }
    }
  },

  // TODO: On ne devrait plus avoir besoin de cette partie étant donné que l'on utilise
  //  plus le plugin Meilisearch. Voir pour le supprimer.
  meilisearch: {
    transformEntry({entry, model}){

      let lead =  entry.modules.find(function(module) {
        return module.__component === "module.lead";
      });

      if( lead ) {
        lead = strapi.config.functions["utils"].removeMarkdown(lead.content);
        lead = strapi.config.functions["utils"].removeHtml(lead);
      }

      let route = '/';

      if(entry.type === 'podcast'){
        let podcastModule =  entry.modules.find(function(module) {
          return module.__component === "module.podcast";
        });
        route = `/podcasts/${podcastModule?.podcast?.slug}/${entry.slug}`;
      } else if(entry.type !== 'article'){
        route = `/${entry.type}s/${entry.slug}`;
      } else {
        route = `/article/${entry.slug}`;
      }

      let body = strapi.config.functions["utils"].removeMarkdown(entry.body);
      body = strapi.config.functions["utils"].removeHtml(body);

      const transformedEntry = {
        id: entry.id,
        title: entry.title,
        slug: entry.slug,
        author: entry.author?.fullName || null,
        tags: entry.tags.map((tag) => tag.name),
        topics: entry.topics.map((topic) => topic.name),
        type: entry.type || null,
        lead: lead || null,
        body: body || null,
        date: entry.created_at,
        blog: entry.blog?.slug || null,
        image: entry.image || null,
        route: route
      }
      return transformedEntry;
    },
  },
};
