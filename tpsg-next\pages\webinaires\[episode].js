import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import { queries } from "api/gql-queries";
import { modulesAsObj } from "/utils/components.utils";
import { WebinarLayout } from "../../components/layout/PagesLayout";

const BUILD_LIMIT = process.env.LIGHT_BUILD === "true" ? 10 : 9999;

export default function WebinarEpisode({ post, relatedPosts }) {
  return (
    <WebinarLayout episode={post} relatedPosts={relatedPosts} preview={false} />
  )
}


export async function getStaticProps({ params }) {

  let episode = await client.query({
    query: GET_WEBINAR_EPISODE,
    variables: { "episodeSlug": params.episode }
  }).then(response => {
    return response.data.posts[0]
  })

  if (!episode) {
    return { notFound: true };
  }

  const relatedPosts = await client.query({
    query: queries.QUERY_RELATED,
    variables: { id: episode.id }
  }).then(response => {
    return response.data.relatedPosts
  })


  episode = {
    ...episode,
    modules: modulesAsObj(episode.modules)
  };

  return {
    props: {
      post: episode,
      relatedPosts: relatedPosts || undefined
    },
    revalidate: 10
  }
}

export async function getStaticPaths() {

  const episodes = await client.query({
    query: QUERY_WEBINAR_SLUGS,
    variables: { limit: BUILD_LIMIT },
  }).then(response => {
    return response.data.posts
  })

  return {
    paths: episodes.map(episode => ({
      params: { episode: episode.slug }
    })),
    fallback: true,
  }
}

const QUERY_WEBINAR_SLUGS = gql`
    query WebinarSlugs($limit: Int!){
        posts(where: {type: "webinar"}, limit: $limit){
            slug
        }
    }
`

// noinspection GraphQLUnresolvedReference
const GET_WEBINAR_EPISODE = gql`
    query WebinarEpisode($episodeSlug: String!){
        posts(where: {slug: $episodeSlug}){
            title
            slug
            type
            published_at
            body
            id
            image {
                url
                provider
            }
            topics {
                name
                slug
            }
            modules {
                ... on ComponentModuleWebinar{
                    __typename
                    webinar {
                        slug,
                    }
                    speakers {
                        fullName
                        slug
                        picture {
                            url
                            provider
                        }
                    }
                    embedVideo
                }
                ... on ComponentModuleLead{
                    __typename
                    content
                }
                ... on ComponentModuleEvent{
                    __typename
                    date
                    url
                }
              ... on ComponentModuleSeo {
                __typename
                metaDescription
                metaTitle
              }
            }
        }
    }
`

