import styled from "styled-components";
import { Fragment } from "react";
import Link from "next/link";


function RowTopic({ topic }) {
  return (
    <li>
      <Link href={`/categories/${topic.slug}`}>
        {topic.name} ({topic.postCount})
      </Link>
    </li>
  )
}

export default function ChildrenList({ topics }) {
  return (
    <ul>
      {
        topics.map((N1Topic) => {
          return (
            <Fragment key={N1Topic.slug}>
              <RowTopic topic={N1Topic}/>
              {
                N1Topic.children &&
                <ul>
                  {
                    N1Topic.children.map((N2Topic) => {
                      return (
                        <Fragment key={N2Topic.slug}>
                          <RowTopic topic={N2Topic}/>
                          {
                            N2Topic.children &&
                            <ul>
                              {
                                N2Topic.children.map((N3Topic) => {
                                  return ( <RowTopic key={N3Topic.slug} topic={N3Topic}/> )
                                })
                              }
                            </ul>
                          }
                        </Fragment>
                      )
                    })
                  }
                </ul>
              }
            </Fragment>
          )
        })
      }
    </ul>
  )
}
