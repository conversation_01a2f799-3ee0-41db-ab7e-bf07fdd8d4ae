"use strict";(()=>{var e={};e.id=1583,e.ids=[1583,2888],e.modules={4138:(e,t,a)=>{a.r(t),a.d(t,{config:()=>X,default:()=>G,getServerSideProps:()=>O,getStaticPaths:()=>F,getStaticProps:()=>H,reportWebVitals:()=>B,routeModule:()=>et,unstable_getServerProps:()=>Y,unstable_getServerSideProps:()=>ee,unstable_getStaticParams:()=>Q,unstable_getStaticPaths:()=>K,unstable_getStaticProps:()=>J});var s={};a.r(s),a.d(s,{default:()=>Podcast,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var i=a(7093),o=a(5244),r=a(1323),n=a(779),l=a(4033),p=a(997),d=a(9114),c=a(1385),g=a(7518),m=a.n(g),u=a(7467),x=a(723),h=a(1163),f=a(6068),b=a(2705);let P=require("swr");var v=a.n(P),w=a(2558),j=a(6689),y=a(2924),S=a(2715),q=a(3462);let PodcastDescriptionData=({content:e})=>(0,p.jsxs)(_,{children:[(0,p.jsxs)($,{children:[(0,p.jsxs)("div",{className:"post-info",children:[p.jsx("p",{className:"post-secondary-text",children:"\xc9pisodes"}),p.jsx("p",{className:"post-info-bold",children:e.nbHits})]}),(0,p.jsxs)("div",{className:"post-info",children:[p.jsx("p",{className:"post-secondary-text",children:"Dur\xe9e Moy."}),p.jsx("p",{className:"post-info-bold",children:e.post.averageDuration||"inconnu"})]})]}),p.jsx("p",{className:"post-description",children:e.post.description}),p.jsx("div",{className:"post-platform",children:e.post.platforms&&e.post.platforms.map((e,t)=>p.jsx("a",{href:e.url,target:"_blank",rel:"noreferrer",children:p.jsx(q.Z,{plateform:e.name})},t))})]}),_=m().div.withConfig({componentId:"sc-46655392-0"})`
  .post-description {
    width: 100%;
    font-size: 16px;
    color: #161616;
  }

  .post-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 24px;
    width: 100%;
    gap: 17px;
  }

  @media ${u.U.tablet} {
    .post-platform {
      width: 75%;
      margin-top: 38px;
    }

    .post-description {
      font-size: 20px;
      width: 75%;
    }
  }
`,$=m().div.withConfig({componentId:"sc-46655392-1"})`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 25px;

  .post-info {
    margin-right: 55px;

    p {
      margin: 0;
    }

    .post-info-bold {
      font-size: 20px;
      font-weight: bold;
    }
  }

  @media ${u.U.tablet} {
    .post-info {
      .post-info-bold {
        font-size: 32px;
      }
    }
  }
`;var k=a(3229),N=a(7178),I=a(1664),C=a.n(I),z=a(6641),D=a(4130);function fetcher(e){return x.V.search("",{...(0,b.Rj)(e,15,"podcast"),sort:["date:desc"]})}function setInitialPage(e,t){let a=1;return e>t?.totalPages?a=t?.totalPages:e>1&&(a=e),a}function Podcast({podcast:e,fallback:t}){let a;let{blogs:s}=(0,f.o)(),i=t?.episodes?.totalHits||0,o=(0,h.useRouter)(),{query:r}=o,[n,l]=(0,j.useState)({totalItem:i,maxPage:t?.episodes?.totalPages||1,activePage:setInitialPage(+r.page,t?.episodes),previousPage:null,activeChannel:"tous",postPerPage:15,loading:!1}),[d,c]=(0,j.useState)(n.activePage),g=v()({...{page:n.activePage},tag:`channel.${e.slug}`},fetcher)?.data?.hits,[m,u]=(0,j.useState)({transitioning:"init",direction:null});return(0,j.useEffect)(()=>{if(1>+r?.page||+r?.page>n.maxPage){let e=1>+r?.page?1:n.maxPage;o.replace({pathname:o.pathname,query:{...r,page:e}},void 0,{scroll:!1})}else(!+r?.page||!1===n.loading&&+r?.page>1)&&(l(e=>({...e,totalItem:i,maxPage:t?.episodes?.totalPages||1,activePage:setInitialPage(+r.page,t?.episodes),previousPage:null,activeChannel:"tous",postPerPage:15,loading:!1})),c(setInitialPage(+r.page,t?.episodes)))},[r]),(0,p.jsxs)(P.SWRConfig,{value:{fallback:t},children:[p.jsx(z.NextSeo,{title:`Podcast ${e.name}`,description:e?.description,openGraph:{title:`Podcast ${e.name}`,description:e?.description,url:`https://toutpoursagloire.com/podcast/${e.slug}`,images:[{url:(0,D.k)(e.cover),alt:""}]},twitter:{site:"@t_p_s_g",cardType:"summary_large_image"}}),(0,p.jsxs)(U,{className:"site-padding",children:[(0,p.jsxs)(A,{children:[(0,p.jsxs)(T,{children:[p.jsx(C(),{href:"/podcasts",children:p.jsx("p",{className:"podcast-category",children:"PODCAST \xa0/"})}),p.jsx("h1",{className:"podcast-title",children:e.name}),p.jsx(Z,{children:e.speakers.length>3?"":e.speakers.map((t,a)=>{let i=!!s.find(e=>e.slug===t.slug),o=i?`/blog/${t.slug}`:`/recherche?author=${t.fullName}`,r=a>0?a===e.speakers.length-1?" et ":", ":e.speakers.length<3?"Par ":"";return(0,p.jsxs)(p.Fragment,{children:[r,p.jsx(C(),{href:o,children:p.jsx("span",{className:"speakers-name",children:t.fullName})},a)]})})})]}),p.jsx(R,{children:p.jsx(w.Z,{imageData:e.cover})})]}),p.jsx(L,{children:(0,p.jsxs)(k.b.Provider,{value:[n,l,e=>{clearTimeout(a),e>=1&&e<=n.maxPage&&"done"===m.transitioning&&(c(e),u(t=>({...t,transitioning:"init",direction:n.activePage-e})),l(e=>({...e,loading:!0})),a=setTimeout(()=>{l(t=>({...t,previousPage:t.activePage,activePage:e,loading:!0})),u(t=>({...t,transitioning:"fetched",direction:n.activePage-e}))},500))},d],children:[p.jsx(M,{children:p.jsx(S.Z,{url:r.podcast,title:"",children:p.jsx(PodcastDescriptionData,{content:{post:e,nbHits:i}})})}),p.jsx(V,{children:p.jsx(N.Z,{animationTransition:m,setAnimationTransition:u,children:p.jsx("ul",{className:"podcast-all-card",children:g?.map((t,a)=>p.jsx(y.Z,{post:t,options:{showLead:!0,showDate:!0,showAuthor:!0},link:`${e.slug}/${t.slug}`},a))})})})]})})]})]})}let U=m().div.withConfig({componentId:"sc-ed545b57-0"})`
  .podcast-secondary-text {
    font-size: 12px;
    opacity: 50%;
  }

  .mobile-hide {
    display: none;
  }

  @media ${u.U.tablet} {
    .mobile-hide {
      display: block;
    }

    .podcast-secondary-text {
      font-size: 17px;
    }
  }
`,Z=m().p.withConfig({componentId:"sc-ed545b57-1"})`
  font-size: 17px;
  font-family: Switzer, sans-serif;
  .speakers-by {
    font-weight: bold;
  }
  .speakers-name {
    font-weight: normal;
    text-decoration: underline;
    &:hover {
      color: var(--brand-color);
    }
  }
`,A=m().div.withConfig({componentId:"sc-ed545b57-2"})`
  display: flex;
  justify-content: space-between;
  margin-top: 50px;
  flex-wrap: wrap;
  flex-direction: column-reverse;

  @media ${u.U.tablet} {
    flex-direction: row;
    margin-bottom: 50px;
  }
`,T=m().div.withConfig({componentId:"sc-ed545b57-3"})`
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 24px;
  align-self: flex-start;
  width: 100%;


  .podcast-category {
    display: none;
    opacity: 50%;
    font-size: 20px;
    margin-bottom: 40px;
  }

  .podcast-title {
    margin: 0;
    font-weight: 500;
    font-size: clamp(32px, calc(12px + 9vw), 144px);
    line-height: 90%;
  }

  @media ${u.U.tablet} {
    // Little screen only
    width: 60%;
    margin-top: 0;
    align-self: flex-end;

  }
  @media ${u.U.tablet} {
    .podcast-category {
      display: block;
    }
  }
  @media ${u.U.desktop} {
    align-self: flex-start;
  }
`,R=m().div.withConfig({componentId:"sc-ed545b57-4"})`
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  aspect-ratio: 16/9;
  margin-bottom: 32px;

  @media ${u.U.tablet} {
    width: 30%;
    height: 100%;
    aspect-ratio: 1/1;
    margin-bottom: 0;
  }
`,L=m().div.withConfig({componentId:"sc-ed545b57-5"})`
  position: relative;
  @media ${u.U.tablet} {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    border-top: 1px solid #444444;
    border-bottom: none;
  }
`,M=m().div.withConfig({componentId:"sc-ed545b57-6"})`
  z-index: 1;
  position: relative;
  @media ${u.U.tablet} {
    width: 28%;
  }
`,V=m().div.withConfig({componentId:"sc-ed545b57-7"})`
  margin-top: 30px;
  margin-bottom: 100px;

  .podcast-all-card {
    padding: 0;
  }

  .show-episode {
    font-size: 24px;
    font-weight: bold;
  }

  @media ${u.U.tablet} {
    margin-bottom: 0;
    border-top: none;
    margin-top: 0;
    width: 72%;
    border-left: 1px solid #444444;
    display: flex;
    justify-content: flex-end;
    padding-top: 40px;

    .podcast-all-card {
      width: 92%;
    }

    .show-episode {
      display: none;
    }

    .animation-list {
      // AnimatedList css
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
    }
  }
`;async function getStaticProps({params:e}){let t=await c.Z.query({query:E,variables:{slug:e.podcast}}).then(e=>e.data.podcasts[0]),a=await fetcher({tag:`channel.${t.slug}`});return{props:{podcast:t,fallback:{episodes:a}},revalidate:10}}async function getStaticPaths(){let e=await c.Z.query({query:W}).then(e=>e.data.podcasts);return{paths:e.map(e=>({params:{podcast:e.slug,page:"1"}})),fallback:!1}}let W=d.gql`
  query {
    podcasts {
      slug
    }
  }
`,E=d.gql`
  query Podcast($slug: String) {
    podcasts(where: { slug: $slug }) {
      description
      name
      slug
      platforms {
        name
        url
      }
      speakers {
        fullName
        slug
      }
      cover {
        url
        provider
      }
      count
      averageDuration
    }
  }
`,G=(0,r.l)(s,"default"),H=(0,r.l)(s,"getStaticProps"),F=(0,r.l)(s,"getStaticPaths"),O=(0,r.l)(s,"getServerSideProps"),X=(0,r.l)(s,"config"),B=(0,r.l)(s,"reportWebVitals"),J=(0,r.l)(s,"unstable_getStaticProps"),K=(0,r.l)(s,"unstable_getStaticPaths"),Q=(0,r.l)(s,"unstable_getStaticParams"),Y=(0,r.l)(s,"unstable_getServerProps"),ee=(0,r.l)(s,"unstable_getServerSideProps"),et=new i.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/podcasts/[podcast]",pathname:"/podcasts/[podcast]",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:s})},9114:e=>{e.exports=require("@apollo/client")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},9997:e=>{e.exports=require("meilisearch")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[3181,5016,6859,8450,9755,4033,779,7113,6453,1077,8672,723,5745,3462,2924,1857],()=>__webpack_exec__(4138));module.exports=a})();