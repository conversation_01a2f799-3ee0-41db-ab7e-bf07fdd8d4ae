/**
 * Script pour réindexer les données de Strapi vers Meilisearch
 *
 * Pour exécuter ce script :
 * 1. Assurez-vous que Strapi et Meilisearch sont en cours d'exécution
 * 2. Exécutez la commande : node scripts/reindex-meilisearch.js
 */

// Importer Strapi
const strapi = require('strapi');
const { MeiliSearch } = require('meilisearch');
require('dotenv').config();

// Connexion à Meilisearch
const client = new MeiliSearch({
  host: process.env.MEILI_HOST || 'http://localhost:7700',
  apiKey: process.env.MEILI_ADMIN_KEY || '',
});

// Fonction pour préparer les posts au format attendu par Meilisearch
const preparePosts = (entries) => {
  return entries.map((entry) => {
    let lead = entry.modules.find(function (module) {
      return module.__component === "module.lead";
    });

    if (lead) {
      lead = strapi.config.functions["utils"].removeMarkdown(lead.content);
      lead = strapi.config.functions["utils"].removeHtml(lead);
    }

    let route = "/";

    switch(entry.type) {
      case "podcast":
        let podcastModule = entry.modules.find(function (module) {
          return module.__component === "module.podcast";
        });
        route = `/podcasts/${podcastModule?.podcast?.slug}/${entry.slug}`;
        break;
      case "parcours":
        route = `/parcours-emails/${entry.slug}`;
        break;
      case "webinaire":
        route = `/webinaires/${entry.slug}`;
        break;
      case "formation":
        route = `/article/${entry.slug}`;
        let formationModule = entry.modules.find(function (module) {
          return module.__component === "module.formation";
        });
        if(formationModule?.link) {
          route = formationModule?.link;
        }
        break;
      default:
        route = `/article/${entry.slug}`;
    }

    let body = strapi.config.functions["utils"].removeMarkdown(entry.body);
    body = strapi.config.functions["utils"].removeHtml(body);

    return {
      id: `post-${entry.id}`,
      title: entry.title,
      slug: entry.slug,
      author: entry.author?.fullName || null,
      tags: entry.tags.map((tag) => tag.name),
      topics: entry.topics.map((topic) => topic.name),
      type: entry.type || null,
      lead: lead || null,
      body: body || null,
      date: entry.published_at,
      blog: entry.blog?.slug || null,
      image: entry.image || null,
      route: route,
      cs: entry.isCornerStone
    }
  });
};

// Fonction principale pour réindexer les données
async function reindexMeilisearch() {
  try {
    console.log('Démarrage de la réindexation...');

    // Vérifier si l'index existe
    const indexes = await client.getIndexes();
    const indexExists = indexes.results.some(index => index.uid === 'post');

    // Si l'index n'existe pas, le créer
    if (!indexExists) {
      console.log('Création de l\'index "post"...');
      await client.createIndex('post');
    }

    // Configurer les attributs filtrables
    console.log('Configuration des attributs filtrables...');
    await client.index('post').updateFilterableAttributes([
      'title',
      'topics',
      'tags',
      'author',
      'type',
      'blog',
      'cs',
    ]);

    // Configurer les attributs triables
    console.log('Configuration des attributs triables...');
    await client.index('post').updateSortableAttributes(['date']);

    // Configurer les attributs de recherche
    console.log('Configuration des attributs de recherche...');
    await client.index('post').updateSearchableAttributes([
      'title',
      'body'
    ]);

    // Récupérer tous les posts depuis Strapi
    console.log('Récupération des posts depuis Strapi...');
    const posts = await strapi.services.post.find({_limit: -1});

    // Préparer les posts pour Meilisearch
    console.log('Préparation des posts pour Meilisearch...');
    const preparedPosts = preparePosts(posts);

    // Ajouter les posts à l'index
    console.log('Ajout des posts à l\'index Meilisearch...');
    await client.index('post').addDocuments(preparedPosts);

    console.log(`Réindexation terminée ! ${preparedPosts.length} documents ajoutés.`);
  } catch (error) {
    console.error('Erreur lors de la réindexation :', error);
  }
}

// Exécuter le script
strapi.start().then(() => {
  reindexMeilisearch()
    .then(() => process.exit(0))
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
});
