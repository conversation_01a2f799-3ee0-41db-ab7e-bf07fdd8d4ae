{"kind": "FETCH", "data": {"headers": {"connection": "keep-alive", "content-length": "135701", "content-type": "application/json", "date": "<PERSON><PERSON>, 27 May 2025 11:41:38 GMT", "keep-alive": "timeout=5", "strict-transport-security": "max-age=31536000; includeSubDomains", "vary": "Origin", "x-frame-options": "SAMEORIGIN", "x-powered-by": "Strapi <strapi.io>", "x-response-time": "243ms"}, "body": "eyJkYXRhIjp7InBvc3RzIjpbeyJpZCI6IjQxMzEiLCJ0aXRsZSI6IlF1aSBlc3QgRGlldT8gKFBzYXVtZXMgMTM5KSIsInNsdWciOiJxdWktZXN0LWRpZXUtcHNhdW1lcy0xMzkiLCJ0eXBlIjoicG9kY2FzdCIsInB1Ymxpc2hlZF9hdCI6IjIwMjQtMTAtMDJUMDQ6MzA6MDAuMDAwWiIsImJvZHkiOiJcXFxuIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiUmFwaGHDq2wgQ2hhcnJpZXIiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvcmFwaGFlbF9jaGFycmllcl8wM19jb3BpZV8xX3NjYWxlZF9lMTY1NzgwMTI1Nzc4N19mOTM1OTk0MDM4LmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L1BzYXVtZXNfMTM5X1lvdV9UdWJlX2IzZDJlNDVjOWIucG5nIiwiaGVpZ2h0Ijo3MjAsIndpZHRoIjoxMjgwLCJhbHRlcm5hdGl2ZVRleHQiOiIiLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiJEb2N0cmluZSBkZSBEaWV1In0seyJuYW1lIjoiUHNhdW1lcyJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiRGFucyBQc2F1bWVzIDEzOSwgRGF2aWQgbm91cyBpbnZpdGUgw6AgY29udGVtcGxlciBsYSBncmFuZGV1ciBkZSBEaWV1IHF1aSBjb25uYcOudCB0b3V0IGRlIG5vdHJlIHZpZSwgZGUgbm9zIHBlbnPDqWVzIGV0IGRlIG5vcyBhc3BpcmF0aW9ucy4gSWwgY29uY2x1dDogY29tbWVudCDDqWNoYXBwZXIgw6Agc2EgcHLDqXNlbmNlPyBDZSB0ZXh0ZSBub3VzIGNvbmR1aXQgw6AgZMOpY291dnJpciBxdWkgZXN0IERpZXUsIGFpbnNpIHF1ZSBzYSBwcm94aW1pdMOpIGV0IHNvbiBhbW91ciBpbmZpbmkgcG91ciBub3VzLiJ9LHt9LHsiX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZVBvZGNhc3QiLCJwb2RjYXN0Ijp7InNsdWciOiJwcmVkaWNhdGlvbnMtdHBzZyIsIm5hbWUiOiJQcsOpZGljYXRpb25zIFRQU0cifSwiZW1iZWRBdWRpbyI6bnVsbCwiZW1iZWRWaWRlbyI6IjxpZnJhbWUgd2lkdGg9XCIxMDYwXCIgaGVpZ2h0PVwiNTk2XCIgc3JjPVwiaHR0cHM6Ly93d3cueW91dHViZS5jb20vZW1iZWQvN1lLY1NWNkotSlFcIiB0aXRsZT1cIlwiIGZyYW1lYm9yZGVyPVwiMFwiIGFsbG93PVwiYWNjZWxlcm9tZXRlcjsgYXV0b3BsYXk7IGNsaXBib2FyZC13cml0ZTsgZW5jcnlwdGVkLW1lZGlhOyBneXJvc2NvcGU7IHBpY3R1cmUtaW4tcGljdHVyZTsgd2ViLXNoYXJlXCIgcmVmZXJyZXJwb2xpY3k9XCJzdHJpY3Qtb3JpZ2luLXdoZW4tY3Jvc3Mtb3JpZ2luXCIgYWxsb3dmdWxsc2NyZWVuPjwvaWZyYW1lPiJ9XX0seyJpZCI6IjQxMTYiLCJ0aXRsZSI6IlBvdXJxdW9pIERhdmlkIGEtdC1pbCB2b3VsdSBvZmZyaXIgdW4gc2FjcmlmaWNlIHF1aSBsdWkgY2/Du3RlPyAoMiBTYW11ZWwgMjQuMjQpIiwic2x1ZyI6InBvdXJxdW9pLWRhdmlkLXZvdWx1LW9mZnJpci1zYWNyaWZpY2UtcXVpLWNvdXRlIiwidHlwZSI6ImFydGljbGUiLCJwdWJsaXNoZWRfYXQiOiIyMDI0LTA5LTIwVDAzOjQ1OjAwLjAwMFoiLCJib2R5IjoiQ2UgcsOpY2l0IG1ldCBlbiBsdW1pw6hyZSB1bmUgbGXDp29uIGVzc2VudGllbGxlIHN1ciBsZSBzYWNyaWZpY2UgdsOpcml0YWJsZSBldCBzdXIgbCdhbW91ciBkZSBEaWV1LlxuXG4jIyBMZSBjb250ZXh0ZSBkdSBkw6lub21icmVtZW50XG5cbkFwcsOocyBhdm9pciBzdWJpIHVuZSBww6lyaW9kZSBkJ2h1bWlsaWF0aW9uIMOgIGxhIHN1aXRlIGRlIGxhIHLDqWJlbGxpb24gZGUgc29uIGZpbHMgQWJzYWxvbSwgRGF2aWQsIMOgIGxhIGZpbiBkZSBzb24gcsOoZ25lLCBkw6ljaWRlIGRlIGZhaXJlIHVuIHJlY2Vuc2VtZW50IGRlIHNlcyBmb3JjZXMgbWlsaXRhaXJlcy4gQ2UgZMOpbm9tYnJlbWVudCBuJ8OpdGFpdCBwYXMgYW5vZGluLiBMYSBCaWJsZSBub3VzIGRvbm5lIHBldSBk4oCZw6lsw6ltZW50cyBxdWFudCBhdXggbW90aXZhdGlvbnMgZGUgRGF2aWQuIMOJdGFpdC1jZSBwYXIgaW5jcsOpZHVsaXTDqSBzdWl0ZSDDoCB1bmUgZMOpZmFpdGU/IFBhciBvcmd1ZWlsLCBzdWl0ZSDDoCBsYSByw6liZWxsaW9uIGTigJlBYnNhbG9tPyBRdW9pIHF14oCZaWwgZW4gc29pdCwgRGF2aWQgYSBkw6lzb2LDqWkgYXV4IGF2ZXJ0aXNzZW1lbnRzIGRlIERpZXUgZG9ubsOpcyBwYXIgTW/Dr3NlIChEdCA4LjE3LTE4KS4gTGUgZmFpdCBkZSBjb21wdGVyIHN1ciBzZXMgcHJvcHJlcyBmb3JjZXMgcGx1dMO0dCBxdWUgc3VyIERpZXUgw6l0YWl0IHVuIGFjdGUgZGUgdmFuaXTDqSBxdWkgbWl0IERpZXUgZW4gY29sw6hyZS5cblxuRGF2aWQsIGFwcsOocyBhdm9pciByZcOndSBsZSByYXBwb3J0IGRlIHNlcyBnw6luw6lyYXV4LCBwcmVuZCBjb25zY2llbmNlIGRlIGxhIGdyYXZpdMOpIGRlIHNvbiBhY3RlIGV0IHJlc3NlbnQgdW4gcHJvZm9uZCByZWdyZXQuIERpZXUsIHBhciBsJ2ludGVybcOpZGlhaXJlIGR1IHByb3Bow6h0ZSBHYWQsIGx1aSBwcm9wb3NlIHRyb2lzIG9wdGlvbnMgZGUganVnZW1lbnQgcG91ciBJc3Jhw6tsOiB0cm9pcyBhbnMgZGUgZmFtaW5lLCB0cm9pcyBtb2lzIGRlIGTDqWZhaXRlIG1pbGl0YWlyZSwgb3UgdHJvaXMgam91cnMgZGUgcGVzdGUuIERhdmlkIGNob2lzaXQgZGUgc2UgcmVtZXR0cmUgZW50cmUgbGVzIG1haW5zIGRlIERpZXUgZW4gb3B0YW50IHBvdXIgbGEgcGVzdGUsIHVuZSBtYWxhZGllIHN5bWJvbGlzYW50IGxlIHDDqWNow6kgZXQgbCfDqXRhdCBzcGlyaXR1ZWwgZHUgcGV1cGxlLiBDZSBjaG9peCBtb250cmUgcXVlIERhdmlkIHJlY29ubmHDrnQgc2EgY3VscGFiaWxpdMOpIGV0IHF1ZSBzZXVsIERpZXUsIGRhbnMgc2EgbWlzw6lyaWNvcmRlLCBwZXV0IGF0dMOpbnVlciBsYSBzZW50ZW5jZS5cblxuIyMgTGEgbWlzw6lyaWNvcmRlIGRlIERpZXVcblxuQWxvcnMgcXVlIGwnYW5nZSBkZSBEaWV1IHMnYXBwcsOqdGUgw6AgZMOpdHJ1aXJlIErDqXJ1c2FsZW0sIGlsIHMnYXJyw6p0ZSBzdXIgbGVzIHRlcnJlcyBkJ0FyYXZuYSAob3UgT3JuYW4gZGFucyBsZSByw6ljaXQgcGFyYWxsw6hsZSBkZSAxIENocm9uaXF1ZXMpIHNpdHXDqWVzIHN1ciBsZSBtb250IE1vcmlqYS4gRGlldSwgZGFucyBzYSBtaXPDqXJpY29yZGUsIGTDqWNpZGUgZGUgcmV0ZW5pciBzb24ganVnZW1lbnQuIEdhZCBkZW1hbmRlIGFsb3JzIMOgIERhdmlkIGRlIG1vbnRlciBzdXIgY2V0dGUgYWlyZSBldCBkJ3kgb2ZmcmlyIHVuIHNhY3JpZmljZSDDoCBEaWV1LCBzeW1ib2xlIGRlIHNvbiBhdmV1IGRlIGN1bHBhYmlsaXTDqSBldCBkZSBsYSBtaXPDqXJpY29yZGUgZGl2aW5lLlxuXG5Mb3JzcXVlIERhdmlkIGFycml2ZSBjaGV6IEFyYXZuYSwgY2UgZGVybmllciBwcm9wb3NlIGfDqW7DqXJldXNlbWVudCBkZSBsdWkgb2ZmcmlyIGdyYXR1aXRlbWVudCBsZSB0ZXJyYWluLCBsZSBiw6l0YWlsIGV0IHRvdXQgY2UgcXVpIGVzdCBuw6ljZXNzYWlyZSBwb3VyIGxlIHNhY3JpZmljZS4gRGF2aWQsIGJpZW4gcXUnaWwgYXVyYWl0IHB1IGFjY2VwdGVyIGNldHRlIG9mZnJlIGV0IHByZW5kcmUgcG9zc2Vzc2lvbiBkdSB0ZXJyYWluIGdyYXR1aXRlbWVudCBlbiB0YW50IHF1ZSByb2ksIHJlZnVzZSBjYXTDqWdvcmlxdWVtZW50OlxuXG4+IE5vbiEgSmUgdmV1eCB04oCZYWNoZXRlciBjZWxhIMOgIHNvbiBqdXN0ZSBwcml4LiBKZSBu4oCZb2ZmcmlyYWkgcGFzIMOgIGzigJnDiXRlcm5lbCwgbW9uIERpZXUsIGRlcyBob2xvY2F1c3RlcyBxdWkgbmUgbWUgY2/Du3RlbnQgcmllbi5cbj5cbj4gPHN1Yj48Y2l0ZT4yIFNhbXVlbCAyNC4yNFxuXG4jIyBQb3VycXVvaSBEYXZpZCBpbnNpc3RlLXQtaWwgc3VyIGxlIGNvw7t0IGRlIHNvbiBzYWNyaWZpY2U/XG5cbkxvcnNxdWUgauKAmcOpdGFpcyBmaWFuY8OpLCBq4oCZYWkgbWlzIGxlIHBldSBk4oCZYXJnZW50IHF1ZSBq4oCZYXZhaXMgZGFucyB1bmUgYmFndWUgZGUgZmlhbsOnYWlsbGVzICh2w6lyaXRhYmxlIGltaXRhdGlvbikgcG91ciBjZWxsZSBxdWkgYWxsYWl0IGRldmVuaXIgbWEgZmVtbWUuIErigJlhdXJhaXMgcHUgZGVtYW5kZXIgZGUgbOKAmWFpZGUgcG91ciBsYSBwYXllciwgbWFpcyBqZSB2b3VsYWlzIGxhIHBheWVyIG1vaS1tw6ptZSwgY2FyIGPigJnDqXRhaXQgKm1hKiBwcmV1dmUgZOKAmWFtb3VyIHBvdXIgZWxsZS5cblxuTOKAmWFtb3VyIGRlIERhdmlkIHBvdXIgRGlldSBuZSBwb3V2YWl0IMOqdHJlIGV4cHJpbcOpIHBhciBxdWVscXVlIGNob3NlIHF1aSBuZSBub3VzIGNvw7t0YWl0IHJpZW4uIEVuIHJlZnVzYW50IGRlIGZhaXJlIHVuIHNhY3JpZmljZSBncmF0dWl0LCBpbCB2b3VsYWl0IG1hbmlmZXN0ZXIgw6AgRGlldSBsYSBzaW5jw6lyaXTDqSBkZSBzb24gcmVwZW50aXIgZXQgbCdpbXBvcnRhbmNlIGRlIHNhIHJlY29ubmFpc3NhbmNlLiBVbiBzYWNyaWZpY2Ugb2ZmZXJ0IHNhbnMgY2/Du3QgcGVyc29ubmVsIG5lIHNlcmFpdCBxdWUgZGUgbCdoeXBvY3Jpc2llIHJlbGlnaWV1c2UsIHVuZSBzaW1wbGUgZm9ybWFsaXTDqSBkw6ludcOpZSBkZSB2YWxldXIgYXV4IHlldXggZGUgRGlldS5cblxuTCdhY3RlIGRlIERhdmlkIG1vbnRyZSBxdWUgbCdhbW91ciBhdXRoZW50aXF1ZSBzZSBtZXN1cmUgYXUgc2FjcmlmaWNlIHF1ZSBsJ29uIGVzdCBwcsOqdCDDoCBmYWlyZS4gQ2V0IGFtb3VyIG5lIHNlIGxpbWl0ZSBwYXMgw6AgZGVzIGRvbnMgbWF0w6lyaWVscywgbWFpcyBpbXBsaXF1ZSB1bmUgZGlzcG9zaXRpb24gZHUgY8WTdXIuIEwnZXhww6lyaWVuY2UgcGVyc29ubmVsbGUgZGUgRGF2aWQsIGlsbHVzdHLDqWUgcGFyIHNvbiBkw6lzaXIgZCdhY2hldGVyIGxhIHRlcnJlIGV0IGQnb2ZmcmlyIHVuIHNhY3JpZmljZSBjb8O7dGV1eCwgbm91cyBlbnNlaWduZSBxdWUgdG91dCB2w6lyaXRhYmxlIGFtb3VyLCBxdWUgY2Ugc29pdCBwb3VyIERpZXUgb3UgcG91ciBhdXRydWksIHNlIG1hbmlmZXN0ZSBwYXIgZGVzIGFjdGVzIHF1aSBub3VzIGNvw7t0ZW50IHF1ZWxxdWUgY2hvc2UuXG5cbiMjIFBvdXJxdW9pIERpZXUgYS10LWlsIHZvdWx1IG5vdXMgZmFpcmUgY29ubmHDrnRyZSBjZXQgw6l2w6huZW1lbnQ/XG5cbkxlIGNob2l4IGRlIGwnYWlyZSBkJ0FyYXZuYSwgc3VyIGxlIG1vbnQgTW9yaWphLCBwb3VyIGxlIHNhY3JpZmljZSBuJ2VzdCBwYXMgYW5vZGluLiBD4oCZZXN0IMOgIGNlIGxpZXUtbcOqbWUgcXXigJlBYnJhaGFtLCBtaWxsZSBhbnMgcGx1cyB0w7R0LCBhdmFpdCBmYWlsbGkgc2FjcmlmaWVyIHNvbiBmaWxzIElzYWFjIHBhciBvYsOpaXNzYW5jZSDDoCBEaWV1IChHbiAyMi4yKS4gQydlc3QgbMOgIHF1ZSBEaWV1IGF2YWl0IHJldGVudSBsYSBtYWluIGQnQWJyYWhhbSBldCBmb3VybmkgdW4gYsOpbGllciBlbiByZW1wbGFjZW1lbnQsIHByb21ldHRhbnQgcXUnw6AgY2V0IGVuZHJvaXQsIGlsIHBvdXJ2b2lyYWl0IGF1IHNhY3JpZmljZSAoR24gMjIuMTQpLiBDZXR0ZSBwcm9tZXNzZSBwcmVuYWl0IHVuIHNlbnMgcHJvcGjDqXRpcXVlLlxuXG5FbiByYWNoZXRhbnQgY2UgdGVycmFpbiwgRGF2aWQgbmUgY2FsbWUgcGFzIHNldWxlbWVudCBsYSBjb2zDqHJlIGRpdmluZSBwb3VyIHVuIG1vbWVudDsgaWwgY29uc2FjcmUgY2UgbGlldSDDoCBsJ8OJdGVybmVsIHBvdXIgbCfDqXRlcm5pdMOpLiBDZSBtw6ptZSBsaWV1IGRldmllbmRyYSBwbHVzIHRhcmQgbCdlbXBsYWNlbWVudCBkdSB0ZW1wbGUgZGUgSsOpcnVzYWxlbSwgY29uc3RydWl0IHBhciBzb24gZmlscyBTYWxvbW9uLCBvw7kgc2Vyb250IG9mZmVydHMgbGVzIHNhY3JpZmljZXMgZXhwaWF0b2lyZXMgYXUgbm9tIGR1IHBldXBsZS4gQ2VwZW5kYW50LCBtYWxncsOpIHRvdXMgbGVzIHNhY3JpZmljZXMgb2ZmZXJ0cyBkYW5zIGNlIHRlbXBsZSwgYXVjdW4gbmUgcG91dmFpdCByw6llbGxlbWVudCDDtHRlciBsYSBjdWxwYWJpbGl0w6kgZHUgcGV1cGxlLlxuXG5Ub3VzIGNlcyBzYWNyaWZpY2VzIGZhaXNhaWVudCBwYXJ0aWUgZHUgcGxhbiBkZSBEaWV1LCBxdWkgY29uZHVpcmFpZW50IGF1IHNhY3JpZmljZSB1bHRpbWUgZGUgSsOpc3VzLUNocmlzdC4gSsOpc3VzLCBjcnVjaWZpw6kgc3VyIGxlIG3Dqm1lIG1vbnQgTW9yaWphLCBlc3QgbGUgdsOpcml0YWJsZSBhY2NvbXBsaXNzZW1lbnQgZGUgbGEgcHJvbWVzc2UgZmFpdGUgw6AgQWJyYWhhbTogKuKAnMOAIGxhIG1vbnRhZ25lIGRlIGzigJnDiXRlcm5lbCwgaWwgc2VyYSBwb3VydnXigJ0qIChHbiAyMi4xNCkuIFBhciBzb24gc2FjcmlmaWNlLCBKw6lzdXMgZGV2aWVudCBsYSB2aWN0aW1lIGV4cGlhdG9pcmUgcGFyZmFpdGUsIG9mZnJhbnQgdW5lIGV4cGlhdGlvbiBkw6lmaW5pdGl2ZSBwb3VyIGxlcyBww6ljaMOpcyBkZSB0b3VzIGNldXggcXVpIGNyb2llbnQgKFJtIDMuMjMtMjYpLiBFbiBsdWksIERpZXUgYSBwb3VydnUgYXUgc2FjcmlmaWNlIHVsdGltZSwgZMOpbW9udHJhbnQgYWluc2kgc29uIGFtb3VyIGV0IHNhIGp1c3RpY2UuXG5cbiMjIExlw6dvbnMgcG91ciBub3VzIGF1am91cmQnaHVpXG5cbkNlIHBhc3NhZ2Ugbm91cyBlbnNlaWduZSBxdWUgbGEgdmllIGNocsOpdGllbm5lIGF1dGhlbnRpcXVlIG5lIGNvbnNpc3RlIHBhcyDDoCBvZmZyaXIgw6AgRGlldSBjZSBxdWkgbmUgbm91cyBjb8O7dGUgcmllbi4gTGEgdsOpcml0YWJsZSBwacOpdMOpIGV4aWdlIHVuIGVuZ2FnZW1lbnQgdG90YWwsIHVuIHNhY3JpZmljZSBkZSBzb2ktbcOqbWUgZW4gcsOpcG9uc2UgKGV0IG5vbiBwb3VyIG9idGVuaXIpIMOgIGwnaW1tZW5zZSBncsOiY2UgZGUgRGlldS4gTGUgc2FsdXQgZXN0IHVuIGRvbiBncmF0dWl0LCBtYWlzIG5vdHJlIHLDqXBvbnNlIGRvaXQgw6p0cmUgdW5lIHZpZSBjb25zYWNyw6llIMOgIERpZXUuXG5cblNvbW1lcy1ub3VzIHByw6p0cyDDoCBvZmZyaXIgw6AgRGlldSBub24gc2V1bGVtZW50IGNlIHF1aSBlc3QgZmFjaWxlLCBtYWlzIGF1c3NpIGNlIHF1aSBub3VzIGNvw7t0ZSB2cmFpbWVudD8gUXVlbGxlIGVzdCBub3RyZSBkaXNwb3NpdGlvbiBkdSBjxZN1ciBsb3JzcXUnaWwgcydhZ2l0IGRlIHNlcnZpciBEaWV1IGV0IHNvbiDDiWdsaXNlLCBkZSBwYXJkb25uZXIgb3UgZGUgZG9ubmVyIGRlIG5vdHJlIHRlbXBzIGV0IGRlIG5vcyByZXNzb3VyY2VzP1xuXG5MJ2Ftb3VyIHbDqXJpdGFibGUgc2UgbWVzdXJlIHBhciBsZSBzYWNyaWZpY2UgcXUnaWwgaW1wbGlxdWUuIFNpIG5vdXMgbidvZmZyb25zIMOgIERpZXUgcXVlIGNlIHF1aSBuZSBub3VzIGNvw7t0ZSByaWVuLCBjZWxhIHJlZmzDqHRlIGxhIHRpw6lkZXVyIGRlIG5vdHJlIGFtb3VyIGV0IGRlIG5vdHJlIGVuZ2FnZW1lbnQgZW52ZXJzIGx1aS5cblxuRGF2aWQgbm91cyByYXBwZWxsZSBxdWUgbGUgdsOpcml0YWJsZSBhbW91ciBwb3VyIERpZXUgc2UgbWFuaWZlc3RlIHBhciBub3RyZSB2b2xvbnTDqSBkZSBzYWNyaWZpZXIgY2UgcXVpIG5vdXMgZXN0IHByw6ljaWV1eCBwb3VyIGwnYWRvcmVyIGV0IGxlIHNlcnZpci5cblxuUXVvaXF1ZSBub3VzIG9mZnJpb25zIMOgIERpZXUsIGNlIG5lIHNlcmEgamFtYWlzIMOgIGxhIGhhdXRldXIgZHUgc2FjcmlmaWNlIHF1J2lsIGEgZmFpdCBwb3VyIG5vdXMgw6AgdHJhdmVycyBsYSBjcm9peC4gQ2VwZW5kYW50LCBub3RyZSByZWNvbm5haXNzYW5jZSBwb3VyIGNlIHNhY3JpZmljZSBkb2l0IG5vdXMgcG91c3NlciDDoCBvZmZyaXIgbm90cmUgdmllIGVudGnDqHJlIMOgIERpZXUsIG5vbiBwYXIgb2JsaWdhdGlvbiwgbWFpcyBwYXIgYW1vdXIgZXQgcGFyIGTDqXNpciBkZSBsZSBnbG9yaWZpZXIuXG5cblF1ZSBs4oCZRXNwcml0IHNhaW50IG91dnJlIG5vcyB5ZXV4IHN1ciBsYSBncmFuZGV1ciBkZSBsYSBncsOiY2UgZGUgRGlldSBldCBub3VzIGluY2l0ZSDDoCB2aXZyZSB1bmUgdmllIGRlIGTDqXZvdGlvbiB0b3RhbGUsIG/DuSBjaGFxdWUgYWN0ZSBkZSBzYWNyaWZpY2UgY29uY8OpZMOpIGF2ZWMgam9pZSBlc3QgdW5lIGV4cHJlc3Npb24gZGUgbm90cmUgYW1vdXIgc2luY8OocmUgcG91ciBsdWkuIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiUmFwaGHDq2wgQ2hhcnJpZXIiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvcmFwaGFlbF9jaGFycmllcl8wM19jb3BpZV8xX3NjYWxlZF9lMTY1NzgwMTI1Nzc4N19mOTM1OTk0MDM4LmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L3BleGVsc19waG90b180OTI3NzY4X2EyM2YwNDIzY2Eud2VicCIsImhlaWdodCI6NzUwLCJ3aWR0aCI6MTEyNSwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiQW1vdXIgZGUgRGlldSJ9LHsibmFtZSI6IlNvdWZmcmFuY2VzIGV0IGxhIG1vcnQgZGUgQ2hyaXN0In0seyJuYW1lIjoiRG9jdHJpbmUgZHUgU2FsdXQifSx7Im5hbWUiOiJUaMOpb2xvZ2llIGRlIGzigJlhbGxpYW5jZSJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiRGFucyAyIFNhbXVlbCAyNC4yNCwgbm91cyBkw6ljb3V2cm9ucyB1biBwYXNzYWdlIGZhc2NpbmFudCBxdWkgcmVsYXRlIHVuIMOpcGlzb2RlIGNydWNpYWwgZGUgbGEgdmllIGR1IHJvaSBEYXZpZC4gQ2V0IMOpdsOpbmVtZW50LCBzb3V2ZW50IG1pcyBlbiBwYXJhbGzDqGxlIGF2ZWMgbGUgcsOpY2l0IGRlIDEgQ2hyb25pcXVlcyAyMSwgc2UgY29uY2VudHJlIHN1ciBsZSBqdWdlbWVudCBkZSBEaWV1IGVudmVycyBJc3Jhw6tsLCBzdWl0ZSDDoCB1biByZWNlbnNlbWVudCBpbml0acOpIHBhciBEYXZpZC4ifSx7fV19LHsiaWQiOiI0MTA3IiwidGl0bGUiOiJQb3VycXVvaSBsYSBGcmFuY2UgdmV1dC1lbGxlIGNyb2lyZSBlbiBsYSBmaW4gZHUgY2hyaXN0aWFuaXNtZT8iLCJzbHVnIjoicG91cnF1b2ktZnJhbmNlLXZldXQtY3JvaXJlLWZpbi1jaHJpc3RpYW5pc21lIiwidHlwZSI6ImFydGljbGUiLCJwdWJsaXNoZWRfYXQiOiIyMDI0LTA5LTEzVDAzOjQ1OjAwLjAwMFoiLCJib2R5IjoiU2kgYmVhdWNvdXAgcGVuc2VudCBhaW5zaSwgY+KAmWVzdCBwYXJjZSBxdWUgbGEgRnJhbmNlLCBjb21tZSBs4oCZT2NjaWRlbnQgZGFucyBzb24gZW5zZW1ibGUsIGEgY29ubnUgbGUgcHJvY2Vzc3VzIGRlIHPDqWN1bGFyaXNhdGlvbi4gSWwgc+KAmWFnaXQgZHUgcGjDqW5vbcOobmUgZ3JhZHVlbCBkZSByw6lncmVzc2lvbiBkZSBs4oCZaW1wYWN0IGR1IHJlbGlnaWV1eCBzdXIgbGEgc29jacOpdMOpIGV0IGzigJlpbmRpdmlkdS4gQXV0cmVtZW50IGRpdCwgbGEgc8OpY3VsYXJpc2F0aW9uIMOpdm9xdWUgPGEgaHJlZj1cIi9hcnRpY2xlL3Zpc2lvbi1kdS1tb25kZS1kZXNlbmNoYW50ZWVcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+bGEgbWFuaWZlc3RhdGlvbiBkdSBkw6lzZW5jaGFudGVtZW50IGR1IG1vbmRlPC9hPi5cblxuTGEgcmVsaWdpb24sIHBlcmRhbnQgZGUgcGx1cyBlbiBwbHVzIHNvbiBpbmZsdWVuY2Ugc29jacOpdGFsZSBldCBzb2NpYWxlLCB2YS10LWVsbGUgZGlzcGFyYcOudHJlPyBCZWF1Y291cCBk4oCZYXRow6llcyBsZSBzb3VoYWl0ZW50LCBsZXMgY2hyw6l0aWVucyBsZSByZWRvdXRlbnQuXG5cbiMjIFBvdXJxdW9pIGxlcyBnZW5zIHBlbnNlbnQtaWxzIHF1ZSBsZSBjaHJpc3RpYW5pc21lIGVzdCBhbmFjaHJvbmlxdWU/XG5cbkzigJlow6lyaXRhZ2UgZMOpaXN0ZSBkdSBzacOoY2xlIGRlcyBMdW1pw6hyZXMgZW4gRXVyb3BlIGEgc2FucyBjb250ZXN0ZSBpbnRyb2R1aXQgbOKAmWlkw6llIHF1ZSBsYSBmb2kgZGlzcGFyYcOudHJhaXQgdMO0dCBvdSB0YXJkLiBMZXMgaW50ZWxsZWN0dWVscyBvY2NpZGVudGF1eCBvbnQgcHLDqXN1cHBvc8OpIHF1ZSBsZSBtb2Rlcm5pc21lIGV0IGxlIG1hdMOpcmlhbGlzbWUgdHVlcmFpZW50IERpZXUuIEVuIGVmZmV0LCBsZXMgcGhpbG9zb3BoZXMgZXQgbGVzIHDDqHJlcyBkZXMgc2NpZW5jZXMgc29jaWFsZXMgbW9kZXJuZXMgZHUgWElY4bWJIGV0IGTDqWJ1dCBYWOG1iSwgdGVscyBxdeKAmUF1Z3VzdGUgQ29tdGUsIEVybmVzdCBSZW5hbiwgRnJpZWRyaWNoIE5pZXR6c2NoZSwgS2FybCBNYXJ4LCBNYXggV2ViZXIsIMOJbWlsZSBEdXJraGVpbSBvdSBTaWdtdW5kIEZyZXVkIChwb3VyIG5lIGNpdGVyIHF14oCZZXV4KSDDqXRhaWVudCBjb252YWluY3VzIHF1ZSBs4oCZYXZlbmlyIGFwcGFydGllbmRyYWl0IMOgIGxhIHNjaWVuY2UgcGx1dMO0dCBxdeKAmcOgIGxhIHJlbGlnaW9uLlxuXG5Tb3VzIGzigJllZmZldCBkZSBsYSBtb2Rlcm5pc2F0aW9uIGV0IGRlIGzigJlpbmR1c3RyaWFsaXNhdGlvbiBkaXJpZ8OpZXMgcGFyIGxlcyBzY2llbmNlcywgbGEgc29jacOpdMOpIHPigJnDqW1hbmNpcGVyYWl0IGTDqWZpbml0aXZlbWVudCBkZSBsYSByZWxpZ2lvbi4gU2Vsb24gY2V0dGUgaHlwb3Row6hzZSwgbGUgcHJvY2Vzc3VzIGRlIHPDqWN1bGFyaXNhdGlvbiBhcnJpdsOpIMOgIHNvbiB0ZXJtZSBjb25kdWlyYWl0IMOgIGxhIGRpc3Bhcml0aW9uIGRlIGxhIHJlbGlnaW9uIGV0IGRlIGxhIGNyb3lhbmNlIHJlbGlnaWV1c2UuXG5cbkNldHRlIHRow6lvcmllIG5vbW3DqWUgXCJzw6ljdWxhcmlzYXRpb24gcGFyIHNvdXN0cmFjdGlvblwiPHN1cCBpZD1cIjFcIj5bMV0oL2FydGljbGUvcG91cnF1b2ktZnJhbmNlLXZldXQtY3JvaXJlLWZpbi1jaHJpc3RpYW5pc21lI25vdGUxKTwvc3VwPiAoU1BTKSBlc3QgbGEgcGx1cyBjb3VyYW50ZSBldCBsYSBwbHVzIHBvcHVsYWlyZSwgY29tbWUgbGUgc291bGlnbmUgbOKAmWhpc3RvcmllbiBldCBzb2Npb2xvZ3VlIE1hcmNlbCBHYXVjaGV0OlxuXG4+IFVuZSBsZWN0dXJlIGNvdXJhbnRlIGVuIHRlcm1lcyBkZSBcInNvdXN0cmFjdGlvblwiIGF0dHJpYnVlIHRvdXQgYXUgZMOpc2VuY2hhbnRlbWVudC4gTGEgc2NpZW5jZSBub3VzIGEgZOKAmWFib3JkIGRvbm7DqSB1bmUgZXhwbGljYXRpb24gXCJuYXR1cmFsaXN0ZVwiIGR1IG1vbmRlIGV0IGxlcyBob21tZXMgb250IGVuc3VpdGUgY29tbWVuY8OpIMOgIGNoZXJjaGVyIGTigJlhdXRyZXMgdm9pZXMsIGVuIGRlaG9ycyBkZSBEaWV1LiBPciBsZXMgY2hvc2VzIG5lIHNlIHNvbnQgcGFzIGTDqXJvdWzDqWVzIGRlIGNldHRlIGZhw6dvbi4gTG9yc3F1ZSBsYSBzY2llbmNlIG3DqWNhbmlxdWUgYXBwYXJhw650IGF1IFhWSUnhtYkgc2nDqGNsZSwgZWxsZSBu4oCZZXN0IHBhcyBuw6ljZXNzYWlyZW1lbnQgcGVyw6d1ZSBjb21tZSB1bmUgbWVuYWNlIMOgIGzigJnDqWdhcmQgZGUgRGlldSwgc2V1bGVtZW50IHBvdXIgbOKAmXVuaXZlcnMgZW5jaGFudMOpIGV0IG1hZ2lxdWUuIENlbGEgY29tbWVuw6dhaXQgw6lnYWxlbWVudCDDoCBtZXR0cmUgZW4gZGlmZmljdWx0w6kgbGVzIHByb3ZpZGVuY2VzIHBhcnRpY3VsacOocmVzLCBtYWlzIGxlIGNocmlzdGlhbmlzbWUgZG9ubmFpdCBkZXMgcmFpc29ucyBzdWZmaXNhbnRlcyBk4oCZZW1wcnVudGVyIGxhIHZvaWUgZHUgZMOpc2VuY2hhbnRlbWVudC4gRGFyd2luIMOpdGFpdCBlbmNvcmUgbG9pbiwgbcOqbWUgYXUgWFZJSUnhtYkgc2nDqGNsZTxzdXAgaWQ9XCIyXCI+WzJdKC9hcnRpY2xlL3BvdXJxdW9pLWZyYW5jZS12ZXV0LWNyb2lyZS1maW4tY2hyaXN0aWFuaXNtZSNub3RlMik8L3N1cD4uXG5cbkNldXggcXVpIG9udCBwcsOpZGl0IGxhIHPDqWN1bGFyaXNhdGlvbiBkZSB0b3V0ZXMgbGVzIHNvY2nDqXTDqXMgbW9kZXJuaXPDqWVzIG9udCBmb25kw6kgbGV1cnMgdGjDqW9yaWVzIHN1ciBs4oCZaHlwb3Row6hzZSBzZWxvbiBsYXF1ZWxsZSBsYSBzY2llbmNlIG1vZGVybmUgZXQgbGEgcmVsaWdpb24gc29udCBpbmNvbXBhdGlibGVzLiBWb2lsw6AgdW4gcGFyYWRveGU6IGRlIG5vbWJyZXV4IHBlbnNldXJzIG9udCBhZGjDqXLDqSDDoCBjZSBtw6l0YXLDqWNpdCBtYWxncsOpIHNvbiBtYW5xdWUgZGUgZm9uZGVtZW50IHNjaWVudGlmaXF1ZS4gRW4gZWZmZXQsIGNlIG5hcnJhdGlmIHRyw6hzIHPDqWR1aXNhbnQgcG91ciBsZXMgZMOpdHJhY3RldXJzIGRlIGxhIHJlbGlnaW9uIGVzdCBhcHByw6ljacOpIGNvbW1lIMOpdGFudCBjZWx1aSDigJxkZSBsYSBtYXR1cml0w6kgZXQgZHUgY291cmFnZeKAnSwgc291bGlnbmUgSmFtZXMgSy4gQS4gU21pdGg8c3VwIGlkPVwiM1wiPlszXSgvYXJ0aWNsZS9wb3VycXVvaS1mcmFuY2UtdmV1dC1jcm9pcmUtZmluLWNocmlzdGlhbmlzbWUjbm90ZTMpPC9zdXA+LiBQYXNzZXIgZGUgbGEgcmVsaWdpb24gw6AgbOKAmWh1bWFuaXNtZSBhdGjDqWUgZXN0IHVuIHLDqWNpdCBjb3VyYWdldXggZOKAmcOpbWFuY2lwYXRpb24gZHUgY2FyY2FuIGRlIGxhIHJlbGlnaW9uIHZlcnMgbGEgcmFpc29uIGxpYsOpcmF0cmljZS4gKuKAnEF2YW50LCBub3VzIMOpdGlvbnMgbmHDr2ZzLCBub3VzIGF2aW9ucyBiZXNvaW4gZGUgY3JvaXJlLCBhdWpvdXJk4oCZaHVpLCBub3VzIHNvbW1lcyBtYXR1cmVzLCBub3VzIHNhdm9ucyBiaWVuIHF1ZeKApuKAnSpcblxuQXUgcGF5cyBkZXMgTHVtacOocmVzLCBjZXR0ZSBhbmFseXNlIGEgYmVhdWNvdXAgc8OpZHVpdCwgb2ZmcmFudCB1bmUgZGVzdGluw6llIHJhdGlvbm5lbGxlIGRvbnQgbOKAmWhvbW1lIGVzdCBsZSBtYcOudHJlLlxuXG5MYSBwaGlsb3NvcGhlIFN5bHZpZSBUYXVzc2lnIGTDqWNyaXQgYWluc2kgbGEgU1BTOlxuXG4+IEzigJloaXN0b2lyZSBcInBhciBzb3VzdHJhY3Rpb25cIiBlc3QgcHJvZm9uZMOpbWVudCBhbmNyw6llIGRhbnMgbGEgY29uc2NpZW5jZSBodW1hbmlzdGUgbW9kZXJuZSwgc2kgYmllbiBxdeKAmWVsbGUgZXN0IGRldmVudWUgb2JqZXQgZGUgY3JveWFuY2UgKGRpc29ucyB1bmUgaWTDqW9sb2dpZSk6IHBvdXIgc29uIFwiZ3JhbmQgcsOpY2l0XCIsIGzigJlodW1hbmlzbWUgbW9kZXJuZSBlc3QgYXBwYXJ1IHBhcmNlIHF1ZSBsZXMgaG9tbWVzIHNvbnQgZGV2ZW51cyBjYXBhYmxlcyBkZSBzZSBkw6liYXJyYXNzZXIgZGVzIGFuY2llbm5lcyDDqXRoaXF1ZXMgYXNjw6l0aXF1ZXMgZGUgbOKAmWF1dHJlIG1vbmRlLiBDZXR0ZSBoaXN0b2lyZSBzZSBmb25kZSBzdXIgdW5lIHJlcHLDqXNlbnRhdGlvbiBkZSBsYSBtb3RpdmF0aW9uIGh1bWFpbmUgZXQgZGVzIHNvdXJjZXMgZGUgbGEgY3JveWFuY2UgcmVsaWdpZXVzZSwgZMOpY3JpdGUgY29tbWUgbGUgcsOpc3VsdGF0IGRlIGxhIG1pc8OocmUsIGRlIGxhIHByaXZhdGlvbiwgZGUgbOKAmWh1bWlsaWF0aW9uIGV0IGRlIGxhIGTDqXNlc3DDqXJhbmNlLCBs4oCZYXV0cmUgZmFjZSBkdSBkw6lzaXIgaHVtYWluIGRlIGzigJlhY2NvbXBsaXNzZW1lbnQsIHF1YW5kIGNlbHVpLWNpIGVzdCBmcnVzdHLDqS4gQ2V0dGUgdmlzaW9uIG9ww6hyZSBsYSBuYXR1cmFsaXNhdGlvbiBkZSB0b3V0ZXMgbGVzIGZhY2V0dGVzIGh1bWFpbmVzIGV0IHNvdXRpZW50IGxlIG1hdMOpcmlhbGlzbWUgZW4gZmFpc2FudCBkZSBsYSBzY2llbmNlIGzigJllbm5lbWkgZGUgbGEgcmVsaWdpb24gc2FucyB2b2lyIHF1ZSBsZSBtYXTDqXJpYWxpc21lIGVzdCBsdWktbcOqbWUgdW5lIHRow6hzZSBvbnRvbG9naXF1ZTxzdXAgaWQ9XCI0XCI+WzRdKC9hcnRpY2xlL3BvdXJxdW9pLWZyYW5jZS12ZXV0LWNyb2lyZS1maW4tY2hyaXN0aWFuaXNtZSNub3RlNCk8L3N1cD4uXG5cbiMjIExhIEZyYW5jZSBu4oCZZXN0LWVsbGUgcGFzIGxhIHByZXV2ZSBxdWUgbGVzIGNocsOpdGllbnMgc29udCBlbiB2b2llIGRlIGRpc3Bhcml0aW9uP1xuXG5DZXJ0ZXMsIGxhIHRow6lvcmllIGRlIGxhIHNvdXN0cmFjdGlvbiBhIHRyb3V2w6kgdW4gY2VydGFpbiBhcHB1aSBkYW5zIGxlcyBkb25uw6llcyBldXJvcMOpZW5uZXMsIGRvbnQgbGEgRnJhbmNlLCBtb250cmFudCB1biBkw6ljbGluIGRlIGzigJlhcHBhcnRlbmFuY2UgZXQgZGUgbGEgcHJhdGlxdWUgcmVsaWdpZXVzZXMuIFByZXV2ZSBpcnLDqWZ1dGFibGU/XG5cbk5vdXMsIMOpdmFuZ8OpbGlxdWVzLCBzb21tZXMgbGEgcHJldXZlIHF1ZSBub24uIE5vdXMgc29tbWVzIGVuIHBsZWluZSBjcm9pc3NhbmNlLiBRdWUgZGlyZSBkZXMgY2F0aG9saXF1ZXMgcXVpIG9udCBjb25udSB1bmUgZm9ydGUgaGF1c3NlIGRlcyBiYXB0w6ptZXMgY2hleiBsZXMgYWR1bHRlcyAoKzMxICUpIGV0IGxlcyBhZG9sZXNjZW50cyAoKzUwICUgZW4gbW95ZW5uZSksIGVuIDIwMjQ/XG5cbkxlIHLDqWNpdCBkZSBsYSBTUFMgc+KAmWFwcGxpcXVlIGRpZmZpY2lsZW1lbnQgYXUgY2FzLCBwYXIgZXhlbXBsZSwgZGVzIMOJdGF0cy1VbmlzLCBsZSBwYXlzIGxlIHBsdXMgbW9kZXJuZSAgYXUgbW9uZGUsIG1haXMgcXVpIGRlbWV1cmUg4oCcb25lIG5hdGlvbiB1bmRlciBHb2TigJ0uIExlcyBwcsOpc2lkZW50cyB5IHByw6p0ZW50IHNlcm1lbnQgc3VyIGxhIEJpYmxlIGV0IGxhIHJlbGlnaW9uIGNvbnNlcnZlIHVuZSBpbmZsdWVuY2UgZXh0csOqbWVtZW50IGZvcnRlIGRhbnMgdW5lIGdyYW5kZSBwYXJ0aWUgZHUgcGF5cy5cblxuRW4gRnJhbmNlLCBsZXMgZGlmZsOpcmVudGVzIHZhZ3VlcyBk4oCZaW1taWdyYXRpb24gYXlhbnQgZGVzIGxpZW5zIGRpcmVjdHMgb3UgaW5kaXJlY3RzIGF2ZWMgbGVzIGFuY2llbm5lcyBjb2xvbmllcyBk4oCZQWZyaXF1ZSBkdSBOb3JkIGV0IHN1YnNhaGFyaWVubmUsIG9udCBhbWVuw6kgdW5lIGNvbW11bmF1dMOpIG11c3VsbWFuZSwgbGEgcGx1cyBpbXBvcnRhbnRlIGVuIEV1cm9wZSBvY2NpZGVudGFsZSwgZG9udCBsYSBwZXJzaXN0YW5jZSBkZSBs4oCZYXR0YWNoZW1lbnQgw6AgbOKAmWlzbGFtIHN1ciBkZXMgZ8OpbsOpcmF0aW9ucyBkYW5zIHVuIMOJdGF0IGxhw69xdWUgaW50ZXJyb2dlLlxuXG5FbiBlZmZldCwgY3JveWFudCBlbiBsYSB0aMOpb3JpZSBkZSBsYSBkw6ktc8OpY3VsYXJpc2F0aW9uIHBhciBzb3VzdHJhY3Rpb24sIGzigJnDiXRhdCBmcmFuw6dhaXMgw6l0YWl0IGNvbnZhaW5jdSBxdWUgbGUgXCJzYW5jdHVhaXJlIGRlIGxhIFLDqXB1YmxpcXVlXCIgcXXigJllc3QgbOKAmcOpY29sZSBsYcOvcXVlIGRlIGxhIFLDqXB1YmxpcXVlIHRyYW5zZm9ybWVyYWl0IGNlcyBwb3B1bGF0aW9ucyBjcm95YW50ZXMgZXQgbmHDr3ZlcyBlbiBkZSBib25zIGF0aMOpZXMgYW1vdXJldXggZGUgbGEgbGHDr2NpdMOpLiBQb3VydGFudCBsJ2lkZW50aXTDqSBtdXN1bG1hbmUgZGVtZXVyZSB0csOocyBwcsOpZ25hbnRlIGNoZXogbGVzIGRlc2NlbmRhbnRzIGQnaW1taWdyw6lzIGV0IHPigJlpbnZpdGUgc291dmVudCBkYW5zIGxlIGTDqWJhdCBkZSBs4oCZw6ljb2xlLiBE4oCZb8O5IGxlcyBkw6liYXRzIGludGVybWluYWJsZXMgcXVhbnQgw6AgbGEgZmFpbGxpdGUgZGUgbOKAmcOJZHVjYXRpb24gTmF0aW9uYWxlIGV0IGRlIGxhIGxhw69jaXTDqS5cblxuUXVlIGRpcmUgZW5jb3JlIGRlcyBwYXlzIGRlIGzigJlleC1VUlNTLCBvw7kgbGUgY2hyaXN0aWFuaXNtZSBvcnRob2RveGUgc+KAmWVzdCBtYWludGVudSBtYWxncsOpIGxlIHLDqWdpbWUgdG90YWxpdGFpcmUgYXRow6lpc3RlIGV0IHF1aSBjb25udXQgdW5lIGZvcnRlIHLDqXN1cmdlbmNlIGxvcnMgZGUgc2EgZGlzc29sdXRpb24gZW4gMTk5MT9cblxuIyMgTGUgZMOpc2VuY2hhbnRlbWVudCBkdSBkw6lzZW5jaGFudGVtZW50XG5cbkNlcyDDqWzDqW1lbnRzLCBldCBiaWVuIGTigJlhdXRyZXMsIG9udCBjb25kdWl0IGRlIG5vbWJyZXV4IHNvY2lvbG9ndWVzIMOgIGNoYW5nZXIgbGV1ciBwb2ludCBkZSB2dWUsIMOgIGzigJlleGVtcGxlIGRlIFBldGVyIEJlcmdlcjpcblxuPiBMZSBtb25kZSBhdWpvdXJk4oCZaHVpLCBhdmVjIHF1ZWxxdWVzIGV4Y2VwdGlvbnMgXFxb4oCmXFxdIGVzdCBhdXNzaSBpbnRlbnPDqW1lbnQgcmVsaWdpZXV4IHF14oCZaWwgbuKAmWEgamFtYWlzIMOpdMOpLCBldCDDoCBjZXJ0YWlucyBlbmRyb2l0cyBwbHVzIHF1ZSBqYW1haXM8c3VwIGlkPVwiNVwiPls1XSgvYXJ0aWNsZS9wb3VycXVvaS1mcmFuY2UtdmV1dC1jcm9pcmUtZmluLWNocmlzdGlhbmlzbWUjbm90ZTUpPC9zdXA+LlxuXG5NYXJjZWwgR2F1Y2hldCBhZmZpcm1lIGVmZmVjdGl2ZW1lbnQ6XG5cbj4gVHLDqHMgbG9pbiBlbiBlZmZldCBkZSBs4oCZw6ltYW5jaXBhdGlvbiBhbm5vbmPDqWUgZXQgZXNww6lyw6llIHBhciBsZXMgTHVtacOocmVzLCBxdWkgZGV2YWl0IHBlcm1ldHRyZSBsYSByw6ljb25jaWxpYXRpb24gZGUgbOKAmWh1bWFuaXTDqSBhdmVjIGVsbGUtbcOqbWUgcGFyIGxlIGTDqXBhc3NlbWVudCBkZSBs4oCZYWxpw6luYXRpb24gcmVsaWdpZXVzZSwgbGUgbW9uZGUgc29ydGkgZGUgbGEgcmVsaWdpb24gc+KAmWF2w6hyZSBwbHVzIHByb2Jsw6ltYXRpcXVlIHF1ZSBqYW1haXMuIE9uIHBvdXJyYWl0IHBhcmxlciDDoCBjZXQgw6lnYXJkIGTigJl1biBcImTDqXNlbmNoYW50ZW1lbnQgZHUgZMOpc2VuY2hhbnRlbWVudFwiOiBub3VzIHkgc29tbWVzLCBldCBjZWxhIG5lIHJlc3NlbWJsZSB2cmFpbWVudCBwYXMgw6AgY2UgcXVpIMOpdGFpdCBhdHRlbmR1LCDDoCBjZSBxdeKAmWlsIHBhcmFpc3NhaXQgcGVybWlzIGTigJllbiBhdHRlbmRyZTxzdXAgaWQ9XCI2XCI+WzZdKC9hcnRpY2xlL3BvdXJxdW9pLWZyYW5jZS12ZXV0LWNyb2lyZS1maW4tY2hyaXN0aWFuaXNtZSNub3RlNik8L3N1cD4uXG5cbkxlIGTDqXNlbmNoYW50ZW1lbnQgZHUgbW9uZGUgZXN0IGTDqXNlbmNoYW50w6kuIExhIHPDqWN1bGFyaXNhdGlvbiBkZSBs4oCZT2NjaWRlbnQgbuKAmWEgcGFzIGNvbmR1aXQgw6AgdW4gYXRow6lpc21lIGjDqWfDqW1vbmlxdWUsIG1haXMgw6AgZGUgbm91dmVsbGVzIG1hbmnDqHJlcyBkZSBkw6lmaW5pciBsYSBjcm95YW5jZS5cblxuTGEgcGhpbG9zb3BoZSBDaGFudGFsIERlbHNvbCBmb3JtdWxlIGxlIG3Dqm1lIGNvbnN0YXQ6XG5cbj4gTm9uLCBsYSBmaW4gZGUgbGEgQ2hyw6l0aWVudMOpIG5lIGxhaXNzZXJhIHBhcyDDqW1lcmdlciBkZXMgc29jacOpdMOpcyBhdGjDqWVzLCBwYXJjZSBxdWUgY2VsYSBuJ2V4aXN0ZSBzaW1wbGVtZW50IHBhcy4gTGVzIHNvY2nDqXTDqXMgbmUgc29udCBwYXMgY29tcG9zw6llcyBkZSBxdWVscXVlcyBpbnRlbGxlY3R1ZWxzLCBtYWlzIGRlIHBldXBsZXMsIGF1eHF1ZWxzIGxlIGJvbiBzZW5zIHNvdWZmbGUgcXXigJlpbCBleGlzdGUgZGVzIG15c3TDqHJlcyBkZXJyacOocmUgbGEgcG9ydGUsIGV0IHF1aSBzb250IGF1IG1vaW5zIGFnbm9zdGlxdWVzIHPigJlpbHMgbmUgc29udCBwYXMgY3JveWFudHM8c3VwIGlkPVwiN1wiPls3XSgvYXJ0aWNsZS9wb3VycXVvaS1mcmFuY2UtdmV1dC1jcm9pcmUtZmluLWNocmlzdGlhbmlzbWUjbm90ZTcpPC9zdXA+LlxuXG5MZXMgb2JzZXJ2YXRpb25zIHNvY2lvbG9naXF1ZXMgZXQgbGEgcsOpZmxleGlvbiBwaGlsb3NvcGhpcXVlIHLDqWNlbnRlcyBpbmRpcXVlbnQgdW4gcGF5c2FnZSByZWxpZ2lldXggZW4gbXV0YXRpb24gcGx1dMO0dCBxdeKAmWVuIGTDqWNsaW4uIEVuIGVmZmV0LCBsZSBjaHJpc3RpYW5pc21lIGEgcGVyZHUgc29uIGluZmx1ZW5jZSBldCBzb24gbW9ub3BvbGUsIG1haXMgbGEgcmVsaWdpb3NpdMOpIG7igJlhIHBhcyByw6llbGxlbWVudCBkaW1pbnXDqS4gRGUgcGx1cyBlbiBwbHVzIGRlIHBlcnNvbm5lcyBzZSBkw6ljbGFyZW50IHNhbnMgYXBwYXJ0ZW5hbmNlIHJlbGlnaWV1c2Ugb2ZmaWNpZWxsZS4gTWFpcyBjZXMgbcOqbWVzIHBlcnNvbm5lcyBjb25zZXJ2ZW50IHVuZSBjZXJ0YWluZSBzcGlyaXR1YWxpdMOpIGV0IHVuZSBjdXJpb3NpdMOpIHBvdXIgbGEgdHJhbnNjZW5kYW5jZS5cblxuIyMgVW5lIGltcGxpY2F0aW9uIHBvdXIgbm90cmUgYXBvbG9nw6l0aXF1ZVxuXG5TaSBub3RyZSBhcG9sb2fDqXRpcXVlIHBhcnQgZHUgcHLDqXN1cHBvc8OpIHF1ZSBj4oCZZXN0IMOgIGNhdXNlIGRlcyBMdW1pw6hyZXMgKHBvdXIgZmFpcmUgc2ltcGxlKSBxdWUgbGVzIGdlbnMgbmUgY3JvaWVudCBwbHVzLCBub3VzIHZhbGlkb25zIGxhIHRow6lvcmllIGRlIGxhIHPDqWN1bGFyaXNhdGlvbiBwYXIgc291c3RyYWN0aW9uLiBOb3VzIGFjY2VwdG9ucyBsZSBwcsOpc3VwcG9zw6kgZGUgY2V1eCBxdWkgbm91cyBkaXNjcsOpZGl0ZW50LiBJbCBub3VzIHJldmllbnQgYWxvcnMgbGEgY2hhcmdlIGRlIHByb3V2ZXIgcXVlIGxhIHZpc2lvbiBiaWJsaXF1ZSBkdSBtb25kZSBlc3QgcmFpc29ubmFibGUuXG5cbk5vdXMgcG91dm9ucyDDqnRyZSBjb252YWluY3VzIHF1ZSwgY29uZHVpdHMgcGFyIGxldXIgcmFpc29uIMOpY2xhaXLDqWUgcGFyIGxhIGx1bWnDqHJlIGRlIGzigJnDiXZhbmdpbGUsIGxlcyBzY2VwdGlxdWVzIHZvbnQgc2UgY29udmVydGlyLiBOb3VzIMOpdmFuZ8OpbGlzb25zIGNvbW1lIGxlcyBMdW1pw6hyZXM8c3VwIGlkPVwiOFwiPls4XSgvYXJ0aWNsZS9wb3VycXVvaS1mcmFuY2UtdmV1dC1jcm9pcmUtZmluLWNocmlzdGlhbmlzbWUjbm90ZTgpPC9zdXA+LlxuXG5GYWlzb25zIGF0dGVudGlvbiDDoCBuZSBwYXMgdG9tYmVyIGRhbnMgbGUgcGnDqGdlIHRlbmR1IHBhciBsYSBTUFMuIEwnZW5qZXUgbuKAmWVzdCBwYXMgbGEgZm9pIHZzIGzigJlhdGjDqWlzbWUgb3UgbGEgbW9kZXJuaXTDqS4gPGEgaHJlZj1cIi9hcnRpY2xlL3Zpc2lvbi1kdS1tb25kZS1kZXNlbmNoYW50ZWVcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+TGEgc29jacOpdMOpIHBsdXJhbGlzdGUgZXQgc8OpY3VsYXJpc8OpZSBlc3QgdW5lIHNvY2nDqXTDqSBuw6lvLXBhw69lbm5lPC9hPi4gTm91cyBkZXZvbnMgYW5hbHlzZXIgbGVzIHZpc2lvbnMgZHUgbW9uZGUgYXV4cXVlbHMgbm9zIGNvbnRlbXBvcmFpbnMgYWRow6hyZW50IGNvbW1lIGF1dGFudCBkZSByZWxpZ2lvbnMsIHF14oCZZWxsZXMgc29pZW50IHRow6lpc3RlcyAoY29tbWUgbOKAmWlzbGFtKSBvdSBpbW1hbmVudGlzdGVzIChjb21tZSBsZSBwcm9ncmVzc2lzbWUpLiBNw6ptZSBz4oCZaWxzIG7igJllbiBvbnQgcGFzIGNvbnNjaWVuY2UsIG5vcyBjb250ZW1wb3JhaW5zIG9mZnJlbnQgZGVzIHByacOocmVzIGV0IGRlcyBzYWNyaWZpY2VzIMOgIHVuIFwiZGlldSBpbmNvbm51XCIgKEFjIDE3LjIzKS5cblxuVm9pciBsZXMgY2hvc2VzIGFpbnNpIGNoYW5nZSB0b3V0LiBJbCBuZSBz4oCZYWdpdCBwYXMgZGUgZGVtYW5kZXIgYXV4IGdlbnMgc+KAmWlscyBjcm9pZW50IG91IHBhcy4gTmkgcG91ciBub3VzLCBkZSBub3VzIGp1c3RpZmllciBkZSBjcm9pcmUuXG5cbk5vdXMgZGV2b25zIGNoZXJjaGVyIMOgIGNvbXByZW5kcmUgY2UgZW4gcXVpIChvdSBlbiBxdW9pKSBub3MgYW1pcyBjcm9pZW50IGFmaW4gZGUgZMOpY2VsZXIgbGVzIG1lbnNvbmdlcyBkZXMgaWRvbGVzLiBD4oCZZXN0IGFpbnNpIHF1ZSBub3VzIHBvdXZvbnMgbGV1ciBwYXJ0YWdlciBjb21tZW50IErDqXN1cyByw6lwb25kIMOgIGxldXJzIGFzcGlyYXRpb25zLCBldCBsZXVyIGV4cGxpcXVlciBwb3VycXVvaSBub3VzIHBsYcOnb25zIG5vdHJlIGNvbmZpYW5jZSBldCBub3RyZSBvYsOpaXNzYW5jZSBlbiBsdWktc2V1bC4gTm91cyBkZXZvbnMgbGV1ciBleHBsaXF1ZXIgZW4gcXVvaSBs4oCZw4l2YW5naWxlIGVzdCBsYSBCb25uZSBOb3V2ZWxsZSBxdWkgYXBwb3J0ZSB1bmUgdnJhaWUgZMOpbGl2cmFuY2UgZXQgdW5lIGVzcMOpcmFuY2UgcGx1cyBncmFuZGUgcXVlIHRvdXQuXG5cbk5vdHJlIHNvY2nDqXTDqSBwbHVyYWxpc3RlIGVzdCB1biBhcsOpb3BhZ2UgKGNmLiBBYyAxNykuIENvbW1lIGxlcyBhdGjDqW5pZW5zLCBub3MgY29udGVtcG9yYWlucyBhdmFuY2VudCDDoCB0w6J0b25zIMOgIGxhIHJlY2hlcmNoZSBkZSBsYSBwbMOpbml0dWRlLiBT4oCZaWxzIG5lIHBldXZlbnQgc+KAmWVtcMOqY2hlciBk4oCZYXZvaXIgdW5lIHF1w6p0ZSBzcGlyaXR1ZWxsZSwgY+KAmWVzdCBwYXJjZSBxdeKAmWlscyBzb250IGNyw6nDqXMgcGFyIHVuIERpZXUgcXVpIGxlcyBhcHBlbGxlIMOgIHZlbmlyIMOgIGx1aSBwYXIgbOKAmWFubm9uY2UgZGUgbGEgQm9ubmUgTm91dmVsbGUgZGUgSsOpc3VzLUNocmlzdC5cblxuPGJyIGlkPVwibm90ZTFcIj5cblxuYDxzdWI+MS4gTWFyY2VsIEdhdWNoZXQgw6l2b3F1ZSBsZSB0ZXJtZSBkZSBcInPDqWN1bGFyaXNhdGlvbiBwYXIgc3Vic3RpdHV0aW9uXCIsIFBoaWxpcHBlIFBvcnRpZXIgY2VsdWkgZGUgXCJzw6ljdWxhcmlzYXRpb24gb3J0aG9kb3hlXCIgKGluIDxlbT5DaGFybGVzIFRheWxvcjogUmVsaWdpb24gZXQgc8OpY3VsYXJpc2F0aW9uPC9lbT4sIHNvdXMgZGlyLiBTLiBUYXVzc2lnLCBQYXJpcywgQ05SUyDDiWQsIDIwMTQpLjxzdXA+WzxhIGhyZWY9XCIvYXJ0aWNsZS9wb3VycXVvaS1mcmFuY2UtdmV1dC1jcm9pcmUtZmluLWNocmlzdGlhbmlzbWUjMVwiPnJldG91csKg4oaRPC9hPl08L3N1cD48YnIgaWQ9XCJub3RlMlwiPjIuIENoYXJsZXMgVGF5bG9yLCA8ZW0+TOKAmcOCZ2UgU8OpY3VsaWVyPC9lbT4sIFNldWlsLCAyMDExLCBwLjU2LjxzdXA+WzxhIGhyZWY9XCIvYXJ0aWNsZS9wb3VycXVvaS1mcmFuY2UtdmV1dC1jcm9pcmUtZmluLWNocmlzdGlhbmlzbWUjMlwiPnJldG91csKg4oaRPC9hPl08L3N1cD48YnIgaWQ9XCJub3RlM1wiPjMuIEphbWVzIEsuIEEuIFNtaXRoLCA8ZW0+SG93IChub3QpIHRvIGJlIHNlY3VsYXI8L2VtPiwgcC4gNzcuPHN1cD5bPGEgaHJlZj1cIi9hcnRpY2xlL3BvdXJxdW9pLWZyYW5jZS12ZXV0LWNyb2lyZS1maW4tY2hyaXN0aWFuaXNtZSMzXCI+cmV0b3VywqDihpE8L2E+XTwvc3VwPjxiciBpZD1cIm5vdGU0XCI+NC4gU3lsdmllIFRhdXNzaWcsIDxlbT5DaGFybGVzIFRheWxvcjogUmVsaWdpb24gZXQgc8OpY3VsYXJpc2F0aW9uPC9lbT4sIHNvdXMgZGlyLiBTLiBUYXVzc2lnLCBQYXJpcywgQ05SUyDDiWQsIDIwMTQsIHAuIDM5LjxzdXA+WzxhIGhyZWY9XCIvYXJ0aWNsZS9wb3VycXVvaS1mcmFuY2UtdmV1dC1jcm9pcmUtZmluLWNocmlzdGlhbmlzbWUjNFwiPnJldG91csKg4oaRPC9hPl08L3N1cD48YnIgaWQ9XCJub3RlNVwiPjUuIFAuIEJFUkdFUiwgPGVtPlRoZSBEZXNlY3VsYXJpemF0aW9uIG9mIHRoZSBXb3JsZDogUmVzdXJnZW50IFJlbGlnaW9uIGFuZCBXb3JsZCBQb2xpdGljczwvZW0+LCBXYXNoaW5ndG9uLCBELkMuLCBHcmFuZCBSYXBpZHMsIE1pY2gsIFdpbGxpYW0gQiBFZXJkbWFucyBQdWJsaXNoaW5nIENvLCAxOTk5LCBwLiAyLjxzdXA+WzxhIGhyZWY9XCIvYXJ0aWNsZS9wb3VycXVvaS1mcmFuY2UtdmV1dC1jcm9pcmUtZmluLWNocmlzdGlhbmlzbWUjNVwiPnJldG91csKg4oaRPC9hPl08L3N1cD48YnIgaWQ9XCJub3RlNlwiPjYuIE1hcmNlbCBHYXVjaGV0LCBcIkxlIGTDqXNlbmNoYW50ZW1lbnQgZMOpc2VuY2hhbnTDqVwiIGluIDxlbT5DaGFybGVzIFRheWxvcjogUmVsaWdpb24gZXQgc8OpY3VsYXJpc2F0aW9uPC9lbT4sIHNvdXMgZGlyLiBTLiBUYXVzc2lnLCBwLiA4MC48c3VwPls8YSBocmVmPVwiL2FydGljbGUvcG91cnF1b2ktZnJhbmNlLXZldXQtY3JvaXJlLWZpbi1jaHJpc3RpYW5pc21lIzZcIj5yZXRvdXLCoOKGkTwvYT5dPC9zdXA+PGJyIGlkPVwibm90ZTdcIj43LiBDaGFudGFsIERlbHNvbCwgPGVtPkxhIGZpbiBkZSBsYSBjaHLDqXRpZW50w6k8L2VtPiwgcC4gMzQuPHN1cD5bPGEgaHJlZj1cIi9hcnRpY2xlL3BvdXJxdW9pLWZyYW5jZS12ZXV0LWNyb2lyZS1maW4tY2hyaXN0aWFuaXNtZSM3XCI+cmV0b3VywqDihpE8L2E+XTwvc3VwPjxiciBpZD1cIm5vdGU4XCI+OC4gSuKAmWFpbWUgY2UgZ2VucmUgZGUgZGlzY3Vzc2lvbiBzY2llbmNlIHZzIGZvaSBhdmVjIG1lcyBhbWlzIG5vbi1jaHLDqXRpZW5zLiBKZSBjcm9pcyBxdeKAmWlsIG7igJllc3QgcGFzIHZhaW4gZGUgZMOpbW9udHJlciBxdWUgbGEgZMOpbWFyY2hlIHNjaWVudGlmaXF1ZSBldCBsYSBmb2kgZW4gSsOpc3VzIG9udCBsYSBtw6ptZSBvcmlnaW5lLCBjZWxsZSBkZSBsYSByw6l2w6lsYXRpb24gZGVzIMOJY3JpdHVyZXMuIENlcGVuZGFudCwgauKAmWFpIGdhZ27DqSBkZSBub21icmV1eCBkZSBjZXMgZMOpYmF0cywgZXQgY2UgbuKAmWVzdCBwYXMgY2VsYSBxdWkgYSBjb25kdWl0IGNlcyBhbWlzIHJhaXNvbm5hYmxlcyBhdSBwaWVkIGRlIGxhIGNyb2l4LjxzdXA+WzxhIGhyZWY9XCIvYXJ0aWNsZS9wb3VycXVvaS1mcmFuY2UtdmV1dC1jcm9pcmUtZmluLWNocmlzdGlhbmlzbWUjOFwiPnJldG91csKg4oaRPC9hPl08L3N1cD48L3N1Yj5gIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiUmFwaGHDq2wgQ2hhcnJpZXIiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvcmFwaGFlbF9jaGFycmllcl8wM19jb3BpZV8xX3NjYWxlZF9lMTY1NzgwMTI1Nzc4N19mOTM1OTk0MDM4LmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L3Yyb3NrX1VsX1JfRXBfU2lyanNjX3Vuc3BsYXNoXzI3NWQxN2UzMTYuanBnIiwiaGVpZ2h0Ijo4NTQsIndpZHRoIjoxMjgwLCJhbHRlcm5hdGl2ZVRleHQiOiIiLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiJBcG9sb2fDqXRpcXVlIGV0IHZpc2lvbiBkdSBtb25kZSJ9LHsibmFtZSI6IlZpc2lvbiBjaHLDqXRpZW5uZSBkdSBtb25kZSJ9LHsibmFtZSI6IlJlbGlnaW9ucyBkdSBtb25kZSJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiQmVhdWNvdXAgZGUgZnJhbsOnYWlzIGF0aMOpZXMgdm9pZW50IGxhIHJlbGlnaW9uLCBlbiBwYXJ0aWN1bGllciBsZSBjaHJpc3RpYW5pc21lLCBjb21tZSBsZSByw6lzaWR1IGFuYWNocm9uaXF1ZSBk4oCZdW4gYXV0cmUgdGVtcHMuIExlcyBjaHLDqXRpZW5zIHNvbnQgZGVzIGJ1Z3MgZGUgbGEgbWF0cmljZSBkZSBsYSBtb2Rlcm5pdMOpLiBDb21tZW50IHBldXQtb24gZW5jb3JlIGNyb2lyZSBlbiBEaWV1IGF1am91cmQnaHVpPyBD4oCZZXN0IGTigJl1biBhdXRyZSB0ZW1wcy4ifV19LHsiaWQiOiIzNDkxIiwidGl0bGUiOiJDb21tZW50IGZhaXJlIGZhY2UgYXV4IMOpcHJldXZlcyBhdmVjIGZvaT8gKDIgQ29yaW50aGllbnMgMTIuNi0xMCkiLCJzbHVnIjoiZm9ydGlmaWUtc291ZmZyYW5jZS1lY2hhcmRlLWRlLWdyYWNlIiwidHlwZSI6InBvZGNhc3QiLCJwdWJsaXNoZWRfYXQiOiIyMDI0LTA5LTExVDA0OjMwOjAwLjAwMFoiLCJib2R5IjoiSuKAmWFpIGV1IGxlIHByaXZpbMOoZ2UgZGUgcHLDqmNoZXIgYXUgY29uZ3LDqHMgamV1bmVzc2UgZGUgUMOicXVlcyAyMDIzIMOgIGzigJlJQkcgc3VyIGxlIHN1amV0IGRlIGxhIHNvdWZmcmFuY2UuIErigJlhaSBjaG9pc2kgbGUgdGV4dGUgbWVydmVpbGxldXggZGUgMiBDb3JpbnRoaWVucyAxMi42LTEwLlxuXG5KZSB2b3VzIHByb3Bvc2UgZGUgZMOpY291dnJpciBpY2kgbGUgbWVzc2FnZS4gTuKAmWjDqXNpdGV6IHBhcyDDoCBsZSBwYXJ0YWdlciDDoCBkZXMgcGVyc29ubmVzIHF1aSBvbnQgYmVzb2luIGTigJnDqnRyZSBmb3J0aWZpw6llcyBwYXIgbGEgZ3LDomNlIGRlIErDqXN1cyBkYW5zIGxldXIgc291ZmZyYW5jZS5cblxuXG4tLS1cblxuIyMgVHJhbnNjcmlwdGlvbiBkZSBsYSBwcsOpZGljYXRpb25cblxuPHN1Yj48ZW0+Q2V0dGUgdHJhbnNjcmlwdGlvbiBhIMOpdMOpIGfDqW7DqXLDqWUgYXV0b21hdGlxdWVtZW50LCBu4oCZaMOpc2l0ZXogcGFzIMOgIG5vdXMgc2lnbmFsZXIgdG91dGUgZXJyZXVyIG91IGluY29ow6lyZW5jZSBxdWkgbm91cyBhdXJhaXQgw6ljaGFwcMOpLjwvc3ViPjwvZW0+XG5cbkplIHZvdWRyYWlzIGNvbW1lbmNlciBwYXIgZmFpcmUgdHJvaXMgYWZmaXJtYXRpb25zIMOgIHByb3BvcyBkZSBub3VzIHRvdXMgaWNpLiBOb3VzIGF2b25zIHRyb2lzIGNob3NlcyBlbiBjb21tdW46IHByZW1pw6hyZW1lbnQsIG5vdXMgYXZvbnMgdG91cyBzb3VmZmVydCBkYW5zIG5vcyB2aWVzLCDDoCBkZXMgZGVncsOpcyBkaWZmw6lyZW50cyBjZXJ0ZXMsIG1haXMgY+KAmWVzdCB1bmUgZXhww6lyaWVuY2UgcXVlIG5vdXMgcGFydGFnZW9ucyB0b3VzLiBEZXV4acOobWVtZW50LCBub3VzIHNvdWZmcm9ucyB0b3VzIGVuIGNlIG1vbWVudCwgZOKAmXVuZSBtYW5pw6hyZSBvdSBk4oCZdW5lIGF1dHJlLCBlbmNvcmUgdW5lIGZvaXMgw6AgZGVzIGRlZ3LDqXMgdmFyacOpcy4gTm91cyBhdm9ucyB0b3VzIGRhbnMgbm9zIHZpZXMgZGVzIHNpdHVhdGlvbnMgZGlmZmljaWxlcywgZGVzIMOpcHJldXZlcyBxdWkgbm91cyBpbnF1acOodGVudCBldCBub3VzIGZvbnQgc291ZmZyaXIuIEV0IHRyb2lzacOobWVtZW50LCBtYXV2YWlzZSBub3V2ZWxsZSwgbm91cyBhbGxvbnMgZW5jb3JlIHNvdWZmcmlyLiBIw6lsYXMsIGNlIHNlcmEgcHJvYmFibGVtZW50IHBsdXMgc3VycHJlbmFudCwgcGx1cyBkdXIsIHBsdXMgaW5qdXN0ZSwgcGx1cyBsb25nIGV0IHBsdXMgcMOpbmlibGUgcXVlIGNlIHF1ZSBub3VzIHBvdXZvbnMgaW1hZ2luZXIuXG5cbkxhIHF1ZXN0aW9uIG7igJllc3QgcGFzIGRlIHNhdm9pciAqc2kqIHZvdXMgYWxsZXogc291ZmZyaXIsIG1haXMgcGx1dMO0dCAqcXVhbmQqIGV0ICpjb21tZW50KiB2b3VzIGFsbGV6IHNvdWZmcmlyLiBFdCBlbiBwbHVzIGRlIGNlcyBncmFuZGVzIMOpcHJldXZlcyBkZSBsYSB2aWUgcXVlIG5vdXMgdHJhdmVyc29ucywgaWwgeSBhIHRvdXRlcyBsZXMgcGV0aXRlcyBzb3VmZnJhbmNlcyBxdW90aWRpZW5uZXM6IGxlcyBtb3F1ZXJpZXMsIGxlIHJlamV0LCBsZXMgbWFsYWRpZXMsIGxlcyDDqWNoZWNzLCBsZXMgdHJhaGlzb25zLCBsZXMgaW5jZXJ0aXR1ZGVzLCBsZXMgZMOpY2VwdGlvbnMsIGV0IGrigJllbiBwYXNzZS4gQ29tbWVudCB0cmF2ZXJzZXIgdG91dGVzIGNlcyBzb3VmZnJhbmNlcyBhdSBjb3VycyBkZSBub3RyZSB2aWU/XG5cbkNlIHF1ZSBqZSBwZXV4IHZvdXMgYWZmaXJtZXIsIGPigJllc3QgcXVlIHNpIG5vdHJlIGZvaSBuZSBz4oCZZW5yYWNpbmUgcGFzIGRhbnMgdW5lIGNvbm5haXNzYW5jZSBwZXJzb25uZWxsZSBldCBpbnRpbWUgZGUgbGEgcGVyc29ubmUgZGUgSsOpc3VzLUNocmlzdCwgY2VzIMOpcHJldXZlcyByaXNxdWVudCBmb3J0IGRlIG5vdXMgw6lsb2lnbmVyIGRlIGxhIGZvaS5cblxuSMOpbGFzLCBq4oCZYWkgdnUgcGx1c2lldXJzIHBlcnNvbm5lcywgZHVyYW50IG1vbiBtaW5pc3TDqHJlLCBxdWkgZmFjZSDDoCBkZXMgw6lwcmV1dmVzIG9udCBjb21tZW5jw6kgw6AgZG91dGVyIGRlIGxldXIgY29uZmlhbmNlIGVuIErDqXN1cy1DaHJpc3QsIHB1aXMgw6AgcmVtZXR0cmUgZW4gcXVlc3Rpb24gbGEgZmlhYmlsaXTDqSBkdSBTZWlnbmV1ciwgZXQgZmluYWxlbWVudCwgZWxsZXMgbOKAmW9udCByZWpldMOpLlxuXG5Ob3VzIGF2b25zIGRvbmMgdG91cyBiZXNvaW4sIG1lIHNlbWJsZS10LWlsLCBk4oCZdW5lIHNvbGlkZSB0aMOpb2xvZ2llIGRlIGxhIHNvdWZmcmFuY2UsIGTigJl1bmUgdmlzaW9uIGJpYmxpcXVlIGR1IHN1amV0LCBwb3VyIG5vdXMgcGVybWV0dHJlIGRlIHRlbmlyIGZlcm1lIGRhbnMgbGVzIHRlbXBzIGRpZmZpY2lsZXMuIEV0IGNlbHVpIHF1aSB2YSBub3VzIGVuc2VpZ25lciBjZSBtYXRpbiBhdmFpdCwgcG91ciBhaW5zaSBkaXJlLCB1biBkb2N0b3JhdCBlbiBzb3VmZnJhbmNlLiBKZSBwZW5zZSBxdeKAmWlsIMOpdGFpdCBjZWludHVyZSBub2lyZSB0cm9pc2nDqG1lIGRhbiwgY+KAmcOpdGFpdCB1biBzcMOpY2lhbGlzdGUuIElsIHPigJlhZ2l0IGRlIGzigJlhcMO0dHJlIFBhdWwsIHF1aSB2YSBub3VzIGVuc2VpZ25lciBhdWpvdXJk4oCZaHVpLiBNYSBwcmnDqHJlIGVzdCBxdWUsIHNpIHZvdXMgw6p0ZXMgY2UgbWF0aW4gYXZlYyB1biBjxZN1ciBsb3VyZCwgZMOpY291cmFnw6ksIGxhIFBhcm9sZSBkZSBEaWV1IGV0IHNvbiBFc3ByaXQgcHVpc3NlbnQgdmVuaXIgc+KAmWFkcmVzc2VyIMOgIHZvdXMgcGVyc29ubmVsbGVtZW50IHBvdXIgdm91cyBleGhvcnRlciBkYW5zIHZvdHJlIGZvaS5cblxuSmUgdm91cyBpbnZpdGUgw6Agb3V2cmlyIHZvcyBCaWJsZXMgYXZlYyBtb2kuIE5vdXMgYWxsb25zIGVuc2VtYmxlIGRhbnMgbGEgZGV1eGnDqG1lIGxldHRyZSBkZSBQYXVsIGF1eCBDb3JpbnRoaWVucywgYXUgY2hhcGl0cmUgMTIsIGV0IGplIHZhaXMgbGlyZSBsZXMgdmVyc2V0cyA2IMOgIDEwLiBEb25jLCAyIENvcmludGhpZW5zIDEyLCB2ZXJzZXRzIDYgw6AgMTAuXG5cblwiU2kgamUgdm91bGFpcyBtZSBnbG9yaWZpZXIsIGplIG5lIHNlcmFpcyBwYXMgaW5zZW5zw6ksIGNhciBqZSBkaXJhaXMgbGEgdsOpcml0w6kuIE1haXMgamUgbSdlbiBhYnN0aWVucywgZGUgcGV1ciBxdWUgcXVlbHF1J3VuIG5lIG0nZXN0aW1lIGF1LWRlc3N1cyBkZSBjZSBxdSdpbCB2b2l0IG91IGVudGVuZCBkZSBtb2kuIMOAIGNhdXNlIGRlIGwnZXhjZWxsZW5jZSBkZSBjZXMgcsOpdsOpbGF0aW9ucywgZXQgcG91ciBxdWUgamUgbmUgc29pcyBwYXMgZW5mbMOpIGQnb3JndWVpbCwgaWwgbSdhIMOpdMOpIG1pcyB1bmUgw6ljaGFyZGUgZGFucyBsYSBjaGFpciwgdW4gYW5nZSBkZSBTYXRhbiBwb3VyIG1lIHNvdWZmbGV0ZXIsIGFmaW4gcXVlIGplIG5lIHNvaXMgcGFzIGVuZmzDqSBkJ29yZ3VlaWwuIFRyb2lzIGZvaXMgaidhaSBzdXBwbGnDqSBsZSBTZWlnbmV1ciBkZSBsJ8OpbG9pZ25lciBkZSBtb2ksIGV0IGlsIG0nYSBkaXQ6IOKAmE1hIGdyw6JjZSB0ZSBzdWZmaXQsIGNhciBtYSBwdWlzc2FuY2Ugc+KAmWFjY29tcGxpdCBkYW5zIGxhIGZhaWJsZXNzZS7igJkgSmUgbWUgZ2xvcmlmaWVyYWkgZG9uYyBiaWVuIHBsdXMgdm9sb250aWVycyBkZSBtZXMgZmFpYmxlc3NlcywgYWZpbiBxdWUgbGEgcHVpc3NhbmNlIGRlIENocmlzdCByZXBvc2Ugc3VyIG1vaS4gQ+KAmWVzdCBwb3VycXVvaSBqZSBtZSBwbGFpcyBkYW5zIGxlcyBmYWlibGVzc2VzLCBkYW5zIGxlcyBvdXRyYWdlcywgZGFucyBsZXMgcHJpdmF0aW9ucywgZGFucyBsZXMgcGVyc8OpY3V0aW9ucywgZGFucyBsZXMgYW5nb2lzc2VzIHBvdXIgQ2hyaXN0LiBDYXIgcXVhbmQgamUgc3VpcyBmYWlibGUsIGMnZXN0IGFsb3JzIHF1ZSBqZSBzdWlzIGZvcnQuXCJcblxuTOKAmcOpZ2xpc2UgZGUgQ29yaW50aGUgw6l0YWl0IGVuIHByb2llIMOgIGRlcyBmYXV4IGFww7R0cmVzIHF1aSBsYSBzw6lkdWlzYWllbnQgcG91ciBsYSBkw6l0b3VybmVyIGRlIGwnw4l2YW5naWxlLiBDZXMgcGVyc29ubmVzIG1ldHRhaWVudCBlbiBhdmFudCBsZXVyIHBlZGlncmVlLCBsZXVycyBkb25zLCBsZXVycyBjYXBhY2l0w6lzIGV4dHJhb3JkaW5haXJlcywgZGVzIHBzZXVkby1yw6l2w6lsYXRpb25zIHF14oCZZWxsZXMgYXVyYWllbnQgcmXDp3VlcyBwb3VyIGTDqXRvdXJuZXIgbGVzIENvcmludGhpZW5zIGRlIGzigJnDiXZhbmdpbGUgcXVlIHByw6pjaGFpdCBs4oCZYXDDtHRyZSBQYXVsLiBJbHMgbmUgbWFucXVhaWVudCBwYXMgdW5lIG9jY2FzaW9uIGRlIGxlIGTDqW5pZ3JlciBldCBkZSBjaGVyY2hlciDDoCBsZSByYWJhaXNzZXIuXG5cblBhdWwgcmVmdXNlIGNhdMOpZ29yaXF1ZW1lbnQgZGUgcmVudHJlciBkYW5zIGNlIGTDqWJhdCBwdcOpcmlsIGRlIHN1cmVuY2jDqHJlLCBjYXIgaWwgZGl0IHF14oCZaWwgbuKAmWEgcmllbiDDoCBmYWlyZSB2YWxvaXIgcXVpIHZpZW5kcmFpdCBkZSBsdWktbcOqbWUuIElsIG7igJlhIHJpZW4gw6AgcHJvdXZlciBhdXggYXV0cmVzIGV0IGlsIG5lIHZldXQgcGFzIHF1ZSBsZXMgQ29yaW50aGllbnMgbOKAmWVzdGltZW50IGF1LWRlbMOgIGRlIGNlIHF14oCZaWwgZXN0IHLDqWVsbGVtZW50LiBQb3VydGFudCwgUGF1bCBkaXQsIGF1IGTDqWJ1dCBkdSBjaGFwaXRyZSAxMiwgcXXigJlpbCBhIHJlw6d1IHVuZSBncsOiY2UgZXhjZXB0aW9ubmVsbGUsIHVuZSBncsOiY2UgaW5jcm95YWJsZS4gUXVhdG9yemUgYW5zIHBsdXMgdMO0dCwgbGUgU2VpZ25ldXIgbHVpIGEgZG9ubsOpIHVuZSB2aXNpb24gZHUgY2llbCwgdW5lIHZpc2lvbiBkdSBwYXJhZGlzLCBxdWkgYSBub3Vycmkgc29uIMOJdmFuZ2lsZSwgc29uIGVuc2VpZ25lbWVudCBldCBzb24gbWluaXN0w6hyZS4gQ2VsYSBsdWkgYSBwZXJtaXMgZGUgZMOpdmVsb3BwZXIgc29uIHNlcnZpY2UgYXVwcsOocyBkZSBs4oCZw6lnbGlzZS5cblxuTWFpcywgaMOpbGFzLCBzdWl0ZSDDoCBjZXR0ZSByw6l2w6lsYXRpb24sIFBhdWwgc291ZmZyZS4gSWwgc2VtYmxlIHF1ZSBjZXR0ZSBzb3VmZnJhbmNlIHNvaXQgYXNzZXogc8OpdsOocmUuIENlIHF1ZSBqZSB2b3VzIHByb3Bvc2UgYXVqb3VyZOKAmWh1aSwgY+KAmWVzdCBkZSBjaGVtaW5lciBhdmVjIGx1aSBlbiBub3VzIHBvc2FudCBxdWF0cmUgcXVlc3Rpb25zIHBvdXIgY29tcHJlbmRyZSBsYSBwbGFjZSBkZSBjZXR0ZSDDqXByZXV2ZSwgZGUgY2V0dGUgc291ZmZyYW5jZSwgZGFucyBzYSB2aWUuXG5cbiMjIDEuIERlIHF1b2kgUGF1bCBzb3VmZnJlLXQtaWw/IFxuXG5MZSB0ZXh0ZSBub3VzIGRpdDogXCJJbCBt4oCZYSDDqXTDqSBtaXMgdW5lIMOpY2hhcmRlIGRhbnMgbWEgY2hhaXIsIHVuIGFuZ2UgZGUgU2F0YW4gcG91ciBtZSBzb3VmZmxldGVyLlwiIEwnw6ljaGFyZGUgZXN0IHVuZSBtw6l0YXBob3JlLCB1bmUgaW1hZ2UuIEJlYXVjb3VwIGTigJllbmNyZSBhIGNvdWzDqSBldCBkZSBub21icmV1c2VzIGh5cG90aMOoc2VzIG9udCDDqXTDqSBmb3JtdWzDqWVzIHBvdXIgdGVudGVyIGTigJlpZGVudGlmaWVyIGNldHRlIMOpY2hhcmRlLCBsYSBuYXR1cmUgZGUgc2Egc291ZmZyYW5jZS4gVHLDqHMgcHJvYmFibGVtZW50IHVuZSBtYWxhZGllLCBtYWlzIGTigJlhdXRyZXMgaHlwb3Row6hzZXMgc29udCBhdXNzaSBhdmFuY8OpZXMuIENlcGVuZGFudCwgamUgY3JvaXMgcXVlIGNoZXJjaGVyIMOgIHRvdXQgcHJpeCDDoCBpZGVudGlmaWVyIGRlIHF1ZWwgbWFsIHNvdWZmcmFpdCBQYXVsLCBj4oCZZXN0IHBhc3NlciDDoCBjw7R0w6kgZGUgbOKAmWVmZmV0IHF14oCZaWwgdmV1dCBwcm9kdWlyZS4gQ+KAmWVzdCBtYW5xdWVyIHNvbiBpbnRlbnRpb24uXG5cbkVuIGNob2lzaXNzYW50IGRlIHJlc3RlciB2YWd1ZSBldCBk4oCZdXRpbGlzZXIgdW5lIG3DqXRhcGhvcmUsIFBhdWwgcGVybWV0IMOgIHNlcyBsZWN0ZXVycyBkZSBz4oCZaWRlbnRpZmllciDDoCBsdWkgZXQgZGUgZmFpcmUgdW4gcGFyYWxsw6hsZSBhdmVjIGxldXJzIHByb3ByZXMgw6lwcmV1dmVzLCBhdmVjIGxldXIgdmllLiBDZSBxdWUgbOKAmW9uIHNhaXQsIGPigJllc3QgcXVlIGNldHRlIMOpcHJldXZlIGxlIGZhaXQgc291ZmZyaXIgZGVwdWlzIDE0IGFucy4gUXVhdG9yemUgYW5zIGRlIHNvdWZmcmFuY2UsIHZvdXMgaW1hZ2luZXo/IExhIG5hdHVyZSBkZSBjZXR0ZSDDqXByZXV2ZSBlc3QgdnJhaW1lbnQgbXlzdMOpcmlldXNlLCBlbGxlIG5vdXMgZMOpcGFzc2UuIEV0IFBhdWwgcsOpdW5pdCBs4oCZaW1wZW5zYWJsZSBlbiB1bmUgc2V1bGUgcGhyYXNlOiBcIklsIG3igJlhIMOpdMOpIG1pc1wiIOKAkyBzb3VzLWVudGVuZHUgcGFyIGxlIFNlaWduZXVyIOKAkyBcInVuZSDDqWNoYXJkZVwiLCBxdWUgUGF1bCBxdWFsaWZpZSBjb21tZSDDqXRhbnQgdW4gYW5nZSBkZSBTYXRhbiBwb3VyIGxlIHNvdWZmbGV0ZXIuIElsIHLDqXVuaXQgbOKAmWltcGVuc2FibGU6IFNhdGFuIGVzdCBsYSBjYXVzZSBpbW3DqWRpYXRlIGRlIGzigJnDqXByZXV2ZSBkZSBQYXVsLCBj4oCZZXN0IMOgIGNhdXNlIGRlIFNhdGFuIHF14oCZaWwgc291ZmZyZSBkYW5zIHNhIGNoYWlyLCBtYWlzIFNhdGFuIGVzdCB1dGlsaXPDqSBwYXIgRGlldS4gUXVlbCBteXN0w6hyZSFcblxuQ2VsYSBub3VzIHRyb3VibGUsIG4nZXN0LWNlIHBhcz8gQ+KAmWVzdCBwZXJ0dXJiYW50IHF1ZSBEaWV1IGluc3RydW1lbnRhbGlzZSBTYXRhbiBkZSBjZXR0ZSBtYW5pw6hyZSwgZXQgY2VsYSBub3VzIGTDqXJhbmdlLCBjZSBxdWkgZXN0IHRvdXQgw6AgZmFpdCBub3JtYWwuIE5vdXMgZGV2b25zIGFjY2VwdGVyIHF14oCZaWwgeSBhIHVuIG15c3TDqHJlLCB1bmUgdGVuc2lvbiBxdWUgUGF1bCBub3VzIHByw6lzZW50ZSBpY2kuIERpZXUgYSBkZXMgcmFpc29ucyBxdWkgbm91cyDDqWNoYXBwZW50IGTigJl1dGlsaXNlciBTYXRhbiBwb3VyIGFjY29tcGxpciB1biBiaWVuIHN1cMOpcmlldXIuXG5cbk1haXMgcXVhbmQgb24gcHJlbmQgdW4gcGV1IGRlIHJlY3VsLCBub3VzIGxlIHNhdm9ucyBkw6lqw6AuIE5vdXMgdm95b25zIGNlbGEgZGUgbWFuacOocmUgYXNzZXogw6l2aWRlbnRlIGRhbnMgbGEgQmlibGUuIEzigJlpbGx1c3RyYXRpb24gdWx0aW1lIGRlIGNlIG15c3TDqHJlLCBj4oCZZXN0IGxhIGNyb2l4LiBD4oCZZXN0IGJpZW4gU2F0YW4gcXVpIGVzdCBlbnRyw6kgZGFucyBsZSBjxZN1ciBkZSBKdWRhcyBwb3VyIHF14oCZaWwgdHJhaGlzc2UgSsOpc3VzIGV0IHF14oCZSWwgc29pdCBsaXZyw6kgw6AgbGEgY3JvaXguIFBvdXJ0YW50LCBsYSBtb3J0IGRlIErDqXN1cyBmYWl0IHBhcnRpZSBkdSBwbGFuIHNvdXZlcmFpbiBkZSBEaWV1IHBvdXIgbm91cyBzYXV2ZXIuXG5cbkFpbnNpLCBvdWksIFNhdGFuIHBldXQgbm91cyBhdHRhcXVlciBkaXJlY3RlbWVudCBwb3VyIG5vdXMgZmFpcmUgc291ZmZyaXIsIG1haXMgRGlldSBzYWl0IGzigJl1dGlsaXNlciBteXN0w6lyaWV1c2VtZW50IHBvdXIgdW4gYmllbiBwbHVzIGdyYW5kLlxuXG5FdCBjb21tZW50IFBhdWwgcsOpYWdpdC1pbCBmYWNlIMOgIGNldHRlIMOpcHJldXZlPyBQYXVsIG7igJllc3QgcGFzIHVuIG1hc29jaGlzdGUuIElsIG7igJllc3QgcGFzIHVuIHN0b8OvY2llbiBxdWkgZGlyYWl0OiBcIsOHYSBuZSBtZSBmYWl0IHJpZW4uXCIgSWwgbuKAmWVzdCBwYXMgbm9uIHBsdXMgYm91ZGRoaXN0ZSwgcHLDqXRlbmRhbnQgcXVlIGxhIHNvdWZmcmFuY2UgbuKAmWVzdCBxdeKAmXVuZSBpbGx1c2lvbi4gUGF1bCBuZSBkaXQgcGFzOiBcIlZhcy15LCBmYWlzLW1vaSBtYWwsIGplIG7igJlhaSBwYXMgcGV1ciBkZSBzb3VmZnJpciwgamUgc3VpcyB1biBkdXIsIHJlZ2FyZGUgY29tbWUgamUgc3VpcyBmb3J0LlwiIENlIG7igJllc3QgcGFzIGNvbW1lIGRhbnMgbGVzIHNhbGxlcyBkZSBmaXRuZXNzIG/DuSBs4oCZb24gdm9pdCBpbnNjcml0IHN1ciBsZXMgbXVyczogKk5vIHBhaW4sIG5vIGdhaW4qIChcIlBhcyBkZSBkb3VsZXVyLCBwYXMgZGUgcsOpc3VsdGF0XCIpLiBOb24sIGF1IGNvbnRyYWlyZSwgUGF1bCBhIHBsYWlkw6kgcG91ciBxdWUgY2V0dGUgc291ZmZyYW5jZSBjZXNzZS4gSWwgZGl0OiBcIlRyb2lzIGZvaXMgauKAmWFpIHN1cHBsacOpIGxlIFNlaWduZXVyLlwiIElsIGzigJlhIHN1cHBsacOpLCBldCBj4oCZZXN0IHRvdGFsZW1lbnQgbMOpZ2l0aW1lIGRlIHByaWVyIHBvdXIgcXVlIGzigJnDqXByZXV2ZSBz4oCZYXJyw6p0ZS4gRGlldSBub3VzIGVuY291cmFnZSDDoCBsZSBmYWlyZS4gTmUgcGFzIHByaWVyIGFpbnNpIHJldmllbmRyYWl0IMOgIGNyb2lyZSBxdWUgRGlldSBu4oCZZXN0IHBhcyBjYXBhYmxlIGRlIG5vdXMgZMOpbGl2cmVyLCBxdWUgRGlldSBu4oCZZXN0IHBhcyBEaWV1LlxuXG5D4oCZZXN0IGRvbmMgdG91dCDDoCBmYWl0IGzDqWdpdGltZSBkZSBwcmllciBwb3VyIHF1ZSBs4oCZw6lwcmV1dmUgc+KAmWFycsOqdGUuIEV0IGJpZW4gc291dmVudCwgRGlldSByw6lwb25kOiBcIk91aSwgamUgdGUgZMOpbGl2cmUuXCIgUGFyZm9pcywgY2VwZW5kYW50LCBpbCBkaXQ6IFwiTm9uLlwiIERpZXUgcsOpcG9uZCB0b3Vqb3VycyDDoCBub3MgcHJpw6hyZXMsIG1haXMgcGFzIHRvdWpvdXJzIGRlIGxhIG1hbmnDqHJlIHF1ZSBub3VzIHNvdWhhaXRvbnMuIEZyw6hyZXMgZXQgc8WTdXJzLCBzaSBsZSBTZWlnbmV1ciBuZSByZXRpcmUgcGFzIGzigJnDqWNoYXJkZSwgY2UgbuKAmWVzdCBwYXMgcGFyY2UgcXXigJlpbCBuZSBub3VzIMOpY291dGUgcGFzLCBjZSBu4oCZZXN0IHBhcyBwYXJjZSBxdeKAmWlsIG5lIG5vdXMgYWltZSBwYXMsIG5pIHBhcmNlIHF14oCZaWwgbuKAmWVzdCBwYXMgYXNzZXogcHVpc3NhbnQgcG91ciBub3VzIGTDqWxpdnJlci4gRGlldSDDqWNvdXRlIHRvdWpvdXJzIG5vcyBwcmnDqHJlcywgbWFpcyBpbCByw6lwb25kIHRvdWpvdXJzIHNlbG9uIHNhIHZvbG9udMOpLiBTJ2lsIGp1Z2UgcXUnaWwgZXN0IGF2YW50YWdldXggcG91ciBub3VzIGRlIHJldGlyZXIgbOKAmcOpY2hhcmRlLCBpbCBsZSBmZXJhLiBTJ2lsIGVzdGltZSBxdWUgbm9uLCBpbCBuZSBsZSBmZXJhIHBhcy4gRW4gY2Ugc2VucywgUGF1bCBlc3QgcG91ciBub3VzIHVuIG1vZMOobGUgZGUgcHJpw6hyZSBwYXIgbGEgZm9pLiBJbCBkaXQ6IFwiVHJvaXMgZm9pcyBq4oCZYWkgc3VwcGxpw6kgbGUgU2VpZ25ldXIuXCJcblxuSmUgbmUgY3JvaXMgcGFzIHF1ZSBjZSBzb2l0IHVuZSByw6hnbGUgZGUgdHJvaXMsIGNvbW1lIHNpIG5vdXMgYXZpb25zIGxlIGRyb2l0IGRlIGRlbWFuZGVyIHRyb2lzIGZvaXMgZXQgcXXigJnDoCBsYSBxdWF0cmnDqG1lLCBjJ8OpdGFpdCB1bmUgZGVtYW5kZSBkZSB0cm9wLCBvdSBxdWUgZGV1eCBmb2lzIG5lIHNlcmFpdCBwYXMgc3VmZmlzYW50LiBDZSBuJ2VzdCBwYXMgdW5lIGZvcm11bGUgbWFnaXF1ZSBxdSdpbCBmYXVkcmFpdCByw6lww6l0ZXIgdHJvaXMgZm9pcywgbm9uLCBwYXMgZHUgdG91dC4gTWFpcyBjZWxhIG1vbnRyZSBxdWUgUGF1bCBuZSB2b2l0IHBhcyBsYSBwcmnDqHJlIGNvbW1lIHVuIG1veWVuIGRlIHRvcmRyZSBsZSBicmFzIGRlIERpZXUsIG91IGRlIGZhaXJlIHBsaWVyIHNhIHZvbG9udMOpIMOgIGxhIG7DtHRyZS4gSWwgbGEgcGVyw6dvaXQgcGx1dMO0dCBjb21tZSB1biBtb3llbiBkZSBkaXNjZXJuZXIgbGEgdm9sb250w6kgZGUgRGlldSBldCBk4oCZYXBwcmVuZHJlIMOgIGzigJlhY2NlcHRlci5cblxuUGF1bCBhIGNlc3PDqSBkZSBwcmllciBwYXJjZSBxdSdpbCBhIGFjY2VwdMOpIGxhIHLDqXBvbnNlIGR1IFNlaWduZXVyLiBDZSBxdWUgbm91cyBkZXZvbnMgcmVjaGVyY2hlciBkYW5zIGxhIHByacOocmUsIHF1YW5kIG5vdXMgdHJhdmVyc29ucyBkZXMgw6lwcmV1dmVzLCBjZSBu4oCZZXN0IHBhcyBzaW1wbGVtZW50IHF1ZSBEaWV1IG5vdXMgZXhhdWNlLCBtYWlzIHF14oCZaWwgYWNjb21wbGlzc2Ugc2Egdm9sb250w6ksIGNhciBub3VzIGF2b25zIGNvbmZpYW5jZSBlbiBsdWkuIE5vdXMgY3JveW9ucyBxdSdpbCBzYWl0IG1pZXV4IHF1ZSBub3VzIGNlIHF1aSBlc3QgYm9uIHBvdXIgbm91cyBldCBxdWUsIGRhbnMgc2EgZ3LDomNlIHNvdXZlcmFpbmUsIGlsIG5lIG1hbnF1ZXJhIHBhcyBkZSBub3VzIGRvbm5lciBjZSBxdWkgZXN0IGxlIG1laWxsZXVyIHBvdXIgbm91cy5cblxuIyMgMi4gUG91cnF1b2kgUGF1bCBzb3VmZnJlLXQtaWw/IFxuXG5RdWVsbGUgZXN0IGxhIGNhdXNlIGRlIGNldHRlIHNvdWZmcmFuY2U/IElsIHkgYSB1bmUgZG91YmxlIGNhdXNlIHF1aSBub3VzIGVzdCBwcsOpc2VudMOpZSBkYW5zIGxlIHRleHRlLiBEZXJyacOocmUgbCfDqXByZXV2ZSBkZSBsJ8OpY2hhcmRlLCBQYXVsIGNvdXJ0IHVuIGdyYXZlIGRhbmdlci4gUmVnYXJkZXogbGUgdmVyc2V0IDcuIENlIHF1aSBmYWl0IHNvdWZmcmlyIFBhdWwgZXN0IGV4cGxpcXXDqSDDoCB0cmF2ZXJzIGxhIHJhaXNvbiBkZSBjZXR0ZSDDqXByZXV2ZS4gSWwgY29tbWVuY2UgcGFyIGRpcmU6IFwiRXQgcG91ciBxdWUgamUgbmUgc29pcyBwYXMgZW5mbMOpIGQnb3JndWVpbCwgaWwgbSdhIMOpdMOpIG1pcyB1bmUgw6ljaGFyZGUgZGFucyBsYSBjaGFpciwgdW4gYW5nZSBkZSBTYXRhbiBwb3VyIG1lIHNvdWZmbGV0ZXIsIGFmaW4gcXVlIGplIG5lIHNvaXMgcGFzIGVuZmzDqSBkJ29yZ3VlaWwuXCIgUGF1bCByw6lww6h0ZSDDoCBwbHVzaWV1cnMgcmVwcmlzZXMgcXVlIGwnw6ljaGFyZGUgZXN0IGzDoCBwb3VyIHF1J2lsIG5lIHNvaXQgcGFzIGVuZmzDqSBkJ29yZ3VlaWwuXG5cblBvdXJxdW9pIFBhdWwgc291ZmZyZS10LWlsPyBJbCBub3VzIGxlIGRpdCBjbGFpcmVtZW50OiBwYXJjZSBxdSdpbCByaXNxdWUgbOKAmW9yZ3VlaWwuIFBhdWwgcmlzcXVhaXQgZGUgY3JvaXJlIHF1ZSBsYSByw6l1c3NpdGUgZGUgc29uIG1pbmlzdMOocmUgdmVuYWl0IGRlIGx1aSBldCBkZSBz4oCZYXR0cmlidWVyIGxhIGdsb2lyZSBxdWkgcmV2aWVudCDDoCBDaHJpc3Qgc2V1bC4gQyfDqXRhaXQgc29uIGdyYW5kIGRhbmdlci4gSWwgcmlzcXVhaXQgYWluc2kgZGUgcmVzc2VtYmxlciDDoCBjZXMgZmF1eCBhcMO0dHJlcyBxdWkgY2hlcmNoYWllbnQgYXZhbnQgdG91dCDDoCBzZSBnbG9yaWZpZXIgZXV4LW3Dqm1lcyBhdSBzZWluIGRlIGwnw4lnbGlzZSwgZMOpdG91cm5hbnQgYWluc2kgbGVzIGZpZMOobGVzIGRlIGzigJlhcMO0dHJlIFBhdWwuXG5cbkxlIGdyYW5kIG1hbCBxdWkgbWVuYWNlIFBhdWwgaWNpLCBsYSBncmFuZGUgbWFsYWRpZSwgYydlc3QgbCdvcmd1ZWlsLiBFdCBjJ2VzdCBhdXNzaSBsZSBncmFuZCBtYWwgcXVpIG5vdXMgbWVuYWNlIHRvdXMuIEwnb3JndWVpbCBlc3QgY2UgcMOpY2jDqSBxdWkgbm91cyBwb3Vzc2Ugw6AgY2hlcmNoZXIgbm90cmUgcHJvcHJlIGdsb2lyZSwgw6Agbm91cyDDqWxldmVyIGF1LWRlc3N1cyBkZSBEaWV1LiBMJ29yZ3VlaWwsIGMnZXN0IGNlIHF1aSBmYWl0IGdvbmZsZXIgbm90cmUgZWdvIGNvbW1lIHVuIGJhbGxvbiwgbm91cyByZW5kYW50IGNvbnN0YW1tZW50IGF2aWRlcyBkZSByZWNvbm5haXNzYW5jZSwgZCdhZG1pcmF0aW9uLCBkJ2VzdGltZSBkZSBsYSBwYXJ0IGRlcyBhdXRyZXMuIE5vdXMgc29tbWVzIHRvdXMgZGVzIGRyb2d1w6lzIMOgIGwnb3JndWVpbCwgdG90YWxlbWVudCBkw6lwZW5kYW50cyBkZSBjZSBww6ljaMOpLiBOb3VzIHZpdm9ucyBlbiBwZXJww6l0dWVsbGUgY29tcMOpdGl0aW9uIGF2ZWMgbGVzIGF1dHJlcywgY2hlcmNoYW50IMOgIGF2b2lyIHJhaXNvbiwgw6AgYXZvaXIgbGUgZGVybmllciBtb3QsIMOgIHBhcmHDrnRyZSBwbHVzIGludGVsbGlnZW50cywgcGx1cyBiZWF1eCwgcGx1cyBjb21ww6l0ZW50cywgcGx1cyBmb3J0cywgcGx1cyBpbmZsdWVudHMsIHBsdXMgcsOpdXNzaXMuXG5cbk5vdXMgcGFzc29ucyBub3RyZSB0ZW1wcyDDoCBub3VzIGNvbXBhcmVyIGF1eCBhdXRyZXMsIMOgIGVudmllciBjZSBxdSdpbHMgb250IGV0IHF1ZSBub3VzIG4nYXZvbnMgcGFzLCBldCBub3VzIGNoZXJjaG9ucyDDoCBsJ29idGVuaXIuIE5vdHJlIGVnbyBu4oCZYSBxdeKAmXVuIHNldWwgYnV0OiBjcsOpZXIgdW5lIHZlcnNpb24gaWTDqWFsaXPDqWUgZGUgbm91cy1tw6ptZXMgw6AgYWZmaWNoZXIgZGV2YW50IGxlcyBhdXRyZXMsIG3Dqm1lIHNpIGVsbGUgZXN0IGZhY3RpY2UsIGFmaW4gZGUgcmVjZXZvaXIgZGVzIGxvdWFuZ2VzIHF1aSwgYmllbiBxdWUgbm9uIG3DqXJpdMOpZXMsIG5vdXJyaXNzZW50IG5vdHJlIGVnby4gUGV1IGltcG9ydGUgc+KAmWlsIHkgYSBkdSBtZW5zb25nZSwgY2UgcXVlIG5vdXMgdm91bG9ucywgY+KAmWVzdCBxdWUgbm90cmUgZWdvIHNlIGdvbmZsZS4gRXQgcGx1cyBsZSBiYWxsb24gZGUgbm90cmUgZWdvIHNlIGdvbmZsZSwgcGx1cyBub3VzIG5vdXMgc2VudG9ucyBhaW3DqXMsIHZhbG9yaXPDqXMsIGFkbWlyw6lzLCBlc3RpbcOpcyBldCBtw6lyaXRhbnRzLlxuXG5Ob3VzIHNvbW1lcyBkZXMgZHJvZ3XDqXMgw6AgbOKAmW9yZ3VlaWwuIEV0IHNpIHZvdXMgdm91cyBkaXRlczogXCJNb2ksIGplIG5lIHN1aXMgcGFzIGNvbW1lIMOnYSwgamUgbmUgc3VpcyBwYXMgY29tbWUgbGVzIGF1dHJlc1wiLCBkw6lzb2zDqSwgbWFpcyB2b3VzIMOqdGVzIHRvbWLDqSBkYW5zIGxlIHBpw6hnZS4gVm91cyDDqnRlcyBjb21tZSBub3VzLlxuXG5M4oCZb3JndWVpbCBlc3QgbGUgcGx1cyBncmFuZCBkYW5nZXIgcG91ciBsJ2h1bWFuaXTDqS4gSWwgYSBjYXVzw6kgdGFudCBkZSBndWVycmVzLCB0YW50IGRlIGRpdm9yY2VzLCB0YW50IGRlIHZpb2xlbmNlcy4gSWwgY2F1c2UgcGx1cyBkZSBzb3VmZnJhbmNlIGV0IGRlIG1vcnQgZGFucyBsZSBtb25kZSBxdWUgbidpbXBvcnRlIHF1b2kgZCdhdXRyZS4gTOKAmW9yZ3VlaWwgZXN0IHVuZSBtYWxhZGllIGJpZW4gcGx1cyBncmF2ZSBwb3VyIG5vcyDDom1lcyBxdWUgdG91dGVzIGxlcyBhdXRyZXMgw6lwcmV1dmVzIGRlIGxhIHZpZS4gTOKAmW9yZ3VlaWwgcGV1dCBub3VzIGTDqXRydWlyZS5cblxuUXVlbGxlIGVzdCBsYSByYWlzb24gc2luZ3VsacOocmUgcG91ciBsYXF1ZWxsZSBTYXRhbiBhIMOpdMOpIGNoYXNzw6kgZHUgY2llbD8gTCdvcmd1ZWlsLCBsYSBqYWxvdXNpZS4gRXQgbm91cyBhdm9ucyBpY2kgdW5lIG1pc2UgZW4gZ2FyZGUsIGF2ZWMgbOKAmWV4ZW1wbGUgZGUgbOKAmWFww7R0cmUgUGF1bC4gT24gcG91cnJhaXQgc2UgZGlyZTogXCJDJ2VzdCB0b3V0IGRlIG3Dqm1lIFBhdWwhXCIgTWFpcyB2b3Vsb2lyIHZpdnJlIHBvdXIgbGUgU2VpZ25ldXIgbmUgbm91cyBwcm90w6hnZSBhYnNvbHVtZW50IHBhcyBkZSBjZSBkYW5nZXIuIFBhdWwgbHVpLW3Dqm1lIGRldmFpdCBsdXR0ZXIgY29udHJlIGwnb3JndWVpbC4gRXQgcGx1cyBub3VzIGTDqXNpcm9ucyB2aXZyZSBwb3VyIGxlIFNlaWduZXVyLCBwbHVzIG5vdXMgc29tbWVzIGV4cG9zw6lzIMOgIHVuIG9yZ3VlaWwgc3VidGlsLCB1biBvcmd1ZWlsIHRyw6hzIHNwaXJpdHVlbDogbCdpbGx1c2lvbiBkZSBub3RyZSBncmFuZGV1ciBmYWNlIGF1eCBkaWZmaWN1bHTDqXMgZGUgbGEgdmllLlxuXG5Ob3VzIG5vdXMgZGlzb25zOiBcIk5vdXMgbmUgc29tbWVzIHBhcyBjb21tZSBjZXMgcGVyc29ubmVzIHF1aSBtYW5xdWVudCBkZSBmb2kuIE5vdXMsIHF1YW5kIG5vdXMgcsOpdXNzaXNzb25zIHRlbGxlIG91IHRlbGxlIGNob3NlLiBOb3VzLCBxdWFuZCBub3VzIHNvbW1lcyBmaWTDqGxlcyBhbG9ycyBxdWUgZCdhdXRyZXMgc29udCBpbmZpZMOobGVzIGRhbnMgbGV1ciB2aWUuwrsgUXXigJllc3QtY2UgcXXigJlvbiBzZSBzZW50IG1laWxsZXVyIHF14oCZZXV4IVxuXG5Ob3MgZ3JhbmRlcyByw6l1c3NpdGVzIHNvbnQgcGx1cyBkYW5nZXJldXNlcyBwb3VyIG5vdHJlIMOibWUgcXVlIG5vcyBncmFuZHMgw6ljaGVjcy4gTGVzIMOpY2hlY3MgZGUgbm90cmUgdmllIG5vdXMgaHVtaWxpZW50LCBtYWlzIG5vcyByw6l1c3NpdGVzIHJlcHLDqXNlbnRlbnQgdW4gZ3JhbmQgZGFuZ2VyIGTigJlvcmd1ZWlsLiBQb3VycXVvaSBQYXVsIHNvdWZmcmUtdC1pbD8gUG91ciDDqnRyZSBnYXJkw6kgZGUgbCdvcmd1ZWlsLCBjJ2VzdCBsYSBwcmVtacOocmUgcmFpc29uLiBMYSBkZXV4acOobWUgcmFpc29uLCBjJ2VzdCBwb3VyIHF1J2lsIGNvbXByZW5uZSBlbmNvcmUgcGx1cyBsYSBncsOiY2UuXG5cbkrDqXN1cyBwcm90w6hnZSBQYXVsIGRlIGwnb3JndWVpbCBxdWkgbGUgbWVuYWNlIGVuIGx1aSBwcmVzY3JpdmFudCwgdGVsIHVuIG3DqWRlY2luLCB1bmUgw6ljaGFyZGUgcXVpIHZpZW50IGZhaXJlIMOpY2xhdGVyIGxlIGJhbGxvbiBkZSBzb24gb3JndWVpbC4gQ2UgYmFsbG9uLCBjcmV2w6kgw6AgY2F1c2UgZGUgY2V0dGUgw6ljaGFyZGUsIG5lIHBvdXJyYSBwYXMgc2UgZ29uZmxlci4gQ2Ugbidlc3QgcGFzIGxlIHN1Y2PDqHMgcXVpIHZhIGZhaXJlIHByb2dyZXNzZXIgbGUgbWluaXN0w6hyZSBkZSBQYXVsIG91IGxlIGNyw6lkaWJpbGlzZXIgYXVwcsOocyBkZSBsJ8OJZ2xpc2UsIG1haXMgdW5lIHBsdXMgZ3JhbmRlIGTDqXBlbmRhbmNlIMOgIGxhIGdyw6JjZSBxdSdpbCBwcsOqY2hlIGF1eCBhdXRyZXMuIErDqXN1cyBsZSBsdWkgcsOpdsOobGUgYXZlYyB1bmUgdGVsbGUgdGVuZHJlc3NlIGF1IHZlcnNldCA5OiBcIklsIG0nYSBkaXQ6IE1hIGdyw6JjZSB0ZSBzdWZmaXQsIGNhciBtYSBwdWlzc2FuY2UgcydhY2NvbXBsaXQgZGFucyBsYSBmYWlibGVzc2UuXCJcblxuTWEgZ3LDomNlIHRlIHN1ZmZpdC4gTGEgZ3LDomNlIGRlIErDqXN1cywgY2Ugbidlc3QgcGFzIGp1c3RlIHVuIHNlbnRpbWVudCBlbnZlcnMgUGF1bC4gQydlc3QgY2UgcXVlIErDqXN1cyBmYWl0IHBvdXIgUGF1bCwgYmllbiBxdWUgUGF1bCBuZSBsZSBtw6lyaXRlIHBhcy4gSsOpc3VzIGx1aSBkaXQ6IFwiUGF1bCwgbWEgZ3LDomNlIHRlIHN1ZmZpdC5cIiBBdXRyZW1lbnQgZGl0LCBlbGxlIGVzdCBwYXJmYWl0ZS4gRWxsZSBlc3QgcGFyZmFpdGUgcG91ciB0b2kuIFNpIGxhIGdyw6JjZSBkZSBKw6lzdXMgZW52ZXJzIGx1aSBlc3QgcGFyZmFpdGUsIGMnZXN0IHBhcmNlIHF1J2lsIG5lIGx1aSBtYW5xdWUgcmllbi4gSWwgbmUgbWFucXVlIHJpZW4uIErDqXN1cyBuJ2EgcmllbiBvdWJsacOpIGRlIGRvbm5lciDDoCBQYXVsLCBpbCBhIG3Dqm1lIHBlbnPDqSDDoCBsdWkgZmFpcmUgZG9uIGRlIGNldHRlIMOpY2hhcmRlLiBKw6lzdXMgdXRpbGlzZSBsJ8OpY2hhcmRlIGNvbW1lIHVuIGluc3RydW1lbnQuIFBhdWwgZXN0IGVuIGRhbmdlciBkJ29yZ3VlaWwsIGFsb3JzIErDqXN1cyBsJ2FmZmFpYmxpdCwgbGUgYmxlc3NlLCBtYWlzIGlsIGxlIHJlbmZvcmNlLiBJbCB0cmFuc2Zvcm1lIGxhIGZvcmNlIGNoYXJuZWxsZSwgY2VsbGUgZHUgcMOpY2jDqSwgZW4gZm9yY2Ugc3Bpcml0dWVsbGUsIGNlbGxlIGRlIGwnaHVtaWxpdMOpLlxuXG5Wb2lsw6AgY2UgcXVlIHByb2R1aXQgY2V0dGUgZ3LDomNlLiBMJ8OpY2hhcmRlIGTDqXRydWl0IGxhIGZvcmNlIGRlIFBhdWwgcG91ciBxdWUgbGEgcHVpc3NhbmNlIGRlIENocmlzdCBhZ2lzc2UgZW4gbHVpLiBKw6lzdXMgbmUgYmxlc3NlIHF1ZSBwb3VyIGd1w6lyaXIuIErDqXN1cyBsdWkgZGl0OiBcIlBhdWwsIHBhcmNlIHF1ZSB0dSBlcyBsJ29iamV0IGRlIG1hIGdyw6JjZSwgamUgdmFpcyB0ZSBkw6lsaXZyZXIgZGUgbCdvcmd1ZWlsIGVuIG5lIHRlIGTDqWxpdnJhbnQgcGFzIGRlIGwnw6ljaGFyZGUuIEplIHZhaXMgdXRpbGlzZXIgdW4gbWFsIGV4dMOpcmlldXIsIGwnw6ljaGFyZGUsIHBvdXIgdGUgZMOpbGl2cmVyIGR1IG1hbCBpbnTDqXJpZXVyLCBsJ29yZ3VlaWwuIE1hIGdyw6JjZSB0ZSBzdWZmaXQsIGNhciBtYSBwdWlzc2FuY2UgcydhY2NvbXBsaXQgZGFucyBsYSBmYWlibGVzc2UuXCJcblxuTCfDqXByZXV2ZSBmYWl0IHBhcnRpZSBkZSBsYSBmb3JtYXRpb24gc3Bpcml0dWVsbGUuIEVsbGUgZmFpdCBwYXJ0aWUgZGUgbm90cmUgZGlzY2lwdWxhdCBkYW5zIGwnw6ljb2xlIGRlIERpZXUuIExlIGNvdXJzIHN1ciBsYSBzb3VmZnJhbmNlIGVzdCB1biBjb3VycyBvYmxpZ2F0b2lyZS4gTWFkYW1lIMOJcHJldXZlIGVzdCB1bmUgZW5zZWlnbmFudGUgcXVpIG5lIGZhaXQgamFtYWlzIGdyw6h2ZSwgZXQgYydlc3QgdW5lIHNhY3LDqWUgcGVhdSBkZSB2YWNoZSwgZnJhbmNoZW1lbnQuIEMnZXN0IGNlbGxlIHF1J29uIG5lIHZvdWRyYWl0IHBhcyBhdm9pciwgbWFpcyBxdSdvbiBhLiBFbGxlIGVzdCB0ZXJyaWJsZS4gSWwgbid5IGEgcGFzIGRlIGRpc3BlbnNlIHBvc3NpYmxlIHBvdXIgc29uIGNvdXJzLiBFbGxlIG5lIGZhaXQgcXVlIGRlcyBpbnRlcnJvZ2F0aW9ucyBzdXJwcmlzZXMsIGV0IGNlIG4nZXN0IHBhcyBkZSBsYSB0aMOpb3JpZSBhdmVjIGVsbGUsIGMnZXN0IGRlIGxhIHByYXRpcXVlLCBjZSBzb250IGRlcyB0cmF2YXV4IHByYXRpcXVlcy4gRXQgSsOpc3VzIGEgZmFpdCBhc3Nlb2lyIFBhdWwgYXUgcHJlbWllciByYW5nIHBvdXIgcXUnaWwgw6ljb3V0ZSBiaWVuIGNldHRlIGxlw6dvbi5cblxuRXQgY2UgY291cnMgZGUgTWFkYW1lIMOJcHJldXZlLCBkZXZpbmV6IHF1b2k/IEPigJllc3QgdW4gY291cnMgZGUgbWF0aMOpbWF0aXF1ZXMsIGZvcmPDqW1lbnQuIEFsb3JzIHF1ZSBQYXVsIHBlbnNhaXQgcXVlIGxhIHNvbHV0aW9uIGF1IHByb2Jsw6htZSDDqXRhaXQgdW5lIHNvdXN0cmFjdGlvbiwgw6Agc2F2b2lyOiBQYXVsIG1vaW5zIMOpcHJldXZlIMOpZ2FsZSBQYXVsIHBsdXMgZm9ydCwgTWFkYW1lIMOJcHJldXZlIGx1aSBhIGRpdDogXCJMZXMgY2FsY3VscyBzb250IGZhdXgsIFBhdWwuXCIgTGVzIGNhbGN1bHMgc29udCBtYXV2YWlzOiBQYXVsIG1vaW5zIMOpcHJldXZlIMOpZ2FsZSBQYXVsIGVuIGRhbmdlci4gUGF1bCBwbHVzIMOpcHJldXZlIMOpZ2FsZSBQYXVsIGZhaWJsZSwgbWFpcyBQYXVsIGZhaWJsZSDDqWdhbGUgUGF1bCBxdWkgY29tcHRlIHN1ciBsYSBwdWlzc2FuY2UgZGUgQ2hyaXN0LiBEb25jIFBhdWwgZmFpYmxlIMOpZ2FsZSBQYXVsIGZvcnQuIE9uIG5lIGNvbXByZW5kIHJpZW4sIGPigJllc3QgZGVzIG1hdGhzLCBj4oCZZXN0IG5vcm1hbC5cblxuQWxvcnMgamUgdHJhZHVpcyBwb3VyIGNldXggcXVpIG7igJlhaW1lbnQgcGFzIGxlcyBtYXRocy4gU291dmVudCwgb24gcGVuc2UgcXVlIERpZXUgZGV2cmFpdCBlbmxldmVyIGxlcyDDqWNoYXJkZXMgcG91ciBub3RyZSBiaWVuLCBtYWlzIHBhcmZvaXMsIERpZXUgbGVzIGFqb3V0ZSBwb3VyIG5vdXMgYXBwcmVuZHJlIMOgIG1vdXJpciDDoCBub3VzLW3Dqm1lcy4gTCfDqWNoYXJkZSBkZSBsYSBncsOiY2Ugbm91cyBwb3Vzc2UgYXUgcGllZCBkdSB0csO0bmUgZGUgbGEgZ3LDomNlLCBvw7kgSsOpc3VzIG5vdXMgcmVsw6h2ZSBldCBub3VzIHRyYW5zZm9ybWUgcGFyIHNhIHB1aXNzYW5jZS4gSWwgeSBhIGRlcyBjaG9zZXMgcXVpIG5lIHPigJlhcHByZW5uZW50LCBxdWkgbmUgc2UgZm9ybWVudCBlbiBub3VzLCBxdWkgbmUgc2UgZMOpdmVsb3BwZW50IHF14oCZw6AgdHJhdmVycyBs4oCZw6lwcmV1dmUuIEPigJllc3QgYWluc2kuXG5cbiMjIDMuIENvbW1lbnQgSsOpc3VzIGZvcnRpZmllLXQtaWwgUGF1bCBwYXIgbGEgc291ZmZyYW5jZT9cblxuUXVlbCBlc3QgbGUgcHJvY2Vzc3VzPyBDb21tZW50IGNlbGEgZm9uY3Rpb25uZS10LWlsPyBGYXV0LWlsIGNvbXByZW5kcmUgcXVlIERpZXUgdXRpbGlzZSBsZXMgw6lwcmV1dmVzIGNvbW1lIGRlcyBlc3DDqGNlcyBkZSB2aXRhbWluZXMgc3Bpcml0dWVsbGVzPyBFc3QtY2UgdW5lIGZvcmNlIHNwaXJpdHVlbGxlIGFic3RyYWl0ZSBxdWkgdmEgc2UgZMOpdmVsb3BwZXI/IE5vbiwgamUgY3JvaXMgcXUnaWwgeSBhIHF1ZWxxdWUgY2hvc2UgZGUgYmVhdWNvdXAgcGx1cyBwcm9mb25kLCBkZSBiZWF1Y291cCBwbHVzIGludGltZSwgcXVpIHNlIHBhc3NlIGVudHJlIErDqXN1cyBldCBQYXVsLiBQb3VyIGxlIGNvbXByZW5kcmUsIGlsIGZhdXQgcmV2ZW5pciDDoCBsJ8OJdmFuZ2lsZS4gQ2UgcXVlIG5vdXMgZMOpY291dnJvbnMgZGFucyBsJ8OJdmFuZ2lsZSwgZXQgw6AgdHJhdmVycyBsZSB0ZXh0ZSBkJ8OJc2HDr2UgNTMgcXVpIG5vdXMgYSDDqXTDqSBsdSB0b3V0IMOgIGzigJloZXVyZSwgY+KAmWVzdCBxdeKAmWlsIG7igJl5IGEgcGFzIGRlIHbDqXJpdGFibGUgYW1vdXIgc2FucyBzYWNyaWZpY2UuIEFpbWVyIGzigJlhdXRyZSwgY+KAmWVzdCBzZSBzYWNyaWZpZXIgcG91ciBsdWkuXG5cbkV0IGNvbW1lbnQgc2UgbWVzdXJlIGxhIGZvcmNlIGRlIGzigJlhbW91ciBkaXZpbiBwb3VyIG5vdXM/IFBhdWwgbHVpLW3Dqm1lIG5vdXMgZGl0IGRhbnMgUm9tYWlucyA1LCB2ZXJzZXQgODogXCJWb2ljaSBjb21tZW50IERpZXUgcHJvdXZlIHNvbiBhbW91ciBlbnZlcnMgbm91czogYWxvcnMgcXVlIG5vdXMgw6l0aW9ucyBlbmNvcmUgZGVzIHDDqWNoZXVycywgQ2hyaXN0IGVzdCBtb3J0IHBvdXIgbm91cy5cIiBBaW1lciBs4oCZYXV0cmUgaW1wbGlxdWUgZGUgc2Ugc2FjcmlmaWVyIHBvdXIgbHVpLiBRdWVscXXigJl1biBxdWkgbmUgc2FjcmlmaWUgcmllbiBkZSBsdWkgcG91ciB2b3VzIGVzdCBxdWVscXXigJl1biBxdWkgbmUgdm91cyBhaW1lIHBhcy5cblxuRXQgbGEgZm9yY2UgZGUgbCdhbW91ciBkZSBKw6lzdXMgcG91ciBub3VzIHNlIG1lc3VyZSDDoCBjZSBxdSdpbCBhIHNhY3JpZmnDqSBwb3VyIG5vdXMuIEwnw6ljaGFyZGUgcXVpIGEgcGVyY8OpIErDqXN1cywgYydlc3QgbGUgcG9pZHMgZGUgbm9zIHDDqWNow6lzLiBOb3MgcMOpY2jDqXMgbCdvbnQgY2xvdcOpIHN1ciBsYSBjcm9peCwgZXQgaWwgYSBzb3VmZmVydCBwb3VyIG5vcyBmYXV0ZXMuIElsIGEgc3ViaSBsYSBjb25kYW1uYXRpb24gcXVlIG5vdXMgbcOpcml0aW9ucy5cblxuRGFucyBHZXRoc8OpbWFuw6ksIErDqXN1cyBhIGx1aSBhdXNzaSBzdXBwbGnDqSBwYXIgdHJvaXMgZm9pcyBsZSBQw6hyZSwgYWZpbiBxdWUsIHMnaWwgw6l0YWl0IHBvc3NpYmxlLCBsYSBzb3VmZnJhbmNlIGRlIGxhIGNyb2l4IGx1aSBzb2l0IMOpcGFyZ27DqWUuIE1haXMgbGUgUMOocmUgbHVpIGEgZGl0IG5vbi4gTm9uLCBjYXIgbGUgcGxhbiDDqXRhaXQgcXUnaWwgbWFuaWZlc3RlIHNhIHB1aXNzYW5jZSDDoCB0cmF2ZXJzIGxhIG1vcnQgZGUgQ2hyaXN0LiBFdCBKw6lzdXMgYSBtYW5pZmVzdMOpIGxhIHB1aXNzYW5jZSBkZSBsJ2Ftb3VyIGRlIERpZXUgZGFucyBzYSBmYWlibGVzc2UsIGxhIHTDqnRlIGNvdXJvbm7DqWUgZCfDqXBpbmVzLCBjbG91w6kgc3VyIHVuZSBjcm9peC4gUG91ciBwb3V2b2lyIG5vdXMgZG9ubmVyIHNhIHZpZSwgaWwgZmFsbGFpdCBkJ2Fib3JkIHF1J2lsIGxhIHBlcmRlLlxuXG7DgCBzb24gdG91ciwgUGF1bCBhIHN1cHBsacOpIHRyb2lzIGZvaXMgSsOpc3VzIHBvdXIgcXVlIGwnw6ljaGFyZGUgbHVpIHNvaXQgZW5sZXbDqWUuIE1haXMgSsOpc3VzIGEgZGl0IG5vbiwgY2FyIHNvbiBwbGFuIMOpdGFpdCBxdSdpbCBtYW5pZmVzdGUgw6Agc29uIHRvdXIgc2EgcHVpc3NhbmNlIGRhbnMgbGEgZmFpYmxlc3NlIGRlIFBhdWwuIFBhdWwgw6l0YWJsaXQgbHVpLW3Dqm1lIGNlIGxpZW4gdW4gcGV1IHBsdXMgbG9pbiwgYXUgY2hhcGl0cmUgMTMsIHZlcnNldCA0OiBcIkNhciBpbCBhIMOpdMOpIGNydWNpZmnDqSBlbiByYWlzb24gZGUgc2EgZmFpYmxlc3NlLCBtYWlzIGlsIHZpdCBlbiByYWlzb24gZGUgbGEgcHVpc3NhbmNlIGRlIERpZXUuIE5vdXMgYXVzc2ksIG5vdXMgc29tbWVzIGZhaWJsZXMgZW4gbHVpLCBtYWlzIG5vdXMgdml2b25zIGF2ZWMgbHVpIHBvdXIgdm91cyBlbiByYWlzb24gZGUgbGEgcHVpc3NhbmNlIGRlIERpZXUuXCJcblxuSsOpc3VzIHMnZXN0IGFmZmFpYmxpIHBvdXIgc2F1dmVyIFBhdWwuIEFsb3JzLCDDoCBzb24gdG91ciwgUGF1bCBhY2NlcHRlIGQnw6p0cmUgZmFpYmxlIHBvdXIgcXVlIErDqXN1cyBtYW5pZmVzdGUgc2EgcHVpc3NhbmNlIMOgIHRyYXZlcnMgbHVpLiBQYXVsIHZldXQgcXUnaWwgbid5IGFpdCBhdWN1biBkb3V0ZSBxdWUgdG91dCBjZSBxdSdpbCBmYWl0LCB0b3V0IGNlIHF1J2lsIHByb2R1aXQsIHRvdXQgY2UgcXUnaWwgZW5kdXJlLCB0b3VzIGxlcyBib25zIGZydWl0cyBkZSBzb24gbWluaXN0w6hyZSwgaWwgbGVzIGFjY29tcGxpdCBwYXIgbGEgZ3LDomNlIGRlIErDqXN1cyBxdWkgYWdpdCBlbiBsdWkgZXQgcG91ciBsYSBnbG9pcmUgZGUgSsOpc3VzIHNldWwuXG5cbkF1IHZlcnNldCA5YiBkZSBub3RyZSBwYXNzYWdlLCBpbCBkaXQ6IFwiSmUgbWUgZ2xvcmlmaWVyYWkgZG9uYyBiaWVuIHBsdXMgdm9sb250aWVycyBkZSBtZXMgZmFpYmxlc3NlcywgYWZpbiBxdWUgbGEgcHVpc3NhbmNlIGRlIENocmlzdCByZXBvc2Ugc3VyIG1vaS4gQydlc3QgcG91cnF1b2kgamUgbWUgcGxhaXMgZGFucyBsZXMgZmFpYmxlc3NlcywgZGFucyBsZXMgb3V0cmFnZXMsIGRhbnMgbGVzIHByaXZhdGlvbnMsIGRhbnMgbGVzIHBlcnPDqWN1dGlvbnMsIGRhbnMgbGVzIGFuZ29pc3NlcyBwb3VyIENocmlzdC4gQ2FyLCBlbiBlZmZldCwgcXVhbmQgamUgc3VpcyBmYWlibGUsIGMnZXN0IGFsb3JzIHF1ZSBqZSBzdWlzIGZvcnQuXCJcblxuQ2UgcXVlIFBhdWwgZGl0IHZhIGF1LWRlbMOgIGRlIGNlIHF1ZSBub3VzIHBvdXZvbnMgaW1hZ2luZXIsIGNlbGEgZMOpcGFzc2UgbCdlbnRlbmRlbWVudC4gSWwgc2UgcsOpam91aXQgZCfDqnRyZSBmYWlibGUsIGNhciBjJ2VzdCBsZSBtb3llbiBwb3VyIGx1aSBkJ8OqdHJlIGTDqXBlbmRhbnQgZGUgSsOpc3VzLCBldCBsZXMgYm9ucyBmcnVpdHMgZGUgc29uIG1pbmlzdMOocmUgbmUgcGV1dmVudCBsdWkgw6p0cmUgYXR0cmlidcOpcy4gQ2FyIGlsIGVzdCB0cm9wIGZhaWJsZSBwb3VyIHF1J29uIHB1aXNzZSBkaXJlOiBcIsOHYSwgw6dhIHZpZW50IGRlIFBhdWwuXCIgUHVpc3F1J2lsIGVzdCBmYWlibGUsIG9uIHNhaXQgcXVlIGNlbGEgdmllbnQgZGUgSsOpc3VzLCBxdWUgY2Ugbidlc3QgcGFzIFBhdWwgcXVpIHByb2R1aXQgY2VzIGNob3Nlcy1sw6AsIG1haXMgcXVlIGMnZXN0IENocmlzdCBxdWkgYWdpdCBlbiBsdWkuIEV0IFBhdWwgc2UgcsOpam91aXQgZGUgY2VsYSwgY2FyIHNvbiBwbHVzIGNoZXIgZMOpc2lyIGVzdCBxdWUgdG91dGUgbGEgZ2xvaXJlIHNvaXQgcmVuZHVlIMOgIENocmlzdCwgZXQgbm9uIMOgIGx1aS5cblxuV291aCEgUXVlbCBjb250cmFzdGUgYXZlYyBjZXMgcHNldWRvLWFww7R0cmVzIVxuXG4jIyA0LiBRdWVsbGVzIGltcGxpY2F0aW9ucyBwb3VyIG5vdXM/XG5cbkFsb3JzLCBxdWVsbGVzIHNvbnQgbGVzIGltcGxpY2F0aW9ucyBwb3VyIG5vdXM/IEplIHZvdWRyYWlzIGVuIHNvdWxpZ25lciBkZXV4LlxuXG4jIyMgUGFzIGRlIGNyb2lzc2FuY2Ugc2FucyBzb3VmZnJhbmNlXG5cbkRhbnMgbGEgdmllIGNocsOpdGllbm5lLCBpbCBuJ3kgYSBwYXMgZGUgY3JvaXNzYW5jZSBzYW5zIHNvdWZmcmFuY2UuIERpZXUgbm91cyBmb3JtZSBjZXJ0ZXMgZ3LDomNlIMOgIGxhIG3DqWRpdGF0aW9uIGRlIHNhIFBhcm9sZSwgZ3LDomNlIMOgIGxhIHByacOocmUsIGdyw6JjZSDDoCBsYSB2aWUgY29tbXVuYXV0YWlyZSwgZ3LDomNlIGF1IGN1bHRlLCBncsOiY2UgYXUgc2VydmljZSwgdG91dGVzIGNlcyBjaG9zZXMtbMOgLCBtYWlzIGF1c3NpIGdyw6JjZSDDoCBsYSBzb3VmZnJhbmNlLiBFbGxlIGZhaXQgcGFydGllIGRlIG5vdHJlIGZvcm1hdGlvbiBzcGlyaXR1ZWxsZS4gTm91cyB2aXZyb25zIGRlcyBzb3VmZnJhbmNlcyBwb3VyIGxlc3F1ZWxsZXMgbm91cyBuJ2F1cm9ucyBqYW1haXMgZGUgcsOpcG9uc2UsIG1haXMgZWxsZXMgZm9udCBwYXJ0aWUgZGUgbCfDqWNvbGUsIGVsbGVzIHNvbnQgYXUgcHJvZ3JhbW1lLlxuXG5Ub3V0ZWZvaXMsIGVuIGRvbm5hbnQgY2V0dGUgcsOpcG9uc2UgZGlyZWN0ZSDDoCBQYXVsIChjYXIgbHVpIGEgZXUgbGEgam9pZSwgbGUgYsOpbsOpZmljZSwgbGUgcHJpdmlsw6hnZSBkJ2VudGVuZHJlIGxhIHLDqXBvbnNlIGRpcmVjdGUgZGUgSsOpc3VzKSwgamUgY3JvaXMgcXVlIErDqXN1cywgZW4gcydhZHJlc3NhbnQgw6AgUGF1bCBkaXJlY3RlbWVudCwgcydhZHJlc3NlIGluZGlyZWN0ZW1lbnQgw6AgY2hhY3VuIGQnZW50cmUgbm91cy4gSWwgbm91cyBkaXQsIGV0IGMnZXN0IHNhIHZvaXggcXVlIG5vdXMgZGV2b25zIGVudGVuZHJlIHBvdXIgbm91czogXCJNYSBncsOiY2UgdGUgc3VmZml0LCBjYXIgbWEgcHVpc3NhbmNlIHMnYWNjb21wbGl0IGRhbnMgbGEgZmFpYmxlc3NlLlwiIErDqXN1cyBzYWl0IG1pZXV4IHF1ZSBub3VzIGNlIHF1aSBlc3QgYm9uIHBvdXIgbm91cywgZXQgaWwgbmUgbWFucXVlcmEgamFtYWlzIGRlIG5vdXMgbGUgZG9ubmVyLCBjYXIgc2EgZ3LDomNlIGVzdCBwYXJmYWl0ZS5cblxuQWxvcnMgb3VpLCBub3VzIHNvdWZmcm9ucyBzb3V2ZW50LCBldCBub3VzIGNvbXByZW5vbnMgcmFyZW1lbnQsIG1haXMgbm91cyBwb3V2b25zIGx1aSBmYWlyZSBjb25maWFuY2UgZW4gdG91dCB0ZW1wcy4gSsOpc3VzIHV0aWxpc2VyYSB0b3V0IGNlIHF1ZSBub3VzIHZpdm9ucyBhZmluIGRlIG5vdXMgYXBwcmVuZHJlIMOgIG1vdXJpciDDoCBub3VzLW3Dqm1lcyBldCDDoCBkw6lwZW5kcmUgZGUgc2EgZ3LDomNlLiDDgCB2aXZyZSBkZSBwbHVzIGVuIHBsdXMgZGFucyBjZXR0ZSBkw6lwZW5kYW5jZSBxdW90aWRpZW5uZSDDoCBjZXR0ZSBncsOiY2UuXG5cbkFsb3JzLCBzaSBKw6lzdXMgbm91cyBsYWlzc2UgbCfDqWNoYXJkZSwgZmFpc29ucy1sdWkgY29uZmlhbmNlIGVuIHNhIGdyw6JjZSBzb3V2ZXJhaW5lLCBzYSBncsOiY2UgcGFyZmFpdGUsIHNhIGdyw6JjZSBzdWZmaXNhbnRlLiBFdCBwcmlvbnMgcG91ciBjb21wcmVuZHJlIGNvbW1lbnQgZWxsZSBub3VzIGVuc2VpZ25lIGwnaHVtaWxpdMOpIGV0IGxhIGTDqXBlbmRhbmNlIGVuIGx1aS5cblxuRmluYWxlbWVudCwgaWwgeSBhIHF1ZWxxdWUgY2hvc2UgZGUgdGVsbGVtZW50IGxpYsOpcmF0ZXVyIHBvdXIgbm91cyBkYW5zIGNldHRlIMOpY29sZS4gRW4gQ2hyaXN0LCBub3VzIGF2b25zIGxhIGxpYmVydMOpIGQnZW1icmFzc2VyIG5vdHJlIGZhaWJsZXNzZS4gRW4gQ2hyaXN0LCBub3VzIHBvdXZvbnMgZW1icmFzc2VyIG5vdHJlIGZhaWJsZXNzZSwgY2FyIG5vdXMgc2F2b25zIHF1J2lsIHZlaWxsZSBzdXIgbm91cyBlbiBub3VzIGZhaXNhbnQgZ3LDomNlLlxuXG5Eb25jLCBkYW5zIGxhIHZpZSBjaHLDqXRpZW5uZSwgaWwgbid5IGEgcGFzIGRlIGNyb2lzc2FuY2Ugc2FucyBzb3VmZnJhbmNlLiBFbGxlIGVzdCB1bmUgbsOpY2Vzc2l0w6kuXG5cbiMjIyBQYXMgZGUgc291ZmZyYW5jZSBzYW5zIGNyb2lzc2FuY2VcblxuRCdhYm9yZCwgaWwgbid5IGEgcGFzIGRlIGNyb2lzc2FuY2Ugc2FucyBzb3VmZnJhbmNlLCBtYWlzIGlsIG4neSBhIHBhcyBub24gcGx1cyBkZSBzb3VmZnJhbmNlIHNhbnMgY3JvaXNzYW5jZS4gRWxsZSBlc3QgdW4gbW95ZW4sIHVuIHRlcnJlYXUgcXVlIERpZXUgdXRpbGlzZSBwb3VyIG5vdHJlIGNyb2lzc2FuY2UuIEwnw6lwcmV1dmUgZmFpdCBwYXJ0aWUgZGUgbGEgZ3LDomNlLCBjYXIgZWxsZSBwYXJ0aWNpcGUgw6Agbm91cyB0cmFuc2Zvcm1lciDDoCBsJ2ltYWdlIGRlIErDqXN1cy4gTCfDqXByZXV2ZSBlc3QgYmllbiBzb3V2ZW50IGxlIG1vbWVudCBvw7kgc2UgbWFuaWZlc3RlIGxhIHB1aXNzYW5jZSBkZSBDaHJpc3QgZW4gbm91cy4gRXQgcXVlbCByw6ljb25mb3J0IGRhbnMgbm9zIMOpcHJldXZlczogSsOpc3VzIHNlIHRpZW50IGF1IHBsdXMgcHLDqHMgZGUgbm91cywgYydlc3QgbMOgIHF1J2lsIG1hbmlmZXN0ZSBjZXR0ZSBncsOiY2UuXG5cbkVuIGZhaXQsIGxhIHZpZSBkZSBkaXNjaXBsZSwgbGEgdmllIGNocsOpdGllbm5lLCBzZSByw6lzdW1lIMOgIHJlY29ubmHDrnRyZSBub3RyZSBmYWlibGVzc2Ugw6AgdG91dCBwb2ludCBkZSB2dWUsIGFmaW4gZGUgbGFpc3NlciBDaHJpc3QgbWFuaWZlc3RlciBzYSBwdWlzc2FuY2UgZW4gbm91cy4gTm91cyBzb21tZXMgc2F1dsOpcyBwYXIgbCfDiXZhbmdpbGUsIG5vdXMgc29tbWVzIHRyYW5zZm9ybcOpcyBwYXIgbCfDiXZhbmdpbGUsIGV0IG5vdXMgc29tbWVzIGFwcGVsw6lzIMOgIHZpdnJlIHNlbG9uIGwnw4l2YW5naWxlLiBRdWFuZCBub3VzIGF2b25zIGNvbmZpYW5jZSBxdWUgbm9zIMOpcHJldXZlcyBmb250IHBhcnRpZSBkdSBwbGFuIHNvdXZlcmFpbiBkZSBEaWV1IHBvdXIgbm90cmUgdmllLCBub3VzIHJlc3NlbWJsb25zIHVuIHBldSBwbHVzIMOgIErDqXN1cywgcXVpIGEgYWNjZXB0w6kgbCfDqXByZXV2ZSBkZSBsYSBjcm9peCBwb3VyIG5vdXMgc2F1dmVyLiBOb3VzIHJlc3NlbWJsb25zIHVuIHBldSBwbHVzIMOgIErDqXN1cywgcXVpIHNhdmFpdCBxdWUgbCfDqXByZXV2ZSBmYWlzYWl0IHBhcnRpZSBkdSBwbGFuIGRlIERpZXUgcG91ciBsdWkuIE5vdXMgY29tcHJlbm9ucyBkb25jIHBsdXMgcHJvZm9uZMOpbWVudCBzYSBncsOiY2UgZW52ZXJzIG5vdXMuXG5cbkrDqXN1cyBlc3QgbW9ydCBwb3VyIG1vaSBldCBpbCBlc3QgcmVzc3VzY2l0w6kgcG91ciBtZSBkb25uZXIgc2EgdmllLiBRdWFuZCBqZSBtZXVycyDDoCBtb2ktbcOqbWUsIGlsIG1hbmlmZXN0ZSBzYSBwdWlzc2FuY2UgZW4gbW9pLiBMdWkgcXVpIGVzdCBtb3J0IGV0IHJlc3N1c2NpdMOpIHBvdXIgbWUgZG9ubmVyIGxhIHZpZSwgcXVhbmQgw6AgbW9uIHRvdXIgaidhY2NlcHRlIGRlIG1vdXJpciDDoCBtb2ktbcOqbWUsIGMnZXN0IGzDoCBxdSdpbCBtYW5pZmVzdGUgbGEgcHVpc3NhbmNlIGRlIHNhIHZpZSBlbiBtb2kuIFF1YW5kIGplIHJlY29ubmFpcyBtYSBmYWlibGVzc2UsIGlsIG1hbmlmZXN0ZSBzYSBwdWlzc2FuY2UgZGFucyBtYSB2aWUuIEFpbnNpLCBlbiBhY2NlcHRhbnQgZCdpbWl0ZXIgSsOpc3VzIGRhbnMgbm9zIMOpcHJldXZlcywgbHVpIHF1aSBhdmFpdCB1bmUgdG90YWxlIGNvbmZpYW5jZSBhdSBQw6hyZSwgbm91cyBsdWkgcmVzc2VtYmxvbnMsIGV0IGMnZXN0IGFpbnNpIHF1J2lsIG5vdXMgdHJhbnNmb3JtZSDDoCBzb24gaW1hZ2UuIElsIG5vdXMgZmFpdCB2aXZyZSBsJ8OJdmFuZ2lsZSBkYW5zIGxlcyDDqXByZXV2ZXMuIE5vdXMgbcOpZGl0b25zIGRvbmMgc3VyIHNhIGdyw6JjZTogcXUnZXN0LWNlIHF1ZSBKw6lzdXMgZXN0IGVuIHRyYWluIGRlIG0nYXBwcmVuZHJlIGRlIGx1aSBldCBkZSBzYSBncsOiY2U/IENvbW1lbnQgY2V0dGUgw6lwcmV1dmUgbSdhcHByZW5kLWVsbGUgw6AgbWlldXggbGUgY29ubmHDrnRyZT8gUXVlIHZldXQtaWwgZmFpcmUgbW91cmlyIGVuIG1vaT8gUXUnZXN0LWNlIHF1ZSBsJ8OpY2hhcmRlIGVzdCBlbiB0cmFpbiBkZSBndcOpcmlyIGFmaW4gZGUgbSdhcHByZW5kcmUgw6AgY29tcHRlciBzdXIgc2EgcHVpc3NhbmNlIGRhbnMgbWEgdmllP1xuXG5KJ2F2YWlzIGTDqWrDoCBjb25udSBwYXMgbWFsIGQnw6lwcmV1dmVzOiBsYSBkw6lwcmVzc2lvbiwgdW5lIHRlbnRhdGl2ZSBkZSBzdWljaWRlLCBsJ2jDtHBpdGFsIHBzeWNoaWF0cmlxdWUsIGwnZXNjbGF2YWdlIGRlIGxhIGTDqXBlbmRhbmNlLCBkaXZlcnMgYWNjaWRlbnRzIGRlIGxhIHZpZSBwbHVzIG91IG1vaW5zIHPDqXJpZXV4LCBsZSBkaXZvcmNlIGRlIG1lcyBwYXJlbnRzLCBldCBiaWVuIGQnYXV0cmVzIGVuY29yZS4gRW4gMjAxNCwgaidhaSBjb21tZW5jw6kgw6AgYXZvaXIgbWFsIGF1IGRvcy4gSidhaSBiZWF1Y291cCBwcmnDqSBwb3VyIHF1ZSDDp2EgY2Vzc2UsIG1haXMgaMOpbGFzLCBsZXMgY2hvc2VzIHNlIHNvbnQgZMOpZ3JhZMOpZXMgYWxvcnMgcXVlIGplIHZlbmFpcyBkZSBwcmVuZHJlIHVuIHBvc3RlIHBhc3RvcmFsLiBDJ8OpdGFpdCBhdSB0b3V0IGTDqWJ1dCBkZSBtb24gbWluaXN0w6hyZS4gSifDqXRhaXMgc3VwZXIgY29udGVudC4gSidhcnJpdmFpcyBkYW5zIGwnw6lnbGlzZSBhdmVjIHBsZWluIGQnZW52aWUsIHBsZWluIGRlIHByb2pldHMsIGplIHZveWFpcyBjbGFpciBzdXIgY2UgcXUnaWwgZmFsbGFpdCBmYWlyZS4gSifDqXRhaXMgdHLDqHMgZW50aG91c2lhc3RlLCBldCBsw6AsIGxlIG1hbCBkZSBkb3MgYSBjb21tZW5jw6kuIENlbGEgcydlc3QgYWdncmF2w6kgYXUgcG9pbnQgcXVlIGplIGRldmFpcyDDqnRyZSBvcMOpcsOpIHVuZSBwcmVtacOocmUgZm9pcy4gSMOpbGFzLCBsJ29ww6lyYXRpb24gcydlc3QgbWFsIGTDqXJvdWzDqWUgZXQgbMOgLCBjZWxhIHMnZXN0IGVuY29yZSBhZ2dyYXbDqS5cblxuQ2VsYSBtJ2EgY29uZHVpdCDDoCBwYXNzZXIgb256ZSBtb2lzIGFsaXTDqSwgZG9udCBuZXVmIMOgIGwnaMO0cGl0YWwuIFBlbmRhbnQgdG91dGUgdW5lIHDDqXJpb2RlLCBqJ2FpIGNvbm51IGRlcyBzb3VmZnJhbmNlcyBwaHlzaXF1ZXMgw6AgdW4gcG9pbnQgcXVlIGplIG5lIHNhdmFpcyBtw6ptZSBwYXMgcXUnaWwgw6l0YWl0IHBvc3NpYmxlIGRlIHNvdWZmcmlyIMOgIGNlIHBvaW50LiBBdSBuaXZlYXUgZGUgbGEgbW9ycGhpbmUsIG9uIG1lIGRvbm5haXQgY2UgcXUnb24gZG9ubmUgw6AgcXVlbHF1J3VuIGF0dGVpbnQgZCd1biBjYW5jZXIgZW4gcGhhc2UgdGVybWluYWxlLiBPbiBuZSBwb3V2YWl0IHBhcyBhbGxlciBhdS1kZWzDoCBkZSDDp2EuIEonw6l0YWlzIGF1eCBsaW1pdGVzIGRlIHRvdXMgbGVzIGFuYWxnw6lzaXF1ZXMgcG9zc2libGVzLCBldCBwb3VydGFudCwgbGEgc291ZmZyYW5jZSDDqXRhaXQgYXRyb2NlLiBMYSBzaW1wbGUgcmVzcGlyYXRpb24gbWUgY2F1c2FpdCBkZXMgZG91bGV1cnMganVzcXVlIGRhbnMgbGVzIGphbWJlcyBldCBkYW5zIHRvdXQgbGUgY29ycHMuIEonYXZhaXMgbCdpbXByZXNzaW9uIHF1ZSBtb24gY29ycHMgYWxsYWl0IHNlIGRpc2xvcXVlciwgcXVlIGonYXZhaXMgw6l0w6kgw6ljcmFzw6kgcGFyIHVuIGNhbWlvbi4gQ2VydGFpbmVzIG51aXRzLCBqZSBjcmlhaXMgw6AgRGlldTogXCJNYWlzIFNlaWduZXVyLCByZXByZW5kcy1tb2ksIGplIG4nZW4gcGV1eCBwbHVzLCBqZSBuZSBwZXV4IHBhcyBjb250aW51ZXIgY29tbWUgw6dhIGVuY29yZSB1bmUgbWludXRlLCBjJ2VzdCB0ZXJyaWJsZS5cIlxuXG5KZSBtZSBzb3V2aWVucyBkZSB2b2lyIGRlcyBpbmZpcm1pw6hyZXMgcXVpIHBhc3NhaWVudCBkYW5zIG1hIGNoYW1icmUgZXQgcGxldXJhaWVudCBkZSBkw6lzYXJyb2ksIGNhciBlbGxlcyBuZSBzYXZhaWVudCBwYXMgY2UgcXUnZWxsZXMgcG91dmFpZW50IGZhaXJlIHBvdXIgbW9pLiBDJ8OpdGFpdCB0ZXJyaWJsZS4gQ2UgcXVlIGonYWkgYXBwcmlzIGRhbnMgY2V0dGUgw6lwcmV1dmUgZXN0IGV4dHJhb3JkaW5haXJlLiBBdWpvdXJkJ2h1aSwgcG91ciByaWVuIGF1IG1vbmRlIGplIG5lIHZvdWRyYWlzIG5lIHBhcyDDqnRyZSBwYXNzw6kgcGFyIGNldHRlIMOpcHJldXZlLCBjYXIgamUgc2FpcyBxdWUgY2UgcXVlIGxlIFNlaWduZXVyIG0nYSBhcHBvcnTDqSBtJ2EgdGVsbGVtZW50IGZhaXQgZ3JhbmRpci4gQ2VsYSBtJ2EgdGVsbGVtZW50IGFwcHJpcyBzdXIgbWEgZMOpcGVuZGFuY2UgZXQgc3VyIGwnaHVtaWxpdMOpIHF1ZSBjZWxhIG0nYSBmYWl0IGJlYXVjb3VwIGRlIGJpZW4uIEV0IHMnaWwgYSB2b3VsdSwgYXUgZMOpYnV0IGRlIG1vbiBtaW5pc3TDqHJlLCBhbG9ycyBxdWUgamUgZGV2YWlzIG1lIHRlbmlyIGRldmFudCBsJ8OpZ2xpc2UsIG1lIGRpcmU6IFwiTm9uLCB0b2ksIHR1IHZhcyB2ZW5pciBsw6AsIGV0IHR1IHZhcyByZXN0ZXIgZGFucyB1biBsaXQgcGVuZGFudCB1biBwZXRpdCBtb21lbnQsIGV0IGplIHZhaXMgdCdlbnNlaWduZXIgZW5jb3JlIHF1ZWxxdWUgY2hvc2UgcXVlIHR1IG4nYXMgcGFzIHB1IGFwcHJlbmRyZSDDoCBsJ0luc3RpdHV0IGJpYmxpcXVlXCIsIGlsIG0nYSBwcm9iYWJsZW1lbnQgZ2FyZMOpIGRlIGZhaXJlIGR1IG1hbCDDoCBsJ8OpZ2xpc2Ugb3Ugw6AgbWEgZmFtaWxsZS4gQ2UgcXVlIGonYWkgYXBwcmlzIGRhbnMgY2V0dGUgw6lwcmV1dmUgbSdhIGJlYXVjb3VwIGFpZMOpLCBtYWlzIGplIHNhaXMgcXVlIGNlIG4nZXN0IHBhcyBmaW5pLiBKJ2F1cmFpIGQnYXV0cmVzIMOpcHJldXZlcy5cblxuRHVyYW50IHRvdXRlIGNldHRlIHDDqXJpb2RlIGRlIGRvdWxldXIsIErDqXN1cyBtJ2EgcG9zw6kgdW5lIHF1ZXN0aW9uIHF1aSBhIHRyYW5zZm9ybcOpIG1hIGNvbXByw6loZW5zaW9uIGRlIGxhIHZpZSBkZSBkaXNjaXBsZS4gSmUgdm91ZHJhaXMgY29uY2x1cmUgZW4gdm91cyBwb3NhbnQgY2V0dGUgcXVlc3Rpb24gZXQgdm91cyBpbnZpdGVyIMOgIGxhIG3DqWRpdGVyOiBKw6lzdXMgZXN0LWlsIHN1ZmZpc2FudCBwb3VyIHZvdXM/IiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiUmFwaGHDq2wgQ2hhcnJpZXIiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvcmFwaGFlbF9jaGFycmllcl8wM19jb3BpZV8xX3NjYWxlZF9lMTY1NzgwMTI1Nzc4N19mOTM1OTk0MDM4LmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0LzFfMjE0YTE3MmRiMC5wbmciLCJoZWlnaHQiOjEwODAsIndpZHRoIjoxOTIwLCJhbHRlcm5hdGl2ZVRleHQiOiIiLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiJWaWUgY2hyw6l0aWVubmUifSx7Im5hbWUiOiJTb3VmZnJhbmNlIn0seyJuYW1lIjoiUHLDqWRpY2F0aW9uIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlUG9kY2FzdCIsInBvZGNhc3QiOnsic2x1ZyI6InByZWRpY2F0aW9ucy10cHNnIiwibmFtZSI6IlByw6lkaWNhdGlvbnMgVFBTRyJ9LCJlbWJlZEF1ZGlvIjoiPGlmcmFtZSBzdHlsZT1cImJvcmRlci1yYWRpdXM6MTJweFwiIHNyYz1cImh0dHBzOi8vb3Blbi5zcG90aWZ5LmNvbS9lbWJlZC9lcGlzb2RlLzRoT3hudVBZSGdyVzExcjV2QzNxSlc/dXRtX3NvdXJjZT1nZW5lcmF0b3ImdGhlbWU9MFwiIHdpZHRoPVwiMTAwJVwiIGhlaWdodD1cIjE1MlwiIGZyYW1lQm9yZGVyPVwiMFwiIGFsbG93ZnVsbHNjcmVlbj1cIlwiIGFsbG93PVwiYXV0b3BsYXk7IGNsaXBib2FyZC13cml0ZTsgZW5jcnlwdGVkLW1lZGlhOyBmdWxsc2NyZWVuOyBwaWN0dXJlLWluLXBpY3R1cmVcIiBsb2FkaW5nPVwibGF6eVwiPjwvaWZyYW1lPiIsImVtYmVkVmlkZW8iOiI8aWZyYW1lIHdpZHRoPVwiMTc1OVwiIGhlaWdodD1cIjYyN1wiIHNyYz1cImh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL2VtYmVkL2dzVzk2anY4a1RjXCIgdGl0bGU9XCJcIiBmcmFtZWJvcmRlcj1cIjBcIiBhbGxvdz1cImFjY2VsZXJvbWV0ZXI7IGF1dG9wbGF5OyBjbGlwYm9hcmQtd3JpdGU7IGVuY3J5cHRlZC1tZWRpYTsgZ3lyb3Njb3BlOyBwaWN0dXJlLWluLXBpY3R1cmU7IHdlYi1zaGFyZVwiIHJlZmVycmVycG9saWN5PVwic3RyaWN0LW9yaWdpbi13aGVuLWNyb3NzLW9yaWdpblwiIGFsbG93ZnVsbHNjcmVlbj48L2lmcmFtZT4ifSx7Il9fdHlwZW5hbWUiOiJDb21wb25lbnRNb2R1bGVMZWFkIiwiY29udGVudCI6Ik5vdXMgYXZvbnMgdG91cyBlbiBjb21tdW4gdW5lIHLDqWFsaXTDqSBpbmNvbnRvdXJuYWJsZTogbGEgc291ZmZyYW5jZS4gUXVlIGNlIHNvaXQgw6AgdHJhdmVycyBkZXMgZ3JhbmRlcyDDqXByZXV2ZXMgb3UgZGVzIHBldGl0ZXMgZGlmZmljdWx0w6lzIHF1b3RpZGllbm5lcywgbm91cyBzb3VmZnJvbnMsIGF2b25zIHNvdWZmZXJ0LCBldCBzb3VmZnJpcm9ucyBlbmNvcmUuIExhIHF1ZXN0aW9uIGNydWNpYWxlIG4nZXN0IHBhcyAqc2kqIG5vdXMgc291ZmZyaXJvbnMsIG1haXMgKmNvbW1lbnQqIG5vdXMgbGUgdml2cm9ucy4gU2kgbm90cmUgZm9pIG4nZXN0IHBhcyBwcm9mb25kw6ltZW50IGVucmFjaW7DqWUgZW4gSsOpc3VzLUNocmlzdCwgY2VzIMOpcHJldXZlcyByaXNxdWVudCBkZSBub3VzIMOpbG9pZ25lciBkZSBsYSBmb2kuIn0se31dfSx7ImlkIjoiNDA5OSIsInRpdGxlIjoiVml2cmUgZW4gcMOobGVyaW4gZGFucyB1biBtb25kZSBob3N0aWxlIiwic2x1ZyI6InZpdnJlLXBlbGVyaW4tbW9uZGUtaG9zdGlsZSIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyNC0wOS0wNlQwMzo0NTowMC4wMDBaIiwiYm9keSI6IiMjIFVuIHBzYXVtZSBwb3VyIGxlcyBww6hsZXJpbnNcblxuTGUgcHNhdW1lIDEyMSwgdW4gZGVzIFwiY2FudGlxdWVzIGRlcyBtb250w6llc1wiLCBlc3QgdW5lIHNvdXJjZSBk4oCZZW5jb3VyYWdlbWVudCBwb3VyIHRvdXMgY2V1eCBxdWkgc2UgZMOpY291cmFnZW50IGVuIGNoZW1pbi5cblxuQ2UgcHNhdW1lIGZhaXNhaXQgcGFydGllIGRlcyBjYW50aXF1ZXMgY2hhbnTDqXMgcGFyIGxlcyBqdWlmcyBsb3JzIGRlIGxldXJzIHDDqGxlcmluYWdlcyBhbm51ZWxzIMOgIErDqXJ1c2FsZW0gcG91ciBsZXMgZ3JhbmRlcyBmw6p0ZXMgcmVsaWdpZXVzZXM6IFDDonF1ZSwgUGVudGVjw7R0ZSwgZXQgbGEgZsOqdGUgZGVzIGNhYmFuZXMuIENlcyBjYW50aXF1ZXMgcsOpc29ubmFpZW50IMOgIHRyYXZlcnMgbGVzIGNvbGxpbmVzIGV0IGxlcyB2YWxsw6llcywgcmFwcGVsYW50IGF1eCBww6hsZXJpbnMgbGVzIGdyYW5kZXMgdsOpcml0w6lzIGRlIGxhIFBhcm9sZSBkZSBEaWV1LiBQb3VyIGxlcyBjaHLDqXRpZW5zLCBjZSBwc2F1bWUgZXhwcmltZSBub3RyZSB2dWxuw6lyYWJpbGl0w6kgZXQgbm90cmUgYmVzb2luIGRlIHNlY291cnMgZGFucyBub3RyZSBwcm9wcmUgcMOobGVyaW5hZ2UsIG5vbiBwYXMgdmVycyBKw6lydXNhbGVtLCBtYWlzIHZlcnMgbGUgY2llbC5cblxuIyMgTGVzIMOpcHJldXZlcyBkdSBww6hsZXJpblxuXG4+IEplIGzDqHZlIG1lcyB5ZXV4IHZlcnMgbGVzIG1vbnRhZ25lczogZOKAmW/DuSBtZSB2aWVuZHJhIGxlIHNlY291cnM/IExlIHNlY291cnMgbWUgdmllbnQgZGUgbOKAmcOJdGVybmVsLCBxdWkgYSBmYWl0IGxlIGNpZWwgZXQgbGEgdGVycmUuXG4+XG4+IDxzdWI+PGNpdGU+UHNhdW1lcyAxMjEuMS0yPC9jaXRlPjwvc3ViPlxuXG5MZSBww6hsZXJpbiwgZW4gcm91dGUgdmVycyBKw6lydXNhbGVtLCBkZXZhaXQgdHJhdmVyc2VyIGRlcyBtb250YWduZXMgZGFuZ2VyZXVzZXMuIENvbnRyYWlyZW1lbnQgw6Agbm91cyAoZXQgZW4gcGFydGljdWxpZXIgw6AgbW9pLCBncmVub2Jsb2lzKSwgcG91ciBxdWkgbGVzIG1vbnRhZ25lcyBzb250IHNvdXZlbnQgc3lub255bWVzIGRlIGxvaXNpcnMsIGxlcyB0cmF2ZXJzZXIgw6l0YWl0IHBvdXIgZXV4IHVuZSBlbnRyZXByaXNlIHDDqXJpbGxldXNlLCBwbGVpbmUgZGUgZGFuZ2Vycy5cblxuTGVzIGRhbmdlcnMgw6l0YWllbnQgbm9tYnJldXg6IHNlIHBlcmRyZSwgY2h1dGVyIGRhbnMgdW4gcmF2aW4sIHNvdWZmcmlyIGTigJlpbnNvbGF0aW9uLCBhZmZyb250ZXIgZGVzIGNvbmRpdGlvbnMgY2xpbWF0aXF1ZXMgZXh0csOqbWVzLCBvdSBtw6ptZSDDqnRyZSBhdHRhcXXDqSBwYXIgZGVzIGJhbmRpdHMuIExhIG51aXQgYXBwb3J0YWl0IMOpZ2FsZW1lbnQgc29uIGxvdCBk4oCZaW5xdWnDqXR1ZGVzOiBsZSBmcm9pZCwgbGVzIGLDqnRlcyBzYXV2YWdlcywgZXQgbGVzIHBpbGxhcmRzLiBDZXMgbW9udGFnbmVzIHBvdXZhaWVudCBwYXIgYWlsbGV1cnMgw6p0cmUgZGVzIGxpZXV4IGRlIHRlbnRhdGlvbiBzcGlyaXR1ZWxsZS4gTGVzIGN1bHRlcyBwYcOvZW5zIG9mZnJhaWVudCBsZXVycyBzYWNyaWZpY2VzIMOgIGxldXJzIGZhdXggZGlldXggc3VyIGxlcyBoYXV0cyBsaWV1eC4gQ2VsYSBwb3V2YWl0IGFwcGFyYcOudHJlIGNvbW1lIGRlcyBoYXZyZXMgZOKAmWVzcG9pciBzw6lkdWlzYW50cyBvdSBkZXMgc291cmNlcyBkZSBwcsOpb2NjdXBhdGlvbiBpbnRpbWlkYW50ZXMuXG5cbkRhbnMgbm90cmUgdmllIG1vZGVybmUsIG5vdXMgcmVuY29udHJvbnMgYXVzc2kgZGVzIG1vbnRhZ25lcyAobcOpdGFwaG9yaXF1ZXMpIHF1aSBub3VzIHJlbmRlbnQgdnVsbsOpcmFibGVzOiBsYSBzYW50w6ksIGxlIHRyYXZhaWwsIGxlcyBmaW5hbmNlcywgbGVzIHJlbGF0aW9ucywgZXQgYmllbiBk4oCZYXV0cmVzIGRvbWFpbmVzIG/DuSBub3VzIG5vdXMgc2VudG9ucyBlbiBpbnPDqWN1cml0w6kuIExlcyBjcmlzZXMgZHUgWFhJ4bWJIHNpw6hjbGUg4oCUZW52aXJvbm5lbWVudGFsZXMsIHBvbGl0aXF1ZXMsIHNvY2lhbGVz4oCUIGV4YWNlcmJlbnQgY2VzIHNlbnRpbWVudHMgZGUgdnVsbsOpcmFiaWxpdMOpLiBEYW5zIGNlIGNvbnRleHRlLCBsYSB0ZW50YXRpb24gZGUgY2hlcmNoZXIgbGUgc2Vjb3VycyBhaWxsZXVycyBxdeKAmWVuIERpZXUgZXN0IG9tbmlwcsOpc2VudGUuXG5cbiMjIExlIHNlY291cnMgZHUgcMOobGVyaW46IHVuIERpZXUgaW5mYWlsbGlibGUgZXQgaW7DqXB1aXNhYmxlXG5cbkxvcnNxdeKAmW9uIGVzdCBjb25mcm9udMOpIMOgIGRlcyBtb250YWduZXMgaW1wb3NhbnRlcywgaWwgZXN0IG5hdHVyZWwgZGUgc2Ugc2VudGlyIGTDqXBhc3PDqSwgdnVsbsOpcmFibGUsIGV0IG3Dqm1lIGVmZnJhecOpLiBMZSBwc2FsbWlzdGUsIGVuIGxldmFudCBsZXMgeWV1eCB2ZXJzIGNlcyBtb250YWduZXMsIHBvc2UgdW5lIHF1ZXN0aW9uIHF1aSByw6lzb25uZSBwcm9mb25kw6ltZW50IGVuIGNoYXF1ZSDDqnRyZSBodW1haW46XG5cbj4gROKAmW/DuSBtZSB2aWVuZHJhIGxlIHNlY291cnM/XG4+XG4+IDxzdWI+PGNpdGU+UHNhdW1lcyAxMjEuMTwvY2l0ZT48L3N1Yj5cblxuQ2V0dGUgcXVlc3Rpb24gdHJhZHVpdCB1bmUgYW5nb2lzc2UgdW5pdmVyc2VsbGUgcXVlIG5vdXMgcGFydGFnZW9ucyB0b3VzIMOgIGRpZmbDqXJlbnRzIG1vbWVudHMgZGUgbm90cmUgdmllLlxuXG5MZSBtb25kZSBtb2Rlcm5lIG5vdXMgYm9tYmFyZGUgZGUgc29sdXRpb25zIHJhcGlkZXMgcG91ciBhcGFpc2VyIG5vcyBpbnF1acOpdHVkZXM6IGRlcyBsaXZyZXMgZGUgZMOpdmVsb3BwZW1lbnQgcGVyc29ubmVsLCBkZXMgcm91dGluZXMgbWF0aW5hbGVzIHByb21pc2VzIGNvbW1lIGxhIGNsw6kgZHUgc3VjY8Oocywgb3UgZGVzIHLDqWdpbWVzIHF1aSBnYXJhbnRpc3NlbnQgZGUgcsOpc291ZHJlIHRvdXMgbm9zIHByb2Jsw6htZXMgZGUgc2FudMOpLiBFbiBww6lyaW9kZSBkZSBjcmlzZSwgbm91cyBlbnRlbmRvbnMgc291dmVudCBkZXMgcHJvbWVzc2VzIHBvbGl0aXF1ZXMgb3UgaWTDqW9sb2dpcXVlcyBxdWkgc2VtYmxlbnQgb2ZmcmlyIHVuZSBpc3N1ZS4gUG91cnRhbnQsIHRvdXRlcyBjZXMgc29sdXRpb25zLCBiaWVuIHF1ZSBzw6lkdWlzYW50ZXMsIG5lIHBldXZlbnQgbm91cyBhcnJhY2hlciBkZSBub3RyZSBjb25kaXRpb24gZOKAmcOqdHJlcyBodW1haW5zIHZ1bG7DqXJhYmxlcy5cblxuTGUgcHNhbG1pc3RlIG5vdXMgbW9udHJlIHVuZSBhdXRyZSB2b2llLiBJbCBub3VzIGludml0ZSDDoCByZWNvbm5hw650cmUgcXVlIGxlIHNlY291cnMgdsOpcml0YWJsZSBuZSB2aWVudCBuaSBkZSBub3VzLW3Dqm1lcyBuaSBkZSBjZSBxdWUgbGUgbW9uZGUgcGV1dCBvZmZyaXIsIG1haXMgZGUgRGlldSwgbGUgQ3LDqWF0ZXVyIGR1IGNpZWwgZXQgZGUgbGEgdGVycmUuIEPigJllc3QgdW5lIGTDqWNsYXJhdGlvbiBkZSBmb2kgcHVpc3NhbnRlOlxuXG4+IExlIHNlY291cnMgbWUgdmllbnQgZGUgbOKAmcOJdGVybmVsIHF1aSBhIGZhaXQgbGUgY2llbCBldCBsYSB0ZXJyZS5cbj5cbj4gPHN1Yj48Y2l0ZT5Qc2F1bWVzIDEyMS4yPC9jaXRlPjwvc3ViPlxuXG5DZXR0ZSBhZmZpcm1hdGlvbiBub3VzIHJhcHBlbGxlIHF1ZSBEaWV1IGVzdCBzb3V2ZXJhaW4gc3VyIHRvdXRlIGxhIGNyw6lhdGlvbiwgeSBjb21wcmlzIHN1ciBsZXMgbW9udGFnbmVzLlxuXG5EaWV1LCBxdWkgYSBjcsOpw6kgbGVzIG1vbnRhZ25lcywgdmVpbGxlIHN1ciBub3VzLiBJbCBlc3QgZMOpY3JpdCBkYW5zIGxlIFBzYXVtZSBjb21tZSB1biBnYXJkaWVuIGluZmF0aWdhYmxlOlxuXG4+IElsIG5lIHBlcm1ldCBwYXMgw6AgdG9uIHBpZWQgZGUgY2hhbmNlbGVyLCBjZWx1aSBxdWkgdGUgZ2FyZGUgbmUgc29tbWVpbGxlIHBhcy5cbj5cbj4gPHN1Yj48Y2l0ZT5Qc2F1bWVzIDEyMS4zPC9jaXRlPjwvc3ViPlxuXG5Db250cmFpcmVtZW50IMOgIG5vdXMsIHF1aSBzb21tZXMgbGltaXTDqXMgcGFyIG5vdHJlIGJlc29pbiBkZSByZXBvcyBldCBkZSBzb21tZWlsLCBEaWV1IGVzdCBjb25zdGFtbWVudCBlbiBhbGVydGUsIHByb3TDqWdlYW50IGV0IGd1aWRhbnQgc2VzIGVuZmFudHMuIE5vdXMgcG91dm9ucyBub3VzIHJlcG9zZXIgZW4gc2FjaGFudCBxdWUsIG3Dqm1lIGxvcnNxdWUgbm91cyBkb3Jtb25zLCBEaWV1IHZlaWxsZS5cblxuQmllbiBxdWUgbm91cyBwdWlzc2lvbnMgdHLDqWJ1Y2hlciBkYW5zIGxhIHZpZSwgRGlldSBlc3QgbMOgIHBvdXIgbm91cyByZWxldmVyLiBTYSBtYWluIHB1aXNzYW50ZSBub3VzIHNvdXRpZW50LCBub3VzIGd1aWRlLCBldCBub3VzIGFzc3VyZSBxdWUgbm91cyBuZSBub3VzIHBlcmRyb25zIHBhcyBlbiBjaGVtaW4uIExhIHByb21lc3NlIHF1ZSBEaWV1IG5lIHBlcm1ldHRyYSBwYXMgw6Agbm90cmUgcGllZCBkZSBjaGFuY2VsZXIgc2lnbmlmaWUgcXXigJlpbCBub3VzIHByb3TDqGdlIGRlIGxhIGNodXRlIGZhdGFsZSwgY2VsbGUgcXVpIHBvdXJyYWl0IG5vdXMgw6lsb2lnbmVyIGTDqWZpbml0aXZlbWVudCBkZSBsdWkuXG5cbkxlIHBzYXVtZSAxMjEgZMOpY3JpdCDDqWdhbGVtZW50IERpZXUgY29tbWUgbm90cmUgb21icmUgcHJvdGVjdHJpY2U6XG5cbj4gTOKAmcOJdGVybmVsIGVzdCBjZWx1aSBxdWkgdGUgZ2FyZGUsIGzigJnDiXRlcm5lbCBlc3QgdG9uIG9tYnJlIHByb3RlY3RyaWNlLCBpbCBzZSB0aWVudCDDoCB0YSBkcm9pdGUuXG4+XG4+IDxzdWI+PGNpdGU+UHNhdW1lcyAxMjEuNTwvY2l0ZT48L3N1Yj5cblxuRGFucyBsZSBjbGltYXQgYXJpZGUgZHUgTW95ZW4tT3JpZW50LCBs4oCZb21icmUgZXN0IHVuZSBpbWFnZSBmb3J0ZSBkZSBwcm90ZWN0aW9uIGNvbnRyZSBsYSBjaGFsZXVyIGFjY2FibGFudGUgZHUgc29sZWlsLiBEZSBsYSBtw6ptZSBtYW5pw6hyZSwgRGlldSBub3VzIHByb3TDqGdlIGRlcyBcImNoYWxldXJzXCIgYWNjYWJsYW50ZXMgZGUgbm9zIMOpcHJldXZlcyBxdW90aWRpZW5uZXMuIElsIGVzdCDDoCBub3MgY8O0dMOpcywgdG91am91cnMgcHLDqXNlbnQsIHByw6p0IMOgIG5vdXMgYWJyaXRlciBzb3VzIHNvbiBhaWxlIHByb3RlY3RyaWNlLlxuXG4+IFBlbmRhbnQgbGUgam91ciwgbGUgc29sZWlsIG5lIHRlIGZlcmEgcGFzIGRlIG1hbCwgbmkgbGEgbHVuZSBwZW5kYW50IGxhIG51aXQuXG4+XG4+IDxzdWI+PGNpdGU+UHNhdW1lcyAxMjEuNjwvY2l0ZT48L3N1Yj5cblxuQ2UgdmVyc2V0IHV0aWxpc2UgbGEgcG/DqXNpZSBow6licmHDr3F1ZSBwb3VyIGV4cHJpbWVyIHVuZSBwcm90ZWN0aW9uIGNvbXBsw6h0ZSwgam91ciBldCBudWl0LiBDZWxhIHNpZ25pZmllIHF1ZSBEaWV1IG5vdXMgcHJvdMOoZ2UgZW4gdG91dGVzIGNpcmNvbnN0YW5jZXMsIMOgIHRvdXQgbW9tZW50LiBSaWVuIG7igJnDqWNoYXBwZSDDoCBsYSBwcm92aWRlbmNlIGR1IERpZXUgYmllbnZlaWxsYW50LlxuXG5FbmZpbiwgbGUgcHNhbG1pc3RlIMOpbGFyZ2l0IGNldHRlIHByb21lc3NlIGRlIHByb3RlY3Rpb24gw6AgdG91dGUgbGEgdmllOlxuXG4+IEzigJnDiXRlcm5lbCB0ZSBnYXJkZXJhIGRlIHRvdXQgbWFsLCBpbCBnYXJkZXJhIHRhIHZpZS4gTOKAmcOJdGVybmVsIGdhcmRlcmEgdG9uIGTDqXBhcnQgZXQgdG9uIGFycml2w6llIGTDqHMgbWFpbnRlbmFudCBldCBwb3VyIHRvdWpvdXJzLlxuPlxuPiA8c3ViPjxjaXRlPlBzYXVtZXMgMTIxLjctODwvY2l0ZT48L3N1Yj5cblxuQ2V0dGUgYXNzdXJhbmNlIGRpdmluZSBu4oCZZXN0IHBhcyBsaW1pdMOpZSBkYW5zIGxlIHRlbXBzIG91IGRhbnMgbOKAmWVzcGFjZS4gRWxsZSBlbmdsb2JlIGNoYXF1ZSBhc3BlY3QgZGUgbm90cmUgZXhpc3RlbmNlLCBkZSBub3RyZSBkw6lwYXJ0IGp1c3F14oCZw6Agbm90cmUgYXJyaXbDqWUsIGV0IGVsbGUgc+KAmcOpdGVuZCBtw6ptZSBkYW5zIGzigJnDqXRlcm5pdMOpLlxuXG7DgCBsYSBsdW1pw6hyZSBkZSBs4oCZZW5zZWlnbmVtZW50IGR1IHJlc3RlIGRlcyDDiWNyaXR1cmVzLCBjZXR0ZSBwcm9tZXNzZSBuZSBzaWduaWZpZSBwYXMgcXVlIG5vdHJlIHZpZSBzZXJhIGV4ZW1wdGUgZGUgZGlmZmljdWx0w6lzIG91IGRlIHNvdWZmcmFuY2VzLCBtYWlzIHF1ZSBEaWV1IG5vdXMgZG9ubmVyYSBsZXMgcmVzc291cmNlcyBuw6ljZXNzYWlyZXMgcG91ciBzdXJtb250ZXIgbGVzIG1vbnRhZ25lcyBkZSBsYSB2aWUuIEVuIGx1aSwgbm91cyB0cm91dm9ucyBsYSBmb3JjZSwgbGUgY291cmFnZSwgZXQgbGEgcGVyc8OpdsOpcmFuY2UgcG91ciBjb250aW51ZXIgbm90cmUgcMOobGVyaW5hZ2UsIGVuIHNhY2hhbnQgcXVlIG5vdHJlIGRlc3RpbmF0aW9uIGZpbmFsZSBlc3QgYXNzdXLDqWUuXG5cbiMjIExhIGNvbW11bmF1dMOpIGRlcyBww6hsZXJpbnNcblxuYDxwPkrigJlhaW1lIGxpcmUgPGEgaHJlZj1cImh0dHBzOi8vYmxmc3RvcmUuY29tL3NlYXJjaD9xPUxlJTIwVm95YWdlJTIwZHUlMjBQJUMzJUE4bGVyaW5cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+PGVtPkxlIHZveWFnZSBkdSBww6hsZXJpbjwvZW0+PC9hPiwgY2V0IGltbWVuc2UgY2xhc3NpcXVlIGRlIEpvaG4gQnVueWFuIGR1IFhWSUnhtYkgc2nDqGNsZS4gQ+KAmWVzdCB1bmUgYWxsw6lnb3JpZSBkZSBsYSB2aWUgY2hyw6l0aWVubmUuIERhbnMgY2UgbGl2cmUsIENocsOpdGllbiBlc3QgdW4gaG9tbWUgcXVpIHBvcnRlIHVuIGxvdXJkIGZhcmRlYXUgZXQgcXVpIHBhcnQgZW4gdm95YWdlIHBvdXIgbGUgZMOpcG9zZXIgw6AgbGEgY3JvaXgsIHB1aXMgc2UgcmVuZHJlIMOgIFNpb24sIGxhIGNpdMOpIGPDqWxlc3RlLiBTb24gcGFyY291cnMgZXN0IHNlbcOpIGRlIGRhbmdlcnM6IHZhbGzDqWUgZGUgbGEgZMOpc29sYXRpb24sIG1vbnQgZGVzIMOpcHJldXZlc+KApjwvcD5gXG5cbkzigJlpbWFnaW5hdGlvbiBwdWlzc2FudGUgZXQgbGEgcGVyc3BpY2FjaXTDqSBzcGlyaXR1ZWxsZSBkZSBCdW55YW4gb250IGFpZMOpIGRlcyBtaWxsaW9ucyBkZSBjaHLDqXRpZW5zIMOgIHRyYXZlcnMgbGVzIMOiZ2VzIMOgIGNvbXByZW5kcmUgY2UgcXVlIHNvbnQgbGVzIHRlbnRhdGlvbnMsIGxlcyDDqXByZXV2ZXMgZXQgbGEgZ3LDomNlLiBKZSBs4oCZYWkgbHUgcG91ciBtb2ktbcOqbWUsIG1haXMgYXVzc2kgw6AgY2hhY3VuIGRlIG1lcyBlbmZhbnRzLlxuXG5TaSBq4oCZb3NlIGZhaXJlIHVuIHJlcHJvY2hlIMOgIGNldHRlIMWTdXZyZSBtYWpldXJlIGRlIGzigJloaXN0b2lyZSBkZSBs4oCZw4lnbGlzZSwgY+KAmWVzdCBxdWUgQ2hyw6l0aWVuIHDDqGxlcmluZSBlc3NlbnRpZWxsZW1lbnQgc2V1bC4gTOKAmcOJZ2xpc2UgZXN0IGFic2VudGUuIFBvdXJ0YW50LCBkYW5zIGxlIHBzYXVtZSAxMjEsIG5vdXMgb2JzZXJ2b25zIHVuZSBpbnRlcmFjdGlvbiBlbnRyZSBkZXV4IHBlcnNvbm5lczpcblxuPiBKZSBsw6h2ZSBsZXMgeWV1eCB2ZXJzIGxlcyBtb250YWduZXM6IGTigJlvw7kgbWUgdmllbmRyYSBsZSBzZWNvdXJzP1xuPlxuPiA8c3ViPjxjaXRlPlBzYXVtZXMgMTIxLjE8L2NpdGU+PC9zdWI+XG5cbkVuc3VpdGUsIHVuZSBhdXRyZSB2b2l4IHLDqXBvbmQ6XG5cbj4gUXXigJlpbCBuZSBwZXJtZXR0ZSBwYXMgw6AgdG9uIHBpZWQgZGUgdHLDqWJ1Y2hlciwgcXXigJlpbCBuZSBzb21ub2xlIHBhcywgY2VsdWkgcXVpIHRlIGdhcmRlIVxuPlxuPiA8c3ViPjxjaXRlPlBzYXVtZXMgMTIxLjM8L2NpdGU+PC9zdWI+XG5cbkxlcyBww6hsZXJpbnMgbmUgdm95YWdlYWllbnQgcGFzIHNldWxzOyBpbHMgc+KAmWV4aG9ydGFpZW50IGV0IHNlIHJhc3N1cmFpZW50IGxlcyB1bnMgbGVzIGF1dHJlcy4gTGEgY29tbXVuYXV0w6kgbG9jYWxlLCBs4oCZw4lnbGlzZSwgZXN0IGxlIGxpZXUgb8O5IGzigJlvbiBhcHByZW5kIMOgIG1hcmNoZXIgZW5zZW1ibGUuIEF1Y3VuIGNocsOpdGllbiBu4oCZZXN0IGFwcGVsw6kgw6AgZmFpcmUgbGUgcMOobGVyaW5hZ2UgdG91dCBzZXVsLCBldCBhdWN1biBuZSBkZXZyYWl0IGxlIGZhaXJlLiBOb3VzIGF2b25zIGJlc29pbiBkZXMgYXV0cmVzIHN1ciBsYSByb3V0ZSwgY2FyIERpZXUgbm91cyBzZWNvdXJ0IHNvdXZlbnQgw6AgdHJhdmVycyBldXgsIHF1aSBzZXJvbnQgbMOgIHBvdXIgbm91cyBwb3J0ZXIgZXQgbm91cyByYXBwZWxlciBkZSByZWdhcmRlciBhdS1kZWzDoCBkZXMgbW9udGFnbmVzLiBOb3VzIHNvbW1lcyByZXNwb25zYWJsZXMgZGUgdmVpbGxlciBsZXMgdW5zIHN1ciBsZXMgYXV0cmVzLlxuXG4jIyBM4oCZdWx0aW1lIHDDqGxlcmluOiBKw6lzdXMtQ2hyaXN0IGV0IGxlIHDDqGxlcmluYWdlIHZlcnMgR29sZ290aGFcblxuTGUgcHNhdW1lIDEyMSwgY2hhbnTDqSBwYXIgbGVzIHDDqGxlcmlucyBqdWlmcyBsb3JzIGRlIGxldXJzIHZveWFnZXMgdmVycyBKw6lydXNhbGVtLCBhIHRyw6hzIHByb2JhYmxlbWVudCDDqXTDqSBjaGFudMOpIHBhciBKw6lzdXMuIFRvdXQgYXUgbG9uZyBkZSBzYSB2aWUgdGVycmVzdHJlLCBKw6lzdXMgYSBhY2NvbXBsaSBjZXMgcMOobGVyaW5hZ2VzIHZlcnMgSsOpcnVzYWxlbSwgcGFydGljaXBhbnQgYXV4IGbDqnRlcyByZWxpZ2lldXNlcyBhdmVjIHNhIGZhbWlsbGUgZXQgc2VzIGRpc2NpcGxlcy4gTWFpcyBpbCBzYXZhaXQgcXVlIGNlcyB2b3lhZ2VzIHZlcnMgbGEgY2l0w6kgc2FpbnRlIG7igJnDqXRhaWVudCBxdeKAmXVuZSBvbWJyZSBk4oCZdW5lIHLDqWFsaXTDqSBzcGlyaXR1ZWxsZSBiaWVuIHBsdXMgZ3JhbmRlLCB1biBww6hsZXJpbmFnZSBiaWVuIHBsdXMgZGlmZmljaWxlIGV0IHNpZ25pZmljYXRpZiBxdeKAmWlsIGRldmFpdCBlbnRyZXByZW5kcmUuXG5cblVuIGpvdXIsIErDqXN1cyBhIGVudHJlcHJpcyBsZSBww6hsZXJpbmFnZSB1bHRpbWUsIG5vbiBwYXMgcG91ciBjw6lsw6licmVyIHVuZSBmw6p0ZSwgbWFpcyBwb3VyIGFjY29tcGxpciBsYSAgcsOpZGVtcHRpb24gZGUgc29uIHBldXBsZS4gSWwgZXN0IG1vbnTDqSDDoCBKw6lydXNhbGVtLCBub24gcG91ciBsb3VlciBhdSB0ZW1wbGUsIG1haXMgcG91ciBzb3VmZnJpciDDoCBHb2xnb3RoYS4gQ2UgbuKAmcOpdGFpdCBwYXMgdW4gdm95YWdlIGpveWV1eCBhY2NvbXBhZ27DqSBkZSBjYW50aXF1ZXMsIG1haXMgdW4gY2hlbWluIGRlIHNvdWZmcmFuY2UgZXQgZGUgc2FjcmlmaWNlLlxuXG5JbCBuZSBwb3J0YWl0IHBhcyBzb24gc2FjIMOgIGRvcywgbWFpcyBzYSBjcm9peCBzdXIgbGFxdWVsbGUgaWwgc2VyYWl0IGNydWNpZmnDqS4gSsOpc3VzIGEgYWZmcm9udMOpIGxhIG1vbnRhZ25lIGxhIHBsdXMgcmVkb3V0YWJsZSBxdWUgbOKAmWh1bWFuaXTDqSBhaXQgamFtYWlzIGNvbm51ZTogbGEganVzdGUgY29sw6hyZSBkZSBEaWV1IGNvbnRyZSBsZSBww6ljaMOpLiBTb24gcMOobGVyaW5hZ2UgdmVycyBsYSBjcm9peCDDqXRhaXQgdW4gYWN0ZSBk4oCZb2LDqWlzc2FuY2UgZXQgZOKAmWFtb3VyIHVsdGltZSwgYWNjb21wbGlzc2FudCBsYSBtaXNzaW9uIHBvdXIgbGFxdWVsbGUgaWwgw6l0YWl0IHZlbnUgc3VyIHRlcnJlLlxuXG5TdXIgY2V0dGUgbW9udGFnbmUsIErDqXN1cyBhIGV4cMOpcmltZW50w6kgbOKAmWFiYW5kb24gbGUgcGx1cyB0b3RhbC4gQ29udHJhaXJlbWVudCBhdSBwc2FsbWlzdGUgcXVpIHByb2NsYW1haXQgcXVlIERpZXUgbmUgc29tbWVpbGxlIG5pIG5lIGRvcnQsIErDqXN1cyBhIGNvbm51IHVuIG1vbWVudCBvw7kgaWwgc+KAmWVzdCDDqWNyacOpOiAq4oCcTW9uIERpZXUsIG1vbiBEaWV1LCBwb3VycXVvaSBt4oCZYXMtdHUgYWJhbmRvbm7DqT/igJ0qIChNdCAyNy40NikuIExlIFDDqHJlIGEgZMOpdG91cm7DqSBzb24gcmVnYXJkLCBsYWlzc2FudCBKw6lzdXMgcG9ydGVyIHNldWwgbGUgcG9pZHMgZHUgcMOpY2jDqSBkdSBtb25kZSBkYW5zIGxlcyB0w6luw6hicmVzLiBJbCBhIGJ1IGxhIGNvdXBlIGRlIGxhIGNvbMOocmUgZGl2aW5lIHBvdXIgcXVlIG5vdXMgcHVpc3Npb25zLCBub3VzLCDDqWNoYXBwZXIgw6AgY2UganVnZW1lbnQuXG5cblNvbiBwaWVkIGEgY2hhbmNlbMOpIGV0IGEgw6l0w6kgcGVyY8OpIHBvdXIgcXVlIGxlIG7DtHRyZSBuZSBjaGFuY2VsbGUgamFtYWlzLiBKw6lzdXMgYSB0csOpYnVjaMOpIHNvdXMgbGUgcG9pZHMgZGUgbGEgY3JvaXgsIG1haXMgaWwgYSBjb250aW51w6ksIGTDqXRlcm1pbsOpIMOgIGFjY29tcGxpciBub3RyZSBzYWx1dC4gTGEgbnVpdCBvcHByZXNzYW50ZSBxdWkgc+KAmWVzdCBhYmF0dHVlIHN1ciBsdWkgZW4gcGxlaW4gam91ciDDqXRhaXQgbGUgc3ltYm9sZSBkZSBs4oCZYWJhbmRvbiBkaXZpbiwgdW4gcHJpeCBxdeKAmWlsIGEgcGF5w6kgcG91ciBxdWUgbm91cyBzb3lvbnMgw6AgamFtYWlzIHByb3TDqWfDqXMgcGFyIGzigJlhbW91ciBkZSBEaWV1LlxuXG5QYXIgc2EgbW9ydCBldCBzYSByw6lzdXJyZWN0aW9uLCBKw6lzdXMgYSBhc3N1csOpIGxhIHZpY3RvaXJlIGZpbmFsZSBzdXIgbGUgcMOpY2jDqSBldCBsYSBtb3J0LiBHcsOiY2Ugw6AgbHVpLCBub3VzIGF2b25zIGzigJlhc3N1cmFuY2UgcXVlICps4oCZw4l0ZXJuZWwgZ2FyZGVyYSBub3RyZSB2aWUgZMOocyBtYWludGVuYW50IGV0IHBvdXIgdG91am91cnMqIChQcyAxMjEuOCkuIFNvbiBww6hsZXJpbmFnZSBqdXNxdeKAmcOgIGxhIGNyb2l4IGEgb3V2ZXJ0IHBvdXIgbm91cyB1biBjaGVtaW4gdmVycyBsYSB2aWUgw6l0ZXJuZWxsZSwgdW5lIHJvdXRlIHF1ZSBub3VzIHNvbW1lcyBhcHBlbMOpcyDDoCBzdWl2cmUuXG5cbkrDqXN1cyBlc3Qgbm9uIHNldWxlbWVudCBub3RyZSBtb2TDqGxlLCBtYWlzIGF1c3NpIGxlIG1hw650cmUgcXVlIG5vdXMgc3Vpdm9ucyBzdXIgbGUgY2hlbWluLiBTb24gcMOobGVyaW5hZ2Ugbm91cyBhIGTDqXZpw6lzIGRlIG5vdHJlIGVycmFuY2UgcG91ciBub3VzIGNvbmR1aXJlIHN1ciBsZSBjaGVtaW4gZGUgbGEgdmllLCBub3VzIGRvbm5hbnQgbOKAmWFzc3VyYW5jZSBxdWUgbm91cyBuZSBzb21tZXMgamFtYWlzIHNldWxzIHN1ciBjZSBjaGVtaW4sIGV0IHF1ZSBub3RyZSBkZXN0aW5hdGlvbiBmaW5hbGUgZXN0IHPDu3JlLlxuXG4jIyBcIkNlIHF1aSBjb21wdGUsIGNlIG7igJllc3QgcGFzIGxhIGRlc3RpbmF0aW9uLCBj4oCZZXN0IGxlIGNoZW1pbi5cIiBWcmFpbWVudD9cblxuQ2V0IGFkYWdlIHR5cGlxdWVtZW50IGjDqWRvbmlzdGUgbmUgY29uZHVpdCBxdeKAmcOgIGzigJnDqWdhcmVtZW50IGRlIGNldXggcXVpIHRlbnRlbnQgZGUgbGUgc3VpdnJlLiBRdWFuZCBsZSBjaGVtaW4gZGV2aWVudCB1biBib3VyYmllciBleGlzdGVudGllbCwgbGEgcXVlc3Rpb24gZHUgc2VucyBkZSBsYSB2aWUgZXQgZGUgc2EgZmluYWxpdMOpIGVzdCBjZSBxdWkgdm91cyBhaWRlIMOgIHN1cnZpdnJlLiBRdWFuZCBsZXMgw6lwcmV1dmVzIG5vdXMgZm9udCBkw6l2aWVyIGRlIG5vcyBwcm9qZXRzIGluaXRpYXV4LCBub3VzIHNvbW1lcyBjb21tZSB1bmUgc29uZGUgc3BhdGlhbGUgcXVpIHNlIHBlcmQgZGFucyBs4oCZdW5pdmVycyBzYW5zIGphbWFpcyBzYXZvaXIgb8O5IGVsbGUgdmEuIE5vdXMgc29tbWVzIHRvdXMgZW4gY2hlbWluLCBldCBs4oCZaW1wb3J0YW50LCBjJ2VzdCBk4oCZw6p0cmUgc8O7ciBkZSBzb24gZ3VpZGUgZXQgZGUgc2EgZGVzdGluYXRpb24uXG5cbkrDqXN1cyBlc3QgY2xhaXI6IGlsIG7igJlleGlzdGUgcXVlIGRldXggY2hlbWlucy4gTOKAmXVuIGNvbmR1aXQgw6AgbGEgcGVyZGl0aW9uLCBs4oCZYXV0cmUgw6AgbGEgdmllLiBKw6lzdXMgbm91cyByw6l2w6hsZSBxdeKAmWlsIGVzdCAqbGUgY2hlbWluLCBsYSB2w6lyaXTDqSwgZXQgbGEgdmllKiAoSm4gMTQuNikuIElsIGVzdCBsZSBzZXVsIGNoZW1pbiBxdWkgbcOobmUgYXUgUMOocmUsIGV0IGlsIG5vdXMgYXBwZWxsZSDDoCBsZSBzdWl2cmUuXG5cbkVuIHRhbnQgcXVlIGNocsOpdGllbnMsIG5vdXMgc29tbWVzIGFwcGVsw6lzIMOgIMOqdHJlIGRlcyBww6hsZXJpbnMgc3VyIGNldHRlIHRlcnJlLCBkZXMgKsOpdHJhbmdlcnMgZXQgdm95YWdldXJzIHN1ciBsYSB0ZXJyZSogKEjDqSAxMS4xMykuIE1haXMgY2UgcMOobGVyaW5hZ2UgbuKAmWVzdCBwYXMgc2FucyBlc3BvaXIgbmkgZGVzdGluYXRpb24sIGNhciBub3VzIHNhdm9ucyBxdWUsIGdyw6JjZSDDoCBDaHJpc3QsIG5vdXMgYXR0ZWluZHJvbnMgdW4gam91ciBsYSBKw6lydXNhbGVtIGPDqWxlc3RlLCBvw7kgbm91cyBzZXJvbnMgZW5maW4gY2hleiBub3VzLCByw6lzaWRlbnRzIHBlcm1hbmVudHMgZHUgcm95YXVtZSBkZSBEaWV1LlxuXG5FbiBjaGVtaW5hbnQgw6AgdHJhdmVycyBsZXMgbW9udGFnbmVzIGRlIG5vcyBkaWZmaWN1bHTDqXMgcHLDqXNlbnRlcywgZml4b25zIGxlcyByZWdhcmRzIHN1ciBjZWxsZSBkZSBHb2xnb3RoYSwgb8O5IGzigJnDiXRlcm5lbCBub3VzIGEgc2Vjb3VydXMuIERlIHNvbiBzb21tZXQsIG5vdXMgdm95b25zIHBhciBsYSBmb2kgbGEgbW9udGFnbmUgZGUgbGEgSsOpcnVzYWxlbSBjw6lsZXN0ZSwgbm90cmUgdsOpcml0YWJsZSBtYWlzb24uIE5vdXMgeSBzZXJvbnMgYmllbnTDtHQhIEzDoCwgbm90cmUgcMOobGVyaW5hZ2UgcHJlbmRyYSBmaW4sIGV0IG5vdXMgc2Vyb25zIGVuIHPDqWN1cml0w6kgcG91ciB0b3Vqb3Vycywgc291cyBsYSBwcm90ZWN0aW9uIGR1IERpZXUgcXVpIG5lIHNvbW1laWxsZSBuaSBuZSBkb3J0LiIsImF1dGhvciI6eyJmdWxsTmFtZSI6IlJhcGhhw6tsIENoYXJyaWVyIiwicGljdHVyZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L3JhcGhhZWxfY2hhcnJpZXJfMDNfY29waWVfMV9zY2FsZWRfZTE2NTc4MDEyNTc3ODdfZjkzNTk5NDAzOC5qcGciLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9fSwiaW1hZ2UiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9ub21hZF85MzEwZjViMmQ5LmpwZyIsImhlaWdodCI6MTU5OCwid2lkdGgiOjIxOTYsImFsdGVybmF0aXZlVGV4dCI6IiIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn0sInRvcGljcyI6W3sibmFtZSI6IlZpZSBjaHLDqXRpZW5uZSJ9LHsibmFtZSI6IlNvdWZmcmFuY2UifSx7Im5hbWUiOiJWaXNpb24gY2hyw6l0aWVubmUgZHUgbW9uZGUifV0sIm1vZHVsZXMiOlt7Il9fdHlwZW5hbWUiOiJDb21wb25lbnRNb2R1bGVMZWFkIiwiY29udGVudCI6IkxhIHZpZSBlc3Qgc291dmVudCBjb21wYXLDqWUgw6AgdW4gdm95YWdlLCB1biBww6hsZXJpbmFnZSBxdWkgbm91cyBwb3Vzc2Ugw6AgdHJhdmVyc2VyIGRlcyBtb250YWduZXMsIMOgIHN1cm1vbnRlciBkZXMgb2JzdGFjbGVzLCBldCDDoCBhdHRlaW5kcmUgZGVzIHNvbW1ldHMuIE5vdXMgZXNww6lyb25zIHRvdXMgYXJyaXZlciDDoCBib24gcG9ydCwgbWFpcyBlbiBjaGVtaW4sIGlsIG7igJllc3QgcGFzIHJhcmUgZGUgc2UgcGVyZHJlLCBkZSB0csOpYnVjaGVyLCBvdSBkZSBzZSBzZW50aXIgYWNjYWJsw6kgcGFyIGxlcyDDqXByZXV2ZXMuIEPigJllc3QgdW4gcGV1IFwidGFydGUgw6AgbGEgY3LDqG1lXCIsIGplIHZvdXMgbOKAmWFjY29yZGUsIG1haXMgbGEgbcOpdGFwaG9yZSBlc3QgZWZmaWNhY2UuIn0se31dfSx7ImlkIjoiMTgxMCIsInRpdGxlIjoiNSBwcm9tZXNzZXMgZGUgSsOpc3VzLUNocmlzdCBhdSBzdWpldCBkZSBsYSBtb3J0IGR1IGNocsOpdGllbiIsInNsdWciOiJxdWUtc2UtcGFzc2UtdGlsLWF1LW1vbWVudC1kZS1sYS1tb3J0LWR1LWNocmV0aWVuLTUtcHJvbWVzc2VzLWRlLWplc3VzLWNocmlzdCIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyNC0wOC0zMFQwMzo0NTowMC4wMDBaIiwiYm9keSI6Ik9tbmlwcsOpc2VudGUgZGFucyBsZXMgbcOpZGlhcyBldCBhc2VwdGlzw6llIGRhbnMgbm90cmUgcsOpYWxpdMOpIG9jY2lkZW50YWxlLCBub3VzIGxhIGNvbnNpZMOpcm9ucyBwcmVzcXVlIGNvbW1lIGRlIGxhIGZpY3Rpb24uIE9uIHNhaXQgcXVlIMOnYSB2YSBhcnJpdmVyLCBtYWlzIG9uIHZpdCBjb21tZSBzaSBlbGxlIG7igJlleGlzdGFpdCBwYXMuXG5cbkrDqXN1cyBwYXNzYWl0IGR1IHRlbXBzIMOgIHBhcmxlciBkZSBzYSBtb3J0LiBFbiBKZWFuwqAxNC4xLTQsIErDqXN1cyBhIGV4cGxpcXXDqSDDoCBzZXMgZGlzY2lwbGVzIHBvdXJxdW9pIGlsIGRldmFpdCBzZSBzw6lwYXJlciBk4oCZZXV4OlxuXG4+IDxzdXA+MSA8L3N1cD5RdWUgdm90cmUgY8WTdXIgbmUgc2UgdHJvdWJsZSBwYXMuIENyb3lleiBlbiBEaWV1LCBjcm95ZXogYXVzc2kgZW4gbW9pLjxzdXA+MiA8L3N1cD5JbCB5IGEgYmVhdWNvdXAgZGUgZGVtZXVyZXMgZGFucyBsYSBtYWlzb24gZGUgbW9uIFDDqHJlLiBTaW5vbiwgamUgdm91cyBs4oCZYXVyYWlzIGRpdDsgY2FyIGplIHZhaXMgdm91cyBwcsOpcGFyZXIgdW5lIHBsYWNlLiA8c3VwPjMgPC9zdXA+XG4+XG4+IFNpIGRvbmMgamUgbeKAmWVuIHZhaXMgZXQgdm91cyBwcsOpcGFyZSB1bmUgcGxhY2UsIGplIHJldmllbmRyYWkgZXQgamUgdm91cyBwcmVuZHJhaSBhdmVjIG1vaSwgYWZpbiBxdWUgbMOgIG/DuSBqZSBzdWlzLCB2b3VzIHkgc295ZXogYXVzc2kuIDxzdXA+NCA8L3N1cD5Mw6Agb8O5IGplIHZhaXMsIHZvdXMgZW4gc2F2ZXogbGUgY2hlbWluLlxuPlxuPiA8c3ViPjxjaXRlPkplYW7CoDE0LjEtNDwvY2l0ZT48L3N1Yj5cblxuSsOpc3VzIG5lIHPigJlhZHJlc3NlIHBhcyB1bmlxdWVtZW50IGF1eCBkaXNjaXBsZXMgcHLDqXNlbnRzLiDDgCB0cmF2ZXJzIGNlIHRleHRlLCBpbCBwYXJsZSDDoCB0b3VzIGxlcyBlbmZhbnRzIGRlIERpZXUgZGVwdWlzIGRldXggbWlsbMOpbmFpcmVzLlxuXG5QYXJmb2lzLCBsb3JzcXVlIGrigJl5IHBlbnNlLCBsYSB0ZXJyZXVyIG3igJllbnZhaGl0LiBNYWlzLCBkZXMgcGFyb2xlcyBkZSBKw6lzdXMsIGplIGTDqWdhZ2UgY2lucSBwcm9tZXNzZXMgcXVpIG5vdXJyaXNzZW50IG1vbiBlc3DDqXJhbmNlIGV0IG1lIHJhc3N1cmVudCBmYWNlIMOgIG1hIG1vcnQgZnV0dXJlOlxuXG4jIyAxLiBK4oCZYXVyYWkgdW5lIHBsYWNlIGF1cHLDqHMgZHUgUMOocmVcblxuSsOpc3VzIHJhcHBlbGxlIHF1ZSBsYSB2aWUgw6l0ZXJuZWxsZSBzZSBmYWl0IGRhbnMgbGEgbWFpc29uIGR1IFDDqHJlIGV0IG5vbiBkYW5zIHVuIGxpZXUgaW1wZXJzb25uZWwuIENlcyBub21icmV1c2VzIGRlbWV1cmVzIGTDqXNpZ25lbnQgbOKAmWluZmluaWUgYm9udMOpIGRlIERpZXUsIGdyw6JjZSDDoCBsYXF1ZWxsZSBpbCB5IGEgdW5lIHBsYWNlIHBvdXIgY2hhY3VuIGRlIHNlcyBlbmZhbnRzLlxuXG5NYSBtYWlzb24gbuKAmWVzdCBwYXMgaWNpLWJhcy4gSmUgbmUgc3VpcyBxdWUgZGUgcGFzc2FnZS4gTW9uIHZyYWkgZm95ZXIgZXN0IGNlbHVpIGR1IFDDqHJlLlxuXG4jIyAyLiBD4oCZZXN0IErDqXN1cyBsdWktbcOqbWUgcXVpIHByw6lwYXJlIGNldHRlIHBsYWNlXG5cbj4gSmUgdmFpcyB2b3VzIHByw6lwYXJlciB1bmUgcGxhY2UuXG5cblBlcnNvbm5lIG5lIHBldXQgcHLDqXBhcmVyIHNhIHByb3ByZSBwbGFjZSBkYW5zIGxhIG1haXNvbiBkdSBQw6hyZS4gQXVjdW4gZGUgbm91cyBu4oCZZW4gYSBsZXMgbW95ZW5zLiBBdWN1biBkZSBub3VzIG5lIHNhdXJhaXQgbGUgZmFpcmUuIEF1Y3VuIGRlIG5vdXMgbmUgbGUgcG91cnJhaXQsIGNhciBhdWN1biBkZSBub3VzIG7igJlhIGFjY8OocyBhdSBQw6hyZSBlbiBkZWhvcnMgZGUgbOKAmcWTdXZyZSBkZSBKw6lzdXMuXG5cbkxlcyBwcsOpcGFyYXRpZnMgZGUgY2V0dGUgcGxhY2Ugc2Ugc29udCBlZmZlY3R1w6lzIHN1ciBsYSBjcm9peCwgbG9yc3F1ZSBKw6lzdXMgYSBvZmZlcnQgc2EgdmllIGF1IFDDqHJlIHBvdXIgbGUgcGFyZG9uIGRlIG5vcyBww6ljaMOpcy4gUmVzc3VzY2l0w6ksIGlsIGVzdCBjZWx1aSBxdWkgbeKAmW91dnJpcmEgbGEgcG9ydGUgZGUgbGEgZGVtZXVyZSBkdSBQw6hyZS5cblxuRXhpc3RlLXQtaWwgdW5lIHBsdXMgZ3JhbmRlIGFzc3VyYW5jZSBxdWUgZGUgc2F2b2lyIGNldHRlIHBsYWNlIHByw6lwYXLDqWUgcGFyIGxlIGNyw6lhdGV1ciBkZSB0b3V0ZXMgY2hvc2VzIGV0IGzigJlhdXRldXIgZGUgbW9uIHNhbHV04oCJP1xuXG4jIyAzLiBD4oCZZXN0IErDqXN1cyBsdWktbcOqbWUgcXVpIG3igJl5IGNvbmR1aXJhIGxlIG1vbWVudCB2ZW51XG5cbj4gSmUgcmV2aWVuZHJhaSBldCBqZSB2b3VzIHByZW5kcmFpIGF2ZWMgbW9pLlxuXG5DZWx1aSBxdWkgYSB2YWluY3UgbGEgbW9ydCBub3VzIHByb21ldCBkZSBub3VzIGFpZGVyIGx1aS1tw6ptZSDDoCBs4oCZYWZmcm9udGVyIHBhciBsYSBmb2kuIErDqXN1cyBt4oCZYWlkZXJhIMOgIGFmZnJvbnRlciBtb24gZGVybmllciBlbm5lbWkgcGFyIGxhIGZvaS4gVW5lIGZvaXMgdmFpbmN1LCBj4oCZZXN0IErDqXN1cyBxdWUgamUgdmVycmFpIGVuIHRvdXQgcHJlbWllci5cblxuUXXigJl5IGEtdC1pbCBhcHLDqHMgbGEgbW9ydD8gTm90cmUgcHJlbWnDqHJlIHLDqXBvbnNlIGVzdDogSsOpc3VzISBQYXMgZGVzIGFuZ2VzLCBwYXMgc2FpbnQtUGllcnJlLCBKw6lzdXMgbHVpLW3Dqm1lIVxuXG4jIyA0LiBKZSBzZXJhaSBhdSBwbHVzIHByw6hzIGRlIGxhIHByw6lzZW5jZSBkZSBEaWV1XG5cbj4gQWZpbiBxdWXCoGzDoCBvw7kgamUgc3Vpcywgdm91cyB5IHNveWV6IGF1c3NpLlxuXG5M4oCZw6l0ZXJuaXTDqSBlc3QgYXUgcGx1cyBwcsOocyBkZSBsYSBwcsOpc2VuY2UgZHUgRGlldSB0cmluaXRhaXJlLiBM4oCZw6l0ZXJuaXTDqSwgY+KAmWVzdCBhdmFudCB0b3V0IGxlIHJlc3RlLCBmYWlyZSBs4oCZZXhww6lyaWVuY2Ugw6l0ZXJuZWxsZSBkZSBs4oCZaW50aW1pdMOpIGF2ZWMgRGlldS4gTOKAmcOpdGVybml0w6kgc2FucyBEaWV1IGVzdCBsYSBwaXJlIGNob3NlIHF1aSBwdWlzc2UgZXhpc3Rlci4gQ+KAmWVzdCBbbOKAmUVuZmVyXSgvYXJ0aWNsZS92b2ljaS1jb21tZW50LWFpZGVyLXZvcy1hbWlzLWEtYWxsZXItZW4tZW5mZXIvKS5cblxuU2FucyBmaW4sIG5vdXMgZXhww6lyaW1lbnRlcm9ucyBsYSBqb2llIGRlIGxhIHByw6lzZW5jZSBkZSBEaWV1LCBwbHVzIGdsb3JpZXVzZSBlbmNvcmUgcXXigJlhdSB0ZW1wcyBkZSBs4oCZaW5ub2NlbmNlLiBVbmUgZm9pcyByZXNzdXNjaXTDqXMsIG5vdXMgdml2cm9ucyB1bmUgbm91dmVsbGUgaHVtYW5pdMOpLCBkw6lsaXZyw6llIGR1IHDDqWNow6kgZXQgZGUgdG91dGVzIHNlcyBjb25zw6lxdWVuY2VzIHF1aSBuZSBzZXJvbnQgcGx1cy4gTm91cyBqb3Vpcm9ucyBkZSBzYSBwcsOpc2VuY2UgZGFucyB1bmUgbm91dmVsbGUgY3LDqWF0aW9uLiBFbmZpbiByw6l1bmlzLlxuXG4jIyA1LiBEaWV1IGVzdCBsYSBzZXVsZSB2w6lyaXTDqSBxdWkgdGllbmRyYSBmYWNlIMOgIG1hIG1vcnRcblxuPiBRdWUgdm90cmUgY8WTdXIgbmUgc2UgdHJvdWJsZSBwYXMhIENyb3lleiBlbiBEaWV1LCBjcm95ZXogYXVzc2kgZW4gbW9pLlxuXG5BbG9ycyBxdeKAmWlsIHZhIGxlcyBxdWl0dGVyLCBKw6lzdXMgbmUgbGFpc3NlIGVuIGdhZ2UgcXVlIHNlcyBwcm9tZXNzZXMuIEV0IGPigJllc3QgYmllbiBwYXJjZSBxdWUgY2Ugc29udCAqc2VzKiBwcm9tZXNzZXMgcXVlIG5vdXMgbuKAmWF2b25zIGJlc29pbiBkZSByaWVuIGRlIHBsdXMuXG5cbkxlcyBzY2llbmNlcywgbGVzIHBoaWxvc29waGllcyBldCBsZXMgcml0ZXMgcmVsaWdpZXV4IG5lIHBldXZlbnQgb2ZmcmlyIGF1Y3VuZSBjZXJ0aXR1ZGUgYWJzb2x1ZSBmYWNlIMOgIGxhIG1vcnQuIERpZXUsIG91aS5cblxuSsOpc3VzIG5vdXMgYXNzdXJlIHF14oCZZW4gcGxhw6dhbnQgbm90cmUgZm9pICplbiBsdWkqLCBub3VzIG7igJlhdm9ucyByaWVuIMOgIGNyYWluZHJlLiBJbCBhIHZhaW5jdSBsYSBtb3J0LiBQb3VyIGFmZnJvbnRlciBjZSBkZXJuaWVyIGVubmVtaSwgc2V1bGVzIGxlcyBwcm9tZXNzZXMgZGUgSsOpc3VzIHBldXZlbnQgbWUgZ2FyYW50aXIgbGEgdmljdG9pcmUuXG5cbiMjIENlcyBwcm9tZXNzZXMgc29udCB1biBhcHBlbCBhZHJlc3PDqSDDoCB0b3V0ZSBs4oCZaHVtYW5pdMOpXG5cbsOAIHRyYXZlcnMgY2VzIHBhcm9sZXMgcXVlIErDqXN1cyBhIGzDqWd1w6llcyDDoCBzZXMgZGlzY2lwbGVzLCBpbCBz4oCZYWRyZXNzZSBhdSBtb25kZSBlbnRpZXIuIENoYWN1biBlc3QgYXBwZWzDqSDDoCB5IHLDqXBvbmRyZSBldCDDoCBmYWlyZSBs4oCZZXhww6lyaWVuY2UgZGUgbGEgcGFpeCBxdeKAmWVsbGVzIHByb2N1cmVudDogdW5lIHbDqXJpdMOpIHF1aSByYXNzdXJlIGNldXggcXVpIG9udCBkw6lqw6AgZG9ubsOpIGxldXIgdmllIMOgIENocmlzdCwgZXQgdW5lIHbDqXJpdMOpIHF1aSBpbnZpdGUgY2V1eCBxdWkgZG91dGVudCDDoCBwbGFjZXIgbGV1ciBjb25maWFuY2UgZW4gSsOpc3VzIHBvdXIgw6p0cmUgc2F1dsOpcy5cblxuPiBTZSBwcsOpcGFyZXIgw6AgbW91cmlyLCBj4oCZZXN0IMOqdHJlIHByw6p0IMOgIHZpdnJlLlxuPlxuPiA8c3ViPjxjaXRlPsKgQ2hhcmxlcyBTcHVyZ2VvbjwvY2l0ZT48L3N1Yj5cblxuPGJyPlxuXG48c3ViPkFydGljbGUgcHVibGnDqSBwb3VyIGxhIHByZW1pw6hyZSBmb2lzIGxlIDcgbWFycyAyMDE4LiBSZW1pcyBlbiBhdmFudCBwYXIgbGEgUsOpZGFjdGlvbiBsZSAzMCBhb8O7dCAyMDI0IHBvdXIgYXR0ZWluZHJlIHVuZSBub3V2ZWxsZSBnw6luw6lyYXRpb24gZGUgbGVjdGV1ci48L3N1Yj4iLCJhdXRob3IiOnsiZnVsbE5hbWUiOiJSYXBoYcOrbCBDaGFycmllciIsInBpY3R1cmUiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9yYXBoYWVsX2NoYXJyaWVyXzAzX2NvcGllXzFfc2NhbGVkX2UxNjU3ODAxMjU3Nzg3X2Y5MzU5OTQwMzguanBnIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifX0sImltYWdlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvYWhtZWRfcml6a2hhYW5fTnFnX0RSX0R1X0xfVGRfTV91bnNwbGFzaF9iYzc5Y2NmOTUwLmpwZyIsImhlaWdodCI6MTEwMCwid2lkdGgiOjExMDAsImFsdGVybmF0aXZlVGV4dCI6IiIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn0sInRvcGljcyI6W3sibmFtZSI6IkRvY3RyaW5lIGR1IFNhbHV0In0seyJuYW1lIjoiTW9ydC/DiXRhdCBpbnRlcm3DqWRpYWlyZSJ9LHsibmFtZSI6IlJveWF1bWUgZGUgRGlldSJ9LHsibmFtZSI6IkVzcMOpcmFuY2UifSx7Im5hbWUiOiJNb3J0IGV0IERldWlsIn1dLCJtb2R1bGVzIjpbe30seyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJKZSBzdWlzIGNvbnZhaW5jdSBxdWUgbOKAmXVuIGRlcyBwcm9ibMOobWVzIGRlIGzigJlIb21tZSBtb2Rlcm5lIGVzdCBxdeKAmWlsIG5lIHBlbnNlIHBhcyBhc3NleiDDoCBsYSBtb3J0LiBQYXJhZG94YWxlbWVudCwgY2hhcXVlIGpvdXIgcXVpIHBhc3NlIG5vdXMgZW4gcmFwcHJvY2hl4oCmIMOAIG1vaW5zIHF1ZSBKw6lzdXMgbmUgcmV2aWVubmUgdHLDqHMgYmllbnTDtHQsIGplIHZhaXMgbW91cmlyLCBldCBqZSBu4oCZYWkgYXVjdW5lIGlkw6llIGR1IGpvdXIuIn1dfSx7ImlkIjoiOTIiLCJ0aXRsZSI6IkNvbW1lbnQgZmFpcmUgZ3JhbmRpciBs4oCZw4lnbGlzZT8gKMOJcGjDqXNpZW5zIDQuMTEtMTYpIiwic2x1ZyI6InByZWRpY2F0aW9uLXRwc2ctdml2cmUtZWdsaXNlLXJhcGhhZWwtY2hhcnJpZXIiLCJ0eXBlIjoicG9kY2FzdCIsInB1Ymxpc2hlZF9hdCI6IjIwMjQtMDgtMjFUMDQ6MzA6MDAuMDAwWiIsImJvZHkiOiJMYSBwbHVwYXJ0IGRlcyBibG9ndWV1cnMgVFBTRyBzb250IMOpZ2FsZW1lbnQgcGFzdGV1cnMuIEF1am91cmTigJlodWksIHR1IHBldXggdG9pIGF1c3NpIGLDqW7DqWZpY2llciBkZSBsZXVycyBlbnNlaWduZW1lbnRzIGdyw6JjZSDDoCBub3RyZSBwb2RjYXN0IDxhIGhyZWY9XCIvcG9kY2FzdHMvcHJlZGljYXRpb25zLXRwc2dcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+UHLDqWRpY2F0aW9ucyBUUFNHPC9hPi4gQ2VzIHByw6lkaWNhdGlvbnMsIHF1aSBzZSB2ZXVsZW50IHLDqXNvbHVtZW50IHRleHR1ZWxsZXMgZXQgY2hyaXN0b2NlbnRyaXF1ZXMsIHRlIGZlcm9udCByZWTDqWNvdXZyaXIgbGUgc2VucyBwcm9mb25kIGRlcyDDiWNyaXR1cmVzIGV0IG5vdXJyaXJvbnQgdGEgZm9pIGVuIENocmlzdC5cblxuPGJyPlxuXG48c3ViPlxcW+KEue+4j1xcXSA8ZW0+Tm91cyBhdmlvbnMgcHVibGnDqSBjZXR0ZSBwcsOpZGljYXRpb24gcG91ciBsYSBwcmVtacOocmUgZm9pcyBsZSAyMyBqdWlsbGV0IDIwMjIgc291cyBsZSB0aXRyZSBcIlZpdnJlIGzigJnDiWdsaXNlXCIuIE5vdXMgbCdhdm9ucyByZXB1Ymxpw6llIGxlIDIxIGFvw7t0IDIwMjQgYXZlYyB1biBub3V2ZWwgZW5yZWdpc3RyZW1lbnQgZGUgbWVpbGxldXIgcXVhbGl0w6kgZXQgdW4gbm91dmVhdSB0aXRyZS4gSWwgc2UgcGV1dCBkb25jIHF1ZSBsZXMgbm90ZXMgYWllbnQgZGUgdHLDqHMgbMOpZ8OocmVzIGRpZmbDqXJlbmNlcyBhdmVjIGzigJlhdWRpby48L2VtPjwvc3ViPlxuXG5cbi0tLVxuXG4jIyBOb3RlcyDDqWNyaXRlcyBkZSBsYSBwcsOpZGljYXRpb246XG5cbiMjIyDigJxWb3VzIMOqdGVzIGNvbWJpZW4/4oCdXG5cblZvaWzDoCBjZSBxdWkgbm91cyBkb25uZSB1bmUgaW1hZ2UgY29uY3LDqHRlIGRlIGzigJnDiWdsaXNlLiBFdCBtw6ptZSBxdWFuZCBvbiBhYm9yZGUgXCIqbGEgcXVhbGl0w6lcIio6IExlcyBncmFuZGVzIMOJZ2xpc2VzIHZvdWRyYWllbnQgw6p0cmUgcGx1cyBwZXRpdGVzLiBMZXMgcGV0aXRlcyDDiWdsaXNlcyB2b3VkcmFpZW50IMOqdHJlIHBsdXMgZ3JhbmRlcy5cblxuKipQb3VyIFBhdWw6IGxhIGNyb2lzc2FuY2UgZGUgbOKAmcOJdmFuZ2lsZSDihpIgY3JvaXNzYW5jZSBkZSBs4oCZw4lnbGlzZS4qKlxuXG5FbmdhZ2VvbnMtbm91cyBlbnNlbWJsZSwgcGFyIGxhIGdyw6JjZSBkdSBTZWlnbmV1ciwgw6Agdml2cmUgZGVzIHJlbGF0aW9ucyBmYcOnb25uw6llcyBwYXIgbOKAmcOJdmFuZ2lsZS5cblxuKipMZWN0dXJlIMOJcGjDqXNpZW5zIDQuMTEtMTYsIFMyMSBwLiA3NjgqKlxuXG5DZSB0ZXh0ZSByw6lwb25kIMOgIDMgcXVlc3Rpb25zIGltcG9ydGFudGVzOlxuXG4xLiBRdWkgZXN0IHJlc3BvbnNhYmxlIGRlIGxhIGNyb2lzc2FuY2UgZGUgbOKAmcOJZ2xpc2UuXG4yLiBRdWVsIGVzdCBsZSBidXQgZGUgbGEgY3JvaXNzYW5jZSBkZSBs4oCZw4lnbGlzZT9cbjMuIENvbW1lbnQgbOKAmcOJZ2xpc2UgZ3JhbmRpdC1lbGxlP1xuXG4jIyAxLiBRdWkgZXN0IHJlc3BvbnNhYmxlIGRlIGxhIGNyb2lzc2FuY2UgZGUgbOKAmcOJZ2xpc2U/XG5cbiMjIyBDaHJpc3RcblxuPiBD4oCZZXN0IExVSSBxdWkgYSBkb25uw6nigKZcbj5cbj4gPHN1Yj48Y2l0ZT7DiXBow6lzaWVucyA0LjExYTwvY2l0ZT48L3N1Yj5cblxuQ2hyaXN0IGVzdCBjZWx1aSBxdWkgcG91cnZvaXQgYXV4IGJlc29pbnMgZGUgbGEgY3JvaXNzYW5jZS4gTOKAmcOJZ2xpc2UgZXN0IGxlIGZydWl0IGRlIHNhIGdyw6JjZS4gQ+KAmWVzdCBzb24gcHJvamV0LCBzYSBtaXNzaW9uLCBj4oCZZXN0IMOgIGx1aSBxdeKAmWVsbGUgYXBwYXJ0aWVudCBldCBpbCBsYSBiw6J0aXQgY29tbWUgaWwgbGUgc291aGFpdGUuIFNpIENocmlzdCBuZSBkb25uZSBwYXMsIGzigJnDiWdsaXNlIG5lIG11csOudCBwYXMuXG5cbkljaSwgQ2hyaXN0IGRvbm5lIGRlcyBwZXJzb25uZXMgcG91ciBhaWRlciDDoCBsYSBjcm9pc3NhbmNlOlxuXG4jIyMgQXDDtHRyZXMgZXQgcHJvcGjDqHRlc1xuXG4+IOKApmxlcyB1bnMgY29tbWUgYXDDtHRyZXMsIGxlcyBhdXRyZXMgY29tbWUgcHJvcGjDqHRlc+KAplxuPlxuPiA8c3ViPjxjaXRlPsOJcGjDqXNpZW5zIDQuMTFiPC9jaXRlPjwvc3ViPlxuXG5CaW7DtG1lIHF1aSByZXZpZW50IHBvdXIgbGEgM8OobWUgZm9pcyBkYW5zIGzigJnDiXBpdHJlLCBQYXVsIGVuIHBhcmxlIGF1IHNlbnMgZm9ydC5cblxuQ2Ugc29udCBjZXV4IHF1aSBvbnQgcmXDp3UgZGUgQ2hyaXN0IHVuZSBhdXRvcml0w6kgcGFydGljdWxpw6hyZSBwb3VyIGxldXIgbWlzc2lvbjogZGUgcsOpdsOpbGVyIGxlIG1lc3NhZ2UgZGUgbOKAmcOJdmFuZ2lsZSBhZmluIHF14oCZaWxzIHBvc2VudCBsZSBmb25kZW1lbnQgZGUgY2UgcXXigJllc3QgbOKAmcOJZ2xpc2UuIERlcHVpcyAyMDAwIGFucywgbm91cyBiw6J0aXNzb25zIGzigJnDiWdsaXNlIHN1ciBsZXVyIGVuc2VpZ25lbWVudC5cblxuIyMjIFBldXZlbnQtaWxzIMOqdHJlIGNvbXByaXMgZGFucyB1biBzZW5zIHBsdXMgZmFpYmxlP1xuXG5E4oCZdW5lIGNlcnRhaW5lIG1hbmnDqHJlLCBsZXMgaW1wbGFudGV1cnMsIGxlcyBwaW9ubmllcnMgZXQgbGVzIG1pc3Npb25uYWlyZXMgcXVpIHBhcnRlbnQgZGFucyBkZXMgcGV1cGxlcyBub24gYXR0ZWludHMgb250IHVuIG1pbmlzdMOocmUgcXVpIHJlc3NlbWJsZSDDoCBjZWx1aSBkZXMgYXDDtHRyZXMuXG5cbkVzdC1jZSBxdeKAmW9uIHBldXQgcGFybGVyIGRlIHByb3Bow6h0ZSwgbG9yc3F14oCZaWwgbuKAmXkgYSBwYXMgZGUgcsOpdsOpbGF0aW9uOiDigJxBaW5zaSBwYXJsZSBs4oCZw4l0ZXJuZWzigJ0/XG5cblVuZSBzZW5zaWJpbGl0w6kgcGFydGljdWxpw6hyZSBhdXggZMOpZmlzIGV0IGJlc29pbnMgZGUgbm90cmUgbW9uZGUgY29udGVtcG9yYWluIGV0IGNvbW1lbnQgbOKAmcOJZ2xpc2UgZG9pdCB5IHLDqXBvbmRyZSBhdmVjIHNhZ2Vzc2UuXG5cbkPigJllc3QgdW4gbWluaXN0w6hyZSBxdWkgYWlkZSBs4oCZw4lnbGlzZSBQQVIgbOKAmWVuc2VpZ25lbWVudCBkZSBsYSBQYXJvbGUgw6AgZGlzY2VybmVyIGxhIHZvbG9udMOpIGRlIERpZXUuXG5cbiMjIyBRdWVsIGVzdCBsZSBwb2ludCBjb21tdW4gZGUgY2VzIG1pbmlzdMOocmVzIGNpdMOpcyBwYXIgUGF1bD9cblxuVG91cyBjZXMgZG9ucyBzb250IGVuIGxpZW4gYXZlYyBsYSBwcm9jbGFtYXRpb24gZGUgbGEgQmlibGUsIGPigJllc3QgcG91ciBjZWxhIHF14oCZb24gbGVzIGFwcGVsbGUgKmxlcyBtaW5pc3TDqHJlcyBkZSBsYSBQYXJvbGUqLlxuXG4jIyMgUG91cnF1b2kgSsOpc3VzIGEgZG9ubsOpIMOgIGzigJnDiWdsaXNlIGNlcyBtaW5pc3TDqHJlcyBkZSBsYSBQYXJvbGU/XG5cbkxhIFBhcm9sZSBlc3QgbGEgc291cmNlIGTigJlhdXRvcml0w6ksIGxhIG5vcm1lIGVuIG1hdGnDqHJlIGRlIGZvaSBldCBkZSB2aWUuXG5cbk5vdXMgbmUgcG91dm9ucyBwYXMgZMOpZmluaXIgcGFyIG5vdXMtbcOqbWUgY2UgcXXigJllc3QgbOKAmcOJZ2xpc2UsIGNlIHF14oCZZWxsZSBkb2l0IHZpdnJlLCBvbiBuZSBwZXV0IHBhcyBjaG9pc2lyIG5vdHJlIMOpdGhpcXVlIG91IGxlcyBidXRzIGRlIG5vdHJlIMOJZ2xpc2UuXG5cbkxhIHZpZSBjaHLDqXRpZW5uZSBu4oCZZXN0IHBhcyB1biBidWZmZXQgw6Agdm9sb250w6kgb8O5IGzigJlvbiBwZXV0IHBpY29yZXIgY2UgcXVlIHF1ZSBs4oCZb24gdmV1dC5cblxuIyMjIFF1ZSBkb2l2ZW50IGRvbmMgZmFpcmUgY2VzIG1pbmlzdHJlcyBkZSBsYSBQYXJvbGU/XG5cbiogTWlzZSBlbiBwcmF0aXF1ZSBkZSBsYSBQYXJvbGUgZXQgw6p0cmUgZGVzIG1vZMOobGVzIGVuY291cmFnZWFudHMgZGUgbGEgdmllIGRlIGRpc2NpcGxlLlxuKiBFbnNlaWduZXIsIHByw6pjaGVyLCBtYWlzIGF1c3NpIGRlIGZvcm1lciwgY29uc2VpbGxlciwgYWNjb21wYWduZXIgbGVzIG1lbWJyZXMuXG5cbk1haXMgZGFucyBxdWVsIGJ1dD9cblxuVm9pY2kgbGEgbGVjdHVyZSAobWF1dmFpc2UpIHF1ZSBub3VzIGZhaXNvbnMgZOKAmcOJcGjDqXNpZW5zIDQuMTEtMTYgKFMyMSk6XG5cbj4gPHN1cD4xMSA8L3N1cD5D4oCZZXN0IGx1aSBcXFtKw6lzdXNcXF0gcXVpIGEgZG9ubsOpIGxlcyB1bnMgY29tbWUgYXDDtHRyZXMsIGxlcyBhdXRyZXMgY29tbWUgcHJvcGjDqHRlcywgbGVzIGF1dHJlcyBjb21tZSDDqXZhbmfDqWxpc3RlcywgbGVzIGF1dHJlcyBjb21tZSBiZXJnZXJzIGV0IGVuc2VpZ25hbnRzLiBcXFtlbiBnw6luw6lyYWwsIG9uIHphcHBlIGxlcyB2ZXJzZXQgMTIgZXQgMTNcXF0gPHN1cD4xNCA8L3N1cD5BaW5zaSwgbm91cyBuZSBzZXJvbnMgcGx1cyBkZSBwZXRpdHMgZW5mYW50cywgYmFsbG90dMOpcyBldCBlbXBvcnTDqXMgcGFyIHRvdXQgdmVudCBkZSBkb2N0cmluZSwgcGFyIGxhIHJ1c2UgZGVzIGhvbW1lcyBldCBsZXVyIGhhYmlsZXTDqSBkYW5zIGxlcyBtYW7Fk3V2cmVzIGTigJnDqWdhcmVtZW50LlxuXG5NYWlzIG9uIG91YmxpZSBxdeKAmWVuIHLDqWFsaXTDqTpcblxuPiA8c3VwPjEyIDwvc3VwPklsIGzigJlhIGZhaXQgcG91ciBmb3JtZXIgbGVzIHNhaW50cyBhdXggdMOiY2hlcyBkdSBzZXJ2aWNlIGVuIHZ1ZSBkZSBs4oCZw6lkaWZpY2F0aW9uIGR1IGNvcnBzIGRlIENocmlzdC5cblxuUXVpIGVzdCByZXNwb25zYWJsZSBkZSBsYSBjcm9pc3NhbmNlIGRlIGzigJnDiWdsaXNlPyBMZXMgbWVtYnJlcyBkZSBs4oCZw4lnbGlzZSEgQ2V1eCBxdWkgb250IGxlIG1pbmlzdMOocmUgZGUgbGEgUGFyb2xlIMOpcXVpcGVudCBsZXMgbWVtYnJlcyBkZSBs4oCZw4lnbGlzZSBwb3VyIHF14oCZaWxzIHB1aXNzZW50IGFjY29tcGxpciBsZXVyIG1pc3Npb24uXG5cbiMjIyBMZXMgcmVzcG9uc2FibGVzIHNvbnQgZGVzIGZvcm1hdGV1cnMuXG5cbkRhbnMgbGEgQmlibGUgaWwgbuKAmXkgYSBwYXMgZGUgY2xlcmfDqSBjb25zYWNyw6kgZGFucyB1bmUgY29tbXVuYXV0w6kgcGFzc2l2ZSBldCBjb25zb21tYXRyaWNlLiBM4oCZw4lnbGlzZSBlc3QgdW5lIG5hdGlvbiBkZSBwcsOqdHJlcy5cblxuUXXigJllc3QtY2UgcXVlIMOnYSB2ZXV0IGRpcmU/XG5cbkxlcyByZXNwb25zYWJsZXMgbmUgZG9pdmVudCBwYXMgYXNzdW1lciBsZXMgbWluaXN0w6hyZXMgZGUgbOKAmcOJZ2xpc2UsIG1haXMgZG9pdmVudCBz4oCZYXNzdXJlciBxdWUgdG91cyBsZXMgbWluaXN0w6hyZXMgZGUgbOKAmcOJZ2xpc2Ugc2UgZMOpdmVsb3BwZW50IGhhcm1vbmlldXNlbWVudC5cblxuRW4gYnJlZiwgY2UgcXVlIHNvdWxpZ25lIGljaSBQYXVsIGPigJllc3QgcXVlIGxlIG1pbmlzdMOocmUgZGVzIHJlc3BvbnNhYmxlcyBlc3QgZGUgZMOpdmVsb3BwZXIgY2VsdWkgZGVzIG1lbWJyZXMgZGUgbOKAmcOJZ2xpc2UuIElscyBu4oCZb250IHBhcyBzZXVsZW1lbnQgcG91ciByZXNwb25zYWJpbGl0w6kgZGUgcHJlbmRyZSBzb2luIGRlIGzigJnDiWdsaXNlLCBtYWlzIGTigJlhcHByZW5kcmUgw6AgbOKAmcOJZ2xpc2Ugw6AgcHJlbmRyZSBzb2luIGRlIGzigJnDiWdsaXNlIGVuIGzigJllbnNlaWduYW50IGV0IGVuIG1vbnRyYW50IGzigJlleGVtcGxlIHBhciBsZXVyIHZpZSBldCBsZXVyIHNlcnZpY2UuIElsIG7igJl5IGEgcGFzIHVuIG1lbWJyZSBxdWkgbuKAmWFpdCBwYXMgc29uIHLDtGxlIMOgIGpvdWVyIG91IHF1aSBu4oCZZXN0IHBhcyDDqXF1aXDDqSBwb3VyIHNlcnZpci4gSWwgbuKAmXkgYSBwYXMgZGUgYmFuYyBkZSB0b3VjaGUsIG5pIGRlIHJlbXBsYcOnYW50cy5cblxuTGVzIG1lbWJyZXMgZGUgbOKAmcOJZ2xpc2Ugc29udCBjZXV4IHF1aSBmb250IGdyYW5kaXIgbOKAmcOJZ2xpc2UhIElscyBlbiBzb250IGxlcyBhY3RldXJzIGVzc2VudGllbHMuXG5cbiMjIDIuIFF1ZWwgZXN0IGxlIGJ1dCBkZSBsYSBjcm9pc3NhbmNlIGRlIGzigJnDiWdsaXNlP1xuXG4+IDxzdXA+MTIgPC9zdXA+TOKAmcOpZGlmaWNhdGlvbiBkdSBjb3JwcyBkZSBDaHJpc3QsIGp1c3F14oCZw6AgY2UgcXVlIG5vdXMgcGFydmVuaW9ucyB0b3VzIMOgIGzigJl1bml0w6kgZGUgbGEgZm9pIGV0IGRlIGxhIGNvbm5haXNzYW5jZSBkdSBGaWxzIGRlIERpZXUsIMOgIGxhIG1hdHVyaXTDqSBkZSBs4oCZYWR1bHRlLCDDoCBsYSBtZXN1cmUgZGUgbGEgc3RhdHVyZSBwYXJmYWl0ZSBkZSBDaHJpc3QuIFxcW+KAplxcXSA8c3VwPjE0IDwvc3VwPkFpbnNpLCBub3VzIG5lIHNlcm9ucyBwbHVzIGRlIHBldGl0cyBlbmZhbnRzLCBiYWxsb3R0w6lzIGV0IGVtcG9ydMOpcyBwYXIgdG91dCB2ZW50IGRlIGRvY3RyaW5lLCBwYXIgbGEgcnVzZSBkZXMgaG9tbWVzIGV0IGxldXIgaGFiaWxldMOpIGRhbnMgbGVzIG1hbsWTdXZyZXMgZOKAmcOpZ2FyZW1lbnQuXG4+XG4+IDxzdWI+PGNpdGU+w4lwaMOpc2llbnMgNC4xMi0xNDwvY2l0ZT48L3N1Yj5cblxuIyMjIDFlciBidXQ6IEzigJl1bml0w6lcblxuQ2UgbuKAmWVzdCBwYXMgbOKAmWFic2VuY2UgZGUgY29uZmxpdCBxdWkgZMOpZmluaXQgbOKAmXVuaXTDqS5cblxuTOKAmXVuaXTDqSBlc3Qgc291dmVudCBjb21wcmlzZSB1bmlxdWVtZW50IGRhbnMgc2EgZGltZW5zaW9uIHBzeWNob2xvZ2lxdWUgXCJqZSBtZSBzZW5zIGJpZW5cIiBvdSBcIm9uIGVzdCBlbiBwYWl4XCIuXG5cbiogTOKAmXVuaXTDqSBlc3QgdW5lIHVuaXTDqSBzdXJuYXR1cmVsbGUgcHJvZHVpdGUgcGFyIGxlIFNhaW50LUVzcHJpdC5cbiogTWFpcyBlbGxlIGVzdCBhdXNzaSBsZSBmcnVpdCBkZSBub3RyZSBlbmdhZ2VtZW50IMOgIGTDqXNpcmVyIGNvbm5haXRyZSBlbnNlbWJsZSBKw6lzdXMuXG5cbipcIkzigJl1bml0w6kgZGFucyBsYSBmb2lcIiogY+KAmWVzdCBkb25jIGzigJl1bml0w6kgc3Bpcml0dWVsbGUgZm9yZ8OpZSBwYXIgZGVzIGNvbnZpY3Rpb25zIGJpYmxpcXVlcywgdW5pdMOpIGRhbnMgdW5lIGNvbXByw6loZW5zaW9uIGRlIGNlIHF14oCZZXN0IFwiw6p0cmUgbGUgY29ycHMgZGUgQ2hyaXN0XCIuXG5cblBsdXMgbm91cyBjb21wcmVub25zIHF1aSBlc3QgSsOpc3VzLCBwbHVzIG5vdXMgbGUgY29ubmFpc3NvbnMsIHBsdXMgbm91cyBub3VzIGNvbm5haXNzb25zIGV0IG5vdXMgY29tcHJlbm9ucyBub3VzLW3Dqm1lcy5cblxuTOKAmXVuaXTDqSBj4oCZZXN0IGzigJllbmdhZ2VtZW50IGNvbW11bmF1dGFpcmUgZGVzIHVucyBlbnZlcnMgbGVzIGF1dHJlcyDDoCBz4oCZZW50cmFpZGVyIGFmaW4gZGUgdml2cmUgcG91ciBDaHJpc3QgZXQgbHVpIHJlc3NlbWJsZXIuXG5cbipTaSBDaHJpc3QgZXN0IGF1IGNlbnRyZSwgcGx1cyBvbiBhdmFuY2UgZW5zZW1ibGUgcGx1cyBvbiBzZSByYXBwcm9jaGUuIENvbW1lIGxlcyByYXlvbnMgZOKAmXVuZSByb3VlIGRlIHbDqWxvLipcblxuQ2V0dGUgdW5pdMOpIGVzdCBuw6ljZXNzYWlyZSDDoCBsYSBjcm9pc3NhbmNlIGNvbW11bmF1dGFpcmUgZGFucyBsYSBtYXR1cml0w6kuXG5cbiMjIyBMZSAyw6htZSBidXQ6IExhIG1hdHVyaXTDqVxuXG5QYXVsIGVudHJlbcOqbGUgZGV1eCBpbWFnZXM6XG5cbiogbGEgY3JvaXNzYW5jZSBkdSBjb3JwcywgY2VsbGUgZGUgbOKAmWVuZmFudCB2ZXJzIGzigJlhZHVsdGUsXG4qIGNlbGxlIGRlIGxhIGNvbnN0cnVjdGlvbiBkdSB0ZW1wbGUgcXXigJllc3QgbOKAmcOJZ2xpc2UuXG5cbipRdWVsIGVzdCBsZSBidXQgZGUgbGEgY3JvaXNzYW5jZSBkZSBs4oCZw4lnbGlzZT8qIFF14oCZZW5zZW1ibGUsIGxlcyBtZW1icmVzIHJlc3NlbWJsZW50IMOgIErDqXN1cywgcXXigJlpbHMgZGV2aWVubmVudCBhdXNzaSBtYXR1cmVzIHF1ZSBsdWkuXG5cbkNocmlzdCBlc3QgbOKAmWV4ZW1wbGUgZGUgbGEgbWF0dXJpdMOpIHBhcmZhaXRlLiBJbCBu4oCZeSBhdmFpdCByaWVuIGVuIHF1b2kgaWwgcG91dmFpdCBwcm9ncmVzc2VyLlxuXG5MZSBidXQgZGUgbGEgY3JvaXNzYW5jZSBkZSBs4oCZw4lnbGlzZSBlc3QgZGUgdGVuZHJlIMOgIGxhIHBlcmZlY3Rpb24gbW9yYWxlLCBzcGlyaXR1ZWxsZSwgcmVsYXRpb25uZWxsZSwgYWZmZWN0aXZlIGV0IG1pc3Npb25uYWlyZSBkZSBDaHJpc3QuXG5cbkzigJnDiWdsaXNlIGVuIHRhbnQgcXVlIGNvcnBzIGRlIENocmlzdCBkb2l0IGltaXRlciBKw6lzdXMuXG5cbiMjIyBQYXVsIG5lIHBhcmxlIHBhcyBpY2kgZGUgbGEgY3JvaXNzYW5jZSBkZXMgY2hyw6l0aWVucywgbWFpcyBkZSBsYSBjcm9pc3NhbmNlIGRlIGzigJnDiWdsaXNlXG5cbkPigJllc3Qgw6AgZGlyZSBxdeKAmW9uIG5lIGRvaXQgcGFzIGNoYWN1biBkZSBub3RyZSBjw7R0w6kgY2hlcmNoZXIgw6AgZ3JhbmRpciwgbWFpcyBlbnNlbWJsZS5cblxuUGFyZm9pcywgbOKAmcOJZ2xpc2UgcmVzc2VtYmxlIHBsdXMgw6AgZGVzIGF0aGzDqHRlcywgKipjaGFjdW4gc3VyIHNhIGxpZ25lKiosIHF14oCZw6AgdW5lIMOpcXVpcGUgcXVpIGpvdWUgZW5zZW1ibGUuXG5cbkzigJnDiWdsaXNlLCBj4oCZZXN0IHBsdXMgcXVlIGRlcyBpbmRpdmlkdWFsaXTDqXMgY8O0dGUgw6AgY8O0dGUuIEVsbGUgZXhpc3RlIGRhbnMgXCJs4oCZZW50cmVcIiwgZGFucyBsZXMgXCJsZXMgdW5zIGxlcyBhdXRyZXNcIi4gU2kgbm91cyBhcHBhcnRlbm9ucyDDoCBDaHJpc3QsIGlsIG5vdXMgYSBkb25uw6kgw6Agc29uIMOJZ2xpc2UuXG5cbkxhIHNhbmN0aWZpY2F0aW9uIG7igJllc3QgcGFzIHF14oCZdW4gYnV0IGluZGl2aWR1ZWwsIG1haXMgY29tbXVuYXV0YWlyZS5cblxuRW4gZmFpdCwgb24gbmUgcGV1dCBwYXMgdGVuZHJlIMOgIHJlc3NlbWJsZXIgw6AgQ2hyaXN0IMOgIHRpdHJlIHBlcnNvbm5lbCBzYW5zIGxlIHZpdnJlIGRlIG1hbmnDqHJlIGNvbW11bmF1dGFpcmUuXG5cbiMjIDMuIENvbW1lbnQgbOKAmcOJZ2xpc2UgZ3JhbmRpdC1lbGxlIGVuIHVuaXTDqSBldCBtYXR1cml0w6k/XG5cbj4gPHN1cD4xNSA8L3N1cD5NYWlzIGVuIGRpc2FudCBsYSB2w6lyaXTDqSBkYW5zIGzigJlhbW91ciwgbm91cyBncmFuZGlyb25zIMOgIHRvdXQgcG9pbnQgZGUgdnVlIHZlcnMgY2VsdWkgcXVpIGVzdCBsYSB0w6p0ZSwgQ2hyaXN0LiA8c3VwPjE2IDwvc3VwPkPigJllc3QgZGUgbHVpIHF1ZSBsZSBjb3JwcyB0b3V0IGVudGllciwgYmllbiBjb29yZG9ubsOpIGV0IHNvbGlkZW1lbnQgdW5pIGdyw6JjZSBhdXggYXJ0aWN1bGF0aW9ucyBkb250IGlsIGVzdCBtdW5pLCB0aXJlIHNhIGNyb2lzc2FuY2UgZW4gZm9uY3Rpb24gZGUgbOKAmWFjdGl2aXTDqSBxdWkgY29udmllbnQgw6AgY2hhY3VuZSBkZSBzZXMgcGFydGllcyBldCBz4oCZw6lkaWZpZSBsdWktbcOqbWUgZGFucyBs4oCZYW1vdXIuXG4+XG4+IDxzdWI+PGNpdGU+w4lwaMOpc2llbnMgNC4xNS0xNjwvY2l0ZT48L3N1Yj5cblxuTm91cyBhdm9ucyB2dSBxdWUgQ2hyaXN0IGZhaXQgZGVzIGRvbnMgYXV4IGhvbW1lcyBxdeKAmWlsIGEgY2hvaXNpcyBwb3VyIHF14oCZaWxzIHNlcnZlbnQgbOKAmcOJZ2xpc2UgcGFyIGxlIG1pbmlzdMOocmUgKmRlIGxhIFBhcm9sZSBkZSBEaWV1LipcblxuSWNpIFBhdWwgcGFybGUgZGUgcmVsYXRpb24gZGlzYW50IFwiKmxhIHbDqXJpdMOpXCIqLiDDh2Egc291cy1lbnRlbmQgY2hleiBQYXVsIFwiKmxhIHbDqXJpdMOpIGRlIGzigJnDiXZhbmdpbGVcIiogcGFydGFnw6kgYXZlYyBhbW91ci5cblxuTOKAmcOJZ2xpc2UgZ3JhbmRpdCBlbiBtYXR1cml0w6kgw6AgbWVzdXJlIG/DuSBlbGxlIGVzdCBzYXR1csOpZSBkZSByZWxhdGlvbnMgYWZmZWN0dWV1c2VzIGV0IGNlbnRyw6llIHN1ciBs4oCZw4l2YW5naWxlLlxuXG5QbHVzIEzigJnDiXZhbmdpbGUgY2lyY3VsZSBhdSBzZWluIGRlIGzigJnDiWdsaXNlLCBwbHVzIGlsIHRyYW5zZm9ybWUgZXQgZmHDp29ubmUgbOKAmcOJZ2xpc2UgZXQgcGx1cyBs4oCZw4lnbGlzZSByZW5kIHRhbmdpYmxlIGzigJnDiXZhbmdpbGUuXG5cblZvdXMgdm91cyBwb3NleiBwZXV0LcOqdHJlIGxhIHF1ZXN0aW9uOiAq4oCcTWFpcyBtb2ksIGNvbW1lbnQgcHVpcy1qZSBhaWRlcj/igJ0qXG5cbiMjIyBDb21tZW50IGFpZGVyIGzigJnDiWdsaXNlIMOgIHJlc3NlbWJsZXIgw6AgSsOpc3Vz4oCmXG5cbuKApnF1YW5kIG9uIGVzdCAqbWFsYWRlKiwgb3UgZGFucyB1bmUgKnNpdHVhdGlvbiBkZSB2aWUgY29tcGxpcXXDqWUqLCBvdSBlbmNvcmUgbG9yc3F1ZSBs4oCZb24gcGVuc2UgKm5lIHBhcyBhdm9pciBkZSBjYXBhY2l0w6lzKj9cblxuSmUgY3JvaXMgcXVlIGzigJl1biBkZSBub3MgYW5nbGVzIG1vcnRzIGVzdCBxdWUgbm91cyBhdm9ucyBsYSBmYWlibGVzc2UgZGUgcsOpc3VtZXIgbOKAmcOJZ2xpc2UgYXUgY3VsdGUgZG9taW5pY2FsIGV0IGxlIHNlcnZpY2UgYXV4IGNob3NlcyBtYXTDqXJpZWxsZXMuIER1IGNvdXAsIG9uIGNyb2l0IHF1ZSBsZXMgZG9ucyBzb250IGRvbm7DqXMgcG91ciBsZSBjdWx0ZS5cblxuKuKAnFNpIG9uIG7igJllc3QgcGFzIGRvdcOpIHBvdXIgbGEgbXVzaXF1ZSBvdSBs4oCZZW5zZWlnbmVtZW50IGRlcyBlbmZhbnRzIGV0IGFkdWx0ZXMsIG9uIGVzdCBpbnV0aWxlLuKAnSpcblxuTm90cmUgcHJvYmzDqG1lLCBj4oCZZXN0IHF14oCZb24gcGVuc2UgcXVlIHNlcnZpY2UgPSBhY3Rpdml0w6k7IGFsb3JzIHF1ZSBzZXJ2aWNlID0gZW50cmFpZGUgYXZhbnQgdG91dC5cblxuTGVzIGRvbnMgbmUgc29udCBwYXMgZG9ubsOpcyBwb3VyIGxlIGN1bHRlIChvdSBwb3VyIHRlbGxlIG91IHRlbGxlIGFjdGl2aXTDqSksIG1haXMgcG91ciBs4oCZw4lnbGlzZSA9IHNlcnZpciBsZXMgbWVtYnJlcy4gTOKAmcOJZ2xpc2UgY+KAmWVzdCB1bmUgY29tbXVuYXV0w6kgZGUgcGVyc29ubmUsIHVuZSBmYW1pbGxlLCB1biBjb3JwcywgcGFzIHVuZSBzb21tZSBk4oCZYWN0aXZpdMOpcyBoZWJkb21hZGFpcmVzLlxuXG5TdWlzLWplIGVuIHRyYWluIGRlIGRpcmUgcXVlIG5vdXMgbmUgZGV2cmlvbnMgcGFzIGF2b2lyIGTigJlhY3Rpdml0w6lzP1xuXG5Ob24sIG1haXMgZWxsZXMgc29udCBsZSBzcXVlbGV0dGUgYXUgc2VydmljZSBkZSBub3MgcmVsYXRpb25zIGV0IGRlIG5vdHJlIHJlbGF0aW9uIGF2ZWMgRGlldS4gTm9zIHJlbGF0aW9ucyBzb250IGxlcyBtdXNjbGVzLCBsYSBjaGFpci5cblxuVm91cyB2b3VzIGRpdGUgcGV1dC3DqnRyZTogKuKAnE9LLCBtYWlzIGNvbW1lbnQgdHJhbnNtZXR0cmUgbGEgdsOpcml0w6k/IE1vaSBqZSBuZSBzYWlzIHBhcyBmYWlyZSB1bmUgw6l0dWRlIGJpYmxpcXVlLiBKZSBuZSBtZSBzZW5zIHBhcyBsw6lnaXRpbWUu4oCdKlxuXG4jIyMgVHJhbnNtZXR0cmUgXCJsYSB2w6lyaXTDqVwiIGPigJllc3QgYmllbiBwbHVzIHF1ZSB0cmFuc21ldHRyZSBsYSBjb25uYWlzc2FuY2UgZGUgbGEgZG9jdHJpbmVcblxuPiA8c3VwPjEgPC9zdXA+SmUgdm91cyBlbmNvdXJhZ2UgZG9uYywgbW9pLCBsZSBwcmlzb25uaWVyIGRhbnMgbGUgU2VpZ25ldXIsIMOgIHZvdXMgY29uZHVpcmUgZOKAmXVuZSBtYW5pw6hyZSBkaWduZSBkZSBs4oCZYXBwZWwgcXVlIHZvdXMgYXZleiByZcOndS4gPHN1cD4yIDwvc3VwPkVuIHRvdXRlIGh1bWlsaXTDqSBldCBkb3VjZXVyLCBhdmVjIHBhdGllbmNlLCBzdXBwb3J0ZXotdm91cyBsZXMgdW5zIGxlcyBhdXRyZXMgZGFucyBs4oCZYW1vdXIuIDxzdXA+MyA8L3N1cD5FZmZvcmNlei12b3VzIGRlIGNvbnNlcnZlciBs4oCZdW5pdMOpIGRlIGzigJlFc3ByaXQgcGFyIGxlIGxpZW4gZGUgbGEgcGFpeC5cbj5cbj4gPHN1Yj48Y2l0ZT7DiXBow6lzaWVucyA0LjEtMyAoUzIxKTwvY2l0ZT48L3N1Yj5cblxuVHJhbnNtZXR0cmUgbGEgdsOpcml0w6ksIGPigJllc3Qgc+KAmWVuY291cmFnZXIgw6Agbm91cyBjb25kdWlyZSBk4oCZdW5lIG1hbmnDqHJlIGRpZ25lIGRlIGzigJnDiXZhbmdpbGUuXG5cbklsIG5lIHPigJlhZ2l0IHBhcyBzaW1wbGVtZW50IGRlIHRyYW5zbWV0dHJlIHVuZSBjb25uYWlzc2FuY2UgZGUgbOKAmcOJdmFuZ2lsZSwgbWFpcyBkZSB0cmFuc21ldHRyZSB1bmUgbWFuacOocmUgZGUgdml2cmUgc2Vsb24gc2Vsb24gbOKAmcOJdmFuZ2lsZS5cblxuRXhlbXBsZSBk4oCZdW5lIHPFk3VyIGRlIG5vdHJlIGFzc2VtYmzDqWU6IEPigJllc3QgdW4gbW9kw6hsZSBkZSB2aWUgZGUgZm9pIHF1ZSBub3VzIGRldm9ucyB0cmFuc21ldHRyZS5cblxuUGFyZm9pczpcblxuKiBK4oCZYWkgw6l0w6kgcGx1cyBiw6luaSBwYXIgbOKAmWhvc3BpdGFsaXTDqSBxdWUgbOKAmWFjdGl2aXTDqVxuKiBK4oCZYWkgw6l0w6kgcGx1cyBiw6luaSBwYXIgbGEgcHJpw6hyZSBvdSBs4oCZw6ljaGFuZ2Ugw6AgbGEgZmluIGTigJl1biBjdWx0ZSBxdWUgcGFyIGxhIHByw6lkaWNhdGlvblxuXG4jIyMgTGUgbW9kw6hsZSBiaWJsaXF1ZSBkZSBsYSB0cmFuc21pc3Npb24gZXN0IGNlbHVpIGRlIGzigJlpbWl0YXRpb24uXG5cbj4gPHN1cD45PC9zdXA+IENlIHF1ZSB2b3VzIGF2ZXogYXBwcmlzLCByZcOndSBldCBlbnRlbmR1IGRlIG1vaSBldCBjZSBxdWUgdm91cyBhdmV6IHZ1IGVuIG1vaSwgbWV0dGV6LWxlIGVuIHByYXRpcXVlLiBFdCBsZSBEaWV1IGRlIGxhIHBhaXggc2VyYSBhdmVjIHZvdXMuIFxuPlxuPiA8c3ViPjxjaXRlPlBoaWxpcHBpZW5zIDQuOTwvY2l0ZT48L3N1Yj5cblxuQ2VydGVzLCBsYSBjb25uYWlzc2FuY2UgYmlibGlxdWUgZXN0IG7DqWNlc3NhaXJlLCBtYWlzIGNlIHF1aSBlc3QgYXUgY2VudHJlIGRlIGxhIGZvcm1hdGlvbiBkYW5zIGxhIEJpYmxlIGPigJllc3QgbGEgcmVsYXRpb24uXG5cbkxlIGJ1dCBkZSBsYSByZWxhdGlvbiBlc3QgZGUgbm91cyBhaWRlciDDoCBmYWlyZSBncmFuZGlyIG5vdHJlIGZvaSBldCBmYcOnb25uZXIgbm90cmUgY2FyYWN0w6hyZSBhZmluIGRlIHZpdnJlIGTigJl1bmUgbWFuacOocmUgcXVpIHBsYcOudCDDoCBEaWV1LiBD4oCZZXN0IGRvbmMgZW4gc+KAmWVuZ2FnZWFudCBsZXMgdW5zIGVudmVycyBsZXMgYXV0cmVzIMOgIHPigJlhaWRlciDDoCB2aXZyZSDDoCBsYSBzdWl0ZSBkdSBTZWlnbmV1ciBxdWUgbm91cyBncmFuZGlzc29ucy5cblxuIyMjIExhIGJvbm5lIHF1ZXN0aW9uIMOgIHNlIHBvc2VyOlxuXG4qKirigJxRdWkgcHVpcy1qZSBlbmNvdXJhZ2VyIGF2ZWMgYW1vdXIgw6Agc3VpdnJlIErDqXN1cz/igJ0qKipcblxuUXVlbHF1ZXMgZXhlbXBsZXM6XG5cbiogSuKAmWFpIGRlcyByZWxhdGlvbnMgcGx1cyBwZXJzb25uZWxsZXMgYXZlYyBjZXJ0YWlucyBtZW1icmVzIGRlIG1vbiDDiWdsaXNlIGV0IG9uIHPigJlhaWRlIG11dHVlbGxlbWVudCDDoCBncmFuZGlyIGRhbnMg4oCcbCphIGNvbm5haXNzYW5jZSBkdSBGaWxzIGRlIERpZXXigJ0qICh2LjEzKVxuICAqIE9uIHPigJlhaWRlIMOgIGx1dHRlciBjb250cmUgbm9zIHRlbnRhdGlvbnMuXG4gICogT24gc+KAmcOpY291dGUsIG9uIHByaWUgZW5zZW1ibGUuXG4qIExvcnMgZHUgY3VsdGUgb3UgZGVzIGF1dHJlcyByZW5jb250cmVzLCBqZSB2YWlzIHZlcnMgZGVzIG5vdXZlbGxlcyBwZXJzb25uZXMgcG91ciBsZXMgYWNjdWVpbGxpciBldCBsZXVyIG1vbnRyZXIgZGUgbOKAmWFtb3VyICh0aW1pZGVzIG91IHLDqXNlcnbDqWVzIGV0Yy4pXG4qIGplIHByYXRpcXVlIGzigJlob3NwaXRhbGl0w6lcbiogU2kgamUgc2FpcyBxdeKAmXVuZSBwZXJzb25uZSBlc3Qgw6lwcm91dsOpZSBkYW5zIHNhIGZvaSBvdSBzYSBzYW50w6ksIGplIGNoZXJjaGUgY29tbWVudCBs4oCZYWlkZXIgZXQgcHJpZSBwb3VyIGVsbGUuXG4qIEVuIEdEUzogSmUgcGFydGFnZSBtZXMgZmFyZGVhdXggYXV4IGF1dHJlcywgZXQgbGVzIGF1dHJlcyBzYXZlbnQgcXXigJlpbHMgcGV1dmVudCBzZSBzZW50aXIgbGlicmVzIGRlIHZlbmlyIHZvdXMgcGFydGFnZXIgbGVzIGxldXJzLlxuXG5Wb3VzIHZvdXMgZGl0ZXMgcGV1dC3DqnRyZTogKuKAnEplIG1lIHNlbnMgZGlzcXVhbGlmacOpIGNhciBqZSBzdWlzIHVuIG1hdXZhaXMgY2hyw6l0aWVuLuKAnSpcblxuQ+KAmWVzdCBsZSBwacOoZ2UgZGUgbOKAmWlzb2xlbWVudC5cblxuKiBPbiBuZSBncmFuZGl0IHBhcyB0b3V0IHNldWwuXG4qIEPigJllc3QgbGUgcsO0bGUgZGUgbOKAmcOJZ2xpc2UgZGUgbm91cyBhaWRlciDDoCBncmFuZGlyLlxuKiBM4oCZw4lnbGlzZSBlc3QgbGUgbW95ZW4gcXVlIERpZXUgdmV1dCB1dGlsaXNlciBwb3VyIG5vdXMgcmFwcGVsZXIgc2EgZ3LDomNlLlxuXG4jIyBRdWUgcmV0ZW5pcj9cblxuT24gYsOidGl0IGzigJnDiWdsaXNlIHRvdXMgZW5zZW1ibGUgb3Ugb24gZmFpdCBkdSBzdXIgcGxhY2UgdG91cyBlbnNlbWJsZS4gQ2UgcXVlIGRpdCBKw6lzdXMgYXV4IMOJZ2xpc2VzIGRlIGzigJlBcG9jYWx5cHNlLlxuXG5FbmdhZ2VvbnMtbm91cyBlbnNlbWJsZSwgcGFyIGxhIGdyw6JjZSBkdSBTZWlnbmV1ciwgw6Agdml2cmUgZGVzIHJlbGF0aW9ucyBmYcOnb25uw6llcyBwYXIgbOKAmcOJdmFuZ2lsZS5cblxuQ+KAmWVzdCB1biBpbW1lbnNlIGTDqWZpLCBq4oCZZW4gc3VpcyBjb25zY2llbnQuXG5cbk1haXMgY2UgbuKAmWVzdCBwYXMgw6Agbm91cyBkZSBmaXhlciBsZXMgZXhpZ2VuY2VzLiBD4oCZZXN0IGx1aSBxdWkgZW4gZXN0IGxlIGNoZWYgZXQgY+KAmWVzdCBsdWkgcXVpIHNhaXQgY29tbWVudCBzb24gw4lnbGlzZSBkb2l0IHZpdnJlIHNlcyByZWxhdGlvbnMuXG5cbiMjIyBFdCwgbm91cyBhdm9ucyBkZSBs4oCZZXNwb2lyLCBjYXI6XG5cbjEuIFF1aSBlc3QgcmVzcG9uc2FibGUgZGUgbGEgY3JvaXNzYW5jZSBkZSBs4oCZw4lnbGlzZTogQ2hyaXN0LlxuMi4gUXVlbCBlc3QgbGUgYnV0IGRlIGxhIGNyb2lzc2FuY2UgZGUgbOKAmcOJZ2xpc2U6IHJlc3NlbWJsZXIgQ2hyaXN0LlxuMy4gQ29tbWVudCBs4oCZw4lnbGlzZSBncmFuZGl0LWVsbGU6IGdyw6JjZSDDoCBDaHJpc3QuXG5cbj4gQ+KAmWVzdCBkZSBsdWkgcXVlIGxlIGNvcnBzIHRvdXQgZW50aWVyLCBiaWVuIGNvb3Jkb25uw6kgZXQgc29saWRlbWVudCB1bmkgZ3LDomNlIGF1eCBhcnRpY3VsYXRpb25zIGRvbnQgaWwgZXN0IG11bmksIHRpcmUgc2EgY3JvaXNzYW5jZSBlbiBmb25jdGlvbiBkZSBs4oCZYWN0aXZpdMOpIHF1aSBjb252aWVudCDDoCBjaGFjdW5lIGRlIHNlcyBwYXJ0aWVzIGV0IHPigJnDqWRpZmllIGx1aS1tw6ptZSBkYW5zIGzigJlhbW91ci5cbj5cbj4gPHN1Yj48Y2l0ZT7DiXBow6lzaWVucyA0LjE2PC9jaXRlPjwvc3ViPlxuXG4jIyMgQ29tbWVudCBs4oCZw4lnbGlzZSBncmFuZGl0LWVsbGUgZW4gdW5pdMOpIGV0IG1hdHVyaXTDqT9cblxuKiBQYXIgQ2hyaXN0IHF1aSBlc3QgbGEgc291cmNlIGV0IGxhIGZpbmFsaXTDqSBkZSBsYSBjcm9pc3NhbmNlIGRlIHNvbiDDiWdsaXNlLlxuKiBQYXIgQ2hyaXN0IHF1aSBhZ2l0IGVuIG5vdXMgcG91ciBxdWUgbm91cyBheW9ucyBkZXMgcmVsYXRpb25zIMOgIHNvbiBpbWFnZSwgZW1wcmVpbnRlIGTigJlhbW91ciwgZGUgdsOpcml0w6kgZXQgZGUgbWF0dXJpdMOpLlxuXG5JbCBlc3QgbGEgbWFpbiwgbm91cyBzb21tZXMgc2VzIGluc3RydW1lbnRzLiBMYSBjb25zw6lxdWVuY2UgZXN0IHF1ZSBwb3VyIG5hw650cmUgZXQgcG91ciBncmFuZGlyLCBs4oCZw4lnbGlzZSBu4oCZYSBiZXNvaW4gcXVlIGRlIENocmlzdCBldCBzb24gw4l2YW5naWxlLiBOb3RyZSBkZXZvaXIgZXN0IGRlIGTDqWxpYsOpcsOpbWVudCB2b3Vsb2lyIHZpdnJlIGzigJnDiXZhbmdpbGUgZXQgbOKAmcOJZ2xpc2UuXG5cbiMjIFF1ZWwgUsOpY29uZm9ydCFcblxuTm91cyBwb3V2b25zIGRvbmMgcmVjb25uYWl0cmUgaHVtYmxlbWVudCBub3RyZSBmYWlibGVzc2UsIG5vdHJlIGluY2FwYWNpdMOpIGV0IG5vdHJlIGluZGlnbml0w6kgw6AgbGUgc2VydmlyLlxuXG5D4oCZZXN0IG3Dqm1lIGluZGlzcGVuc2FibGUgZGUgbGUgZmFpcmUgYWZpbiBkZSBzZSBsYWlzc2VyIGNvbmR1aXJlIHBhciBzYSBncsOiY2UgZXQgcG91ciBxdWUgc29uIGFtb3VyIGFnaXNzYW50IGVuIG5vdXMgc29pdCBtYW5pZmVzdGUgZGFucyBub3MgcmVsYXRpb25zLlxuXG5FbmdhZ2VvbnMtbm91cyBlbnNlbWJsZSwgcGFyIGxhIGdyw6JjZSBkdSBTZWlnbmV1ciwgw6Agdml2cmUgZGVzIHJlbGF0aW9ucyBjZW50csOpZXMgc3VyIGzigJnDiXZhbmdpbGUuIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiUmFwaGHDq2wgQ2hhcnJpZXIiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvcmFwaGFlbF9jaGFycmllcl8wM19jb3BpZV8xX3NjYWxlZF9lMTY1NzgwMTI1Nzc4N19mOTM1OTk0MDM4LmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0LzFfMjdkYTVlMDNhMy5wbmciLCJoZWlnaHQiOjcyMCwid2lkdGgiOjEyODAsImFsdGVybmF0aXZlVGV4dCI6IiIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn0sInRvcGljcyI6W3sibmFtZSI6IkRvY3RyaW5lIGRlIGzigJnDiWdsaXNlIn0seyJuYW1lIjoiR291dmVybmVtZW50IGRlIGzigJnDiWdsaXNlIn0seyJuYW1lIjoiVmllIGTigJnDiWdsaXNlIn0seyJuYW1lIjoiRGlzY2lwbGluZSBk4oCZw4lnbGlzZSJ9LHsibmFtZSI6IlByw6lkaWNhdGlvbiJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZVBvZGNhc3QiLCJwb2RjYXN0Ijp7InNsdWciOiJwcmVkaWNhdGlvbnMtdHBzZyIsIm5hbWUiOiJQcsOpZGljYXRpb25zIFRQU0cifSwiZW1iZWRBdWRpbyI6IjxpZnJhbWUgc3R5bGU9XCJib3JkZXItcmFkaXVzOjEycHhcIiBzcmM9XCJodHRwczovL29wZW4uc3BvdGlmeS5jb20vZW1iZWQvZXBpc29kZS81bERuSlJYMjFTY3BQTkxNc2Ewc1hFP3V0bV9zb3VyY2U9Z2VuZXJhdG9yJnRoZW1lPTBcIiB3aWR0aD1cIjEwMCVcIiBoZWlnaHQ9XCIxNTJcIiBmcmFtZUJvcmRlcj1cIjBcIiBhbGxvd2Z1bGxzY3JlZW49XCJcIiBhbGxvdz1cImF1dG9wbGF5OyBjbGlwYm9hcmQtd3JpdGU7IGVuY3J5cHRlZC1tZWRpYTsgZnVsbHNjcmVlbjsgcGljdHVyZS1pbi1waWN0dXJlXCIgbG9hZGluZz1cImxhenlcIj48L2lmcmFtZT4iLCJlbWJlZFZpZGVvIjoiPGlmcmFtZSB3aWR0aD1cIjE3NTlcIiBoZWlnaHQ9XCI2NDNcIiBzcmM9XCJodHRwczovL3d3dy55b3V0dWJlLmNvbS9lbWJlZC9WMjBnVGg2SXo0OFwiIHRpdGxlPVwiXCIgZnJhbWVib3JkZXI9XCIwXCIgYWxsb3c9XCJhY2NlbGVyb21ldGVyOyBhdXRvcGxheTsgY2xpcGJvYXJkLXdyaXRlOyBlbmNyeXB0ZWQtbWVkaWE7IGd5cm9zY29wZTsgcGljdHVyZS1pbi1waWN0dXJlOyB3ZWItc2hhcmVcIiByZWZlcnJlcnBvbGljeT1cInN0cmljdC1vcmlnaW4td2hlbi1jcm9zcy1vcmlnaW5cIiBhbGxvd2Z1bGxzY3JlZW4+PC9pZnJhbWU+In0se30seyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJDb21tZW50IGzigJnDiWdsaXNlIGxvY2FsZSBwZXV0LWVsbGUgZ3JhbmRpciBlbiBtYXR1cml0w6kgZXQgZW4gdW5pdMOpPyBDb21tZW50IHBvdXZvbnMtbm91cywgZW4gdGFudCBxdWUgY29tbXVuYXV0w6ksIHZpdnJlIGV0IHNlcnZpciBs4oCZw4lnbGlzZSBjb21tZSBDaHJpc3QgbGUgc291aGFpdGU/IMOAIHRyYXZlcnMgbGVzIMOJY3JpdHVyZXMsIG5vdXMgZMOpY291dnJvbnMgY29tbWVudCBs4oCZw4lnbGlzZSBlc3QgYXBwZWzDqWUgw6Agc2UgZMOpdmVsb3BwZXIgcG91ciByZWZsw6l0ZXIgbOKAmWFtb3VyIGV0IGzigJlleGVtcGxlIGRlIErDqXN1cy1DaHJpc3QuIn1dfSx7ImlkIjoiNDA4NiIsInRpdGxlIjoiTGEgY8OpcsOpbW9uaWUgZOKAmW91dmVydHVyZSBkZXMgSk8gUGFyaXMgMjAyNCDDqXRhaXQtZWxsZSB2cmFpbWVudCBzYXRhbmlxdWU/Iiwic2x1ZyI6ImNlcmVtb25pZS1vdXZlcnR1cmUtam8tcGFyaXMtMjAyNC12cmFpbWVudC1zYXRhbmlxdWUiLCJ0eXBlIjoiYXJ0aWNsZSIsInB1Ymxpc2hlZF9hdCI6IjIwMjQtMDgtMDJUMDM6NDU6MDAuMDAwWiIsImJvZHkiOiJgPGRpdiBpZD1cImVsZXZlbmxhYnMtYXVkaW9uYXRpdmUtd2lkZ2V0XCIgZGF0YS1oZWlnaHQ9XCI5MFwiIGRhdGEtd2lkdGg9XCIxMDAlXCIgZGF0YS1mcmFtZWJvcmRlcj1cIm5vXCIgZGF0YS1zY3JvbGxpbmc9XCJub1wiIGRhdGEtcHVibGljdXNlcmlkPVwiYzM5YjIzYmJiMTdlODBiZTZiNWZlM2Q2ZmVkNGQ5N2VmOGMxZTQ1YmM1N2Y1NDIwNzQyM2I4ZDJlYjExMWZiMlwiIGRhdGEtcGxheWVydXJsPVwiaHR0cHM6Ly9lbGV2ZW5sYWJzLmlvL3BsYXllci9pbmRleC5odG1sXCIgPkxvYWRpbmcgdGhlIDxhIGhyZWY9XCJodHRwczovL2VsZXZlbmxhYnMuaW8vdGV4dC10by1zcGVlY2hcIiB0YXJnZXQ9XCJfYmxhbmtcIiByZWw9XCJub29wZW5lclwiPkVsZXZlbmxhYnMgVGV4dCB0byBTcGVlY2g8L2E+IEF1ZGlvTmF0aXZlIFBsYXllci4uLjwvZGl2PjxzY3JpcHQgc3JjPVwiaHR0cHM6Ly9lbGV2ZW5sYWJzLmlvL3BsYXllci9hdWRpb05hdGl2ZUhlbHBlci5qc1wiIHR5cGU9XCJ0ZXh0L2phdmFzY3JpcHRcIj48L3NjcmlwdD5gXG5cbkrigJlhaSBldSBsZSBzZW50aW1lbnQgZOKAmWFzc2lzdGVyIMOgIHVuIGhvbGQtdXAuIENlIHF1aSBhdXJhaXQgZMO7IGPDqWzDqWJyZXIgbCd1bml2ZXJzYWxpc21lIGRlcyB2YWxldXJzIGR1IHNwb3J0IHMnZXN0IHRyYW5zZm9ybcOpIGVuIFwiY2FtcGFnbmUgZCfDqXZhbmfDqWxpc2F0aW9uXCIgcXVlZXIsIHVuZSBpZMOpb2xvZ2llIHF1aSDigJRzb2l0IGRpdCBlbiBwYXNzYW504oCUIHNlcmEgY2F0YXN0cm9waGlxdWUgcG91ciBsZXMgZmVtbWVzIHF1YW5kIGVsbGUgc2VyYSBkw6ltb2NyYXRpc8OpZSBwYXIgbGUgQ0lPPHN1cD5bMV0oL2FydGljbGUvY2VyZW1vbmllLW91dmVydHVyZS1qby1wYXJpcy0yMDI0LXZyYWltZW50LXNhdGFuaXF1ZSNub3Rlcyk8L3N1cD4gKGonZXhwbGlxdWUgcGx1cyBiYXMgcG91cnF1b2kgamUgcmV0aWVucyBsZSB0ZXJtZSBcInF1ZWVyXCI8c3VwPlsyXSgvYXJ0aWNsZS9jZXJlbW9uaWUtb3V2ZXJ0dXJlLWpvLXBhcmlzLTIwMjQtdnJhaW1lbnQtc2F0YW5pcXVlI25vdGVzKTwvc3VwPikuXG5cbkNvbW1lIGJlYXVjb3VwIGRlIGdlbnMsIGV0IHBhcnRpY3VsacOocmVtZW50IGxlcyBjaHLDqXRpZW5zLCBqJ2FpIMOpdMOpIGNob3F1w6kgcGFyIGxlIHRyb3VwbGUsIGwnb2RlIGF1eCBMR0JUUUlBKywgbGUgZMOpZmlsw6kgZGVzIGdlbmRlciBmbHVpZGVzIGV0IGRlcyBkcmFnIHF1ZWVucywgbGEgcGFyb2RpZSBkZSBsYSBjw6huZSBldCBsZSBtZWMgZW4gYmxldSB0b3V0IG51LlxuXG5MZXMgY29tbWVudGFpcmVzIGRlIEZyYW5jZSBUw6lsw6l2aXNpb25zLCBub3RhbW1lbnQgY2V1eCBkZSBEYXBobsOpIELDvHJraSAoZGlyZWN0cmljZSBcInN0eWxpc21lIGV0IGNvc3R1bWVzXCIgZGUgbGEgY8OpcsOpbW9uaWUpLCBlbiBjaGFyZ2UgZGUgbCdleMOpZ8Ooc2UgZGVzIHZhbGV1cnMgZHUgc2hvdywgb250IGFqb3V0w6kgw6AgbW9uIGV4YXNww6lyYXRpb24uIChPbiBhIG3Dqm1lIGV1IGwnw6lsb2dlIGR1IG1lc3NhZ2UgYW50aWNhcGl0YWxpc3RlIGRlIGxhIGNoYW5zb24gKkltYWdpbmUqIGVudHJlIHVuIHBsYWNlbWVudCBkZSBwcm9kdWl0IHBvdXIgTFZNSCBldCBsZXMgTWluaW9ucy4pXG5cbkVuIHBsdXMgZGUgY2VsYSwgamUgbmUgY29tcHJlbmFpcyByaWVuIGF1IGTDqXJvdWxlbWVudCBkZSBsYSBjw6lyw6ltb25pZTsgdG91dCBtZSBzZW1ibGFpdCBkw6ljb3VzdSBldCBwYXJ0aXIgZGFucyB0b3VzIGxlcyBzZW5zLiBKJ2FpIGRvbmMgZGl0IMOgIG1hIGZlbW1lIHF1ZSBqZSBuZSBjb21wcmVuYWlzIGTDqWNpZMOpbWVudCByaWVuIMOgIGwnYXJ0IGNvbnRlbXBvcmFpbi4g4oCcSmUgY29uZmlybWXigJ0sIG0nYS10LWVsbGUgcsOpcG9uZHUuXG5cbkplIG5lIHZvdXMgY2FjaGUgcGFzIHF1ZSBqJ2FpIHBlc3TDqSBjb250cmUgbGUgQ09KTyBldCBsZSBnb3V2ZXJuZW1lbnQgcXVpIG9udCB2YWxpZMOpIGNlIHByb2dyYW1tZSBvdXRyYW5jaWVyIGV0IHBlcnZlcnMuIEplIG1lIHN1aXMgc2VudGkgZW4gcGhhc2UgYXZlYyBsYSB0cmlzdGVzc2UgZXhwcmltw6llIHBhciBNb25zZWlnbmV1ciBFbW1hbnVlbCBHb2JpbGxpYXJkLCDDqXbDqnF1ZSBkw6lsw6lndcOpIHBvdXIgbGVzIEpldXggb2x5bXBpcXVlczxzdXA+WzNdKC9hcnRpY2xlL2NlcmVtb25pZS1vdXZlcnR1cmUtam8tcGFyaXMtMjAyNC12cmFpbWVudC1zYXRhbmlxdWUjbm90ZXMpPC9zdXA+LlxuXG5BdS1kZWzDoCBkZSBsJ8OpbW90aW9uIGNvbXByw6loZW5zaWJsZSBldCBsw6lnaXRpbWUgZHUgbW9tZW50LCBjJ2VzdCBlbiBkaXNjaXBsZSBkZSBKw6lzdXMgcXVlIG5vdXMgZGV2b25zIHLDqWZsw6ljaGlyIGV0IHLDqWFnaXIuIEMnZXN0IGNlIHF1ZSBqZSB2YWlzIG1vZGVzdGVtZW50IHRlbnRlciBkZSBmYWlyZS5cblxuIyMgVW5lIGPDqXLDqW1vbmllIMOgIG5vdHJlIGltYWdlXG5cbk1hbGdyw6kgbGUgcMOpY2jDqSwgbCdob21tZSBlc3QgbCdvYmpldCBkZSBsYSBncsOiY2UgY29tbXVuZSBkZSBEaWV1LiDDgCBjYXVzZSBkZSBjZWxhLCB0b3V0ZSBwcm9kdWN0aW9uIGN1bHR1cmVsbGUgZGUgbCdodW1hbml0w6kgZXN0IGNhcmFjdMOpcmlzw6llIHBhciBsZSB2cmFpLCBsZSBiZWF1LCBldCBsZSBib24uXG5cbk1haXMgbCdob21tZSBkw6ljaHUgZXN0IGTDqXByYXbDqSBwYXIgbGUgcMOpY2jDqS4gw4AgY2F1c2UgZGUgY2VsYSwgdG91dGUgcHJvZHVjdGlvbiBjdWx0dXJlbGxlIGVzdCBlbnRhY2jDqWUgcGFyIGxlIG1lbnNvbmdlLCBsYSBsYWlkZXVyIGV0IGwnaW1tb3JhbGl0w6kuXG5cbkNldHRlIGPDqXLDqW1vbmllIGZhaXNhaXQgbGEgc3ludGjDqHNlIGRlIGNlIGNsYWlyLW9ic2N1ci4gSWwgZXN0IGRvbmMgbm9ybWFsIGQneSByZXRyb3V2ZXIgZGVzIMOpbMOpbWVudHMgcXVpIG5vdXMgcGxhaXNlbnQgKHZpdmUgWml6b3UpIGV0IGQnYXV0cmVzIHF1aSBub3VzIGV4YXNww6hyZW50LiBDZXR0ZSBjw6lyw6ltb25pZSBu4oCZYXBwb3J0YWl0IHJpZW4gZGUgc3VycHJlbmFudCBuaSBkZSBub3V2ZWF1LiBFbGxlIMOpdGFpdCBtw6ptZSB0csOocyBjb25mb3JtZSDDoCBs4oCZZXNwcml0IG9jY2lkZW50YWwgZHUgc2nDqGNsZSwgYWxsYW50IGp1c3F14oCZw6AgbGEgY2FyaWNhdHVyZS5cblxuVW5lIHBhcnRpZSBkZSBub3RyZSDDqW1vaSBlc3QgZHVlIMOgIGwnaW1wb3J0YW5jZSBkZSBsJ8OpdsOpbmVtZW50IGV0IGRlIHNhIHBvcnTDqWUgdW5pdmVyc2VsbGUgKDIgbWlsbGlhcmRzIGRlIHTDqWzDqXNwZWN0YXRldXJzKSBldCBhdSBzZW50aW1lbnQgcXUnaWwgZXN0IHLDqWFsaXPDqSBlbiBub3RyZSBub20uXG5cbiMjIExpdHVyZ2llIG9seW1waXF1ZSBldCByZWxpZ2lvbiBjaXZpbGVcblxuUHVpc3F1ZSBsJ2hvbW1lIGVzdCBjcsOpw6kgw6AgbCdpbWFnZSBkZSBEaWV1LCB0b3V0IGNlIHF1J2lsIGZhaXQgcmV2w6p0IHVuZSBkaW1lbnNpb24gc3Bpcml0dWVsbGUgZXQgcmVsaWdpZXVzZS4gQydlc3QgY2UgcXVlIEplYW4gQ2FsdmluIGFwcGVsYWl0ICpsZSBzZW5zIGRlIGxhIGRpdmluaXTDqSouXG5cblNlbG9uIENoYXJsZXMgVGF5bG9yLCBsYSBzw6ljdWxhcmlzYXRpb24gZXN0LCBlbnRyZSBhdXRyZXMsIGxlIHBhc3NhZ2UgZCd1bmUgdmlzaW9uIGR1IG1vbmRlIGluc2NyaXRlIGRhbnMgdW4gY2FkcmUgdHJhbnNjZW5kYW50IMOgIHVuIGNhZHJlIGltbWFuZW50LCBvw7kgcmllbiBuJ2VzdCBwbHVzIGdsb3JpZXV4IHF1ZSBsJ2hvbW1lLiDDgCBjYXVzZSBkdSBzZW5zIGRlIGxhIGRpdmluaXTDqSBxdWUgbm91cyBhdm9ucyBlbiBub3VzLCBsYSByZWxpZ2lvbiBuZSBkaXNwYXJhw650IHBhcywgZWxsZSBkZXZpZW50IHNpbXBsZW1lbnQgaW1tYW5lbnRlLlxuXG5UaW0gS2VsbGVyIGwnaWxsdXN0cmUgcGFyZmFpdGVtZW50OlxuXG4+IE5vdXMgbmUgbm91cyBwcm9zdGVybm9ucyBwYXMgcGh5c2lxdWVtZW50IGRldmFudCB1bmUgc3RhdHVlIGQnQXBocm9kaXRlLCBtYWlzIGRlIG5vbWJyZXVzZXMgZmVtbWVzIGF1am91cmQnaHVpIHNvbnQgcG91c3PDqWVzIMOgIGxhIGTDqXByZXNzaW9uIGV0IGF1eCB0cm91YmxlcyBhbGltZW50YWlyZXMgcGFyIHNvdWNpIG9ic2Vzc2lvbm5lbCBkZSBsZXVyIGltYWdlLiBOb3VzIG5lIGJyw7tsb25zIHBhcyBkJ2VuY2VucyBkZXZhbnQgbCdpbWFnZSBkJ0FydMOpbWlzLCBtYWlzIHF1YW5kIGwnYXJnZW50IGV0IGxhIGNhcnJpw6hyZSBwcmVubmVudCBkZXMgcHJvcG9ydGlvbnMgaW5jb21tZW5zdXJhYmxlcywgbm91cyBhY2NvbXBsaXNzb25zIHVuZSBzb3J0ZSBkZSBzYWNyaWZpY2UgZCdlbmZhbnRzLCBlbiBuw6lnbGlnZWFudCBub3MgZmFtaWxsZXMgZXQgbm9zIGNvbW11bmF1dMOpcyBhZmluIGQnYXJyaXZlciBlbiBoYXV0IGRlIGwnw6ljaGVsbGUgZmluYW5jacOocmUgZXQgcHJvZmVzc2lvbm5lbGxlLlxuPlxuPiBgPHN1Yj48Y2l0ZT5UaW0gS2VsbGVyLCA8YSBocmVmPVwiaHR0cHM6Ly9ibGZzdG9yZS5jb20vcHJvZHVjdHMvbGVzLWlkb2xlcy1kdS1jb2V1clwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj48ZW0+SWRvbGVzIGR1IGPFk3VyPC9lbT48L2E+LCBDbMOpLCBwLjk8L2NpdGU+PC9zdWI+YFxuXG5MYSBwaGlsb3NvcGhlIENoYW50YWwgRGVsc29sIGFmZmlybWUgcXVlIGxhIHBvc3QtY2hyw6l0aWVudMOpIHJlcHLDqXNlbnRlIHVuIHJldG91ciDDoCBsYSBwcsOpLWNocsOpdGllbnTDqSwgYXUgcGFnYW5pc21lIGFudGlxdWUuIFNlbG9uIGVsbGUsIGxlIGTDqXNlbmNoYW50ZW1lbnQgZGUgbCdPY2NpZGVudCBuZSBjb25kdWl0IHBhcyDDoCB1biBzaW1wbGUgcmVtcGxhY2VtZW50IGRlIGwnYW5jaWVuIHBhciBkdSBub3V2ZWF1LCBtYWlzIMOgIHVuZSBmb3JtZSBkZSByZXRvdXIgw6AgdW4gY2FkcmUgbW9yYWwgZXQgb250b2xvZ2lxdWUgcXVpIHByw6ljw6hkZSBjZWx1aSBkdSBjaHJpc3RpYW5pc21lLCB1bmUgcsOpdm9sdXRpb24g4oCcYXUgc2VucyBzdHJpY3QgZGUgcmV0b3VyIGRlIGN5Y2xlLCBkYW5zIGxlcyBkZXV4IGRvbWFpbmVzIGZvbmRhdGV1cnMgZGUgbCdleGlzdGVuY2UgaHVtYWluZTogbGEgbW9yYWxlIGV0IGwnb250b2xvZ2ll4oCdPHN1cD5bNF0oL2FydGljbGUvY2VyZW1vbmllLW91dmVydHVyZS1qby1wYXJpcy0yMDI0LXZyYWltZW50LXNhdGFuaXF1ZSNub3Rlcyk8L3N1cD4uXG5cblB1aXNxdWUgbm91cyB2aXZvbnMgZGFucyB1bmUgw6hyZSBkdSBuw6lvcGFnYW5pc21lLCBj4oCZZXN0IGxvZ2lxdWUgcXVlIGxlcyBKZXV4IE9seW1waXF1ZXMsIHRyb3V2YW50IGxldXIgb3JpZ2luZSBkYW5zIGwnYW50aXF1aXTDqSwgc29pZW50IGxhIHBsdXMgdW5pdmVyc2VsbGUgZGVzIGbDqnRlcywgcmFzc2VtYmxhbnQgbGVzIG5hdGlvbnMgcG91ciB1biBjdWx0ZSBkb250IGwnaG9tbWUgZXN0IGwnb2JqZXQuIFBpZXJyZSBkZSBDb3ViZXJ0aW4gcGFybGFpdCBkZSBcInJlbGlnaW9uIGRlIGzigJlhdGhsw6h0ZVwiPHN1cD5bNV0oL2FydGljbGUvY2VyZW1vbmllLW91dmVydHVyZS1qby1wYXJpcy0yMDI0LXZyYWltZW50LXNhdGFuaXF1ZSNub3Rlcyk8L3N1cD4uXG5cbkR1cmFudCB1biBtb2lzLCBsYSB2aWxsZSDDqWx1ZSBkZXZpZW50IHVuZSB0ZXJyZSBwcm9taXNlLCBzZXMgc3RhZGVzIGRlcyB0ZW1wbGVzIG/DuSBsZXMgcMOobGVyaW5zIHByaWVudCBldCBjb21tdW5pZW50IGF1dG91ciBkZSBsZXVycyBjaGFtcGlvbnMgZXQgbG91ZW50IGxldXJzIGV4cGxvaXRzLiBQcsOqdHJlcyBtw6lkYWlsbMOpcyBldCBhdXLDqW9sw6lzIGRlIGdsb2lyZSwgaWxzIHJlcHLDqXNlbnRlbnQgbGV1ciBwZXVwbGUgZXQgbCdlc3ByaXQgZGUgbCdvbHltcGlzbWUuIENlcyByaXRlcyBmb25jdGlvbm5lbnQgY29tbWUgZGVzIG9mZnJhbmRlcyDDoCBsJ2VzcHJpdCBkZXMgSmV1eCwgdW5pZmlhbnQgbCdhdWRpZW5jZSBkYW5zIHVuZSBleHDDqXJpZW5jZSBjb2xsZWN0aXZlIGVzdGjDqXRpcXVlIGV0IHNwaXJpdHVlbGxlLlxuXG5MYSBjw6lyw6ltb25pZSBkJ291dmVydHVyZSBkZXMgSk8gZXN0IGxhIGbDqnRlIHJlbGlnaWV1c2UgaW5hdWd1cmFsZSBkZSBjZSBncmFuZCBtb21lbnQgZGUgY29tbXVuaW9uIHVuaXZlcnNlbGxlLCBjb21tZSDDoCBCYWJlbC4gQWNjb21wYWduw6llcyBkZSBkYW5zZXMgZXQgZGUgY2hhbnRzLCBsZXMgbmF0aW9ucyBmb250IGxldXIgcHJvY2Vzc2lvbiBzb2xlbm5lbGxlIMOgIGxhIHN1aXRlIGRlIGxhIGZsYW1tZS5cblxuRGFucyAqTGEgY2l0w6kgZGUgRGlldSosIHNhaW50IEF1Z3VzdGluIGNvbnNhY3JlIGJlYXVjb3VwIGQnZXNwYWNlIMOgIGxhIGNyaXRpcXVlIGRlIGxhIHJlbGlnaW9uIGNpdmlsZSBkZSBsYSBSb21lIGFudGlxdWUuIENlcyBjw6lsw6licmF0aW9ucyBwdWJsaXF1ZXMgZMOpbWVzdXLDqWVzIHZpc2FpZW50IMOgIGZvcmdlciB1bmUgdW5pdMOpIMOgIHRyYXZlcnMgbCdFbXBpcmUgZXQgw6AgdHJhbnNtZXR0cmUgZGVzIHZhbGV1cnMgY29tbXVuZXMuIERlcnJpw6hyZSBsZXMgcml0ZXMgcGHDr2VucywgQXVndXN0aW4gZGlzY2VybmFpdCBsJ2FjdGl2aXTDqSBkw6ltb25pYXF1ZSBxdWkgZW50cmHDrm5haXQgdW5lIGltbW9yYWxpdMOpIGdhbG9wYW50ZSwgbGEgZGl2aXNpb24gZXQgbGEgZG9taW5hdGlvbi4gU2Vsb24gbHVpLCBjZXMgaWRvbGVzL2TDqW1vbnMgcGHDr2VucyBuZSBjb25kdWlzZW50IMOgIHJpZW4gZCdhdXRyZSBxdSfDoCBsYSBzYXRpc2ZhY3Rpb24gc2FucyBlbnRyYXZlIGRlcyBkw6lzaXJzIGRlIGxhIGNoYWlyLlxuXG5BdWd1c3RpbiBkw6l2ZWxvcHBlIGxlIHRow6htZSBkZSBsYSBcImxpYmlkbyBkb21pbmFuZGlcIiBxdWkgYW5pbWUgUm9tZS4gQ2UgdGVybWUgZMOpc2lnbmUgbCdhc3NlcnZpc3NlbWVudCDDoCBsYSBjb252b2l0aXNlIGRlIGRvbWluZXI6IGwnaG9tbWUgZXN0IGRvbWluw6kgcGFyIHNvbiBlbnZpZSBkZSBkb21pbmVyLiBBdWd1c3RpbiBvYnNlcnZlIGNlbGEgZGFucyBsZSBkw6lzaXIgZXhjZXNzaWYgZGUgUm9tZSBwb3VyIGxhIGxvdWFuZ2UgZGUgc2EgZ2xvaXJlLiBFbiBlZmZldCwgbGVzIFJvbWFpbnMgZMOpc2lyYWllbnQgw6p0cmUgbGlicmVzLCBldCBuZSBwb3V2YWllbnQgdG9sw6lyZXIgbGEgaG9udGUgZCfDqnRyZSBlbiBzZXJ2aXR1ZGUuIExlIGTDqXNpciBkZSBnbG9pcmUgc2lnbmlmaWFpdCBxdWUgbGV1ciBsaWJlcnTDqSBzZXVsZSDDqXRhaXQgaW5zdWZmaXNhbnRlOyBpbHMgZGV2YWllbnQgc291bWV0dHJlIGNldXggcXVpIGxlcyBlbnRvdXJhaWVudC5cblxuUmllbiBkZSBub3V2ZWF1IHNvdXMgbGUgc29sZWlsLiBDZXR0ZSBsb2dpcXVlIHBldXQgw6p0cmUgw6l0ZW5kdWUgw6AgbGEgY8OpcsOpbW9uaWUgZGVzIEpPLiDDgCBSb21lIGNvbW1lIMOgIFBhcmlzLCBsJ2hvbW1lLCBlc2NsYXZlIGRlIHNhIHF1w6p0ZSBkZSBsaWJlcnTDqSwgYXNzb3V2aXQgc2VzIGTDqXNpcnMgZXQgc291bWV0IChjdWx0dXJlbGxlbWVudCkgbGVzIGF1dHJlcyBhZmluIHF14oCZaWxzIGzigJlhcHByb3V2ZW50LiBQcm9jbGFtZXIgZmnDqHJlbWVudCBsYSBsaWJlcnTDqSBzZXh1ZWxsZSBldCBpZGVudGl0YWlyZSwgdHJhbnNncmVzc2VyIHRvdXMgbGVzIHRhYm91cywgbW9xdWVyIGxlcyByYWNpbmVzIGN1bHR1cmVsbGVzIGV0IHJlbGlnaWV1c2VzIChNYXJpZSBBbnRvaW5ldHRlIGTDqWNhcGl0w6llIGV0IGPDqG5lIHBhcm9kacOpZSkuXG5cblBhcmlzIHZvdWxhaXQgw6p0cmUgZ2xvcmlmacOpZSBwb3VyIHNhIHByw6l0ZW5kdWUgbGliZXJ0w6kgZXQgc29uIGFycm9nYW5jZS4gRWxsZSBlc3QgbGEgdmlsbGUgbHVtacOocmUgcXVpIMOpdmVpbGxlIGxlcyBjb25zY2llbmNlcyBkZXMgaG9tbWVzIGFmaW4gZGUgbGVzIHJlbmRyZSBcIitcIiBxdWVlci4gUXVhbmQgbGEgY8OpcsOpbW9uaWUgZXN0IGNyaXRpcXXDqWUsIHNlcyBwYXJ0aXNhbnMgdHJhcXVlbnQsIGF1IG5vbSBkZSBsJ2luY2x1c2l2aXTDqSwgbGVzIHLDqWFjcyBkZSBcImxhIEZyYW5jZSByYW5jZVwiPHN1cD5bNl0oL2FydGljbGUvY2VyZW1vbmllLW91dmVydHVyZS1qby1wYXJpcy0yMDI0LXZyYWltZW50LXNhdGFuaXF1ZSNub3Rlcyk8L3N1cD4gcXVpIG9zZW50IGxlcyBqdWdlci5cblxuQ2V0dGUgKmxpYmlkbyBkb21pbmFuZGkqIHF1ZWVyIHF1aSBqdWJpbGUgZGUgXCJtZXR0cmUgZW4gUExTIHRvdXMgbGVzIGZhY2hvc1wiPHN1cD5bN10oL2FydGljbGUvY2VyZW1vbmllLW91dmVydHVyZS1qby1wYXJpcy0yMDI0LXZyYWltZW50LXNhdGFuaXF1ZSNub3Rlcyk8L3N1cD4gcsOpdsOobGUgbGVzIG1vdGl2YXRpb25zIHNvdXMtamFjZW50ZXMgZGUgbGEgY8OpcsOpbW9uaWUuXG5cblNhY2hlei1sZSwgc2kgdm91cyBu4oCZw6p0ZXMgcGFzIGRhbnMgbGV1ciBjYW1wLCB2b3VzIMOqdGVzIGZhc2Npc3RlLCByYWNpc3RlLCBk4oCZZXh0csOqbWUgZHJvaXRlLCBuYXppLCBiaWdvdCwgdHJ1bXBpc3RlLCBwcm8tUnVzc2UgZXQgY291cGFibGUgZGUgdG91dGVzIGxlcyBwaG9iaWVzLlxuXG4jIyBMJ2VzY2hhdG9sb2dpZSBxdWVlciBkZSBsYSBjw6lyw6ltb25pZVxuXG5DZXJ0YWlucywgeSBjb21wcmlzIGRlcyBjaHLDqXRpZW5zLCBvbnQgdnUgdW4gY29tcGxvdCBzYXRhbmlzdGVzIG91cmRpIHNlIGNhY2hhbnQgZGFucyBkZXMgcsOpZsOpcmVuY2VzIG9jY3VsdGVzLiBKZSBjcm9pcyBxdWUgYydlc3QgdW5lIG1hdXZhaXNlIGdyaWxsZSBkZSBsZWN0dXJlLiBOb3VzIGRldm9ucyBub3VzIG3DqWZpZXIgZGUgbm9zIGJpYWlzIGRlIGNvbmZpcm1hdGlvbi5cblxuRW4gb3V0cmUsIG51bCBiZXNvaW4gcXVlIGNldHRlIGPDqXLDqW1vbmllIHNvaXQgb2NjdWx0ZSBvdSBjcnlwdGlxdWUgcG91ciBxdSdlbGxlIHNvaXQgZGlhYm9saXF1ZTogbGUgbW9uZGUgZXN0IGVuIGd1ZXJyZSBjb250cmUgRGlldSBkZXB1aXMgbGEgY2h1dGUgZXQgc291cyBsYSBjb3VwZSBkdSBwcmluY2UgZGUgY2UgbW9uZGUgKMOJcCAyLjIpLlxuXG5DZSBxdWUgbm91cyBkZXZvbnMgY29tcHJlbmRyZSwgY+KAmWVzdCBxdWVsbGUgZXNjaGF0b2xvZ2llLCBxdWVsIG1vbmRlIGlkw6lhbCBsZSBjb21pdMOpIG9yZ2FuaXNhdGV1ciBldCBsZSBnb3V2ZXJuZW1lbnQgdm91bGFpZW50IHByw6lzZW50ZXIgYXV4IG5hdGlvbnM6IHVuIG1vbmRlIFwicXVlZXJpc8OpXCIuXG5cbkphbWVzIExpbmRzYXkgZXhwbGlxdWUgbOKAmWFnZW5kYSBxdWVlcjxzdXA+WzhdKC9hcnRpY2xlL2NlcmVtb25pZS1vdXZlcnR1cmUtam8tcGFyaXMtMjAyNC12cmFpbWVudC1zYXRhbmlxdWUjbm90ZXMpPC9zdXA+OlxuXG4+IERhbnMgbGEgYnJhbmNoZSBkdSBwb3N0bW9kZXJuaXNtZSBhcHBsaXF1w6kgYXBwZWzDqWUgVGjDqW9yaWUgUXVlZXIsIGxlIHRlcm1lIFwicXVlZXJcIiBuJ2VzdCBwYXMgc2V1bGVtZW50IHV0aWxpc8OpIGNvbW1lIHVuIG5vbSBldCB1biBhZGplY3RpZiwgbWFpcyBhdXNzaSBjb21tZSB1biB2ZXJiZSwgXCJxdWVlcmlzZXJcIiAocXVlZXJpbmcpLiBcIlF1ZWVyaXNlclwiIHNpZ25pZmllIHByb3ZvcXVlciB1biBjaGFuZ2VtZW50IG91IGFnaXIgZGUgbWFuacOocmUgw6AgcGVydHVyYmVyIGxlcyBub3JtYXRpdml0w6lzLCBjJ2VzdC3DoC1kaXJlIGRlIG1hbmnDqHJlIMOgIHJlamV0ZXIgw6AgbGEgZm9pcyBsZSBub3JtYWwgZXQgbGVzIG5vcm1lcyBlbiB0YW50IHF1ZSBwcmluY2lwZSwgcGFydGljdWxpw6hyZW1lbnQgbWFpcyBwYXMgdW5pcXVlbWVudCBlbiBjZSBxdWkgY29uY2VybmUgbGVzIHF1ZXN0aW9ucyBkZSBzZXhlLCBkZSBnZW5yZSBldCBkZSBzZXh1YWxpdMOpLiBcXFsuLi5cXF1cblxuTGUgXCJxdWVlcmluZ1wiIGVzdCB1biB0eXBlIGQnYWN0aXZpc21lIHF1aSBlbXBsb2llIG5vdGFtbWVudCBsYSBwYXJvZGllLCBsJ2Vzc2VudGlhbGlzbWUgc3RyYXTDqWdpcXVlIGRlcyBvcHBvc2FudHMgZXQgbGEgcmjDqXRvcmlxdWUgZHUgdG90YWxpdGFyaXNtZSBzb2Z0IHBvdXIgaW1wb3NlciBzYSB2aXNpb24gZHUgbW9uZGUuIChVbiBiZWwgZXhlbXBsZSBkZSBjZWxhIGRvbm7DqSBwYXIgU29uaWEgRGV2aWxsZXJzIMOgIGwnZW5jb250cmUgZGUgTWFyaW9uIE1hcsOpY2hhbCBxdWkgY3JpdGlxdWFpdCBsZSBmYWl0IHF1ZSBsZSBDw6lzYXIgZGUgbGEgbWVpbGxldXJlIGFjdHJpY2Ugc29pdCBhdHRyaWJ1w6kgw6AgdW5lIGZlbW1lIHRyYW5zPHN1cD5bOV0oL2FydGljbGUvY2VyZW1vbmllLW91dmVydHVyZS1qby1wYXJpcy0yMDI0LXZyYWltZW50LXNhdGFuaXF1ZSNub3Rlcyk8L3N1cD4uXG5cbkxpbmRzYXkgcG91cnN1aXQ6XG5cbj4gUGx1cyBwcsOpY2lzw6ltZW50LCBsZSBxdWVlcmluZyBjaGVyY2hlIMOgIGV4cG9zZXIgb3Ugw6AgZMOpY291dnJpciBxdWUgbm9zIG5vcm1lcyBuZSBzb250IGVuIGZhaXQgcXVlIGRlcyBsaW1pdGVzIMOgIHVuIGVuc2VtYmxlIGJpZW4gcGx1cyBsYXJnZSBkZSBwb3NzaWJpbGl0w6lzIOKAlCBkZXMgY29uc3RydWN0aW9ucyBzb2NpYWxlcyBxdWUgbm91cyBub3VzIHNlbnRvbnMgaW5jYXBhYmxlcyBkZSBwcm9kdWlyZSwgbWFpcyBxdWkgcG91cnJhaWVudCDDqnRyZSBkaWZmw6lyZW50ZXMgc2kgbm91cyBzb3J0aW9ucyBkZXMgc2Now6ltYXMgZGUgcGVuc8OpZSBzdGFnbmFudHMsIGRlcyBkaXNjb3VycyBkb21pbmFudHMgZXQgZGVzIGF0dGVudGVzIHNvY2lhbGVzIGNvbnRyYWlnbmFudGVzIHF1aSBub3VzIGVtcMOqY2hlbnQgZGUgbGVzIG1ldHRyZSBlbiDFk3V2cmUuIENldCDDqXRhdCBkJ2VzcHJpdCBkb2l0IGJlYXVjb3VwIGF1IHBoaWxvc29waGUgcG9zdG1vZGVybmUgZnJhbsOnYWlzIE1pY2hlbCBGb3VjYXVsdCBcXFvigKZcXF0uIERlIGNldHRlIG1hbmnDqHJlLCBpbCBlc3QgcG9zc2libGUgZGUgdm9pciBsZSBnZXJtZSBzZW5zaWJsZSBldCBpbnTDqXJlc3NhbnQgcXVpIG1haW50aWVudCBsYSB0aMOpb3JpZSBxdWVlciBlbiB2aWUgdG91dCBlbiByZWNvbm5haXNzYW50IHF1J2VsbGUgZXN0IHJpZGljdWxlLCBmb25kYW1lbnRhbGVtZW50IHBldSBzw6lyaWV1c2UgZXQgZMOpc2ludMOpcmVzc8OpZSBkZSBsYSByw6lhbGl0w6ksIHZvaXJlIGhvc3RpbGUgw6AgY2VsbGUtY2kgKGxhIHbDqXJpdMOpLCBhcHLDqHMgdG91dCwgbm91cyBjb250cmFpbnQgdG91cykuXG5cbk5vdXMgbmUgZGV2cmlvbnMgamFtYWlzIG91YmxpZXIgcXVlIGxhIHBlbnPDqWUgcG9zdG1vZGVybmUgZXN0IG7DqWUgZW4gRnJhbmNlLiBMZXMgb3JnYW5pc2F0ZXVycyBkZSBsYSBjw6lyw6ltb25pZSB2b3VsYWllbnQgc2FuY3RpZmllciBsZXMgbmF0aW9ucyBlbiBicmlzYW50IGxlcyBjb2RlcyBxdWkgbGVzIGVtcMOqY2hlbnQgZCfDqnRyZSBkYW5zIGwnZXNwcml0IHF1ZWVyLiBFdCB0YW50IG1pZXV4IHNpIMOnYSBjaG9xdWUuXG5cbihQYXJhZG94YWxlbWVudCwgbGUgcGVuc2V1ciBkw6ljb2xvbmlhbGlzdGUgZGV2cmFpdCB5IHZvaXIgdW5lIHLDqXN1cmdlbmNlIGRlIGwnZXNwcml0IGNvbG9uaWFsIGltcG9zYW50IHNhIHZpc2lvbiBkdSBtb25kZSBldCBzZXMgdmFsZXVycyBhdXggbmF0aW9ucyBqdWfDqWVzIG1vaW5zIMOpY2xhaXLDqWVzLiBNYWlzLCBlbiByw6lhbGl0w6ksIGNvbW1lIGNlIHNvbnQgZGVzIG1pbm9yaXTDqXMgcHLDqXRlbmR1bWVudCBpbnZpc2liaWxpc8OpZXMgcXVpIHNvbnQgw6AgbGEgbWFuxZN1dnJlLCBlbGxlcyBzb250IG7DqWNlc3NhaXJlbWVudCBkYW5zIGxlIGNhbXAgZHUgYmllbi4pXG5cbkxlIENJTyBhIHByb2JhYmxlbWVudCBkw7sgcmVjZXZvaXIgcXVlbHF1ZXMgcmVtYXJxdWVzIGRlIHNwb25zb3JzIG91IGTigJnDiXRhdHMgw6AgcHJvcG9zIGRlIGxhIHBhcm9kaWUgZGUgbGEgY8OobmUsIGNhciBkZXMgZXhjdXNlcyBvbnQgw6l0w6kgcHLDqXNlbnTDqWVzIGRhbnMgbGVzIG3DqWRpYXMuIFBsdXNpZXVycyBhY3RldXJzIG9udCBww6lkYWzDqSBwb3VyIGZhaXJlIGNyb2lyZSBxdWUgY2UgbuKAmWVzdCBwYXMgbGEgY8OobmUgcXVpIMOpdGFpdCB2aXPDqWUsIG1haXMgcGVyc29ubmUgbmUgc2VtYmxlIHkgYXZvaXIgY3J1LlxuXG5DZXR0ZSBjw6lyw6ltb25pZSBzb3VoYWl0YWl0IFwiYnJpc2VyIGxlcyBjb2Rlc1wiIGF1IG5vbSBkZSBsJ2luY2x1c2l2aXTDqSBhZmluIGRlIGZhaXJlIGFkdmVuaXIgbGUgbW9uZGUgdGVsIHF1ZSBzZXMgb3JnYW5pc2F0ZXVycyB2ZXVsZW50IHF1J2lsIHNvaXQuIFVuIG1vbmRlIGRhbnMgbGVxdWVsIGwnaW5kaXZpZHUgZXN0IGFmZnJhbmNoaSBkZSB0b3V0ZSBlbnRyYXZlIGJpb2xvZ2lxdWUsIG1vcmFsZSwgc29jaWFsZSBldCByZWxpZ2lldXNlLiBVbiBtb25kZSBkw6lkacOpIMOgIGwnYW1vdXIgZGUgc29pIGFic29sdXRpc8OpIOKAlCB1biBhbW91ciBkZSBzb2kgcXVpIGVzdCBkw6lmaW5pIHBhciBzb2ktbcOqbWUgZGFucyBsZSBtw6lwcmlzIGRlIERpZXUgZXQgZGVzIG5vcm1lcyBkZSBzYSBjcsOpYXRpb24uIEVuIGJyZWYsIGxlIG1vbmRlIGR1IFwic3VyaHVtYWluXCIgbmlldHpzY2jDqWVuLlxuXG5MZSBww6ljaMOpIGFyY2jDqXR5cGFsIGRlIGNldHRlIGPDqXLDqW1vbmllIG4nZXN0IGRvbmMgcGFzIGwnaW1wdWRpY2l0w6kgb3UgbGUgYmxhc3Bow6htZSwgbWFpcyBsJ29yZ3VlaWwuIEMnZXN0IGVuIGNlbGEgcXVlIGxhIGNpdMOpIHJlYmVsbGUsIHF1J2VsbGUgc2Ugbm9tbWUgQmFiZWwsIFJvbWUgb3UgUGFyaXMsIHN1aXQgbGVzIHRyYWNlcyBkdSBkaWFibGUgZW4gcmVtb2RlbGFudCBsZSBtb25kZSBzZWxvbiBzZXMgZMOpc2lycyByZWJlbGxlcy5cblxuQ29tbWVudCByw6lhZ2lyPyBK4oCZb3V2cmUgZGV1eCBwaXN0ZXMuXG5cbiMjIFJlZnVzb25zIGxhIHZpY3RpbWlzYXRpb24sIGLDqW5pc3NvbnMgY2V1eCBxdWkgbm91cyBtYXVkaXNzZW50XG5cbkVuIHRhbnQgcXVlIGRpc2NpcGxlcyBkZSBKw6lzdXMsIG5vdXMgZGV2b25zIHJlZnVzZXIgZGUgcmVuZHJlIGxlIG1hbCBwb3VyIGxlIG1hbC4gTm90cmUgcsOpYWN0aW9uIG5lIGRvaXQgcGFzIMOqdHJlIHLDqWFjdGlvbm5haXJlLiBOb3VzIG5lIHNvbW1lcyBwYXMgbm9zdGFsZ2lxdWVzIGQndW5lIGNocsOpdGllbnTDqSBwYXNzw6llIGlkw6lhbGlzw6llLiBKw6lzdXMgbidhIHBhcyBiZXNvaW4gZCdhdm9jYXQsIGNvbnRyYWlyZW1lbnQgw6Agbm91cy4gSWwgZXN0IGF1LWRlc3N1cyBkZSB0b3V0IG5vbSBldCBzJ29jY3VwZXJhIGx1aS1tw6ptZSBkZSBzZXMgZW5uZW1pcy5cblxuTm91cyBuZSBzb21tZXMgcGFzIGVuIGx1dHRlIGNvbnRyZSBub3MgY29udGVtcG9yYWlucy4gRGUgZmFpdCwgbm91cyBhcHBhcnRlbmlvbnMgYXUgbcOqbWUgcm95YXVtZSBlbnTDqW7DqWJyw6kgcXUnZXV4LiBOb3RyZSB1bmlvbiDDoCBKw6lzdXMgbidlc3QgZHVlIHF1J8OgIGxhIGdyw6JjZSBldCBub24gw6AgdW5lIHByw6l0ZW5kdWUgc3Vww6lyaW9yaXTDqSBtb3JhbGUuXG5cblJhcHBlbG9ucy1ub3VzIHF1ZSBub3VzIGx1dHRvbnMgY29udHJlIGxlcyBwdWlzc2FuY2VzIGV0IGxlcyBwcmluY2lwYXV0w6lzLCBwYXMgY29udHJlIGxlcyBob21tZXMuIE5vdXMgbGUgZmFpc29ucyBlbiByZWZ1c2FudCBsZSBtZW5zb25nZSwgZMOpbm9uw6dhbnQgbGUgbWFsIGV0IHByb2NsYW1hbnQgbOKAmcOJdmFuZ2lsZS5cblxuRGFucyBjZSBjb21iYXQsIG5vdXMgbmUgZGV2b25zIGphbWFpcyBub3VzIGNvbXByb21ldHRyZSBuaSBjw6lkZXIgw6AgbGEgaGFpbmUgb3UgcmVub25jZXIgw6AgbCdhbW91ci4gTm91cyBsdXR0b25zIHBvdXIgYXBwcmVuZHJlIMOgIGFpbWVyIGV0IMOgIGLDqW5pciBub3MgcHJvY2hhaW5zLCBwYXJ0aWN1bGnDqHJlbWVudCBxdWFuZCBpbHMgbm91cyBtYXVkaXNzZW50LlxuXG5HYXJkb25zLW5vdXMgZG9uYyBkZXMgcsOpYWN0aW9ucyBk4oCZaHVtZXVyIGV0IGV4aG9ydG9ucy1ub3VzIGF1eCBib25uZXMgxZN1dnJlcy4gTmUgc295b25zIHBhcyBzdXJwcmlzIOKAnHF1J29uIG5vdXMgbWFyY2hlIHN1ciBsYSBndWV1bGXigJ0gY29tbWUgbCdhIMOpdm9xdcOpIHVuIHByw6p0cmUgc3VyIGxlcyByw6lzZWF1eCBzb2NpYXV4LiBDJ2VzdCBub3JtYWwsIGMnZXN0IHVuIGdhZ2UgZGUgbm90cmUgYXBwYXJ0ZW5hbmNlIMOgIERpZXUuXG5cblF1ZSBsZSBTZWlnbmV1ciBub3VzIGFpZGUgw6AgcsOpYWxpc2VyIHF1ZSBjJ2VzdCB1biBob25uZXVyIGQnw6p0cmUganVnw6lzIGRpZ25lcyBkJ8OqdHJlIG1vcXXDqXMgcG91ciBsZSBub20gZGUgSsOpc3VzIChBYyA1LjQxKS5cblxuIyMgU295b25zIGNvdXJhZ2V1eCBwb3VyIHTDqW1vaWduZXIgZGUgQ2hyaXN0XG5cbkRhbnMgY2V0dGUgY8OpcsOpbW9uaWUsIHRvdXQgbGFuZ3Vpc3NhaXQgYXByw6hzIGxhIHRyYW5zY2VuZGFuY2UgZXQgbGUgc2FjcsOpLiBFbGxlIG5vdXMgYSBkw6l2b2lsw6kgbGVzIGlkb2xlcyBxdWkgdGllbm5lbnQgY2FwdGl2ZXMgbGVzIHBlbnPDqWVzLCBsJ2ltYWdpbmF0aW9uIGV0IGxlcyByw6p2ZXMgZGUgbm9tYnJldXggZGUgbm9zIGNvbnRlbXBvcmFpbnMsIGluY2FwYWJsZXMgZGUgbmUgcGFzIGNoZXJjaGVyIERpZXUgZXQgaW5jYXBhYmxlcyBkZSBsZSB0cm91dmVyIHBhciBldXgtbcOqbWVzLlxuXG4+IElsIGEgdm91bHUgcXUnaWxzIGNoZXJjaGVudCBsZSBTZWlnbmV1ciBldCBxdSdpbHMgcydlZmZvcmNlbnQgZGUgbGUgdHJvdXZlciBlbiB0w6J0b25uYW50LCBiaWVuIHF1J2lsIG5lIHNvaXQgcGFzIGxvaW4gZGUgY2hhY3VuIGRlIG5vdXMuXG4+XG4+IDxzdWI+PGNpdGU+QWN0ZXMgMTcuMjc8L2NpdGU+PC9zdWI+XG5cbk5vdXMgZGV2b25zIHZvaXIsIGRhbnMgY2UgcXVpIG5vdXMgYXBwYXJhw650IGF1IHByZW1pZXIgcmVnYXJkIGNvbW1lIHVuZSBwcm92b2NhdGlvbiBkZXMgcmViZWxsZXMsIGxhIHNvdWZmcmFuY2UgZGVzIGVzY2xhdmVzIHF1aSBlc3NhaWVudCBkZSBzZSByYXNzdXJlciBxdSdpbHMgY29udHLDtGxlbnQgbGV1ciB2aWUuIExhIHZpZSBlbiBhYm9uZGFuY2UgZXQgbGEgbGliZXJ0w6kgcXUnaWxzIGNoZXJjaGVudCBkw6lzZXNww6lyw6ltZW50IMOgIGV4cMOpcmltZW50ZXIgbmUgc2UgdHJvdXZlbnQgcXUnZW4gdml2YW50IHNvdXMgbGUgcsOoZ25lIGRlIGNlbHVpIHF1aSBlc3QgbGEgdsOpcml0w6kuIFNldWxlIHNhIFBhcm9sZSBhIGxlIHBvdXZvaXIgZGUgbGliw6lyZXIgZXQgZGUgZmFpcmUgZW50cmVyIGRhbnMgbGUgcmVwb3MgdGFudCByZWNoZXJjaMOpIChKbiA4LjMwLTM2KS5cblxuTGEgcmVsaWdpb24gY2l2aWxlIG5lIHBvdXJyYSBqYW1haXMgY3LDqWVyIGwndW5pdmVyc2FsaXTDqSDDoCBsYXF1ZWxsZSBlbGxlIGFzcGlyZS4gTCdpZG9sw6J0cmllIHJlZnVzZSBvYnN0aW7DqW1lbnQgZGUgY8OpZGVyIMOgIGxhIHbDqXJpdMOpLCBlbnRyYcOubmFudCBsYSBydWluZSBkZSBjZWx1aSBxdWkgZXN0IGRvbWluw6kgcGFyIGwnb3JndWVpbC4gQWltb25zIG5vdHJlIHByb2NoYWluIGVuIGRpc2FudCBldCBlbiBmYWlzYW50IGxhIHbDqXJpdMOpIHF1aSBlc3QgZW4gQ2hyaXN0LlxuXG5SYXBwZWxvbnMtbm91cyBub3RyZSBtaXNzaW9uLiBEYW5zIG5vdHJlIHNvY2nDqXTDqSBwb3N0Y2hyw6l0aWVubmUsIGMnZXN0IHNldWxlbWVudCBhdSBjb250YWN0IGRlIGwnw4lnbGlzZSBxdWUgbGUgbW9uZGUgZXN0IHLDqWVsbGVtZW50IGF1IGNvbnRhY3QgZGUgbGEgdHJhbnNjZW5kYW5jZSwgZ3LDomNlIMOgIGwnYW5ub25jZSBkZSBsJ8OJdmFuZ2lsZSBldCDDoCBsJ0VzcHJpdCBhZ2lzc2FudCBkYW5zIGxhIGNvbW11bmF1dMOpLlxuXG5TYWlzaXNzb25zIGRvbmMgbCdleGhvcnRhdGlvbiBkZSBMZXNzbGllIE5ld2JpZ2luOlxuXG4+IExlcyBjaHLDqXRpZW5zIHBvdXJyb250IGpvdWVyIGxlIHLDtGxlIHF1aSBsZXVyIHJldmllbnQgZGFucyB1bmUgc29jacOpdMOpIHPDqWN1bGFyaXPDqWUgcHLDqWNpc8OpbWVudCBkYW5zIGxhIG1lc3VyZSBvw7kgaWxzIHNvbnQgZW5yYWNpbsOpcyBkYW5zIHVuZSByw6lhbGl0w6kgcmVsaWdpZXVzZSwgZGFucyBsYSBjb25uYWlzc2FuY2UgZGUgc2F0aXNmYWN0aW9ucyBxdWkgZMOpcGFzc2VudCBsZXMgb2JqZWN0aWZzIGRlIGNldHRlIHNvY2nDqXTDqS4gRXQsIGludmVyc2VtZW50LCBsYSBzb2Npw6l0w6kgc8OpY3VsacOocmUgcG91cnJhIHBlcmR1cmVyIGV0IGF0dGVpbmRyZSBzZXMgb2JqZWN0aWZzIGRhbnMgbGEgbWVzdXJlIG/DuSBlbGxlIGVzdCBzZXJ2aWUgcGFyIGRlcyBob21tZXMgZXQgZGVzIGZlbW1lcyBxdWkgb250IMOpdMOpIGTDqWxpdnLDqXMgZHUgc2VydmljZSBkZSB0b3VzIGxlcyBmYXV4IGRpZXV4IGV0IG1lc3NpZXMgcG91ciBsZSBzZXJ2aWNlIGR1IEZpbHMgZGUgbCdIb21tZSBjcnVjaWZpw6kgZXQgcXVpIHLDqGduZSDDqXRlcm5lbGxlbWVudC5cbj5cbj4gPHN1Yj48Y2l0ZT5MZXNzbGllIE5ld2JpZ2luLCBIb25lc3QgcmVsaWdpb24gZm9yIHNlY3VsYXIgbWFuLCAyMDExLCBwLjEyOS48L2NpdGU+PC9zdWI+XG5cbk4nYXlvbnMgcGFzIGhvbnRlIGRlIGwnw4l2YW5naWxlIGV0IG5lIGNyYWlnbm9ucyBwYXMgbGVzIG1vcXVlcmllcy4gQWNjZXB0b25zIGQnw6p0cmUgYWNjdXPDqXMgZmF1c3NlbWVudCBkZSBmYXNjaXNtZSAob3UgZCdhdXRyZXMgYWNjdXNhdGlvbnMpIGV0IG5lIG5vdXMgcHLDqW9jY3Vwb25zIHBhcyBkZSBub3RyZSBub3RvcmnDqXTDqSB0YW50IHF1ZSBub3VzIGNoZXJjaG9ucyDDoCBpbWl0ZXIgSsOpc3VzLiBDcm95b25zIHF14oCZaWxzIHJlbWFycXVlcm9udCBub3RyZSBiZWxsZSBtYW5pw6hyZSBk4oCZYWdpciBldCByZW5kcm9udCBnbG9pcmUgw6AgRGlldSBsZSBqb3VyIG/DuSBpbCBpbnRlcnZpZW5kcmEgZW4gbGV1ciBmYXZldXIgKDFQIDIuMTIpLlxuXG5KZSBjb25jbHVzIHBhciBsZXMgcGFyb2xlcyBk4oCZRXJ3YW4gQ2xvYXJlYywgbGUgcHLDqXNpZGVudCBkdSBDTkVGOlxuXG4+IFZveW9ucyBkYW5zIGxhIHNpdHVhdGlvbiBxdWkgc3VydmllbnQgdW5lIHLDqWVsbGUgb3Bwb3J0dW5pdMOpIGRlIHTDqW1vaWduZXIgZGUgbm90cmUgZm9pIGFsb3JzIHF1ZSBsYSBwZXJzb25uZSBkdSBDaHJpc3QgdmllbnQgZOKAmcOqdHJlIHBsYWPDqWUgYXUgY2VudHJlIGRlIGNlcyBqZXV4LiBFbnRlbmRvbnMgbGVzIGNyaXMgZHUgY8WTdXIgZXQgbGUgYmVzb2luIGRlIHLDqWNvbmNpbGlhdGlvbiBkZSBub3MgY29udGVtcG9yYWlucywgbGV1ciBxdcOqdGUgZOKAmWlkZW50aXTDqSBldCBk4oCZYXBwYXJ0ZW5hbmNlLiBJbHMgY3JpZW50IGRhbnMgdW5lIHNvY2nDqXTDqSBwbHVyYWxpc3RlOyBtb250cm9ucy1sZXVyIGNvbW1lbnQgY3JpZXIgcGx1cyBoYXV0IHZlcnMgY2VsdWkgcXVpIGxlcyBpbnZpdGUgdG91cyDDoCBzYSB0YWJsZSBldCBvZmZyZSBsYSB2cmFpZSByw6ljb25jaWxpYXRpb24sIGwnaWRlbnRpdMOpIGV0IGzigJlhcHBhcnRlbmFuY2UgdsOpcml0YWJsZXMuPHN1cD5bMTBdKC9hcnRpY2xlL2NlcmVtb25pZS1vdXZlcnR1cmUtam8tcGFyaXMtMjAyNC12cmFpbWVudC1zYXRhbmlxdWUjbm90ZXMpPC9zdXA+XG5cbkVuZmluLCBwcmlvbnMgcG91ciBub3MgYXV0b3JpdMOpcy4gUXVlIERpZXUgaW5jbGluZSBsZXVyIGPFk3VyIHZlcnMgY2UgcXVpIGVzdCB2cmFpLCBiZWF1IGV0IGJvbiwgcG91ciBsZSBiaWVuIGRlIHRvdXMgbGVzIGhvbW1lcyBldCBwb3VyIHF1ZSBub3VzIHB1aXNzaW9ucyBjb250aW51ZXIgZGUgdml2cmUgbm90cmUgZm9pIGF1IGdyYW5kIGpvdXIgKDFUbSAyLjIpLlxuXG4jIyBQb3VyIGFsbGVyIHBsdXMgbG9pblxuXG4qIGA8ZGl2PlNpIHZvdXMgZMOpc2lyZXogcG91cnN1aXZyZSBjZXR0ZSByw6lmbGV4aW9uLCA8YSBocmVmPVwiaHR0cHM6Ly9tYXR0aGlldWdpcmFsdC5ub3Rpb24uc2l0ZS9MZS1tYW5pZmVzdGUtZHUtbWFxdWlzLWVzY2hhdG9sb2dpcXVlLTVhMmRmZjc3ZDUyMzRlNDI5NjJiNTY3Mjc3YTc5NzJmXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPmxpc2V6IGxlIG1hbmlmZXN0ZSBkZSA8ZW0+TWVtZW50byBNb3JpPC9lbT48L2E+PC9kaXY+YFxuXG48YnIgaWQ9XCJub3Rlc1wiPlxuXG4jIyMjIE5vdGVzIGV0IHLDqWbDqXJlbmNlczpcblxuYDxzdWI+MS4gVm9pciA8YSBocmVmPVwiaHR0cHM6Ly9zdGlsbG1lZC5vbHltcGljcy5jb20vbWVkaWEvRG9jdW1lbnRzL05ld3MvMjAyMS8xMS9DYWRyZS1kdS1DSU8tRXF1aXRlLUluY2x1c2lvbi1Ob24tZGlzY3JpbWluYXRpb24tMjAyMS5wZGY/X2dhPTIuMTk0OTAzMjExLjc4MzI0NDQ3My4xNjM4ODA4NjYzLTEzNzAxMjc4ODYuMTYyNDYzNzA1MFwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5cIkNhZHJlIGR1IENJTyBzdXIgbOKAmcOpcXVpdMOpLCBs4oCZaW5jbHVzaW9uIGV0IGxhIG5vbi1kaXNjcmltaW5hdGlvbiBzdXIgbGEgYmFzZSBkZSBs4oCZaWRlbnRpdMOpIHNleHVlbGxlIGV0IGRlIGzigJlpbnRlcnNleHVhdGlvblwiPC9hPiwgcGFyIGxlIENvbWl0w6kgSW50ZXJuYXRpb25hbCBPbHltcGlxdWUuPGJyPjIuIExlIHRlcm1lIFwicXVlZXJcIiBlc3QgdXRpbGlzw6kgcG91ciBkw6lzaWduZXIgZGVzIGlkw6llcyBldCBkZXMgcHJhdGlxdWVzIHF1aSBjaGVyY2hlbnQgw6AgZMOpY29uc3RydWlyZSBsZXMgbm9ybWVzIHRyYWRpdGlvbm5lbGxlcyBzdXIgbGUgZ2VucmUgZXQgbGEgc2V4dWFsaXTDqS48YnI+My4gPGEgaHJlZj1cImh0dHBzOi8vd3d3LmxlZmlnYXJvLmZyL2FjdHVhbGl0ZS1mcmFuY2UvcHJvZm9uZGVtZW50LWJsZXNzZS1wYXItbGEtY2VuZS1sLWV2ZXF1ZS1kZWxlZ3VlLXBvdXItbGVzLWpvLXJlYWdpdC1hLWxhLWNlcmVtb25pZS0yMDI0MDcyN1wiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj5BcnRpY2xlIHBhcnUgbGUgMjcganVpbGxldCAyMDI0IHN1ciBMZUZpZ2Fyby5mcjwvYT4uPGJyPjQuIENoYW50YWwgRGVsc29sLCA8YSBocmVmPVwiaHR0cHM6Ly93d3cuYW1hem9uLmZyL2Zpbi1jaHIlQzMlQTl0aWVudCVDMyVBOS1DaGFudGFsLURlbHNvbC9kcC8yMjA0MTU1MjVYL3JlZj1zcl8xXzE/X19ta19mcl9GUj0lQzMlODVNJUMzJTg1JUM1JUJEJUMzJTk1JUMzJTkxJmNyaWQ9MVVJQUlMTFJJMU03VyZkaWI9ZXlKMklqb2lNU0o5LjNRNTdVTk5NcUhETFFnRkRVRjVRdncuSjVEOC16NElQZDJvVDlLejBwUHA3LUU5cGFNYkM1Y0ZjSDBSblRqaHpIYyZkaWJfdGFnPXNlJmtleXdvcmRzPUxhK2ZpbitkZStsYStjaHIlQzMlQTl0aWVudCVDMyVBOSUzQStMJTI3aW52ZXJzaW9uK25vcm1hdGl2ZStldCtsZStub3V2ZWwrJUMzJUEyZ2UmcWlkPTE3MjI1MDc4Njgmc3ByZWZpeD1sYStmaW4rZGUrbGErY2hyJUMzJUE5dGllbnQlQzMlQTkrbCUyN2ludmVyc2lvbitub3JtYXRpdmUrZXQrbGUrbm91dmVsKyVDMyVBMmdlJTJDYXBzJTJDMjQzJnNyPTgtMVwiIHRhcmdldD1cImJsYW5rXCIgcmVsPVwibm9vcGVuZXJcIj48ZW0+TGEgZmluIGRlIGxhIGNocsOpdGllbnTDqTogTCdpbnZlcnNpb24gbm9ybWF0aXZlIGV0IGxlIG5vdXZlbCDDomdlPC9lbT48L2E+LCBMZXMgw6lkaXRpb25zIGR1IENlcmYsIDIwMjEsIHAuMzY8YnI+NS4gPGEgaHJlZj1cImh0dHBzOi8vd3d3LmxlbW9uZGUuZnIvbGUtbW9uZGUtZGVzLXJlbGlnaW9ucy9hcnRpY2xlLzIwMjQvMDcvMzEvcGllcnJlLWRlLWNvdWJlcnRpbi1kdS1jYXRob2xpY2lzbWUtYS1sYS1yZWxpZ2lvbi1kZS1sLWF0aGxldGVfNjI2MjM3OF82MDM4NTE0Lmh0bWxcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+QXJ0aWNsZSBwYXJ1IGxlIDMxIGp1aWxsZXQgMjAyNCBzdXIgTGVNb25kZS5mcjwvYT4uPGJyPjYuIDxhIGhyZWY9XCJMSUVOXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPkFydGljbGUgcGFydSBsZSA4IHNlcHQuIDIwMjMgc3VyIExpYsOpcmF0aW9uLmZyPC9hPiwgPGEgaHJlZj1cImh0dHBzOi8vd3d3LmV1cm9wZTEuZnIvcGVvcGxlL2plYW4tZHVqYXJkaW4tcmVhZ2l0LWEtbGEtcG9sZW1pcXVlLXN1ci1sYS1jZXJlbW9uaWUtZG91dmVydHVyZS1kdS1tb25kaWFsLWRlLXJ1Z2J5LWplLXRvbWJlLWEtbGEtcmVudmVyc2UtNDIwMzgwNiM6fjp0ZXh0PUxpYiVDMyVBOXJhdGlvbiUyMGElMjBtJUMzJUFBbWUlMjB0aXRyJUMzJUE5JTIwJTIyQWxsZXosbGElMjBwZWF1JTIwZCd1biUyMGJvdWxhbmdlci5cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+Y2l0w6kgcGFyIEV1cm9wZSAxPC9hPi48YnI+Ny4gPGEgaHJlZj1cImh0dHBzOi8vd3d3LmxlZmlnYXJvLmZyL3BvbGl0aXF1ZS9wcm9wYWdhbmRlLXdva2UtZXQtZ3Jvc3NpZXJlLWwtZXh0cmVtZS1kcm9pdGUtZW4tcGxzLWxhLWNsYXNzZS1wb2xpdGlxdWUtZGl2aXNlZS1hcHJlcy1sYS1jZXJlbW9uaWUtZC1vdXZlcnR1cmUtZGVzLWpvLTIwMjQtMjAyNDA3MjdcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+QXJ0aWNsZSBwYXJ1IGxlIDI3IGp1aWxsZXQgMjAyNCBzdXIgTGVGaWdhcm8uZnI8L2E+PGJyPjguIEphbWVzIExpbmRzYXksIDxhIGhyZWY9XCJodHRwczovL25ld2Rpc2NvdXJzZXMuY29tL3RmdHctcXVlZXItdi9cIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+ZXh0cmFpdCAoZW4gYW5nbGFpcykgZGUgbOKAmTxlbT5FbmN5Y2xvcGVkaWEgb2YgRGl2ZXJzaXR5IGFuZCBTb2NpYWwgSnVzdGljZTwvZW0+PC9hPiBwdWJsacOpIHN1ciBOZXdEaXNjb3Vyc2VzLmNvbS48YnI+OS4gPGEgaHJlZj1cImh0dHBzOi8veW91dHUuYmUvV1Y1bTAtNmkzWnM/dD0xNDNcIiB0YXJnZXQ9XCJibGFua1wiIHJlbD1cIm5vb3BlbmVyXCI+SW50ZXJ2aWV3IGRlIE1hcmlvbiBNYXLDqWNoYWwsIGludml0w6llIGRlIEZyYW5jZSBJbnRlcjwvYT4gcG9zdMOpZSBzdXIgWW91VHViZS4gPGJyPjEwLiA8YSBocmVmPVwiaHR0cHM6Ly93d3cubGVjbmVmLm9yZy9hcnRpY2xlcy8xNTQ1MDYtY2VyZW1vbmllLWQtb3V2ZXJ0dXJlLWRlcy1qby11bi1hcHBlbC1hLWxhLXByaWVyZS1ldC1hLWwtYWN0aW9uXCIgdGFyZ2V0PVwiYmxhbmtcIiByZWw9XCJub29wZW5lclwiPkNvbW11bmlxdcOpIGR1IENORUYsIHB1Ymxpw6kgbGUgMzAganVpbGxldCAyMDI0LjwvYT48L3N1Yj5gIiwiYXV0aG9yIjp7ImZ1bGxOYW1lIjoiUmFwaGHDq2wgQ2hhcnJpZXIiLCJwaWN0dXJlIjp7InVybCI6Imh0dHBzOi8vOWM4NTA0MDAtYmx1ZS10b3V0cG91cnNhZ2xvaXJlLWNvbS1zdHJhcGktdXBsb2FkLnMzLmdyYS5wZXJmLmNsb3VkLm92aC5uZXQvcmFwaGFlbF9jaGFycmllcl8wM19jb3BpZV8xX3NjYWxlZF9lMTY1NzgwMTI1Nzc4N19mOTM1OTk0MDM4LmpwZyIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn19LCJpbWFnZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L0NlcmVtb25pZV9kX291dmVydHVyZV9KT19QYXJpc18yMDI0XzEwZDI4NzY1MGUucG5nIiwiaGVpZ2h0Ijo3MjAsIndpZHRoIjoxMDQxLCJhbHRlcm5hdGl2ZVRleHQiOiIiLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9LCJ0b3BpY3MiOlt7Im5hbWUiOiJUaMOpb2xvZ2llIFByYXRpcXVlIn0seyJuYW1lIjoiSG9tb3NleHVhbGl0w6kgZXQgTEdCVCJ9LHsibmFtZSI6IlRyYW5zZ2VucmUifSx7Im5hbWUiOiJQb2xpdGlxdWUgZXQgc29jacOpdMOpIn0seyJuYW1lIjoiQ3VsdHVyZSBldCBhcnRzIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJDb21tZSBiZWF1Y291cCBkJ8OpdsOpbmVtZW50cyByw6ljZW50cyBlbiBGcmFuY2UsIGxhIGPDqXLDqW1vbmllIGRlcyBKTyBkZSBQYXJpcyAyMDI0IGEgZGl2aXPDqSBsZSBwYXlzIGV0IHN1c2NpdMOpIGQnaW1wb3J0YW50ZXMgcG9sw6ltaXF1ZXMuIE1haXMgY2VsYSBuJ2VzdCBndcOocmUgc3VycHJlbmFudC4gRGVwdWlzIHRvdWpvdXJzLCBsZXMgamV1eCBzb250IGF1c3NpIGRlcyBqZXV4IGRlIHBvdXZvaXIgcG9saXRpcXVlIGV0IGlkw6lvbG9naXF1ZS4ifSx7fV19LHsiaWQiOiI0MDUwIiwidGl0bGUiOiJRdSdlc3QtY2UgcXUndW5lIHZpc2lvbiBkdSBtb25kZSBkw6lzZW5jaGFudMOpZT8iLCJzbHVnIjoidmlzaW9uLWR1LW1vbmRlLWRlc2VuY2hhbnRlZSIsInR5cGUiOiJhcnRpY2xlIiwicHVibGlzaGVkX2F0IjoiMjAyNC0wNi0yOFQwNTozMzo1OC4wMDBaIiwiYm9keSI6IkxhIHPDqWN1bGFyaXNhdGlvbiBlc3QgbGUgcHJvY2Vzc3VzIHF1aSBhIHByb2R1aXQgY2UgcXVlIGxlIHNvY2lvbG9ndWUgTWFyY2VsIEdhdWNoZXQgYXBwZWxsZSDigJxs4oCZw6lwdWlzZW1lbnQgZHUgcsOoZ25lIGRlIGzigJlpbnZpc2libGU8c3VwPlsxXSgvYXJ0aWNsZS92aXNpb24tZHUtbW9uZGUtZGVzZW5jaGFudGVlI25vdGVzKTwvc3VwPuKAnSBvdSBsZSBkw6lzZW5jaGFudGVtZW50LlxuXG5TZWxvbiBsZSBwaGlsb3NvcGhlIENoYXJsZXMgVGF5bG9yLCBub3VzIG5lIHBvdXZvbnMgZMOpZmluaXIgbGUgZMOpc2VuY2hhbnRlbWVudCBzYW5zLCBwcsOpYWxhYmxlbWVudCwgZMOpY3JpcmUgY29tbWVudCBsZXMgaG9tbWVzIGNvbXByZW5haWVudCBsZSBtb25kZSBlbmNoYW50w6kuIENlIGRlcm5pZXIgc+KAmWFydGljdWxhaXQgYXV0b3VyIGRlIHRyb2lzIGJpYWlzOlxuXG4+ICgxKSBMZSBtb25kZSBkYW5zIHNhIGRpbWVuc2lvbiBuYXR1cmVsbGUsIGxlcXVlbCBz4oCZaW5zY3JpdmFpdCBwYXJmYWl0ZW1lbnQgZGFucyBsZSBjb3Ntb3MgdGVsIHF14oCZaWwgw6l0YWl0IGFsb3JzIGNvbsOndSwgYXR0ZXN0YWl0IHVuZSBpbnRlbnRpb24gZXQgdW5lIGFjdGlvbiBkaXZpbmVzLiBcXFvigKZcXF0gKDIpIERpZXUgw6l0YWl0IMOpZ2FsZW1lbnQgw6AgbOKAmcWTdXZyZSBkYW5zIGzigJlleGlzdGVuY2UgbcOqbWUgZGUgbGEgc29jacOpdMOp4oCmIFVuIHJveWF1bWUgbmUgcG91dmFpdCBwYXMgw6p0cmUgY29uw6d1IGNvbW1lIHLDqXN1bHRhbnQgc2ltcGxlbWVudCBkZSBs4oCZYWN0aW9uIGh1bWFpbmUgZGFucyB1biB0ZW1wcyBzw6ljdWxpZXIsIG1haXMgZGV2YWl0IG7DqWNlc3NhaXJlbWVudCBzZSBmb25kZXIgc3VyIHVuIG9yZHJlIHBsdXMgw6lsZXbDqeKApiBEaWV1IMOpdGFpdCBwYXJ0b3V0LCBpbCDDqXRhaXQgaW1wb3NzaWJsZSBkZSBuZSBwYXMgbGUgdHJvdXZlci4gXFxb4oCmXFxdICgzKSBMZXMgaG9tbWVzIMOpdm9sdWFpZW50IGF1IHNlaW4gZOKAmXVuIG1vbmRlIFwiZW5jaGFudMOpXCLigKYgTGUgbW9uZGUgZGFucyBsZXF1ZWwgdml2YWllbnQgbm9zIGFuY8OqdHJlcywgcXVpIGVzdCB1biBtb25kZSBk4oCZZXNwcml0cywgZGUgZMOpbW9ucyBldCBkZSBmb3JjZSBtb3JhbGUsIGVzdCB1biBtb25kZSBlbmNoYW50w6k8c3VwPlsyXSgvYXJ0aWNsZS92aXNpb24tZHUtbW9uZGUtZGVzZW5jaGFudGVlI25vdGVzKTwvc3VwPi5cblxuw4AgbOKAmcOocmUgcHLDqWPDqWRhbnQgbGEgdmFndWUgZGUgc8OpY3VsYXJpc2F0aW9uLCB1biBwaMOpbm9tw6huZSBxdWUgVGF5bG9yIHNpdHVlIMOgIGzigJlhdWJlIGR1IFhWSUnhtYkgc2nDqGNsZSwgbGEgcGVyY2VwdGlvbiBkdSBtb25kZSBcImVuY2hhbnTDqVwiIHPigJlhcnRpY3VsYWl0IGF1dG91ciBk4oCZdW5lIGNvc21vbG9naWUgdGjDqW9jZW50cmlxdWUuIExlIGNvc21vcyAobOKAmXVuaXZlcnMgaGFiaXTDqSkgw6l0YWl0IGFsb3JzIHBlcsOndSBjb21tZSBs4oCZw6ltYW5hdGlvbiBkZSBsYSB2b2xvbnTDqSBkaXZpbmUsIHVuIGFnZW5jZW1lbnQgcHJvdmlkZW50aWVsIG/DuSBjaGFxdWUgaHVtYWluIHRyb3V2YWl0IHNhIHJhaXNvbiBk4oCZw6p0cmUgYXUgc2VpbiBk4oCZdW4gb3JkcmUgc3VybmF0dXJlbC4gTOKAmWV4aXN0ZW5jZSBz4oCZaW5zY3JpdmFpdCBkYW5zIHVuIGNhZHJlIG/DuSBsYSB0cmFuc2NlbmRhbmNlIGltcHLDqWduYWl0IGxhIHLDqWFsaXTDqSBtYXTDqXJpZWxsZSwgbGEgc29jacOpdMOpIGV0IGxlcyBwcsOpY2VwdGVzIG1vcmF1eCwgc+KAmWFsaWduYW50IHN1ciBsYSBMb2kgZGUgRGlldTogKuKAnFF1ZSB0YSB2b2xvbnTDqSBzb2l0IGZhaXRlIHN1ciB0ZXJyZSBjb21tZSBhdSBDaWVsLuKAnSpcblxuPiDDgCB0b3VzIGNlcyBuaXZlYXV4LCBsZXMgbGllbnMgc29jaWF1eCDDqXRhaWVudCBlbmNow6Jzc8OpcyBkYW5zIGxlIHNhY3LDqSwgZXQgaW5pbWFnaW5hYmxlcyBhdXRyZW1lbnQuIFxcW+KAplxcXSBMYSBzb2Npw6l0w6ksIHLDqWFsaXTDqSB0YW5naWJsZSBldCBpbmRpc3BlbnNhYmxlLCBwbGFpZGFpdCBwb3VyIERpZXUuIExlIHJhaXNvbm5lbWVudCBuZSBjb25zaXN0YWl0IHBhcyBzaW1wbGVtZW50IMOgIGRpcmU6IGrigJlhaSBkZXMgYXNwaXJhdGlvbnMgbW9yYWxlcyBldCBzcGlyaXR1ZWxsZXMsIGRvbmMgRGlldSBleGlzdGUsIG1haXMgZW5jb3JlOiBub3VzIHNvbW1lcyBsacOpcyDDoCBsYSBzb2Npw6l0w6ksIGRvbmMgRGlldSBleGlzdGUuIEPigJllc3QgY2V0IGFzcGVjdCwgcGFyIGxlcXVlbCBEaWV1IHJlbXBsaXQgbGUgcsO0bGUgZGUgZm9uZGVtZW50IGRlIGzigJlleGlzdGVuY2Ugc29jaWFsZSwgcXVpIGV4cGxpcXVlIHBldXQtw6p0cmUgbGUgbWlldXggY29tYmllbiBpbCDDqXRhaXQgZGlmZmljaWxlIGRlIGNvbmNldm9pciBsYSBwb3NzaWJpbGl0w6kgcXXigJl1bmUgc29jacOpdMOpIG5lIHNvaXQgcGFzIGZvbmTDqWUgc3VyIGRlcyBjcm95YW5jZXMgcmVsaWdpZXVzZXMgY29tbXVuZXM8c3VwPlszXSgvYXJ0aWNsZS92aXNpb24tZHUtbW9uZGUtZGVzZW5jaGFudGVlI25vdGVzKTwvc3VwPiFcblxuQ2UgcGFyYWRpZ21lIG9udG9sb2dpcXVlIG7igJnDqXRhaXQgcGFzIGV4ZW1wdCBkZSBteXN0w6hyZXMgZXQgYWRtZXR0YWl0IGzigJlpbnRlcnZlbnRpb24gY29uc3RhbnRlIGTigJl1bmUgc3Bow6hyZSBzcGlyaXR1ZWxsZSBpbnZpc2libGUsIG1haXMgb21uaXByw6lzZW50ZS5cblxuUGFyIGzigJlhdsOobmVtZW50IGR1IGTDqXNlbmNoYW50ZW1lbnQgZHUgbW9uZGUsIGluZHVpdCBwYXIgbGUgcHJvY2Vzc3VzIGRlIHPDqWN1bGFyaXNhdGlvbiwgbGEgdmlzaW9uIGFudGhyb3BvY2VudHJpcXVlIGEgc3VwcGxhbnTDqSBsYSBwcsOpY8OpZGVudGUuIExlIGNvc21vcyBhdXRyZWZvaXMgcGVyw6d1IGNvbW1lIGhhcm1vbmlldXggZXQgaW50ZW50aW9ubmVsIHPigJllc3QgZGlzc291dCBlbiB1biB1bml2ZXJzIHZpZGUgZGUgc2VucyBpbnRyaW5zw6hxdWUgZXQgc2FucyAqdGVsb3MqLiBM4oCZZXhpc3RlbmNlIHkgZXN0IHLDqWR1aXRlIMOgIHVuZSBzdWNjZXNzaW9uIGTigJlhY2NpZGVudHMgaGFzYXJkZXV4IGV0IGzigJllc3NlbmNlIGh1bWFpbmUgZXN0IGTDqW51w6llIGRlIHNpZ25pZmljYXRpb24gdWx0aW1lLlxuXG5EYW5zIGxlIG1vbmRlIGTDqXNlbmNoYW50w6ksIGxlcyBzdHJ1Y3R1cmVzIHBvbGl0aXF1ZXMsIHNvY2lhbGVzIGV0IGN1bHR1cmVsbGVzIHNlIHNvbnQgw6ltYW5jaXDDqWVzIGRlIGzigJlpbmZsdWVuY2UgZWNjbMOpc2lhc3RpcXVlLCDDqXZvbHVhbnQgZGUgbWFuacOocmUgYXV0b25vbWUgb8O5IGxhIHBlbnPDqWUgZXQgbOKAmWFjdGl2aXTDqSBodW1haW5lcyBzZSBzb250IGFmZnJhbmNoaWVzIGRlIGxhIHR1dGVsbGUgcmVsaWdpZXVzZS4gTGVzIGVzY2hhdG9sb2dpZXMgc8OpY3VsacOocmVzLCBpbW1hbmVudGVzIGV0IGTDqXNlbmNoYW50w6llcywgc2UgcsOpc3VtZW50IMOgIG1ldHRyZSBsZSBtb25kZSBtYXTDqXJpZWwgZW4gb3JkcmUgYWZpbiBk4oCZeSBwcm9zcMOpcmVyPHN1cD5bNF0oL2FydGljbGUvdmlzaW9uLWR1LW1vbmRlLWRlc2VuY2hhbnRlZSNub3Rlcyk8L3N1cD4uXG5cbkRhbnMgY2UgY29udGV4dGUgZMOpc2VuY2hhbnTDqSwgbGEgcmF0aW9uYWxpdMOpIGh1bWFuaXN0ZSBhIMOpbWVyZ8OpIGNvbW1lIGxhIHNldWxlIHNvdXJjZSBsw6lnaXRpbWUgZGUgbGEgY29ubmFpc3NhbmNlIGV0IGRlIGxhIG1vcmFsaXTDqSwgw6l2aW7Dp2FudCBs4oCZYW5jaWVubmUgaW50ZXJwcsOpdGF0aW9uIGTigJl1biBtb25kZSBpbXByw6lnbsOpIHBhciBsZSBjb21iYXQgc3Bpcml0dWVsLlxuXG5FbiBzb21tZSwgbGUgcGFzc2FnZSBk4oCZdW4gbW9uZGUgZW5jaGFudMOpIMOgIHVuIG1vbmRlIGTDqXNlbmNoYW50w6kgbWFycXVlIGxhIHRyYW5zaXRpb24gZOKAmXVuZSBjb3Ntb2xvZ2llIGRhbnMgbGFxdWVsbGUgbGUgZGl2aW4gb3JjaGVzdHJhaXQgbOKAmWVuc2VtYmxlIGRlIGxhIHZpZSwgw6AgdW4gcGFyYWRpZ21lIG/DuSBsZSByw6llbCBz4oCZYXbDqHJlIMOqdHJlIGxlIHByb2R1aXQgZOKAmXVuZSBzw6lyaWUgZOKAmcOpdsOpbmVtZW50cyBmb3J0dWl0cywgcHJpdsOpcyBkZSB0b3V0ZSBkaXJlY3Rpb24gZGl2aW5lLlxuXG4jIETigJl1biBjYWRyZSB0cmFuc2NlbmRhbnQgw6AgdW4gY2FkcmUgaW1tYW5lbnRcblxuU2Vsb24gVGF5bG9yLCBsZSBkw6lzZW5jaGFudGVtZW50IGVzdCBsYSB0cmFuc2l0aW9uIGTigJl1biBjYWRyZSB0cmFuc2NlbmRhbnQgw6AgdW4gY2FkcmUgaW1tYW5lbnQuXG5cbj4gTGEgZ3JhbmRlIGludmVudGlvbiBvY2NpZGVudGFsZSwgY+KAmWVzdCBs4oCZaWTDqWUgcXXigJlpbCBwdWlzc2UgZXhpc3RlciB1biBvcmRyZSBpbW1hbmVudCBkYW5zIGxhIE5hdHVyZSwgZG9udCBvbiBwdWlzc2UgY29tcHJlbmRyZSBzeXN0w6ltYXRpcXVlbWVudCBsZSBmb25jdGlvbm5lbWVudCBldCBs4oCZZXhwbGlxdWVyIGRhbnMgZGVzIHRlcm1lcyBxdWkgbHVpIHNvaWVudCBwcm9wcmVzLCBsYWlzc2FudCBvdXZlcnRlIGxhIHF1ZXN0aW9uIGRlIHNhdm9pciBzaSBjZXQgb3JkcmUgdG90YWwgcG9zc8OoZGUgdW5lIHNpZ25pZmljYXRpb24gcGx1cyBwcm9mb25kZSwgZXQgZGFucyBjZXR0ZSDDqXZlbnR1YWxpdMOpLCBzaSBub3VzIGRldm9ucyBlbiBpbmbDqXJlciBs4oCZZXhpc3RlbmNlIGTigJl1biBDcsOpYXRldXIgdHJhbnNjZW5kYW50LiBMYSBub3Rpb24gZGUgbOKAmVwiaW1tYW5lbnRcIiBpbXBsaXF1YWl0IGRlIGTDqW5pZXIgb3UgZHUgbW9pbnMgZOKAmWlzb2xlciBldCBkZSBwcm9ibMOpbWF0aXNlciB0b3V0ZSBmb3JtZSBk4oCZaW50ZXJww6luw6l0cmF0aW9uIGVudHJlIGxlcyBjaG9zZXMgZGUgbGEgTmF0dXJlIGV0IGxlIFwic3VybmF0dXJlbFwiLCBxdWUgbOKAmW9uIHZvaWUgZW4gY2UgZGVybmllciB1biBEaWV1IHRyYW5zY2VuZGFudCwgcGx1c2lldXJzIGRpZXV4LCBkZXMgZXNwcml0cyBvdSBkZXMgZm9yY2VzIG1hZ2lxdWVzLCBwZXUgaW1wb3J0ZS4gRMOpZmluaXIgbGEgcmVsaWdpb24gw6AgcGFydGlyIGRlIGxhIGRpc3RpbmN0aW9uIGltbWFuZW50L3RyYW5zY2VuZGFudCBjb3JyZXNwb25kIGRvbmMgw6AgdW4gZMOpcGxhY2VtZW50IHBhcmZhaXRlbWVudCBhanVzdMOpIMOgIG5vdHJlIGN1bHR1cmU8c3VwPls1XSgvYXJ0aWNsZS92aXNpb24tZHUtbW9uZGUtZGVzZW5jaGFudGVlI25vdGVzKTwvc3VwPi5cblxuRW4gZWZmZXQsIGzigJlPY2NpZGVudCBhIMOpdm9sdcOpIGTigJl1biBjYWRyZSB0cmFuc2NlbmRhbnQgb8O5IGxlcyBpZMOpYXV4IHNwaXJpdHVlbHMgZXQgw6l0aGlxdWVzIMOpdGFpZW50IGludHJpbnPDqHF1ZW1lbnQgbGnDqXMgw6AgRGlldSwgw6AgdW4gY2FkcmUgaW1tYW5lbnQgb8O5IGNlcyBtw6ptZXMgaWTDqWF1eCBwZXV2ZW50IHB1aXNlciBsZXVyIG9yaWdpbmUgZGFucyB1bmUgZGl2ZXJzaXTDqSBkZSBwZW5zw6llcyBldCBk4oCZaW5mbHVlbmNlcywgc291dmVudCBlbiBkZWhvcnMgZGUgdG91dGUgY29uc2lkw6lyYXRpb24gZGl2aW5lLlxuXG5EYW5zIHVuIGNhZHJlIHRyYW5zY2VuZGFudCwgRGlldSBkb25uZSB1biBzZW5zIMOgIGxhIHZpZS4gRGFucyB1biBjYWRyZSBpbW1hbmVudCwgbOKAmWhvbW1lIGRvaXQgdHJvdXZlciBlbiBsdWkgbGUgc2VucyBkZSBsYSB2aWUuIERhbnMgdW4gY2FkcmUgdHJhbnNjZW5kYW50LCBhbGxlciBhdSBDaWVsLCBj4oCZZXN0IGFsbGVyIGF1IHBhcmFkaXMsIGV0IGRhbnMgbGUgY2FkcmUgaW1tYW5lbnQsIGFsbGVyIGF1IGNpZWwsIGPigJllc3QgYWxsZXIgc3VyIE1hcnMuIExlIGNhZHJlIGltbWFuZW50IHJlbmQgcG9zc2libGVzIGzigJlodW1hbmlzbWUgZXhjbHVzaWYgZXQgbOKAmWV4aXN0ZW50aWFsaXNtZSBk4oCZdW4gSmVhbi1QYXVsIFNhcnRyZTpcblxuPiBM4oCZZXhpc3RlbnRpYWxpc21lIGF0aMOpZSwgcXVlIGplIHJlcHLDqXNlbnRl4oCmIGTDqWNsYXJlIHF1ZSBzaSBEaWV1IG7igJlleGlzdGUgcGFzLCBpbCB5IGEgYXUgbW9pbnMgdW4gw6p0cmUgY2hleiBxdWkgbOKAmWV4aXN0ZW5jZSBwcsOpY8OoZGUgbOKAmWVzc2VuY2UsIHVuIMOqdHJlIHF1aSBleGlzdGUgYXZhbnQgZGUgcG91dm9pciDDqnRyZSBkw6lmaW5pIHBhciB1biBjb25jZXB0IGV0IHF1ZSBjZXQgw6p0cmUsIGPigJllc3QgbOKAmWhvbW1l4oCmIFF14oCZZXN0LWNlIHF1ZSBzaWduaWZpZSBpY2kgcXVlIGzigJlleGlzdGVuY2UgcHLDqWPDqGRlIGzigJllc3NlbmNlPyBDZWxhIHNpZ25pZmllIHF1ZSBs4oCZaG9tbWUgZXhpc3RlIGTigJlhYm9yZCwgc2UgcmVuY29udHJlLCBzdXJnaSBkYW5zIGxlIG1vbmRlIGV0IHF14oCZaWwgc2UgZMOpZmluaXQgYXByw6hzLiAo4oCmKSBBaW5zaSwgaWwgbuKAmXkgYSBwYXMgZGUgbmF0dXJlIGh1bWFpbmUsIHB1aXNxdeKAmWlsIG7igJl5IGEgcGFzIGRlIERpZXUgcG91ciBsYSBjb25jZXZvaXIuIEzigJlob21tZSBlc3Qgbm9uIHNldWxlbWVudCB0ZWwgcXXigJlpbCBzZSBjb27Dp29pdCwgbWFpcyB0ZWwgcXXigJlpbCBzZSB2ZXV0LCBldCBjb21tZSBpbCBzZSBjb27Dp29pdCBhcHLDqHMgbOKAmWV4aXN0ZW5jZSwgY29tbWUgaWwgc2UgdmV1dCBhcHLDqHMgY2V0IMOpbGFuIHZlcnMgbOKAmWV4aXN0ZW5jZSwgbOKAmWhvbW1lIG7igJllc3QgcmllbiBk4oCZYXV0cmUgcXVlIGNlIHF14oCZaWwgc2UgZmFpdDxzdXA+WzZdKC9hcnRpY2xlL3Zpc2lvbi1kdS1tb25kZS1kZXNlbmNoYW50ZWUjbm90ZXMpPC9zdXA+LlxuXG5TZWxvbiBUYXlsb3IsIHBvdXIgZXhwbGlxdWVyIGNvbW1lbnQgbOKAmcOiZ2Ugc8OpY3VsaWVyIHBldXQgcmVuZHJlIHBvc3NpYmxlcyBsZXMgdmlzaW9ucyBkdSBtb25kZSBpbW1hbmVudGlzdGVzLCBpbCBuZSBmYXV0IGRvbmMgcGFzIHNpbXBsZW1lbnQgcmFjb250ZXIgY29tbWVudCBEaWV1IHPigJllc3QgcmV0aXLDqSBkZXMgdHJvaXMgYmlhaXMgY2l0w6lzIHBsdXMgaGF1dCAoY2YuIGF1IHByb2Nlc3N1cyBkZSBzw6ljdWxhcmlzYXRpb24pLCBtYWlzIMOpZ2FsZW1lbnQgw6ljbGFpcmNpciAq4oCcY29tbWVudCBxdWVscXVlIGNob3NlIHF1aSBu4oCZZXN0IHBhcyBEaWV1IHPigJllc3QgbsOpY2Vzc2FpcmVtZW50IGltcG9zw6kgZmFjZSDDoCBsYSBkZW1hbmRlIHNwaXJpdHVlbGxlIG91IG1vcmFsZSBjb21tZSBsZSByw6lmw6lyZW50IG9iamVjdGlmIGRlIGxhIFwicGzDqW5pdHVkZVwiLiBFbiB1biBzZW5zLCBsYSBxdWVzdGlvbiBkw6ljaXNpdmUgbuKAmWVzdCBwYXMgdGFudCBkZSBzYXZvaXIgY2UgcXVpIHPigJllc3QgcGFzc8OpIHF1ZSBkZSBjb21wcmVuZHJlIGNvbW1lbnQgbGEgcGzDqW5pdHVkZSBhIHB1IGZpbmFsZW1lbnQgcmVudm95ZXIgw6AgYXV0cmUgY2hvc2UgcXXigJnDoCBEaWV14oCdKjxzdXA+WzddKC9hcnRpY2xlL3Zpc2lvbi1kdS1tb25kZS1kZXNlbmNoYW50ZWUjbm90ZXMpPC9zdXA+LlxuXG4jIExhIHLDqXZvbHV0aW9uIG1vcmFsZSBldCBsZSByZXRvdXIgYXUgcGFnYW5pc21lXG5cbkxhIHBoaWxvc29waGUgQ2hhbnRhbCBEZWxzb2wsIMOgIGzigJlpbnN0YXIgZGUgQ2hhcmxlcyBUYXlsb3IsIHPigJlpbnTDqXJlc3NlIMOgIGxhIGZhw6dvbiBkb250IGxlIGTDqWNsaW4gZGUgbOKAmWluZmx1ZW5jZSBjaHLDqXRpZW5uZSBldCBsZSBtb3V2ZW1lbnQgdmVycyBsYSBzw6ljdWxhcmlzYXRpb24gcmVudmVyc2VudCBsZXMgc3RydWN0dXJlcyBtb3JhbGVzIGV0IG9udG9sb2dpcXVlcyBxdWkgw6l0YWllbnQgYXVwYXJhdmFudCBlbiBwbGFjZS4gRGVsc29sIHByb3Bvc2UgcXVlIGxhIHPDqWN1bGFyaXNhdGlvbiBhaXQgYWJvdXRpIMOgIHVuIHJlbnZlcnNlbWVudCBub3JtYXRpZiBkZSBsYSBtb3JhbGUgZXQgZGUgbOKAmW9udG9sb2dpZS4gU2Vsb24gZWxsZSwgbGUgZMOpc2VuY2hhbnRlbWVudCBkZSBs4oCZT2NjaWRlbnQgbmUgY29uZHVpdCBwYXMgw6AgdW4gc2ltcGxlIHJlbXBsYWNlbWVudCBkZSBs4oCZYW5jaWVuIHBhciBkdSBub3V2ZWF1LCBtYWlzIHVuZSBmb3JtZSBkZSByZXRvdXIgw6AgdW4gY2FkcmUgbW9yYWwgZXQgb250b2xvZ2lxdWUgcXVpIHByw6ljw6hkZSBjZWx1aSBkdSBjaHJpc3RpYW5pc21lLCB1bmUgcsOpdm9sdXRpb24gKuKAnGF1IHNlbnMgc3RyaWN0IGRlIHJldG91ciBkZSBjeWNsZSwgZGFucyBsZXMgZGV1eCBkb21haW5lcyBmb25kYXRldXJzIGRlIGzigJlleGlzdGVuY2UgaHVtYWluZTogbGEgbW9yYWxlIGV0IGzigJlvbnRvbG9naWXigJ0qLiBFbGxlIHBvdXJzdWl0OlxuXG4+IE5vdXMgc29tbWVzIMOgIGxhIGZvaXMgbGVzIHN1amV0cyBldCBsZXMgYWN0ZXVycyBk4oCZdW5lIGludmVyc2lvbiBub3JtYXRpdmUsIGV0IGTigJl1bmUgaW52ZXJzaW9uIG9udG9sb2dpcXVlLiBD4oCZZXN0IGRpcmUgcXVlIG5vcyBwcsOpY2VwdGVzIG1vcmF1eCwgYXVzc2kgYmllbiBxdWUgbm9zIHZpc2lvbnMgZHUgbW9uZGUg4oCUIGF2ZWMgbm90cmUgcGxhY2UgYXUgc2VpbiBkZSBjZSBtb25kZSDigJQsIHNvbnQgZW4gdHJhaW4gZGUgc2UgcmVudmVyc2VyLiBOb24gcGFzIHF1ZSBs4oCZYW5jaWVuIGZhc3NlIHBsYWNlIMOgIGRlcyBub3V2ZWF1dMOpcyBpbsOpZGl0ZXMg4oCUIGlsIG7igJl5IGEgcGFzIGJlYXVjb3VwIGRlIG5vdXZlYXUgc291cyBsZSBzb2xlaWwuIE1haXMgb24gYXNzaXN0ZSBwbHV0w7R0IMOgIHVuZSBzb3J0ZSBkZSByZXRvdXIgYXV4IHNvdXJjZXMuIOKAlCDDgCBxdWVsbGVzIHNvdXJjZXM/IMOAIGNlbGxlcyBxdWkgcHLDqWPDqWRhaWVudCBsYSBDaHLDqXRpZW50w6ksIGV0IG3Dqm1lIGxhIGp1ZMOpby1jaHLDqXRpZW50w6k8c3VwPls4XSgvYXJ0aWNsZS92aXNpb24tZHUtbW9uZGUtZGVzZW5jaGFudGVlI25vdGVzKTwvc3VwPi5cblxuTOKAmWludmVyc2lvbiBub3JtYXRpdmUgbW9yYWxlIGV0IG9udG9sb2dpcXVlIGTDqXBlaW50ZSBwYXIgRGVsc29sIGluZGlxdWUgdW4gZMOpcGxhY2VtZW50IGRlcyBzb3VyY2VzIGTigJlhdXRvcml0w6kgcG91ciBkw6ljcmlyZSBs4oCZZXhpc3RlbmNlIGV0IGxhIG1vcmFsZS5cblxuVGF5bG9yIGV0IERlbHNvbCBwYXJ2aWVubmVudCDDoCB1bmUgY29uY2x1c2lvbiBpZGVudGlxdWUuIEVuIE9jY2lkZW50LCBs4oCZw4lnbGlzZSBu4oCZYSBwbHVzIGxlIG1vbm9wb2xlIGRlIGzigJlhdXRvcml0w6kgbm9ybWF0aXZlIGV0IG5vcm1hbnRlPHN1cD5bOV0oL2FydGljbGUvdmlzaW9uLWR1LW1vbmRlLWRlc2VuY2hhbnRlZSNub3Rlcyk8L3N1cD4uIEVsbGUgZXN0IGZvcnRlbWVudCBjb25jdXJyZW5jw6llIHBhciBkZSBub21icmV1c2VzIHNvdXJjZXMgZOKAmWF1dG9yaXTDqXMsIGNvbW1lIGRhbnMgbOKAmWFudGlxdWl0w6kgcGHDr2VubmUgcHLDqS1jaHLDqXRpZW5uZTxzdXA+WzEwXSgvYXJ0aWNsZS92aXNpb24tZHUtbW9uZGUtZGVzZW5jaGFudGVlI25vdGVzKTwvc3VwPi4gRW4gc29tbWUsIG5vdXMgZGV2b25zIGNvbXByZW5kcmUgbm90cmUgY29udGV4dGUgY29tbWUgw6l0YW50IHVuZSBzb2Npw6l0w6kgbsOpb3BhZ2FuaXN0ZSwgbWFpcyBkb250IGxlcyBkaXZpbml0w6lzIHNvbnQgZGVzIGlkw6lvbG9naWVzIGltbWFuZW50aXN0ZXMgKG1hdMOpcmlhbGlzbWUsIGF0aMOpw69zbWUsIGNhcGl0YWxpc21lLCB3b2tpc21lLCDDqWNvbG9naXNtZeKApikuXG5cbjxiciBpZD1cIm5vdGVzXCI+XG5cbjxzdWI+MS4gTWFyY2VsIEdhdWNoZXQsICpMZSBkw6lzZW5jaGFudGVtZW50IGR1IG1vbmRlOiBVbmUgaGlzdG9pcmUgcG9saXRpcXVlIGRlIGxhIHJlbGlnaW9uKiwgTm91dmVsbGUgw6lkaXRpb24sIEZvbGlvLCBubyA0NjYsIFBhcmlzLCBHYWxsaW1hcmQsIDIwMDUsIHAuMTAuPGJyPjIuIENoYXJsZXMgVGF5bG9yLCAqTOKAmcOCZ2Ugc8OpY3VsaWVyKiwgUGFyaXMsIFNldWlsLCAyMDExLjxicj4zLiAqSWJpZCosIHAuODUuPGJyPjQuIFBhciBleGVtcGxlIGxlcyBpZMOpYXV4IG1hcnhpc3RlcywgY2FwaXRhbGlzdGVzIG91IMOpY29sb2dpc3Rlcy4gPGJyPjUuIENoYXJsZXMgVGF5bG9yLCAqb3AuIGNpdC4qLCBwMzgtMzkuPGJyPjYuIEplYW4tUGF1bCBTYXJ0cmUsICpM4oCZZXhpc3RlbnRpYWxpc21lIGVzdCB1biBodW1hbmlzbWUqLCBQYXJpcywgR2FsbGltYXJkLCAyMDIxLCBwLiAyOS0zMC48YnI+Ny4gQ2hhcmxlcyBUYXlsb3IsICpvcC4gY2l0LiosIHAuNTUuPGJyPjguIENoYW50YWwgRGVsc29sLCAqTGEgZmluIGRlIGxhIGNocsOpdGllbnTDqTogTOKAmWludmVyc2lvbiBub3JtYXRpdmUgZXQgbGUgbm91dmVsIMOiZ2UqLCBQYXJpcywgTGVzIMOpZGl0aW9ucyBkdSBDZXJmLCAyMDIxLCBwLiAzNi4gPGJyPjkuIEljaSwgbCdhdXRvcml0w6kgbm9ybWF0aXZlIGNyw6llIGxlcyBub3JtZXMsIHRhbmRpcyBxdWUgbCdhdXRvcml0w6kgbm9ybWFudGUgbGVzIG1ldCBlbiDFk3V2cmUgZXQgbGVzIGZhaXQgcmVzcGVjdGVyLiA8YnI+MTAuIERlbHNvbCBjb3Jyw6hsZSBsZSBkw6lidXQgZGUgbGEgY2hyw6l0aWVudMOpIGF1IHLDqGduZSBkZSBDb25zdGFudGluLCBwcmVtaWVyIGVtcGVyZXVyIHJvbWFpbiBjaHLDqXRpZW4gKDMwNi0zMzcpLjwvc3ViPiIsImF1dGhvciI6eyJmdWxsTmFtZSI6IlJhcGhhw6tsIENoYXJyaWVyIiwicGljdHVyZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L3JhcGhhZWxfY2hhcnJpZXJfMDNfY29waWVfMV9zY2FsZWRfZTE2NTc4MDEyNTc3ODdfZjkzNTk5NDAzOC5qcGciLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9fSwiaW1hZ2UiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9kb2xsYXJfZ2lsbF9qbV9Oal9YcjN4X1o0X01fdW5zcGxhc2hfYTBiYTYwMjVmZS5qcGciLCJoZWlnaHQiOjczMywid2lkdGgiOjExMDAsImFsdGVybmF0aXZlVGV4dCI6IiIsInByb3ZpZGVyIjoiYXdzLXMzLWVuaGFuY2VkIn0sInRvcGljcyI6W3sibmFtZSI6IkRvY3RyaW5lIGRlIERpZXUifSx7Im5hbWUiOiJUaMOpb2xvZ2llIEhpc3RvcmlxdWUifSx7Im5hbWUiOiJBcG9sb2fDqXRpcXVlIGV0IHZpc2lvbiBkdSBtb25kZSJ9LHsibmFtZSI6IkN1bHR1cmUgZXQgYXJ0cyJ9LHsibmFtZSI6IlBoaWxvc29waGllIn1dLCJtb2R1bGVzIjpbeyJfX3R5cGVuYW1lIjoiQ29tcG9uZW50TW9kdWxlTGVhZCIsImNvbnRlbnQiOiJOb3VzIHNvbW1lcyBkYW5zIHVuIG1vbmRlIGTDqXNlbmNoYW50w6kuIENlcnRhaW5zIHF1YWxpZmllbnQgY2UgZMOpc2VuY2hhbnRlbWVudCBjb21tZSB1bmUgZm9ybWUgZGUgcGVydGUgZOKAmWVzcG9pci4gTWFpcyBsZSBkw6lzZW5jaGFudGVtZW50IGVzdCB1biBwaMOpbm9tw6huZSBiZWF1Y291cCBwbHVzIHByb2ZvbmQgcXVlIGNlbGEsIGNvbnPDqXF1ZW5jZSBkZSBsYSBzw6ljdWxhcmlzYXRpb24uIn1dfSx7ImlkIjoiNDA0MiIsInRpdGxlIjoiTGEgZm9ybWF0aW9uIGRlcyBkaXNjaXBsZXMgZGFucyBsZXMgw4l2YW5naWxlcyIsInNsdWciOiJmb3JtYXRpb24tZGlzY2lwbGVzLWRhbnMtZXZhbmdpbGVzIiwidHlwZSI6ImFydGljbGUiLCJwdWJsaXNoZWRfYXQiOiIyMDI0LTA2LTIxVDAzOjQ1OjAwLjAwMFoiLCJib2R5IjoiIyAxLiBM4oCZYXBwZWwgZXhpZ2VhbnQgZGUgSsOpc3VzXG5cbkTDqHMgcXVlIErDqXN1cyBmdXQgYmFwdGlzw6ksIGRlcyBnZW5zIGNvbW1lbmPDqHJlbnQgw6AgbGUgc3VpdnJlIGRlIGxldXIgcHJvcHJlIGluaXRpYXRpdmUuIFBhcm1pIGV1eCwgZGVzIGRpc2NpcGxlcyBkZSBKZWFuLUJhcHRpc3RlLiBMZXVycyBtb3RpdmF0aW9ucyDDqXRhaWVudCBkaXZlcnNlczogYXR0ZW50ZSBtZXNzaWFuaXF1ZSwgY3VyaW9zaXTDqSwgZMOpc2lyIGRlIHZvaXIgZGVzIG1pcmFjbGVzIChKbiAxLjM1LTQzKS4gVm95YWdlciBhdmVjIErDqXN1cyBhZmluIGRlIGzigJnDqWNvdXRlciBldCBkZSBsZSB2b2lyIGFjY29tcGxpciBkZXMgbWlyYWNsZXMgZmFpdC1pbCBwb3VyIGF1dGFudCBkZSBjZXMgcGVyc29ubmVzIHNlcyBkaXNjaXBsZXM/IExlIHLDqWNpdCBkZSBs4oCZYXBwZWwgYWRyZXNzw6kgcGFyIErDqXN1cyDDoCBsYSBmb3VsZSBlbiBMdWMsIGNoYXBpdHJlIHF1YXRvcnplLCBub3VzIMOpY2xhaXJlIHN1ciBjZSBwb2ludC4gTHVjIHJlbGF0ZSBxdWUgZGVzIGZvdWxlcyBmYWlzYWllbnQgcm91dGUgYXZlYyBKw6lzdXMgKGV0IGxlcyBkaXNjaXBsZXMgZMOpasOgIGVuZ2Fnw6lzIMOgIHNlcyBjw7R0w6lzKSBhbG9ycyBxdeKAmWlsIHNlIHJlbmRhaXQgw6AgSsOpcnVzYWxlbSAoTHVjLiAxNC4yNS0zNSkuIErDqXN1cyBzZSByZXRvdXJuZSBldCBsZXVyIGFkcmVzc2UgY2V0IGFwcGVsOlxuXG4+IFNpIHF1ZWxxdeKAmXVuIHZpZW50IMOgIG1vaSBzYW5zIG1lIHByw6lmw6lyZXIgw6Agc29uIHDDqHJlLCBzYSBtw6hyZSwgc2EgZmVtbWUsIHNlcyBlbmZhbnRzLCBzZXMgZnLDqHJlcyBldCBzZXMgc8WTdXJzLCBldCBtw6ptZSDDoCBzYSBwcm9wcmUgdmllLCBpbCBuZSBwZXV0IHBhcyDDqnRyZSBtb24gZGlzY2lwbGUuIENlbHVpIHF1aSBuZSBwb3J0ZSBwYXMgc2EgY3JvaXggZXQgbmUgbWUgc3VpdCBwYXMgbmUgcGV1dCBwYXMgw6p0cmUgbW9uIGRpc2NpcGxlLlxuPlxuPiA8c3ViPjxjaXRlPkx1YyAxNC4yNi0yNzwvY2l0ZT48L3N1Yj5cblxuQ2hyaXN0IGVzdCDDoCBs4oCZaW5pdGlhdGl2ZSwgZXQgY29udHJhaXJlbWVudCDDoCBsYSB0cmFkaXRpb24gcmFiYmluaXF1ZSwgY2UgbmUgc29udCBwYXMgbGVzIGRpc2NpcGxlcyBxdWkgdm9udCDDoCBsdWksIG1haXMgY+KAmWVzdCBsdWkgcXVpIHZhIHZlcnMgZXV4IGV0IGxlcyBhcHBlbGxlPHN1cD5bMl0oL2FydGljbGUvZm9ybWF0aW9uLWRpc2NpcGxlcy1kYW5zLWV2YW5naWxlcyNub3Rlcyk8L3N1cD4uIE5lIHBldXQgZG9uYyByw6llbGxlbWVudCDDqnRyZSBkaXNjaXBsZSBkZSBKw6lzdXMgcXVlIGNlbHVpIHF1aSBhIHLDqXBvbmR1IMOgIHNvbiBhcHBlbC4gRW4gZWZmZXQsIErDqXN1cyBhcHBlbGxlIGx1aS1tw6ptZSBkZXMgcGVyc29ubmVzIGVuIHBhcnRpY3VsaWVyIMOgIGRldmVuaXIgc2VzIGRpc2NpcGxlcyAoTXQgNC4xOC0yMjsgMTkuMjEtMjI7IE1jIDIuMTQ7IExjIDkuNTctNjIpIGFpbnNpIHF1ZSBkZXMgZ3JvdXBlcyBkZSBwZXJzb25uZXMsIGNvbW1lIG5vdXMgdmVub25zIGRlIGzigJlvYnNlcnZlci5cblxuQWluc2ksIGxhIHLDqXBvbnNlIGRlIGxhIHBlcnNvbm5lIGVzdCB1biBwb2ludCBkZSBwaXZvdC4gRWxsZSBkw6l0ZXJtaW5lcmEgc2kgZWxsZSBkZXZpZW50IHNvbiBkaXNjaXBsZSBlbiByw6lwb25kYW50IMOgIGzigJlpbnZpdGF0aW9uIGV0IGF1eCBjb25kaXRpb25zIGltcG9zw6llcyBwYXIgSsOpc3VzIG91IHNpIGVsbGUgc+KAmcOpbG9pZ25lcmEgZGUgbHVpPHN1cD5bM10oL2FydGljbGUvZm9ybWF0aW9uLWRpc2NpcGxlcy1kYW5zLWV2YW5naWxlcyNub3Rlcyk8L3N1cD4uXG5cbkVuIG91dHJlLCBKw6lzdXMgdm91bGFpdCBxdWUgY2V1eCBxdWkgdmV1bGVudCBkZXZlbmlyIHNlcyBkaXNjaXBsZXMgbWVzdXJlbnQgbGUgcHJpeCDDoCBwYXllciBwb3VyIGzigJnDqnRyZS4gQWluc2ksIGlsIG7igJlhIHBhcyBjYWNow6kgbGVzIGRpZmZpY3VsdMOpcyBpbmjDqXJlbnRlcyDDoCBsYSB2aWUgw6Agc2Egc3VpdGUgKExjIDE0LjI4LTMyKS4gU3VpdnJlIErDqXN1cyBwZXV0IGltcGxpcXVlciBkZSByZW5vbmNlciDDoCBkZXMgY2hvc2VzIGzDqWdpdGltZXMgKExjIDkuNTctNjI7IExjIDE0LjMzKS5cblxuUGVyc29ubmUgbmUgcGV1dCBkb25jIMOqdHJlIHNvbiBkaXNjaXBsZSwgw6AgbW9pbnMgcXXigJlpbCBuZSBwbGFjZSBKw6lzdXMgYXUtZGVzc3VzIGRlIHRvdXQsIG3Dqm1lIGRlIHNhIHByb3ByZSB2aWUgKExjIDkuMjMtMjcpLiBQZXUgaW1wb3J0ZSBsZSBjb8O7dCBldCBsZXMgc2FjcmlmaWNlcyBldCBsZXMgw6lwcmV1dmVzLiBTdWl2cmUgSsOpc3VzIGltcGxpcXVlIHVuZSBjb25zw6ljcmF0aW9uIGV0IHVuIGTDqXZvdWVtZW50IHRvdGFsLiBQb3VyIGVudHJlciDDoCBzb24gw6ljb2xlLCBsYSBwZXJzb25uZSBkb2l0IGFjY2VwdGVyIGxlIHJlbm9uY2VtZW50IGTDqWNpc2lmIMOgIHNlcyBkcm9pdHMgc3VyIHNhIHZpZSwgc29uIGF1dG9ub21pZSBldCBkw6lzaXJlciB1bmUgaWRlbnRpZmljYXRpb24gY29udGludWVsbGUgZXQgcXVvdGlkaWVubmUgw6AgbHVpLCB5IGNvbXByaXMgZGFucyBsYSBzb3VmZnJhbmNlIGV0IGxhIG1vcnQuXG5cblLDqXBvbmRyZSDDoCBs4oCZYXBwZWwgZGUgSsOpc3VzLCBj4oCZZXN0IGRvbmMgbHVpIGZhaXJlIGFsbMOpZ2VhbmNlLCBlbiBsYWlzc2FudCBkZXJyacOocmUgc29pIChkZSBtYW5pw6hyZSBsaXR0w6lyYWxlIG91IGZpZ3Vyw6llKSBjZSBxdWUgbOKAmW9uIMOpdGFpdCwgYWZpbiBkZSBkZXZlbmlyIGNlIHF1ZSBKw6lzdXMgbm91cyBhcHBlbGxlIMOgIMOqdHJlOiBzb24gZGlzY2lwbGU8c3VwPls0XSgvYXJ0aWNsZS9mb3JtYXRpb24tZGlzY2lwbGVzLWRhbnMtZXZhbmdpbGVzI25vdGVzKTwvc3VwPi5cblxuQ2V0IGFwcGVsIGluZGl2aWR1ZWwgYWJvdXRpdCDDoCBs4oCZaW50w6lncmF0aW9uIGF1IHNlaW4gZGUgbGEgY29tbXVuYXV0w6kgZGUgSsOpc3VzLiBFbiBlZmZldCwgc3VpdnJlIErDqXN1cywgY+KAmWVzdCByZWpvaW5kcmUgbGUgZ3JvdXBlIGRlcyBkaXNjaXBsZXMgcXVlIErDqXN1cyByYXNzZW1ibGUgYXV0b3VyIGRlIGx1aS4gQ2VydGVzLCBKZWFuLUJhcHRpc3RlLCBsZXMgZXNzw6luaWVucyBldCBsZXMgcXVtcsOibmllbnMgY3LDqWFpZW50IMOpZ2FsZW1lbnQgZGVzIGNvbW11bmF1dMOpcyBhdXRvdXIgZOKAmXVuIHByb2pldCByZWxpZ2lldXggb3UgcG9saXRpcXVlLiBNYWlzIErDqXN1cyBjcsOpZSB1bmUgY29tbXVuYXV0w6kgZG9udCBpbCBlc3QgbGUgY2VudHJlIGV0LCBjb21tZSBsZSBzb3VsaWduZSBEYXZpZCBCb3NoLCDigJxkZXZpZW50IGzigJlvYmpldCBkZSBzb24gcHJvcHJlIGVuc2VpZ25lbWVudCBwZW5kYW50IHNvbiBtaW5pc3TDqHJlIHRlcnJlc3RyZTogY+KAmWVzdCBsdWkgcXVpIGluY2FybmUgbGUgcsOoZ25lIGRlIERpZXUsIGzigJnDiXZhbmdpbGU8c3VwPls1XSgvYXJ0aWNsZS9mb3JtYXRpb24tZGlzY2lwbGVzLWRhbnMtZXZhbmdpbGVzI25vdGVzKTwvc3VwPuKAnS5cblxuSGVucmkgQmxvY2hlciBs4oCZb2JzZXJ2ZSBwYXIgYWlsbGV1cnM6XG5cbj4gSsOpc3VzIGNvbnN0aXR1ZSBhdXRvdXIgZGUgc2EgcGVyc29ubmUgdW5lIHNvcnRlIGRlIGNvbW11bmF1dMOpLiBDZSBu4oCZZXN0IHBhcyBsYSBzZXVsZSBwcsOpc2VuY2UgZGUgZGlzY2lwbGVzIHF1aSBlc3QgaWNpIHNpZ25pZmljYXRpdmUg4oCUaWwgeSBlbiBhdmFpdCBhdXRvdXIgZGUgSmVhbi1CYXB0aXN0ZeKAlCBtYWlzIGxhIG1hbmnDqHJlIGRvbnQgSsOpc3VzIGxlcyBhcHBlbGxlLCBsZXMgb3JnYW5pc2UsIGxlcyBzaXR1ZSBwYXIgcmFwcG9ydCDDoCBsdWktbcOqbWUgZXQgc2Ugc2l0dWUgbHVpLW3Dqm1lIHBhciByYXBwb3J0IMOgIGV1eDxzdXA+WzZdKC9hcnRpY2xlL2Zvcm1hdGlvbi1kaXNjaXBsZXMtZGFucy1ldmFuZ2lsZXMjbm90ZXMpPC9zdXA+LlxuXG5DZXR0ZSBjb21tdW5hdXTDqSwgcXVpIG5lIHJhc3NlbWJsZSBxdWUgZGVzIGp1aWZzIGR1cmFudCBsZSBtaW5pc3TDqHJlIHRlcnJlc3RyZSBkZSBKw6lzdXMsIG11ZXJhIGFwcsOocyBsYSBQZW50ZWPDtHRlLCBwb3VyIHJhc3NlbWJsZXIgZW4gdW4gcGV1cGxlIG5vdXZlYXUsIGRlcyBkaXNjaXBsZXMgZGUgdG91dGVzIGxlcyBuYXRpb25zIChNdCAyOC4xOTsgw4lwIDIuMTEtMjIpLlxuXG4jIDIuIExhIGZvcm1hdGlvbiBjb21wbMOodGUgZG9ubsOpZSBwYXIgSsOpc3VzXG5cbldpbGtpbnMgYWZmaXJtZTpcblxuPiBMYSB2aWUgZHUgZGlzY2lwbGUgY29tbWVuY2UgcGFyIGxhIHJlbGF0aW9uIGF2ZWMgbGUgbWHDrnRyZSBldCBzZSBkw6lwbG9pZSBkYW5zIHRvdXMgbGVzIGRvbWFpbmVzIGRlIGxhIHZpZS4gXFxb4oCmXFxdIFN1aXZyZSBKw6lzdXMgc2lnbmlmaWFpdCDDqnRyZSBhdmVjIGx1aSBldCBsZSBzZXJ2aXIgZGFucyBs4oCZYWNjb21wbGlzc2VtZW50IGRlIHNhIG1pc3Npb248c3VwPls3XSgvYXJ0aWNsZS9mb3JtYXRpb24tZGlzY2lwbGVzLWRhbnMtZXZhbmdpbGVzI25vdGVzKTwvc3VwPi5cblxuUHVpcywgaWwgcHJvcG9zZSBxdWF0cmUgY29tcG9zYW50ZXMgw6AgbGEgc3VpdmFuY2UgZGUgSsOpc3VzIGRhbnMgbGVzIMOJdmFuZ2lsZXMuIFByZW1pw6hyZW1lbnQsIGzigJlhdHRhY2hlbWVudCBjb250aW51ZWwgZXQgdG90YWwgw6AgSsOpc3VzLiBEZXV4acOobWVtZW50LCBKw6lzdXMgbmUgY2FjaGUgcGFzIMOgIHNlcyBkaXNjaXBsZXMgcXXigJlpbHMgZGV2cm9udCBwYXllciBsZSBwcml4IGRhbnMgbGEgZHVyw6llLiBUcm9pc2nDqG1lbWVudCwgbGEgZm9ybWF0aW9uIGRlIGRpc2NpcGxlcyBpbXBsaXF1ZSBkZSBkZXZlbmlyIGNvbW1lIErDqXN1czpcblxuRGV2ZW5pciBjb21tZSBKw6lzdXMgY29tcHJlbmQgbGUgZmFpdCBk4oCZw6p0cmUgZW52b3nDqSBwcm9jbGFtZXIgbGUgbcOqbWUgbWVzc2FnZSBxdWUgbHVpLCBhY2NvbXBsaXIgbGUgbcOqbWUgbWluaXN0w6hyZSBldCBtYW5pZmVzdGVyIGxhIG3Dqm1lIGNvbXBhc3Npb24gKE10IDEwLjUtNDIpLCBwcmF0aXF1ZXIgbGVzIG3Dqm1lcyB0cmFkaXRpb25zIHJlbGlnaWV1c2VzIGV0IHNvY2lhbGVzIChNdCAxMi4xLTg7IE1jIDIuMTgtMjIpLCBhcHBhcnRlbmlyIMOgIGxhIG3Dqm1lIGZhbWlsbGUgZOKAmW9iw6lpc3NhbmNlIGF1IFDDqHJlIChNdCAxMi40Ni00OSksIGVmZmVjdHVlciBsZSBtw6ptZSBzZXJ2aWNlIChNdCAyMC4yNi0yODsgTWMgMTAuNDItNDU7IEpuIDEzLjEyLTE3KSBldCBhdm9pciBwYXJ0IGF1eCBtw6ptZXMgc291ZmZyYW5jZXMgKE10IDEwLjE2LTI1OyBNYyAxMC4zOC0zOSk8c3VwPls4XSgvYXJ0aWNsZS9mb3JtYXRpb24tZGlzY2lwbGVzLWRhbnMtZXZhbmdpbGVzI25vdGVzKTwvc3VwPi5cblxuRW5maW4sIGxhIGZvcm1hdGlvbiBkZXMgZGlzY2lwbGVzIHByb2R1aXQgbGV1ciBjcm9pc3NhbmNlLiBNYWlzIGxlcyDDiXZhbmdpbGVzIGJyb3NzZW50IHVuIHBvcnRyYWl0IHNhbnMgY29uY2Vzc2lvbiBkZSBjZXMgZGVybmllcnMuIElscyBuZSBzb250IHBhcyBwcsOpc2VudMOpcyBjb21tZSBwYXJmYWl0cywgbWFpcyBlbiBjaGVtaW5lbWVudC4gSWxzIHByb2dyZXNzZW50LCBtYWlzIGRlIG1hbmnDqHJlIG9zY2lsbGF0b2lyZS4gV2lsa2lucyBkw6ljcml0IGNvbW1lbnQgSsOpc3VzIGxlcyBhaWRlIMOgIGdyYW5kaXI6XG5cbkrDqXN1cyBsZXMgZW5zZWlnbmUgKE1jIDQuMTAtMTIpLCBsZXMgcmVwcmVuZCAoTXQgMTYuNS0xMiksIGxlcyBtZXQgZW4gZ2FyZGUgKE10IDE3LjE5LTIwKSwgbGVzIHNvdXRpZW50IChMYyAyMi4zMS0zNCksIGxlcyByw6ljb25mb3J0ZSAoSm4gMjAuMTktMjIpIGV0IGxlcyByZWzDqHZlIChJbiAyMS4xNS0xOSkuIMOAIGxldXIgdG91ciwgbGVzIGRpc2NpcGxlcyBkZXZpZW5kcm9udCBkZXMgZXhlbXBsZXMgZGUgY2UgcXVlIErDqXN1cyBkw6lzaXJlIGZhaXJlIHBvdXIgbOKAmcOJZ2xpc2UgKE10IDI4LjE5LTIwKS4gVG91cyBjZXV4IHF1aSBjcm95YWllbnQgdnJhaW1lbnQgZW4gSsOpc3VzIMOpdGFpZW50IGFwcGVsw6lzIFwiZGlzY2lwbGVzXCIgZHUgdGVtcHMgZGUgSsOpc3VzOyBpbHMgc29udCBkZXMgZXhlbXBsZXMgZGUgbGEgZmHDp29uIGRvbnQgbGVzIGNocsOpdGllbnMgZOKAmWF1am91cmTigJlodWkgcGV1dmVudCBldCBkb2l2ZW50IGdyYW5kaXIgZGFucyBsYSBmb2k8c3VwPls5XSgvYXJ0aWNsZS9mb3JtYXRpb24tZGlzY2lwbGVzLWRhbnMtZXZhbmdpbGVzI25vdGVzKTwvc3VwPuKAplxuXG5Db2xpbiBNYXJzaGFsbCBldCBUb255IFBheW5lIHByb3Bvc2VudCBkZSBzeW50aMOpdGlzZXIgbGUgcHJvZ3JhbW1lIGRlIGZvcm1hdGlvbiBkZSBKw6lzdXMgZW4gdHJvaXMgZG9tYWluZXMsIFwidHJvaXMgQ1wiLiBDYXJhY3TDqHJlLCBDb252aWN0aW9uIGV0IENvbXDDqXRlbmNlczxzdXA+WzEwXSgvYXJ0aWNsZS9mb3JtYXRpb24tZGlzY2lwbGVzLWRhbnMtZXZhbmdpbGVzI25vdGVzKTwvc3VwPjpcblxuTcO7cmlyIGVuIGNhcmFjdMOocmUgZXQgZMOpdmVsb3BwZXIgdW5lIHBlcnNvbm5hbGl0w6kgcXVpIGNoZXJjaGUgw6AgcGxhaXJlIMOgIERpZXUgZXQgcXVpIGVzdCBlbiBhY2NvcmQgYXZlYyBsYSBzYWluZSBkb2N0cmluZS4gTcO7cmlyIGVuIGNvbnZpY3Rpb24gZGFucyBsZXVyIGNvbm5haXNzYW5jZSBkZSBEaWV1IGV0IGRhbnMgbGV1ciBjb21wcsOpaGVuc2lvbiBkZSBsYSBCaWJsZS4gTcO7cmlyIGVuIGNvbXDDqXRlbmNlIGRhbnMgbGEgcHJvY2xhbWF0aW9uIGRlIGxhIFBhcm9sZSBkZSBEaWV1IHBhciBkaXZlcnMgbW95ZW5zLCBtYWlzIHRvdWpvdXJzIGRhbnMgbGEgZMOpcGVuZGFuY2UgZGUgRGlldSBldCBsYSBwcmnDqHJlPHN1cD5bMTFdKC9hcnRpY2xlL2Zvcm1hdGlvbi1kaXNjaXBsZXMtZGFucy1ldmFuZ2lsZXMjbm90ZXMpPC9zdXA+LlxuXG4jIDMuIEzigJllbnZvaSBkZSBKw6lzdXNcblxuRGFucyBjaGFjdW4gZGVzIMOJdmFuZ2lsZXMsIErDqXN1cyBlbnZvaWUgc2VzIGRpc2NpcGxlcyBkYW5zIGxlIG1vbmRlIGFmaW4gcXXigJlpbHMgZmFzc2VudCDDoCBsZXVyIHRvdXIgZGVzIGRpc2NpcGxlcyBxdWkgbHVpIGFwcGFydGllbm5lbnQ8c3VwPlsxMl0oL2FydGljbGUvZm9ybWF0aW9uLWRpc2NpcGxlcy1kYW5zLWV2YW5naWxlcyNub3Rlcyk8L3N1cD4uIErDqXN1cyBsZSByZWZvcm11bGUgYXUgZMOpYnV0IGR1IGxpdnJlIGRlcyBBY3RlcyBkZXMgQXDDtHRyZXMgKEFjIDEuNy04KS5cblxuQ2hhcXVlIGV4cHJlc3Npb24gZGUgY2V0IGVudm9pIGNvbnRpZW50IHNlcyBwcm9wcmVzIGFjY2VudHMgYXUgc3VqZXQgZGVzIGRlcm5pw6hyZXMgZGlyZWN0aXZlcyBkb25uw6llcyBwYXIgQ2hyaXN0IMOgIHNlcyBkaXNjaXBsZXMuIENlbHVpIGRlIE1hdHRoaWV1IDI4LjE4LTIwIGVzdCBsZSBwbHVzIGTDqXZlbG9wcMOpIGV0IGFwcGVsw6kg4oCcbGUgbWFuZGF0IG1pc3Npb25uYWlyZeKAnS4gTm91cyBs4oCZw6l0dWRpb25zIMOgIGxhIGZpbiBkZSBub3RyZSBzdXJ2b2wgZGUgbGEgZm9ybWF0aW9uIGRlcyBkaXNjaXBsZXMgZGFucyBs4oCZw4l2YW5naWxlIHNlbG9uIE1hdHRoaWV1LlxuRGFucyBsYSBkZXV4acOobWUgcGFydGllIGR1IGRpcHR5cXVlIGx1Y2FuaWVuLCBsZSB0ZXJtZSBkaXNjaXBsZSBlc3QgZW5jb3JlIHV0aWxpc8OpIHBvdXIgY2V1eCBxdWkgc2UgY29udmVydGlzc2VudCBhcHLDqHMgbGEgUGVudGVjw7R0ZSAoQWMgNC4zMjsgNS4xNDsgNi4xKTxzdXA+WzEzXSgvYXJ0aWNsZS9mb3JtYXRpb24tZGlzY2lwbGVzLWRhbnMtZXZhbmdpbGVzI25vdGVzKTwvc3VwPi4gRW4gdXRpbGlzYW50IGNlIHRlcm1lIHBvdXIgZMOpc2lnbmVyIGNldXggcXVpIHNlIGNvbnZlcnRpc3NlbnQgw6AgQ2hyaXN0IGdyw6JjZSBhdSB0w6ltb2lnbmFnZSBkZSBzZXMgcHJlbWllcnMgZGlzY2lwbGVzLCBMdWMgdMOpbW9pZ25lIGTigJl1bmUgY29udGludWl0w6kgZGUgY29tcHLDqWhlbnNpb24gZGUgbGEgdmllIGNocsOpdGllbm5lIGVudHJlIGNldXggcXVpIG9udCBzdWl2aSBKw6lzdXMgcGVuZGFudCBzb24gbWluaXN0w6hyZSB0ZXJyZXN0cmUgZXQgY2V1eCBkZSBs4oCZw4lnbGlzZSBwb3N0w6lyaWV1cmUgw6AgbGEgcsOpc3VycmVjdGlvbjxzdXA+WzE0XSgvYXJ0aWNsZS9mb3JtYXRpb24tZGlzY2lwbGVzLWRhbnMtZXZhbmdpbGVzI25vdGVzKTwvc3VwPi5cblxuPGJyIGlkPVwibm90ZXNcIj5cblxuPHN1Yj4xLiBJYmlkLiwgcC4gMjkxLiAgPGJyPjIuIEpvaG4gSC4gT0FLLCAqVG91cyBkaXNjaXBsZXMqLCBFeGNlbHNpcywgQ2hhcm9scywgMjAwNiwgcC4gOTkuIDxicj4zLiBNaWNoYWVsIEouIFdpbGtpbnMsICpGb2xsb3dpbmcgdGhlIE1hc3RlciosIHAuIDEwOC4gPGJyPjQuIE1pY2hhZWwgSi4gV2lsa2lucywg4oCcRGlzY2lwbGUgZXQgZGlzY2lwdWxhdOKAnSBkYW5zICpEaWN0aW9ubmFpcmUgZGUgSsOpc3VzIGV0IGRlcyDDiXZhbmdpbGVzKiwgcC4gMjkzLiA8YnI+NS4gRGF2aWQgSi4gQm9zY2gsIEQqeW5hbWlxdWUgZGUgbGEgbWlzc2lvbiBjaHLDqXRpZW5uZSosIHAuIDkxLiA8YnI+Ni4gSGVucmkgQmxvY2hlciwgKkxhIGRvY3RyaW5lIGRlIGzigJnDiWdsaXNlIGV0IGRlcyBzYWNyZW1lbnRzKiwgVmF1eC1zdXItU2VpbmUsIMOJZGlmYWMsIDIwMjIsIHAuIDI4LiA8YnI+Ny4gTWljaGFlbCBKLiBXSUxLSU5TLCBvcC4gY2l0LiwgcC4yOTMuIDxicj44LiBJYmlkLiwgcC4gMjkzLiA8YnI+OS4gSWJpZC4sIHAuIDI5My4gPGJyPjEwLiBSb2JlcnQgZGUgVnJpZXMgZm9ybXVsZSBwbHVzIGNsYXNzaXF1ZW1lbnQgbGEgdHJhbnNtaXNzaW9uIGRlIGxhIGZvaTog4oCcTGEgZm9pIGNocsOpdGllbm5lIHNlIG1hbmlmZXN0ZSBkZSB0cm9pcyBtYW5pw6hyZXMgcXVlIGzigJlvbiBwZXV0IGTDqXNpZ25lciBwYXIgbGVzIHRlcm1lczogb3J0aG9kb3hpZSwgb3J0aG9wYXRoaWUgZXQgb3J0aG9wcmF4aWUu4oCdIChSb2JlcnQgRGUgVnJpZXMsICpUcmFuc21ldHRyZSBsYSBmb2kqLCBDaGFyb2xzLCDDiWQuIEV4Y2Vsc2lzLCAyMDAyLCBwLiAxNykuIDxicj4xMS4gQ29saW4gTWFyc2hhbGwsIFRvbnkgUGF5bmUsICpM4oCZZXNzZW50aWVsIGRhbnMgbOKAmcOJZ2xpc2UqLCBwLiA4My4gPGJyPjEyLiBNdCAyOC4xOTsgSm4gMjAuMjE7IExjIDI0LjQ2LTQ5LiBNYXJjIGVuIGNvbnRpZW50IMOpZ2FsZW1lbnQgdW4sIG1haXMgZGFucyBsYSBmaW4gbG9uZ3VlIHF1aSBlc3QgYWJzZW50ZSBkZSBub21icmV1eCBtYW51c2NyaXRzIChNYyAxNi4xNS0xNikuIDxicj4xMy4gROKAmWF1dHJlcyB0ZXJtZXMgdmllbmRyb250IGVucmljaGlyIGxlIHZvY2FidWxhaXJlLCBwYXIgZXhlbXBsZTogbGVzIFwiY2hyw6l0aWVuc1wiIChBYyAxMS4yNik7IGNldXggcXVpIHNvbnQgZGUgbGEgXCJ2b2llXCIgKEFjIDkuMik7IGxlcyBcInNhaW50c1wiIChBYyA5LjEzKS4gPGJyPjE0LiBNaWNoYWVsIEouIFdpbGtpbnMsICpGb2xsb3dpbmcgdGhlIE1hc3RlciosIHAuMjQ5Ljwvc3ViPiIsImF1dGhvciI6eyJmdWxsTmFtZSI6IlJhcGhhw6tsIENoYXJyaWVyIiwicGljdHVyZSI6eyJ1cmwiOiJodHRwczovLzljODUwNDAwLWJsdWUtdG91dHBvdXJzYWdsb2lyZS1jb20tc3RyYXBpLXVwbG9hZC5zMy5ncmEucGVyZi5jbG91ZC5vdmgubmV0L3JhcGhhZWxfY2hhcnJpZXJfMDNfY29waWVfMV9zY2FsZWRfZTE2NTc4MDEyNTc3ODdfZjkzNTk5NDAzOC5qcGciLCJwcm92aWRlciI6ImF3cy1zMy1lbmhhbmNlZCJ9fSwiaW1hZ2UiOnsidXJsIjoiaHR0cHM6Ly85Yzg1MDQwMC1ibHVlLXRvdXRwb3Vyc2FnbG9pcmUtY29tLXN0cmFwaS11cGxvYWQuczMuZ3JhLnBlcmYuY2xvdWQub3ZoLm5ldC9tb2hhbWFkX2JhYmF5YW5fNTBfQzNfSl9WZHVrYV9FX3Vuc3BsYXNoX2RiYTA1MjdjYWUuanBnIiwiaGVpZ2h0IjoxMjgwLCJ3aWR0aCI6MTkyMCwiYWx0ZXJuYXRpdmVUZXh0IjoiIiwicHJvdmlkZXIiOiJhd3MtczMtZW5oYW5jZWQifSwidG9waWNzIjpbeyJuYW1lIjoiRGlzY2lwdWxhdC9NZW50b3JhdCJ9XSwibW9kdWxlcyI6W3siX190eXBlbmFtZSI6IkNvbXBvbmVudE1vZHVsZUxlYWQiLCJjb250ZW50IjoiTWlja2FlbCBKLiBXaWxraW5zLCBs4oCZdW4gZGVzIHNww6ljaWFsaXN0ZXMgZHUgTm91dmVhdSBUZXN0YW1lbnQsIGlkZW50aWZpZSB0cm9pcyDDqXRhcGVzIGF1IGRpc2NpcHVsYXQgZGUgSsOpc3VzIHByw6lzZW50IGRhbnMgY2hhY3VuIGRlcyDDqXZhbmdpbGVzOiBhcHBlbGVyLCBmb3JtZXIgZXQgZW52b3llclvCuV0oL2FydGljbGUvZm9ybWF0aW9uLWRpc2NpcGxlcy1kYW5zLWV2YW5naWxlcyNub3RlcykuIERhbnMgY2V0IGFydGljbGUsIGplIHZvdXMgcHJvcG9zZSBkZSBmYWlyZSB1bmUgc3ludGjDqHNlIGRlIGNlcyB0cm9pcyBwb2ludHMuIn1dfV19fQo=", "status": 200, "url": "http://127.0.0.1:1337/graphql"}, "revalidate": 3600, "tags": []}