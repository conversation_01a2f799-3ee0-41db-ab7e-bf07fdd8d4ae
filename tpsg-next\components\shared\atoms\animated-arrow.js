import styled from "styled-components";

const AnimatedArrow = () => {
  return (
    <Wrapper>
      <Arrow>
        <div className={"background-dot"}/>
        <Subtract/>
        <div className={"color-dot"}/>
      </Arrow>
    </Wrapper>
  )
}

export default AnimatedArrow;

const Wrapper = styled.div`
  position: relative;
  width: 100%;
  height: 100%;

  &:hover {
    cursor: pointer;
    svg {
      transform: rotate(-45deg);
      width: 325%;
      height: 325%;
      left: -112%;
      bottom: -112%;
    }
    .background-dot {
      transform: translate3d(-66px, 66px, 0);
    }
    .color-dot {
      transform: translate3d(-16px, 16px, 0);
    }
  }
`;

const Arrow = styled.div`
  position: absolute;
  bottom: 0;
  left: 0;
  height: 200px;
  width: 200px;
  overflow: hidden;

  .background-dot {
    position: absolute;
    background-color: white;
    width: 66px;
    height: 66px;
    left: -33px;
    bottom: -33px;
    z-index: 10;
    border-radius: 100px;
    transition: all 450ms cubic-bezier(0.93, 0.03, 0.23, 0.84);
  }

  .color-dot {
    position: absolute;
    background-color: black;
    width: 16px;
    height: 16px;
    left: 0;
    bottom: 0;
    z-index: 10;
    border-radius: 16px;
    transition: all 450ms cubic-bezier(0.93, 0.03, 0.23, 0.84);
  }

  svg {
    position: absolute;
    left: -33px;
    bottom: -33px;
    z-index: 8;
    transition: all 450ms cubic-bezier(0.93, 0.03, 0.23, 0.84);
  }
`

const Subtract = () => {
  return (
    <svg width="66" height="66" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd"
        d="M33 66C51.2254 66 66 51.2254 66 33C66 14.7746 51.2254 0 33 0C14.7746 0 0 14.7746 0 33C0 51.2254 14.7746 66 33 66ZM40.6553 32.0546C40.7373 32.0546 40.7646 31.9152 40.6826 31.8595C36.1768 30.1316 31.7802 26.9546 31.7802 26.9546L34.4837 24.53C37.2964 28.5431 43.55 33.03 43.55 33.03C43.55 33.03 37.4057 37.3497 34.4837 41.53L31.7802 39.1054C31.7802 39.1054 36.286 35.9562 40.6826 34.2005C40.7646 34.1448 40.7373 34.0054 40.6553 34.0054H22.55V32.0546H40.6553Z"
        fill="white"/>
    </svg>
  )
}



