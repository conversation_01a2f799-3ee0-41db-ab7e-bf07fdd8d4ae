# This script provides instructions for reinstalling NVM to a path without spaces

Write-Host "=== How to Reinstall NVM to a Path Without Spaces ===" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Uninstall the current NVM installation:" -ForegroundColor Yellow
Write-Host "   - Go to Control Panel > Programs > Uninstall a program" -ForegroundColor White
Write-Host "   - Find and uninstall NVM for Windows" -ForegroundColor White
Write-Host ""
Write-Host "2. Download the latest NVM for Windows installer:" -ForegroundColor Yellow
Write-Host "   - Visit: https://github.com/coreybutler/nvm-windows/releases" -ForegroundColor White
Write-Host "   - Download the nvm-setup.exe file" -ForegroundColor White
Write-Host ""
Write-Host "3. Install NVM to a path WITHOUT spaces:" -ForegroundColor Yellow
Write-Host "   - Run the installer" -ForegroundColor White
Write-Host "   - When prompted for installation location, use a path WITHOUT spaces" -ForegroundColor White
Write-Host "   - Recommended path: C:\nvm" -ForegroundColor Green
Write-Host ""
Write-Host "4. After installation, open a NEW PowerShell window and run:" -ForegroundColor Yellow
Write-Host "   nvm list" -ForegroundColor White
Write-Host "   nvm install 14.21.3" -ForegroundColor White
Write-Host "   nvm use 14.21.3" -ForegroundColor White
Write-Host ""
Write-Host "This should resolve the issue with spaces in the path." -ForegroundColor Green
