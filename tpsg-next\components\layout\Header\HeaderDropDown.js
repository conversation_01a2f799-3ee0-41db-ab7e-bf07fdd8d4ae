import styled from "styled-components";
import { HeaderContext } from "context/HeaderContext";
import { useCoreData } from "context/CoreDataContext";
import { useContext } from "react";
import Link from "next/link";
import { dynamicSort } from "utils/list.utils";


export default function HeaderDropDown() {
  const { blogs, podcasts } = useCoreData()
  const { headerState } = useContext(HeaderContext);

  const items = {
    blogs: blogs.map(blog => ({ name: blog.blogger.fullName, slug: blog.slug, lastName: blog.blogger.lastName }) )
      .sort(dynamicSort("lastName")),
    podcasts: podcasts.sort(dynamicSort("name"))
  }

  if(!items.blogs || !items.podcasts)
    return null;

  return (
    <Wrapper
      show={headerState.dropDownOpen}
      blog={headerState.dropDownKey === "blogs"}
      podcast={headerState.dropDownKey === "podcasts"}
      podcastHeight={(items.podcasts.length * 34) + 80}
      blogHeight={(items.blogs.length * 34) + 80}>
      <ul className={"dd-blogs"}>
        { items.blogs.map((item, key) =>
          <li key={key}>
            <Link href={`/blog/${item.slug}`}>{item.name}</Link>
          </li>
        )}
      </ul>
      <ul className={"dd-podcasts"}>
        { items.podcasts.map((item, key) =>
          <li key={key}>
            <Link href={`/podcasts/${item.slug}`}>{item.name}</Link>
          </li>
        )}
      </ul>

    </Wrapper>
  )
}

const Wrapper = styled.div`
  position: relative;
  margin-top: -1px;
  width: 100%;
  height: ${p => p.show ? p.podcast ? p.podcastHeight : p.blogHeight : 0}px;
  //background: linear-gradient(56.8deg, #081921 18.37%, rgba(8, 25, 33, 0.8) 100.63%);
  background-color: var(--blue-dark);
  transition: all 350ms ease-out;
  overflow: hidden;
  z-index: 2;

  //backdrop-filter: blur(12px);
  //
  //-webkit-backface-visibility: hidden; // Safari shit here
  //-moz-backface-visibility: hidden; // Safari shit here
  //-webkit-transform: translate3d(0, 0, 0); // Safari shit here
  //-moz-transform: translate3d(0, 0, 0); // Safari shit here

  .dd-podcasts {
    margin: 0 0 0 calc(100% - (445px + var(--border-space)));
    &:before {
      top: ${p => p.show && p.podcast ? -10 : -24}px;
    }
    li {
      opacity: ${p => p.blog ? 0 : 1};
    }
  }

  .dd-blogs {
    margin: 0 0 0 calc(100% - (727px + var(--border-space)));
    &:before {
      top: ${p => p.show && p.blog ? -10 : -24}px;
    }
    li {
      opacity: ${p => p.blog ? 1 : 0};
    }
  }

  ul {
    position: absolute;
    padding: 28px 0;

    &:before {
      position: absolute;
      left: 5px;
      content: "";
      display: block;
      width: 20px;
      height: 20px;
      background-color: var(--soft-white);
      transform: rotate(45deg);
      transition: all 350ms ease-out;
    }
  }

  li {
    font-family: "Switzer", sans-serif;
    font-size: 16px;
    margin-top: 14px;
    color: #F9F1E6;
    list-style: none;
    transition: opacity 250ms ease-out;

    &:hover {
      color: var(--c-brand-lighter);
      cursor: pointer;
    }
  }

`;
