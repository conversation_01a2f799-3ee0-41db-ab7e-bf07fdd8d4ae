"use strict";(()=>{var e={};e.id=8813,e.ids=[8813,2888],e.modules={2565:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>q,default:()=>c,getServerSideProps:()=>m,getStaticPaths:()=>d,getStaticProps:()=>g,reportWebVitals:()=>x,routeModule:()=>y,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>_,unstable_getStaticParams:()=>h,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>S});var a=r(7093),l=r(5244),i=r(1323),o=r(779),u=r(4033),n=r(296),p=e([n]);n=(p.then?(await p)():p)[0];let c=(0,i.l)(n,"default"),g=(0,i.l)(n,"getStaticProps"),d=(0,i.l)(n,"getStaticPaths"),m=(0,i.l)(n,"getServerSideProps"),q=(0,i.l)(n,"config"),x=(0,i.l)(n,"reportWebVitals"),S=(0,i.l)(n,"unstable_getStaticProps"),P=(0,i.l)(n,"unstable_getStaticPaths"),h=(0,i.l)(n,"unstable_getStaticParams"),b=(0,i.l)(n,"unstable_getServerProps"),_=(0,i.l)(n,"unstable_getServerSideProps"),y=new a.PagesRouteModule({definition:{kind:l.x.PAGES,page:"/article/[article]",pathname:"/article/[article]",bundlePath:"",filename:""},components:{App:u.default,Document:o.default},userland:n});s()}catch(e){s(e)}})},296:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>Article,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var a=r(997),l=r(9114),i=r(1385),o=r(7672),u=r(635),n=r(3123),p=r(1077),c=r(6303),g=e([c]);c=(g.then?(await g)():g)[0];let d="true"===process.env.LIGHT_BUILD?10:9999;function Article({post:e,relatedPosts:t,newsletter:r}){if(!e)return null;let s=e.topics.map(e=>e.name);return(0,a.jsxs)(a.Fragment,{children:[a.jsx(c.Bv,{post:e,newsletter:r,preview:!1}),a.jsx(n.Z,{items:t,baseTopicNames:s})]})}async function getStaticProps({params:e}){let t=await i.Z.query({query:q,variables:{slug:e.article}}).then(e=>e.data.posts[0]);if(!t)return{notFound:!0};let r=await i.Z.query({query:o.o$.QUERY_RELATED,variables:{id:t.id}}).then(e=>e.data.relatedPosts),s=null;if(t.author){let e=await i.Z.query({query:x,variables:{slug:t.author.slug}}).then(e=>e.data.blogs);e.length&&(s=e[0].newsletter)}return{props:{post:{...t,modules:(0,u.fw)(t.modules),route:(0,p.qt)(t)},relatedPosts:r||null,newsletter:s},revalidate:10}}async function getStaticPaths(){let{data:e,error:t}=await i.Z.query({query:m,variables:{limit:d}});return t&&console.log(t),{paths:e.posts.map(e=>({params:{article:e.slug}})),fallback:!0}}let m=l.gql`
    query ArticleSlugs($limit: Int!) {
        posts(where: { type: "article" }, limit: $limit) {
            slug
        }
    }
`,q=l.gql`
    ${o.Pq.CORE_POST_FIELDS}
    query Article($slug: String!) {
        posts(where: { slug: $slug }) {
            ...CorePostFields
        }
    }
`,x=l.gql`
    query Blog($slug: String!){
        blogs(where: {slug: $slug}){
            id
            slug
            newsletter
        }
    }
`;s()}catch(e){s(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},6158:e=>{e.exports=require("react-share")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,9755,5152,4033,779,7113,6453,1077,7620,8672,2832,8657,4004,7482,3462,3646,6303],()=>__webpack_exec__(2565));module.exports=r})();