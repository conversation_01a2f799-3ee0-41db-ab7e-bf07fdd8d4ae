import styled from "styled-components";
import Link from "next/link";
import { useContext } from "react";
import { HeaderContext } from "context/HeaderContext";
import { device } from "styles/device";
import { useCoreData } from "context/CoreDataContext";
import Blogs from "./Blogs";
import Vocations from "./Vocations";
import AnimatedTextButton from "../../../shared/Buttons/AnimatedTextButton";

const mainNavigationItems = [
  { name: "Articles", route: "/recherche" },
  { name: "Thèmes", route: "/categories" },
  { name: "Formations", route: "/formations" },
  { name: "Parcours e-mails", route: "/parcours-emails" },
  { name: "Podcasts", route: "/podcasts" },
  { name: "Webinaires", route: "/webinaires" },
]

const setChildren = (children) => {
  return children.map(child => ({
    route: `/categories/${child.type}/${child.slug}`,
    name: child.name
  }))
}

export default function HeaderMenu() {

  const { headerState, toggleMenu } = useContext(HeaderContext);
  const { blogs, topicGroups } = useCoreData()

  const vocationsData = topicGroups
    .filter(voc => voc.children?.length > 0)
    .map(voc => ({
      route: `/categories/${voc.type}/${voc.slug}`,
      name: voc.name,
      children: setChildren(voc.children),
    }) );

  const blogsData = blogs
    .map(blog => ({
      route: `/blog/${blog.slug}`,
      name: blog.blogger.fullName,
      lastName: blog.blogger.lastName,
    }))

  return (
    <Main isOpen={headerState.showMenu}>

      <div className={"dark-filter"}/>

      <LeftSection isOpen={headerState.showMenu}>
        <LeftContent>
          <ul>
            { mainNavigationItems.map((item, key) =>
              <Link href={item.route} key={key}>
                <LeftItem onClick={() => toggleMenu()}>{item.name}</LeftItem>
              </Link>
            )}
          </ul>
        </LeftContent>

        <div className={"menu-buttons"}>
          <AnimatedTextButton
            theme={"light"}
            text={"Nous soutenir"}
            link={"/soutenir"}
            onClickFunction={() => toggleMenu()}
          />
          <AnimatedTextButton
            theme={"light"}
            text={"À propos"}
            link={"/a-propos"}
            onClickFunction={() => toggleMenu()}/>
        </div>
      </LeftSection>

      <RightSection isOpen={headerState.showMenu}>
        <Vocations title={"vocations"} data={vocationsData}/>
        <Blogs title={"blogs"} data={blogsData}/>
      </RightSection>

    </Main>
  )
}

const Main = styled.div`
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  visibility: ${p => p.isOpen ? "visible" : "hidden"};
  transition-delay: ${p => p.isOpen ? "0ms" : "600ms"};
  z-index: 2000;


  // Hide Scrollbar
  &::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }

  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  overflow-y: scroll;

  .dark-filter {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: orangered;
    opacity: ${p => p.isOpen ? "1" : "0"};
    transition: opacity 800ms;
  }
`;

const LeftSection = styled.div`
  position: relative;
  background-color: var(--blue-dark);
  width: 100vw;
  height: 100vh;
  padding: 126px var(--border-space) 64px 0;
  clip-path: inset(0 0 ${p => p.isOpen ? "0%" : "100%"} 0);
  transition: clip-path 600ms cubic-bezier(0.93, 0.03, 0.23, 0.84);

  @media ${device.desktop} {
    position: absolute;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-auto-rows: max-content;
    column-gap: 24px;
    padding: 126px 0;
    height: 100vh;
    width: 50%;
    left: 0;
    top: 0;
    clip-path: inset(0 ${p => p.isOpen ? "0%" : "100%"} 0 0);

    .menu-buttons {
      grid-column: 1/4;
      margin-top: 64px;
    }
  }

  .menu-buttons {
    position: relative;
    display: flex;
    flex-direction: row-reverse;
    gap: 16px;
  }
`;

const LeftContent = styled.div`
  position: relative;
  grid-column: 2/4;
  height: 100%;
  ul {
    padding: 0;
  }
`;

const LeftItem = styled.li`
  position: relative;
  width: 100%;
  text-align: right;
  list-style: none;
  font-size: 32px;
  margin-top: 20px;
  font-weight: 400;
  color: var(--c-cream);
  white-space: nowrap;

  @media ${device.tablet} {
    font-size: 40px;
    margin-bottom: 16px;
    &:hover {
      color: var(--c-brand-lighter);
      cursor: pointer;
    }
  }
`

const RightSection = styled.div`
  position: relative;
  width: 100%;
  background-color: var(--blue-dark);
  padding: 64px var(--border-space);
  clip-path: inset(${p => p.isOpen ? "0%" : "100%"} 0 0 0);
  transition: clip-path 600ms cubic-bezier(0.93, 0.03, 0.23, 0.84);

  @media ${device.desktop} {
    position: absolute;
    right: 0;
    top: 0;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 126px var(--border-space) 126px 0;
    height: 100vh;
    width: 51%;
    overflow-y: scroll;
    clip-path: inset(0 0 0 ${p => p.isOpen ? "0%" : "100%"});
  }
`;
