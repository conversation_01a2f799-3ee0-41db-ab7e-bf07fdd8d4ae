import styled from "styled-components";
import LargeCard from "../shared/Card/LargeCard";
import { modulesAsObj } from "utils/components.utils";
import { dateForHumans } from "utils/date.utils";
import SectionHeader from "../shared/section/SectionHeader";
import { device } from "styles/device";
import AnimatedTextButton from "../shared/Buttons/AnimatedTextButton";
import { getPostRoute } from "utils/posts.utils";


export default function SectionDouble({ webinars, formations }) {

  // Pour le moment on part du principe que l'on affichera qu'un
  // webinaire et une formation.
  if (webinars.posts.length < 1 || formations.posts.length < 1) return <></>

  function preparedPost(post){
    const { lead }  = modulesAsObj(post.modules)
    return {
      title: post.title,
      link: getPostRoute(post),
      author: post.author.fullName,
      image: post.image,
      date: dateForHumans(post["published_at"]),
      lead: lead?.content || ""
    }
  }

  return(
    <Wrapper>
      <div className={"section-item"}>
        <SpaceM/>
        <SectionHeader title={"Webinaire"} supTitle={"À voir ou à revoir"} light/>
        <CardWrapper>
          <LargeCard post={preparedPost(webinars.posts[0])} theme={"dark"}/>
        </CardWrapper>
        <AnimatedTextButton text={"Voir tout"} theme={"light"} link={"/webinaires"}/>
      </div>
      <div className={"section-item"}>
        <SpaceM/>
        <SectionHeader title={"Formation"} supTitle={"Pour aller plus loin"} light/>
        <CardWrapper>
          <LargeCard post={preparedPost(formations.posts[0])} theme={"dark"}/>
        </CardWrapper>
        <AnimatedTextButton text={"Voir tout"} theme={"light"} link={"/formations"}/>
        <S80/>
      </div>
    </Wrapper>
  );
};


const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 0 var(--border-space);
  background-color: var(--blue-dark);
  .animated-text-button {
    margin-top: 24px;
  }
  
  @media ${device.tablet} {
    gap: 16px;
    flex-direction: row;
    .section-item {
      width: 50%;
    }
  }
  
  @media ${ device.desktop } {
    gap: 24px;
  }
`;

const CardWrapper = styled.div`
  @media ${ device.desktop } {
    margin: 0;
  }
`;

const S80 = styled.div`
  height: 80px;
`;

const SpaceM = styled.div`
  height: 72px;
  @media ${device.desktop} {
    height: 96px;
  }
`;
