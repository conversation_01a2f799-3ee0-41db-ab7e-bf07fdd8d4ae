import ar from './ar.json';
import cs from './cs.json';
import de from './de.json';
import en from './en.json';
import es from './es.json';
import fr from './fr.json';
import id from './id.json';
import it from './it.json';
import ko from './ko.json';
import ms from './ms.json';
import nl from './nl.json';
import pl from './pl.json';
import ptBR from './pt-BR.json';
import pt from './pt.json';
import ru from './ru.json';
import th from './th.json';
import tr from './tr.json';
import uk from './uk.json';
import vi from './vi.json';
import zhHans from './zh-Hans.json';
import zh from './zh.json';
import sk from './sk.json';

const trads = {
  ar,
  cs,
  de,
  en,
  es,
  fr,
  id,
  it,
  ko,
  ms,
  nl,
  pl,
  'pt-BR': ptBR,
  pt,
  ru,
  th,
  tr,
  uk,
  vi,
  'zh-Hans': zhHans,
  zh,
  sk,
};

export default trads;
