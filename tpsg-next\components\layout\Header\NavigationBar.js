import styled from "styled-components";
import Logo from "./Logo";
import { useContext, useEffect, useRef } from "react";
import { HeaderContext } from "context/HeaderContext";
import MenuButtons from "./MenuButtons";
import { device } from "styles/device";
import Link from "next/link";

export default function NavigationBar({ invert }) {

  const ddRef = useRef(null);

  const { headerState, onDropDownButtonClick, onDropDownClickOutside } = useContext(HeaderContext);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (ddRef.current && !ddRef.current.contains(event.target)) {
        if(headerState.dropDownOpen) {
          onDropDownClickOutside();
          event.stopPropagation();
        }
      }
    };

    document &&
    document.addEventListener("click", handleClickOutside, true);

    return () => {
      document &&
      document.removeEventListener("click", handleClickOutside, true);
    };
  }, [headerState]);

  return (
    <Wrapper menuOpen={headerState.showMenu} invert={invert}>
      <hr className={"animated-buttons-line"}/>
      <Logo white={invert}/>
      <RightNav invert={invert} ref={ddRef}>
        <ul>
          <li onClick={() => onDropDownButtonClick("blogs")}>Blogs</li>
          <li onClick={() => onDropDownClickOutside() }><Link href={"/categories"}>Thèmes</Link></li>
          <li onClick={() => onDropDownClickOutside() }><Link href={"/formations"}>Formations</Link></li>
          <li onClick={() => onDropDownButtonClick("podcasts")}>Podcasts</li>
          <li onClick={() => onDropDownClickOutside() }><Link href={"/webinaires"}>Webinaires</Link></li>
          <li onClick={() => onDropDownClickOutside() } className={"highlight-top-menu"}><Link href={"/soutenir"}>Soutenir</Link></li>
        </ul>
        <div className={"nav-v-separator"}/>
        <MenuButtons invert={invert}/>
      </RightNav>

      <div className={"animated-background"}/>

    </Wrapper>
  )
}


const Wrapper = styled.div`
  display: flex;
  width: 100%;
  height: 80px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0,0,0,0.20);

  .animated-buttons-line {
    position: absolute;
    right: 0;
    top: 70px;
    z-index: 2100;
    border: ${p => p.menuOpen ? "1px solid #1C2E33" : "1px solid transparent"};
    width: 100%;
    transition: all 600ms ease-in-out;

    @media ${ device.desktop } {
      right: 15px;
      width: ${p => p.menuOpen ? "calc(var(--border-space) + 98px)" : "0"};
    }
  }
  
  .animated-background {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: var(--blue-dark);
    transform: ${p => p.menuOpen ? "translateY(0)" : "translateY(-100%)"};
    transition: transform 450ms ease-in-out;
    z-index: 2100;

    @media ${ device.desktop } {
      display: none;
    }
  }
  

`;

const RightNav = styled.div`

  position: relative;
  height: 100%;
  display: flex;
  align-items: center;

  font-family: "Switzer", "Helvetica Neue", Helvetica, sans-serif;
  font-size: 16px;
  font-weight: 500;

  ul li {
    list-style: none;
    display: none;
    @media ${ device.desktop } {
      display: inline;
    }
  }
  li {
    margin-left: 32px;
    cursor: pointer;
    color: ${p => p.invert ? "var(--soft-white)" : "black"};
  }
  
  .highlight-top-menu {
    color: ${p => p.invert ? "var(--soft-white)" : "var(--brand-color)"};
  }
  
  .nav-v-separator {
    height: 100%;
    margin: 0 0 0 32px;
    width: 0;
    border-left: 1px solid rgba(0,0,0,0.2);
  }
  
`;
