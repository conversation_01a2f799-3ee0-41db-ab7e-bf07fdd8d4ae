import styled from "styled-components";
import { dateForHumans } from "utils/date.utils";
import CondImage from "components/shared/condimage";
import { device } from "styles/device";
import { AnimatedIcon } from "components/shared/atoms";
import { getPostRoute } from "utils/posts.utils";
import Link from "next/link";

/**
 * @param post
 * @param options
 * @return {JSX.Element}
 *
 * options possibles:
 * showDate[boolean]
 * showAuthor[boolean]
 * showLead[boolean]
 * dotColors({front[string], back[string]})
 */
export default function FirstArticleCard({ post, options }) {
  const date = options?.showDate ? dateForHumans(post.date) : null;
  const author = options?.showAuthor ? post.author?.fullName : null;
  const route = getPostRoute(post);

  return (
    <CardWrapper className={"fa-card"}>
      <Link href={route}>
        {date && <p className={"fa-card-date"}>{dateForHumans(post.date)}</p>}
        <div className={"fa-card-image"}>
          <CondImage imageData={post.image} priority={true} />
          <AnimatedIcon type={post.type} colors={options?.dotColors} />
        </div>
        {author && <p className={"fa-card-author"}>{author}</p>}
        <h3 className={"fa-card-title primary-hover"}>{post.title}</h3>
      </Link>
    </CardWrapper>
  );
}

const CardWrapper = styled.div`
  width: 100%;
  grid-area: "FirstArticle";
  //overflow: auto;
  //grid-column: 1/3;

  .fa-card-date {
    font-family: "Novela", "Lora", sans-serif;
    font-size: 14px;
    color: #888888;
    margin-bottom: 8px;
    margin-top: 0;
    font-weight: 400;
  }

  .fa-card-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16/10;
  }

  .fa-card-title {
    width: 95%;
    font-size: 30px;
    font-weight: 600;
    margin-top: 18px;
    line-height: 110%;
    color: #262626;
    cursor: pointer;
  }

  .fa-card-author {
    font-size: 18px;
    font-family: "Lora", serif;
    font-style: italic;
    color: #888888;
    margin-top: 24px;
    margin-bottom: 0;
  }

  @media ${device.tablet} {
    //grid-column: 2/4;

    .fa-card-image {
    }

    .fa-card-title {
      font-size: 48px;
      font-weight: 500;
      line-height: 105%;
      margin-top: 32px;
    }
    .fa-card-date {
      font-size: 18px;
    }
  }
  @media ${device.desktop} {
    .fa-card-image {
    }
  }
`;
