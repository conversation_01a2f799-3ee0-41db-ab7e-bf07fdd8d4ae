/**
 * Concatène le nom de chaque topics pour pouvoirs sortir une string qui ira dans le filter de meilisearch
 * @param {Array<Object>} topics 
 * @returns string
 */
function FilterTopicsString(topics) {
  let filterString = "";
  topics.map((topic, index) => {
    if (index !== 0) {
      filterString += " OR ";
    }
    filterString += `topics="${topic.name}"`;
  });
  return filterString;
};

export {
  FilterTopicsString
}
