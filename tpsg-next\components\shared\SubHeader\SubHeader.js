import styled from "styled-components";
import { device } from "styles/device";
import { Authors, LinkButton, Social, Text } from "./Items";


/**
 * Bandeau affiché sous le titre et l'image des pages webinaires et podcasts
 * @param children
 * @return {JSX.Element}
 * @constructor
 */
const SubHeader = ({ children }) => {
  return (
    <Wrapper className={"subheader"}>
      {children}
    </Wrapper>
  )
}

SubHeader.Authors = Authors;
SubHeader.Text = Text;
SubHeader.Social = Social;
SubHeader.LinkButton = LinkButton;

export default SubHeader;


const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;

  width: 100%;

  padding-top: 16px;
  padding-bottom: 16px;
  border-top: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;

  @media ${device.tablet} {
    padding-top: 24px;
    padding-bottom: 24px;
  }
  .subheader-item {
    padding-left: 16px;
    margin-right: 48px;
    border-left: 1px solid #dddddd;
  }
  .subheader-item:first-child {
    padding-left: 0;
    border-left: 0;
  }
  .subheader-item:last-child {
    margin-right: 0;
  }

`;
