{"kind": "collectionType", "collectionName": "pages", "info": {"name": "page", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": true}, "pluginOptions": {}, "attributes": {"blocks": {"type": "dynamiczone", "components": ["block.text"]}, "title": {"type": "string"}, "slug": {"type": "uid", "targetField": "title", "required": true}, "cover": {"model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}}, "metas": {"type": "component", "repeatable": false, "component": "module.seo"}}}