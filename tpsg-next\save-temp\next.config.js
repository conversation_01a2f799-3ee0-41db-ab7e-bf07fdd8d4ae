module.exports = {
  reactStrictMode: true,
  experimental: {
    // ssr and displayName are configured by default
    // removeConsole: true,
    scrollRestoration: true,
  },
  compiler: {
    // Enables the styled-components SWC transform
    styledComponents: true,
  },
  images: {
    domains: process.env.NEXT_IMAGES_DOMAINS ? process.env.NEXT_IMAGES_DOMAINS.split(',') : ["localhost"],
  },
  // webpack5: true,
  webpack: (config) => {
    config.resolve.fallback = { fs: false };
    return config;
  },
}
