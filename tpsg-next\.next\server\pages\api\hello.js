"use strict";(()=>{var e={};e.id=2453,e.ids=[2453],e.modules={145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5098:(e,a,t)=>{t.r(a),t.d(a,{config:()=>l,default:()=>o,routeModule:()=>u});var n={};t.r(n),t.d(n,{default:()=>handler});var r=t(1802),i=t(7153),d=t(6249);function handler(e,a){a.status(200).json({name:"<PERSON>",datetime:new Date(Date.now()).toString()})}let o=(0,d.l)(n,"default"),l=(0,d.l)(n,"config"),u=new r.PagesAPIRouteModule({definition:{kind:i.x.PAGES_API,page:"/api/hello",pathname:"/api/hello",bundlePath:"",filename:""},userland:n})}};var a=require("../../webpack-api-runtime.js");a.C(e);var __webpack_exec__=e=>a(a.s=e),t=a.X(0,[4222],()=>__webpack_exec__(5098));module.exports=t})();