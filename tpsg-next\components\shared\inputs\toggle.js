import styled from "styled-components";
import { useState } from "react";

export default function Toggle({ isChecked, formKey, handleValueChange, label }){

  const [checked, setChecked] = useState(isChecked);

  function handleClick(){
    handleValueChange(formKey, !checked);
    setChecked(!checked);
  }

  return(
    <StlToogle checked={checked}>
      <div className={"toggle-button"} onClick={() => handleClick()}>
        <div className={"inner-dot"}/>
      </div>
      <p className={"toggle-label"}>{label}</p>
    </StlToogle>
  )
}

const StlToogle = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;

  .toggle-button {
    height: 22px;
    width: 38px;
    border: 1.5px solid var(--c-dark-green);
    background-color: ${p => p.checked ? "var(--c-dark-green)" : "transparent"};
    border-radius: 21px;
    cursor: pointer;
  }
  
  .inner-dot {
    height: 15px;
    width: 15px;
    background-color: ${p => p.checked ? "white" : "var(--c-dark-green)"};
    border-radius: 15px;
    margin-top: 2px;
    margin-left: 2px;
    transform: translateX(${p => p.checked ? "100%" : "0%"});
    transition: all 350ms;
  }
  
  .toggle-label {
    margin: 0;
    padding-top: 4px;
    font-size: 16px;
    line-height: 21px;
    font-weight: 500;
    letter-spacing: 0.4px;
  }
`;
