"use strict";exports.id=7367,exports.ids=[7367],exports.modules={6652:(e,t,i)=>{i.d(t,{Z:()=>GridCard});var o=i(997);i(1664);var r=i(2558),a=i(7518),n=i.n(a),s=i(6453),l=i(2892),d=i(2570);function GridCard({post:e}){return e?.link?(0,o.jsxs)(m,{children:[(0,o.jsxs)(d.Z,{link:e.link,children:[e?.image&&o.jsx(x,{children:o.jsx("div",{className:"post-image-container",children:o.jsx(r.Z,{imageData:e.image})})}),(0,o.jsxs)("div",{children:[o.jsx(s.My,{className:0===e.details.length?"post-card-details-space":"",children:e.details}),o.jsx(s.kz,{children:e.title}),o.jsx(s.X0,{children:e.lead?e.lead:""})]})]}),e.youtubeEmbed&&o.jsx(l.Z,{style:c,youtubeEmbed:e.youtubeEmbed,children:o.jsx(p,{children:"Voir l'aper\xe7u"})})]}):null}let c={position:"absolute",top:"0",right:"0"},p=n().div.withConfig({componentId:"sc-5a8dd6d4-0"})`
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 18px;
  background-color: #161616;
  color: #f4f4f4;
  padding: 12px 24px 6px 24px;
  white-space: nowrap;
  cursor: pointer;
  box-shadow: 0 4px 13px rgba(0, 0, 0, 0.57);
`,m=n().div.withConfig({componentId:"sc-5a8dd6d4-1"})`
  position: relative;
  margin-bottom: 48px;
  .post-card-details-space{
    margin-bottom: 20px;
  }
`,x=n().div.withConfig({componentId:"sc-5a8dd6d4-2"})`
  position: relative;
  display: block;
  height: auto;
  width: 100%;
  aspect-ratio: 16/10;
  background-color: gray;
  overflow: hidden;
  margin-bottom: 10px;
  .post-image-container{
    width: 100%;
    height: 100%;
    position: relative;
  }
`},2892:(e,t,i)=>{i.d(t,{Z:()=>Preview});var o=i(997),r=i(7518),a=i.n(r),n=i(6689),s=i(7199);function YoutubeEmbed({video:e,autoplay:t}){let i=(0,s.gc)(e),r=`?&autoplay=${t?1:0}&mute=1&enablejsapi=1`;return o.jsx("iframe",{src:"https://www.youtube.com/embed/"+i+r,width:"100%",height:"100%",frameBorder:"0",allow:"fullscreen"})}function Preview({youtubeEmbed:e,style:t,children:i}){let[r,a]=(0,n.useState)(!1);return(0,o.jsxs)(l,{style:t,onClick:()=>a(!r),children:[r&&(0,o.jsxs)("div",{className:"fullscreen-preview",children:[o.jsx("div",{className:"video-player",children:o.jsx(YoutubeEmbed,{autoplay:!0,video:e})}),o.jsx("div",{className:"close-button",onClick:()=>a(!1),children:o.jsx("p",{children:"X"})})]}),o.jsx("div",{onClick:()=>a(!0),children:i})]})}let l=a().div.withConfig({componentId:"sc-281e56b5-0"})`

  .fullscreen-preview {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 4000;
    background-color: rgba(0, 0, 0, 0.9);
    animation-duration: 600ms;
    animation-name: showIn;
  }

  @keyframes showIn {
    from {
      background-color: transparent;
    }
    to {
      background-color: rgba(0, 0, 0, 0.9);
    }
  }

  .video-player {
    position: relative;
    width: 60%;
    aspect-ratio: 16/10;
  }

  .close-button {
    position: absolute;
    border: 1px solid white;
    border-radius: 48px;
    top: 48px;
    right: var(--border-space);
    height: 32px;
    width: 64px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    p {
      color: white;
      transform: scaleX(1.5);
      font-weight: 400;
      font-size: 18px;
      line-height: 52px;
      margin-top: 8px;
      margin-bottom: 0;
    }
  }
`},9658:(e,t,i)=>{i.d(t,{Z:()=>GridCardSection});var o=i(997),r=i(7518),a=i.n(r),n=i(7467);function GridCardSection({nameSection:e,children:t}){return(0,o.jsxs)(s,{id:e.normalize("NFD").replace(/[\u0300-\u036f]/g,"").toLowerCase().replace(/\s+/g,"-").replace(/[^\w-]/g,""),children:[o.jsx("h2",{className:"page-section-title",children:e}),o.jsx("div",{className:"all-post-card",children:t})]})}let s=a().section.withConfig({componentId:"sc-3dc537d8-0"})`
  margin-top: 96px;
  width: 100%;
  border-top: 1px solid black;

  .page-section-title {
    margin: 16px 0px 12px 0px;
    font-size: 28px;
  }
  .all-post-card {
    display: grid;
    grid-template-columns: 1fr;
    column-gap: 16px;
  }

  &:last-child {
    margin-bottom: 96px;
  }

  @media ${n.U.tablet} {
    .page-section-title {
      margin: 32px 0px;
      font-size: 46px;
    }
    .all-post-card {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media ${n.U.desktop} {
    .all-post-card {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media ${n.U.desktopXL} {
    .all-post-card {
      grid-template-columns: repeat(4, 1fr);
    }
  }
`},5432:(e,t,i)=>{function getDotColor(e){let t={formation:{front:"#262424",back:"#FFFFFF"},emailJourney:{front:"#AA2DD6",back:"#FFFFFF"},article:{front:"#000000",back:"#FFFFFF"},default:{front:"#000000",back:"#FFFFFF"}};return t[e]?t[e]:t.default}function hexToRgb(e){let t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null}i.d(t,{Q:()=>getDotColor,o:()=>hexToRgb})}};