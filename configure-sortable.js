/**
 * Script pour configurer les attributs triables de Meilisearch
 * 
 * Ce script configure l'attribut 'date' comme étant triable dans Meilisearch
 * 
 * Pour exécuter ce script :
 * 1. Assurez-vous que Meilisearch est en cours d'exécution
 * 2. Exécutez la commande : node configure-sortable.js
 */

const http = require('http');

const options = {
  hostname: '127.0.0.1',
  port: 7700,
  path: '/indexes/post/settings/sortable-attributes',
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json'
  }
};

const data = JSON.stringify(['date']);

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  
  res.on('data', (chunk) => {
    console.log(`Response: ${chunk}`);
  });
  
  res.on('end', () => {
    console.log('Configuration terminée');
    
    // Vérifier la configuration
    const checkOptions = {
      hostname: '127.0.0.1',
      port: 7700,
      path: '/indexes/post/settings/sortable-attributes',
      method: 'GET'
    };
    
    const checkReq = http.request(checkOptions, (checkRes) => {
      console.log(`Vérification - Status: ${checkRes.statusCode}`);
      
      let data = '';
      
      checkRes.on('data', (chunk) => {
        data += chunk;
      });
      
      checkRes.on('end', () => {
        console.log(`Attributs triables: ${data}`);
      });
    });
    
    checkReq.on('error', (error) => {
      console.error(`Erreur lors de la vérification: ${error}`);
    });
    
    checkReq.end();
  });
});

req.on('error', (error) => {
  console.error(`Erreur lors de la configuration: ${error}`);
});

req.write(data);
req.end();

console.log('Envoi de la requête...');
