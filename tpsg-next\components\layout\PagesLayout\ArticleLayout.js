import { NextSeo } from "next-seo";
import RoundedLabel from "components/shared/atoms/rounded-label";
import CondImage from "components/shared/condimage";
import { dateForHumans } from "utils/date.utils";
import ReactMarkdown from "react-markdown";
import rehypeRaw from "rehype-raw";
import SocialMedia from "components/shared/atoms/SocialMedia";
import MDPost from "components/shared/post/md-body";
import DynamicForm from "components/shared/ConvertkitForm/DynamicForm";
import AuthorBox from "components/shared/post/author-box";
import styled from "styled-components";
import { device } from "styles/device";
import { withRealSrc } from "utils/image-utils";
import PreviewButton from "../../Preview/PreviewButton";

export default function ArticleLayout({ post, preview, newsletter }) {
  const { modules, route } = post;

  const authorLink = post.blog
    ? `/blog/${post.blog.blogger.slug}`
    : `/recherche?author=${post.author.fullName}`;

  return (
    <PageWrapper>
      <NextSeo
        title={modules.seo?.metaTitle || post.title}
        description={modules.seo?.metaDescription || null}
        openGraph={{
          title: modules.seo?.metaTitle || post.title,
          description: modules.seo?.metaDescription || null,
          url: `https://toutpoursagloire.com${route}`,
          type: "article",
          article: {
            publishedTime: post.published_at,
            tags: post.topics.map((tag) => tag.name),
          },
          images: [
            {
              url: withRealSrc(post.image),
              alt: post.image?.alternativeText || "",
            },
          ],
        }}
        twitter={{
          site: "@t_p_s_g",
          cardType: "summary_large_image",
        }}
      />

      {preview && <PreviewButton url={route} />}

      <ArticleWrapper>
        <div className={"article-head-text"}>
          <h1 className={"article-title"}>{post.title}</h1>
          {post.topics &&
            post.topics.map((x, key) => (
              <RoundedLabel key={key} text={x.name} />
            ))}
          {post.readingTime ? (
            <div className={"reading-time"}>{post.readingTime} min de lecture</div>
          ) : null}
        </div>

        <div className={"article-image"}>
          <div className={"image-wrapper"}>
            <CondImage imageData={post.image} />
          </div>
        </div>

        <ArticleInfos>
          <a href={authorLink}>
            <div className={"article-author-picture"}>
              <CondImage imageData={post.author?.picture} />
            </div>
          </a>
          <div>
            <p className={"article-author-name"}>
              <a href={authorLink}>{post.author?.fullName || ""}</a>
            </p>
            <p className={"article-date"}>{dateForHumans(post.published_at)}</p>
          </div>
          {/*<p className={"article-rt"}><span>{readingTime(post.body)}</span> min de lecture</p>*/}
        </ArticleInfos>

        <ArticleLead>
          <ReactMarkdown rehypePlugins={[rehypeRaw]}>
            {modules.lead?.content || ""}
          </ReactMarkdown>
        </ArticleLead>

        <SocialMedia
          url={`https://toutpoursagloire.com/article/${post.slug}`}
        />
        <MDPost content={post.body} />
      </ArticleWrapper>

      <ArticleBottom>
        <FormBox>
          <DynamicForm
            formString={newsletter}
            title={"Ma newsletter"}
            desc={""}
          />
        </FormBox>
        <AuthorBox author={post.author} blog={post.blog}/>
      </ArticleBottom>
    </PageWrapper>
  );
}

const PageWrapper = styled.div`
  width: 100vw;
  padding: 0 var(--border-space);

  .preview-warning {
    position: fixed;
    bottom: 16px;
    left: 16px;
    margin: 0;
    display: inline-block;
    font-family: Switzer, sans-serif;
    font-size: 18px;
    padding: 10px 40px;
    border-radius: 100px;
    color: var(--soft-white);
    background-color: var(--brand-color);
    z-index: 9999;
    opacity: 0.86;
  }
`;

const ArticleBottom = styled.div`
  display: flex;
  flex-direction: column-reverse;
  margin-bottom: 128px;
  margin-top: 128px;

  .author-box {
    margin-bottom: 40px;
  }

  @media ${device.desktop} {
    flex-direction: row;
    margin-bottom: 126px;
    .author-box {
      padding-left: 40px;
    }
  }
`;

const FormBox = styled.div`
  border-top: 1px solid rgba(0, 0, 0, 0.4);
  padding-right: 40px;
  padding-top: 32px;
  @media ${device.desktop} {
    min-width: 40%;
    border-right: 1px solid rgba(0, 0, 0, 0.4);
  }
`;

const ArticleWrapper = styled.div`
  .card-label {
    display: none;
    margin-right: 8px;
  }

  .article-title {
    font-size: 38px;
    font-weight: 500;
    line-height: 110%;
    margin-top: 40px;
    margin-bottom: 14px;
  }

  .article-image {
    position: relative;
    width: 100%;
    height: 260px;
  }

  .reading-time {
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 14px;
  }

  .social-buttons {
    display: none;
  }

  @media ${device.tablet} {
    display: grid;
    margin: 0 0 80px 0;
    grid-template-columns: repeat(8, 1fr);

    .article-head-text {
      grid-column: 1/5;
      align-self: center;
      grid-row: 1;
      padding: 40px 0 40px 0;
    }

    .card-label {
      display: inline-block;
    }

    .article-title {
      margin: 20px 0 10px 0;
      font-size: 56px;
      line-height: 110%;
    }

    .article-image {
      position: relative;
      grid-column: 6/9;
      width: calc(100%);
      height: unset;
      margin-top: 0;

      &:after {
        content: "";
        display: block;
        padding-bottom: 100%;
      }

      .image-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
      }
    }

    .reading-time {
      font-size: 16px;
      font-weight: 400;
      margin-top: 10px;
    }

    .md-post {
      grid-column: 2 / 7;
    }

    .social-buttons {
      display: block;
      position: sticky;
      top: 40px;
      margin-top: 80px;
    }

    .abox-facename {
      margin-top: 80px;
      grid-column: 2/4;
    }

    .abox-about {
      margin-top: 80px;
      grid-column: 4/7;
    }
  }
`;

const ArticleInfos = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 40px;

  .article-author-picture {
    position: relative;
    visibility: visible;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    overflow: hidden;
    margin-right: 18px;
  }

  .article-author-name {
    font-size: 20px;
    margin-top: 8px;
    font-weight: 600;
    margin-bottom: 0;
  }

  .article-date {
    font-size: 16px;
    margin-top: 4px;
    margin-bottom: 8px;
    line-height: 100%;
  }

  .article-rt {
    position: relative;
    font-size: 16px;
    margin-bottom: 8px;
    line-height: 100%;

    span {
      font-size: 20px;
      margin-right: 2px;
    }
  }

  @media ${device.tablet} {
    display: flex;
    margin: 0;
    grid-column: 1/3;
    grid-row: 2;
    align-self: start;

    border-top: 1px solid #dcdcdc;
    border-bottom: none;
    padding-top: 80px;
    line-height: 120%;

    .article-author-name {
      margin: 12px 0 0 0;
      font-size: 22px;
      font-weight: 600;
      color: #323232;
      letter-spacing: 0.02em;
    }

    .article-date {
      margin-top: 8px;
      font-size: 20px;
      font-weight: 400;
      color: #323232;
      letter-spacing: 0.02em;
    }

    .article-rt {
      position: relative;
      font-size: 18px;

      span {
        display: inline-block;
        width: 28px;
        text-align: center;
        margin-right: 4px;
        color: white;
      }

      &:before {
        content: "";
        display: inline-block;
        position: absolute;
        z-index: -1;
        left: 0;
        top: -7px;
        background-color: #161616;
        height: 28px;
        width: 28px;
        border-radius: 22px;
      }
    }
  }
`;

const ArticleLead = styled.div`
  font-weight: 400;
  font-size: 26px;
  padding: 40px 0 24px 0;

  p {
    color: #161616;
    margin: 0;
  }

  @media ${device.tablet} {
    border-top: 1px solid #dcdcdc;
    border-left: 1px solid #dcdcdc;
    grid-column: 3/9;
    grid-row: 2;
    font-size: 32px;
    padding: 80px 0 40px 80px;
  }
`;
