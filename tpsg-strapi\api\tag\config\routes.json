{"routes": [{"method": "GET", "path": "/tags", "handler": "tag.find", "config": {"policies": []}}, {"method": "GET", "path": "/tags/count", "handler": "tag.count", "config": {"policies": []}}, {"method": "GET", "path": "/tags/:id", "handler": "tag.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/tags", "handler": "tag.create", "config": {"policies": []}}, {"method": "PUT", "path": "/tags/:id", "handler": "tag.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/tags/:id", "handler": "tag.delete", "config": {"policies": []}}]}