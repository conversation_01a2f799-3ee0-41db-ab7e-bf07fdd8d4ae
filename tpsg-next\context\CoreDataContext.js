"use client"
import { createContext, useContext, useState, useEffect } from "react"

const CoreDataContext = createContext(null);

export function CoreDataProvider({ children }) {
  const [coreData, setCoreData] = useState({ 'authors': [], 'blogs': [], 'topics': [], 'podcasts': [], 'topicGroups': [] });

  useEffect(() => {
    // we must fetch data this way because within the Page Router, we only have client components...
    const fetchCoreData = async () => {
      const response = await fetch('/api/coredata')
      const coreData = await response.json()
      setCoreData(coreData)
    }

    fetchCoreData()
      .catch(console.error)
  }, [])

	return coreData && (
		<CoreDataContext.Provider value={coreData}>
			{children}
		</CoreDataContext.Provider>
	);
}

export function useCoreData() {
  let context = useContext(CoreDataContext)
  if (context === null) {
    throw new Error('useCoreData must be used within a CoreDataProvider')
  }
  return context
}
