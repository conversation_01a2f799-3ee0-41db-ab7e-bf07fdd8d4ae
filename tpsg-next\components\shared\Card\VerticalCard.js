import styled from "styled-components";
import { Author } from "./Elements";
import Image from "next/image";
import { dateForHumans } from "utils/date.utils";
import { withRealSrc } from "utils/image-utils";
import { getPostRoute } from "utils/posts.utils";
import Link from "next/link";
import { device } from "styles/device";

/**
 *
 * @param {object}  post
 * @param {object}  label         petite étiquette affichée au-dessus du titre
 * @param {string}  label.text   texte de l'étiquette
 * @param {string}  label.link    lien optionnel
 * @param {object}  options
 * @param {string}  options.theme
 * @param {boolean} options.showType
 * @constructor
 */
export default function VerticalCard({
  label,
  post,
  options= {
    showType: false,
    theme: "light"
  }
}) {

  const date = dateForHumans(post.date);
  const image = withRealSrc(post.image);
  const link = getPostRoute(post);

  const authorOptions = {
    showName: true,
    showDate: false,
    theme: options.theme,
    direction: "column",
    showPicture: true
  }

  return(
    <Link href={link}>
      <Wrapper theme={options.theme}>
        { options.showType && <p>{post.type}</p> }
        <div className={"vc-image"}>
          <Image
            src={image}
            alt={""}
            sizes={"15vw"}
            fill
            style={styles.postImage}
          />
        </div>
        <div className={"vc-content"}>
          { label && <p className={"vc-label"}>{label.text}</p> }
          <p className={"vc-title"}>{post.title}</p>
          <Author author={post.author} date={date} options={authorOptions}/>
        </div>
      </Wrapper>
    </Link>
  )
}

const styles = {
  postImage: {
    objectFit: "cover"
  }
}

const Wrapper = styled.div`
  width: 100%;
  border: 1px solid ${(p) => (p.theme === "dark" ?
    "rgba(250,247,243,0.2)" : "rgba(22,22,22,0.2)")};

  .vc-image {
    position: relative;
    height: 200px;
    background-size: 80px;
    background-image: url(/images/tpsg-logo.svg);
    background-color: var(--c-soft-cream);
    background-repeat: no-repeat;
    background-position: center;
  }

  .vc-content {
    padding: 26px 24px 24px 24px;
  }

  .vc-label {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.72);
  }

  .vc-title {
    margin-top: 0;
    height: clamp(4.5rem, 2.65rem + 2.88vw, 5.25rem);
    margin-bottom: 40px;

    color: ${p => p.theme === "dark" ?
    "var(--c-soft-cream)" : "#161616"};

    font-family: Switzer, "Helvetica Neue", Helvetica, sans-serif;
    font-size: clamp(1.125rem, 0.82rem + 0.48vw, 1.25rem);
    font-weight: ${p => p.theme === "dark" ? 400 : 500};

    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  
  transition: all 300ms ease;

  @media ${device.desktop} {
    margin: 2px 2px;
    &:hover {
      transform: translateY(-8px);
      cursor: pointer;
      border: 1px solid var(--c-brand-lighter);
      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);
      box-shadow: 0 0 0 1px var(--c-brand-lighter);
    }
  }
`;
