'use strict';

module.exports = {
  /**
   * Initialisation des topics
   * - crée nes nouveaux topics depuis la liste au format json en bas de page.
   * - ajoute les relations avec les anciens topics
   * - ajoute les relations avec les articles
   *
   * @param ctx
   * @returns {Promise<string>}
   */
  async init(ctx) {
    await create_new_topics();
    return "should be done";
  },
  async initPostCount() {
    const topics = await strapi.query("topic").find({_limit: -1})
    await topics.forEach(topic => {
      strapi.services['topic'].updatePostCount(topic)
    })
    let res =  topics.sort((a, b) => (a.postCount < b.postCount) ? 1 : -1);
    res = res.map(topic => { return {
      name: topic.name,
      postsNbr: topic.postCount
    }})
    return({
      "message": "topics post count were initialized",
      "values": res
    })
  },
  /**
   * Retourne la liste de tous les enfants du thème
   * 
   * @param {*} ctx 
   * @returns [Topic]
   */
  async children(ctx) {
    const response = [];
    const topic = await strapi.query('topic').findOne({id: ctx.query.id})
    // récupération des enfants
    const children = await strapi.query('topic').find({
      "parent.id_eq": topic.id
    })
    response.push(...children)
    // Si le thème de la requête à un parent, il ne peut pas avoir de 
    // petit enfants.
    if(!topic.parent){
      for(const child of children){
        let grandchildren = await strapi.query('topic').find({
          "parent.id_eq": child.id
        })
        response.push(...grandchildren)
      }
    }
    return response;
  },
  async updatePostCnt(ctx){
    return "J'ai rien fait, tu devrais arrêter de m'appeler en fait"
  }
}


async function create_new_topics(){
  // création des nouveaux topics
  for(const topic of topicList){
    let newEntry = await strapi.query("topic").create({
      name: topic.name,
      slug: slugify(topic.name),
      parentSlug: topic.parent ? slugify(topic.parent) : null,
      parent: null,
      description: null,
    });
    // ajoute la relation avec les anciens topics si
    // le nouveau thème possède des correspondances
    if(topic.old_names){
      let oldNames = topic.old_names.split(";");
      for(const oldName of oldNames){
        let oldTopic = await strapi.query('old-topics').findOne({slug: slugify(oldName)})
        if(oldTopic){
          await strapi.query('old-topics').update({id: oldTopic.id}, {topic: newEntry.id});
        }
      }
    }
  }
  // update les parents des nouveaux topics
  let new_topics = await strapi.query("topic").find({_limit: -1});
  for(const new_topic of new_topics){
    let parent = await getTopicParent(new_topic);
    if(parent) {
      await strapi
        .query("topic")
        .update({id: new_topic.id}, {parent: parent.id});
    }
  }
}


async function getTopicParent(topic) {
  if(topic.parentSlug){
    return await strapi.query("topic").findOne({slug: topic.parentSlug});
  }
  return null;
}

const topicList = [
  {
    "name": "Théologie Systématique",
    "lvl": 1,
    "parent": null,
    "old_names": null
  },
  {
    "name": "Doctrine de Dieu",
    "lvl": 2,
    "parent": "Théologie Systématique",
    "old_names": "Doctrine de Dieu"
  },
  {
    "name": "Trinité",
    "lvl": 3,
    "parent": "Doctrine de Dieu",
    "old_names": null
  },
  {
    "name": "Le Saint-Esprit",
    "lvl": 3,
    "parent": "Doctrine de Dieu",
    "old_names": "Doctrine du Saint Esprit"
  },
  {
    "name": "Gloire de Dieu",
    "lvl": 3,
    "parent": "Doctrine de Dieu",
    "old_names": null
  },
  {
    "name": "Amour de Dieu",
    "lvl": 3,
    "parent": "Doctrine de Dieu",
    "old_names": null
  },
  {
    "name": "Sainteté de Dieu",
    "lvl": 3,
    "parent": "Doctrine de Dieu",
    "old_names": null
  },
  {
    "name": "Colère de Dieu",
    "lvl": 3,
    "parent": "Doctrine de Dieu",
    "old_names": null
  },
  {
    "name": "Doctrine de L'écriture",
    "lvl": 2,
    "parent": "Théologie Systématique",
    "old_names": "Bibliologie;Bible"
  },
  {
    "name": "Attributs de L'écriture",
    "lvl": 3,
    "parent": "Doctrine de L'écriture",
    "old_names": null
  },
  {
    "name": "Herméneutique",
    "lvl": 3,
    "parent": "Doctrine de L'écriture",
    "old_names": "Exégèse de texte;Histoire de l'interprétation;Méthodes, Alias Herméneutique;Traduction de la Bible"
  },
  {
    "name": "Inspiration et Innerance",
    "lvl": 3,
    "parent": "Doctrine de L'écriture",
    "old_names": null
  },
  {
    "name": "Textes difficiles",
    "lvl": 3,
    "parent": "Doctrine de L'écriture",
    "old_names": "Difficultés bibliques"
  },
  {
    "name": "Doctrine de la création",
    "lvl": 2,
    "parent": "Théologie Systématique",
    "old_names": "Création"
  },
  {
    "name": "Création/Évolution",
    "lvl": 3,
    "parent": "Doctrine de la création",
    "old_names": null
  },
  {
    "name": "Mandat culturel",
    "lvl": 3,
    "parent": "Doctrine de la création",
    "old_names": null
  },
  {
    "name": "Providence",
    "lvl": 3,
    "parent": "Doctrine de la création",
    "old_names": "Souveraineté de Dieu;Souveraineté de Christ "
  },
  {
    "name": "Miracles",
    "lvl": 3,
    "parent": "Doctrine de la création",
    "old_names": "Miracles;Signes et Miracles"
  },
  {
    "name": "Doctrine de l’homme",
    "lvl": 2,
    "parent": "Théologie Systématique",
    "old_names": "Doctrine de l’homme;Anthropologie"
  },
  {
    "name": "Image de Dieu",
    "lvl": 3,
    "parent": "Doctrine de l’homme",
    "old_names": null
  },
  {
    "name": "Complémentarisme",
    "lvl": 3,
    "parent": "Doctrine de l’homme",
    "old_names": "Complémentarité homme-femme"
  },
  {
    "name": "Composition de l'homme (dicho-tricho)",
    "lvl": 3,
    "parent": "Doctrine de l’homme",
    "old_names": null
  },
  {
    "name": "Doctrine du péché",
    "lvl": 2,
    "parent": "Théologie Systématique",
    "old_names": "Doctrine du Péché"
  },
  {
    "name": "La chute",
    "lvl": 3,
    "parent": "Doctrine du péché",
    "old_names": null
  },
  {
    "name": "Grâce commune",
    "lvl": 3,
    "parent": "Doctrine du péché",
    "old_names": null
  },
  {
    "name": "Péché originel",
    "lvl": 3,
    "parent": "Doctrine du péché",
    "old_names": null
  },
  {
    "name": "Satan et les démons",
    "lvl": 3,
    "parent": "Doctrine du péché",
    "old_names": "Anges, Démons et Satan"
  },
  {
    "name": "Combat spirituel",
    "lvl": 3,
    "parent": "Doctrine du péché",
    "old_names": "Combat spirituel"
  },
  {
    "name": "Doctrine du Christ",
    "lvl": 2,
    "parent": "Théologie Systématique",
    "old_names": "Christologie"
  },
  {
    "name": "Union hypostatique",
    "lvl": 3,
    "parent": "Doctrine du Christ",
    "old_names": null
  },
  {
    "name": "La naissance de Christ",
    "lvl": 3,
    "parent": "Doctrine du Christ",
    "old_names": null
  },
  {
    "name": "Vie et le ministère de Christ",
    "lvl": 3,
    "parent": "Doctrine du Christ",
    "old_names": null
  },
  {
    "name": "Souffrances et la mort de Christ",
    "lvl": 3,
    "parent": "Doctrine du Christ",
    "old_names": null
  },
  {
    "name": "La résurrection de Christ",
    "lvl": 3,
    "parent": "Doctrine du Christ",
    "old_names": "Résurrection de Christ "
  },
  {
    "name": "Doctrine du salut",
    "lvl": 2,
    "parent": "Théologie Systématique",
    "old_names": "Doctrine du salut"
  },
  {
    "name": "Expiation",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Évangile",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": "Évangile"
  },
  {
    "name": "Élection et prédestination",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Union à Christ",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": "Union à Christ"
  },
  {
    "name": "Appel général",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Appel efficace",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Foi et repentance",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Justification",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Régénération",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Adoption",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Sanctification",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Persévérance des saints",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Glorification",
    "lvl": 3,
    "parent": "Doctrine du salut",
    "old_names": null
  },
  {
    "name": "Doctrine de l’Église",
    "lvl": 2,
    "parent": "Théologie Systématique",
    "old_names": "Doctrine de l'Église"
  },
  {
    "name": "Gouvernement de l’Église",
    "lvl": 3,
    "parent": "Doctrine de l’Église",
    "old_names": null
  },
  {
    "name": "Pureté de l’Église",
    "lvl": 3,
    "parent": "Doctrine de l’Église",
    "old_names": null
  },
  {
    "name": "Culte et Liturgie",
    "lvl": 3,
    "parent": "Doctrine de l’Église",
    "old_names": null
  },
  {
    "name": "Doctrine des temps de la fin",
    "lvl": 2,
    "parent": "Théologie Systématique",
    "old_names": "Doctrine de la fin des temps"
  },
  {
    "name": "Mort/État intermédiaire",
    "lvl": 3,
    "parent": "Doctrine des temps de la fin",
    "old_names": null
  },
  {
    "name": "Millenium",
    "lvl": 3,
    "parent": "Doctrine des temps de la fin",
    "old_names": null
  },
  {
    "name": "Jugement et récompenses",
    "lvl": 3,
    "parent": "Doctrine des temps de la fin",
    "old_names": null
  },
  {
    "name": "Enfer/Peines éternelles",
    "lvl": 3,
    "parent": "Doctrine des temps de la fin",
    "old_names": null
  },
  {
    "name": "Nouvelle création",
    "lvl": 3,
    "parent": "Doctrine des temps de la fin",
    "old_names": null
  },
  {
    "name": "Théologie Biblique",
    "lvl": 1,
    "parent": null,
    "old_names": null
  },
  {
    "name": "Livres de la Bible",
    "lvl": 2,
    "parent": "Théologie Biblique",
    "old_names": null
  },
  {
    "name": "Pentateuque",
    "lvl": 3,
    "parent": "Livres de la Bible",
    "old_names": "Pentateuque"
  },
  {
    "name": "Livres historiques",
    "lvl": 3,
    "parent": "Livres de la Bible",
    "old_names": "Josué;Esther"
  },
  {
    "name": "Livres prophétiques",
    "lvl": 3,
    "parent": "Livres de la Bible",
    "old_names": "Prophètes"
  },
  {
    "name": "Livres de sagesse",
    "lvl": 3,
    "parent": "Livres de la Bible",
    "old_names": "Livres de sagesse;Proverbes"
  },
  {
    "name": "Psaumes",
    "lvl": 3,
    "parent": "Livres de la Bible",
    "old_names": "Psaumes"
  },
  {
    "name": "Évangiles",
    "lvl": 3,
    "parent": "Livres de la Bible",
    "old_names": "Évangile selon Matthieu;Évangile selon Marc;Évangile selon Luc;Évangile selon Jean;Évangiles"
  },
  {
    "name": "Actes",
    "lvl": 3,
    "parent": "Livres de la Bible",
    "old_names": "Actes"
  },
  {
    "name": "Épîtres",
    "lvl": 3,
    "parent": "Livres de la Bible",
    "old_names": "Éphésiens;Épître aux Hébreux;Épîtres de Jean;Épîtres de Paul;Épîtres générales"
  },
  {
    "name": "Apocalypse",
    "lvl": 3,
    "parent": "Livres de la Bible",
    "old_names": "Apocalypse"
  },
  {
    "name": "Histoire du salut",
    "lvl": 2,
    "parent": "Théologie Biblique",
    "old_names": "Histoire de la Rédemption;Jésus dans l'Ancien Testament;Personnages de l'Ancien Testament;Personnages du Nouveau Testament"
  },
  {
    "name": "Théologie de l’alliance",
    "lvl": 2,
    "parent": "Théologie Biblique",
    "old_names": "Théologie de l’Alliance"
  },
  {
    "name": "Royaume de Dieu",
    "lvl": 2,
    "parent": "Théologie Biblique",
    "old_names": "Royaume de Dieu"
  },
  {
    "name": "Théologie Pratique",
    "lvl": 1,
    "parent": null,
    "old_names": null
  },
  {
    "name": "Vie chrétienne",
    "lvl": 2,
    "parent": "Théologie Pratique",
    "old_names": "Vie chrétienne"
  },
  {
    "name": "Addictions",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": "Addiction"
  },
  {
    "name": "Espérance",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Joie",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": "Hédonisme chrétien"
  },
  {
    "name": "Pardon",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": "Pardon"
  },
  {
    "name": "Courage",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Dépression",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Colère",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Orgueil et humilité",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Médisance",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Connaitre la volonté de Dieu",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": "Connaître la volonté de Dieu"
  },
  {
    "name": "Argent et budget",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": "Argent et biens matériels"
  },
  {
    "name": "Persécution",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Pornographie",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Alimentation",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Sport",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Sexualité",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": "Sexualité"
  },
  {
    "name": "Corps",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Loisirs",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": null
  },
  {
    "name": "Souffrance",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": "Souffrance"
  },
  {
    "name": "Productivité",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": "Productivité;Bien commencer l'année"
  },
  {
    "name": "Mort et Deuil",
    "lvl": 3,
    "parent": "Vie chrétienne",
    "old_names": "Mort"
  },
  {
    "name": "Croissance spirituelle",
    "lvl": 2,
    "parent": "Théologie Pratique",
    "old_names": "Piété;Disciplines spirituelles;Foi"
  },
  {
    "name": "Lecture de la Bible",
    "lvl": 3,
    "parent": "Croissance spirituelle",
    "old_names": null
  },
  {
    "name": "Mémorisation de la Bible",
    "lvl": 3,
    "parent": "Croissance spirituelle",
    "old_names": null
  },
  {
    "name": "Prière",
    "lvl": 3,
    "parent": "Croissance spirituelle",
    "old_names": "Prière"
  },
  {
    "name": "Jeûne",
    "lvl": 3,
    "parent": "Croissance spirituelle",
    "old_names": null
  },
  {
    "name": "Libéralité/Générosité",
    "lvl": 3,
    "parent": "Croissance spirituelle",
    "old_names": null
  },
  {
    "name": "Combat contre le péché",
    "lvl": 3,
    "parent": "Croissance spirituelle",
    "old_names": "Sainteté et Sanctification;Péchés;Redevabilité"
  },
  {
    "name": "Vie de famille",
    "lvl": 2,
    "parent": "Théologie Pratique",
    "old_names": "Famille"
  },
  {
    "name": "Mariage",
    "lvl": 3,
    "parent": "Vie de famille",
    "old_names": "Mariage"
  },
  {
    "name": "Célibat",
    "lvl": 3,
    "parent": "Vie de famille",
    "old_names": "Célibat"
  },
  {
    "name": "Parentalité",
    "lvl": 3,
    "parent": "Vie de famille",
    "old_names": "Ados;Parents;Enfants;Adoption"
  },
  {
    "name": "Féminité",
    "lvl": 3,
    "parent": "Vie de famille",
    "old_names": "Féminité;Femmes vertueuses"
  },
  {
    "name": "Masculinité",
    "lvl": 3,
    "parent": "Vie de famille",
    "old_names": "Masculinité"
  },
  {
    "name": "Foyer",
    "lvl": 3,
    "parent": "Vie de famille",
    "old_names": "Famille"
  },
  {
    "name": "Vie d’Église",
    "lvl": 2,
    "parent": "Théologie Pratique",
    "old_names": "Vie et Église"
  },
  {
    "name": "Ministère pastoral",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Leadership;Ministère pastoral"
  },
  {
    "name": "Culte communautaire",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Culte dominical;Adoration"
  },
  {
    "name": "Musique et louange",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": null
  },
  {
    "name": "Discipline d’Église",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Discipline d'Église"
  },
  {
    "name": "Prédication et enseignement",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Homilétique;Ministère d’enseignement;Saine Doctrine"
  },
  {
    "name": "Membriété",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": null
  },
  {
    "name": "Discipulat/Mentorat",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Discipulat"
  },
  {
    "name": "Enfance et jeunesse",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Jeunesse"
  },
  {
    "name": "Accompagnement biblique",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Psychologie;Relation d'aide"
  },
  {
    "name": "Baptême",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": null
  },
  {
    "name": "Cène",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": null
  },
  {
    "name": "Évangélisation",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Évangélisation"
  },
  {
    "name": "Implantation d’Église",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Implantation d’Église"
  },
  {
    "name": "Mission de l’Église",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Mission"
  },
  {
    "name": "Peuples non atteints",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": null
  },
  {
    "name": "Noël",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Noël"
  },
  {
    "name": "Pentecôte",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": null
  },
  {
    "name": "Pâques",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Pâques"
  },
  {
    "name": "Ascension",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Ascension"
  },
  {
    "name": "Avent",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": null
  },
  {
    "name": "Sabbat",
    "lvl": 3,
    "parent": "Vie d’Église",
    "old_names": "Sabbat et Jour du Seigneur"
  },
  {
    "name": "Vie sociale",
    "lvl": 2,
    "parent": "Théologie Pratique",
    "old_names": null
  },
  {
    "name": "Hospitalité",
    "lvl": 3,
    "parent": "Vie sociale",
    "old_names": "Hospitalité"
  },
  {
    "name": "Amitié",
    "lvl": 3,
    "parent": "Vie sociale",
    "old_names": null
  },
  {
    "name": "Travail",
    "lvl": 3,
    "parent": "Vie sociale",
    "old_names": "Travail et Vocation"
  },
  {
    "name": "Théologie Historique",
    "lvl": 1,
    "parent": null,
    "old_names": null
  },
  {
    "name": "Catéchismes et confessions de foi",
    "lvl": 2,
    "parent": "Théologie Historique",
    "old_names": "Catéchismes et Confessions de Foi"
  },
  {
    "name": "Histoire de l’Église",
    "lvl": 2,
    "parent": "Théologie Historique",
    "old_names": "Histoire de l’Église;Biographie"
  },
  {
    "name": "Église primitive",
    "lvl": 3,
    "parent": "Histoire de l’Église",
    "old_names": "Église primitive"
  },
  {
    "name": "Pères de l’Église",
    "lvl": 3,
    "parent": "Histoire de l’Église",
    "old_names": "Pères de l'Église"
  },
  {
    "name": "Église médiévale",
    "lvl": 3,
    "parent": "Histoire de l’Église",
    "old_names": "Scolastiques"
  },
  {
    "name": "Réforme",
    "lvl": 3,
    "parent": "Histoire de l’Église",
    "old_names": "Calvinisme;La Réforme"
  },
  {
    "name": "Puritains",
    "lvl": 3,
    "parent": "Histoire de l’Église",
    "old_names": "Puritains"
  },
  {
    "name": "Réveils",
    "lvl": 3,
    "parent": "Histoire de l’Église",
    "old_names": "Réveils"
  },
  {
    "name": "Église contemporaine",
    "lvl": 3,
    "parent": "Histoire de l’Église",
    "old_names": null
  },
  {
    "name": "Figures historiques",
    "lvl": 2,
    "parent": "Théologie Historique",
    "old_names": null
  },
  {
    "name": "Calvin",
    "lvl": 3,
    "parent": "Figures historiques",
    "old_names": null
  },
  {
    "name": "Luther",
    "lvl": 3,
    "parent": "Figures historiques",
    "old_names": null
  },
  {
    "name": "C. S. Lewis",
    "lvl": 3,
    "parent": "Figures historiques",
    "old_names": null
  },
  {
    "name": "Éthique",
    "lvl": 1,
    "parent": null,
    "old_names": null
  },
  {
    "name": "Homosexualité et LGBT",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": "Homosexualité"
  },
  {
    "name": "Transgenre",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": null
  },
  {
    "name": "Justice sociale",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": "Justice sociale"
  },
  {
    "name": "Politique et société",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": "Politique et Laïcité"
  },
  {
    "name": "Avortement",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": "Avortement"
  },
  {
    "name": "Divorce",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": null
  },
  {
    "name": "Fin de vie et euthanasie",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": null
  },
  {
    "name": "Suicide",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": null
  },
  {
    "name": "PMA et GPA",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": null
  },
  {
    "name": "Pauvreté",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": null
  },
  {
    "name": "Harmonie raciale",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": null
  },
  {
    "name": "Écologie",
    "lvl": 2,
    "parent": "Éthique",
    "old_names": "Écologie"
  },
  {
    "name": "Apologétique et vision du monde",
    "lvl": 1,
    "parent": null,
    "old_names": null
  },
  {
    "name": "Défense de la foi (évidentialisme)",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": null
  },
  {
    "name": "Théodicée",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": null
  },
  {
    "name": "Culture et arts",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": "Culture et Art"
  },
  {
    "name": "Technologie et médias",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": "Technologie;Éthique des médias"
  },
  {
    "name": "Vision du monde chrétienne",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": "Vision du monde"
  },
  {
    "name": "Islam",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": "Islam"
  },
  {
    "name": "Religions du monde",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": "Apologétique et autres religions"
  },
  {
    "name": "Occultisme",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": null
  },
  {
    "name": "Sectes",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": null
  },
  {
    "name": "Hérésies/Faux enseignements",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": "Hérésies;Apostasie"
  },
  {
    "name": "Philosophie",
    "lvl": 2,
    "parent": "Apologétique et vision du monde",
    "old_names": "Philosophie"
  },
  {
    "name": "Ressources",
    "lvl": 1,
    "parent": null,
    "old_names": "Ressources;Audio;Citation;Ebooks;Musique;Ressources gratuites;Sites recommandés;Vidéos"
  },
  {
    "name": "Livre audio",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": null
  },
  {
    "name": "Conférence",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Conférence"
  },
  {
    "name": "Évènement",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Évènement"
  },
  {
    "name": "Foire aux liens",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Foire aux liens"
  },
  {
    "name": "Outil",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Logiciels bibliques;Outils"
  },
  {
    "name": "Recension de livre",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Recensions de livres;Litétature;Livres"
  },
  {
    "name": "Critique de film",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Critique de film"
  },
  {
    "name": "Actualité de TPSG",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Actualité de TPSG"
  },
  {
    "name": "Actualité perso",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Actualité perso"
  },
  {
    "name": "Prédication",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Prédications"
  },
  {
    "name": "Témoignage",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Témoignage"
  },
  {
    "name": "Interview",
    "lvl": 2,
    "parent": "Ressources",
    "old_names": "Interviews"
  }
]

function slugify(str) {
  str = str.replace(/^\s+|\s+$/g, '');

  // Make the string lowercase
  str = str.toLowerCase();

  // Remove accents, swap ñ for n, etc
  var from = "ÁÄÂÀÃÅČÇĆĎÉĚËÈÊẼĔȆÍÌÎÏŇÑÓÖÒÔÕØŘŔŠŤÚŮÜÙÛÝŸŽáäâàãåčçćďéěëèêẽĕȇíìîïňñóöòôõøðřŕšťúůüùûýÿžþÞĐđßÆa·/_,:;";
  var to   = "AAAAAACCCDEEEEEEEEIIIINNOOOOOORRSTUUUUUYYZaaaaaacccdeeeeeeeeiiiinnooooooorrstuuuuuyyzbBDdBAa------";
  for (var i=0, l=from.length ; i<l ; i++) {
    str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
  }

  // Remove invalid chars
  str = str.replace(/[^a-z0-9 -]/g, '')
    // Collapse whitespace and replace by -
    .replace(/\s+/g, '-')
    // Collapse dashes
    .replace(/-+/g, '-');

  return str;
}

