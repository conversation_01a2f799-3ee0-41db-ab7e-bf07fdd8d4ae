import * as Section from "components/home"
import { CORE_POST_SET } from "api/gql-queries";
import { gql } from "@apollo/client";
import client from "api/apollo-client";
import { Featured } from "components/shared"
import { NextSeo } from "next-seo";
import styled from "styled-components";
import { device } from "../styles/device";

export default function Home({ home }) {
  return (
    <PageWrapper className={"page"}>
      <NextSeo
        title={"ToutPourSaGloire.com"}
        description={"ToutPourSaGloire.com c'est des milliers de ressources pour vous aider à mener une vie qui glorifie Dieu."}
      />
      {home.articles && <Section.Articles data={home.articles}/>}
      {home.featured.length > 0 && <Featured content={home.featured[0]}/>}
      {home.mission && <Section.Mission data={home.mission}/>}
      {home.podcasts && <Section.Podcasts data={home.podcasts}/>}

      {home.webinars && home.formations && <Section.Double webinars={home.webinars} formations={home.formations}/>}
      {home.bloggers && <Section.Bloggers data={home.bloggers}/>}
      <WithColumn className={"site-padding"}>
        {home.topics && <Section.Topics data={home.topics}/>}
        {home.mostRead && <Section.MostRead data={home.mostRead} newsletter={home.newsletter}/>}
      </WithColumn>
    </PageWrapper>)
}

const PageWrapper = styled.div`
  width: 100vw;
  //padding-right: 15px;
  background-color: var(--soft-white);

  .double-section {
    background-color: var(--blue-dark);
    display: flex;
    flex-direction: column;

    @media ${device.desktop} {
      flex-direction: row;
    }
  }
`

const WithColumn = styled.div`
  display: grid;
  grid-gap: 16px;
  grid-template-columns: repeat(12, 1fr);
`

export async function getStaticProps() {
  const { data } = await client.query({ query: QUERY_HOME })

  return {
    props: {
      home: data.home
    },
    revalidate: 10
  };
}

const QUERY_HOME = gql`
  ${CORE_POST_SET}
  query GetHome {
	home{
	  newsletter
	  articles{
		name
		posts(sort: "published_at:DESC"){
		  ...CorePostSet
		  modules {
			...on ComponentModuleLead {
			  __typename
			  content
			}
		  }
		}
	  }
	  podcasts{
		name
		posts{
		  ...CorePostSet
		  modules {
			__typename
			...on ComponentModulePodcast{
			  podcast {
				logoSmall {
				  formats
				  url
				  provider
				}
				name
				slug
			  }
			}
		  }
		}
	  }
	  shop {
		text
		name
		image {
		  formats
		}
	  }
	  quote {
		author
		text
	  }
	  bloggers {
		name
		posts {
		  ...CorePostSet
		  blog{
			slug
			blogger {
			  firstName
			  lastName
			  fullName
			  slug
			  picture {
				url
				width
				height
				provider
			  }
			}
		  }
		}
	  }
	  mission{
		partOne
		partTwo
	  }
	  topics {
		name
		posts{
		  ...CorePostSet
		}
	  }
	  formations {
		name
		posts {
		  ...CorePostSet
		  modules {
			...on ComponentModuleLead {
			  __typename
			  content
			}
		  }
		},
	  }
	  webinars {
		name
		posts {
		  ...CorePostSet
		  modules {
			...on ComponentModuleLead {
			  __typename
			  content
			}
		  }
		},

	  }
	  mostRead {
		name
		posts {
		  ...CorePostSet
		}
	  }
	  featured {
		type
		title
		description
		cta {
		  name
		  url
		  outline
		}
		cta2 {
		  name
		  url
		  outline
		}
		color {
		  foreground
		  background
		}
		image {
		  width
		  height
		  url
		  provider
		  formats
		}
		postRef {
		  id
		  author {
			fullName
		  }
		}
	  }
	}
  }
`
