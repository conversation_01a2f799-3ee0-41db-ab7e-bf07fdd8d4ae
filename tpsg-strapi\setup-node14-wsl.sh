#!/bin/bash
# Script pour configurer Node.js 14 comme version par défaut dans WSL

# Charger NVM
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"

# Définir Node.js 14 comme version par défaut
nvm alias default 14

# Vérifier la version de Node.js
echo "Version de Node.js par défaut :"
node -v

# Ajouter les commandes d'initialisation de NVM au fichier .bashrc si elles n'y sont pas déjà
if ! grep -q "NVM_DIR" ~/.bashrc; then
  echo 'export NVM_DIR="$HOME/.nvm"' >> ~/.bashrc
  echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm' >> ~/.bashrc
  echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion' >> ~/.bashrc
  echo "Configuration ajoutée au fichier .bashrc"
fi

echo "Node.js 14 est maintenant la version par défaut dans WSL"
