"use strict";


module.exports = {

  issues: async function issues() {

    let issues = [];
    let posts = await strapi.query("post").find({_limit: -1});
    let lead = false;

    for (const post of posts) {
      lead = getModule(post, "lead");
      if (lead?.content.length < 20) {
        issues.push({
          title: post.title,
          slug: post.slug,
          chapo: "< à 20 caractères"
        });
      }
      if (!lead) {
        issues.push({
          title: post.title,
          slug: post.slug,
          chapo: "vide"
        });
      }
    }
    return issues;
  },
  /**
   * Retourne l'ensemble des ressources dont le sujet se rapproche le plus
   * du post dont l'ID est passé en paramètre.
   * @param post postId
   */
  related: async (post) => {

    const response = [];

    // Array qui stock les IDs des posts déjà sélectionnés.
    // Il permet d'exclure certains résultats pour les recherches consécutives
    let nin = [post.id];

    // topics du post d'origine
    const oTopics = post.topics.map((topic) => topic.id);

    if (oTopics.length <= 0) return [];

    // Tags du post d'origine
    const oTags = post.tags.map((tag) => tag.id);

    // récupère l'ensemble des posts qui partagent au moins un thème en communs
    // avec le post de base.
    const relatedPosts = await strapi.query("post").find({
      topics_in: oTopics,
      id_nin: nin,
      published_at_null: false
    });

    // Création de la liste des résultats
    let scoredPosts = createScoredPosts(relatedPosts, oTopics, oTags);

    // Dans la liste des résultats, on extrait l'id des premiers webinaire et formation
    const {
      webinarIndex,
      formationIndex
    } = getSpecialPosts(scoredPosts);

    if (webinarIndex) {
      scoredPosts[webinarIndex].section = "webinar";
      response.push(scoredPosts[webinarIndex]);
      scoredPosts.splice(webinarIndex, 1);
    }
    if (formationIndex) {
      scoredPosts[formationIndex].section = "formation";
      response.push(scoredPosts[formationIndex]);
      scoredPosts.splice(formationIndex, 1);
    }

    // Ajout des 2 posts les plus pertinents à la liste principale
    response.push(...scoredPosts.slice(0, 2))

    // Mise à jour de l'Id des posts à exclure
    nin.push(...response.map((sp) => sp.post.id));

    // stock le nombre de posts scorés qui ont déjà été ajouté au résultat;
    let spCount = 2;

    // Ajout d'un post du même ministère (si ministère)
    let postWithSameMinistry = await getTopicGroupPosts(nin, oTopics, "ministere");
    let ministryPost = postWithSameMinistry.posts.length > 0 ?
      createScoredPosts(postWithSameMinistry.posts, oTopics, oTags)[0]
      :
      null;
    if (ministryPost) {
      ministryPost.origin = postWithSameMinistry.origin
      ministryPost.section = "ministry";
      response.push(ministryPost);
      nin.push(ministryPost.post.id);

      // Suppression du post ajouté des scoredPosts. Cette vérification est nécessaire
      // afin que le dernier post, s'il n'est pas issue d'une vocation, ne soit pas le
      // même que celui qui vient d'être ajouté.
      scoredPosts = scoredPosts.filter(item => item.post.id !== ministryPost.post.id);

    } else {
      response.push(...scoredPosts.slice(2, 3))
      spCount = 3;
      // Mise à jour de l'Id des posts à exclure pour la prochaine requête.
      nin.push(response[response.length - 1].post.id);
    }


    // ajout d'un post de la même vocation (si vocation)
    let postWithSameVocation = await getTopicGroupPosts(nin, oTopics, "vocation");
    let vocationPost = postWithSameVocation.posts.length > 0 ?
      createScoredPosts(postWithSameVocation.posts, oTopics, oTags)[0]
      :
      null;
    if (vocationPost) {
      vocationPost.origin = postWithSameVocation.origin
      vocationPost.section = "vocation";
      response.push(vocationPost);
    } else {
      response.push(...scoredPosts.slice(spCount, spCount+1));
    }

    // Vérification de la présence d'un auteur sur les posts.
    // En cas d'auteur absent, on le crée avec des valeurs nulles;
    return response.map(item => {
      if(item.post.author === null || item.post.author === undefined) {
        return item;
      }
      if (Object.keys(item.post.author).length === 0) {
        item.post.author = null;
      }
      return item;
    });
  },


  getMetas: async (data) => {

    let metas = {
      type: "article",
      tags: data.tags?.map(tag => tag.id ? tag.id : tag) || []
    };

    let mediaId;

    for (const module of data.modules || []) {
      switch (module.__component) {
        case "module.webinar":
          metas.type = "webinaire";
          mediaId = module?.webinar?.id ? module.webinar.id : module.webinar;
          metas.tags = await getNewTags(metas.tags, "webinar", mediaId);
          break;
        case "module.podcast":
          metas.type = "podcast";
          mediaId = module?.podcast?.id ? module.podcast.id : module.podcast;
          metas.tags = await getNewTags(metas.tags, "podcast", mediaId);
          break;
        // add channel tag here
        case "module.email-journey":
          metas.type = "parcours";
          break;
        case "module.formation":
          metas.type = "formation";
          break;
      }
    }
    return metas;
  }
};

/**
 * Retourne un tableau d'id des tags du post mis à jour avec celui de la chaine du media
 * à laquel il appartient
 * @param {[number]} tags tags du post
 * @param {string} mediaType webinar/podcast
 * @param {number} mediaId Identifiant du webinaire/podcast
 * @returns {[number]}
 */
async function getNewTags(tags, mediaType, mediaId) {


  const {slug} = await strapi.query(mediaType).findOne({id: mediaId});
  let channelTag = await strapi.query("tag").findOne({slug: `channel-${slug}`});
  /**
   * Si le tag n'est pas présent dans la DB, alors il doit être ajouté.
   */
  if (!channelTag) {
    channelTag = await strapi.query("tag").create({
      name: `channel.${slug}`,
      slug: `channel-${slug}`,
    })
  }

  if(mediaType === 'podcast'){
    const otherChannels = await strapi.query("tag").find({
      slug_contains: "channel"
    });
    
    const allTags = otherChannels.map((e) => e.id)
    const tagsToRemove = allTags.filter(id => id !== channelTag.id)

    tags = tags.filter(id => !tagsToRemove.includes(id))
   }

  if (tags) {
    if (tags.indexOf(channelTag.id) === -1) {
      tags.push(channelTag.id)
    }
    return tags;
  } else {
    return [channelTag.id]
  }
}

function createScoredPosts(relatedPosts, topics = null, tags = null) {
  let score;
  let scoredPosts = []
  for (const relatedPost of relatedPosts) {
    score = 0;
    score += topics ? // 1 point par topic similaire
      relatedPost.topics
        .map((topic) => topic.id)
        .filter(topicId => topics.includes(topicId)).length
      :
      0

    score += tags ? // 2 points par tag similaire
      relatedPost.tags
        .map((tag) => tag.id)
        .filter(tagId => tags.includes(tagId)).length * 2
      :
      0

    // 4 points bonus si l'article est un cornerStone
    score += relatedPost.isCornerStone ? 4 : 0;
    scoredPosts.push({
      post: relatedPost,
      section: "Default",
      origin: "Multiple",
      score: score,
    })
  }
  if (scoredPosts.length > 0) {
    scoredPosts = scoredPosts.sort((a, b) => (a.score < b.score) ? 1 : -1); // .slice(0,4);
  }
  return scoredPosts
}

/**
 * Retourne l'index du webinaire et de la formation trouvés parmis
 * une liste de posts scorés. Si plusieurs webinaires ou formations
 * éxistent, on retourne le premier rencontré (ce sera celui avec le
 * score le plus haut si la liste arrive déjà triée)
 * @param {*} scoredPosts
 * @returns
 */
function getSpecialPosts(scoredPosts) {
  const res = {
    webinarIndex: undefined,
    formationIndex: undefined,
  }
  const typeFound = {
    webinar: false,
    formation: false
  }
  scoredPosts.map((scoredPost, index) => {
    if (scoredPost.post.type === "webinaire" && typeFound.webinar === false) {
      res.webinarIndex = index;
      typeFound.webinar = true;
    }
    if (scoredPost.post.type === "formation" && typeFound.formation === false) {
      res.formationIndex = index;
      typeFound.formation = true;
    }
  })
  return res;
}

async function getTopicGroupPosts(nin, postTopicIds, groupType) {
  let posts = [];
  let topicGroups = await strapi.query("topic-group").find({
    topics_in: postTopicIds,
    type: groupType,
  })
  if (topicGroups.length > 0) {
    posts = await strapi.query("post").find({
      topics_in: topicGroups[0].topics.map((t) => t.id),
      id_nin: nin,
      type: "article",
      published_at_null: false
    })
  }
  return {
    posts: posts,
    origin: topicGroups[0]?.name || "unknown"
  };
}


function getModule(post, moduleName) {
  return post.modules?.find(function (module) {
    return module.__component === `module.${moduleName}`;
  });
}
