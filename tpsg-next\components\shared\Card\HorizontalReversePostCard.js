import { dateForHumans } from "/utils/date.utils";
import Link from "next/link";
import styled from "styled-components";
import CondImage from "components/shared/condimage";
import { device } from "styles/device";
import {
  PostCardDescription,
  PostCardDetails,
  PostCardTitle,
} from "styles/styled-typography";
import { getPostRoute } from "utils/posts.utils";
import { AnimatedIcon } from "../atoms";
import { getDotColor } from "utils/color";
import { useMediaQuery } from "hooks/UseMediaQuery";
import { modulesAsObj } from "utils/components.utils";
import { getPostLead } from "utils/posts.utils"
import CondLink from "../CondLink";

//options: showDate/showAuthor/showAnimatedIcon/showLead
export default function HorizontalReversePostCard({ post, options }) {
  //boolean
  const isMobile = !useMediaQuery({ mediaQuery: device.tablet })

  if (!post.type) return <></>;

  let lead = undefined;
  let date = undefined;
  let author = undefined;
  let route = getPostRoute(post);

  // Gros délire avec le premier slash de la chaine que se fait éjecter pour les articles...

  if (options?.showDate) {
    date = !post.date ? dateForHumans(post.published_at) : dateForHumans(post.date);
    date = date.replace(".", "");
  }

  if (options?.showAuthor) {
    // Gestion des différents formats d'auteur (objet avec fullName ou chaîne directe)
    if (post.author) {
      author = post.author?.fullName ? post.author.fullName : post.author;
    }
    // Si post.author est null/undefined, author reste undefined et ne s'affiche pas
  }

  if (options?.showLead) {
    lead = getPostLead(post)
  }

  return (
    <CardEpisode>
      <div className="post-info">
        <PostCardDetails>
          { date && <span>{date}</span>}
          { date && author && " - "}
          { author && <span>{author}</span>}
        </PostCardDetails>
        <CondLink link={route}>
          <PostCardTitle>{post.title}</PostCardTitle>
          { (!isMobile && lead) &&
            <PostCardDescription>{lead}</PostCardDescription>
          }
        </CondLink>
      </div>
      <div className="post-image-container">
        <CondLink link={route}>
          <PostImage>
            <div className="post-image">
              <CondImage imageData={post.image} />
            </div>
            {options?.showAnimatedIcon && (
              <AnimatedIcon
                type={post.type}
                colors={getDotColor(post.type)}
              />
            )}
          </PostImage>
        </CondLink>
      </div>
    </CardEpisode>
  );
}

const PostImage = styled.div`
  width: 100%;
  position: relative;
  .post-image {
    width: 100%;
    aspect-ratio: 1/1;
    position: relative;
  }
`;

const CardEpisode = styled.div`
  list-style: none;
  position: relative;
  width: 100%;
  list-style: none;

  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  &:after {
    content: "";
    display: block;
    width: 100%;
    margin: 24px 0px;
    background-color: #40444444;
    height: 1px;
  }

  .post-info {
    width: 65%;
    padding-right: 10px;
  }
  .post-image-container {
    width: 30%;
  }
  @media screen and (max-width: 320px) {
    /* Little screen only */
    flex-direction: column;
    .post-info {
      width: 100%;
    }
    .post-image-container {
      width: 40%;
    }
  }
  @media ${device.tablet} {
    flex-direction: row-reverse;
    &:after {
      content: "";
      display: block;
      width: 100%;
      margin: 37px 0px 30px 0px;

      background-color: #40444444;
      height: 1px;
    }
    .post-info {
      width: 75%;
    }
    .post-image-container {
      width: 20%;
    }
  }
`;
