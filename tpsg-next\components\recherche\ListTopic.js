import styled from "styled-components";
import { Fragment, memo } from "react";
import { dynamicSort } from "utils/list.utils";

const LetterSeparator = ({ word, prevWord }) => {
  if (word.charAt(0) !== prevWord.charAt(0)) {
    return <li key={`s-${word.charAt(0)}`} className={"letter-separator"}>{word.charAt(0)}</li>
  } else {
    return null;
  }
}

function ListTopic({ topics, setFilter }) {

  topics.sort(dynamicSort("name"));

  const handleSelect = (topicName) => {
    setFilter("topics", topicName);
    window.scrollTo({ top: 0, behavior: "smooth" })
  }

  return (
    <Wrapper className={"site-padding"}>
      {topics.map((t, i) => {
        return (
          <Fragment key={i}>
            {i !== 0 && <LetterSeparator word={t.name} prevWord={topics[i - 1].name}/>}
            <li
              onClick={() => handleSelect(t.name)}>
              <span>{t.name}</span>
            </li>
          </Fragment>
        )
      })}
    </Wrapper>
  )
}

export default memo(ListTopic);

const Wrapper = styled.ul`
  position: relative;
  z-index: 400;
  margin-top: 64px;
  width: calc(33.33% + var(--desktop-gap));
  column-width: 260px;

  li {
    margin: 4px 0 4px 0;
    font-size: 19px;
    line-height: 28px;
    list-style: none;
    color: #c0c0c0;

    span {
      cursor: pointer;
      margin-left: -12px;
      padding: 8px 12px 2px 12px;
      transition: padding 300ms;

      &:hover {
        background-color: var(--soft-white);
        margin-left: -14px;
        color: #242424;
        padding: 10px 14px 4px 14px;
        border-radius: 16px;
        font-weight: 500;
      }
    }
  }

  .letter-separator {
    color: #4b4b4b;
    font-weight: 500;
    font-size: 64px;
    margin: 42px 0 24px 0;
    text-transform: uppercase;
  }
`;