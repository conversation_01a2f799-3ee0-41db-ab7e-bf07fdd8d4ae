import { gql } from "@apollo/client";
import client from "api/apollo-client";
import { queries } from "api/gql-queries";
import styled from "styled-components";
import { topicSort } from "/utils/list.utils";
import { FirstArticle, SquareVertical } from "components/shared/Card";
import { device } from "styles/device";
import TopicsHorizontalList from "components/shared/topics-horizontal-list";
import TopicHeader from "components/categories/TopicHeader";
import SSRPaginate from "components/shared/pagination/ssr-paginate";
import { topicsPostsfetcher } from "utils/fetcher";
import { FilterTopicsString } from "utils/filterSearchString";
import { Featured } from "components/shared";
import HorizontalReversePostCard from "components/shared/Card/HorizontalReversePostCard";
import CornerStoneCard from "components/shared/Card/CornerStoneCard";
import { MeiliApi } from "api/meili-client";

const postPerPage = 15; // Number items per page

export default function PageTopic({ topics, fallback }) {
  const nbHits = fallback?.posts?.totalHits || 0;
  const posts = fallback?.posts?.hits || [];
  const cornerStones = fallback.cornerStones?.hits || [];
  let startIndexList = posts?.length > 3 ? 3 : 0;

  topics = topicSort(topics);

  const mainTopic = topics[0];

  const showSubTopics = (children) => {
    let show = false;
    for (const child of children) {
      if (child.postCount > 0) show = true;
    }
    return show;
  };

  return (
    <PageWrapper>
      <section className={"site-padding"}>
        <TopicHeader topic={mainTopic} topicName={mainTopic.name} type={"theme"}/>
      </section>
      {posts?.length > 3 && (
        <FirstSection className={"site-padding"}>
          <FirstArticle
            post={posts[0]}
            options={{
              showAuthor: true,
              dotColors: { back: "#F4F4F4" },
            }}
          />
          <SquareVertical
            post={posts[1]}
            options={{ showAuthor: true, dotColors: { back: "#F4F4F4" } }}
          />
          <SquareVertical
            post={posts[2]}
            options={{ showAuthor: true, dotColors: { back: "#F4F4F4" } }}
          />
        </FirstSection>
      )}

      {cornerStones[0] && (
        <section>
          <Featured content={cornerStones[0]} />
        </section>
      )}

      {mainTopic.children && showSubTopics(mainTopic.children) && (
        <section style={{ marginTop: "96px" }} className={"site-padding"}>
          <h2>Sous thèmes</h2>
          <TopicsHorizontalList topics={mainTopic.children} />
        </section>
      )}

      <SectionPosts className={"site-padding"}>
        <p className="label-type">Dernières ressources</p>
        <div className="posts-container ">
          <LeftContent>
            <ul className={"list-container"}>
              {posts?.slice(startIndexList)?.map((post, key) => {
                return (
                  <li key={`post-${key}`} className={"post-card-li"}>
                    <HorizontalReversePostCard
                      post={post}
                      options={{
                        showLead: true,
                        showDate: true,
                        showAuthor: true,
                      }}
                    />
                  </li>
                );
              })}
            </ul>
            <SSRPaginate
              nbHits={nbHits}
              baseUrl={`/categories/${mainTopic.slug}/ressources?page=`}
              currentPage={1}
              options={{
                postPerPage: postPerPage,
              }}
            />
          </LeftContent>
          <RightContent>
            <div className="cornerstone-container">
              {cornerStones[1] && (
                <CornerStoneCard
                  post={cornerStones[1]}
                  options={{
                    showAuthor: true,
                    showBlur: true,
                    aspectRatio: 16 / 9,
                  }}
                />
              )}
              {cornerStones[2] && (
                <CornerStoneCard
                  post={cornerStones[2]}
                  options={{
                    showAuthor: true,
                    showBlur: true,
                    aspectRatio: 16 / 9,
                  }}
                />
              )}
            </div>
          </RightContent>
        </div>
      </SectionPosts>

      {cornerStones[3] && (
        <section>
          <Featured content={cornerStones[3]} />
        </section>
      )}

      <PageBackground />
    </PageWrapper>
  );
}

export async function getStaticProps({ params }) {
  // noinspection DuplicatedCode
  const mainTopic = await client
    .query({
      query: queries.QUERY_TOPIC,
      variables: { slug: params.topic },
    })
    .then((response) => {
      return response.data.topics[0];
    });

  // Put all topic into an array
  let topics = [mainTopic];

  const childrenTopics = await client
    .query({
      query: QUERY_TOPIC_CHILDREN,
      variables: { id: mainTopic.id },
    })
    .then((response) => {
      return response.data.topicChildren;
    });

  topics.push(...childrenTopics);

  // Clear duplicate topics
  topics = topics.filter(
    (topic, index, self) =>
      self.findIndex((topic2) => topic2.id === topic.id) === index
  );

  //fetch posts
  const filterTopicsString = FilterTopicsString(topics);
  let posts = await topicsPostsfetcher({}, filterTopicsString, postPerPage);
  if (posts?.hits?.length === 0 || filterTopicsString.length === 0) {
    return {
      notFound: true,
    };
  }

  //fetch cornerStones
  const filterTopicsStringCornerStone = `cs=true AND (${filterTopicsString})`;

  const cornerStones = await MeiliApi.searchHighlight("", {
    filter: filterTopicsStringCornerStone,
    sort: ["date:desc"],
    limit: 4,
  });

  // Supprime les posts qui on les mêmes route que les cornerstones
  posts.hits = posts?.hits?.filter(
    (post) => !cornerStones?.hits?.find((cs) => cs?.route === post?.route)
  );

  return {
    revalidate: 10,
    props: {
      topics: topics,
      fallback: {
        posts,
        cornerStones,
      },
    },
  };
}

export async function getStaticPaths() {
  const topics = await client
    .query({
      query: QUERY_TOPIC_SLUGS,
    })
    .then((response) => {
      return response.data.topics;
    });
  return {
    paths: topics.map((topic) => ({
      params: {
        topic: topic.slug,
      },
    })),
    fallback: false,
  };
}

const PageWrapper = styled.div`
  position: relative;
  ul {
    padding: 0 0 0 0;
  }
`;

const FirstSection = styled.div`
  display: grid;
  grid-gap: 16px;
  grid-template-columns: repeat(2, 1fr);

  .fa-card {
    grid-column: 1/3;
  }

  @media ${device.desktop} {
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 32px;
  }
`;

const PageBackground = styled.div`
  position: absolute;
  width: 100vw;
  height: 100%;
  margin: auto;
  top: 275px;
  left: 0;
  background-color: #f4f4f4;
  z-index: -1;

  @media ${device.desktopXL} {
    top: 375px;
  }
`;

const SectionPosts = styled.section`
  margin-top: 96px;

  .posts-container {
    display: block;

    @media ${device.desktop} {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
`;
const LeftContent = styled.article`
  width: 100%;
  margin-bottom: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }
  .post-card-li {
    list-style: none;
    padding-right: 0;
  }

  @media ${device.desktop} {
    width: 66.7%;
    margin-bottom: 164px;
    .post-card-li {
      padding-right: 142px;
    }
  }
`;
const RightContent = styled.div`
  position: relative;
  width: 100%;

  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }
  @media ${device.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${device.desktop} {
    width: 33.3%;
  }
`;

const QUERY_TOPIC_SLUGS = gql`
  query {
    topics {
      slug
    }
  }
`;

const QUERY_TOPIC_CHILDREN = gql`
  query TopicChildren($id: ID!) {
    topicChildren(id: $id) {
      name
      id
      slug
      postCount
      parent {
        id
        slug
        name
      }
    }
  }
`;
