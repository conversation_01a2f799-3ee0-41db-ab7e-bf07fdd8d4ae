import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import { useState, useEffect } from "react";
import { device } from "styles/device";
import styled from "styled-components";
import { PageTitle } from "styles/styled-typography";
import Ticket from "components/webinars/Ticket/Ticket";
import { useRouter } from "next/router";
import StickyPagination from "components/shared/pagination/sticky-pagination/StickyPagination";
import HorizontalPostCard from "components/shared/Card/HorizontalPostCard";
import WebSelector from "components/webinars/WebSelector";
import AnimatedList from "components/shared/animation/AnimatedList";
import { PaginateContext } from "context/PaginateContext";
import { getChannelSlug, modulesAsObj } from "/utils/components.utils";
import Link from "next/link";
import { NextSeo } from "next-seo";

const postPerPage = 15;

function setInitialPage(page, totalItems) {
  const pageMax = Math.ceil(totalItems / postPerPage);
  let initalPage = 1;
  if (page > pageMax) {
    initalPage = pageMax;
  } else if (page > 1) {
    initalPage = page;
  }
  return initalPage;
}

export default function Webinars({ lastWepisode, wepisodeList, webinarList }) {
  const router = useRouter();
  const { query } = router;
  const page = +query.page || 1;

  /**
   * For AnimationList
   * init = Première animation
   * fetched = Seconde animation avec la nouvelle page
   * done = Animation fini / standby
   */
  const [animationTransition, setAnimationTransition] = useState({
    transitioning: "init", // init | fetched | done
    direction: null,
  });

  const [pageState, setPageState] = useState({
    totalItem: wepisodeList.length,
    maxPage: Math.ceil(wepisodeList.length / postPerPage),
    activePage: setInitialPage(page, wepisodeList.length),
    previousPage: null,
    activeChannel: "tous",
    postPerPage: postPerPage,
    loading: false,
  });

  // Actualise le numéro affiché pour la pagination
  const [nextNumber, setNextNumber] = useState(pageState.activePage);

  // Select N posts
  let start = (pageState.activePage - 1) * postPerPage;
  let end = start + postPerPage;

  let filteredList = wepisodeList
    .filter(
      (ep) =>
        pageState.activeChannel === "tous" ||
        getChannelSlug(ep, "webinar") ===
          pageState.activeChannel.replace(/ /g, "-")
    )
    .slice(start, end);

  function changeAnimationState(nextPage, animationState) {
    // rerender page and get new data
    setPageState((prevState) => ({
      ...prevState,
      previousPage: prevState.activePage,
      activePage: nextPage,
    }));
    // start second part of animation
    setAnimationTransition((prevState) => ({
      ...prevState,
      transitioning: animationState,
      direction: pageState.activePage - nextPage,
    }));
  }

  // Après 500ms, actualise la page avec les nouvelles données et
  // lance la deuxième partie de l'animation
  let transitionTimeout;

  /**
   * 'SetPage' est la fonction envoyée par le context de la pagination.
   * Elle permet de faire fonctionner la pagination.
   */
  const setPage = (nextPage) => {
    clearTimeout(transitionTimeout);
    if (
      nextPage >= 1 &&
      nextPage <= pageState.maxPage &&
      animationTransition.transitioning === "done"
    ) {
      setNextNumber(nextPage);
      setAnimationTransition((prevState) => {
        return {
          ...prevState,
          transitioning: "init",
          direction: pageState.activePage - nextPage,
        };
      });

      // start processus for fetch new data
      setPageState((prevState) => {
        return {
          ...prevState,
          loading: true,
        };
      });
      transitionTimeout = setTimeout(() => {
        changeAnimationState(nextPage, "fetched");
      }, 500);
    }
  };

  const getLink = (name) => {
    if (name === "1 soirée pour le glorifier")
      return "https://www.youtube.com/watch?v=SV4YdHJBVnA&list=PLU-GvOiiJadAszanRuxLu2hklSMmP6v2u&index=1";
    else if (name === "1 heure pour comprendre")
      return "https://www.youtube.com/watch?v=isvfJ2Pt6KI&list=PLU-GvOiiJadB0kgyxRu2_d-kgJEZgJ4Ew";
    else return "#";
  };

  useEffect(() => {
    // Remplace la query si ( page < 1 || page > max)
    if (+query?.page < 1 || +query?.page > pageState.maxPage) {
      let newPageQuery = +query?.page < 1 ? 1 : pageState.maxPage;
      router.replace(
        {
          pathname: router.pathname,
          query: { ...query, page: newPageQuery },
        },
        undefined,
        { scroll: false }
      );
    } else if (
      !+query?.page ||
      (pageState.loading === false && +query?.page > 1)
    ) {
      // Reset pageSate et nextNumber si !query.page ou que query.page est different alors que pageState n'a jamais changé
      setPageState((prevState) => {
        return {
          ...prevState,
          totalItem: wepisodeList.length,
          maxPage: Math.ceil(wepisodeList.length / postPerPage) || 1,
          activePage: setInitialPage(+query.page, wepisodeList.length),
          previousPage: null,
          activeChannel: "tous",
          postPerPage: postPerPage,
          loading: false,
        };
      });
      setNextNumber(setInitialPage(+query.page, wepisodeList.length));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query]);

  return (
    <>
      <NextSeo
        title={"Webinaires TPSG"}
        description={
          "Retrouve l'ensemble des webinaires du site toutpoursagloire.com"
        }
        openGraph={{
          title: "Webinaires TPSG",
          description:
            "Retrouve l'ensemble des webinaires du site toutpoursagloire.com",
          url: "https://toutpoursagloire.com/webinaires",
        }}
        twitter={{
          site: "@t_p_s_g",
          cardType: "summary_large_image",
        }}
      />
      <PageWrapper>
        <header className={"site-padding"}>
          <PageTitle>Webinaires</PageTitle>
        </header>
        {lastWepisode && <Ticket webEvent={lastWepisode} />}
        <MainContent className={"site-padding"}>
          <PaginateContext.Provider
            value={[
              pageState,
              setPageState,
              setPage,
              nextNumber,
              setNextNumber,
            ]}
          >
            <NavSection>
              <StickyPagination title={"Replays"}>
                <PageDescription>
                  <p>Retrouvez les webinaires de TPSG déjà passés.</p>
                  {webinarList.length > 0 &&
                    webinarList.map((webinar) => (
                      <>
                        <p>
                          <Link href={getLink(webinar.name)} target={"_blank"}>
                            <b>{webinar.name}</b>
                          </Link>{" "}
                          {webinar.description}
                        </p>
                      </>
                    ))}

                  <p>
                    Retrouvez aussi ces webinaires sur notre chaîne YouTube.
                  </p>
                </PageDescription>
                <WebSelector
                  webinars={webinarList}
                  label={"Webinaires:"}
                  wepisodes={wepisodeList} // need episodes (not filterEpisode)
                />
              </StickyPagination>
            </NavSection>
            <PostSection>
              <AnimatedList
                animationTransition={animationTransition}
                setAnimationTransition={setAnimationTransition}
              >
                <ul className="all-post-card">
                  {filteredList.map((wepisode, key) => (
                    <HorizontalPostCard
                      key={key}
                      post={wepisode}
                      options={{
                        showLead: true,
                        showDate: true,
                        showAuthor: true,
                      }}
                    />
                  ))}
                </ul>
              </AnimatedList>
            </PostSection>
          </PaginateContext.Provider>
        </MainContent>
      </PageWrapper>
    </>
  );
}

const PageWrapper = styled.div``;

const MainContent = styled.div`
  position: relative;
  @media ${device.tablet} {
    display: flex;
    flex-wrap: wrap;
  }
`;

const NavSection = styled.section`
  z-index: 1;
  position: relative;
  margin-top: 8px;

  @media ${device.tablet} {
    margin-top: 0;
    width: 28%;
  }
`;

const PostSection = styled.section`
  margin-top: 30px;
  margin-bottom: 100px;

  .all-post-card {
    padding: 0;
  }

  @media ${device.tablet} {
    margin-bottom: 0;
    border-top: none;
    margin-top: 0;
    width: 72%;
    //border-left: 1px solid #444444;
    display: flex;
    justify-content: flex-end;
    padding-top: 40px;

    .all-post-card {
      width: 92%;
    }

    &:before {
      content: none;
    }

    .animation-list {
      // AnimatedList css
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
    }
  }
`;

const PageDescription = styled.div`
  margin-top: 48px;
  margin-bottom: 48px;
  height: 225px;
  overflow-y: auto;

  p {
    font-size: 18px;
  }

  @media ${device.desktop} {
    margin-top: 16px;
    margin-bottom: 24px;
    margin-right: 16px;
    p {
      margin-top: 8px;
      margin-bottom: 0;
    }
  }
`;

export async function getStaticProps() {
  let wepisodes = await client
    .query({
      query: GET_WEBINAR_EPISODES,
      variables: { type: "webinaire" },
    })
    .then((response) => {
      return response.data.posts;
    });

  let channels = [];

  wepisodes.map((wepisode) => {
    let { webinar } = modulesAsObj(wepisode.modules);
    let channelSlug = getChannelSlug(wepisode, "webinar");
    if (channelSlug) {
      channels.findIndex((item) => item.slug === channelSlug) === -1 &&
        channels.push(webinar.webinar);
    }
  });

  const webinar = await client
    .query({
      query: GET_WEBINAR,
    })
    .then((response) => {
      return response.data.webinars;
    });

  wepisodes = wepisodes.sort(function (a, b) {
    return new Date(b.published_at) - new Date(a.published_at);
  });

  return {
    props: {
      lastWepisode: wepisodes[0],
      wepisodeList: wepisodes.slice(1, -1),
      webinarList: webinar,
    },
    revalidate: 10,
  };
}

// noinspection GraphQLUnresolvedReference
const GET_WEBINAR_EPISODES = gql`
  query WebinarEpisodes($type: String) {
    posts(where: { type: $type }, limit: 3000) {
      title
      slug
      type
      published_at
      body
      image {
        url
        width
        height
        provider
        caption
        alternativeText
      }
      modules {
        ... on ComponentModuleWebinar {
          __typename
          speakers {
            fullName
          }
          webinar {
            slug
            name
          }
        }
        ... on ComponentModuleEvent {
          __typename
          date
          url
        }
        ... on ComponentModuleLead {
          __typename
          content
        }
      }
    }
  }
`;

const GET_WEBINAR = gql`
  query {
    webinars {
      id
      name
      description
      slug
    }
  }
`;
