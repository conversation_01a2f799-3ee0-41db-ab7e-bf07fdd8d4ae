"use strict";(()=>{var e={};e.id=139,e.ids=[139],e.modules={399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7147:e=>{e.exports=require("fs")},2781:e=>{e.exports=require("stream")},1576:e=>{e.exports=require("string_decoder")},8946:(e,t,o)=>{o.r(t),o.d(t,{headerHooks:()=>v,originalPathname:()=>$,requestAsyncStorage:()=>_,routeModule:()=>f,serverHooks:()=>P,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>w});var r={};o.r(r),o.d(r,{GET:()=>GET,revalidate:()=>b}),o(8976);var a=o(884),l=o(6132),i=o(6876);let s=new i.ApolloClient({uri:"http://127.0.0.1:1337/graphql",cache:new i.InMemoryCache({addTypename:!1}),defaultOptions:{query:{fetchPolicy:"no-cache"}}});var n=o(6585),u=o(1912),p=o.n(u);function components_utils_modulesAsObj(e){if(!e)return null;let t={};return e.forEach(e=>{switch(e.__typename){case"ComponentModuleLead":t.lead=e;break;case"ComponentModuleWebinar":t.webinar=e;break;case"ComponentModuleEmailJourney":t.journey=e;break;case"ComponentModuleEvent":t.event=e;break;case"ComponentModuleFormation":t.formation=e;break;case"ComponentModuleSeo":t.seo=e;break;case"ComponentModulePodcast":t.podcast=e}}),t}i.gql`
  fragment postTypeModules on Post {
    modules {
      ... on ComponentModulePodcast {
        podcast {
          slug
          name
        }
      }
      ... on ComponentModuleFormation {
        __typename
        speakers {
          fullName
        }
        link
        youtubeEmbed
      }
    }
  }
`,i.gql`
    fragment CorePostFields on Post {
      id
      title
      body
      slug
      type
      readingTime
      author {
        fullName
        firstName
        lastName
        about
        slug
        picture {
          url
          width
          height
          provider
          alternativeText
        }
      }
      published_at
      image {
        url
        provider
        alternativeText
        caption
        width
        height
      }
      serie {
        id
        name
      }
      topics {
        name
        slug
      }
      blog {
        blogger {
          fullName
          slug
        }
      }
      modules {
        __typename
        ... on ComponentModuleLead {
          content
        }
        ... on ComponentModuleSeo {
          metaDescription
          metaTitle
        }
      }
    }
  `;let d=i.gql`
  fragment fullPostFragment on Post {
    id
    title
    slug
    type
    published_at
    body
    author {
      fullName
      picture {
        url
        provider
      }
    }
    image {
      url
      height
      width
      alternativeText
      provider
    }
    topics {
      name
    }
    modules {
      ... on ComponentModuleWebinar {
        __typename
        webinar {
          slug
          name
        }
        embedVideo
        speakers {
          fullName
          firstName
          lastName
          picture {
            url
            provider
            size
          }
        }
      }
      ... on ComponentModulePodcast {
        __typename
        podcast {
          slug
          name
        }
        embedAudio
        embedVideo
      }
      ... on ComponentModuleLead {
        __typename
        content
      }
    }
  }
`,m={QUERY_TOPIC:i.gql`
    query MainTopic($slug: String!) {
      topics(where: { slug: $slug }) {
        name
        slug
        id
        postCount
        description
        parent {
          slug
          name
          parent {
            slug
            name
          }
        }
      }
    }
  `,QUERY_TOPIC_GROUP:i.gql`
    query TopicGroup($slug: String!) {
      topicGroups(where: { slug: $slug }) {
        name
        description
        cover {
          formats
        }
        topics {
          id
          postCount
        }
        featured {
          title
          description
          inColumn
          image {
            url
            width
            height
            provider
            caption
            alternativeText
          }
        }
      }
    }
  `,QUERY_TOPICS_POSTS:i.gql`
    query Posts($topicIds: [ID], $offset: Int!) {
      posts(
        limit: 20
        start: $offset
        where: { topics: { id_in: $topicIds } }
      ) {
        title
        slug
        published_at
        image {
          url
          height
          width
          provider
          caption
          alternativeText
        }
        author {
          fullName
        }
        topics {
          slug
        }
        modules {
          __typename
          ... on ComponentModulePodcast {
            podcast {
              name
              slug
            }
          }
        }
        type
      }
    }
  `,QUERY_RELATED:i.gql`
    query GetRelated($id: ID!) {
      relatedPosts(id: $id) {
        section
        score
        origin
        post {
          id
          title
          slug
          type
          published_at
          author {
            fullName
            picture {
              url
              provider
            }
          }
          image {
            url
            height
            width
            alternativeText
            provider
          }
          topics {
            name
          }
          modules {
            __typename
            ... on ComponentModuleWebinar {
              webinar {
                slug
                name
              }
              speakers {
                fullName
                firstName
                lastName
                picture {
                  url
                  provider
                  size
                }
              }
            }
            ... on ComponentModulePodcast {
              podcast {
                slug
                name
              }
            }
            ... on ComponentModuleLead {
              content
            }
          }
        }
      }
    }
  `,QUERY_BLOG_POSTS:i.gql`
    ${d}
    query BlogPosts($blog: ID!, $limit: Int!, $sort: String!) {
      posts(limit: $limit, where: { blog: $blog }, sort: $sort) {
        ...fullPostFragment
      }
    }
  `,QUERY_POSTS:i.gql`
    ${d}
    query Posts($limit: Int!, $sort: String!) {
      posts(limit: $limit, sort: $sort) {
        ...fullPostFragment
      }
    }
  `};i.gql`
  fragment CorePostSet on Post {
    title
    slug
    type
    published_at
    topics {
      name
    }
    image {
      url
      width
      height
      alternativeText
      provider
    }
    author {
      fullName
      picture {
        url
        formats
      }
    }
    modules {
      __typename
      ... on ComponentModulePodcast {
        podcast {
          slug
          name
        }
      }
      ... on ComponentModuleFormation {
        speakers {
          fullName
        }
        link
        youtubeEmbed
      }
    }
  }
`,i.gql`
  query Popups {
    popups(sort: "published_at:desc") {
      id
      title
      body
      image {
        url
        provider
      }
      startDate
      endDate
      published_at
      button {
        name
        url
      }
    }
  }
`;var c=o(7147);let g="http://127.0.0.1:3000";async function generateMainFeed(){let e=await s.query({query:m.QUERY_POSTS,variables:{limit:10,sort:"published_at:DESC"}}).then(e=>e.data.posts),t=new Date,o={title:"TPSG",id:g,link:g,description:"ToutPourSaGloire.com, c'est des milliers de ressources pour vous aider \xe0 mener une vie qui glorifie Dieu",language:"fr",updated:t,copyright:`All rights reserved ${t.getFullYear()}, ToutPourSaGloire`,author:{name:"ToutPourSaGloire.com",email:"<EMAIL>"}},r=new n.f(o);e.forEach(e=>{r.addItem(preparePost(e))}),c.writeFileSync("./public/main-rss.xml",r.rss2())}async function generateBlogFeeds(e){let t={query:m.QUERY_BLOG_POSTS,variables:{limit:10,sort:"published_at:DESC"}};for(let o of e){t.variables.blog=o.id;let e=await s.query(t).then(e=>e.data.posts),r=new Date,a={title:o.blogger.fullName,id:`${g}/${o.slug}`,link:`${g}/${o.slug}`,description:`Blog de ${o.blogger.fullName} sur le site toutpoursagloire.com`,language:"fr",updated:r,copyright:`All rights reserved ${r.getFullYear()}, ToutPourSaGloire`,author:{name:o.blogger.fullname,email:`${o.blogger.fullName}@toutpoursagloire.com`}},l=new n.f(a);e.forEach(e=>{l.addItem(preparePost(e))});let i=`./public/${o.slug}-rss.xml`;c.writeFileSync(i,l.rss2())}}function preparePost(e){let t=new(p()).Converter().makeHtml(e.body.replace(/`/g,""));("webinaire"===e.type||"podcast"===e.type)&&(t=function(e){let t=components_utils_modulesAsObj(e.modules);return t.podcast?t.podcast.embedAudio||t.podcast.embedVideo:t.webinar?t.webinar.embedVideo:null}(e)+t);let o=components_utils_modulesAsObj(e.modules),r=o?.lead?.content?o.lead.content:"";return r=r.replace(/(?:_|[*#])|\[(.*?)\]\(.*?\)/gm,"$1"),{title:e.title,link:`${g}${function(e){if(e.route)return e.route.startsWith("/")?e.route:"/"+e.route;let t=e.type||"undefined";switch(t){case"article":default:return`/article/${e.slug}`;case"webinaire":return`/webinaires/${e.slug}`;case"podcast":let{podcast:o}=components_utils_modulesAsObj(e.modules);if(!o?.podcast)return null;return`/podcasts/${o.podcast.slug}/${e.slug}`;case"formation":return e.modules?.formation?.link||`/article/${e.slug}`;case"parcours":return`/parcours-emails/${e.slug}`}}(e)}`,content:t,description:function(e){let t=e,o=e.split(" ").length-1;return o>50&&(t=e.split(" ").splice(0,50).join(" ")+"..."),t}(r),date:new Date(e.published_at)}}let b=3600;async function GET(){var e;let t=await s.query({query:h});return e=t.data,generateMainFeed(),generateBlogFeeds(e.blogs),Response.json({updatedAt:new Date(Date.now()).toString(),...t.data})}let h=i.gql`
  query GetCoreData{
    authors{
      fullName,
      firstName,
      lastName,
    }
    blogs{
      id
      slug
      blogger{
        firstName
        lastName
        fullName
        picture {
          provider
          url
        }
      }
    }
    topics{
      name
      slug
      postCount
    }
    podcasts{
      name
      slug
    }
    topicGroups{
      name
      slug
      type
      children {
        name
        slug
        type
      }
    }
  }
`,f=new a.AppRouteRouteModule({definition:{kind:l.x.APP_ROUTE,page:"/api/coredata/route",pathname:"/api/coredata",filename:"route",bundlePath:"app/api/coredata/route"},resolvedPagePath:"C:\\rep\\TPSG\\tpsg-next\\app\\api\\coredata\\route.js",nextConfigOutput:"",userland:r}),{requestAsyncStorage:_,staticGenerationAsyncStorage:y,serverHooks:P,headerHooks:v,staticGenerationBailout:w}=f,$="/api/coredata/route"}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),o=t.X(0,[4286],()=>__webpack_exec__(8946));module.exports=o})();