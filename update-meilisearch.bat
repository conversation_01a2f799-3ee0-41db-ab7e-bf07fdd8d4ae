@echo off
echo Mise à jour de Meilisearch vers la version 0.30.0...
echo.
echo Note: Bien que le serveur Meilisearch en préproduction soit en version 0.30.5,
echo le package npm disponible est en version 0.30.0.
echo.

REM Aller dans le répertoire du projet Next.js
cd tpsg-next

REM Installer les dépendances avec la nouvelle version de Meilisearch
echo Installation des dépendances...
call npm install

REM Configurer Meilisearch v0.30.x
echo.
echo Configuration de Meilisearch v0.30.x...
cd ..
call node config-meilisearch-v0.30.5.js

echo.
echo Mise à jour terminée !
echo.
echo Pour démarrer l'application Next.js:
echo 1. Assurez-vous que Meilisearch v0.30.5 est en cours d'exécution (utilisez start-meilisearch-v0.30.5.bat)
echo 2. Allez dans le répertoire tpsg-next et exécutez: npm run dev
echo.
echo Appuyez sur une touche pour fermer cette fenêtre...
pause > nul
