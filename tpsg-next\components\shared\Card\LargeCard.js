import styled from "styled-components";
import { removeHtml, removeMarkdown } from "../../../utils/string.utils";
import { device } from "../../../styles/device";
import Link from "next/link";
import CondImage from "../condimage";

export default function LargeCard({ post, theme }) {

  let lead = removeMarkdown(removeHtml(post.lead));

  return (
    <Link href={post.link}>
      <LargeCardWrapper theme={theme}>
        <div className={"lc-image"}>
          <CondImage
            imageData={post.image}
            preserveAspectRatio={false}
            sizes={"(max-width: 768px) 100vw, (max-width: 1220px) 33vw, 25vw"}
          />
        </div>
        <div className={"lc-text"}>
          <div className={"lc-content"}>
            <h3 className={"lc-title"}>{post.title}</h3>
            <p className={"lc-lead"}>{lead}</p>
          </div>
          {/* <div className={"lc-meta"}>
            <p className={"lc-date"}>{post.date}</p>
           </div> */}
        </div>
      </LargeCardWrapper>
    </Link>
  )
}


const LargeCardWrapper = styled.div`
  position: relative;
  width: 100%;
  border: 1px solid ${(p) => ( p.theme === "dark" ? "rgba(250,247,243,0.2)" : "rgba(22,22,22,0.2)" )}; // TODO: var here
  
  .lc-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    overflow: hidden;
    background-color: #F9F1E6;
    background-image: url(/images/tpsg-logo.svg);
    background-repeat: no-repeat;
    background-position: center;
  }
  
  .lc-text {
    display: flex;
    height: 360px;
    flex-direction: column;
    justify-content: space-between;
    padding: clamp(1.5rem, 0.112rem + 2.98vw, 2.5rem);
  }
  
  .lc-title {
    position: relative;
    color: ${p => p.theme === "dark" ? "#FFFFFF" : "#161616"};
    font-family: Stelvio, sans-serif;
    font-weight: 400;
    font-size: clamp(1.75rem, 1.4029850746268657rem + 0.7462686567164178vw, 2rem);
    line-height: clamp(2rem, 1.4794776119402986rem + 1.1194029850746268vw, 2.375rem);
    margin-bottom: -4px;
    margin-top: 6px;
  }
  
  .lc-lead {
    margin-top: 12px;
    font-family: Switzer, sans-serif;
    font-size: clamp(0.875rem, 0.53rem + 0.75vw, 1.125rem);
    line-height: clamp(1.375rem, 0.8544776119402986rem + 1.1194029850746268vw, 1.75rem);
    color: #989AA4;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    font-weight: 400;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  
  // .lc-date {
  //   font-family: Switzer, sans-serif;
  //   font-size: 14px;
  //   line-height: 20px;
  //   color: #989AA4;
  //   margin-bottom: 0;
  //   text-transform: uppercase;
  // }
  
  @media ${ device.desktop } {
    &:hover {
      border-color: var(--brand-color);
      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);
      box-shadow: 0 0 0 1px var(--c-brand-lighter);
      cursor: pointer;
    }
  }
  
`;
