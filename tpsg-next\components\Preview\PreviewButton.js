import styled from "styled-components";
import { useState } from "react";

const baseUrl = "https://toutpoursagloire.com";

export default function PreviewButton({ url }) {

  const [message, setMessage] = useState("");

  const copyUrl = () => {
    navigator.clipboard.writeText(`${baseUrl}${url}`);
    setMessage("Copié!");
		  setTimeout(function(){
						    setMessage("");
		  }, 3000);
  }

  return (
    <Wrapper>
		      <p>Preview</p>
				    <ClipButton onClick={() => copyUrl()}>
						    <span>{baseUrl}{url}</span>
						    <div className={"clip-icon"}>
								    <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
										    <path fillRule="evenodd" clipRule="evenodd"
										          d="M4.14492 0.315878C4.24373 0.122287 4.44572 0 4.66667 0H7C7.22095 0 7.42294 0.122287 7.52175 0.315878L7.94385 1.14286H9.91667C10.8832 1.14286 11.6667 1.91037 11.6667 2.85714V4C11.6667 4.31559 11.4055 4.57143 11.0833 4.57143C10.7612 4.57143 10.5 4.31559 10.5 4V2.85714C10.5 2.54155 10.2388 2.28571 9.91667 2.28571H9.33333V4C9.33333 4.31559 9.07217 4.57143 8.75 4.57143H2.91667C2.5945 4.57143 2.33333 4.31559 2.33333 4V2.28571H1.75C1.42783 2.28571 1.16667 2.54155 1.16667 2.85714V13.1429C1.16667 13.4585 1.42783 13.7143 1.75 13.7143H4.08333C4.4055 13.7143 4.66667 13.9701 4.66667 14.2857C4.66667 14.6013 4.4055 14.8571 4.08333 14.8571H1.75C0.783507 14.8571 0 14.0897 0 13.1429V2.85714C0 1.91037 0.783501 1.14286 1.75 1.14286H3.72281L4.14492 0.315878ZM3.5 2.28571V3.42857H8.16667V2.28571H3.5ZM5.83333 7.42857C5.83333 6.48178 6.61682 5.71429 7.58333 5.71429H12.25C13.2165 5.71429 14 6.48178 14 7.42857V14.2857C14 15.2325 13.2165 16 12.25 16H7.58333C6.61682 16 5.83333 15.2325 5.83333 14.2857V7.42857Z"
										          fill="#080808"/>
								    </svg>
						    </div>
				    </ClipButton>
		      { message !== "" && <p className={"preview-message"}>{message}</p> }
    </Wrapper>
  )
}

const Wrapper = styled.div`
		font-family: Switzer, sans-serif;
  position: fixed;
  bottom: 0;
  left: 0;
  height: 60px;
  width: 100%;
  background-color: #9fe7d3;
  border: 54px;
  display: flex;
  flex-display: row;
  align-items: center;
		padding: 0 30px;
  background-image: linear-gradient(45deg, #aaebda 25%, #9fe7d3 25%, #9fe7d3 50%, #aaebda 50%, #aaebda 75%, #9fe7d3 75%, #9fe7d3 100%);
  background-size: 33.94px 33.94px;		

  p {
    color: 080808;
    font-size: 18px;
    font-weight: 600;
		  margin: 0;
  }
		
		.preview-message {
				margin-left: 12px;
    background-color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
				font-weight: 400;
				height: 30px;
				line-height: 30px;
				padding: 0 18px;
				border-radius: 30px;
  }

  z-index: 1000;
`

const ClipButton = styled.div`

  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.4);
  border-radius: 60px;
  padding: 0 18px;
  height: 30px;
  margin-left: 18px;

  span {
    font-size: 12px;
    font-weight: 500;
    color: rgba(8, 8, 8, 0.6);
		  letter-spacing: 0.2px;
  }

  .clip-icon {
    margin-left: 12px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
  }

  &:hover {
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.7);
  }
`;