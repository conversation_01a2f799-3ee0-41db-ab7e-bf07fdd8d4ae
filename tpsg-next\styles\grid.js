import { device } from "./device"
import styled from "styled-components";

/**
 *  [PROPS]
 *  col: nombre de colones pour les écrans > mobile
 */
export const Grid = styled.div`
  display: grid;

  // Mobile Grid : 2 Col
  grid-template-columns: repeat(2, 1fr);
  column-gap: var(--mobile-gap);

  // Tablet Grid : 6 col (default)
  @media ${device.tablet} {
    grid-template-columns: repeat(${p => p.col ? p.col : 6}, 1fr);
    column-gap: var(--tablet-gap);
  }
  // Tablet Grid : 8 col (default)
  @media ${device.desktop} {
    grid-template-columns: repeat(${p => p.col ? p.col : 8}, 1fr);
    column-gap: var(--tablet-gap);
  }
`
