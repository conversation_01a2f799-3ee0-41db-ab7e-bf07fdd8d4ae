// noinspection GraphQLUnresolvedReference

import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import styled from "styled-components";
import { device } from "styles/device";
import { MeiliApi } from "/api/meili-client";
import { useRouter } from "next/router";
import { useCoreData } from "context/CoreDataContext";
import { getMeiliParamsPaginate } from "utils/query.utils";
import useSWR, { SWRConfig } from "swr";

import CondImage from "components/shared/condimage";

import { useState, useEffect } from "react";
import HorizontalPostCard from "components/shared/Card/HorizontalPostCard";
import StickyPagination from "components/shared/pagination/sticky-pagination/StickyPagination";
import { PodcastDescriptionData } from "components/podcast/PodcastDescriptionData";
import { PaginateContext } from "context/PaginateContext";
import AnimatedList from "components/shared/animation/AnimatedList";
import Link from "next/link";
import { NextSeo } from "next-seo";
import { withRealSrc } from "utils/image-utils";

const postPerPage = 15; // Number items per page

function fetcher(query) {
  return MeiliApi.search("", {
    ...getMeiliParamsPaginate(query, postPerPage, "podcast"),
    sort: ["date:desc"]
  });
}

function setInitialPage(page, episodesData) {
  let initalPage = 1;
  if (page > episodesData?.totalPages) {
    initalPage = episodesData?.totalPages;
  } else if (page > 1) {
    initalPage = page;
  }
  return initalPage;
}

export default function Podcast({ podcast, fallback }) {
  const { blogs } = useCoreData()
  const nbHits = fallback?.episodes?.totalHits || 0;
  const router = useRouter();
  const { query } = router;

  const [pageState, setPageState] = useState({
    totalItem: nbHits,
    maxPage: fallback?.episodes?.totalPages || 1,
    activePage: setInitialPage(+query.page, fallback?.episodes),
    previousPage: null,
    activeChannel: "tous",
    postPerPage: postPerPage,
    loading: false,
  });

  // 'nextNumber' est utilisé pour afficher à l'avance la page suivante dans la pagination
  const [nextNumber, setNextNumber] = useState(pageState.activePage);

  // Fetch meilisearch
  const filteredList = useSWR(
    { ...{ page: pageState.activePage }, tag: `channel.${podcast.slug}` },
    fetcher
  )?.data?.hits;

  /**
   * AnimationList
   * init = Première animation
   * fetched = Seconde animation avec la nouvelle page
   * done = Animation fini / standby
   */
  const [animationTransition, setAnimationTransition] = useState({
    transitioning: "init", // init | fetched | done
    direction: null,
  });

  function changeAnimationState(nextPage, animationState) {
    // rerender page and get new data
    setPageState((prevState) => {
      return {
        ...prevState,
        previousPage: prevState.activePage,
        activePage: nextPage,
        loading: true,
      };
    });
    // start second part of animation
    setAnimationTransition((prevState) => {
      return {
        ...prevState,
        transitioning: animationState,
        direction: pageState.activePage - nextPage,
      };
    });
  }

  /**
   * Actualise la page après 500ms avec la nouvelle data + seconde partie de l'animation
   */
  let transitionTimeout;
  /**
   * 'SetPage' est la fonction envoyée par le context de la pagination.
   * Elle permet de faire fonctionner la pagination.
   */
  const setPage = (nextPage) => {
    clearTimeout(transitionTimeout);
    if (
      nextPage >= 1 &&
      nextPage <= pageState.maxPage &&
      animationTransition.transitioning === "done"
    ) {
      setNextNumber(nextPage);
      setAnimationTransition((prevState) => {
        return {
          ...prevState,
          transitioning: "init",
          direction: pageState.activePage - nextPage,
        };
      });

      // start processus for fetch new data
      setPageState((prevState) => {
        return {
          ...prevState,
          loading: true,
        };
      });
      transitionTimeout = setTimeout(() => {
        changeAnimationState(nextPage, "fetched");
      }, 500);
    }
  };

  useEffect(() => {
    // Remplace la query si ( page < 1 || page > max)
    if (+query?.page < 1 || +query?.page > pageState.maxPage) {
      let newPageQuery = +query?.page < 1 ? 1 : pageState.maxPage;
      router.replace(
        {
          pathname: router.pathname,
          query: { ...query, page: newPageQuery },
        },
        undefined,
        { scroll: false }
      );
    } else if (!+query?.page || (pageState.loading===false && +query?.page>1 )) {
      // Reset pageSate et nextNumber si !query.page ou que query.page est different alors que pageState n'a jamais changé
      setPageState((prevState) => {
        return {
          ...prevState,
          totalItem: nbHits,
          maxPage: fallback?.episodes?.totalPages || 1,
          activePage: setInitialPage(+query.page, fallback?.episodes),
          previousPage: null,
          activeChannel: "tous",
          postPerPage: postPerPage,
          loading: false,
        };
      });
      setNextNumber(setInitialPage(+query.page, fallback?.episodes));
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query]);

  const renderSpeakerLinks = () => {

    if(podcast.speakers.length > 3) {
      return "";
    }

    return podcast.speakers.map((speaker, key) => {
      const isBlogger = !!blogs.find(blog => blog.slug === speaker.slug);

      let route = isBlogger
        ? `/blog/${speaker.slug}`
        : `/recherche?author=${speaker.fullName}`;

      let separator = key > 0 ?
        key === podcast.speakers.length - 1 ? " et " : ", " :
        podcast.speakers.length < 3 ? "Par " : "";

      return (
        <>
          {separator}
          <Link key={key} href={route}>
            <span className={"speakers-name"}>{speaker.fullName}</span>
          </Link>
        </>
      );
    });
  };

  return (
    <SWRConfig value={{ fallback }}>
      <NextSeo
        title={`Podcast ${podcast.name}`}
        description={podcast?.description}
        openGraph={{
          title: `Podcast ${podcast.name}`,
          description: podcast?.description,
          url: `https://toutpoursagloire.com/podcast/${podcast.slug}`,
          images: [{
            url: withRealSrc(podcast.cover),
            alt: "",
          }],
        }}
        twitter={{
          site: "@t_p_s_g",
          cardType: "summary_large_image",
        }}
      />
      <PageWrapper className="site-padding">
        <ContentHeader>
          <TitlePodcast>
            <Link href={"/podcasts"}>
              <p className={"podcast-category"}>PODCAST &nbsp;/</p>
            </Link>
            <h1 className={"podcast-title"}>{podcast.name}</h1>
            <Speakers>
              { renderSpeakerLinks() }
            </Speakers>
          </TitlePodcast>
          <CoverWrapper>
            <CondImage imageData={podcast.cover} />
          </CoverWrapper>
        </ContentHeader>

        <MainContent>
          <PaginateContext.Provider
            value={[pageState, setPageState, setPage, nextNumber]}
          >
            <NavSection>
              <StickyPagination url={query.podcast} title={""}>
                <PodcastDescriptionData content={{ post: podcast, nbHits }} />
              </StickyPagination>
            </NavSection>
            <PostSection>
              <AnimatedList
                animationTransition={animationTransition}
                setAnimationTransition={setAnimationTransition}>
                <ul className="podcast-all-card">
                  {filteredList?.map((episode, key) => (
                    <HorizontalPostCard
                      key={key}
                      post={episode}
                      options={{
                        showLead: true,
                        showDate: true,
                        showAuthor: true,
                      }}
                      link={`${podcast.slug}/${episode.slug}`}
                    />
                  ))}
                </ul>
              </AnimatedList>
            </PostSection>
          </PaginateContext.Provider>
        </MainContent>
      </PageWrapper>
    </SWRConfig>
  );
}

const PageWrapper = styled.div`
  .podcast-secondary-text {
    font-size: 12px;
    opacity: 50%;
  }

  .mobile-hide {
    display: none;
  }

  @media ${device.tablet} {
    .mobile-hide {
      display: block;
    }

    .podcast-secondary-text {
      font-size: 17px;
    }
  }
`;

const Speakers = styled.p`
  font-size: 17px;
  font-family: Switzer, sans-serif;
  .speakers-by {
    font-weight: bold;
  }
  .speakers-name {
    font-weight: normal;
    text-decoration: underline;
    &:hover {
      color: var(--brand-color);
    }
  }
`;

const ContentHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 50px;
  flex-wrap: wrap;
  flex-direction: column-reverse;

  @media ${device.tablet} {
    flex-direction: row;
    margin-bottom: 50px;
  }
`;


const TitlePodcast = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 24px;
  align-self: flex-start;
  width: 100%;


  .podcast-category {
    display: none;
    opacity: 50%;
    font-size: 20px;
    margin-bottom: 40px;
  }

  .podcast-title {
    margin: 0;
    font-weight: 500;
    font-size: clamp(32px, calc(12px + 9vw), 144px);
    line-height: 90%;
  }

  @media ${device.tablet} {
    // Little screen only
    width: 60%;
    margin-top: 0;
    align-self: flex-end;

  }
  @media ${device.tablet} {
    .podcast-category {
      display: block;
    }
  }
  @media ${device.desktop} {
    align-self: flex-start;
  }
`;
const CoverWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  aspect-ratio: 16/9;
  margin-bottom: 32px;

  @media ${device.tablet} {
    width: 30%;
    height: 100%;
    aspect-ratio: 1/1;
    margin-bottom: 0;
  }
`;

const MainContent = styled.div`
  position: relative;
  @media ${device.tablet} {
    display: flex;
    flex-wrap: wrap;
    margin: 0;
    border-top: 1px solid #444444;
    border-bottom: none;
  }
`;

const NavSection = styled.div`
  z-index: 1;
  position: relative;
  @media ${device.tablet} {
    width: 28%;
  }
`;

const PostSection = styled.div`
  margin-top: 30px;
  margin-bottom: 100px;

  .podcast-all-card {
    padding: 0;
  }

  .show-episode {
    font-size: 24px;
    font-weight: bold;
  }

  @media ${device.tablet} {
    margin-bottom: 0;
    border-top: none;
    margin-top: 0;
    width: 72%;
    border-left: 1px solid #444444;
    display: flex;
    justify-content: flex-end;
    padding-top: 40px;

    .podcast-all-card {
      width: 92%;
    }

    .show-episode {
      display: none;
    }

    .animation-list {
      // AnimatedList css
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-end;
    }
  }
`;

export async function getStaticProps({ params }) {
  const podcast = await client
    .query({
      query: GET_PODCAST,
      variables: { slug: params.podcast },
    })
    .then((response) => {
      return response.data.podcasts[0];
    });

  const episodes = await fetcher({ tag: `channel.${podcast.slug}` });

  return {
    props: {
      podcast: podcast,
      fallback: {
        episodes: episodes,
      },
    },
    revalidate: 10,
  };
}

export async function getStaticPaths() {
  const podcasts = await client
    .query({ query: GET_PODCAST_SLUGS })
    .then((response) => {
      return response.data.podcasts;
    });
  return {
    paths: podcasts.map((podcast) => ({
      params: {
        podcast: podcast.slug,
        page: "1",
      },
    })),
    fallback: false,
  };
}

const GET_PODCAST_SLUGS = gql`
  query {
    podcasts {
      slug
    }
  }
`;

const GET_PODCAST = gql`
  query Podcast($slug: String) {
    podcasts(where: { slug: $slug }) {
      description
      name
      slug
      platforms {
        name
        url
      }
      speakers {
        fullName
        slug
      }
      cover {
        url
        provider
      }
      count
      averageDuration
    }
  }
`;
