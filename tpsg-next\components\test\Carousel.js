import styled from "styled-components";
import { useRef, useState } from "react";
import { device } from "styles/device";
import { AnimatedArrowButton, AnimatedTextButton } from "components/shared/Buttons";

/**
 * @param {object} options
 * @param {string} options?.seeMoreUrl
 * @param {string} options?.seeMoreText
 * @param {string} options?.theme
 */
export default function Carousel({ options, children }) {

  const carouselRef = useRef(null);
  const [page, setPage] = useState(0);
  const [buttonStates, setButtonStates] = useState([false,true]);

  const moveForward = (e) => {
    setPage(page + 1);
    handleChangePage(page + 1);
    e.preventDefault()
    carouselRef.current.scrollBy({
      left: carouselRef.current.clientWidth,
      top: 0,
      behavior: "smooth",
    })
  }

  /**
   * @param {event} e
   * @param {number} direction (-1 || +1) gauche ou droite
   */
  function move(e, direction) {
    setPage(page + direction);
    handleChangePage(page + direction);
    e.preventDefault()
    carouselRef.current.scrollBy({
      left: carouselRef.current.clientWidth * direction,
      top: 0,
      behavior: "smooth",
    })
  }

  const moveBackward = (e) => {
    setPage(page - 1);
    handleChangePage(page - 1);
    e.preventDefault()
    carouselRef.current.scrollBy({
      left: -carouselRef.current.clientWidth,
      top: 0,
      behavior: "smooth",
    })
  }

  function handleChangePage(nextValue){
    let maxPage = Math.round(8 / Math.trunc(carouselRef.current.clientWidth / 280));
    if(nextValue === 0){
      setButtonStates([false, true]);
    }
    else if(nextValue >= maxPage -1) {
      setButtonStates([true, false])
    } else {
      setButtonStates([true, true]);
    }
  }

  function handleScroll(e) {
    let scrollLeft = carouselRef.current.scrollLeft;
    let scrollLeftMax = carouselRef.current.scrollLeftMax;
    if(scrollLeft + 50 >= scrollLeftMax) {
      setButtonStates([true, false]);
    } else if(scrollLeft <= 50) {
      setButtonStates([false, true]);
    } else {
      setButtonStates([true,true]);
    }
  }

  return (
    <CarouselWrapper>
      <CardsWrapper ref={carouselRef} onScroll={(e) => handleScroll(e)}>
        {children}
      </CardsWrapper>
      <CarouselButtons>
        <div>
          { options?.seeMoreText &&
            <AnimatedTextButton
              text={options.seeMoreText}
              link={options.seeMoreUrl}
              theme={"light"}/>
          }
        </div>
        <ArrowsWrapper>
          <AnimatedArrowButton
            disabled={!buttonStates[0]}
            theme={options?.theme || "dark"}
            reverse={true}
            onClickFunction={(e) => move(e, -1)}/>
          <AnimatedArrowButton
            disabled={!buttonStates[1]}
            theme={options?.theme || "dark"}
            reverse={false}
            onClickFunction={(e) => move(e, 1 )}/>
        </ArrowsWrapper>
      </CarouselButtons>
    </CarouselWrapper>
  )
};

const CarouselWrapper = styled.div`
  position: relative;
  width: 100%;
  overflow-y: visible;
  @media ${device.desktop} {
    padding: 0 var(--border-space);
  }
`;

const CardsWrapper = styled.div`
  display: flex;
  flex-direction: row;
  overflow-x: scroll;
  scroll-snap-type: x mandatory;
  overflow-y: visible;
  margin-bottom: 24px;

  > * {
    scroll-snap-align: start;
  }
  
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;
`;

const CarouselButtons = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 12px;
  padding: 0 var(--border-space);

  @media ${device.desktop} {
    padding: 0;
  }
`;

const ArrowsWrapper = styled.div`
  display: flex;
  justify-content: right;
  align-items: center;
  gap: 12px;
`;