# Check if NVM is installed
$nvmPath = "C:\Program Files\nvm"
if (-not (Test-Path $nvmPath)) {
    Write-Host "NVM not found at $nvmPath" -ForegroundColor Red
    exit 1
}

# Check if Node.js 14 is installed
$node14Path = Join-Path $nvmPath "v14.21.3"
if (-not (Test-Path $node14Path)) {
    Write-Host "Node.js 14.21.3 not found at $node14Path" -ForegroundColor Red
    exit 1
}

# Create a batch file to use Node.js 14
$batchContent = @"
@echo off
echo Setting up Node.js 14.21.3...
set "PATH=$node14Path;%PATH%"
echo Current Node.js version:
node -v
echo.
echo You can now use Node.js 14 in this command prompt.
"@

$batchPath = Join-Path (Get-Location) "use-node14.bat"
$batchContent | Out-File -FilePath $batchPath -Encoding ascii

Write-Host "Created batch file: $batchPath" -ForegroundColor Green
Write-Host "Run this batch file to use Node.js 14.21.3" -ForegroundColor Yellow
