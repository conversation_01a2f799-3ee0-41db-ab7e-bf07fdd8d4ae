import styled from "styled-components";
import Link from "next/link";
import { useContext, useState } from "react";
import ChevronDown from "components/svg/chevron-down";
import { HeaderContext } from "context/HeaderContext";
import { device } from "../../../../styles/device";


export default function Blogs({ title, data, row }) {

  const [openedList, setOpenedList] = useState("");
  const { toggleMenu } = useContext(HeaderContext)


  return(
    <Wrapper aria-label={title} row={row}>
      { data.map((voc, key) =>
        <li className={"parent-list"} key={key}>
          <ListRow>
            <Link href={voc.route}>
              <p onClick={() => toggleMenu()}>{voc.name}</p>
            </Link>
            <div
              onClick={() => setOpenedList(voc.name === openedList ? null : voc.name)}
              className={"chevron-icon"}>
              <ChevronDown/>
            </div>
          </ListRow>
          <HiddenList className={"hidden-list"}
            itemCount={voc.children.length}
            show={openedList !== `${voc.name}`}>
            { voc.children.map((child, key) =>
              <li key={"hidden-list-" + key}>
                <ListRow>
                  <Link href={child.route}>
                    <p onClick={() => toggleMenu()}>{child.name}</p>
                  </Link>
                </ListRow>
              </li>
            )}
          </HiddenList>
        </li>
      )}
    </Wrapper>
  )
}

const Wrapper = styled.ul`
  grid-row: 1;
  grid-column: 1/3;
  margin-bottom: 64px;
  padding: 0;
  list-style: none;

  :before{
    display: block;
    position: relative;
    content:attr(aria-label);
    font-family: "Switzer", serif;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 0.5px;
    font-weight: 400;
    margin-bottom: 24px;
    color: var(--c-cream-A40);
  }

  .li {
    list-style: none;
  }
`

const ListRow = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: -1px;
  border-top: 1px solid #1C2E33;
  border-bottom: 1px solid #1C2E33;

  p {
    font-family: "Switzer", "Helvetica Neue", Helvetica, sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--c-cream);

    &:before {
      margin-right: 24px;
      left: 0;
      top: 0;
      content: "";
      display: inline-block;
      width: 14px;
      height: 14px;
      background-color: var(--c-cream-A20);
      border-radius: 14px;
    }

    &:hover {
      cursor: pointer;

      &:before {
        background-color: #fa7051;
      }
    }
  }

  .chevron-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 48px;
    width: 32px;
    border-radius: 26px;

    svg {
      width: 14px;
      height: 14px;

      path {
        stroke: var(--c-cream);
      }
    }

    &:hover {
      cursor: pointer;

      path {
        stroke: #fa7051;
        stroke-width: 2.4;
      }
    }
  }
  
  @media ${device.desktop} {
    width: 110%;
  }
`;

const HiddenList = styled.ul`
  height: ${props => props.show ? 0 : props.itemCount * 57}px;
  transition: 350ms ease-in-out;
  padding-left: 38px;
  overflow: hidden;
  list-style: none;
  @media ${ device.desktop } {
    width: 110%;
  }
`;