import Link from "next/link";
import styled from "styled-components";
import { device } from "styles/device";
import { useInView } from "react-intersection-observer";
import { useEffect, useState } from "react";

const NumbersSVG = () => {
  return (
    <svg width="1400" height="200" viewBox="0 0 1400 200" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path opacity="0.01" fillRule="evenodd" clipRule="evenodd" d="M0 0H1400V200H0V0Z" fill="#D9D9D9"/>
      <path
        d="M104.18 14H119V186H94.9798V48.01L93.7504 47.7147C92.1206 50.9085 89.0265 53.3544 85.2076 55.2186C81.3947 57.0797 76.9229 58.3295 72.6351 59.1661C68.3516 60.002 64.2771 60.4206 61.2715 60.6299C59.7694 60.7346 58.5363 60.7868 57.6798 60.8129C57.4165 60.821 57.1889 60.8265 57 60.8303V39.134C58.3775 39.3073 59.5765 39.3072 60.7392 39.3072H60.7533C81.8748 39.3072 99.2009 31.1452 104.18 14Z"
        stroke="#333333"/>
      <path
        d="M307.465 139.468L307.463 139.469L278.462 165.653L277.208 166.786H278.901H362V186H237V172.371L285.019 130.996C297.746 120.074 309.18 110.181 317.424 99.9347C325.68 89.674 330.78 79.0069 330.78 66.5286C330.78 55.7471 327.763 47.6555 322.003 42.2665C316.251 36.8842 307.865 34.2957 297.332 34.2957C284.372 34.2957 274.277 40.2097 268.059 49.4299C261.972 58.4548 259.623 70.6087 261.857 83.424H239.874C236.414 64.086 240.475 46.7423 250.577 34.2405C260.768 21.6292 277.168 13.8678 298.412 14.0017H298.416C316.323 14.0017 330.72 18.9621 340.633 27.8708C350.539 36.7725 356.037 49.6782 356.037 65.7188C356.037 80.6471 350.018 93.604 340.923 105.58C331.819 117.568 319.671 128.527 307.465 139.468Z"
        stroke="#333333"/>
      <path
        d="M537.102 97.0419V97.042L535.845 97.7305L537.189 98.2275V98.2276L537.192 98.2286L537.205 98.2333L537.258 98.2539C537.306 98.2727 537.38 98.3014 537.477 98.3405C537.669 98.4186 537.955 98.5379 538.32 98.7003C539.051 99.0254 540.1 99.5225 541.359 100.209C543.88 101.582 547.24 103.708 550.599 106.714C557.311 112.722 564 122.229 564 136.304C564 155.287 555.789 167.667 543.516 175.334C531.199 183.027 514.745 186 498.278 186C478.288 186 462.801 180.794 452.282 170.906C441.89 161.137 436.255 146.711 436 127.948H459.82C460.039 140.679 462.84 150.207 469.02 156.568C475.311 163.041 484.973 166.106 498.542 166.106C509.253 166.106 519.455 165.252 526.986 161.09C530.77 158.998 533.883 156.07 536.043 152.01C538.2 147.955 539.388 142.809 539.388 136.304V136.293V136.282C539.186 130.293 537.774 125.571 535.424 121.856C533.073 118.14 529.809 115.474 525.971 113.559C518.325 109.746 508.342 108.887 498.56 108.622H498.551H498.542C493.519 108.622 489.209 109.096 485.187 109.594L485.435 87.4912C489.254 87.9632 493.536 88.1982 498.278 88.1982C507.556 88.1982 516.562 87.1411 523.264 83.2406C526.628 81.2824 529.413 78.6068 531.352 74.9985C533.29 71.3936 534.364 66.8934 534.364 61.3108V61.299V61.2872C533.96 50.117 529.846 43.2038 523.28 39.1257C516.773 35.0837 507.957 33.8935 498.278 33.8935C485.889 33.8935 477.269 36.8306 471.706 43.261C466.254 49.5621 463.857 59.0859 463.529 72.0526H439.706C440.074 52.7432 445.131 38.3144 454.775 28.6823C464.528 18.9415 479.078 14 498.542 14C516.598 14 531.577 17.2391 542.094 24.7437C552.572 32.2208 558.714 43.9999 558.976 61.3207V61.3253C559.235 73.0239 553.791 81.9435 548.24 87.9633C545.467 90.9719 542.675 93.246 540.577 94.7673C539.53 95.5275 538.656 96.0987 538.047 96.4788C537.742 96.6688 537.504 96.8108 537.342 96.9048C537.261 96.9518 537.2 96.9867 537.159 97.0096L537.115 97.0349L537.105 97.0407L537.102 97.0418V97.0419Z"
        stroke="#333333"/>
      <path
        d="M721.05 146.213V145.563H720.4H627V119.491L714.655 14H745.325V125.308V125.958H745.975H772V145.563H745.975H745.325V146.213V186H721.05V146.213ZM721.05 40.8575V39.0562L719.9 40.4419L649.774 124.892L648.889 125.958H650.275H720.4H721.05V125.308V40.8575Z"
        stroke="#333333"/>
      <path
        d="M862.967 82.4934L862.746 84.5929L864.106 82.9826V82.9824L864.107 82.9814L864.112 82.9747L864.139 82.9446C864.146 82.9364 864.155 82.9268 864.164 82.9161C864.186 82.8908 864.215 82.8584 864.251 82.8195C864.351 82.7082 864.504 82.5424 864.71 82.3301C865.122 81.9054 865.745 81.2942 866.582 80.5586C868.255 79.0873 870.781 77.1192 874.174 75.1492C880.96 71.2109 891.222 67.261 905.104 67.261C923.291 67.261 938 72.8944 948.161 82.9902C958.322 93.0854 964 107.708 964 125.819C964 144.186 957.792 159.214 946.791 169.652C935.788 180.093 919.93 185.998 900.53 186C873.755 185.461 857.857 176.001 848.533 163.814C839.393 151.87 836.501 137.215 836 125.514L860.03 129.618C861.376 151.351 878.851 165.672 900.793 165.672C912.25 165.672 921.986 161.975 928.861 155.283C935.74 148.587 939.701 138.948 939.701 127.171C939.701 115.126 935.673 105.216 928.662 98.3188C921.652 91.4227 911.713 87.5887 899.985 87.5887C884.341 87.5887 871.273 94.5556 864.865 106.987L837.647 102.566C837.661 102.438 837.676 102.292 837.694 102.128C837.768 101.457 837.874 100.483 838.011 99.2461C838.283 96.7735 838.673 93.2558 839.14 89.0421C840.075 80.6146 841.321 69.4031 842.567 58.2C843.813 46.997 845.059 35.8024 845.994 27.4086L847.123 17.2675L847.439 14.4268L847.487 14H953.493V33.2463H868.731H868.148L868.086 33.8285L862.967 82.4934Z"
        stroke="#333333"/>
      <path
        d="M1237.5 34.7492V35.2492H1238H1334.27C1304.28 70.6968 1285.77 130.921 1284.97 185.993L1284.96 186.5H1285.47H1310.14H1310.64V186C1310.64 122.767 1329.65 72.393 1366.28 35.0996L1366.3 35.0799L1366.32 35.0582L1367.39 33.6931L1367.5 33.5571V33.3841V14V13.5H1367H1238H1237.5V14V34.7492Z"
        stroke="#333333"/>
      <path
        d="M1063.85 88.4617H1064.15L1064.34 88.2332C1071.86 79.3816 1086.45 70.9725 1107.91 70.9725C1140.51 70.9725 1164 96.8831 1164 129.149C1164 163.443 1136.91 186 1103.7 186C1079.63 186 1062.87 175.836 1052.04 160.342C1041.19 144.817 1036.26 123.896 1036 102.381C1036 79.3952 1040.41 57.2838 1051.29 40.9384C1062.15 24.631 1079.49 14 1105.54 14C1129.69 14 1153.39 24.5765 1161.67 47.0575L1136.86 52.5961C1132.83 39.8889 1119.75 33.897 1106.86 33.897C1091.75 33.897 1080.61 40.0155 1073.26 49.6201C1065.93 59.2033 1062.41 72.2073 1062.41 85.9558V87.8107V88.4617H1063.05H1063.85ZM1064.78 129.149C1064.78 151.277 1081.74 166.103 1102.9 166.103C1113.6 166.103 1122.75 162.541 1129.22 156.11C1135.68 149.678 1139.44 140.427 1139.44 129.149C1139.44 117.607 1135.62 108.092 1129.02 101.461C1122.42 94.8306 1113.07 91.1345 1102.11 91.1345C1091.4 91.1345 1082.06 94.8326 1075.4 101.458C1068.73 108.087 1064.78 117.601 1064.78 129.149Z"
        stroke="#333333"/>
    </svg>
  )
};

const N1Theme = ({ topic, id }) => {
  return (
    <div id={`theme-${id}`} className={"main-topic-wrapper"}>
      <h3>{topic.name}</h3>
      <N2List topics={topic.children}/>
    </div>
  )
}

const N2List = ({ topics }) => {
  return (
    <div className={"topic-list-wrapper"}>
      <div>
        {topics.map((topic, index) =>
          topic.postCount > 0 && (
            <N2Theme topic={topic} key={index}/>
          )
        )}
      </div>
    </div>
  );
}

const N2Theme = ({ topic }) => {
  return (
    <>
      <li className="topic-lvl-2">
        <Link href={`/categories/${topic.slug}`}>
          {topic.name} <span>{topic.postCount}</span>
        </Link>
      </li>
      {topic.children?.length > 0 && (
        <ul>
          {topic.children.map((topic, index) => (
            topic.postCount > 0 &&
                  <li key={index} className="topic-lvl-3">
                    <Link href={`/categories/${topic.slug}`}>
                      {topic.name} <span>{topic.postCount}</span>
                    </Link>
                  </li>
          ))}
        </ul>
      )}
    </>
  );
}


export default function MainList({ topics }) {

  const [activeSection, setActiveSection] = useState(0);
  const [S1, S1inView] = useInView();
  const [S2, S2inView] = useInView();
  const [S3, S3inView] = useInView();
  const [S4, S4inView] = useInView();
  const [S5, S5inView] = useInView();
  const [S6, S6inView] = useInView();
  const [S7, S7inView] = useInView();

  const getRef = (id) => {
    switch (id) {
    case 0:
      return S1;
    case 1:
      return S2;
    case 2:
      return S3;
    case 3:
      return S4;
    case 4:
      return S5;
    case 5:
      return S6;
    case 6:
      return S7;
    default:
      return null;
    }
  }

  useEffect(() => {
    let next = activeSection;
    if (S1inView) {
      next = 0;
    }
    if (S2inView) {
      next = 1;
    }
    if (S3inView) {
      next = 2;
    }
    if (S4inView) {
      next = 3;
    }
    if (S5inView) {
      next = 4;
    }
    if (S6inView) {
      next = 5;
    }
    if (S7inView) {
      next = 6;
    }
    setActiveSection(next);
  }, [
    activeSection,
    S1inView,
    S2inView,
    S3inView,
    S4inView,
    S5inView,
    S6inView,
    S7inView,
  ]);

  return (
    <Grid>
      <TopicsNavigation>
        <div className="anchor-list">
          <ul>
            {topics.map((topic, index) => (
              <li key={index}>
                <a href={`#theme-${index}`}>
                  <span className="list-number">{`${
                    index + 1
                  }`}</span>
                  {topic.name}
                </a>
              </li>
            ))}
          </ul>
        </div>
        <BigNumber position={activeSection}>
          <div className={"number-container"}>
            <NumbersSVG/>
          </div>
        </BigNumber>
      </TopicsNavigation>

      <Topics>
        {topics.map((topic, index) => (
          <div key={index}>
            <div className={"section-marker"} ref={getRef(index)}/>
            <N1Theme topic={topic} id={index}/>
          </div>
        ))}
      </Topics>

    </Grid>
  )
}

const TopicsNavigation = styled.div`
  display: none;

  @media ${device.desktop} {
    display: initial;
    padding: 0;
    grid-column: 1 / span 4;
    top: 0;
    height: 80vh;
    position: -webkit-sticky;
    position: sticky;
  }

  @media ${device.desktop} {
    visibility: visible;
  }
  
  .anchor-list {
    position: relative;
    font-family: Stelvio, sans-serif;
    font-size: 20px;
    font-weight: 400;

    .list-number {
      display: inline-block;
      color: #f1f1e8;
      width: 40px;
    }

    ul {
      margin-top: 64px;
      padding: 0;
    }

    li {
      font-family: Switzer, sans-serif;
      list-style: none;
      color: rgba(249, 246, 241, 0.6);
      margin: 16px 0 0 0;
      padding: 0;
    }
  }
`;

const Topics = styled.div`
  position: relative;
  color: rgba(249, 246, 241, 0.85);
  mix-blend-mode: exclusion;
  grid-column: 1 / span 12;

  @media ${device.desktop} {
    margin-top: 64px;
    padding: 0;
    grid-column: 5 / span 8;
  }

  h3 {
    font-size: 32px;
    margin-bottom: 16px;
    font-weight: 500;

    @media ${device.desktop} {
      font-size: clamp(24px, 3vw, 38px);
      max-width: 45%;
      margin-top: 32px;
    }
  }

  .section-marker {
    position: absolute;
    margin-top: 360px;
    height: 100%;
  }

  .main-topic-wrapper {
    scroll-margin-top: 64px;
    @media ${device.desktop} {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      border-top: 1px solid rgba(248, 248, 243, 0.18);
    }
  }

  .topic-list-wrapper {
    ul {
      margin: 0;
      padding: 0;
    }

    li {
      font-family: Switzer, sans-serif;
      list-style: none;

      span {
        float: right;
      }

      //border-bottom: 1px solid rgba(248, 248, 243, 0.2);
      &:hover {
        color: var(--c-brand-lighter)
      }
    }

    li:last-child {
      border: none;
      margin-bottom: 8px;
    }

    .topic-lvl-2 {
      padding-top: 24px;
      padding-bottom: 16px;
      font-size: 20px;
      font-weight: 600;
    }

    .topic-lvl-3 {
      padding: 12px 0;
      font-size: 18px;
      font-weight: 400;
      color: rgba(249, 246, 241, 0.9);
    }

    @media ${device.desktop} {
      width: calc(50% - 40px);
      margin: 16px 0;
    }
  }
`;

const BigNumber = styled.div`
  position: absolute;
  width: 80%;
  aspect-ratio: 1/1;
  left: 25vw;
  bottom: -10%;
  z-index: -1;
  transition: all 450ms ease-in-out;
  border-radius: 100%;
  overflow: hidden;

  .number-container {
    position: relative;
    margin-top: 11%;
    margin-left: ${p => p.position > 0 ? "11%" : "11%"};
    height: 78%;
    width: 546%;
    left: ${(p) => -p.position * 78}%;
    box-sizing: border-box;
    transform-origin: right;
    transition-duration: 650ms;
    transition-timing-function: cubic-bezier(0.79, 0.43, 0.38, 0.99);
  }

  svg {
    position: relative;
    height: 100%;
    width: 100%;

    path {
      stroke: none;
      fill: #1C373C;
    }
  }
`;

const Grid = styled.div`
  position: relative;
  padding: 64px var(--border-space) 128px var(--border-space);
  display: grid;
  background-color: var(--blue-dark);
  grid-template-columns: repeat(${(p) => (p.col ? p.col : 2)}, 1fr);
  column-gap: ${(p) => (p.gutter ? p.gutter : 0)}px;

  @media ${device.desktop} {
    grid-template-columns: repeat(${(p) => (p.col ? p.col : 12)}, 1fr);
    column-gap: ${(p) => (p.gutter ? 64 : 0)}px;
  }
`;
