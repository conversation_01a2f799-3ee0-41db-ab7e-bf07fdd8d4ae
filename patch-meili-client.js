/**
 * Script pour modifier meili-client.js afin qu'il soit compatible avec Meilisearch v0.27.0
 * 
 * Ce script modifie le fichier meili-client.js pour supprimer les paramètres incompatibles
 * (page et hitsPerPage) avant d'envoyer la requête à Meilisearch v0.27.0.
 * 
 * Pour exécuter ce script:
 * 1. Assurez-vous d'être dans le répertoire racine du projet
 * 2. Exécutez: node patch-meili-client.js
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le fichier meili-client.js
const meiliClientPath = path.join('tpsg-next', 'api', 'meili-client.js');

// Lire le contenu du fichier
fs.readFile(meiliClientPath, 'utf8', (err, data) => {
  if (err) {
    console.error(`Erreur lors de la lecture du fichier ${meiliClientPath}:`, err);
    return;
  }

  // Fonction search modifiée
  const searchFunctionOriginal = `const search = async (q = "", params) => {
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "cs", "lead"],
    attributesToHighlight: ["title"],
    ...params,
  })
}`;

  const searchFunctionModified = `const search = async (q = "", params) => {
  // Créer une copie des paramètres pour éviter de modifier l'objet original
  const cleanParams = { ...params };
  
  // Supprimer les paramètres incompatibles avec Meilisearch v0.27.0
  if (cleanParams.page !== undefined) delete cleanParams.page;
  if (cleanParams.hitsPerPage !== undefined) delete cleanParams.hitsPerPage;
  
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "cs", "lead"],
    attributesToHighlight: ["title"],
    ...cleanParams,
  })
}`;

  // Fonction searchHighlight modifiée
  const searchHighlightFunctionOriginal = `const searchHighlight = async (q = "", params) => {
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "body", "lead", "topics", "cs"],
    attributesToHighlight: ["title", "body", "lead"],
    attributesToCrop: ["body", "lead"],
    cropLength: 100,
    ...params,
  })
}`;

  const searchHighlightFunctionModified = `const searchHighlight = async (q = "", params) => {
  // Créer une copie des paramètres pour éviter de modifier l'objet original
  const cleanParams = { ...params };
  
  // Supprimer les paramètres incompatibles avec Meilisearch v0.27.0
  if (cleanParams.page !== undefined) delete cleanParams.page;
  if (cleanParams.hitsPerPage !== undefined) delete cleanParams.hitsPerPage;
  
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "body", "lead", "topics", "cs"],
    attributesToHighlight: ["title", "body", "lead"],
    attributesToCrop: ["body", "lead"],
    cropLength: 100,
    ...cleanParams,
  })
}`;

  // Remplacer les fonctions
  let modifiedContent = data.replace(searchFunctionOriginal, searchFunctionModified);
  modifiedContent = modifiedContent.replace(searchHighlightFunctionOriginal, searchHighlightFunctionModified);

  // Écrire le contenu modifié dans le fichier
  fs.writeFile(meiliClientPath, modifiedContent, 'utf8', (err) => {
    if (err) {
      console.error(`Erreur lors de l'écriture du fichier ${meiliClientPath}:`, err);
      return;
    }
    console.log(`Le fichier ${meiliClientPath} a été modifié avec succès.`);
    console.log('Les paramètres incompatibles (page et hitsPerPage) seront maintenant supprimés avant d\'être envoyés à Meilisearch v0.27.0.');
  });
});
