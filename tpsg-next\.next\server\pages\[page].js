"use strict";(()=>{var e={};e.id=2358,e.ids=[2358,2888],e.modules={3498:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>x,default:()=>g,getServerSideProps:()=>m,getStaticPaths:()=>u,getStaticProps:()=>d,reportWebVitals:()=>h,routeModule:()=>q,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>f});var i=r(7093),s=r(5244),o=r(1323),n=r(779),l=r(4033),p=r(3770),c=e([p]);p=(c.then?(await c)():c)[0];let g=(0,o.l)(p,"default"),d=(0,o.l)(p,"getStaticProps"),u=(0,o.l)(p,"getStaticPaths"),m=(0,o.l)(p,"getServerSideProps"),x=(0,o.l)(p,"config"),h=(0,o.l)(p,"reportWebVitals"),f=(0,o.l)(p,"unstable_getStaticProps"),v=(0,o.l)(p,"unstable_getStaticPaths"),S=(0,o.l)(p,"unstable_getStaticParams"),b=(0,o.l)(p,"unstable_getServerProps"),P=(0,o.l)(p,"unstable_getServerSideProps"),q=new i.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/[page]",pathname:"/[page]",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:p});a()}catch(e){a(e)}})},3770:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>Page,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var i=r(997),s=r(7518),o=r.n(s),n=r(1385),l=r(9114),p=r(8657),c=r(5675),g=r.n(c),d=r(4130),u=r(7467),m=r(6641),x=e([p]);function Page({content:e}){if(e)return(0,i.jsxs)(h,{children:[i.jsx(m.NextSeo,{title:e.metas?.metaTitle||null,description:e.metas?.metaDescription||null}),i.jsx("header",{children:e.cover&&(0,i.jsxs)(f,{children:[i.jsx(g(),{src:(0,d.k)(e.cover),alt:e.cover.alternativeText||"",style:{objectFit:"cover"},fill:!0}),i.jsx(v,{})]})}),(0,i.jsxs)("section",{children:[i.jsx("h1",{className:"page-title",children:e.title}),e.blocks&&e.blocks.map((e,t)=>renderBlock(e))]})]})}p=(x.then?(await x)():x)[0];let renderBlock=e=>"ComponentBlockText"===e.__typename?renderTextBlock(e):null,renderTextBlock=e=>i.jsx(S,{children:i.jsx(p.oJ,{content:e.content})}),h=o().div.withConfig({componentId:"sc-56f310d5-0"})`
  section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    margin-left: var(--border-space);
    margin-right: var(--border-space);
  }
  
  .page-title {
    grid-column: 1/3;
    margin-top: 80px;
    font-family: Stelvio, sans-serif;
    font-size: 52px;
    margin-right: 0;
  }
  
  @media ${u.U.desktop} {
    h1 {
      grid-column: 1;
      margin-right: 120px;
    }
  }
`,f=o().div.withConfig({componentId:"sc-56f310d5-1"})`
  margin-top: -80px;
  position: relative;
  width: 100%;
  height: 50vh;
`,v=o().div.withConfig({componentId:"sc-56f310d5-2"})`
  position: absolute;
  top: 0;
  width: 100%;
  height: 120px;
  background: linear-gradient(180deg, rgba(0,0,0,1) -40%, rgba(0,0,0,0) 100%);
`,S=o().div.withConfig({componentId:"sc-56f310d5-3"})`
  display: flex;
  grid-column: 1/3;
  margin-bottom: 126px;
  
  @media ${u.U.desktop} {
    grid-column: 2;
    margin-top: 40px;
    margin-left: -80px;
    align-items: center;
    justify-content: center;
  }
`;async function getStaticProps({params:e}){let t=await n.Z.query({query:P,variables:{slug:e.page}}).then(e=>e.data.pages[0]);return t?{props:{content:t},revalidate:20}:{notFound:!0}}async function getStaticPaths(){let{data:e}=await n.Z.query({query:b});return{paths:e.pages.map(e=>({params:{page:e.slug}})),fallback:!0}}let b=l.gql`
    query PageSlugs {pages{slug}}
`,P=l.gql`
    query Page($slug: String!) {
        pages(where: { slug: $slug }) {
            title
            cover {
                url
                provider
                alternativeText
            }
            blocks {
                ... on ComponentBlockText {
                    __typename
                    content
                }
            }
            metas {
                metaTitle
                metaDescription
            }
        }
    }
`;a()}catch(e){a(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,9755,4033,779,7113,1077,7620,8672,2832,8657],()=>__webpack_exec__(3498));module.exports=r})();