/**
 * API route /api/coredata
 * Returns coreData, making the most of NextJS's Data Cache
 * Cf. https://nextjs.org/docs/13/app/building-your-application/caching#data-cache
 * Cf. https://nextjs.org/docs/13/app/api-reference/file-conventions/route-segment-config#revalidate
 */

import { gql } from "@apollo/client"
import client from "api/apollo-client"
import generateFeeds from "services/feeds"

export const revalidate = 3600 // seconds // 3600 seconds = 1 hour

export async function GET () {
  let coreData = await client.query({ query: QUERY_CORE_DATA })

  // Mise à jour au passage des flux RSS
  generateFeeds(coreData.data)

  return Response.json({ updatedAt: new Date(Date.now()).toString(), ...coreData.data })
}

const QUERY_CORE_DATA = gql`
  query GetCoreData{
    authors{
      fullName,
      firstName,
      lastName,
    }
    blogs{
      id
      slug
      blogger{
        firstName
        lastName
        fullName
        picture {
          provider
          url
        }
      }
    }
    topics{
      name
      slug
      postCount
    }
    podcasts{
      name
      slug
    }
    topicGroups{
      name
      slug
      type
      children {
        name
        slug
        type
      }
    }
  }
`
