# Migration vers Meilisearch v0.30.x

Ce document explique comment migrer de Meilisearch v0.27.0 vers <PERSON><PERSON>earch v0.30.x, qui est la version utilisée en préproduction.

**Note importante**: Bien que la version en préproduction soit 0.30.5, le package npm disponible est 0.30.0. Nous utiliserons donc la version 0.30.0 du client JavaScript, qui est compatible avec le serveur 0.30.5.

## Fichiers inclus

1. `config-meilisearch-v0.30.5.js` - Script pour configurer Meilisearch v0.30.5
2. `start-meilisearch-v0.30.5.bat` - Script pour démarrer Meilisearch v0.30.5
3. `update-meilisearch.bat` - Script pour mettre à jour les dépendances de Next.js et configurer Meilisearch
4. `update-strapi-meilisearch.bat` - Script pour mettre à jour les dépendances de Strapi

## Étapes de migration

### 1. <PERSON><PERSON><PERSON><PERSON> Meilisearch v0.30.5

Exécutez le script `start-meilisearch-v0.30.5.bat` pour démarrer Meilisearch v0.30.5.

```bash
start-meilisearch-v0.30.5.bat
```

### 2. Mettre à jour Next.js

Exécutez le script `update-meilisearch.bat` pour mettre à jour les dépendances de Next.js et configurer Meilisearch.

```bash
update-meilisearch.bat
```

### 3. Mettre à jour Strapi

Exécutez le script `update-strapi-meilisearch.bat` pour mettre à jour les dépendances de Strapi.

```bash
update-strapi-meilisearch.bat
```

**Note importante**: Strapi nécessite Node.js v14, alors que Next.js nécessite Node.js v18. Assurez-vous d'utiliser la bonne version de Node.js pour chaque application.

### 4. Démarrer l'application Next.js

Allez dans le répertoire `tpsg-next` et démarrez l'application avec Node.js v18.

```bash
cd tpsg-next
npm run dev
```

### 5. Démarrer Strapi

Allez dans le répertoire `tpsg-strapi` et démarrez Strapi avec Node.js v14.

```bash
cd tpsg-strapi
# Utilisez le script approprié pour démarrer Strapi avec Node.js v14
```

## Différences entre Meilisearch v0.27.0 et v0.30.x

Meilisearch v0.30.x apporte plusieurs améliorations par rapport à v0.27.0, notamment :

1. **Support des paramètres de pagination** : Meilisearch v0.30.x supporte les paramètres `page` et `hitsPerPage`, ce qui rend le code existant compatible sans modification.

2. **Amélioration des performances** : Meilisearch v0.30.x offre de meilleures performances pour les recherches et l'indexation.

3. **Nouvelles fonctionnalités** : Meilisearch v0.30.x inclut de nouvelles fonctionnalités comme les facettes, les filtres avancés, et plus encore.

## Vérification de la version

Pour vérifier la version de Meilisearch utilisée par l'application, ouvrez la console du navigateur lors de l'exécution de l'application. La version de Meilisearch sera affichée dans les logs de diagnostic.

## Résolution des problèmes

Si vous rencontrez des problèmes avec Meilisearch v0.30.5, assurez-vous que :

1. Meilisearch v0.30.5 est en cours d'exécution sur le port 7700
2. Les variables d'environnement `NEXT_PUBLIC_MEILISEARCH_HOST` et `NEXT_PUBLIC_MEILISEARCH_KEY` sont correctement définies dans `.env.local`
3. L'index `post` a été correctement configuré avec les attributs filtrables, triables et recherchables

Si les problèmes persistent, vous pouvez revenir à Meilisearch v0.27.0 en modifiant le package.json et en exécutant `npm install`.

## Remarque sur les versions

Il est important de noter que bien que le serveur Meilisearch en préproduction soit en version 0.30.5, le package npm disponible est en version 0.30.0. Cela ne devrait pas poser de problème car les versions mineures (0.30.x) sont généralement compatibles entre elles. Le client JavaScript v0.30.0 peut communiquer avec un serveur Meilisearch v0.30.5 sans problème.
