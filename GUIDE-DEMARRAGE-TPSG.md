# Guide de démarrage pour TPSG

Ce guide détaille les étapes pour démarrer correctement l'environnement de développement TPSG avec Meilisearch v0.30.5, Strapi et Next.js.

## Démarrage normal

### 1. <PERSON><PERSON><PERSON><PERSON> Meilisearch

```powershell
cd C:\ 
.\meilisearch-windows-amd64.exe --http-addr 0.0.0.0:7700 --env development --no-analytics
```

### 2. <PERSON><PERSON>mar<PERSON> Strapi

```powershell
cd tpsg-strapi
nvm use 14  # Assurez-vous d'utiliser Node.js v14 pour Strapi
npm run develop
```

### 3. Démarrer Next.js

```powershell
cd tpsg-next
nvm use 18  # Assurez-vous d'utiliser Node.js v18 pour Next.js
npm run dev
```

## Résolution des problèmes avec Meilisearch

Si vous rencontrez des erreurs liées à Meilisearch, suivez ces étapes pour reconfigurer correctement l'index.

### Vérifier l'état de l'index

```powershell
Invoke-WebRequest -Uri "http://127.0.0.1:7700/indexes/post/stats" -Method GET | Select-Object -ExpandProperty Content
```

### Configurer les attributs triables

Si vous rencontrez l'erreur `Attribute 'date' is not sortable`:

```powershell
node configure-sortable.js
```

Vérifiez que la configuration a été appliquée:

```powershell
Invoke-WebRequest -Uri "http://127.0.0.1:7700/indexes/post/settings/sortable-attributes" -Method GET | Select-Object -ExpandProperty Content
```

### Configurer les attributs filtrables

Si vous rencontrez l'erreur `Attribute 'cs' is not filterable`:

```powershell
node configure-filterable.js
```

Vérifiez que la configuration a été appliquée:

```powershell
Invoke-WebRequest -Uri "http://127.0.0.1:7700/indexes/post/settings/filterable-attributes" -Method GET | Select-Object -ExpandProperty Content
```

### Configurer la pagination

Si vous ne voyez que 1000 résultats au lieu des 3571 attendus:

```powershell
node configure-pagination.js
```

Vérifiez que la configuration a été appliquée:

```powershell
Invoke-WebRequest -Uri "http://127.0.0.1:7700/indexes/post/settings/pagination" -Method GET | Select-Object -ExpandProperty Content
```

## Réindexation complète

Si vous avez besoin de réindexer tous les documents (par exemple après avoir ajouté de nouveaux posts dans Strapi):

### 1. Réindexer les documents

```powershell
cd tpsg-strapi
node reindex-meilisearch.js
```

### 2. Vérifier l'indexation

```powershell
Invoke-WebRequest -Uri "http://127.0.0.1:7700/indexes/post/stats" -Method GET | Select-Object -ExpandProperty Content
```

### 3. Configurer les attributs triables, filtrables et la pagination

Exécutez les scripts de configuration dans cet ordre:

```powershell
node configure-sortable.js
node configure-filterable.js
node configure-pagination.js
```

### 4. Redémarrer Next.js

```powershell
cd tpsg-next
npm run dev
```

## Contenu des scripts de configuration

Si vous avez besoin de recréer les scripts de configuration, voici leur contenu:

### configure-sortable.js

```javascript
const http = require('http');

const options = {
  hostname: '127.0.0.1',
  port: 7700,
  path: '/indexes/post/settings/sortable-attributes',
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json'
  }
};

const data = JSON.stringify(['date']);

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  
  res.on('data', (chunk) => {
    console.log(`Response: ${chunk}`);
  });
  
  res.on('end', () => {
    console.log('Configuration terminée');
    
    // Vérifier la configuration
    const checkOptions = {
      hostname: '127.0.0.1',
      port: 7700,
      path: '/indexes/post/settings/sortable-attributes',
      method: 'GET'
    };
    
    const checkReq = http.request(checkOptions, (checkRes) => {
      console.log(`Vérification - Status: ${checkRes.statusCode}`);
      
      let data = '';
      
      checkRes.on('data', (chunk) => {
        data += chunk;
      });
      
      checkRes.on('end', () => {
        console.log(`Attributs triables: ${data}`);
      });
    });
    
    checkReq.on('error', (error) => {
      console.error(`Erreur lors de la vérification: ${error}`);
    });
    
    checkReq.end();
  });
});

req.on('error', (error) => {
  console.error(`Erreur lors de la configuration: ${error}`);
});

req.write(data);
req.end();

console.log('Envoi de la requête...');
```

### configure-filterable.js

```javascript
const http = require('http');

const options = {
  hostname: '127.0.0.1',
  port: 7700,
  path: '/indexes/post/settings/filterable-attributes',
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json'
  }
};

const data = JSON.stringify([
  'title',
  'topics',
  'tags',
  'author',
  'type',
  'blog',
  'cs'
]);

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  
  res.on('data', (chunk) => {
    console.log(`Response: ${chunk}`);
  });
  
  res.on('end', () => {
    console.log('Configuration terminée');
    
    // Vérifier la configuration
    const checkOptions = {
      hostname: '127.0.0.1',
      port: 7700,
      path: '/indexes/post/settings/filterable-attributes',
      method: 'GET'
    };
    
    const checkReq = http.request(checkOptions, (checkRes) => {
      console.log(`Vérification - Status: ${checkRes.statusCode}`);
      
      let data = '';
      
      checkRes.on('data', (chunk) => {
        data += chunk;
      });
      
      checkRes.on('end', () => {
        console.log(`Attributs filtrables: ${data}`);
      });
    });
    
    checkReq.on('error', (error) => {
      console.error(`Erreur lors de la vérification: ${error}`);
    });
    
    checkReq.end();
  });
});

req.on('error', (error) => {
  console.error(`Erreur lors de la configuration: ${error}`);
});

req.write(data);
req.end();

console.log('Envoi de la requête...');
```

### configure-pagination.js

```javascript
const http = require('http');

const options = {
  hostname: '127.0.0.1',
  port: 7700,
  path: '/indexes/post/settings/pagination',
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json'
  }
};

const data = JSON.stringify({
  maxTotalHits: 4000
});

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  
  res.on('data', (chunk) => {
    console.log(`Response: ${chunk}`);
  });
  
  res.on('end', () => {
    console.log('Configuration terminée');
    
    // Vérifier la configuration
    const checkOptions = {
      hostname: '127.0.0.1',
      port: 7700,
      path: '/indexes/post/settings/pagination',
      method: 'GET'
    };
    
    const checkReq = http.request(checkOptions, (checkRes) => {
      console.log(`Vérification - Status: ${checkRes.statusCode}`);
      
      let data = '';
      
      checkRes.on('data', (chunk) => {
        data += chunk;
      });
      
      checkRes.on('end', () => {
        console.log(`Paramètres de pagination: ${data}`);
      });
    });
    
    checkReq.on('error', (error) => {
      console.error(`Erreur lors de la vérification: ${error}`);
    });
    
    checkReq.end();
  });
});

req.on('error', (error) => {
  console.error(`Erreur lors de la configuration: ${error}`);
});

req.write(data);
req.end();

console.log('Envoi de la requête...');
```

## Notes importantes

1. **Versions de Node.js** : Strapi nécessite Node.js v14, tandis que Next.js nécessite Node.js v18. Utilisez `nvm` pour basculer entre les versions.

2. **Ordre des opérations** : Démarrez toujours Meilisearch avant Strapi et Next.js.

3. **Temps d'indexation** : L'indexation de 3571 documents peut prendre du temps. Soyez patient après avoir lancé la réindexation.

4. **Vérification des configurations** : Après chaque configuration, vérifiez qu'elle a bien été appliquée avant de passer à l'étape suivante.

5. **Redémarrage de Next.js** : Après avoir modifié la configuration de Meilisearch, redémarrez Next.js pour qu'il prenne en compte les changements.
