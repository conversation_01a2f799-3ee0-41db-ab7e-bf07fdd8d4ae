"use strict";(()=>{var e={};e.id=9821,e.ids=[9821,2888],e.modules={670:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{config:()=>u,default:()=>g,getServerSideProps:()=>m,getStaticPaths:()=>x,getStaticProps:()=>c,reportWebVitals:()=>f,routeModule:()=>y,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>h});var o=i(7093),a=i(5244),s=i(1323),l=i(779),n=i(4033),d=i(7297),p=e([d]);d=(p.then?(await p)():p)[0];let g=(0,s.l)(d,"default"),c=(0,s.l)(d,"getStaticProps"),x=(0,s.l)(d,"getStaticPaths"),m=(0,s.l)(d,"getServerSideProps"),u=(0,s.l)(d,"config"),f=(0,s.l)(d,"reportWebVitals"),h=(0,s.l)(d,"unstable_getStaticProps"),b=(0,s.l)(d,"unstable_getStaticPaths"),v=(0,s.l)(d,"unstable_getStaticParams"),w=(0,s.l)(d,"unstable_getServerProps"),j=(0,s.l)(d,"unstable_getServerSideProps"),y=new o.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/blog/[blog]",pathname:"/blog/[blog]",bundlePath:"",filename:""},components:{App:n.default,Document:l.default},userland:d});r()}catch(e){r(e)}})},4241:(e,t,i)=>{i.d(t,{Z:()=>BlogHeader});var r=i(997),o=i(7518),a=i.n(o),s=i(7467),l=i(4130),n=i(5675),d=i.n(n);function BlogHeader({blog:e}){let t=(0,l.k)(e.blogger.picture);return(0,r.jsxs)(g,{children:[r.jsx(c,{children:r.jsx(d(),{fill:!0,sizes:"100px",style:p.picture,src:t,alt:""})}),(0,r.jsxs)("div",{className:"text-content",children:[r.jsx("p",{className:"blog-header-name",children:e.blogger.fullName}),r.jsx("p",{className:"blog-header-label",children:"Blog"})]})]})}let p={picture:{objectFit:"cover"}},g=a().div.withConfig({componentId:"sc-c602c9c9-0"})`
  
  margin: 96px var(--border-space) 32px var(--border-space);
  
  display: flex;
  flex-direction: row;
  align-items: center;
  
  .text-content {
    margin-left: 16px;
  }
  
  .blog-header-name {
    font-family: Stelvio, sans-serif;
    font-size: 24px;
    line-height: 25px;
    font-weight: 500;
    margin: 0;
  }
  
  .blog-header-label {
    font-family: Lora, serif;
    font-style: italic;
    margin: 0;
    font-size: 17px;
    line-height: 20px;
  }
  
  @media ${s.U.desktop} {
    display: none;
  }
`,c=a().div.withConfig({componentId:"sc-c602c9c9-1"})`
  position: relative;
  height: 54px;
  border-radius: 54px;
  background-color: #0070f3;
  overflow: hidden;
  aspect-ratio: 1/1;
`},8102:(e,t,i)=>{i.d(t,{Z:()=>s});var r=i(5152),o=i.n(r);let a=o()(()=>i.e(7992).then(i.bind(i,7992)),{loadableGenerated:{modules:["..\\components\\shared\\ConvertkitForm\\DynamicForm.js -> ./CKForm"]},ssr:!1}),s=a},7297:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{default:()=>Blog,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var o=i(997),a=i(1385),s=i(9114),l=i(7518),n=i.n(l),d=i(7994),p=i(7467),g=i(7199),c=i(2832);i(3169);var x=i(8102),m=i(3646),u=i(8657),f=i(4241),h=i(73),b=i(6641),v=e([m,u]);[m,u]=v.then?(await v)():v;let w={showAuthor:!1,dotColors:{back:"#ffffff"}};function Blog({blog:e,posts:t}){if(!e||t.length<10)return o.jsx(o.Fragment,{});let i=e.featured?.filter(e=>!e.inColumn),r=i.length>0?i[0]:null,a=i.length>1?i[1]:null,s=e.featured?.filter(e=>e.inColumn),l=s.length>0?s[0]:null,n=s.length>1?s[1]:null;return(0,o.jsxs)(P,{children:[o.jsx(b.NextSeo,{title:`TPSG - Blog de ${e.blogger.fullName}`,description:(0,g.Kd)(e.blogger.about).slice(0,160)}),o.jsx(f.Z,{blog:e}),(0,o.jsxs)($,{children:[o.jsx(d.Z,{data:e}),(0,o.jsxs)(_,{children:[(0,o.jsxs)(I,{children:[o.jsx("div",{className:"main-element",children:o.jsx(c.F3,{post:t[0],options:w})}),(0,o.jsxs)("div",{className:"sub-row",children:[o.jsx(c.Zo,{post:t[1],options:w}),o.jsx(c.Zo,{post:t[2],options:w}),o.jsx(c.Zo,{post:t[3],options:w}),o.jsx(c.Zo,{post:t[4],options:w})]})]}),r&&(0,o.jsxs)(o.Fragment,{children:[o.jsx(k,{direction:"top"}),o.jsx(u.g4,{content:r}),o.jsx(k,{direction:"bot"})]}),o.jsx(q,{}),(0,o.jsxs)(Z,{children:[o.jsx(c.Zo,{post:t[5],options:w}),o.jsx(c.Zo,{post:t[6],options:w}),l&&o.jsx(c.h3,{item:l}),o.jsx(c.Zo,{post:t[8],options:w}),o.jsx(c.Zo,{post:t[9],options:w}),n&&o.jsx(c.h3,{item:n})]})]}),o.jsx(C,{children:o.jsx(h.Ty,{theme:"dark",text:"Tous mes articles",link:`/blog/${e.slug}/filtres`})})]}),a&&(0,o.jsxs)(o.Fragment,{children:[o.jsx(y,{}),o.jsx(u.g4,{content:a})]}),(0,o.jsxs)(j,{children:[o.jsx(S,{children:o.jsx(x.Z,{formString:e.newsletter,title:"Ma newsletter"})}),o.jsx(m.Z,{author:e.blogger,blog:e})]})]})}let j=n().div.withConfig({componentId:"sc-17fa9b26-0"})`
  padding-left: var(--border-space);
  padding-right: var(--border-space);
  display: flex;
  flex-direction: column-reverse;
  margin-bottom: 128px;
  margin-top: 128px;

  .author-box {
    margin-bottom: 40px;
  }

  @media ${p.U.desktop} {
    flex-direction: row;
    .author-box {
      padding-left: 40px;
    }
  }
`,y=n().div.withConfig({componentId:"sc-17fa9b26-1"})`
  position: relative;
  height: 64px;
  @media ${p.U.desktop} {
    height: 164px;
  }
`,S=n().div.withConfig({componentId:"sc-17fa9b26-2"})`
  border-top: 1px solid rgba(0, 0, 0, 0.4);
  padding-right: 40px;
  padding-top: 32px;
  @media ${p.U.desktop} {
    min-width: 40%;
    border-right: 1px solid rgba(0, 0, 0, 0.4);
  }
`,k=n().div.withConfig({componentId:"sc-17fa9b26-3"})`
  position: relative;
  height: 40px;
  background: ${e=>"bot"===e.direction?"linear-gradient(#FAF7F3, rgba(250, 247, 243, 0))":"linear-gradient(rgba(250, 247, 243, 0), #FAF7F3)"};
  z-index: 100;
`,q=n().div.withConfig({componentId:"sc-17fa9b26-4"})`
  position: relative;
  height: 64px;
`,P=n().div.withConfig({componentId:"sc-17fa9b26-5"})`
  position: relative;
  background-color: var(--soft-white);
  @media ${p.U.desktop} {
    .blog-menu {
      margin-left: var(--border-space);
    }
  }
  @media ${p.U.desktopXL} {
    .blog-menu {
      margin-left: 114px;
    }
  }
`,$=n().div.withConfig({componentId:"sc-17fa9b26-6"})`
  position: relative;
`,_=n().div.withConfig({componentId:"sc-17fa9b26-7"})`
  position: relative;
  top: 48px;
  left: 0;
  width: 100%;
  @media ${p.U.desktop} {
    margin-top: -100vh;
  }
`,C=n().div.withConfig({componentId:"sc-17fa9b26-8"})`
  position: relative;
  display: flex;
  margin-top: 48px;
  margin-left: var(--border-space);
  .button-link {
    width: calc(100% - var(--border-space));
    text-align: center;
  }
  @media ${p.U.tablet} {
    .button-link {
      width: auto;
      text-align: left;
    }
  }
  @media ${p.U.desktop} {
    margin-left: calc(25% + 24px);
  }
`,I=n().div.withConfig({componentId:"sc-17fa9b26-9"})`
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-right: var(--border-space);
  margin-left: var(--border-space);

  .main-element {
    display: flex;
    width: 100%;
  }

  .sub-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 18px;

    @media ${p.U.desktop} {
      display: flex;
      flex-direction: row;
      gap: 18px;
    }
  }

  @media ${p.U.tablet} {
    margin-left: calc(var(--border-space) - 24px);
  }
  @media ${p.U.desktop} {
    margin-left: 25%;
  }
`,Z=n().div.withConfig({componentId:"sc-17fa9b26-10"})`
  position: relative;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-right: var(--border-space);

  .fa-card {
    position: relative;
    max-width: 100%;
    margin-left: 24px;
    margin-bottom: 40px;
  }
  .sv-card {
    margin-left: 24px;
    margin-bottom: 40px;
    max-width: calc(50% - 24px);
  }
  .svf-card {
    margin-left: 24px;
    margin-bottom: 40px;
    //max-width: calc(50% - 24px);
    max-width: 100%;
  }
  @media ${p.U.tablet} {
    margin-left: calc(var(--border-space) - 24px);
    .fa-card {
      width: calc(66.3% - 24px);
    }
    .sv-card {
      width: calc(33.3% - 24px);
    }
    .svf-card {
      width: calc(33.3% - 24px);
    }
  }
  @media ${p.U.desktop} {
    margin-left: 25%;
  }
`;async function getStaticProps({params:e}){let t=await a.Z.query({query:U,variables:{slug:e.blog}}).then(e=>e.data.blogs[0]);if(!t)return{notFound:!0};let i=t.featured?.map(e=>e.postRef?.id),r=await a.Z.query({query:z,variables:{blogId:t.id,featuredIds:i}}).then(e=>e.data.posts);return{props:{blog:t,posts:r},revalidate:10}}async function getStaticPaths(){let e=await a.Z.query({query:F}).then(e=>e.data.blogs);return{paths:e.map(e=>({params:{blog:e.slug}})),fallback:!0}}n().div.withConfig({componentId:"sc-17fa9b26-11"})`
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  background: transparent;
  margin-top: 126px;
  z-index: 300;

  .blog-featured-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16/10;
  }
  .blog-featured-text {
    background-color: ${e=>e.backgroundColor};
    position: relative;
    padding: var(--border-space);
    width: 100%;
    min-height: 400px;
  }
  .blog-featured-title {
    font-size: 46px;
    margin-top: 24px;
    margin-bottom: 0;
  }
  .blog-featured-desc {
    font-size: 18px;
  }
  .blog-featured-type {
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 18px;
  }
  .blog-featured-buttons {
    margin-top: 40px;
    margin-bottom: 40px;
  }

  @media ${p.U.tablet} {
    flex-direction: row;
    .blog-featured-image {
      width: 50%;
      height: 100%;
      aspect-ratio: inherit;
    }
    .blog-featured-text {
      position: relative;
      padding: 52px;
      width: 50%;
      height: 100%;
    }
  }
  @media ${p.U.desktop} {
    padding-left: calc(25% + 24px);
    .blog-featured-image {
      width: calc(50% - 48px);
      min-height: 400px;
      max-height: 100%;
      height: inherit;
    }
    .blog-featured-text {
      width: calc(50% + 48px);
      height: 100%;
    }
  }
`;let U=s.gql`
  query Blog($slug: String!) {
    blogs(where: { slug: $slug }) {
      id
      slug
      newsletter
      blogger {
        fullName
        picture {
          formats
          url
          provider
        }
        about
      }
      featured {
        title
        description
        type
        image {
          formats
          url
          provider
        }
        inColumn
        cta {
          name
          url
          outline
        }
        cta2 {
          name
          url
          outline
        }
        color {
          foreground
          background
        }
        postRef {
          id
        }
      }
      menu {
        label
        value
        type
      }
    }
  }
`,F=s.gql`
  query BlogSlugs {
    blogs {
      slug
    }
  }
`,z=s.gql`
  query BlogPosts($blogId: ID!, $featuredIds: [String]) {
    posts(
      limit: 14
      where: { blog: $blogId, id_nin: $featuredIds }
      sort: "published_at:DESC"
    ) {
      title
      slug
      type
      image {
        url
        width
        height
        provider
        caption
        alternativeText
      }
      published_at
      modules {
        __typename
        ... on ComponentModulePodcast {
          podcast {
            name
            slug
          }
        }
      }
    }
  }
`;r()}catch(e){r(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),i=t.X(0,[3181,5016,6859,8450,9755,5152,4033,779,7113,1077,7620,8672,2832,8657,3646,3169,7994],()=>__webpack_exec__(670));module.exports=i})();