const types = [
  "podcast",
  "formation",
  "article",
  "webinaire",
  "parcours-email"
];

export default function getMatchingFilter(input, coreData) {
  let res = null;
  res = coreData.authors.filter((x) =>
    x.fullName?.toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .startsWith(input.toLowerCase().normalize("NFD")
        .replace(/[\u0300-\u036f]/g, ""))
  );
  if (res.length > 0) return { type: "author", value: res[0].fullName };

  res = coreData.topics.filter((x) =>
    x.name?.toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .startsWith(input.toLowerCase().normalize("NFD")
        .replace(/[\u0300-\u036f]/g, ""))
  );

  if (res.length > 0) return { type: "topics", value: res[0].name };

  res = types.filter((x) =>
    x.toLowerCase()
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .startsWith(input.toLowerCase().normalize("NFD")
        .replace(/[\u0300-\u036f]/g, ""))
  );

  if (res.length > 0) return { type: "type", value: res[0] };
  return res;
}
