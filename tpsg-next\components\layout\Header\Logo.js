import { useContext } from "react";
import { HeaderContext } from "context/HeaderContext";
import styled from "styled-components";
import Link from "next/link";
import Image from "next/image";

export default function Logo({ white }){

  const { toggleMenu } = useContext(HeaderContext);

  let logoSrc = white === true ? "/images/tpsg-logo-white.svg" : "/images/tpsg-logo.svg";

  return (
    <Link href={"/"}>
      <LogoWrapper onClick={() => toggleMenu(true)}>
        <Image
          src={logoSrc}
          alt={"LOGO_TPSG"}
          sizes={"100px"}
          fill
        />
      </LogoWrapper>
    </Link>
  )
}


// size ratio 2.09
const LogoWrapper = styled.div`
  position: relative;
  height: 25px;
  width: calc(25px * 2.09);
  margin-left: var(--border-space);
`
