import styled from "styled-components";
import { device } from "styles/device";


export default function ButtonClose({ handleClick }) {

  return (
    <Wrapper onClick={handleClick}>
		    <p>+</p>
    </Wrapper>
  )
}

const Wrapper = styled.div`
		
		height: 28px;
		border-radius: 16px;
		padding: 1px 22px 0 18px;
		background-color: var(--c-soft-cream);
		
		cursor: pointer;
		
		p {
				margin: 0;
				font-size: 24px;
				line-height: 32px;
				transform: rotate(45deg);
				color: black;
		}
		
		@media ${device.desktop} {
				&:hover {
						background-color: var(--c-brand-lighter);
						p {
								color: var(--c-soft-cream);
						}
				}
		}
`;