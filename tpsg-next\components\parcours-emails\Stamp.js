import styled from "styled-components";
import { device } from "styles/device";
import ButtonLink from "components/shared/Buttons/ButtonLink";
import SvgDuotone from "components/shared/DuotoneFilter";
import CondImage from "components/shared/condimage";
import { DisplayCardTitle } from "styles/styled-typography";
import { modulesAsObj } from "utils/components.utils";
import { removeMarkdown } from "../../utils/string.utils";

export default function Stamp({ post }) {


  // TODO: Ajouter le lien vers le post dans le bouton
  const { lead } = modulesAsObj(post.modules);

  return (
    <Background>
      <SvgDuotone hexLight={"#FFC7A7"} hexDark={"#2B1DD0"}/>
      <CondImage imageData={post?.image} addClass={"reverse"} priority={true}/>
      <StampWrapper>
        <div className={"stamp-content"}>
          <div className={"text-content"}>
            <DisplayCardTitle color={"#5853d0"} label={"Dernier parcours"}>{post.title}</DisplayCardTitle>
            <div className={"stamp-lead"}>{removeMarkdown(lead?.content)}</div>
            <div className={"stamp-buttons"}>
              <ButtonLink text={"Commencer"} type={"glow-white"} url={`/parcours-emails/${post.slug}`}/>
            </div>
          </div>
          <div className={"cover"}>
            <div className={"cover-image"}>
              <CondImage imageData={post?.image} priority={true}/>
            </div>
            <div className={"stamp-buttons"}>
              <ButtonLink type={"glow-dark"} text={"Commencer"} url={`/parcours-emails/${post.slug}`} />
            </div>
          </div>
        </div>
        <Waves/>
      </StampWrapper>
    </Background>
  )
}

const mask = (size) => ( `
   linear-gradient(#000 0 0) 50%/calc(100% - ${size * 2}px) calc(100% - ${size * 2}px) no-repeat,
   radial-gradient(${size / 2}px, #0000 98%, #000) ${size}px -${size}px/${size * 2}.70px ${size * 2}px round no-repeat,
   radial-gradient(${size / 2}px, #0000 98%, #000) ${size}px calc(100% + ${size}px)/${size * 2}.70px ${size * 2}px round no-repeat,
   radial-gradient(${size / 2}px,#0000 98%,#000) -${size}px ${size}px/${size * 2}px ${size * 2}.70px no-repeat round,
   radial-gradient(${size / 2}px, #0000 98%, #000) calc(100% + ${size}px) ${size}px/${size * 2}px ${size * 2}.70px no-repeat round;
` )

const StampWrapper = styled.div`
  position: relative;
  overflow: visible;
  width: 100%;
  margin: auto;
  height: auto;

  -webkit-mask: ${mask(14)};
  mask: ${mask(14)};

  display: flex;
  flex-direction: column;
  background-color: #242424;

  .stamp-content {
    position: relative;
    background-image: url("/images/ornaments/noise.png");
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .text-content {
    padding: 32px;

    .stamp-buttons {
      display: none;
    }
  }

  .cover {
    position: relative;
    padding: 32px;
    box-sizing: border-box;
    background-color: #ececec;

    .stamp-buttons {
      position: relative;
      width: 100%;
      display: flex;
      justify-content: center;
      padding: 16px;
      z-index: 2;

      .button-link {
        width: 100%;
        text-align: center;
        margin: 0;
      }
    }

    .cover-image {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 100%;
      opacity: 0.4;
      filter: grayscale(0.2);
      z-index: 1;
    }
  }

  .stamp-lead {
    font-size: 18px;
    font-weight: 400;
    color: #b6b6b6;
    max-width: 650px;
    margin-top: 16px;
  }

  // Stamp waves
  
  .waves-wrapper {
    display: none;
    position: absolute;
    flex-direction: row;
    overflow: hidden;
    right: 126px;
    top: 72px;
    height: 118px;
    width: 398px;
    //width: 400px;
    //height: 120px;
    z-index: 2;
    .waves {
      top: 0;
      left: 0;
      display: flex;
      flex-direction: row;
    }
    svg {
      &:first-child {
        margin-right: -1px;
      }
      path {
        fill-opacity: 0.4;
      }
    }
  }
  

  
  @keyframes waveAnimation {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0%);
    }
  }

  @media ${device.tablet} {
    -webkit-mask: ${mask(18)};
    mask: ${mask(18)};

    .stamp-content {
      margin: 24px;
      border: 1px solid #3d3d3d;
      width: calc(100% - 50px);
      flex-direction: row;
      justify-content: space-between;
    }

    .text-content {
      padding: calc(18px + 48px);

      .stamp-buttons {
        margin-top: 32px;
        display: flex;
      }
    }

    .cover {
      width: 33%;
      padding: 0;

      .stamp-buttons {
        display: none;
      }

      .cover-image {
        opacity: 1;
      }
    }

    .stamp-lead {
      margin-top: 16px;
      font-size: 18px
    }
  }

  @media ${device.desktop} {
    -webkit-mask: ${mask(20)};
    mask: ${mask(20)};

    .stamp-lead {
      font-size: 22px;
    }

    .waves-wrapper {
      display: inherit;
    }

    &:hover {
      svg {
        animation: waveAnimation 12000ms linear infinite;
      }
    }
  }

`;


const Background = styled.div`
  position: relative;
  padding: var(--spacing-l) var(--border-space);
  background-color: #2B1DD0;
  // remove this
  margin-bottom: 100px;
`;

const Waves = () => {
  return (
    <div className={"waves-wrapper"}>
      <div className={"waves"}>
        <svg width="394" height="118" viewBox="0 0 394 118" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fillRule="evenodd" clipRule="evenodd" d="M0.00756839 56.667C11.7949 56.667 17.8915 62.3528 24.1335 68.2848L24.3178 68.46C30.5181 74.3535 36.935 80.4528 49.0881 80.4528C61.2412 80.4528 67.658 74.3535 73.8581 68.4601L74.0426 68.2848C80.2845 62.3528 86.3811 56.667 98.1686 56.667C109.967 56.667 116.433 62.3658 122.855 68.2946L123.361 68.7626C129.612 74.5415 136.005 80.4528 147.249 80.4528C158.391 80.4528 164.545 74.6461 170.61 68.9241C170.835 68.7119 171.06 68.4998 171.284 68.2881C177.568 62.3741 184.038 56.667 196.33 56.667L196.33 56.6673C206.186 56.6673 212.334 62.0909 218.957 67.9337L219.385 68.3103C226.133 74.259 233.404 80.4531 245.41 80.4531C257.258 80.4531 263.67 74.4159 269.889 68.5601L270.178 68.2884C276.479 62.3575 282.699 56.6673 294.491 56.6673C306.264 56.6673 312.115 62.3377 318.233 68.2684L318.243 68.2782C324.396 74.2415 330.814 80.4531 343.571 80.4531C356.307 80.4531 363.451 74.2671 369.977 68.2981C370.182 68.1107 370.386 67.9237 370.589 67.7372C376.887 61.9672 382.672 56.6673 392.652 56.6673L392.652 54.6673C381.882 54.6673 375.57 60.4557 369.349 66.161C369.108 66.3817 368.868 66.6022 368.627 66.8223C362.15 72.7462 355.499 78.4531 343.571 78.4531C331.666 78.4531 325.753 72.7718 319.635 66.8421L319.557 66.7657C313.433 60.8305 307.075 54.6673 294.491 54.6673C281.864 54.6673 275.142 60.87 268.807 66.832L268.613 67.0141C262.36 72.9008 256.462 78.4531 245.41 78.4531C234.219 78.4531 227.45 72.7543 220.707 66.81C220.603 66.7184 220.499 66.6266 220.395 66.5348L220.136 66.3065C213.618 60.5527 206.951 54.6673 196.33 54.6673L196.33 54.667C183.227 54.667 176.266 60.8528 169.914 66.8317C169.775 66.9619 169.637 67.092 169.499 67.2219L169.33 67.3813C163.214 73.1471 157.587 78.4528 147.249 78.4528C136.796 78.4528 130.922 73.0252 124.61 67.193L124.211 66.8252C117.752 60.8611 110.788 54.667 98.1686 54.667C85.538 54.667 78.9372 60.8741 72.6648 66.835L72.5669 66.928C66.3466 72.8398 60.4406 78.4528 49.0881 78.4528C37.7356 78.4528 31.8295 72.8397 25.609 66.9279L25.5112 66.835C19.2388 60.8741 12.6379 54.667 0.00756836 54.667L0.00756839 56.667Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M0.00122073 74.9624C11.7885 74.9624 17.8851 80.6482 24.1271 86.5802L24.3115 86.7554C30.5117 92.6489 36.9286 98.7482 49.0818 98.7482C61.2348 98.7482 67.6516 92.6489 73.8518 86.7555L74.0362 86.5802C80.2781 80.6482 86.3747 74.9624 98.1623 74.9624C109.961 74.9624 116.427 80.6612 122.848 86.59L123.355 87.058C129.605 92.8369 135.999 98.7482 147.243 98.7482C158.385 98.7482 164.539 92.9415 170.604 87.2195C170.828 87.0073 171.053 86.7952 171.278 86.5835C177.562 80.6695 184.031 74.9624 196.323 74.9624L196.323 74.9627C206.179 74.9627 212.328 80.3863 218.951 86.2291L219.378 86.6057C226.126 92.5544 233.398 98.7485 245.404 98.7485C257.252 98.7485 263.664 92.7113 269.883 86.8555L270.171 86.5838C276.473 80.6529 282.693 74.9627 294.484 74.9627C306.258 74.9627 312.108 80.6331 318.227 86.5638L318.237 86.5736C324.389 92.5369 330.808 98.7485 343.565 98.7485C356.3 98.7485 363.445 92.5625 369.97 86.5935C370.152 86.4274 370.333 86.2616 370.514 86.0961L370.583 86.0326C376.881 80.2626 382.666 74.9627 392.645 74.9627L392.645 72.9627C381.876 72.9627 375.564 78.7511 369.342 84.4564L369.323 84.4742C369.089 84.6889 368.855 84.9035 368.621 85.1177C362.144 91.0416 355.492 96.7485 343.565 96.7485C331.659 96.7485 325.747 91.0672 319.629 85.1375L319.55 85.0611C313.427 79.1259 307.069 72.9627 294.484 72.9627C281.857 72.9627 275.135 79.1654 268.801 85.1274L268.607 85.3095C262.354 91.1962 256.455 96.7485 245.404 96.7485C234.213 96.7485 227.444 91.0497 220.701 85.1054C220.597 85.0144 220.494 84.9234 220.391 84.8322L220.13 84.6019C213.612 78.8481 206.945 72.9627 196.323 72.9627L196.323 72.9624C183.22 72.9624 176.26 79.1482 169.907 85.1271C169.769 85.2573 169.631 85.3874 169.493 85.5173L169.324 85.6768C163.208 91.4425 157.58 96.7482 147.243 96.7482C136.789 96.7482 130.915 91.3206 124.603 85.4884L124.205 85.1206C117.746 79.1565 110.782 72.9624 98.1623 72.9624C85.5317 72.9624 78.9308 79.1695 72.6584 85.1304L72.5606 85.2234C66.3402 91.1352 60.4342 96.7482 49.0818 96.7482C37.7292 96.7482 31.8231 91.1352 25.6027 85.2234L25.5049 85.1304C19.2325 79.1695 12.6316 72.9624 0.0012207 72.9624L0.00122073 74.9624Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M2.87295e-08 93.2568C11.7873 93.2568 17.8839 98.9426 24.1259 104.875L24.3103 105.05C30.5105 110.943 36.9274 117.043 49.0805 117.043C61.2336 117.043 67.6504 110.943 73.8505 105.05L74.035 104.875C80.2769 98.9426 86.3735 93.2568 98.1611 93.2568C109.96 93.2568 116.426 98.9556 122.847 104.884L123.354 105.352C129.604 111.131 135.998 117.043 147.242 117.043C158.384 117.043 164.538 111.236 170.602 105.514C170.827 105.302 171.052 105.09 171.277 104.878C177.561 98.964 184.03 93.2568 196.322 93.2568L196.322 93.2571C206.178 93.2571 212.326 98.6807 218.95 104.524L219.377 104.9C226.125 110.849 233.397 117.043 245.403 117.043C257.251 117.043 263.662 111.006 269.881 105.15L270.17 104.878C276.472 98.9474 282.692 93.2571 294.483 93.2571C306.257 93.2571 312.107 98.9275 318.226 104.858L318.236 104.868C324.388 110.831 330.806 117.043 343.564 117.043C356.299 117.043 363.443 110.857 369.969 104.888C370.164 104.71 370.359 104.532 370.552 104.354L370.582 104.327C376.88 98.557 382.665 93.2571 392.644 93.2571L392.644 91.2571C381.875 91.2571 375.563 97.0455 369.341 102.751C369.212 102.87 369.082 102.989 368.952 103.107C368.841 103.209 368.73 103.311 368.619 103.412C362.143 109.336 355.491 115.043 343.564 115.043C331.658 115.043 325.745 109.362 319.628 103.432L319.549 103.356C313.426 97.4203 307.068 91.2571 294.483 91.2571C281.856 91.2571 275.134 97.4598 268.799 103.422L268.606 103.604C262.352 109.491 256.454 115.043 245.403 115.043C234.212 115.043 227.443 109.344 220.699 103.4C220.592 103.305 220.485 103.211 220.378 103.116L220.129 102.896C213.611 97.1425 206.944 91.2571 196.322 91.2571L196.322 91.2568C183.219 91.2568 176.259 97.4426 169.906 103.422C169.768 103.552 169.63 103.682 169.492 103.812L169.323 103.971C163.207 109.737 157.579 115.043 147.242 115.043C136.788 115.043 130.914 109.615 124.602 103.783L124.204 103.415C117.744 97.451 110.781 91.2568 98.1611 91.2568C85.5305 91.2568 78.9296 97.4639 72.6572 103.425L72.5594 103.518C66.339 109.43 60.433 115.043 49.0805 115.043C37.728 115.043 31.8219 109.43 25.6014 103.518L25.5037 103.425C19.2312 97.4639 12.6304 91.2568 0 91.2568L2.87295e-08 93.2568Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M1.35596 2C13.1433 2 19.2399 7.6858 25.4819 13.6178L25.6662 13.793C31.8665 19.6865 38.2834 25.7858 50.4365 25.7858C62.5896 25.7858 69.0063 19.6865 75.2065 13.7931L75.3909 13.6178C81.6329 7.6858 87.7295 2 99.517 2C111.316 2 117.782 7.69876 124.203 13.6276L124.709 14.0956C130.96 19.8745 137.354 25.7858 148.598 25.7858C159.74 25.7858 165.894 19.9791 171.958 14.2571C172.183 14.0449 172.408 13.8328 172.633 13.6211C178.917 7.70714 185.386 2 197.678 2L197.678 2.0003C207.534 2.0003 213.682 7.42388 220.306 13.2667L220.733 13.6433C227.481 19.592 234.753 25.7861 246.759 25.7861C258.607 25.7861 265.018 19.7489 271.237 13.8932L271.526 13.6214C277.828 7.69053 284.048 2.0003 295.839 2.0003C307.613 2.0003 313.463 7.67067 319.582 13.6014L319.592 13.6113C325.744 19.5745 332.162 25.7861 344.92 25.7861C357.655 25.7861 364.799 19.6002 371.325 13.6311C371.53 13.4437 371.734 13.2567 371.938 13.0702C378.236 7.30017 384.021 2.0003 394 2.0003L394 0.000299156C383.231 0.000299001 376.919 5.78866 370.697 11.494C370.457 11.7147 370.216 11.9352 369.975 12.1553C363.499 18.0792 356.847 23.7861 344.92 23.7861C333.014 23.7861 327.101 18.1048 320.984 12.1752L320.905 12.0987C314.782 6.16351 308.424 0.000297922 295.839 0.00029774C283.212 0.000297558 276.49 6.20297 270.155 12.165L269.962 12.3471C263.708 18.2338 257.81 23.7861 246.759 23.7861C235.568 23.7861 228.799 18.0873 222.055 12.1431C221.951 12.0514 221.847 11.9596 221.743 11.8678L221.485 11.6395C214.967 5.88568 208.3 0.000296478 197.678 0.000296325L197.678 2.83142e-06C184.575 2.64244e-06 177.615 6.18577 171.262 12.1647C171.124 12.2949 170.986 12.425 170.848 12.5549L170.679 12.7144C164.563 18.4801 158.935 23.7858 148.598 23.7858C138.144 23.7858 132.27 18.3582 125.958 12.526L125.56 12.1582C119.1 6.19415 112.137 1.59771e-06 99.517 1.41571e-06C86.8864 1.23355e-06 80.2856 6.20711 74.0132 12.168L73.9153 12.261C67.695 18.1728 61.789 23.7858 50.4365 23.7858C39.084 23.7858 33.1779 18.1728 26.9574 12.2609L26.8596 12.168C20.5872 6.20711 13.9863 1.82159e-07 1.35596 0L1.35596 2Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M1.35107 20.2954C13.1384 20.2954 19.235 25.9812 25.477 31.9132L25.6613 32.0884C31.8616 37.9819 38.2785 44.0812 50.4316 44.0812C62.5847 44.0812 69.0015 37.9819 75.2016 32.0885L75.3861 31.9132C81.628 25.9812 87.7246 20.2954 99.5121 20.2954C111.311 20.2954 117.777 25.9942 124.198 31.923L124.705 32.391C130.955 38.1699 137.349 44.0812 148.593 44.0812C159.735 44.0812 165.889 38.2745 171.953 32.5525C172.178 32.3403 172.403 32.1282 172.628 31.9165C178.912 26.0026 185.381 20.2955 197.673 20.2954L197.673 20.2957C207.529 20.2957 213.677 25.7193 220.301 31.5621L220.728 31.9388C227.476 37.8874 234.748 44.0815 246.754 44.0815C258.602 44.0815 265.013 38.0443 271.232 32.1886L271.521 31.9168C277.823 25.9859 284.043 20.2957 295.834 20.2957C307.608 20.2957 313.458 25.9661 319.577 31.8968L319.587 31.9067C325.739 37.8699 332.157 44.0815 344.915 44.0815C357.65 44.0815 364.794 37.8956 371.32 31.9265C371.515 31.7482 371.71 31.5701 371.903 31.3925L371.933 31.3656C378.231 25.5956 384.016 20.2957 393.995 20.2957L393.995 18.2957C383.226 18.2957 376.914 24.0841 370.692 29.7894C370.452 30.0101 370.211 30.2306 369.97 30.4507C363.494 36.3746 356.842 42.0815 344.915 42.0815C333.009 42.0815 327.096 36.4003 320.979 30.4706L320.9 30.3941C314.777 24.4589 308.419 18.2957 295.834 18.2957C283.207 18.2957 276.485 24.4984 270.15 30.4604L269.957 30.6425C263.703 36.5292 257.805 42.0815 246.754 42.0815C235.563 42.0815 228.794 36.3827 222.05 30.4385C221.86 30.2708 221.67 30.1029 221.48 29.9349C214.962 24.1811 208.295 18.2958 197.673 18.2957L197.673 18.2954C184.57 18.2954 177.61 24.4812 171.257 30.4601C171.119 30.5904 170.981 30.7204 170.843 30.8503L170.674 31.0098C164.558 36.7755 158.93 42.0812 148.593 42.0812C138.139 42.0812 132.265 36.6536 125.953 30.8214L125.555 30.4536C119.096 24.4896 112.132 18.2954 99.5121 18.2954C86.8815 18.2954 80.2807 24.5025 74.0083 30.4634L73.9104 30.5564C67.6901 36.4682 61.7841 42.0812 50.4316 42.0812C39.0791 42.0812 33.173 36.4682 26.9525 30.5564L26.8547 30.4634C20.5823 24.5025 13.9815 18.2954 1.35107 18.2954L1.35107 20.2954Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M1.34863 38.5894C13.136 38.5894 19.2326 44.2752 25.4746 50.2071L25.6589 50.3824C31.8592 56.2758 38.276 62.3752 50.4292 62.3752C62.5823 62.3752 68.999 56.2759 75.1992 50.3824L75.3836 50.2071C81.6255 44.2752 87.7221 38.5894 99.5097 38.5894C111.308 38.5894 117.774 44.2881 124.196 50.217L124.702 50.685C130.953 56.4639 137.347 62.3752 148.59 62.3752C159.732 62.3752 165.887 56.5684 171.951 50.8464C172.176 50.6342 172.401 50.4221 172.625 50.2105C178.909 44.2965 185.379 38.5894 197.671 38.5894L197.671 38.5897C207.527 38.5897 213.675 44.0132 220.298 49.8561L220.726 50.2327C227.474 56.1814 234.745 62.3755 246.751 62.3755C258.599 62.3755 265.011 56.3382 271.23 50.4825L271.519 50.2108C277.82 44.2799 284.04 38.5897 295.832 38.5897C307.605 38.5897 313.456 44.26 319.574 50.1908L319.584 50.2006C325.737 56.1638 332.155 62.3755 344.912 62.3755C357.648 62.3755 364.792 56.1895 371.318 50.2204C371.513 50.0421 371.707 49.864 371.901 49.6864L371.93 49.6595C378.228 43.8895 384.013 38.5897 393.993 38.5897L393.993 36.5897C383.223 36.5897 376.911 42.378 370.69 48.0833C370.449 48.304 370.209 48.5246 369.968 48.7447C363.491 54.6685 356.84 60.3755 344.912 60.3755C333.007 60.3755 327.094 54.6942 320.976 48.7645L320.898 48.688C314.774 42.7529 308.416 36.5897 295.832 36.5897C283.205 36.5897 276.483 42.7923 270.148 48.7544L269.954 48.9365C263.701 54.8232 257.803 60.3755 246.751 60.3755C235.56 60.3755 228.791 54.6767 222.048 48.7324C221.858 48.5648 221.668 48.3969 221.477 48.2289C214.959 42.4751 208.292 36.5897 197.671 36.5897L197.671 36.5894C184.568 36.5894 177.607 42.7751 171.255 48.7541C171.116 48.8843 170.978 49.0144 170.841 49.1443L170.671 49.3037C164.556 55.0695 158.928 60.3752 148.59 60.3752C138.137 60.3752 132.263 54.9476 125.951 49.1154L125.552 48.7475C119.093 42.7835 112.129 36.5894 99.5097 36.5894C86.8791 36.5894 80.2782 42.7965 74.0059 48.7574L73.908 48.8504C67.6876 54.7622 61.7817 60.3752 50.4292 60.3752C39.0766 60.3752 33.1705 54.7621 26.9501 48.8503L26.8523 48.7574C20.5799 42.7965 13.979 36.5894 1.34863 36.5894L1.34863 38.5894Z" fill="white" fillOpacity="0.7"/>
        </svg>
        <svg width="394" height="118" viewBox="0 0 394 118" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fillRule="evenodd" clipRule="evenodd" d="M0.00756839 56.667C11.7949 56.667 17.8915 62.3528 24.1335 68.2848L24.3178 68.46C30.5181 74.3535 36.935 80.4528 49.0881 80.4528C61.2412 80.4528 67.658 74.3535 73.8581 68.4601L74.0426 68.2848C80.2845 62.3528 86.3811 56.667 98.1686 56.667C109.967 56.667 116.433 62.3658 122.855 68.2946L123.361 68.7626C129.612 74.5415 136.005 80.4528 147.249 80.4528C158.391 80.4528 164.545 74.6461 170.61 68.9241C170.835 68.7119 171.06 68.4998 171.284 68.2881C177.568 62.3741 184.038 56.667 196.33 56.667L196.33 56.6673C206.186 56.6673 212.334 62.0909 218.957 67.9337L219.385 68.3103C226.133 74.259 233.404 80.4531 245.41 80.4531C257.258 80.4531 263.67 74.4159 269.889 68.5601L270.178 68.2884C276.479 62.3575 282.699 56.6673 294.491 56.6673C306.264 56.6673 312.115 62.3377 318.233 68.2684L318.243 68.2782C324.396 74.2415 330.814 80.4531 343.571 80.4531C356.307 80.4531 363.451 74.2671 369.977 68.2981C370.182 68.1107 370.386 67.9237 370.589 67.7372C376.887 61.9672 382.672 56.6673 392.652 56.6673L392.652 54.6673C381.882 54.6673 375.57 60.4557 369.349 66.161C369.108 66.3817 368.868 66.6022 368.627 66.8223C362.15 72.7462 355.499 78.4531 343.571 78.4531C331.666 78.4531 325.753 72.7718 319.635 66.8421L319.557 66.7657C313.433 60.8305 307.075 54.6673 294.491 54.6673C281.864 54.6673 275.142 60.87 268.807 66.832L268.613 67.0141C262.36 72.9008 256.462 78.4531 245.41 78.4531C234.219 78.4531 227.45 72.7543 220.707 66.81C220.603 66.7184 220.499 66.6266 220.395 66.5348L220.136 66.3065C213.618 60.5527 206.951 54.6673 196.33 54.6673L196.33 54.667C183.227 54.667 176.266 60.8528 169.914 66.8317C169.775 66.9619 169.637 67.092 169.499 67.2219L169.33 67.3813C163.214 73.1471 157.587 78.4528 147.249 78.4528C136.796 78.4528 130.922 73.0252 124.61 67.193L124.211 66.8252C117.752 60.8611 110.788 54.667 98.1686 54.667C85.538 54.667 78.9372 60.8741 72.6648 66.835L72.5669 66.928C66.3466 72.8398 60.4406 78.4528 49.0881 78.4528C37.7356 78.4528 31.8295 72.8397 25.609 66.9279L25.5112 66.835C19.2388 60.8741 12.6379 54.667 0.00756836 54.667L0.00756839 56.667Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M0.00122073 74.9624C11.7885 74.9624 17.8851 80.6482 24.1271 86.5802L24.3115 86.7554C30.5117 92.6489 36.9286 98.7482 49.0818 98.7482C61.2348 98.7482 67.6516 92.6489 73.8518 86.7555L74.0362 86.5802C80.2781 80.6482 86.3747 74.9624 98.1623 74.9624C109.961 74.9624 116.427 80.6612 122.848 86.59L123.355 87.058C129.605 92.8369 135.999 98.7482 147.243 98.7482C158.385 98.7482 164.539 92.9415 170.604 87.2195C170.828 87.0073 171.053 86.7952 171.278 86.5835C177.562 80.6695 184.031 74.9624 196.323 74.9624L196.323 74.9627C206.179 74.9627 212.328 80.3863 218.951 86.2291L219.378 86.6057C226.126 92.5544 233.398 98.7485 245.404 98.7485C257.252 98.7485 263.664 92.7113 269.883 86.8555L270.171 86.5838C276.473 80.6529 282.693 74.9627 294.484 74.9627C306.258 74.9627 312.108 80.6331 318.227 86.5638L318.237 86.5736C324.389 92.5369 330.808 98.7485 343.565 98.7485C356.3 98.7485 363.445 92.5625 369.97 86.5935C370.152 86.4274 370.333 86.2616 370.514 86.0961L370.583 86.0326C376.881 80.2626 382.666 74.9627 392.645 74.9627L392.645 72.9627C381.876 72.9627 375.564 78.7511 369.342 84.4564L369.323 84.4742C369.089 84.6889 368.855 84.9035 368.621 85.1177C362.144 91.0416 355.492 96.7485 343.565 96.7485C331.659 96.7485 325.747 91.0672 319.629 85.1375L319.55 85.0611C313.427 79.1259 307.069 72.9627 294.484 72.9627C281.857 72.9627 275.135 79.1654 268.801 85.1274L268.607 85.3095C262.354 91.1962 256.455 96.7485 245.404 96.7485C234.213 96.7485 227.444 91.0497 220.701 85.1054C220.597 85.0144 220.494 84.9234 220.391 84.8322L220.13 84.6019C213.612 78.8481 206.945 72.9627 196.323 72.9627L196.323 72.9624C183.22 72.9624 176.26 79.1482 169.907 85.1271C169.769 85.2573 169.631 85.3874 169.493 85.5173L169.324 85.6768C163.208 91.4425 157.58 96.7482 147.243 96.7482C136.789 96.7482 130.915 91.3206 124.603 85.4884L124.205 85.1206C117.746 79.1565 110.782 72.9624 98.1623 72.9624C85.5317 72.9624 78.9308 79.1695 72.6584 85.1304L72.5606 85.2234C66.3402 91.1352 60.4342 96.7482 49.0818 96.7482C37.7292 96.7482 31.8231 91.1352 25.6027 85.2234L25.5049 85.1304C19.2325 79.1695 12.6316 72.9624 0.0012207 72.9624L0.00122073 74.9624Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M2.87295e-08 93.2568C11.7873 93.2568 17.8839 98.9426 24.1259 104.875L24.3103 105.05C30.5105 110.943 36.9274 117.043 49.0805 117.043C61.2336 117.043 67.6504 110.943 73.8505 105.05L74.035 104.875C80.2769 98.9426 86.3735 93.2568 98.1611 93.2568C109.96 93.2568 116.426 98.9556 122.847 104.884L123.354 105.352C129.604 111.131 135.998 117.043 147.242 117.043C158.384 117.043 164.538 111.236 170.602 105.514C170.827 105.302 171.052 105.09 171.277 104.878C177.561 98.964 184.03 93.2568 196.322 93.2568L196.322 93.2571C206.178 93.2571 212.326 98.6807 218.95 104.524L219.377 104.9C226.125 110.849 233.397 117.043 245.403 117.043C257.251 117.043 263.662 111.006 269.881 105.15L270.17 104.878C276.472 98.9474 282.692 93.2571 294.483 93.2571C306.257 93.2571 312.107 98.9275 318.226 104.858L318.236 104.868C324.388 110.831 330.806 117.043 343.564 117.043C356.299 117.043 363.443 110.857 369.969 104.888C370.164 104.71 370.359 104.532 370.552 104.354L370.582 104.327C376.88 98.557 382.665 93.2571 392.644 93.2571L392.644 91.2571C381.875 91.2571 375.563 97.0455 369.341 102.751C369.212 102.87 369.082 102.989 368.952 103.107C368.841 103.209 368.73 103.311 368.619 103.412C362.143 109.336 355.491 115.043 343.564 115.043C331.658 115.043 325.745 109.362 319.628 103.432L319.549 103.356C313.426 97.4203 307.068 91.2571 294.483 91.2571C281.856 91.2571 275.134 97.4598 268.799 103.422L268.606 103.604C262.352 109.491 256.454 115.043 245.403 115.043C234.212 115.043 227.443 109.344 220.699 103.4C220.592 103.305 220.485 103.211 220.378 103.116L220.129 102.896C213.611 97.1425 206.944 91.2571 196.322 91.2571L196.322 91.2568C183.219 91.2568 176.259 97.4426 169.906 103.422C169.768 103.552 169.63 103.682 169.492 103.812L169.323 103.971C163.207 109.737 157.579 115.043 147.242 115.043C136.788 115.043 130.914 109.615 124.602 103.783L124.204 103.415C117.744 97.451 110.781 91.2568 98.1611 91.2568C85.5305 91.2568 78.9296 97.4639 72.6572 103.425L72.5594 103.518C66.339 109.43 60.433 115.043 49.0805 115.043C37.728 115.043 31.8219 109.43 25.6014 103.518L25.5037 103.425C19.2312 97.4639 12.6304 91.2568 0 91.2568L2.87295e-08 93.2568Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M1.35596 2C13.1433 2 19.2399 7.6858 25.4819 13.6178L25.6662 13.793C31.8665 19.6865 38.2834 25.7858 50.4365 25.7858C62.5896 25.7858 69.0063 19.6865 75.2065 13.7931L75.3909 13.6178C81.6329 7.6858 87.7295 2 99.517 2C111.316 2 117.782 7.69876 124.203 13.6276L124.709 14.0956C130.96 19.8745 137.354 25.7858 148.598 25.7858C159.74 25.7858 165.894 19.9791 171.958 14.2571C172.183 14.0449 172.408 13.8328 172.633 13.6211C178.917 7.70714 185.386 2 197.678 2L197.678 2.0003C207.534 2.0003 213.682 7.42388 220.306 13.2667L220.733 13.6433C227.481 19.592 234.753 25.7861 246.759 25.7861C258.607 25.7861 265.018 19.7489 271.237 13.8932L271.526 13.6214C277.828 7.69053 284.048 2.0003 295.839 2.0003C307.613 2.0003 313.463 7.67067 319.582 13.6014L319.592 13.6113C325.744 19.5745 332.162 25.7861 344.92 25.7861C357.655 25.7861 364.799 19.6002 371.325 13.6311C371.53 13.4437 371.734 13.2567 371.938 13.0702C378.236 7.30017 384.021 2.0003 394 2.0003L394 0.000299156C383.231 0.000299001 376.919 5.78866 370.697 11.494C370.457 11.7147 370.216 11.9352 369.975 12.1553C363.499 18.0792 356.847 23.7861 344.92 23.7861C333.014 23.7861 327.101 18.1048 320.984 12.1752L320.905 12.0987C314.782 6.16351 308.424 0.000297922 295.839 0.00029774C283.212 0.000297558 276.49 6.20297 270.155 12.165L269.962 12.3471C263.708 18.2338 257.81 23.7861 246.759 23.7861C235.568 23.7861 228.799 18.0873 222.055 12.1431C221.951 12.0514 221.847 11.9596 221.743 11.8678L221.485 11.6395C214.967 5.88568 208.3 0.000296478 197.678 0.000296325L197.678 2.83142e-06C184.575 2.64244e-06 177.615 6.18577 171.262 12.1647C171.124 12.2949 170.986 12.425 170.848 12.5549L170.679 12.7144C164.563 18.4801 158.935 23.7858 148.598 23.7858C138.144 23.7858 132.27 18.3582 125.958 12.526L125.56 12.1582C119.1 6.19415 112.137 1.59771e-06 99.517 1.41571e-06C86.8864 1.23355e-06 80.2856 6.20711 74.0132 12.168L73.9153 12.261C67.695 18.1728 61.789 23.7858 50.4365 23.7858C39.084 23.7858 33.1779 18.1728 26.9574 12.2609L26.8596 12.168C20.5872 6.20711 13.9863 1.82159e-07 1.35596 0L1.35596 2Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M1.35107 20.2954C13.1384 20.2954 19.235 25.9812 25.477 31.9132L25.6613 32.0884C31.8616 37.9819 38.2785 44.0812 50.4316 44.0812C62.5847 44.0812 69.0015 37.9819 75.2016 32.0885L75.3861 31.9132C81.628 25.9812 87.7246 20.2954 99.5121 20.2954C111.311 20.2954 117.777 25.9942 124.198 31.923L124.705 32.391C130.955 38.1699 137.349 44.0812 148.593 44.0812C159.735 44.0812 165.889 38.2745 171.953 32.5525C172.178 32.3403 172.403 32.1282 172.628 31.9165C178.912 26.0026 185.381 20.2955 197.673 20.2954L197.673 20.2957C207.529 20.2957 213.677 25.7193 220.301 31.5621L220.728 31.9388C227.476 37.8874 234.748 44.0815 246.754 44.0815C258.602 44.0815 265.013 38.0443 271.232 32.1886L271.521 31.9168C277.823 25.9859 284.043 20.2957 295.834 20.2957C307.608 20.2957 313.458 25.9661 319.577 31.8968L319.587 31.9067C325.739 37.8699 332.157 44.0815 344.915 44.0815C357.65 44.0815 364.794 37.8956 371.32 31.9265C371.515 31.7482 371.71 31.5701 371.903 31.3925L371.933 31.3656C378.231 25.5956 384.016 20.2957 393.995 20.2957L393.995 18.2957C383.226 18.2957 376.914 24.0841 370.692 29.7894C370.452 30.0101 370.211 30.2306 369.97 30.4507C363.494 36.3746 356.842 42.0815 344.915 42.0815C333.009 42.0815 327.096 36.4003 320.979 30.4706L320.9 30.3941C314.777 24.4589 308.419 18.2957 295.834 18.2957C283.207 18.2957 276.485 24.4984 270.15 30.4604L269.957 30.6425C263.703 36.5292 257.805 42.0815 246.754 42.0815C235.563 42.0815 228.794 36.3827 222.05 30.4385C221.86 30.2708 221.67 30.1029 221.48 29.9349C214.962 24.1811 208.295 18.2958 197.673 18.2957L197.673 18.2954C184.57 18.2954 177.61 24.4812 171.257 30.4601C171.119 30.5904 170.981 30.7204 170.843 30.8503L170.674 31.0098C164.558 36.7755 158.93 42.0812 148.593 42.0812C138.139 42.0812 132.265 36.6536 125.953 30.8214L125.555 30.4536C119.096 24.4896 112.132 18.2954 99.5121 18.2954C86.8815 18.2954 80.2807 24.5025 74.0083 30.4634L73.9104 30.5564C67.6901 36.4682 61.7841 42.0812 50.4316 42.0812C39.0791 42.0812 33.173 36.4682 26.9525 30.5564L26.8547 30.4634C20.5823 24.5025 13.9815 18.2954 1.35107 18.2954L1.35107 20.2954Z" fill="white" fillOpacity="0.7"/>
          <path fillRule="evenodd" clipRule="evenodd" d="M1.34863 38.5894C13.136 38.5894 19.2326 44.2752 25.4746 50.2071L25.6589 50.3824C31.8592 56.2758 38.276 62.3752 50.4292 62.3752C62.5823 62.3752 68.999 56.2759 75.1992 50.3824L75.3836 50.2071C81.6255 44.2752 87.7221 38.5894 99.5097 38.5894C111.308 38.5894 117.774 44.2881 124.196 50.217L124.702 50.685C130.953 56.4639 137.347 62.3752 148.59 62.3752C159.732 62.3752 165.887 56.5684 171.951 50.8464C172.176 50.6342 172.401 50.4221 172.625 50.2105C178.909 44.2965 185.379 38.5894 197.671 38.5894L197.671 38.5897C207.527 38.5897 213.675 44.0132 220.298 49.8561L220.726 50.2327C227.474 56.1814 234.745 62.3755 246.751 62.3755C258.599 62.3755 265.011 56.3382 271.23 50.4825L271.519 50.2108C277.82 44.2799 284.04 38.5897 295.832 38.5897C307.605 38.5897 313.456 44.26 319.574 50.1908L319.584 50.2006C325.737 56.1638 332.155 62.3755 344.912 62.3755C357.648 62.3755 364.792 56.1895 371.318 50.2204C371.513 50.0421 371.707 49.864 371.901 49.6864L371.93 49.6595C378.228 43.8895 384.013 38.5897 393.993 38.5897L393.993 36.5897C383.223 36.5897 376.911 42.378 370.69 48.0833C370.449 48.304 370.209 48.5246 369.968 48.7447C363.491 54.6685 356.84 60.3755 344.912 60.3755C333.007 60.3755 327.094 54.6942 320.976 48.7645L320.898 48.688C314.774 42.7529 308.416 36.5897 295.832 36.5897C283.205 36.5897 276.483 42.7923 270.148 48.7544L269.954 48.9365C263.701 54.8232 257.803 60.3755 246.751 60.3755C235.56 60.3755 228.791 54.6767 222.048 48.7324C221.858 48.5648 221.668 48.3969 221.477 48.2289C214.959 42.4751 208.292 36.5897 197.671 36.5897L197.671 36.5894C184.568 36.5894 177.607 42.7751 171.255 48.7541C171.116 48.8843 170.978 49.0144 170.841 49.1443L170.671 49.3037C164.556 55.0695 158.928 60.3752 148.59 60.3752C138.137 60.3752 132.263 54.9476 125.951 49.1154L125.552 48.7475C119.093 42.7835 112.129 36.5894 99.5097 36.5894C86.8791 36.5894 80.2782 42.7965 74.0059 48.7574L73.908 48.8504C67.6876 54.7622 61.7817 60.3752 50.4292 60.3752C39.0766 60.3752 33.1705 54.7621 26.9501 48.8503L26.8523 48.7574C20.5799 42.7965 13.979 36.5894 1.34863 36.5894L1.34863 38.5894Z" fill="white" fillOpacity="0.7"/>
        </svg>
      </div>
    </div>
  )
}



