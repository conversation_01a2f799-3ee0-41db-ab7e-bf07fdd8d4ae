function getDotColor(postType) {
  let colloredArray = {
    formation: {
      front:"#262424",
      back:"#FFFFFF"
    },
    emailJourney: {
      front:"#AA2DD6",
      back:"#FFFFFF"
    },
    article: {
      front:"#000000",
      back:"#FFFFFF"
    },
    default: {
      front:"#000000",
      back:"#FFFFFF"
    }
  }
  return colloredArray[postType] ? colloredArray[postType] : colloredArray["default"]
}

export { getDotColor };

function hexToRgb(hex) {
  let result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null;
}

export {
  hexToRgb
}
