import styled from "styled-components";
import { useContext } from "react";
import { HeaderContext } from "context/HeaderContext";
import { device } from "styles/device";
import Link from "next/link";

export default function MenuButtons({ invert }) {

  const { headerState, toggleMenu, onDropDownClickOutside } = useContext(HeaderContext);

  return (
    <Wrapper invert={invert} menuOpen={headerState.showMenu}>
      <Link href={"/recherche"}>
        <MenuButton onClick={() => { toggleMenu(true); onDropDownClickOutside() }}>
          <SearchIcon/>
        </MenuButton>
      </Link>
      <MenuButton onClick={() => { toggleMenu(); onDropDownClickOutside() }}>
        <MenuIcon/>
      </MenuButton>
    </Wrapper>
  )
}


const Wrapper = styled.div`
  position: relative;
  z-index: 9999;
  display: flex;
  align-items: center;
  height: 101%;
  
  padding-right: var(--border-space);
  padding-left: 32px;
  flex-direction: row;
  gap: 8px;
  
  //border-bottom: ${p => p.menuOpen ? "1px solid #39474D" : "1px solid transparent"};
  transition-delay: 300ms;
  
  svg {
    path {
      fill: ${p => p.menuOpen || p.invert ? "var(--c-cream)" : "black"};
      transition: fill 450ms ease-in-out;
    }
  }
  
  .menu-icon {
    display: flex;
    flex-direction: column;
    align-items: end;
    gap: ${p => p.menuOpen ? 5 : 3}px;
    transition: all 250ms ease-in-out;
    width: 32px;
    hr {
      transform-origin: right;
      color: ${p => p.menuOpen || p.invert ? "var(--c-cream)" : "black"};
      border: 1.2px solid ${p => p.menuOpen || p.invert ? "var(--c-cream)" : "black"};
      margin: 0;
      transition: all 250ms ease-in-out;
    }
    hr:nth-child(1) {
      width: ${p => p.menuOpen? 24 : 27 }px;
      transform: ${p => p.menuOpen? "rotate(-35deg)" : "rotate(0)" };
    }
    hr:nth-child(2){
      width: ${p => p.menuOpen? 0 : 21 }px;
      opacity: ${p => p.menuOpen? 0 : 1 };
    }
    hr:nth-child(3){
      width: 24px;
      transform: ${p => p.menuOpen? "rotate(35deg)" : "rotate(0)" };
    }
  }
  
  @media ${device.desktop} {
    //margin-right: 15px;
  }
`;

const MenuButton = styled.button`
  outline: none;
  border: none;
  display: block;
  background-color: transparent;
  cursor: pointer;
  height: 48px;
  width: 48px;
  
  &:hover {
    * {
      path {
        fill: var(--brand-color);
      }
    }
    * {
      hr {
        color: var(--brand-color);
        border-color: var(--brand-color);
      }
    }
  }
`;


const SearchIcon = () => {
  return (
    <svg width="20" height="19" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd"
        d="M15.2104 8.31546C15.2104 11.7473 12.4283 14.5294 8.99641 14.5294C5.56455 14.5294 2.78247 11.7473 2.78247 8.31546C2.78247 4.8836 5.56455 2.10152 8.99641 2.10152C12.4283 2.10152 15.2104 4.8836 15.2104 8.31546ZM14.0588 14.7844C12.6637 15.8776 10.9062 16.5294 8.99641 16.5294C4.45998 16.5294 0.782471 12.8519 0.782471 8.31546C0.782471 3.77903 4.45998 0.101524 8.99641 0.101524C13.5328 0.101524 17.2104 3.77903 17.2104 8.31546C17.2104 10.2213 16.5612 11.9756 15.4721 13.3693L19.6182 17.5154L18.204 18.9296L14.0588 14.7844Z"
        fill="#161616"/>
    </svg>
  )
}

const MenuIcon = () => {
  return (
    <div className={"menu-icon"}><hr/><hr/><hr/></div>
  )
}