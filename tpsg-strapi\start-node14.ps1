# This script sets up the environment to use Node.js 14.21.3
# It modifies the PATH variable for the current PowerShell session only

# Store the original PATH
$originalPath = $env:PATH

# Add Node.js 14.21.3 to the beginning of the PATH
$nodePath = "C:\Program Files\nvm\v14.21.3"
$env:PATH = "$nodePath;$env:PATH"

# Check if it worked
$nodeVersion = & "$nodePath\node.exe" -v
Write-Host "Now using Node.js $nodeVersion" -ForegroundColor Green
Write-Host "PATH has been modified for this PowerShell session only." -ForegroundColor Yellow
Write-Host "You can now run your Node.js 14 commands." -ForegroundColor Yellow

# Create a function to restore the original PATH
function Restore-OriginalPath {
    $env:PATH = $originalPath
    Write-Host "Original PATH restored." -ForegroundColor Green
    Write-Host "Current Node.js version:" -ForegroundColor Yellow
    node -v
}

Write-Host "`nWhen you're done, you can restore the original PATH by running:" -ForegroundColor Cyan
Write-Host "Restore-OriginalPath" -ForegroundColor Cyan

# Export the function so it's available in the current session
Export-ModuleMember -Function Restore-OriginalPath
