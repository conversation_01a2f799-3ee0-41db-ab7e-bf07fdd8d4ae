const PodcastPMM = () => {
  return (
    <svg width="96" height="96" viewBox="0 0 118 118" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M59 118C91.5848 118 118 91.5848 118 59C118 26.4152 91.5848 0 59 0C26.4152 0 0 26.4152 0 59C0 91.5848 26.4152 118 59 118ZM59 109.26C31.2422 109.26 8.74008 86.7578 8.74008 59C8.74008 31.2422 31.2422 8.74008 59 8.74008C86.7578 8.74008 109.26 31.2422 109.26 59C109.26 86.7578 86.7578 109.26 59 109.26ZM49.5 44.5085C49.5018 44.509 49.5035 44.5095 49.5053 44.5101V53.4086C49.5035 53.4077 49.5018 53.4068 49.5 53.4058V53.5C49.4067 53.4068 49.29 53.3172 49.1545 53.2313C45.4011 51.412 40.9763 51.725 37.8889 54.1843C36.7179 55.2429 35.5413 56.8972 34.5002 59.5C33.2695 62.5767 34.3265 65.8507 35.4868 67.9566C35.9142 68.6595 36.4229 69.3112 37.0036 69.8975L55.9 88.9568C59.2101 90.7741 62.2389 88.9014 63.5002 87.5L68.7019 82.2983L80.4002 70.4992L80.4037 70.4956C80.5185 70.3792 80.6304 70.261 80.7395 70.1411L82.5002 67.5L83.0002 66C83.3867 64.7116 83.8927 62.009 83.5482 59.5552C83.1696 58.0204 82.4471 56.5885 81.3797 55.3917L81.3731 55.3843C80.4245 54.3155 79.2698 53.4529 77.9796 52.8494C76.8804 52.3353 75.7026 52.0184 74.4988 51.9111C72.0042 51.9277 69.585 52.7076 68.0002 53.5V44.5C68.0271 44.4821 68.0543 44.4644 68.0818 44.4471V44.375C68.1673 44.3485 68.2529 44.3227 68.3386 44.2974C70.1247 43.3372 72.9798 43.3536 75.2191 43.6154C76.952 43.7693 78.655 44.166 80.2789 44.7939L81.0002 45C81.9072 45.324 83.2726 46.0609 84.7674 47.2873C85.7643 48.037 86.6871 48.887 87.521 49.826C89.2586 51.7752 90.4797 53.9747 91.2253 56.2867C91.3194 56.5204 91.4111 56.7582 91.5002 57C93.1498 61.4775 92.2194 66.0591 90.6311 69.6815C89.6347 72.1511 88.1571 74.4424 86.2529 76.3727L80.3474 82.3291C74.172 88.6613 66.8315 96.1687 64.0002 99C62.0109 100.989 59.9392 101.412 58.154 101.13C57.2971 101.014 56.4744 100.725 55.735 100.283C55.0177 99.8887 54.4231 99.4229 54.0002 99L48.7598 93.5318L31.1655 75.7858L31.1645 75.7848C29.3658 73.9693 27.953 71.803 27.0125 69.4182C26.8375 68.9745 26.6795 68.525 26.5388 68.0707C26.5134 68.0245 26.5 68 26.5 68C26.191 67.0728 25.9894 66.1058 25.8768 65.123C25.726 64.0634 25.6644 62.9902 25.6941 61.9147C25.7648 59.3492 26.3534 56.8251 27.424 54.4968C27.502 54.327 27.5825 54.1586 27.6654 53.9915C28.0992 52.9744 28.5616 52.1211 29 51.5C29.2724 51.1595 29.6464 50.7327 30.1066 50.2555C30.6648 49.5891 31.2694 48.961 31.9167 48.3762C33.4829 46.956 35.2121 45.8778 37.0316 45.1198C37.5112 44.8899 38.0015 44.6813 38.5 44.5C42.9 42.9 47.6667 43.8333 49.5 44.5V44.5085ZM87.1597 28H31C29.5 27.8333 26.5 28.4 26.5 32C26.9 35.6 29.6667 36.5 31 36.5H55V67L47 59C45.8333 58.5 43.1 57.9 41.5 59.5C39.9 61.5 40.5 63.6667 41 64.5L56.5 80C58.9 82.4 61.5 81 62.5 80L75.5 66.5C76.6667 65.5 78.5 62.9 76.5 60.5C74.5 58.1 71.6667 59.1667 70.5 60L63 67.5V36.5H87.1597C88.4398 36.3333 91 35.2 91 32C91 28.8 88.4398 28 87.1597 28Z" fill="black"/>
    </svg>
  )
}

export default PodcastPMM;