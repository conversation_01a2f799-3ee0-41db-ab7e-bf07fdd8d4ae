@echo off
echo Mise à jour de Meilisearch dans Strapi vers la version 0.30.0...
echo.
echo Note: Bien que le serveur Meilisearch en préproduction soit en version 0.30.5,
echo le package npm disponible est en version 0.30.0.
echo.

REM Aller dans le répertoire du projet Strapi
cd tpsg-strapi

REM Sauvegarder le package.json actuel
echo Sauvegarde du package.json actuel...
copy package.json package.json.backup

REM Mettre à jour la version de Meilisearch dans package.json
echo Mise à jour de la version de Meilisearch dans package.json...
powershell -Command "(Get-Content package.json) -replace '\"meilisearch\": \"\^0.27.0\"', '\"meilisearch\": \"\^0.30.0\"' | Set-Content package.json"

REM Installer les dépendances avec la nouvelle version de Meilisearch
echo Installation des dépendances...
call npm install

echo.
echo Mise à jour terminée !
echo.
echo IMPORTANT:
echo 1. Assurez-vous que Meilisearch v0.30.5 est en cours d'exécution (utilisez start-meilisearch-v0.30.5.bat)
echo 2. Strapi nécessite Node.js v14, alors que Next.js nécessite Node.js v18
echo 3. Utilisez les scripts appropriés pour démarrer Strapi avec Node.js v14
echo.
echo Appuyez sur une touche pour fermer cette fenêtre...
pause > nul
