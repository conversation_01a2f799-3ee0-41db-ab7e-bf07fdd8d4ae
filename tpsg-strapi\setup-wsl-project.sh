#!/bin/bash
# Script pour configurer un environnement de développement dans WSL

# Créer un répertoire de projet dans WSL
mkdir -p ~/projects/tpsg-strapi

# Copier le projet depuis Windows vers WSL
echo "Copie du projet depuis Windows vers WSL..."
cp -r /mnt/c/rep/TPSG/tpsg-strapi/* ~/projects/tpsg-strapi/

# Aller dans le répertoire du projet
cd ~/projects/tpsg-strapi

# Installer les dépendances
echo "Installation des dépendances..."
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
npm install

echo "Configuration terminée!"
echo "Votre projet est maintenant disponible dans ~/projects/tpsg-strapi"
echo "Pour y accéder depuis Windows, vous pouvez utiliser le chemin: \\\\wsl$\\Ubuntu\\home\\$USER\\projects\\tpsg-strapi"
