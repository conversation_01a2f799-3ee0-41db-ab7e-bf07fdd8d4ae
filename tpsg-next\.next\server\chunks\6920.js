"use strict";exports.id=6920,exports.ids=[6920],exports.modules={2556:(t,e,i)=>{i.d(e,{Z:()=>ListLink});var o=i(997),n=i(7518),r=i.n(n),s=i(1664),l=i.n(s),p=i(7467);function ListLink({route:t,image:e,text:i}){return o.jsx(l(),{href:t,children:o.jsx(d,{children:o.jsx("div",{className:"text-container",children:o.jsx("p",{className:"ll-text",children:i})})})})}let d=r().a.withConfig({componentId:"sc-1bebecf-0"})`
  font-family: Switzer, sans-serif;
  font-weight: 500;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: -1px;
  border-top: 1px solid #CCCAC7;
  border-bottom: 1px solid #CCCAC7;
  padding: 10px 0 10px 4px;
  color: #161616;
  font-size: 18px;
  
  .ll-text {
    margin: 0;
  }

  &:after {
    content: "→";
    position: absolute;
    right: 0;
    line-height: 100%;
    padding-bottom: 4px;
  }
  
  &:hover {
    cursor: pointer;
    color: var(--brand-color);
  }

  @media ${p.U.tablet} {
    font-size: 22px;
    padding: 12px 0 12px 4px;
  }
`}};