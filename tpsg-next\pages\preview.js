import client from "../api/apollo-client";
import { gql } from "@apollo/client";
import {
  PodcastLayout,
  ArticleLayout,
  WebinarLayout,
} from "components/layout/PagesLayout";

import { modulesAsObj } from "../utils/components.utils";
import { getPostRoute } from "../utils/posts.utils";

export default function Preview(props) {

  if(!props.post) return;

  switch(props.post.type) {
  case "article":
    return <ArticleLayout post={props.post} preview={true}/>
  case "podcast":
    return <PodcastLayout episode={props.post} preview={true} />
  case "webinaire":
    return <WebinarLayout episode={props.post} preview={true} relatedPosts={[]} />
  default:
    return ""
  }
}

export async function getServerSideProps({ query }) {

  const validSecret  = query.secret === process.env.CLIENT_PREVIEW_SECRET;

  if(validSecret) {
    const post = await client
      .query({
        query: QUERY_POST,
        variables: { slug: query.slug },
      })
      .then((response) => {
        return response.data.posts[0];
      });
    if(!post) {
      return {
        notFound: true,
      };
    }

    return {
      props: {
        post: {
          ...post,
          modules: modulesAsObj(post.modules),
          route: getPostRoute(post)
        },
      }
    };
  }
  return {
    notFound: true,
  };
}

const QUERY_POST = gql`
    query PostPreview($slug: String!) {
        posts(where: { slug: $slug, _publicationState: "preview" }) {
            title
            type
            slug
            body
            id
            image {
                url
                provider
            }
            topics {
                name
                slug
            }
            author {
                fullName
                about
                picture {
                    url
                    provider
                }
            }
            modules {
                ... on ComponentModulePodcast {
                    __typename
                    podcast {
                        slug
                        name
                        logoSmall {
                            url
                            provider
                        }
                        platforms {
                            name
                            url
                        }
                    }
                    embedAudio
                    embedVideo
                }
                ... on ComponentModuleWebinar{
                    __typename
                    webinar {
                        slug,
                    }
                    speakers {
                        fullName
                        picture {
                            url
                            provider
                        }
                    }
                    embedVideo
                }
                ... on ComponentModuleEvent{
                    __typename
                    date
                    url
                }
                ... on ComponentModuleLead {
                    __typename
                    content
                }
                ... on ComponentModuleSeo {
                    metaDescription
                    metaTitle
                }
            }
        }
    }
`;
