"use strict";exports.id=9296,exports.ids=[9296],exports.modules={9296:(t,i,e)=>{e.d(i,{Z:()=>CornerStoneCard});var o=e(997);e(1664);var a=e(7518),n=e.n(a),r=e(7467),d=e(6453);e(1077);var s=e(2558),l=e(2570);function CornerStoneCard({post:t,options:i}){let{showAuthor:e,showBlur:a,aspectRatio:n=1}=i,r=t?.cta?.url||t?.link||(t?.route?.startsWith("/")?t.route:"/"+t?.route);return r?o.jsx(c,{children:o.jsx(l.Z,{link:r,children:(0,o.jsxs)(h,{children:[(0,o.jsxs)(x,{aspectRatio:n,children:[a&&o.jsx(p,{aspectRatio:n,children:o.jsx(s.Z,{imageData:t.image})}),o.jsx(s.Z,{imageData:t.image})]}),(0,o.jsxs)(m,{children:[o.jsx("h2",{className:"corner-stone-title",children:t?.title}),e&&t.author&&o.jsx(d.My,{children:t.author?.fullName?t.author.fullName:t.author})]})]})})}):o.jsx(o.Fragment,{})}let p=n().div.withConfig({componentId:"sc-9da25472-0"})`
  position: absolute;
  width: 100%;
  filter: blur(12px);
  aspect-ratio: ${t=>t.aspectRatio};
`,c=n().div.withConfig({componentId:"sc-9da25472-1"})`
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 50px;
  box-shadow: 0 7px 14px 3px rgba(0, 0, 0, 0.29);
  a {
    width: 100%;
    display: flex;
    justify-content: center;
  }
`,m=n().div.withConfig({componentId:"sc-9da25472-2"})`
  width: 70%;
  padding-left: 20px;
  @media ${r.U.mini} {
    width: 100%;
    padding-left: 0;
  }
  @media ${r.U.desktop} {
    padding-left: 0;
    width: 100%;
  }
`,x=n().div.withConfig({componentId:"sc-9da25472-3"})`
  position: relative;
  width: 30%;
  margin-bottom: 24px;
  aspect-ratio: ${t=>t.aspectRatio};


  @media ${r.U.desktop} {
    width: 100%;
  }
  @media ${r.U.mini} {
    width: 80%;
  }
`,h=n().div.withConfig({componentId:"sc-9da25472-4"})`
  color: white;
  background-color: #0F0F0F;
  padding: 20px 20px;

  width: 100%;
  display: flex;
  flex-wrap: wrap;

  .corner-stone-title {
    padding-top: 8px;
    font-weight: 400;
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 1.25rem;
    font-size: clamp(1.25rem, 1.178635147190009rem + 0.35682426404995543vw, 1.5rem);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  @media ${r.U.desktop} {
    .corner-stone-title {
      -webkit-line-clamp:  2!important;
    }
  }
  @media ${r.U.mini} {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
  }
`}};