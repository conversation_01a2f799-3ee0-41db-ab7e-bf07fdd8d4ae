# TPSG Backoffice

1. [Strapi](#1-strapi)
2. [Les content-type](#2-les-content-types)
3. [<PERSON><PERSON><PERSON><PERSON>](#3-meilisearch)
4. [Tâches cron](#4-tâches-cron)
5. [Custom endpoints](#5-custom-endpoints)
6. [lifecycles](#6-lifecycles)
7. [Ressources similaires](#7-ressources-similaires)
8. [Éditeur](#8-éditeur)

## 1) Strapi

Nous utilisons la version 3 de Strapi. La documentation n'est donc pas accessible directement depuis
le site principal, mais peut se retrouver à cet endroit
[Documentation Strapi V3](https://docs-v3.strapi.io/developer-docs/latest/getting-started/introduction.html)

## 2) Les content-types

Leur structure est définie dans les
fichiers:  `api/[content-type-name]/models/[content-type-name].settings.json`. Nous détaillons
ci-dessous certains aspects et cas d'utilisations des plus important afin de lever de possibles
ambiguïtés.

### post

On considère un post comme étant soit:

- un article
- un épisode de podcast
- un épisode de webinaire
- un parcours email
- une formation

La différenciation entre ces ressources passe par l'ajout de modules lors de la création du post. Si
par exemple, un éditeur ajoute un module webinaire sur un post, ce dernier se verra attribuer le
type 'webinaire' automatiquement juste avant l'enregistrement. Ce mécanisme sera détaillé en guise
d'exemple dans la partie 'Lifecycles'.

### cron-post

Ce content type permet de conserver la trace des opérations de création / suppression et de 
modification ayant eu lieu sur les posts. Il possède les 3 attributs suivants:

- id
- title
- trigger (énumération: 'update' ; 'create' ; 'delete')

À chaque fois qu'un post subit une de ces opérations CRUD, une nouvelle entrée est créée. Son 
nom (cron-post) vient du fait que ce content-type est éssentiellement utilisé pendant les tâches 
cron. Il nous permet de connaitre les posts qui doivent être mis à jour ou supprimé sur 
Meilisearch. Sans cette table, nous n'aurions par la possibilité de connaitre l'identifiant des 
posts qui ont été supprimés entre deux passages des tâches cron. 

> À savoir: Sachant que les nouveaux posts subissent une mise à jour dès lors qu'ils sont publiés, 
> ils sont, eux aussi, ajoutés sur Meilisearch étant donné que la fonction `index.updateDocument()` 
> fonctionne en upsert.

## podcast et webinar

À ne pas confondre avec un épisode, les content-type podcast et webinar permettent de contenir les
informations relatives à une chaine de podcast / webinar. On appelle une chaine, un podcast en
lui-même comme: "Memento Mori", "  Un pasteur vous répond..."

## topic-group

Il existe sur TPSG, une façon de regrouper certains thèmes à travers ce qu'on appelle une vocation
ou un ministère. Les `topic-group` nous permettent de définir les thèmes associés à chaque vocation
ou ministère. Chaque `topic-group`possède le type ministère ou vocation.
Détails: `api/topic-group/models/topic-group.settings.json`.

## 3) Meilisearch

Chacun des posts créés sur le site sont indexés sur une instance de Meilisearch. Cette indexation
nous permet, d'une part de créer un moteur de recherche plus éfficace et d'autre part, de récupérer
plus simplement et plus rapidement le contenu à afficher sur certaines pages du
front.[Documentation de Meilisearch](https://www.meilisearch.com/docs)

Pour communiquer avec notre instance de Meilisearch, nous utilisons le client Javascript  
officiel:[Meilisearch JS Client](https://github.com/meilisearch/meilisearch-js#readme)

Les principales fonctions liées à Meilisearch et le détail de leur utilisation se trouvent dans  
`config/functions/meilisearch`

## 4) Tâches cron

Les tâches cron sont utilisées pour 3 choses en particulier:

- la publication des posts avec une date de programmation
- la synchronisation du contenu entre Strapi et Meilisearch
- la suppression des popups dont la date de fin de vie est atteinte.

Le fichier contenant les différentes actions liés aux tâches cron se trouve
dans: `config/functions/cron.js`

## 5) Custom endpoints

Le code des points d'api custom se retrouvent dans les fichiers:  
`api/[content-type]/controllers/[content-type].js`

Pour être actifs, ils doivent être ajoutés dans le fichier
`api/[content-type]/config/routes.json` et avoir étés activés depuis l'interface de strapi dans
Settings > Rôles (de User & Permissions plugin) > public.

> Note: Une grande partie des routes ont été utilisées lors de la migration du  
> contenu de wordpress vers Strapi.  
> Toutes n'ayant pas encore été supprimées, il est donc encore possible de  
> retrouver certaines d'entre elles  
> dans le code.

| route                            | description                                                                                             |  
|----------------------------------|---------------------------------------------------------------------------------------------------------|  
| /podcsat/set-count               | met à jour le nombre d'épisode présent dans toutes les chaines de podcast                               |  
| /webinar/set-count               | met à jour le nombre d'épisode présent dans toutes les chaines de webinar                               |  
| /topic/children?id=[topic-id]    | retourne la liste de tous les enfants du thème                                                          |  
| /post/related-posts?id=[post-id] | retourne une liste de posts en lien avec le post passé en paramètre                                     |  
| /post/redirects                  | retourne une liste des redirections à faire résultant du changemetn de structure entre strapi wordpress |  
| /post/addreadingtime             | met à jour le temps de lecture de tous les posts                                                        |                                                      |  

## 6) Lifecycles

Les lifecycles se trouvent dans: `api/[content-type]/models/[content-type].js`.  
Ils permettent de définir les actions qui doivent être réalisées avant ou après la création, mise à
jour ou suppression d'une ressource.

> Même si les actions sont en principe assez bien commentés dans les fichiers, il est important de
> connaître certains aspects de Strapi afin de comprendre correctement le but de certaines parties
> du
> code.

### Appartenance du contenu publié

Sur Strapi, il existe 3 rôles d'utilisateurs principaux:

- les administrateurs
- les éditeurs
- les auteurs

Les auteurs (ce sont les blogueurs sur TPSG) n'ont par défaut pas la possibilité  
de voir le contenu qu'ont créé les éditeurs. Ils n'ont accès qu'au contenu qu'ils ont eux même créé.
Pour qu'ils puissent voir et éditer le post qu'un des éditeurs aura créé pour eux, il faut qu'on
force le créateur à être l'utilisateur lié au blog défini sur le post en question

Pour ça, on utilise donc le lifecycle `after-create` du content-type post dans lequel on ajoute le
bout de code suivant:

```js  
if (result.blog && result.blog?.author_user) {
  const knex = strapi.connections.default;
  await knex('posts').update({created_by: result.blog.author_user}).where('id', result.id);
}  
```  

`result` contient les données enregistrées pour le post. Ici, on vérifie donc que le post a été
associé à un blog et que le blog est bien lié à un utilisateur du site (un des blogueurs). Si c'est
le cas, on met à jour le post en changeant la valeur de `created_by` pour qu'elle corresponde au bon
auteur et non à l'éditeur. De cette façon, le blogueur peut voir apparaitre la ressource sur son
panneau d'administration.

> Cela ne change rien pour les éditeurs qui eux, ont des droits d'édition sur l'ensemble du
> contenu.

### Typage des posts

Comme spécifié plus haut le typage des posts se fait en fonctions des modules qui leur sont
associés. Pour ajouter ce type aux posts, nous utilisons donc le lifecycle 'beforeCreate' avec ce
code:

```javascript
const {type, tags} = await strapi.services["post"].getMetas(data);
data.type = type;
data.tags = tags;
```

On peut voir ici l'utilisation de la fonction getMetas d'un service 'post'. On utilise les services
pour définir les différentes fonctions liées à un type de contenu en particulier.

```js
// api/post/services/post.js
for (const module of data.modules || []) {
  switch (module.__component) {
    case "module.webinar":
      metas.type = "webinaire";
      mediaId = module?.webinar?.id ? module.webinar.id : module.webinar;
      metas.tags = await getNewTags(metas.tags, "webinar", mediaId);
      break;
    // ...
  }
}
```

Cette partie de la fonction `getMetas` montre aussi l'ajout d'un tag supplémentaire au post. Ce tag
sert à d'identifier la chaine du webinaire à laquelle le post est associé. Cela nous permet d'avoir
l'information en dehors du module et de trier le contenu par chaines plus simplement.

### HomePage

La page d'accueil est construite avec un ensemble de modules permettant de définir le contenu de
chacune des sections. Sa mise à jour est déclenchée par tout événement de modification d'un post
publié.

```js
// api/post/model/post.js
// [...]

if (!data.import && data.published_at) {
  strapi.services['home'].updateSections();
}
```

Vérifier que le champ `data.published_at` contient une valeur non null permet de savoir si le post à
été ou vient d'être publié. On retrouve l'utilisation d'un service lié à la home page qui nous
permet de mettre à jour l'ensemble des sections.

Les fichiers contenant toutes les opérations de mise à jour de la Home page sont:
`api/home/<USER>/home.js` et `config/functions/updates.js`

## 7) Ressources similaires

La route `/post/related-posts?id=[post-id]` de l'api des posts utilise la fonction `related()`
définie dans le fichier `/api/post/services/post.js`. Elle retourne une liste contenant les 4 posts
ayant le plus de similarités avec celui qui est passé en paramètre. Dans le code, il est fait
référence à ce dernier en parlant de post d'origine.

1. On récupère l'ensemble des posts qui partagent au moins 1 thème (topic) avec le post d'origine.
2. On attribue un score à chacun des posts récupérés. Ce score est basé sur les règles suivantes:
    - 1 point par topic partagé
    - 2 points par tag partagé
    - 4 points s'il est défini en tant que corner stone
3. On crée la liste à retourner en commençant par y ajouter (s'ils existent) les posts contenant
   un webinaire ou une formation qui ont le meilleur score. Ces posts seront identifiables par
   la suite avec l'attribut `section` qui sera égal à `formation` ou `webinar`.
4. On ajoute ensuite deux autres posts (d'un quelconque type) qui ont les meilleurs scores.
   l'attribut `section` de ces deux posts vaut `Default`.
5. Si un des topics du post d'origine fait partie d'un ministère ou d'une vocation, on récupère
   deux nouvelles listes de posts faisant partie de ces groupes, et on leur attribue des scores.
6. Si on obtient un post scoré d'une de ces listes, on l'ajoute à la liste à retourner. Leur
   attribut `section` correspond à `vocation` ou `ministère`.
7. S'il n'existe pas de vocation ou de ministère en lien avec le post ou qu'aucun post n'a pu
   être retourné d'une des deux listes, on ajoute alors les prochains posts de la
   première liste de posts scorés.

Au final, on peut donc obtenir une liste à retourner qui ressemble à cela:

- 1 post de type webinaire (optionnel)
- 1 post de type formation (optionnel)
- 2 posts de type quelconque
- 1 post dans le même ministère OU 1 post de type quelconque
- 1 post dans la même vocation OU 1 post de type quelconque

## 8) Éditeur

L'éditeur markdown utilisé sur le backoffice est un plugin qui permet de remplacer celui par 
défaut. Il est construit sur un fork de l'éditeur de Outline:

- site de Outline: https://www.getoutline.com/
- repo: https://github.com/outline/rich-markdown-editor (archive)
- fork: https://github.com/Darvey/rich-markdown-editor

Les fichiers principaux qui permettent de faire des modifications sur son intégration se 
trouvent dans les dossiers suivants:

- plugins/outline-wysiwyg/admin/src/components/OutlineEditor
- plugins/outline-wysiwyg/admin/src/components/Wysiwyg

> Note: Strapi à récemment sorti une nouvelle mouture de son éditeur dans sa version 4. Nous 
> prévoyons donc de repasser sur ce dernier lors de la migration vers la dernière version de 
> Strapi. Lors de ce changement, il sera peut-être nécessaire de refaire une conversion des 
> posts vers le format html et modifier les composants en charge d'afficher leur contenu sur le
> front.
