import Link from "next/link";
import CondImage from "components/shared/condimage";
import styled from "styled-components";
import { PostCardDescription, PostCardDetails, PostCardTitle } from "styles/styled-typography";
import Preview from "../Preview";
import CondLink from "../CondLink";


export default function GridCard({ post }) { // {isExternal, title, image, link, details, lead, type }

  if (!post?.link) return null;

  return (
    <CardWrapper>
      <CondLink link={post.link}>
        {post?.image && (
          <PostImage>
            <div className="post-image-container">
              <CondImage imageData={post.image}/>
            </div>
          </PostImage>
        )}
        <div>
          <PostCardDetails
            className={post.details.length === 0 ? "post-card-details-space" : ""}>{post.details}</PostCardDetails>
          <PostCardTitle>{post.title}</PostCardTitle>
          <PostCardDescription>
            {post.lead ? post.lead : ""}
          </PostCardDescription>
        </div>
      </CondLink>
      { post.youtubeEmbed &&
        <Preview style={previewStyles} youtubeEmbed={post.youtubeEmbed}>
          <PreviewButton>Voir l&apos;aperçu</PreviewButton>
        </Preview>
      }
    </CardWrapper>
  );
}

const previewStyles = {
  position: "absolute",
  top: "0",
  right: "0",
}

const PreviewButton = styled.div`
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 18px;
  background-color: #161616;
  color: #f4f4f4;
  padding: 12px 24px 6px 24px;
  white-space: nowrap;
  cursor: pointer;
  box-shadow: 0 4px 13px rgba(0, 0, 0, 0.57);
`;

const CardWrapper = styled.div`
  position: relative;
  margin-bottom: 48px;
  .post-card-details-space{
    margin-bottom: 20px;
  }
`;

const PostImage = styled.div`
  position: relative;
  display: block;
  height: auto;
  width: 100%;
  aspect-ratio: 16/10;
  background-color: gray;
  overflow: hidden;
  margin-bottom: 10px;
  .post-image-container{
    width: 100%;
    height: 100%;
    position: relative;
  }
`;

