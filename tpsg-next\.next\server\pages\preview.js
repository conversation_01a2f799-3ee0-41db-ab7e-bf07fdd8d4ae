"use strict";(()=>{var e={};e.id=9175,e.ids=[9175,2888],e.modules={488:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{config:()=>v,default:()=>d,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>x,routeModule:()=>w,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>q,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>_,unstable_getStaticProps:()=>S});var s=r(7093),a=r(5244),i=r(1323),p=r(779),u=r(4033),n=r(7825),l=e([n]);n=(l.then?(await l)():l)[0];let d=(0,i.l)(n,"default"),c=(0,i.l)(n,"getStaticProps"),m=(0,i.l)(n,"getStaticPaths"),g=(0,i.l)(n,"getServerSideProps"),v=(0,i.l)(n,"config"),x=(0,i.l)(n,"reportWebVitals"),S=(0,i.l)(n,"unstable_getStaticProps"),_=(0,i.l)(n,"unstable_getStaticPaths"),P=(0,i.l)(n,"unstable_getStaticParams"),b=(0,i.l)(n,"unstable_getServerProps"),q=(0,i.l)(n,"unstable_getServerSideProps"),w=new s.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/preview",pathname:"/preview",bundlePath:"",filename:""},components:{App:u.default,Document:p.default},userland:n});o()}catch(e){o(e)}})},7825:(e,t,r)=>{r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{default:()=>Preview,getServerSideProps:()=>getServerSideProps});var s=r(997),a=r(1385),i=r(9114),p=r(6303),u=r(635),n=r(1077),l=e([p]);function Preview(e){if(e.post)switch(e.post.type){case"article":return s.jsx(p.Bv,{post:e.post,preview:!0});case"podcast":return s.jsx(p.Q4,{episode:e.post,preview:!0});case"webinaire":return s.jsx(p.KB,{episode:e.post,preview:!0,relatedPosts:[]});default:return""}}async function getServerSideProps({query:e}){let t=e.secret===process.env.CLIENT_PREVIEW_SECRET;if(t){let t=await a.Z.query({query:d,variables:{slug:e.slug}}).then(e=>e.data.posts[0]);return t?{props:{post:{...t,modules:(0,u.fw)(t.modules),route:(0,n.qt)(t)}}}:{notFound:!0}}return{notFound:!0}}p=(l.then?(await l)():l)[0];let d=i.gql`
    query PostPreview($slug: String!) {
        posts(where: { slug: $slug, _publicationState: "preview" }) {
            title
            type
            slug
            body
            id
            image {
                url
                provider
            }
            topics {
                name
                slug
            }
            author {
                fullName
                about
                picture {
                    url
                    provider
                }
            }
            modules {
                ... on ComponentModulePodcast {
                    __typename
                    podcast {
                        slug
                        name
                        logoSmall {
                            url
                            provider
                        }
                        platforms {
                            name
                            url
                        }
                    }
                    embedAudio
                    embedVideo
                }
                ... on ComponentModuleWebinar{
                    __typename
                    webinar {
                        slug,
                    }
                    speakers {
                        fullName
                        picture {
                            url
                            provider
                        }
                    }
                    embedVideo
                }
                ... on ComponentModuleEvent{
                    __typename
                    date
                    url
                }
                ... on ComponentModuleLead {
                    __typename
                    content
                }
                ... on ComponentModuleSeo {
                    metaDescription
                    metaTitle
                }
            }
        }
    }
`;o()}catch(e){o(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},6158:e=>{e.exports=require("react-share")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,9755,5152,4033,779,7113,6453,1077,7620,8672,2832,8657,4004,7482,3462,3646,6303],()=>__webpack_exec__(488));module.exports=r})();