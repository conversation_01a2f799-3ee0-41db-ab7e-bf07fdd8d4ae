import { menuAsObj } from "utils/components.utils";
import Link from "next/link";
import styled from "styled-components";
import { useState } from "react";
import { device } from "styles/device";
import CondImage from "components/shared/condimage";

function RenderGroup({ group, blogPath }) {
  return (
    <>
      <GroupName>{group.name}</GroupName>
      <ul>
        {group.items.map((link, key) => <RenderLink key={key} link={link} blogPath={blogPath}/>)}
      </ul>
    </>
  )
}

function RenderLink({ link, blogPath }) {

  switch (link.type) {
  case "external" :
    return <li><a href={link.value} target="_blank" rel={"noreferrer noopener"}>{link.label}</a></li>
  case "internal" :
    return <li><Link href={link.value}>{link.label}</Link></li>
  case "filter" :
    return (
      <li>
        <Link href={{
          pathname: `/blog/${blogPath}/filtres`,
          query: link.value
        }}>
          {link.label}
        </Link>
      </li>
    )
  default:
    return <></>
  }
}

export default function BlogMenu({ data }) {

  const menu = menuAsObj(data.menu);
  const blogPath = data.slug;
  const [isOpen, toggleBlogMenu] = useState(false);

  return (
    <>
      <Wrapper isOpen={isOpen} className={"blog-menu"}>
        <Content isOpen={isOpen}>
          <Blogger>
            <div className={"menu-blogger-picture"}>
              <CondImage imageData={data.blogger.picture}/>
            </div>
            <div>
              <Link href={`/blog/${blogPath}`}>
                <p className={"menu-blogger-name primary-hover"}>{data.blogger.fullName}</p>
              </Link>
              <p className={"menu-blogger-label"}>Blog</p>
            </div>
          </Blogger>
          <RenderList>
            {menu?.groups &&
              menu.groups.map((group, key) =>
                <RenderGroup key={key} group={group} blogPath={blogPath}/>
              )
            }
            {menu?.singles.length > 0 &&
              menu.singles.map((link, key) => (
                <RenderLink key={key} link={link} blogPath={blogPath}/>
              ))
            }
          </RenderList>
          
        </Content>
      </Wrapper>
      <ToggleButton
        isOpen={isOpen}
        onClick={() => toggleBlogMenu(!isOpen)}>
        <p>→</p>
      </ToggleButton>
    </>
  )
}

const Wrapper = styled.div`
  position: fixed;
  bottom: -70px;
  left: -70px;
  height: calc(70vh + 140px);
  width: calc(100vw + 140px);
  padding: 48px 70px;
  background-color: rgba(${p => !p.isOpen ? "236, 236, 236, 0.5" : "236, 236, 236, 0.8"});
  backdrop-filter: blur(${props => !props.isOpen ? "15px" : "25px"});
  transform: translate3d(${props => !props.isOpen ? "-100vw,70vh,0" : "0,0,0"});
  border-top-right-radius: 70px;
  transition: all 450ms cubic-bezier(0.58, 0, 0.29, 0.91);
  z-index: 1800;

  @media ${device.desktop} {
    grid-row: 1/6;
    background-color: transparent;
    position: sticky;
    display: block;
    backdrop-filter: inherit;
    border-top-right-radius: 0;
    transform: none;
    top: 46px;
    left: 0;
    height: 100vh;
    width: 240px;
    padding: 40px 0 0 0;
    z-index: 50;
  }
`

const Blogger = styled.div`
  display: flex;
  flex-direction: row;
  margin-bottom: 42px;
  .menu-blogger-picture {
    position: relative;
    width: 55px;
    height: 55px;
    border-radius: 50px;
    overflow: hidden;
  }
  .menu-blogger-name {
    margin: 4px 0 0 16px;
    font-family: "Stelvio", sans-serif;
    font-size: 24px;
    line-height: 105%;
    color: #161616;
    font-weight: 600;
  }
  .menu-blogger-label {
    margin: 4px 0 0 16px;
    font-family: "Novela", serif;
    font-style: italic;
    font-size: 20px;
    color: #888888;
    font-weight: 400;
  }
  
  @media ${device.desktop} {
    flex-direction: column;
    width: 50%;
    .menu-blogger-name {
      margin: 16px 0 0 0;
      font-family: "Stelvio", sans-serif;
      font-size: 32px;
      line-height: 105%;
      color: #161616;
      font-weight: 600;
    }
    .menu-blogger-label {
      margin: 4px 0 0 0;
    }
  }
`;

const Content = styled.div`
  
  padding-left: var(--mobile-gap);
  padding-right: var(--mobile-gap);
  
  @media ${device.tablet} {
    padding-left: var(--tablet-gap);
    padding-right: var(tablet-gap);
  }
  @media ${device.desktop} {
    transform: none;
    padding-left: 0;
    padding-right: 0;
    top: 0;
    left: 0;
  }
`;
const RenderList = styled.div`
  ul {
    display: table;
    padding: 0;
  }
  li {
    font-weight: 400;
    position: relative;
    font-family: "Stelvio", sans-serif;
    font-size: 20px;
    margin-top: 8px;
    list-style: none;
  }
  //transform: translate3d(${props => !props.isOpen ? "0, -100px, 0" : "0,0,0"});
  transition: all 800ms cubic-bezier(0.58, 0, 0.29, 0.91);

  @media ${device.desktop} {
    a {
      position: relative;
      height: 32px;
      display: table-row;
      line-height: 24px;
      border-radius: 32px;
      z-index: 900;
      color: black;

      &:hover {
        color: white;

        &:after {
          content: "";
          background-color: black;
          position: absolute;
          height: 100%;
          width: calc(100% + 24px);
          left: -12px;
          top: -7px;
          border-radius: 32px;
          z-index: -1;
        }
      }
    }
  }
`;

const ToggleButton = styled.div`
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  align-items: center;
  justify-content: center;
  height: 70px;
  width: 70px;
  color: black;
  border-radius: 70px;
  p {
    padding-top: 12px;
    margin: 16px 16px 0 0;
    font-size: 32px;
    transform-origin: center;
    transform: rotate(${props => props.isOpen ? "-225deg" : "-45deg"});
    transition: all 450ms cubic-bezier(0.58, -0.42, 0.29, 0.91);
  }
  z-index: 2000;
  @media ${device.desktop} {
    display: none;
  }
`;

const GroupName = styled.label`
  font-weight: 500;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  color: #888888;
`
