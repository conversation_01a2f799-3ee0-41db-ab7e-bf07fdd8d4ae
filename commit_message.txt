Fix: Correction des erreurs de formatage, amélioration des styles CSS et résolution des problèmes de recherche

Ce commit apporte plusieurs types de corrections:

1. Correction des erreurs de formatage:
   - Remplacement des guillemets simples par des guillemets doubles conformément aux règles ESLint
   - Ajout d'espaces appropriés dans les expressions JSX
   - Correction des problèmes d'indentation dans plusieurs fichiers
   - Échappement correct des caractères spéciaux (apostrophes)

2. Amélioration des styles CSS:
   - Ajout de règles CSS spécifiques dans globals.css pour garantir que tous les liens dans le contenu markdown respectent la charte graphique TPSG
   - Augmentation de la spécificité des sélecteurs CSS pour éviter les conflits de styles
   - Correction des styles des boutons d'inscription pour qu'ils ne soient plus orange par défaut

3. Correction des problèmes React:
   - Ajout des dépendances manquantes dans les hooks useEffect
   - Correction de l'utilisation de useRouter dans _app.js
   - Ajout des polices personnalisées dans _document.js
   - Résolution du problème de disparition des résultats de recherche

4. Amélioration de l'accessibilité:
   - Ajout d'attributs alt aux images
   - Remplacement des attributs obsolètes dans les SVG (xlinkHref par href)

Fichiers modifiés:
- components/layout/Header/NavigationBar.js
- components/layout/PagesLayout/ArticleLayout.js
- components/podcast/logos/Podcast1PVR.js
- components/podcast/logos/Podcast1PVRSwitch.js
- components/recherche/big-input.js
- components/recherche/search-tool.js
- components/shared/Buttons/CircleCta.jsx
- components/shared/RenderMardown.js
- components/shared/SubHeader/Items/LinkButton.js
- components/shared/post/author-box.js
- components/shared/post/md-body.js
- components/webinars/WebEvent.js
- components/webinars/WepisodeCard.js
- components/webinars/Ticket/Ticket.js
- pages/_app.js
- pages/_document.js
- pages/blog/[blog]/index.js
- pages/recherche.js
- styles/globals.css

Cette correction complète les modifications précédentes concernant le style des liens hypertextes et assure que le code peut être compilé correctement sur la plateforme d'intégration. De plus, elle résout le problème de disparition des résultats de recherche et corrige l'affichage des boutons d'inscription.
