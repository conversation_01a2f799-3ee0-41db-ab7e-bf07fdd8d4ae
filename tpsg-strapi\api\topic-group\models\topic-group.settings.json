{"kind": "collectionType", "collectionName": "topic_groups", "info": {"name": "topicGroup", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "description": {"type": "text"}, "cover": {"model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}}, "topics": {"collection": "topic"}, "type": {"type": "enumeration", "enum": ["ministere", "vocation"], "required": true}, "featured": {"type": "component", "repeatable": true, "component": "module.featured"}, "children": {"collection": "topic-group", "via": "parent"}, "parent": {"model": "topic-group", "via": "children"}}}