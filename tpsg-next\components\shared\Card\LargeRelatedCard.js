import styled from "styled-components";
import { getPostSpeakers, getPostLead, getPostRoute } from "utils/posts.utils";
import { device } from "styles/device";
import { Speakers } from "./Elements"
import Image from "next/image";
import { withRealSrc } from "utils/image-utils";
import Link from "next/link";
import CondLink from "../CondLink";

export default function LargeRelatedCard({ post }) {

  const lead = getPostLead(post);
  const speakers = getPostSpeakers(post);
  const route = getPostRoute(post);

  return (
    <CondLink link={route}>
      <Wrapper theme={"dark"}>
        <div className={"lrc-content"}>
          <div className={"lrc-text"}>
            <p className={"lrc-type"}>{post.type}</p>
            <p className={"lrc-title"}>{post.title}</p>
            <p className={"lrc-lead"}>{lead}</p>
          </div>
          <Speakers speakers={speakers}/>
        </div>

        <div className={"lrc-image"}>
          <Image
            src={withRealSrc(post.image)}
            sizes={"30vw"}
            fill
            style={imageStyle}
            alt={""}/>
        </div>
      </Wrapper>
    </CondLink>
  );
}

const imageStyle = {
  objectFit: "cover"
}

const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column-reverse;
  width: 100%;

  .lrc-image {
    position: relative;
    min-width: 50%;
    aspect-ratio: 16/9;
    background-color: #F9F1E6;
    background-image: url(/images/tpsg-logo.svg);
    background-repeat: no-repeat;
    background-position: center;
  }
  
  border: 1px solid ${(p) => (p.theme === "dark" ?
    "rgba(250,247,243,0.2)" : "rgba(22,22,22,0.2)")};
  
  .lrc-content {
    display: flex;
    height: 360px;
    flex-direction: column;
    justify-content: space-between;
    padding: clamp(1.5rem, 0.112rem + 2.98vw, 2.5rem);
  }
  .lrc-text {
    color: var(--c-soft-cream);
  }
  .lrc-type {
    margin-top: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
  }
  .lrc-title {
    position: relative;
    color: ${p => p.theme === "dark" ? "#FFFFFF" : "#161616"};
    font-family: Stelvio, sans-serif;
    font-weight: 400;
    font-size: clamp(1.75rem, 1.4029850746268657rem + 0.7462686567164178vw, 2rem);
    line-height: clamp(2rem, 1.4794776119402986rem + 1.1194029850746268vw, 2.375rem);
    margin-bottom: -4px;
    margin-top: 6px;
  }
  .lrc-lead {
    margin-top: 12px;
    font-family: Switzer, sans-serif;
    font-size: clamp(0.875rem, 0.53rem + 0.75vw, 1.125rem);
    line-height: clamp(1.375rem, 0.8544776119402986rem + 1.1194029850746268vw, 1.75rem);
    color: #989AA4;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    font-weight: 400;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  
  @media ${device.desktop} {
    flex-direction: row;
    justify-content: space-between;
    .lrc-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 40px;
      min-height: 100%;
      width: calc(50% - 24px);
    }
    .lrc-text {
      font-family: Switzer, sans-serif;
    }
    .lrc-title {
      font-family: Stelvio, sans-serif;
      font-size: 32px;
      line-height: 38px;
      font-weight: 500;
      margin-top: 24px;
      margin-bottom: 0;
    }
    .lrc-lead {
      font-size: 16px;
      color: #9B9B9B;
      margin-top: 14px;
      margin-bottom: 40px;
    }
    .lrc-image {
      position: relative;
      min-width: 50%;
      aspect-ratio: 16/9;
    }
    &:hover {
      border-color: var(--brand-color);
      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);
      box-shadow: 0 0 0 1px var(--c-brand-lighter);
      cursor: pointer;
    }
  }
`;

