import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import { fragments, queries } from "api/gql-queries";
import { modulesAsObj } from "/utils/components.utils";
import Related from "components/shared/Related";
import { getPostRoute } from "utils/posts.utils";
import { ArticleLayout } from "components/layout/PagesLayout";

const BUILD_LIMIT = process.env.LIGHT_BUILD === "true" ? 10 : 9999;

export default function Article({ post, relatedPosts, newsletter }) {

  if (!post) return null;

  const baseTopicNames = post.topics.map(topic => topic.name);

  return (
    <>
      <ArticleLayout post={post} newsletter={newsletter} preview={false} />
      <Related items={relatedPosts} baseTopicNames={baseTopicNames} />
    </>
  );
}

export async function getStaticProps({ params }) {

  const post = await client
    .query({
      query: QUERY_ARTICLE,
      variables: { slug: params.article },
    })
    .then((response) => {
      return response.data.posts[0];
    });

  if (!post) {
    return {
      notFound: true
    }
  }

  const relatedPosts = await client
    .query({
      query: queries.QUERY_RELATED,
      variables: { id: post.id },
    })
    .then((response) => {
      return response.data.relatedPosts;
    });

  let newsletter = null;

  if (post.author) {
    const blog = await client.query({
      query: QUERY_ARTICLE_BLOG,
      variables: { slug: post.author.slug }
    }).then(response => {
      return response.data.blogs
    })
    if (blog.length) {
      newsletter = blog[0].newsletter
    }
  }

  return {
    props: {
      post: {
        ...post,
        modules: modulesAsObj(post.modules),
        route: getPostRoute(post),
      },
      relatedPosts: relatedPosts || null,
      newsletter
    },
    revalidate: 10,
  };
}

export async function getStaticPaths() {

  const { data, error } = await client.query({
    query: QUERY_ARTICLE_SLUGS,
    variables: { limit: BUILD_LIMIT }
  });

  if (error) {
    console.log(error);
  }

  return {
    paths: data.posts.map((post) => ({
      params: { article: post.slug },
    })),
    fallback: true,
  };
}

const QUERY_ARTICLE_SLUGS = gql`
    query ArticleSlugs($limit: Int!) {
        posts(where: { type: "article" }, limit: $limit) {
            slug
        }
    }
`;

const QUERY_ARTICLE = gql`
    ${fragments.CORE_POST_FIELDS}
    query Article($slug: String!) {
        posts(where: { slug: $slug }) {
            ...CorePostFields
        }
    }
`;

const QUERY_ARTICLE_BLOG = gql`
    query Blog($slug: String!){
        blogs(where: {slug: $slug}){
            id
            slug
            newsletter
        }
    }
`
