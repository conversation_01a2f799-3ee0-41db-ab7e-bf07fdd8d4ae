'use strict';

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-services)
 * to customize this service
 */

module.exports = {
    updatePostCount: async (topic) => {
        const posts = await strapi.query("post").find({
            "topics.id_in": [topic.id],
            _publicationState: "live",
            "_limit": -1
        })

        // const publishedPosts = await posts
        // .filter(e => e.published_at != null)

        await strapi.query('topic').update({id: topic.id}, {postCount: posts.length})
    }
};
