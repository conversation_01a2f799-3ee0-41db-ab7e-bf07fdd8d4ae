(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[229],{4836:function(){var t;t="undefined"!=typeof self?self:this,function(e){var r={searchParams:"URLSearchParams"in t,iterable:"Symbol"in t&&"iterator"in Symbol,blob:"FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(t){return!1}}(),formData:"FormData"in t,arrayBuffer:"ArrayBuffer"in t};if(r.arrayBuffer)var n=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],i=ArrayBuffer.isView||function(t){return t&&n.indexOf(Object.prototype.toString.call(t))>-1};function normalizeName(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.^_`|~]/i.test(t))throw TypeError("Invalid character in header field name");return t.toLowerCase()}function normalizeValue(t){return"string"!=typeof t&&(t=String(t)),t}function iteratorFor(t){var e={next:function(){var e=t.shift();return{done:void 0===e,value:e}}};return r.iterable&&(e[Symbol.iterator]=function(){return e}),e}function Headers(t){this.map={},t instanceof Headers?t.forEach(function(t,e){this.append(e,t)},this):Array.isArray(t)?t.forEach(function(t){this.append(t[0],t[1])},this):t&&Object.getOwnPropertyNames(t).forEach(function(e){this.append(e,t[e])},this)}function consumed(t){if(t.bodyUsed)return Promise.reject(TypeError("Already read"));t.bodyUsed=!0}function fileReaderReady(t){return new Promise(function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}})}function readBlobAsArrayBuffer(t){var e=new FileReader,r=fileReaderReady(e);return e.readAsArrayBuffer(t),r}function bufferClone(t){if(t.slice)return t.slice(0);var e=new Uint8Array(t.byteLength);return e.set(new Uint8Array(t)),e.buffer}function Body(){return this.bodyUsed=!1,this._initBody=function(t){if(this._bodyInit=t,t){if("string"==typeof t)this._bodyText=t;else if(r.blob&&Blob.prototype.isPrototypeOf(t))this._bodyBlob=t;else if(r.formData&&FormData.prototype.isPrototypeOf(t))this._bodyFormData=t;else if(r.searchParams&&URLSearchParams.prototype.isPrototypeOf(t))this._bodyText=t.toString();else{var e;r.arrayBuffer&&r.blob&&(e=t)&&DataView.prototype.isPrototypeOf(e)?(this._bodyArrayBuffer=bufferClone(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):r.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(t)||i(t))?this._bodyArrayBuffer=bufferClone(t):this._bodyText=t=Object.prototype.toString.call(t)}}else this._bodyText="";!this.headers.get("content-type")&&("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):r.searchParams&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},r.blob&&(this.blob=function(){var t=consumed(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(!this._bodyFormData)return Promise.resolve(new Blob([this._bodyText]));throw Error("could not read FormData body as blob")},this.arrayBuffer=function(){return this._bodyArrayBuffer?consumed(this)||Promise.resolve(this._bodyArrayBuffer):this.blob().then(readBlobAsArrayBuffer)}),this.text=function(){var t,e,r,n=consumed(this);if(n)return n;if(this._bodyBlob)return t=this._bodyBlob,r=fileReaderReady(e=new FileReader),e.readAsText(t),r;if(this._bodyArrayBuffer)return Promise.resolve(function(t){for(var e=new Uint8Array(t),r=Array(e.length),n=0;n<e.length;n++)r[n]=String.fromCharCode(e[n]);return r.join("")}(this._bodyArrayBuffer));if(!this._bodyFormData)return Promise.resolve(this._bodyText);throw Error("could not read FormData body as text")},r.formData&&(this.formData=function(){return this.text().then(decode)}),this.json=function(){return this.text().then(JSON.parse)},this}Headers.prototype.append=function(t,e){t=normalizeName(t),e=normalizeValue(e);var r=this.map[t];this.map[t]=r?r+", "+e:e},Headers.prototype.delete=function(t){delete this.map[normalizeName(t)]},Headers.prototype.get=function(t){return t=normalizeName(t),this.has(t)?this.map[t]:null},Headers.prototype.has=function(t){return this.map.hasOwnProperty(normalizeName(t))},Headers.prototype.set=function(t,e){this.map[normalizeName(t)]=normalizeValue(e)},Headers.prototype.forEach=function(t,e){for(var r in this.map)this.map.hasOwnProperty(r)&&t.call(e,this.map[r],r,this)},Headers.prototype.keys=function(){var t=[];return this.forEach(function(e,r){t.push(r)}),iteratorFor(t)},Headers.prototype.values=function(){var t=[];return this.forEach(function(e){t.push(e)}),iteratorFor(t)},Headers.prototype.entries=function(){var t=[];return this.forEach(function(e,r){t.push([r,e])}),iteratorFor(t)},r.iterable&&(Headers.prototype[Symbol.iterator]=Headers.prototype.entries);var s=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function Request(t,e){var r,n,i=(e=e||{}).body;if(t instanceof Request){if(t.bodyUsed)throw TypeError("Already read");this.url=t.url,this.credentials=t.credentials,e.headers||(this.headers=new Headers(t.headers)),this.method=t.method,this.mode=t.mode,this.signal=t.signal,i||null==t._bodyInit||(i=t._bodyInit,t.bodyUsed=!0)}else this.url=String(t);if(this.credentials=e.credentials||this.credentials||"same-origin",(e.headers||!this.headers)&&(this.headers=new Headers(e.headers)),this.method=(n=(r=e.method||this.method||"GET").toUpperCase(),s.indexOf(n)>-1?n:r),this.mode=e.mode||this.mode||null,this.signal=e.signal||this.signal,this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw TypeError("Body not allowed for GET or HEAD requests");this._initBody(i)}function decode(t){var e=new FormData;return t.trim().split("&").forEach(function(t){if(t){var r=t.split("="),n=r.shift().replace(/\+/g," "),i=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(n),decodeURIComponent(i))}}),e}function Response(t,e){e||(e={}),this.type="default",this.status=void 0===e.status?200:e.status,this.ok=this.status>=200&&this.status<300,this.statusText="statusText"in e?e.statusText:"OK",this.headers=new Headers(e.headers),this.url=e.url||"",this._initBody(t)}Request.prototype.clone=function(){return new Request(this,{body:this._bodyInit})},Body.call(Request.prototype),Body.call(Response.prototype),Response.prototype.clone=function(){return new Response(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new Headers(this.headers),url:this.url})},Response.error=function(){var t=new Response(null,{status:0,statusText:""});return t.type="error",t};var o=[301,302,303,307,308];Response.redirect=function(t,e){if(-1===o.indexOf(e))throw RangeError("Invalid status code");return new Response(null,{status:e,headers:{location:t}})},e.DOMException=t.DOMException;try{new e.DOMException}catch(t){e.DOMException=function(t,e){this.message=t,this.name=e;var r=Error(t);this.stack=r.stack},e.DOMException.prototype=Object.create(Error.prototype),e.DOMException.prototype.constructor=e.DOMException}function fetch(t,n){return new Promise(function(i,s){var o=new Request(t,n);if(o.signal&&o.signal.aborted)return s(new e.DOMException("Aborted","AbortError"));var a=new XMLHttpRequest;function abortXhr(){a.abort()}a.onload=function(){var t,e,r={status:a.status,statusText:a.statusText,headers:(t=a.getAllResponseHeaders()||"",e=new Headers,t.replace(/\r?\n[\t ]+/g," ").split(/\r?\n/).forEach(function(t){var r=t.split(":"),n=r.shift().trim();if(n){var i=r.join(":").trim();e.append(n,i)}}),e)};r.url="responseURL"in a?a.responseURL:r.headers.get("X-Request-URL");var n="response"in a?a.response:a.responseText;i(new Response(n,r))},a.onerror=function(){s(TypeError("Network request failed"))},a.ontimeout=function(){s(TypeError("Network request failed"))},a.onabort=function(){s(new e.DOMException("Aborted","AbortError"))},a.open(o.method,o.url,!0),"include"===o.credentials?a.withCredentials=!0:"omit"===o.credentials&&(a.withCredentials=!1),"responseType"in a&&r.blob&&(a.responseType="blob"),o.headers.forEach(function(t,e){a.setRequestHeader(e,t)}),o.signal&&(o.signal.addEventListener("abort",abortXhr),a.onreadystatechange=function(){4===a.readyState&&o.signal.removeEventListener("abort",abortXhr)}),a.send(void 0===o._bodyInit?null:o._bodyInit)})}fetch.polyfill=!0,t.fetch||(t.fetch=fetch,t.Headers=Headers,t.Request=Request,t.Response=Response),e.Headers=Headers,e.Request=Request,e.Response=Response,e.fetch=fetch,Object.defineProperty(e,"__esModule",{value:!0})}({})},5229:function(t,e,r){!function(t){"use strict";var extendStatics=function(t,e){return(extendStatics=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function __extends(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function __(){this.constructor=t}extendStatics(t,e),t.prototype=null===e?Object.create(e):(__.prototype=e.prototype,new __)}var __assign=function(){return(__assign=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function __awaiter(t,e,r,n){return new(r||(r=Promise))(function(i,s){function fulfilled(t){try{step(n.next(t))}catch(t){s(t)}}function rejected(t){try{step(n.throw(t))}catch(t){s(t)}}function step(t){var e;t.done?i(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(fulfilled,rejected)}step((n=n.apply(t,e||[])).next())})}function __generator(t,e){var r,n,i,s,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return s={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function verb(s){return function(a){return function(s){if(r)throw TypeError("Generator is already executing.");for(;o;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!(i=(i=o.trys).length>0&&i[i.length-1])&&(6===s[0]||2===s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=e.call(t,o)}catch(t){s=[6,t],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}}var e=function(t){function MeiliSearchCommunicationError(e,r,n,i){var s,o,a,u=this;return Object.setPrototypeOf(u=t.call(this,e)||this,MeiliSearchCommunicationError.prototype),u.name="MeiliSearchCommunicationError",r instanceof Response&&(u.message=r.statusText,u.statusCode=r.status),r instanceof Error&&(u.errno=r.errno,u.code=r.code),i?(u.stack=i,u.stack=null===(s=u.stack)||void 0===s?void 0:s.replace(/(TypeError|FetchError)/,u.name),u.stack=null===(o=u.stack)||void 0===o?void 0:o.replace("Failed to fetch","request to ".concat(n," failed, reason: connect ECONNREFUSED")),u.stack=null===(a=u.stack)||void 0===a?void 0:a.replace("Not Found","Not Found: ".concat(n))):Error.captureStackTrace&&Error.captureStackTrace(u,MeiliSearchCommunicationError),u}return __extends(MeiliSearchCommunicationError,t),MeiliSearchCommunicationError}(Error),r=function(t){function class_1(e,n){var i=t.call(this,e.message)||this;return Object.setPrototypeOf(i,r.prototype),i.name="MeiliSearchApiError",i.code=e.code,i.type=e.type,i.link=e.link,i.message=e.message,i.httpStatus=n,Error.captureStackTrace&&Error.captureStackTrace(i,r),i}return __extends(class_1,t),class_1}(Error);function httpResponseErrorHandler(t){return __awaiter(this,void 0,void 0,function(){var n;return __generator(this,function(i){switch(i.label){case 0:if(t.ok)return[3,5];n=void 0,i.label=1;case 1:return i.trys.push([1,3,,4]),[4,t.json()];case 2:return n=i.sent(),[3,4];case 3:throw i.sent(),new e(t.statusText,t,t.url);case 4:throw new r(n,t.status);case 5:return[2,t]}})})}function httpErrorHandler(t,r,n){if("MeiliSearchApiError"!==t.name)throw new e(t.message,t,n,r);throw t}var n=function(t){function MeiliSearchError(e){var r=t.call(this,e)||this;return Object.setPrototypeOf(r,MeiliSearchError.prototype),r.name="MeiliSearchError",Error.captureStackTrace&&Error.captureStackTrace(r,MeiliSearchError),r}return __extends(MeiliSearchError,t),MeiliSearchError}(Error),i=function(t){function MeiliSearchTimeOutError(e){var r=t.call(this,e)||this;return Object.setPrototypeOf(r,MeiliSearchTimeOutError.prototype),r.name="MeiliSearchTimeOutError",Error.captureStackTrace&&Error.captureStackTrace(r,MeiliSearchTimeOutError),r}return __extends(MeiliSearchTimeOutError,t),MeiliSearchTimeOutError}(Error);function removeUndefinedFromObject(t){return Object.entries(t).reduce(function(t,e){var r=e[0],n=e[1];return void 0!==n&&(t[r]=n),t},{})}function toQueryParams(t){return Object.keys(t).reduce(function(e,r){var n,i,s,o=t[r];return void 0===o?e:Array.isArray(o)?__assign(__assign({},e),((n={})[r]=o.join(","),n)):o instanceof Date?__assign(__assign({},e),((i={})[r]=o.toISOString(),i)):__assign(__assign({},e),((s={})[r]=o,s))},{})}var s=function(){function HttpRequests(t){this.headers=function(t){var e="X-Meilisearch-Client",r="Meilisearch JavaScript (v".concat("0.30.0",")");t.headers=t.headers||{};var i=Object.assign({},t.headers);if(t.apiKey&&(i.Authorization="Bearer ".concat(t.apiKey)),t.headers["Content-Type"]||(i["Content-Type"]="application/json"),t.clientAgents&&Array.isArray(t.clientAgents)){var s=t.clientAgents.concat(r);i[e]=s.join(" ; ")}else if(t.clientAgents&&!Array.isArray(t.clientAgents))throw new n('Meilisearch: The header "'.concat(e,'" should be an array of string(s).\n'));else i[e]=r;return i}(t);try{var e=function(t){try{var e,r;return(r=t=(e=t).startsWith("https://")||e.startsWith("http://")?e:"http://".concat(e)).endsWith("/")||(r+="/"),t=r}catch(t){throw new n("The provided host is not valid.")}}(t.host);this.url=new URL(e)}catch(t){throw new n("The provided host is not valid.")}}return HttpRequests.prototype.request=function(t){var e=t.method,r=t.url,n=t.params,i=t.body,s=t.config;return __awaiter(this,void 0,void 0,function(){var t,o,a,u;return __generator(this,function(c){switch(c.label){case 0:t=new URL(r,this.url),n&&(o=new URLSearchParams,Object.keys(n).filter(function(t){return null!==n[t]}).map(function(t){return o.set(t,n[t])}),t.search=o.toString()),c.label=1;case 1:return c.trys.push([1,4,,5]),[4,fetch(t.toString(),__assign(__assign({},s),{method:e,body:JSON.stringify(i),headers:this.headers})).then(function(t){return httpResponseErrorHandler(t)})];case 2:return[4,c.sent().json().catch(function(){})];case 3:return[2,c.sent()];case 4:return u=(a=c.sent()).stack,httpErrorHandler(a,u,t.toString()),[3,5];case 5:return[2]}})})},HttpRequests.prototype.get=function(t,e,r){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(n){switch(n.label){case 0:return[4,this.request({method:"GET",url:t,params:e,config:r})];case 1:return[2,n.sent()]}})})},HttpRequests.prototype.post=function(t,e,r,n){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(i){switch(i.label){case 0:return[4,this.request({method:"POST",url:t,body:e,params:r,config:n})];case 1:return[2,i.sent()]}})})},HttpRequests.prototype.put=function(t,e,r,n){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(i){switch(i.label){case 0:return[4,this.request({method:"PUT",url:t,body:e,params:r,config:n})];case 1:return[2,i.sent()]}})})},HttpRequests.prototype.patch=function(t,e,r,n){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(i){switch(i.label){case 0:return[4,this.request({method:"PATCH",url:t,body:e,params:r,config:n})];case 1:return[2,i.sent()]}})})},HttpRequests.prototype.delete=function(t,e,r,n){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(i){switch(i.label){case 0:return[4,this.request({method:"DELETE",url:t,body:e,params:r,config:n})];case 1:return[2,i.sent()]}})})},HttpRequests}(),EnqueuedTask=function(t){this.taskUid=t.taskUid,this.indexUid=t.indexUid,this.status=t.status,this.type=t.type,this.enqueuedAt=new Date(t.enqueuedAt)},Task=function(t){this.indexUid=t.indexUid,this.status=t.status,this.type=t.type,this.uid=t.uid,this.details=t.details,this.canceledBy=t.canceledBy,this.error=t.error,this.duration=t.duration,this.startedAt=new Date(t.startedAt),this.enqueuedAt=new Date(t.enqueuedAt),this.finishedAt=new Date(t.finishedAt)},o=function(){function TaskClient(t){this.httpRequest=new s(t)}return TaskClient.prototype.getTask=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="tasks/".concat(t),[4,this.httpRequest.get(e)];case 1:return r=n.sent(),[2,new Task(r)]}})})},TaskClient.prototype.getTasks=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(r){switch(r.label){case 0:return[4,this.httpRequest.get("tasks",toQueryParams(t))];case 1:return e=r.sent(),[2,__assign(__assign({},e),{results:e.results.map(function(t){return new Task(t)})})]}})})},TaskClient.prototype.waitForTask=function(t,e){var r=void 0===e?{}:e,n=r.timeOutMs,s=void 0===n?5e3:n,o=r.intervalMs,a=void 0===o?50:o;return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:e=Date.now(),n.label=1;case 1:if(!(Date.now()-e<s))return[3,4];return[4,this.getTask(t)];case 2:if(!["enqueued","processing"].includes((r=n.sent()).status))return[2,r];return[4,function(t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,new Promise(function(e){return setTimeout(e,t)})];case 1:return[2,e.sent()]}})})}(a)];case 3:return n.sent(),[3,1];case 4:throw new i("timeout of ".concat(s,"ms has exceeded on process ").concat(t," when waiting a task to be resolved."))}})})},TaskClient.prototype.waitForTasks=function(t,e){var r=void 0===e?{}:e,n=r.timeOutMs,i=void 0===n?5e3:n,s=r.intervalMs,o=void 0===s?50:s;return __awaiter(this,void 0,void 0,function(){var e,r,n,s,a;return __generator(this,function(u){switch(u.label){case 0:e=[],r=0,n=t,u.label=1;case 1:if(!(r<n.length))return[3,4];return s=n[r],[4,this.waitForTask(s,{timeOutMs:i,intervalMs:o})];case 2:a=u.sent(),e.push(a),u.label=3;case 3:return r++,[3,1];case 4:return[2,e]}})})},TaskClient.prototype.cancelTasks=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(r){switch(r.label){case 0:return[4,this.httpRequest.post("tasks/cancel",{},toQueryParams(t))];case 1:return e=r.sent(),[2,new EnqueuedTask(e)]}})})},TaskClient.prototype.deleteTasks=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(r){switch(r.label){case 0:return[4,this.httpRequest.delete("tasks",{},toQueryParams(t))];case 1:return e=r.sent(),[2,new EnqueuedTask(e)]}})})},TaskClient}(),a=function(){function Index(t,e,r){this.uid=e,this.primaryKey=r,this.httpRequest=new s(t),this.tasks=new o(t)}return Index.prototype.search=function(t,e,r){return __awaiter(this,void 0,void 0,function(){var n;return __generator(this,function(i){switch(i.label){case 0:return n="indexes/".concat(this.uid,"/search"),[4,this.httpRequest.post(n,removeUndefinedFromObject(__assign({q:t},e)),void 0,r)];case 1:return[2,i.sent()]}})})},Index.prototype.searchGet=function(t,e,r){var i,s,o,a,u;return __awaiter(this,void 0,void 0,function(){var c,h,d;return __generator(this,function(l){switch(l.label){case 0:return c="indexes/".concat(this.uid,"/search"),h=function(t){if("string"==typeof t)return t;if(Array.isArray(t))throw new n("The filter query parameter should be in string format when using searchGet")},d=__assign(__assign({q:t},e),{filter:h(null==e?void 0:e.filter),sort:null===(i=null==e?void 0:e.sort)||void 0===i?void 0:i.join(","),facets:null===(s=null==e?void 0:e.facets)||void 0===s?void 0:s.join(","),attributesToRetrieve:null===(o=null==e?void 0:e.attributesToRetrieve)||void 0===o?void 0:o.join(","),attributesToCrop:null===(a=null==e?void 0:e.attributesToCrop)||void 0===a?void 0:a.join(","),attributesToHighlight:null===(u=null==e?void 0:e.attributesToHighlight)||void 0===u?void 0:u.join(",")}),[4,this.httpRequest.get(c,removeUndefinedFromObject(d),r)];case 1:return[2,l.sent()]}})})},Index.prototype.getRawInfo=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid),[4,this.httpRequest.get(t)];case 1:return e=r.sent(),this.primaryKey=e.primaryKey,this.updatedAt=new Date(e.updatedAt),this.createdAt=new Date(e.createdAt),[2,e]}})})},Index.prototype.fetchInfo=function(){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(t){switch(t.label){case 0:return[4,this.getRawInfo()];case 1:return t.sent(),[2,this]}})})},Index.prototype.fetchPrimaryKey=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t=this,[4,this.getRawInfo()];case 1:return t.primaryKey=e.sent().primaryKey,[2,this.primaryKey]}})})},Index.create=function(t,e,r){return void 0===e&&(e={}),__awaiter(this,void 0,void 0,function(){var n;return __generator(this,function(i){switch(i.label){case 0:return[4,new s(r).post("indexes",__assign(__assign({},e),{uid:t}))];case 1:return n=i.sent(),[2,new EnqueuedTask(n)]}})})},Index.prototype.update=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid),[4,this.httpRequest.patch(e,t)];case 1:return(r=n.sent()).enqueuedAt=new Date(r.enqueuedAt),[2,r]}})})},Index.prototype.delete=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid),[4,this.httpRequest.delete(t)];case 1:return e=r.sent(),[2,new EnqueuedTask(e)]}})})},Index.prototype.getTasks=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.getTasks(__assign(__assign({},t),{indexUids:[this.uid]}))];case 1:return[2,e.sent()]}})})},Index.prototype.getTask=function(t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.getTask(t)];case 1:return[2,e.sent()]}})})},Index.prototype.waitForTasks=function(t,e){var r=void 0===e?{}:e,n=r.timeOutMs,i=void 0===n?5e3:n,s=r.intervalMs,o=void 0===s?50:s;return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.waitForTasks(t,{timeOutMs:i,intervalMs:o})];case 1:return[2,e.sent()]}})})},Index.prototype.waitForTask=function(t,e){var r=void 0===e?{}:e,n=r.timeOutMs,i=void 0===n?5e3:n,s=r.intervalMs,o=void 0===s?50:s;return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.waitForTask(t,{timeOutMs:i,intervalMs:o})];case 1:return[2,e.sent()]}})})},Index.prototype.getStats=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/stats"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.getDocuments=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/documents"),r=function(){var e;if(Array.isArray(null==t?void 0:t.fields))return null===(e=null==t?void 0:t.fields)||void 0===e?void 0:e.join(",")}(),[4,this.httpRequest.get(e,removeUndefinedFromObject(__assign(__assign({},t),{fields:r})))];case 1:return[2,n.sent()]}})})},Index.prototype.getDocument=function(t,e){return __awaiter(this,void 0,void 0,function(){var r,n;return __generator(this,function(i){switch(i.label){case 0:return r="indexes/".concat(this.uid,"/documents/").concat(t),n=function(){var t;if(Array.isArray(null==e?void 0:e.fields))return null===(t=null==e?void 0:e.fields)||void 0===t?void 0:t.join(",")}(),[4,this.httpRequest.get(r,removeUndefinedFromObject(__assign(__assign({},e),{fields:n})))];case 1:return[2,i.sent()]}})})},Index.prototype.addDocuments=function(t,e){return __awaiter(this,void 0,void 0,function(){var r,n;return __generator(this,function(i){switch(i.label){case 0:return r="indexes/".concat(this.uid,"/documents"),[4,this.httpRequest.post(r,t,e)];case 1:return n=i.sent(),[2,new EnqueuedTask(n)]}})})},Index.prototype.addDocumentsInBatches=function(t,e,r){return void 0===e&&(e=1e3),__awaiter(this,void 0,void 0,function(){var n,i,s,o;return __generator(this,function(a){switch(a.label){case 0:n=[],i=0,a.label=1;case 1:if(!(i<t.length))return[3,4];return o=(s=n).push,[4,this.addDocuments(t.slice(i,i+e),r)];case 2:o.apply(s,[a.sent()]),a.label=3;case 3:return i+=e,[3,1];case 4:return[2,n]}})})},Index.prototype.updateDocuments=function(t,e){return __awaiter(this,void 0,void 0,function(){var r,n;return __generator(this,function(i){switch(i.label){case 0:return r="indexes/".concat(this.uid,"/documents"),[4,this.httpRequest.put(r,t,e)];case 1:return n=i.sent(),[2,new EnqueuedTask(n)]}})})},Index.prototype.updateDocumentsInBatches=function(t,e,r){return void 0===e&&(e=1e3),__awaiter(this,void 0,void 0,function(){var n,i,s,o;return __generator(this,function(a){switch(a.label){case 0:n=[],i=0,a.label=1;case 1:if(!(i<t.length))return[3,4];return o=(s=n).push,[4,this.updateDocuments(t.slice(i,i+e),r)];case 2:o.apply(s,[a.sent()]),a.label=3;case 3:return i+=e,[3,1];case 4:return[2,n]}})})},Index.prototype.deleteDocument=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/documents/").concat(t),[4,this.httpRequest.delete(e)];case 1:return(r=n.sent()).enqueuedAt=new Date(r.enqueuedAt),[2,r]}})})},Index.prototype.deleteDocuments=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/documents/delete-batch"),[4,this.httpRequest.post(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.deleteAllDocuments=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/documents"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getSettings=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateSettings=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings"),[4,this.httpRequest.patch(e,t)];case 1:return(r=n.sent()).enqueued=new Date(r.enqueuedAt),[2,r]}})})},Index.prototype.resetSettings=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getPagination=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/pagination"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updatePagination=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/pagination"),[4,this.httpRequest.patch(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetPagination=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/pagination"),[4,this.httpRequest.delete(t)];case 1:return e=r.sent(),[2,new EnqueuedTask(e)]}})})},Index.prototype.getSynonyms=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/synonyms"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateSynonyms=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/synonyms"),[4,this.httpRequest.put(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetSynonyms=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/synonyms"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getStopWords=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/stop-words"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateStopWords=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/stop-words"),[4,this.httpRequest.put(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetStopWords=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/stop-words"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getRankingRules=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/ranking-rules"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateRankingRules=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/ranking-rules"),[4,this.httpRequest.put(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetRankingRules=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/ranking-rules"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getDistinctAttribute=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/distinct-attribute"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateDistinctAttribute=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/distinct-attribute"),[4,this.httpRequest.put(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetDistinctAttribute=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/distinct-attribute"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getFilterableAttributes=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/filterable-attributes"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateFilterableAttributes=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/filterable-attributes"),[4,this.httpRequest.put(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetFilterableAttributes=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/filterable-attributes"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getSortableAttributes=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/sortable-attributes"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateSortableAttributes=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/sortable-attributes"),[4,this.httpRequest.put(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetSortableAttributes=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/sortable-attributes"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getSearchableAttributes=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/searchable-attributes"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateSearchableAttributes=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/searchable-attributes"),[4,this.httpRequest.put(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetSearchableAttributes=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/searchable-attributes"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getDisplayedAttributes=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/displayed-attributes"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateDisplayedAttributes=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/displayed-attributes"),[4,this.httpRequest.put(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetDisplayedAttributes=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/displayed-attributes"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getTypoTolerance=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/typo-tolerance"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateTypoTolerance=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/typo-tolerance"),[4,this.httpRequest.patch(e,t)];case 1:return(r=n.sent()).enqueuedAt=new Date(r.enqueuedAt),[2,r]}})})},Index.prototype.resetTypoTolerance=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/typo-tolerance"),[4,this.httpRequest.delete(t)];case 1:return(e=r.sent()).enqueuedAt=new Date(e.enqueuedAt),[2,e]}})})},Index.prototype.getFaceting=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return t="indexes/".concat(this.uid,"/settings/faceting"),[4,this.httpRequest.get(t)];case 1:return[2,e.sent()]}})})},Index.prototype.updateFaceting=function(t){return __awaiter(this,void 0,void 0,function(){var e,r;return __generator(this,function(n){switch(n.label){case 0:return e="indexes/".concat(this.uid,"/settings/faceting"),[4,this.httpRequest.patch(e,t)];case 1:return r=n.sent(),[2,new EnqueuedTask(r)]}})})},Index.prototype.resetFaceting=function(){return __awaiter(this,void 0,void 0,function(){var t,e;return __generator(this,function(r){switch(r.label){case 0:return t="indexes/".concat(this.uid,"/settings/faceting"),[4,this.httpRequest.delete(t)];case 1:return e=r.sent(),[2,new EnqueuedTask(e)]}})})},Index}(),u=function(t){function MeiliSearch(e){return t.call(this,e)||this}return __extends(MeiliSearch,t),MeiliSearch}(function(){function Client(t){this.config=t,this.httpRequest=new s(t),this.tasks=new o(t)}return Client.prototype.index=function(t){return new a(this.config,t)},Client.prototype.getIndex=function(t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){return[2,new a(this.config,t).fetchInfo()]})})},Client.prototype.getRawIndex=function(t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){return[2,new a(this.config,t).getRawInfo()]})})},Client.prototype.getIndexes=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){var e,r,n=this;return __generator(this,function(i){switch(i.label){case 0:return[4,this.getRawIndexes(t)];case 1:return r=(e=i.sent()).results.map(function(t){return new a(n.config,t.uid,t.primaryKey)}),[2,__assign(__assign({},e),{results:r})]}})})},Client.prototype.getRawIndexes=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.httpRequest.get("indexes",t)];case 1:return[2,e.sent()]}})})},Client.prototype.createIndex=function(t,e){return void 0===e&&(e={}),__awaiter(this,void 0,void 0,function(){return __generator(this,function(r){switch(r.label){case 0:return[4,a.create(t,e,this.config)];case 1:return[2,r.sent()]}})})},Client.prototype.updateIndex=function(t,e){return void 0===e&&(e={}),__awaiter(this,void 0,void 0,function(){return __generator(this,function(r){switch(r.label){case 0:return[4,new a(this.config,t).update(e)];case 1:return[2,r.sent()]}})})},Client.prototype.deleteIndex=function(t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,new a(this.config,t).delete()];case 1:return[2,e.sent()]}})})},Client.prototype.deleteIndexIfExists=function(t){return __awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(r){switch(r.label){case 0:return r.trys.push([0,2,,3]),[4,this.deleteIndex(t)];case 1:return r.sent(),[2,!0];case 2:if("index_not_found"===(e=r.sent()).code)return[2,!1];throw e;case 3:return[2]}})})},Client.prototype.swapIndexes=function(t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.httpRequest.post("/swap-indexes",t)];case 1:return[2,e.sent()]}})})},Client.prototype.getTasks=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.getTasks(t)];case 1:return[2,e.sent()]}})})},Client.prototype.getTask=function(t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.getTask(t)];case 1:return[2,e.sent()]}})})},Client.prototype.waitForTasks=function(t,e){var r=void 0===e?{}:e,n=r.timeOutMs,i=void 0===n?5e3:n,s=r.intervalMs,o=void 0===s?50:s;return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.waitForTasks(t,{timeOutMs:i,intervalMs:o})];case 1:return[2,e.sent()]}})})},Client.prototype.waitForTask=function(t,e){var r=void 0===e?{}:e,n=r.timeOutMs,i=void 0===n?5e3:n,s=r.intervalMs,o=void 0===s?50:s;return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.waitForTask(t,{timeOutMs:i,intervalMs:o})];case 1:return[2,e.sent()]}})})},Client.prototype.cancelTasks=function(t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.cancelTasks(t)];case 1:return[2,e.sent()]}})})},Client.prototype.deleteTasks=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.tasks.deleteTasks(t)];case 1:return[2,e.sent()]}})})},Client.prototype.getKeys=function(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(r){switch(r.label){case 0:return[4,this.httpRequest.get("keys",t)];case 1:return(e=r.sent()).results=e.results.map(function(t){return __assign(__assign({},t),{createdAt:new Date(t.createdAt),updateAt:new Date(t.updateAt)})}),[2,e]}})})},Client.prototype.getKey=function(t){return __awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(r){switch(r.label){case 0:return e="keys/".concat(t),[4,this.httpRequest.get(e)];case 1:return[2,r.sent()]}})})},Client.prototype.createKey=function(t){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(e){switch(e.label){case 0:return[4,this.httpRequest.post("keys",t)];case 1:return[2,e.sent()]}})})},Client.prototype.updateKey=function(t,e){return __awaiter(this,void 0,void 0,function(){var r;return __generator(this,function(n){switch(n.label){case 0:return r="keys/".concat(t),[4,this.httpRequest.patch(r,e)];case 1:return[2,n.sent()]}})})},Client.prototype.deleteKey=function(t){return __awaiter(this,void 0,void 0,function(){var e;return __generator(this,function(r){switch(r.label){case 0:return e="keys/".concat(t),[4,this.httpRequest.delete(e)];case 1:return[2,r.sent()]}})})},Client.prototype.health=function(){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(t){switch(t.label){case 0:return[4,this.httpRequest.get("health")];case 1:return[2,t.sent()]}})})},Client.prototype.isHealthy=function(){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,this.httpRequest.get("health")];case 1:return t.sent(),[2,!0];case 2:return t.sent(),[2,!1];case 3:return[2]}})})},Client.prototype.getStats=function(){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(t){switch(t.label){case 0:return[4,this.httpRequest.get("stats")];case 1:return[2,t.sent()]}})})},Client.prototype.getVersion=function(){return __awaiter(this,void 0,void 0,function(){return __generator(this,function(t){switch(t.label){case 0:return[4,this.httpRequest.get("version")];case 1:return[2,t.sent()]}})})},Client.prototype.createDump=function(){return __awaiter(this,void 0,void 0,function(){var t;return __generator(this,function(e){switch(e.label){case 0:return[4,this.httpRequest.post("dumps")];case 1:return t=e.sent(),[2,new EnqueuedTask(t)]}})})},Client.prototype.generateTenantToken=function(t,e,r){var n=Error();throw Error("Meilisearch: failed to generate a tenant token. Generation of a token only works in a node environment \n ".concat(n.stack,"."))},Client}());t.Index=a,t.MatchingStrategies={ALL:"all",LAST:"last"},t.MeiliSearch=u,t.MeiliSearchApiError=r,t.MeiliSearchCommunicationError=e,t.MeiliSearchError=n,t.MeiliSearchTimeOutError=i,t.default=u,t.httpErrorHandler=httpErrorHandler,t.httpResponseErrorHandler=httpResponseErrorHandler,Object.defineProperty(t,"__esModule",{value:!0})}(e,r(4836))}}]);