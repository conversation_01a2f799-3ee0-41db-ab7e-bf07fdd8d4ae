'use strict';

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */

module.exports = {
  async init() {

    const response = {
      message: "Terminé sans erreur",
      errors: [],
    }

    let fetchedTopicGroup;

    for(const topicGroup of tgRelations) {
      fetchedTopicGroup = await strapi.query('topic-group').findOne({ slug: topicGroup.name });
      if(fetchedTopicGroup?.id) {
        let { relationIds, errors }  = await getRelationIds(topicGroup.topics);
        if(errors.on) {
          response.message = "Terminé avec erreurs";
          response.errors.push(errors)
        }
        await strapi.query('topic-group').update({id: fetchedTopicGroup.id}, {topics: relationIds})
      } else {
        response.errors.push({
          on: "topic: " + topicGroup.name,
          err: "can't find id with this slug"
        })
        response.message = "Done with errors"
      }
    }
    return response;
  }
};


const getRelationIds = async (relationSlugs) => {
  let relationIds = [];
  let errors = {
    on: null,
    err: "slugs not found"
  }

  for(const relationSlug of relationSlugs) {
    let fetchedTopic = await strapi.query('topic').findOne({ slug: relationSlug });
    if(fetchedTopic) {
      relationIds.push(fetchedTopic.id);
    } else {
      errors.on += "| " + relationSlug
    }
  }
  return { relationIds, errors };
}


const tgRelations = [
  {
    "name": "celibat",
    "topics": [
      "celibat",
      ]
  },
  {
    "name": "citoyen",
    "topics": [
      "mandat-culturel",
      "grace-commune",
      ]
  },
  {
    "name": "discipulat-et-accompagnement-biblique",
    "topics": [
      "doctrine-de-lhomme",
      "la-chute",
      "foi-et-repentance",
      "sanctification",
      "perseverance-des-saints",
      "vie-chretienne",
      "croissance-spirituelle",
      "discipulat-mentorat",
      "accompagnement-biblique",
      "bapteme",
      "homosexualite-et-lgbt",
      "transgenre",
      "justice-sociale",
      "politique-et-societe",
      "avortement",
      "divorce",
      "fin-de-vie-et-euthanasie",
      "suicide",
      "pma-et-gpa",
      "pauvrete",
      "harmonie-raciale",
      "ecologie",
      ]
  },
  {
    "name": "eglise",
    "topics": [
      "vie-deglise",
      ]
  },
  {
    "name": "epoux-epouse",
    "topics": [
      "complementarisme",
      "mariage",
      ]
  },
  {
    "name": "evangelisation-et-mission",
    "topics": [
      "evangelisation",
      "implantation-deglise",
      "mission-de-leglise",
      "peuples-non-atteints",
      "homosexualite-et-lgbt",
      "transgenre",
      "justice-sociale",
      "politique-et-societe",
      "avortement",
      "divorce",
      "fin-de-vie-et-euthanasie",
      "suicide",
      "pma-et-gpa",
      "pauvrete",
      "harmonie-raciale",
      "ecologie",
      "defense-de-la-foi-evidentialisme",
      "theodicee",
      "culture-et-arts",
      "technologie-et-medias",
      "vision-du-monde-chretienne",
      "islam",
      "religions-du-monde",
      "occultisme",
      "sectes",
      "heresies-faux-enseignements",
      "philosophie",
      "critique-de-film",
      ]
  },
  {
    "name": "famille",
    "topics": [
      "vie-de-famille",
      "hospitalite",
      ]
  },
  {
    "name": "homme-femme",
    "topics": [
      "feminite",
      "masculinite",
      ]
  },
  {
    "name": "membre",
    "topics": [
      "vie-chretienne",
      "croissance-spirituelle",
      "membriete",
      "evangelisation",
      "predication",
      ]
  },
  {
    "name": "musique-et-louange",
    "topics": [
      "culte-et-liturgie",
      "culte-communautaire",
      ]
  },
  {
    "name": "pasteur-et-ancien",
    "topics": [
      "doctrine-de-dieu",
      "doctrine-de-lecriture",
      "doctrine-de-la-creation",
      "doctrine-de-lhomme",
      "doctrine-du-peche",
      "doctrine-du-christ",
      "doctrine-du-salut",
      "doctrine-de-leglise",
      "doctrine-des-temps-de-la-fin",
      "livres-de-la-bible",
      "histoire-du-salut",
      "theologie-de-lalliance",
      "royaume-de-dieu",
      "vie-chretienne",
      "croissance-spirituelle",
      "ministere-pastoral",
      "culte-communautaire",
      "discipline-deglise",
      "membriete",
      "discipulat-mentorat",
      "accompagnement-biblique",
      "bapteme",
      "cene",
      "evangelisation",
      "implantation-deglise",
      "mission-de-leglise",
      "peuples-non-atteints",
      "catechismes-et-confessions-de-foi",
      "histoire-de-leglise",
      "figures-historiques",
      "homosexualite-et-lgbt",
      "transgenre",
      "justice-sociale",
      "politique-et-societe",
      "avortement",
      "divorce",
      "fin-de-vie-et-euthanasie",
      "suicide",
      "pma-et-gpa",
      "pauvrete",
      "harmonie-raciale",
      "ecologie",
      ]
  },
  {
    "name": "pere-mere",
    "topics": [
      "parentalite",
      ]
  },
  {
    "name": "predicateur-et-enseignant",
    "topics": [
      "doctrine-de-dieu",
      "doctrine-de-lecriture",
      "doctrine-de-la-creation",
      "doctrine-de-lhomme",
      "doctrine-du-peche",
      "doctrine-du-christ",
      "doctrine-du-salut",
      "doctrine-des-temps-de-la-fin",
      "livres-de-la-bible",
      "histoire-du-salut",
      "theologie-de-lalliance",
      "royaume-de-dieu",
      "predication-et-enseignement",
      "catechismes-et-confessions-de-foi",
      "histoire-de-leglise",
      "figures-historiques",
      "defense-de-la-foi-evidentialisme",
      "theodicee",
      "culture-et-arts",
      "technologie-et-medias",
      "vision-du-monde-chretienne",
      "islam",
      "religions-du-monde",
      "occultisme",
      "sectes",
      "heresies-faux-enseignements",
      "philosophie",
      "predication",
      ]
  },
  {
    "name": "societe",
    "topics": [
      "vie-sociale",
      ]
  },
  {
    "name": "travail",
    "topics": [
      "mandat-culturel",
      "travail",
      ]
  },
  {
    "name": "voisin-et-ami",
    "topics": [
      "hospitalite",
      "amitie",
      ]
  }
]