const fetch = require('node-fetch');

const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function resetMeilisearch() {
  try {
    console.log('Réinitialisation de Meilisearch...');
    
    // Supprimer l'index existant
    console.log('Suppression de l\'index existant...');
    const deleteResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}`, {
      method: 'DELETE'
    });
    
    if (deleteResponse.ok) {
      console.log('Index supprimé avec succès');
    } else {
      console.log('L\'index n\'existe pas ou n\'a pas pu être supprimé');
    }
    
    // Créer un nouvel index
    console.log('Création d\'un nouvel index...');
    const createResponse = await fetch(`${MEILI_URL}/indexes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ uid: INDEX_NAME })
    });
    
    if (createResponse.ok) {
      console.log('Index créé avec succès');
    } else {
      console.error('Erreur lors de la création de l\'index:', await createResponse.text());
      return;
    }
    
    // Attendre un peu pour que l'index soit créé
    console.log('Attente de la création de l\'index...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Configurer les attributs filtrables
    console.log('Configuration des attributs filtrables...');
    const filterableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'title',
        'topics',
        'tags',
        'author',
        'type',
        'blog',
        'cs'
      ])
    });
    
    if (filterableResponse.ok) {
      console.log('Attributs filtrables configurés avec succès');
    } else {
      console.error('Erreur lors de la configuration des attributs filtrables:', await filterableResponse.text());
    }
    
    // Attendre un peu pour que les attributs filtrables soient configurés
    console.log('Attente de la configuration des attributs filtrables...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Configurer les attributs triables
    console.log('Configuration des attributs triables...');
    const sortableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['date'])
    });
    
    if (sortableResponse.ok) {
      console.log('Attributs triables configurés avec succès');
    } else {
      console.error('Erreur lors de la configuration des attributs triables:', await sortableResponse.text());
    }
    
    // Attendre un peu pour que les attributs triables soient configurés
    console.log('Attente de la configuration des attributs triables...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Configurer les attributs recherchables
    console.log('Configuration des attributs recherchables...');
    const searchableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'title',
        'body',
        'lead'
      ])
    });
    
    if (searchableResponse.ok) {
      console.log('Attributs recherchables configurés avec succès');
    } else {
      console.error('Erreur lors de la configuration des attributs recherchables:', await searchableResponse.text());
    }
    
    // Attendre un peu pour que les attributs recherchables soient configurés
    console.log('Attente de la configuration des attributs recherchables...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Configurer la pagination
    console.log('Configuration de la pagination...');
    const paginationResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/pagination`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        maxTotalHits: 4000
      })
    });
    
    if (paginationResponse.ok) {
      console.log('Pagination configurée avec succès');
    } else {
      console.error('Erreur lors de la configuration de la pagination:', await paginationResponse.text());
    }
    
    console.log('Réinitialisation terminée !');
    console.log('Vous devez maintenant réindexer les documents dans Meilisearch.');
  } catch (error) {
    console.error('Erreur lors de la réinitialisation:', error);
  }
}

resetMeilisearch();
