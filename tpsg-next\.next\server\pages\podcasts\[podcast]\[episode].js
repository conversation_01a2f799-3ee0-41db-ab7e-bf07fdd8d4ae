"use strict";(()=>{var e={};e.id=4827,e.ids=[4827,2888],e.modules={6912:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{config:()=>x,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>u,reportWebVitals:()=>h,routeModule:()=>S,unstable_getServerProps:()=>w,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>f});var o=a(7093),i=a(5244),s=a(1323),p=a(779),d=a(4033),l=a(9905),n=e([l]);l=(n.then?(await n)():n)[0];let c=(0,s.l)(l,"default"),u=(0,s.l)(l,"getStaticProps"),m=(0,s.l)(l,"getStaticPaths"),g=(0,s.l)(l,"getServerSideProps"),x=(0,s.l)(l,"config"),h=(0,s.l)(l,"reportWebVitals"),f=(0,s.l)(l,"unstable_getStaticProps"),b=(0,s.l)(l,"unstable_getStaticPaths"),y=(0,s.l)(l,"unstable_getStaticParams"),w=(0,s.l)(l,"unstable_getServerProps"),P=(0,s.l)(l,"unstable_getServerSideProps"),S=new o.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/podcasts/[podcast]/[episode]",pathname:"/podcasts/[podcast]/[episode]",bundlePath:"",filename:""},components:{App:d.default,Document:p.default},userland:l});r()}catch(e){r(e)}})},9905:(e,t,a)=>{a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>PodcastEpisode,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var o=a(997),i=a(9114),s=a(1385),p=a(635),d=a(7672),l=a(7518),n=a.n(l),c=a(7467),u=a(1077),m=a(6303),g=e([m]);m=(g.then?(await g)():g)[0];let x="true"===process.env.LIGHT_BUILD?10:9999;function PodcastEpisode({post:e,relatedPosts:t}){return o.jsx(m.Q4,{episode:e,preview:!1,relatedPosts:t})}async function getStaticPaths(){let e=await s.Z.query({query:h,variables:{limit:x}}).then(e=>e.data.posts),t=e.map(e=>{let t=(0,p.fw)(e.modules);if(t.podcast?.podcast)return{params:{podcast:t.podcast.podcast.slug,episode:e.slug}}});return{paths:t,fallback:!0}}async function getStaticProps({params:e}){let t=await s.Z.query({query:f,variables:{episodeSlug:e.episode}}).then(e=>e.data.posts[0]);if(!t)return{notFound:!0};let a=await s.Z.query({query:d.o$.QUERY_RELATED,variables:{id:t.id}}).then(e=>e.data.relatedPosts);return{props:{post:{...t,modules:(0,p.fw)(t.modules),route:(0,u.qt)(t)},relatedPosts:a||void 0},revalidate:10}}let h=i.gql`
    query EpisodeSlugs($limit: Int!)  {
        posts(where: { type: "podcast" }, limit: $limit) {
            slug
            modules {
                __typename
                ... on ComponentModulePodcast {
                  __typename
                    podcast {
                        slug
                    }
                }
            }
        }
    }
`,f=i.gql`
    query PodcastEpisode($episodeSlug: String!) {
        posts(where: { slug: $episodeSlug }) {
            title
            type
            slug
            published_at
            body
            id
            image {
                url
                provider
            }
            topics {
                name
                slug
            }
            author {
                fullName
                picture {
                    url
                    provider
                }
            }
            modules {
                ... on ComponentModulePodcast {
                    __typename
                    podcast {
                        slug
                        name
                        logoSmall {
                            url
                            provider
                        }
                        platforms {
                            name
                            url
                        }
                    }
                    embedAudio
                    embedVideo
                }
                ... on ComponentModuleLead {
                    __typename
                    content
                }
            }
        }
    }
`;n().div.withConfig({componentId:"sc-5c456b99-0"})`
  padding-bottom: 70px;

  header {
    display: flex;
    flex-direction: column;
    margin-bottom: 48px;
  }

  .header-player-cover {
    width: 100%;
    aspect-ratio: 16 / 10;
  }

  .card-label {
    display: none;
    margin: 8px 8px 0 0;
  }

  .video-player {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }

  .header-img-container {
    position: relative;
    width: 50%;
    aspect-ratio: 16 / 10;
  }

  @media ${c.U.tablet} {
    .card-label {
      display: inline-block;
    }

    .header-player-cover {
      margin-top: 48px;
    }
  }
  @media ${c.U.desktop} {
    header {
      margin-top: 90px;
      margin-bottom: 70px;
      flex-direction: row;
      justify-content: space-between;
    }

    .header-text-container {
      position: relative;
      width: 50%;
      aspect-ratio: 16 / 10;
      padding-top: 24px;
      padding-left: 16px;
    }

    .header-player-cover {
      margin-top: 0;
      width: calc(50% - 32px);
    }
  }
`,n().div.withConfig({componentId:"sc-5c456b99-1"})`
  position: relative;
  width: 100%;

  .right-content-sticky {
    position: sticky;
    top: 50px;
  }

  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }

  @media ${c.U.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${c.U.desktop} {
    border-left: 1px solid #dddddd;
    width: 40%;
    padding-left: 32px;
  }
`,n().main.withConfig({componentId:"sc-5c456b99-2"})`
  margin-top: 70px;
  display: block;

  @media ${c.U.desktop} {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
`,n().article.withConfig({componentId:"sc-5c456b99-3"})`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media ${c.U.desktop} {
    width: 50%;
  }
`,r()}catch(e){r(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},6158:e=>{e.exports=require("react-share")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),a=t.X(0,[3181,5016,6859,8450,9755,5152,4033,779,7113,6453,1077,7620,8672,2832,8657,4004,7482,3462,3646,6303],()=>__webpack_exec__(6912));module.exports=a})();