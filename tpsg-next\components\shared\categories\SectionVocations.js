import styled from "styled-components";
import { device } from "styles/device";
import ListLink from "../ListLink";

const renderChildren = (children) => {
  return (
    <div className={"ministries"}>
      {children?.map((child, key) => (
        <ListLink
          key={key}
          image={child.cover}
          text={child.name}
          route={`/categories/${child.type}/${child.slug}`}
        />
      ))}
    </div>
  );
};
export default function SectionVocations({ groups }) {

  const parents = groups.filter((vocation) => vocation.children.length > 0);

  return (
    <SectionVocationsWrapper>
      <div>
        {parents?.map((parent, key) => (
          <div key={key}>
            <ListLink
              image={parent.cover}
              text={parent.name}
              route={`/categories/vocation/${parent.slug}`}
            />
            {parent.children && renderChildren(parent.children)}
          </div>
        ))}
      </div>
    </SectionVocationsWrapper>
  );
}

const SectionVocationsWrapper = styled.section`
  .ministries {
    margin-left: 24px;
  }
  @media ${device.tablet} {
    .ministries {
      margin-left: 40px;
    }
  }
  @media ${device.desktop} {
    flex-direction: row;
  }
`;
