import styled from "styled-components"
import Link from "next/link";
import { isLinkExternal } from "utils/string.utils";
import { device } from "../../../../styles/device";


/**
 *
 * @param text
 * @param link (optionnel)
 * @param onClickFunction (optionnel)
 * @param theme "light" ou "dark"
 * @return {JSX.Element}
 * @constructor
 */
export default function BigCta({ text, link, onClickFunction, theme, outline= false, fullWidth = true }) {

  const backgroundColor = outline ?
    "transparent" :
    theme === "dark" ? "var(--c-dark-green)" : "var(--c-soft-cream)";

  const textColor = outline ?
    theme === "dark" ? "var(--c-dark-green)" : "var(--c-soft-cream)" :
    theme === "dark" ? "var(--c-soft-cream)" : "var(--c-dark-green)"
  
  const Cta = (
    <ButtonWrapper
      fullWidth={fullWidth}
      text={text}
      theme={theme}
      textColor={textColor}
      backgroundColor={backgroundColor}
      className={"cta-big"}
      outline={outline}>
      <p>{text}</p>
    </ButtonWrapper>
  )

  if(isLinkExternal(link)) {
    return (
      <a href={link}
        rel="noopener noreferrer"
        onClick={onClickFunction || null}
        target="_blank">
        {Cta}
      </a>
    )
  }

  return (
    <Link href={link} 
      onClick={onClickFunction || null}>
      {Cta}
    </Link>
  )
};


const ButtonWrapper = styled.button`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 40px;
  border-radius: 100px;
  border: 1px solid ${p => p.outline ? p.textColor : "transparent" };
  width: ${p => p.fullWidth ? "100%" : "auto"};

  /* Corrects font smoothing for webkit */
  -webkit-font-smoothing: inherit;
  -moz-osx-font-smoothing: inherit;

  /* Corrects inability to style clickable \`input\` types in iOS */
  -webkit-appearance: none;
  
  background-color: ${p => p.backgroundColor};
  
  p {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    font-family: Switzer, sans-serif;
    color: ${p => p.textColor};
  }
  
  cursor: pointer;
  
  &:hover {
    background-color: var(--c-brand-lighter);
    P {
      color: var(--c-soft-cream);
    }
  }
  
  @media ${device.tablet} {
    width: inherit;
  }
`;



