/**
 * Script pour configurer Meilisearch v0.27.0 afin qu'il soit compatible avec le code de production
 * 
 * Ce script configure Meilisearch v0.27.0 pour qu'il fonctionne avec le code existant
 * sans avoir à modifier le code source.
 * 
 * IMPORTANT: Ce script doit être exécuté une seule fois après le démarrage de Meilisearch.
 */

const fetch = require('node-fetch');

// Configuration
const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function configureMeilisearch() {
  console.log('Configuration de Meilisearch v0.27.0 pour compatibilité avec la production...');
  console.log(`URL: ${MEILI_URL}`);
  console.log(`Index: ${INDEX_NAME}`);

  try {
    // 1. Vérifier si l'index existe
    console.log('\nVérification de l\'existence de l\'index...');
    const indexesResponse = await fetch(`${MEILI_URL}/indexes`);
    const indexes = await indexesResponse.json();
    
    const indexExists = indexes.results && indexes.results.some(index => index.uid === INDEX_NAME);
    
    if (!indexExists) {
      console.log('L\'index n\'existe pas, création de l\'index...');
      const createResponse = await fetch(`${MEILI_URL}/indexes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uid: INDEX_NAME })
      });
      
      if (!createResponse.ok) {
        throw new Error(`Erreur lors de la création de l'index: ${createResponse.status}`);
      }
      
      console.log('Index créé avec succès');
    } else {
      console.log('L\'index existe déjà');
    }
    
    // 2. Configurer les attributs filtrables
    console.log('\nConfiguration des attributs filtrables...');
    const filterableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'title', 'topics', 'tags', 'author', 'type', 'blog', 'cs', 'series'
      ])
    });
    
    if (!filterableResponse.ok) {
      throw new Error(`Erreur lors de la configuration des attributs filtrables: ${filterableResponse.status}`);
    }
    
    console.log(`Attributs filtrables configurés: ${filterableResponse.status}`);
    
    // 3. Configurer les attributs triables
    console.log('\nConfiguration des attributs triables...');
    const sortableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['date'])
    });
    
    if (!sortableResponse.ok) {
      throw new Error(`Erreur lors de la configuration des attributs triables: ${sortableResponse.status}`);
    }
    
    console.log(`Attributs triables configurés: ${sortableResponse.status}`);
    
    // 4. Configurer les attributs de recherche
    console.log('\nConfiguration des attributs de recherche...');
    const searchableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['title', 'body', 'lead'])
    });
    
    if (!searchableResponse.ok) {
      throw new Error(`Erreur lors de la configuration des attributs de recherche: ${searchableResponse.status}`);
    }
    
    console.log(`Attributs de recherche configurés: ${searchableResponse.status}`);
    
    // 5. Configurer les règles de classement
    console.log('\nConfiguration des règles de classement...');
    const rankingResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/ranking-rules`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'sort',
        'words',
        'typo',
        'proximity',
        'attribute',
        'exactness'
      ])
    });
    
    if (!rankingResponse.ok) {
      throw new Error(`Erreur lors de la configuration des règles de classement: ${rankingResponse.status}`);
    }
    
    console.log(`Règles de classement configurées: ${rankingResponse.status}`);
    
    // 6. Configurer la pagination
    console.log('\nConfiguration de la pagination...');
    try {
      const paginationResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/pagination`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          maxTotalHits: 4000
        })
      });
      console.log(`Pagination configurée: ${paginationResponse.status}`);
    } catch (error) {
      console.log('Note: La configuration de pagination peut ne pas être supportée dans cette version de Meilisearch');
    }
    
    console.log('\nConfiguration terminée avec succès!');
    console.log('\nIMPORTANT:');
    console.log('1. Redémarrez votre application Next.js pour appliquer les changements');
    console.log('2. Si vous rencontrez encore des problèmes, vérifiez que Meilisearch est bien démarré');
    console.log('3. Assurez-vous que les variables d\'environnement NEXT_PUBLIC_MEILISEARCH_HOST et NEXT_PUBLIC_MEILISEARCH_KEY sont correctement définies');
    
  } catch (error) {
    console.error('\nErreur lors de la configuration de Meilisearch:', error);
  }
}

configureMeilisearch();
