import styled from "styled-components";
import { useCookies, Cookies } from "react-cookie";
import { useEffect, useState } from "react";
import { SmallButton } from "./Buttons";

export default function CookieWall({ style, children }) {
  const [cookie, setCookie] = useCookies(["preferences"]);
  const [authorized, setAuthorized] = useState(undefined);

  useEffect(() => {
    if (cookie?.preferences?.medias) {
      setAuthorized(true);
    } else {
      setAuthorized(false);
    }
  }, [cookie]);

  function handleAccept() {
    setCookie(
      "preferences",
      { ...cookie.preferences, medias: true },
      { sameSite: "strict", path: "/", maxAge: 60 * 60 * 24 * 365 }
    );
  }

  return (
    <>
      {authorized === true ? (
        children
      ) : (
        <StlCookieWall style={{ ...style }}>
          <p className={"cw-text"}>
            Nous n’avons pas d’autorisation de votre part pour l’utilisation de
            services tiers (YouTube, Spotify, SoundCloud, ConvertKit, …) depuis
            toutpoursagloire.com. Cette autorisation est nécessaire pour une
            expérience complète sur notre site. Vous pouvez les accepter en
            appuyant sur le bouton ci-dessous
          </p>
          <SmallButton
            text={"Accepter"}
            theme={"light"}
            action={() => handleAccept()}
          />
        </StlCookieWall>
      )}
    </>
  );
}

const StlCookieWall = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  background-color: var(--c-dark-green);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: start;
  padding: 40px;

  .cw-text {
    margin-top: 0;
    font-family: Switzer, sans-serif;
    font-size: 16px;
    line-height: 24px;
    color: #f4f4f4;
  }
`;
