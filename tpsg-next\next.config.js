module.exports = {
  reactStrictMode: true,
  experimental: {
    // ssr and displayName are configured by default
    // removeConsole: true,
    scrollRestoration: true,
  },
  compiler: {
    // Enables the styled-components SWC transform
    styledComponents: true,
  },
  images: {
    domains: ["localhost", process.env.NEXT_IMAGES_DOMAINS],
  },
  // webpack5: true,
  webpack: (config) => {
    config.resolve.fallback = { fs: false };
    return config;
  },
}
