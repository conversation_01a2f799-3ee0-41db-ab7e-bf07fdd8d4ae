// noinspection GraphQLUnresolvedReference
import { gql } from "@apollo/client";
import client from "api/apollo-client";
import { modulesAsObj } from "utils/components.utils";
import { queries } from "api/gql-queries";
import styled from "styled-components";
import { device } from "styles/device";
import { getPostRoute } from "../../../utils/posts.utils";
import { PodcastLayout } from "components/layout/PagesLayout";

const BUILD_LIMIT = process.env.LIGHT_BUILD === "true" ? 10 : 9999;

export default function PodcastEpisode({ post, relatedPosts }) {
  return <PodcastLayout episode={post} preview={false} relatedPosts={relatedPosts} />;
}

export async function getStaticPaths() {

  const episodes = await client
    .query({
      query: QUERY_EPISODE_SLUGS,
      variables: { limit: BUILD_LIMIT },
    })
    .then((response) => {
      return response.data.posts;
    });

  const paths = episodes.map((episode) => {
    let episode_module = modulesAsObj(episode.modules);
    if (episode_module.podcast?.podcast) {
      return {
        params: {
          podcast: episode_module.podcast.podcast.slug,
          episode: episode.slug,
        },
      };
    }
  });

  return {
    paths, fallback: true,
  };
}

export async function getStaticProps({ params }) {

  // Récupération de l'épisode
  const episode = await client
    .query({
      query: GET_PODCAST_EPISODE, variables: { episodeSlug: params.episode },
    })
    .then((response) => {
      return response.data.posts[0];
    });

  if (!episode) {
    return { notFound: true };
  }

  // Récupération des ressources similaires
  const relatedPosts = await client
    .query({
      query: queries.QUERY_RELATED, variables: { id: episode.id },
    })
    .then((response) => {
      return response.data.relatedPosts;
    });

  return {
    props: {
      post: {
        ...episode,
        modules: modulesAsObj(episode.modules),
        route: getPostRoute(episode)
      },
      relatedPosts: relatedPosts || undefined,
    },
    revalidate: 10,
  };
}

const QUERY_EPISODE_SLUGS = gql`
    query EpisodeSlugs($limit: Int!)  {
        posts(where: { type: "podcast" }, limit: $limit) {
            slug
            modules {
                __typename
                ... on ComponentModulePodcast {
                  __typename
                    podcast {
                        slug
                    }
                }
            }
        }
    }
`;

const GET_PODCAST_EPISODE = gql`
    query PodcastEpisode($episodeSlug: String!) {
        posts(where: { slug: $episodeSlug }) {
            title
            type
            slug
            published_at
            body
            id
            image {
                url
                provider
            }
            topics {
                name
                slug
            }
            author {
                fullName
                picture {
                    url
                    provider
                }
            }
            modules {
                ... on ComponentModulePodcast {
                    __typename
                    podcast {
                        slug
                        name
                        logoSmall {
                            url
                            provider
                        }
                        platforms {
                            name
                            url
                        }
                    }
                    embedAudio
                    embedVideo
                }
                ... on ComponentModuleLead {
                    __typename
                    content
                }
            }
        }
    }
`;

const PageWrapper = styled.div`
  padding-bottom: 70px;

  header {
    display: flex;
    flex-direction: column;
    margin-bottom: 48px;
  }

  .header-player-cover {
    width: 100%;
    aspect-ratio: 16 / 10;
  }

  .card-label {
    display: none;
    margin: 8px 8px 0 0;
  }

  .video-player {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }

  .header-img-container {
    position: relative;
    width: 50%;
    aspect-ratio: 16 / 10;
  }

  @media ${device.tablet} {
    .card-label {
      display: inline-block;
    }

    .header-player-cover {
      margin-top: 48px;
    }
  }
  @media ${device.desktop} {
    header {
      margin-top: 90px;
      margin-bottom: 70px;
      flex-direction: row;
      justify-content: space-between;
    }

    .header-text-container {
      position: relative;
      width: 50%;
      aspect-ratio: 16 / 10;
      padding-top: 24px;
      padding-left: 16px;
    }

    .header-player-cover {
      margin-top: 0;
      width: calc(50% - 32px);
    }
  }
`;

const RightContent = styled.div`
  position: relative;
  width: 100%;

  .right-content-sticky {
    position: sticky;
    top: 50px;
  }

  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }

  @media ${device.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${device.desktop} {
    border-left: 1px solid #dddddd;
    width: 40%;
    padding-left: 32px;
  }
`;

const MainContent = styled.main`
  margin-top: 70px;
  display: block;

  @media ${device.desktop} {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
`;

const LeftContent = styled.article`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media ${device.desktop} {
    width: 50%;
  }
`;
