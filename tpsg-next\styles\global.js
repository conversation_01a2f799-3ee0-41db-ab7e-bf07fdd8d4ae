import { createGlobalStyle } from "styled-components";
import { device } from "styles/device";

export const Global = createGlobalStyle`

  // TODO: Renommer les variables correctement
  :root {
    
    /**
     * COLORS
     */
    --soft-white: #FAF7F3;
    --c-cream: #FFEBD8;
    --c-soft-cream: #F9F1E6;
    --c-cream-A80: rgba(255, 235, 216, 0.8);
    --c-cream-A40: rgba(255, 235, 216, 0.4);
    --c-cream-A20: rgba(255, 235, 216, 0.2);
    --soft-dark: #161616;
    //--blue-dark: #081921;
    --blue-dark: #081D21;
    --c--blue-medium: #1C373C;
    --c-dark-green: #081D21;
    --brand-color: #EE5131;
    --c-brand-light: #F3673B;
    --c-brand-lighter: #FA7051;

    --mobile-gap: 24px;
    --tablet-gap: 60px;
    --desktop-gap: 80px;
    --desktopxl-gap: 96px;
    --max-page-width: 1380px;

    --border-space: 24px; // valeur des marges de la page;
    --spacing-l: 48px;

    @media ${device.tablet} {
      --border-space: 60px;
      --spacing-l: 52px;
    }

    @media ${device.desktop} {
      --border-space: 64px;
      --spacing-l: 96px;
    }

    @media ${device.desktopXL} {
      --border-space: calc((100vw + 32px - var(--max-page-width)) / 2);
      --spacing-l: 96px;
    }

    // Spacing
    --fluid-space-m: clamp(3rem, 0.22rem + 5.97vw, 5rem);
  }

  body {
    width: 100%;
    overflow-x: hidden;
    //overflow-y: scroll;
    margin: auto;
    padding-top: 80px;
    background-color: var(--soft-white);
    font-family: Stelvio, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  }

  html {
    width: 100%;
    background-color: white;
    //background-color: var(--soft-white);
    overscroll-behavior-y: none;
  }

  .site-padding {
    padding-left: var(--mobile-gap);
    padding-right: var(--mobile-gap);
    @media ${device.tablet} {
      padding-left: var(--tablet-gap);
      padding-right: var(--tablet-gap);
    }
    @media ${device.desktop} {
      padding-left: var(--desktop-gap);
      padding-right: var(--desktop-gap);
    }
    @media ${device.desktopXL} {
      max-width: 1380px;
      margin: auto;
      padding-left: 32px;
      padding-right: 32px;
    }
  }

  .site-padding-left {
    padding-left: var(--mobile-gap) !important;
    @media ${device.tablet} {
      padding-left: var(--tablet-gap) !important;
    }
    @media ${device.desktop} {
      padding-left: var(--desktop-gap) !important;
    }
    @media ${device.desktopXL} {
      padding-left: calc(32px + (100vw - 1380px) / 2) !important;
    }
  }

  .site-padding-right {
    padding-right: var(--mobile-gap) !important;
    @media ${device.tablet} {
      padding-right: var(--tablet-gap) !important;
    }
    @media ${device.desktop} {
      padding-right: var(--desktop-gap) !important;
    }
    @media ${device.desktopXL} {
      padding-right: calc(32px + (100vw - 1380px) / 2) !important;
    }
  }

  .site-margin {
    margin-left: var(--mobile-gap);
    margin-right: var(--mobile-gap);
    @media ${device.tablet} {
      margin-left: var(--mobile-gap);
      margin-right: var(--mobile-gap);
    }
    @media ${device.desktop} {
      margin-left: var(--desktop-gap);
      margin-right: var(--desktop-gap);
    }
    @media ${device.desktopXL} {
      max-width: var(--max-page-width);
      margin: auto;
    }
  }

  .fw-background {
    &:before {
      content: '';
      position: absolute;
      width: 105vw;
      height: 100%;
      top: 0;
      left: -24px;
      @media ${device.tablet} {
        left: -60px;
      }
      @media ${device.desktop} {
        left: -80px;
      }
      @media ${device.desktopXL} {
        left: -96px;
      }
      background-color: #080808;
      z-index: -1;
    }
  }


  .mobile-hide {
    display: none;
    @media ${device.tablet} {
      display: inherit;
    }
  }

  .mobile-hide_flex {
    display: none !important;
    @media ${device.tablet} {
      display: flex !important;
    }
  }

  .tablet-hide_flex {
    display: none !important;
    @media ${device.desktop} {
      display: flex !important;
    }
  }

  .svg-filter {
    height: 0;
    left: -9999em;
    margin: 0;
    padding: 0;
    position: absolute;
    width: 0;
  }
/* 
  .with-duotone {
    filter: url('#duotone-filter');
  } */

  .no-select {
    * {
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
  }

  .no-scroll {
    overflow: hidden;
  }
  
  .primary-hover:hover {
		  color: var(--brand-color) !important;
  }
`;

export default Global;
