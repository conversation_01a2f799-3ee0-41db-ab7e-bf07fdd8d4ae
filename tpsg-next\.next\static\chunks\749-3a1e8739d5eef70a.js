"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[749],{4724:function(n,t,e){e.d(t,{Z:function(){return HorizontalReversePostCard}});var i=e(2729),a=e(5893),o=e(4218);e(1664);var r=e(9521),c=e(1304),l=e(7421),p=e(6368),s=e(4871),m=e(9588),u=e(4440),d=e(3265);e(785);var f=e(1261);function _templateObject(){let n=(0,i._)(["\n  width: 100%;\n  position: relative;\n  .post-image {\n    width: 100%;\n    aspect-ratio: 1/1;\n    position: relative;\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(['\n  list-style: none;\n  position: relative;\n  width: 100%;\n  list-style: none;\n\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n\n  &:after {\n    content: "";\n    display: block;\n    width: 100%;\n    margin: 24px 0px;\n    background-color: #40444444;\n    height: 1px;\n  }\n\n  .post-info {\n    width: 65%;\n    padding-right: 10px;\n  }\n  .post-image-container {\n    width: 30%;\n  }\n  @media screen and (max-width: 320px) {\n    // Little screen only\n    flex-direction: column;\n    .post-info {\n      width: 100%;\n    }\n    .post-image-container {\n      width: 40%;\n    }\n  }\n  @media ',' {\n    flex-direction: row-reverse;\n    &:after {\n      content: "";\n      display: block;\n      width: 100%;\n      margin: 37px 0px 30px 0px;\n\n      background-color: #40444444;\n      height: 1px;\n    }\n    .post-info {\n      width: 75%;\n    }\n    .post-image-container {\n      width: 20%;\n    }\n  }\n']);return _templateObject1=function(){return n},n}function HorizontalReversePostCard(n){let t,e,i,{post:r,options:x}=n,b=!(0,d.a)({mediaQuery:l.U.tablet});if(!r.type)return(0,a.jsx)(a.Fragment,{});let j=(0,s.qt)(r);if((null==x?void 0:x.showDate)&&(e=(e=r.date?(0,o.S$)(r.date):(0,o.S$)(r.published_at)).replace(".","")),null==x?void 0:x.showAuthor){if(console.log("\uD83D\uDD0D DIAGNOSTIC AUTEUR:",{title:r.title,slug:r.slug,author:r.author,authorType:typeof r.author,authorKeys:r.author?Object.keys(r.author):"N/A",allPostKeys:Object.keys(r),showAuthor:null==x?void 0:x.showAuthor}),r.author){var _;console.log("✅ AUTEUR TROUV\xc9:",i=(null===(_=r.author)||void 0===_?void 0:_.fullName)?r.author.fullName:r.author)}else console.log("❌ PAS D'AUTEUR pour:",r.title)}return(null==x?void 0:x.showLead)&&(t=(0,s.mj)(r)),(0,a.jsxs)(h,{children:[(0,a.jsxs)("div",{className:"post-info",children:[(0,a.jsxs)(p.My,{children:[e&&(0,a.jsx)("span",{children:e}),e&&i&&" - ",i&&(0,a.jsx)("span",{children:i})]}),(0,a.jsxs)(f.Z,{link:j,children:[(0,a.jsx)(p.kz,{children:r.title}),!b&&t&&(0,a.jsx)(p.X0,{children:t})]})]}),(0,a.jsx)("div",{className:"post-image-container",children:(0,a.jsx)(f.Z,{link:j,children:(0,a.jsxs)(g,{children:[(0,a.jsx)("div",{className:"post-image",children:(0,a.jsx)(c.Z,{imageData:r.image})}),(null==x?void 0:x.showAnimatedIcon)&&(0,a.jsx)(m.YM,{type:r.type,colors:(0,u.Q)(r.type)})]})})})]})}let g=r.ZP.div.withConfig({componentId:"sc-3214d8e3-0"})(_templateObject()),h=r.ZP.div.withConfig({componentId:"sc-3214d8e3-1"})(_templateObject1(),l.U.tablet)},3500:function(n,t,e){e.d(t,{Z:function(){return SSRPaginate}});var i=e(2729),a=e(5893),o=e(9521),r=e(7481),c=e(1664),l=e.n(c),p=e(7421);function _templateObject(){let n=(0,i._)(["\n  display: inline-block;\n  height: 42px;\n  width: 42px;\n  font-size: 26px;\n  padding-top: 10px;\n  font-family: Stelvio, sans-serif;\n  border-radius: 100px;\n  text-align: center;\n  color: #080808;\n  background-color: inherit;\n  border: 1px solid ",";\n\n  &:hover {\n    background-color: transparent;\n    border: 1px solid black;\n  }\n  \n  @media "," {\n    margin: auto 10px;\n  }\n  @media "," {\n    \n  }\n"]);return _templateObject=function(){return n},n}function PaginationNumber(n){let{value:t,active:e,baseUrl:i}=n;return e?(0,a.jsx)(s,{active:!0,children:t}):(0,a.jsx)(l(),{href:i+t,children:(0,a.jsx)(s,{children:t})})}let s=o.ZP.div.withConfig({componentId:"sc-85464bad-0"})(_templateObject(),n=>n.active?"black":"transparent",p.U.tablet,p.U.desktop);function ssr_paginate_templateObject(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n"]);return ssr_paginate_templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  color: #080808;\n"]);return _templateObject1=function(){return n},n}function SSRPaginate(n){let{nbHits:t,currentPage:e,baseUrl:i,options:o}=n,c=(0,r.Z)(t,e||1,o.postPerPage||10,o.maxPages||5);return(0,a.jsxs)(m,{className:"no-select",children:[c.startPage>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(PaginationNumber,{value:1,active:1===e,baseUrl:i}),(0,a.jsx)(u,{children:"..."})]}),c.pages.map((n,t)=>(0,a.jsx)(PaginationNumber,{active:n===c.currentPage,value:n,baseUrl:i},t)),c.totalPages!==c.endPage&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u,{children:"..."}),(0,a.jsx)(PaginationNumber,{baseUrl:i,active:c.currentPage===c.endPage,value:c.totalPages})]})]})}let m=o.ZP.div.withConfig({componentId:"sc-4412c46d-0"})(ssr_paginate_templateObject()),u=o.ZP.span.withConfig({componentId:"sc-4412c46d-1"})(_templateObject1())},3265:function(n,t,e){e.d(t,{a:function(){return useMediaQuery}});var i=e(7294);let useMediaQuery=n=>{let{width:t,mediaQuery:e}=n,[a,o]=(0,i.useState)(!1),r=(0,i.useCallback)(n=>{n.matches?o(!0):o(!1)},[]);return(0,i.useEffect)(()=>{let n=window.matchMedia(t?"(max-width: ".concat(t,"px)"):e);return n.addEventListener("change",r),n.matches&&o(!0),()=>n.removeEventListener("change",r)},[]),a}},6368:function(n,t,e){e.d(t,{DZ:function(){return f},GN:function(){return s},My:function(){return d},NZ:function(){return p},V1:function(){return c},X0:function(){return u},bP:function(){return l},hQ:function(){return g},kz:function(){return m}});var i=e(2729),a=e(9521),o=e(7421);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n  font-weight: 500;\n  color: #161616;\n  font-size: 48px;\n  margin-top:  calc(var(--spacing-l) - 48px * ",");\n  margin-bottom: calc(var(--spacing-l) - 48px * ",");\n  \n  @media "," {\n    font-size: 72px;\n    margin-top:  calc(var(--spacing-l) - 72px * ",");\n    margin-bottom: calc(var(--spacing-l) - 72px * ",");\n  }\n  @media "," {\n    font-size: 104px;\n    margin-top:  calc(var(--spacing-l) - 104px * ",");\n    margin-bottom: calc(var(--spacing-l) - 104px * ",");\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  box-sizing: content-box;\n  color: #ececec;\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: clamp(24px, 1.125rem + 1vw, 32px);\n  font-weight: 400;\n  max-width: 600px;\n  line-height: 105%;\n  &:before {\n    display: block;\n    margin-bottom: 8px;\n    content: '","';\n    color: ",";\n    font-size: 18px;\n  }\n  @media "," {\n    font-size: 32px;\n    margin-right: 24px;\n  }\n  @media "," {\n    font-weight: 400;\n    margin-right: 48px;\n    font-size: clamp(32px, 3.5vw, 48px);\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(['\n  \n  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);\n  \n  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;\n  font-size: var(--title-size);\n  color: ',";\n  font-weight: 400;\n  margin-top: 0;\n  margin-bottom: calc( var(--title-size) * -",");\n  \n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  font-size: 24px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 48px;\n    margin-bottom: 16px;\n    margin-top: 16px;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  font-size: 20px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media "," {\n    font-size: 30px;\n  }\n  span {\n    position: relative;\n    margin-left: 8px;\n    display: inline-block;\n    margin-bottom: -2px;\n    height: 22px;\n    width: 22px;\n  }\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  font-size: 16px;\n  color: #888888;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding-right: 30px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media screen and (max-width:320px){ // Little screen only\n    padding: 0;\n  }\n  @media "," {\n    font-size:20px;\n  }\n"]);return _templateObject5=function(){return n},n}function _templateObject6(){let n=(0,i._)(['\n  font-size: 14px;\n  font-style: italic;\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  color: #7a7a7a;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media '," {\n    font-size: 17px;\n  }\n"]);return _templateObject6=function(){return n},n}function _templateObject7(){let n=(0,i._)(["\n  font-size: 38px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 64px;\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n  @media "," {\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n"]);return _templateObject7=function(){return n},n}function _templateObject8(){let n=(0,i._)(["\n  color: #161616;\n  letter-spacing: 0.04em;\n  margin-top: 48px;\n  text-transform: uppercase;\n  @media "," {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 22px;\n    font-weight: 500;\n    &:hover {\n      color: var(--brand-color);\n    }\n  }\n"]);return _templateObject8=function(){return n},n}function _templateObject9(){let n=(0,i._)(["\n  font-weight: 400;\n  font-size: 26px;\n  margin: 0;\n  color: #161616;\n  @media "," {\n    font-size: 32px;\n  }\n"]);return _templateObject9=function(){return n},n}let r={topSpace:.096,minBottomSpace:.2788,maxBottomSpace:.4711},c=a.ZP.h1.withConfig({componentId:"sc-a3af5335-0"})(_templateObject(),r.topSpace,r.minBottomSpace,o.U.tablet,r.topSpace,r.minBottomSpace,o.U.desktop,r.topSpace,r.minBottomSpace),l=a.ZP.h2.withConfig({componentId:"sc-a3af5335-1"})(_templateObject1(),n=>n.label,n=>n.color,o.U.tablet,o.U.desktop),p=a.ZP.h2.withConfig({componentId:"sc-a3af5335-2"})(_templateObject2(),n=>n.light?"var(--c-soft-cream)":"var(--soft-dark)",r.maxBottomSpace),s=a.ZP.h2.withConfig({componentId:"sc-a3af5335-3"})(_templateObject3(),o.U.tablet),m=a.ZP.h4.withConfig({componentId:"sc-a3af5335-4"})(_templateObject4(),o.U.tablet),u=a.ZP.p.withConfig({componentId:"sc-a3af5335-5"})(_templateObject5(),o.U.tablet),d=a.ZP.p.withConfig({componentId:"sc-a3af5335-6"})(_templateObject6(),o.U.tablet),f=a.ZP.h1.withConfig({componentId:"sc-a3af5335-7"})(_templateObject7(),o.U.tablet,o.U.desktop),g=a.ZP.p.withConfig({componentId:"sc-a3af5335-8"})(_templateObject8(),o.U.desktop);a.ZP.p.withConfig({componentId:"sc-a3af5335-9"})(_templateObject9(),o.U.tablet)},7481:function(n,t,e){e.d(t,{Z:function(){return paginate}});function paginate(n,t,e,i){let a,o,r=Math.ceil(n/e);if(t<1?t=1:t>r&&(t=r),r<=i)a=1,o=r;else{let n=Math.floor(i/2),e=Math.ceil(i/2)-1;t<=n?(a=1,o=i):t+e>=r?(a=r-i+1,o=r):(a=t-n,o=t+e)}let c=(t-1)*e,l=Array.from(Array(o+1-a).keys()).map(n=>a+n);return{totalItems:n,currentPage:t,pageSize:e,totalPages:r,startPage:a,endPage:o,startIndex:c,endIndex:Math.min(c+e-1,n-1),pages:l}}}}]);