(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[633],{3678:function(n,t,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/blog/[blog]/filtres",function(){return e(1452)}])},9487:function(n,t,e){"use strict";e.d(t,{Z:function(){return BlogMenu}});var i=e(2729),a=e(5893),o=e(785),r=e(1664),l=e.n(r),c=e(9521),p=e(7294),s=e(7421),d=e(1304);function _templateObject(){let n=(0,i._)(["\n  position: fixed;\n  bottom: -70px;\n  left: -70px;\n  height: calc(70vh + 140px);\n  width: calc(100vw + 140px);\n  padding: 48px 70px;\n  background-color: rgba(",");\n  backdrop-filter: blur(",");\n  transform: translate3d(",");\n  border-top-right-radius: 70px;\n  transition: all 450ms cubic-bezier(0.58, 0, 0.29, 0.91);\n  z-index: 1800;\n\n  @media "," {\n    grid-row: 1/6;\n    background-color: transparent;\n    position: sticky;\n    display: block;\n    backdrop-filter: inherit;\n    border-top-right-radius: 0;\n    transform: none;\n    top: 46px;\n    left: 0;\n    height: 100vh;\n    width: 240px;\n    padding: 40px 0 0 0;\n    z-index: 50;\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(['\n  display: flex;\n  flex-direction: row;\n  margin-bottom: 42px;\n  .menu-blogger-picture {\n    position: relative;\n    width: 55px;\n    height: 55px;\n    border-radius: 50px;\n    overflow: hidden;\n  }\n  .menu-blogger-name {\n    margin: 4px 0 0 16px;\n    font-family: "Stelvio", sans-serif;\n    font-size: 24px;\n    line-height: 105%;\n    color: #161616;\n    font-weight: 600;\n  }\n  .menu-blogger-label {\n    margin: 4px 0 0 16px;\n    font-family: "Novela", serif;\n    font-style: italic;\n    font-size: 20px;\n    color: #888888;\n    font-weight: 400;\n  }\n  \n  @media ',' {\n    flex-direction: column;\n    width: 50%;\n    .menu-blogger-name {\n      margin: 16px 0 0 0;\n      font-family: "Stelvio", sans-serif;\n      font-size: 32px;\n      line-height: 105%;\n      color: #161616;\n      font-weight: 600;\n    }\n    .menu-blogger-label {\n      margin: 4px 0 0 0;\n    }\n  }\n']);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  \n  padding-left: var(--mobile-gap);\n  padding-right: var(--mobile-gap);\n  \n  @media "," {\n    padding-left: var(--tablet-gap);\n    padding-right: var(tablet-gap);\n  }\n  @media "," {\n    transform: none;\n    padding-left: 0;\n    padding-right: 0;\n    top: 0;\n    left: 0;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(['\n  ul {\n    display: table;\n    padding: 0;\n  }\n  li {\n    font-weight: 400;\n    position: relative;\n    font-family: "Stelvio", sans-serif;\n    font-size: 20px;\n    margin-top: 8px;\n    list-style: none;\n  }\n  //transform: translate3d(',");\n  transition: all 800ms cubic-bezier(0.58, 0, 0.29, 0.91);\n\n  @media ",' {\n    a {\n      position: relative;\n      height: 32px;\n      display: table-row;\n      line-height: 24px;\n      border-radius: 32px;\n      z-index: 900;\n      color: black;\n\n      &:hover {\n        color: white;\n\n        &:after {\n          content: "";\n          background-color: black;\n          position: absolute;\n          height: 100%;\n          width: calc(100% + 24px);\n          left: -12px;\n          top: -7px;\n          border-radius: 32px;\n          z-index: -1;\n        }\n      }\n    }\n  }\n']);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  display: flex;\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  align-items: center;\n  justify-content: center;\n  height: 70px;\n  width: 70px;\n  color: black;\n  border-radius: 70px;\n  p {\n    padding-top: 12px;\n    margin: 16px 16px 0 0;\n    font-size: 32px;\n    transform-origin: center;\n    transform: rotate(",");\n    transition: all 450ms cubic-bezier(0.58, -0.42, 0.29, 0.91);\n  }\n  z-index: 2000;\n  @media "," {\n    display: none;\n  }\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  font-weight: 500;\n  letter-spacing: 0.08em;\n  text-transform: uppercase;\n  color: #888888;\n"]);return _templateObject5=function(){return n},n}function RenderGroup(n){let{group:t,blogPath:e}=n;return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b,{children:t.name}),(0,a.jsx)("ul",{children:t.items.map((n,t)=>(0,a.jsx)(RenderLink,{link:n,blogPath:e},t))})]})}function RenderLink(n){let{link:t,blogPath:e}=n;switch(t.type){case"external":return(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:t.value,target:"_blank",rel:"noreferrer noopener",children:t.label})});case"internal":return(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:t.value,children:t.label})});case"filter":return(0,a.jsx)("li",{children:(0,a.jsx)(l(),{href:{pathname:"/blog/".concat(e,"/filtres"),query:t.value},children:t.label})});default:return(0,a.jsx)(a.Fragment,{})}}function BlogMenu(n){let{data:t}=n,e=(0,o.DG)(t.menu),i=t.slug,[r,c]=(0,p.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m,{isOpen:r,className:"blog-menu",children:(0,a.jsxs)(f,{isOpen:r,children:[(0,a.jsxs)(u,{children:[(0,a.jsx)("div",{className:"menu-blogger-picture",children:(0,a.jsx)(d.Z,{imageData:t.blogger.picture})}),(0,a.jsxs)("div",{children:[(0,a.jsx)(l(),{href:"/blog/".concat(i),children:(0,a.jsx)("p",{className:"menu-blogger-name primary-hover",children:t.blogger.fullName})}),(0,a.jsx)("p",{className:"menu-blogger-label",children:"Blog"})]})]}),(0,a.jsxs)(g,{children:[(null==e?void 0:e.groups)&&e.groups.map((n,t)=>(0,a.jsx)(RenderGroup,{group:n,blogPath:i},t)),(null==e?void 0:e.singles.length)>0&&e.singles.map((n,t)=>(0,a.jsx)(RenderLink,{link:n,blogPath:i},t))]})]})}),(0,a.jsx)(h,{isOpen:r,onClick:()=>c(!r),children:(0,a.jsx)("p",{children:"→"})})]})}let m=c.ZP.div.withConfig({componentId:"sc-f4ce5f7f-0"})(_templateObject(),n=>n.isOpen?"236, 236, 236, 0.8":"236, 236, 236, 0.5",n=>n.isOpen?"25px":"15px",n=>n.isOpen?"0,0,0":"-100vw,70vh,0",s.U.desktop),u=c.ZP.div.withConfig({componentId:"sc-f4ce5f7f-1"})(_templateObject1(),s.U.desktop),f=c.ZP.div.withConfig({componentId:"sc-f4ce5f7f-2"})(_templateObject2(),s.U.tablet,s.U.desktop),g=c.ZP.div.withConfig({componentId:"sc-f4ce5f7f-3"})(_templateObject3(),n=>n.isOpen?"0,0,0":"0, -100px, 0",s.U.desktop),h=c.ZP.div.withConfig({componentId:"sc-f4ce5f7f-4"})(_templateObject4(),n=>n.isOpen?"-225deg":"-45deg",s.U.desktop),b=c.ZP.label.withConfig({componentId:"sc-f4ce5f7f-5"})(_templateObject5())},4724:function(n,t,e){"use strict";e.d(t,{Z:function(){return HorizontalReversePostCard}});var i=e(2729),a=e(5893),o=e(4218);e(1664);var r=e(9521),l=e(1304),c=e(7421),p=e(6368),s=e(4871),d=e(9588),m=e(4440),u=e(3265);e(785);var f=e(1261);function _templateObject(){let n=(0,i._)(["\n  width: 100%;\n  position: relative;\n  .post-image {\n    width: 100%;\n    aspect-ratio: 1/1;\n    position: relative;\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(['\n  list-style: none;\n  position: relative;\n  width: 100%;\n  list-style: none;\n\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n\n  &:after {\n    content: "";\n    display: block;\n    width: 100%;\n    margin: 24px 0px;\n    background-color: #40444444;\n    height: 1px;\n  }\n\n  .post-info {\n    width: 65%;\n    padding-right: 10px;\n  }\n  .post-image-container {\n    width: 30%;\n  }\n  @media screen and (max-width: 320px) {\n    /* Little screen only */\n    flex-direction: column;\n    .post-info {\n      width: 100%;\n    }\n    .post-image-container {\n      width: 40%;\n    }\n  }\n  @media ',' {\n    flex-direction: row-reverse;\n    &:after {\n      content: "";\n      display: block;\n      width: 100%;\n      margin: 37px 0px 30px 0px;\n\n      background-color: #40444444;\n      height: 1px;\n    }\n    .post-info {\n      width: 75%;\n    }\n    .post-image-container {\n      width: 20%;\n    }\n  }\n']);return _templateObject1=function(){return n},n}function HorizontalReversePostCard(n){let t,e,i,{post:r,options:b}=n,x=!(0,u.a)({mediaQuery:c.U.tablet});if(!r.type)return(0,a.jsx)(a.Fragment,{});let j=(0,s.qt)(r);if((null==b?void 0:b.showDate)&&(e=(e=r.date?(0,o.S$)(r.date):(0,o.S$)(r.published_at)).replace(".","")),(null==b?void 0:b.showAuthor)&&r.author){var v;i=(null===(v=r.author)||void 0===v?void 0:v.fullName)?r.author.fullName:r.author}return(null==b?void 0:b.showLead)&&(t=(0,s.mj)(r)),(0,a.jsxs)(h,{children:[(0,a.jsxs)("div",{className:"post-info",children:[(0,a.jsxs)(p.My,{children:[e&&(0,a.jsx)("span",{children:e}),e&&i&&" - ",i&&(0,a.jsx)("span",{children:i})]}),(0,a.jsxs)(f.Z,{link:j,children:[(0,a.jsx)(p.kz,{children:r.title}),!x&&t&&(0,a.jsx)(p.X0,{children:t})]})]}),(0,a.jsx)("div",{className:"post-image-container",children:(0,a.jsx)(f.Z,{link:j,children:(0,a.jsxs)(g,{children:[(0,a.jsx)("div",{className:"post-image",children:(0,a.jsx)(l.Z,{imageData:r.image})}),(null==b?void 0:b.showAnimatedIcon)&&(0,a.jsx)(d.YM,{type:r.type,colors:(0,m.Q)(r.type)})]})})})]})}let g=r.ZP.div.withConfig({componentId:"sc-355f4397-0"})(_templateObject()),h=r.ZP.div.withConfig({componentId:"sc-355f4397-1"})(_templateObject1(),c.U.tablet)},3500:function(n,t,e){"use strict";e.d(t,{Z:function(){return SSRPaginate}});var i=e(2729),a=e(5893),o=e(9521),r=e(7481),l=e(1664),c=e.n(l),p=e(7421);function _templateObject(){let n=(0,i._)(["\n  display: inline-block;\n  height: 42px;\n  width: 42px;\n  font-size: 26px;\n  padding-top: 10px;\n  font-family: Stelvio, sans-serif;\n  border-radius: 100px;\n  text-align: center;\n  color: #080808;\n  background-color: inherit;\n  border: 1px solid ",";\n\n  &:hover {\n    background-color: transparent;\n    border: 1px solid black;\n  }\n  \n  @media "," {\n    margin: auto 10px;\n  }\n  @media "," {\n    \n  }\n"]);return _templateObject=function(){return n},n}function PaginationNumber(n){let{value:t,active:e,baseUrl:i}=n;return e?(0,a.jsx)(s,{active:!0,children:t}):(0,a.jsx)(c(),{href:i+t,children:(0,a.jsx)(s,{children:t})})}let s=o.ZP.div.withConfig({componentId:"sc-85464bad-0"})(_templateObject(),n=>n.active?"black":"transparent",p.U.tablet,p.U.desktop);function ssr_paginate_templateObject(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n"]);return ssr_paginate_templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  color: #080808;\n"]);return _templateObject1=function(){return n},n}function SSRPaginate(n){let{nbHits:t,currentPage:e,baseUrl:i,options:o}=n,l=(0,r.Z)(t,e||1,o.postPerPage||10,o.maxPages||5);return(0,a.jsxs)(d,{className:"no-select",children:[l.startPage>1&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(PaginationNumber,{value:1,active:1===e,baseUrl:i}),(0,a.jsx)(m,{children:"..."})]}),l.pages.map((n,t)=>(0,a.jsx)(PaginationNumber,{active:n===l.currentPage,value:n,baseUrl:i},t)),l.totalPages!==l.endPage&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m,{children:"..."}),(0,a.jsx)(PaginationNumber,{baseUrl:i,active:l.currentPage===l.endPage,value:l.totalPages})]})]})}let d=o.ZP.div.withConfig({componentId:"sc-4412c46d-0"})(ssr_paginate_templateObject()),m=o.ZP.span.withConfig({componentId:"sc-4412c46d-1"})(_templateObject1())},3265:function(n,t,e){"use strict";e.d(t,{a:function(){return useMediaQuery}});var i=e(7294);let useMediaQuery=n=>{let{width:t,mediaQuery:e}=n,[a,o]=(0,i.useState)(!1),r=(0,i.useCallback)(n=>{n.matches?o(!0):o(!1)},[]);return(0,i.useEffect)(()=>{let n=window.matchMedia(t?"(max-width: ".concat(t,"px)"):e);return n.addEventListener("change",r),n.matches&&o(!0),()=>n.removeEventListener("change",r)},[]),a}},1452:function(n,t,e){"use strict";e.r(t),e.d(t,{__N_SSP:function(){return m},default:function(){return PageFiltres}});var i=e(2729),a=e(5893),o=e(1163),r=e(9487),l=e(4724),c=e(9521),p=e(3500),s=e(7421),d=e(7294);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n  background-color: var(--soft-white);\n  @media "," {\n    .blog-menu {\n      \n    }\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  position: relative;\n  display: flex;\n  \n  @media "," {\n    margin-left: var(--border-space);\n    margin-right: var(--border-space);\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  width: 100%;\n  margin-bottom: 64px;\n  padding-top: 40px;\n  padding-left: var(--mobile-gap);\n  padding-right: var(--mobile-gap);\n  .list-container {\n    padding: 0;\n    width: 100%;\n  }\n  .post-card-li {\n    list-style: none;\n    padding-right: 0;\n  }\n  @media "," {\n    padding-left: var(--tablet-gap);\n    padding-right: 0;\n  }\n  @media "," {\n    padding-left: var(--desktop-gap);\n    margin-bottom: 164px;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  position: relative;\n  height: 526px;\n"]);return _templateObject3=function(){return n},n}var m=!0;function PageFiltres(n){var t;let e;let{blog:i,fallback:c}=n,s=null==c?void 0:c.posts,m=(0,o.useRouter)(),{query:h}=m;(0,d.useEffect)(()=>{let n={};h.topic&&(n.topic=h.topic),1>+h.page?(n.page=1,m.replace({pathname:"/blog/".concat(i.slug,"/filtres"),query:n},void 0,{scroll:!1})):+h.page>(null==s?void 0:s.totalPages)&&(n.page=(null==s?void 0:s.totalPages)>0?s.totalPages:1,m.replace({pathname:"/blog/".concat(i.slug,"/filtres"),query:n},void 0,{scroll:!1}))},[i.slug,m.query.page]);let b={showLead:!0,showDate:!0,showAuthor:!0},x=h.topic;return(0,a.jsx)(u,{children:(0,a.jsxs)(f,{children:[(0,a.jsx)(r.Z,{data:i}),(0,a.jsxs)(g,{children:[(0,a.jsx)("h1",{children:x}),(0,a.jsx)("ul",{className:"list-container",children:null==s?void 0:null===(t=s.hits)||void 0===t?void 0:t.map((n,t)=>(0,a.jsx)("li",{className:"post-card-li",children:(0,a.jsx)(l.Z,{post:n,options:b})},"post-".concat(t)))}),(0,a.jsx)(p.Z,{nbHits:null==s?void 0:s.totalHits,baseUrl:(e=h.topic?"topic":h.type?"type":h.tag?"tag":null,"/blog/".concat(i.slug,"/filtres?").concat(e?e+"="+h[e]+"&":"","page=")),currentPage:null==s?void 0:s.page,options:{postPerPage:15}})]})]})})}let u=c.ZP.div.withConfig({componentId:"sc-615abf4e-0"})(_templateObject(),s.U.desktop),f=c.ZP.div.withConfig({componentId:"sc-615abf4e-1"})(_templateObject1(),s.U.desktop),g=c.ZP.div.withConfig({componentId:"sc-615abf4e-2"})(_templateObject2(),s.U.tablet,s.U.desktop);c.ZP.div.withConfig({componentId:"sc-615abf4e-3"})(_templateObject3())},6368:function(n,t,e){"use strict";e.d(t,{DZ:function(){return f},GN:function(){return s},My:function(){return u},NZ:function(){return p},V1:function(){return l},X0:function(){return m},bP:function(){return c},hQ:function(){return g},kz:function(){return d}});var i=e(2729),a=e(9521),o=e(7421);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n  font-weight: 500;\n  color: #161616;\n  font-size: 48px;\n  margin-top:  calc(var(--spacing-l) - 48px * ",");\n  margin-bottom: calc(var(--spacing-l) - 48px * ",");\n  \n  @media "," {\n    font-size: 72px;\n    margin-top:  calc(var(--spacing-l) - 72px * ",");\n    margin-bottom: calc(var(--spacing-l) - 72px * ",");\n  }\n  @media "," {\n    font-size: 104px;\n    margin-top:  calc(var(--spacing-l) - 104px * ",");\n    margin-bottom: calc(var(--spacing-l) - 104px * ",");\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  box-sizing: content-box;\n  color: #ececec;\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: clamp(24px, 1.125rem + 1vw, 32px);\n  font-weight: 400;\n  max-width: 600px;\n  line-height: 105%;\n  &:before {\n    display: block;\n    margin-bottom: 8px;\n    content: '","';\n    color: ",";\n    font-size: 18px;\n  }\n  @media "," {\n    font-size: 32px;\n    margin-right: 24px;\n  }\n  @media "," {\n    font-weight: 400;\n    margin-right: 48px;\n    font-size: clamp(32px, 3.5vw, 48px);\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(['\n  \n  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);\n  \n  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;\n  font-size: var(--title-size);\n  color: ',";\n  font-weight: 400;\n  margin-top: 0;\n  margin-bottom: calc( var(--title-size) * -",");\n  \n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  font-size: 24px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 48px;\n    margin-bottom: 16px;\n    margin-top: 16px;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  font-size: 20px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media "," {\n    font-size: 30px;\n  }\n  span {\n    position: relative;\n    margin-left: 8px;\n    display: inline-block;\n    margin-bottom: -2px;\n    height: 22px;\n    width: 22px;\n  }\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  font-size: 16px;\n  color: #888888;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding-right: 30px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media screen and (max-width:320px){ // Little screen only\n    padding: 0;\n  }\n  @media "," {\n    font-size:20px;\n  }\n"]);return _templateObject5=function(){return n},n}function _templateObject6(){let n=(0,i._)(['\n  font-size: 14px;\n  font-style: italic;\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  color: #7a7a7a;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media '," {\n    font-size: 17px;\n  }\n"]);return _templateObject6=function(){return n},n}function _templateObject7(){let n=(0,i._)(["\n  font-size: 38px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 64px;\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n  @media "," {\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n"]);return _templateObject7=function(){return n},n}function _templateObject8(){let n=(0,i._)(["\n  color: #161616;\n  letter-spacing: 0.04em;\n  margin-top: 48px;\n  text-transform: uppercase;\n  @media "," {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 22px;\n    font-weight: 500;\n    &:hover {\n      color: var(--brand-color);\n    }\n  }\n"]);return _templateObject8=function(){return n},n}function _templateObject9(){let n=(0,i._)(["\n  font-weight: 400;\n  font-size: 26px;\n  margin: 0;\n  color: #161616;\n  @media "," {\n    font-size: 32px;\n  }\n"]);return _templateObject9=function(){return n},n}let r={topSpace:.096,minBottomSpace:.2788,maxBottomSpace:.4711},l=a.ZP.h1.withConfig({componentId:"sc-a3af5335-0"})(_templateObject(),r.topSpace,r.minBottomSpace,o.U.tablet,r.topSpace,r.minBottomSpace,o.U.desktop,r.topSpace,r.minBottomSpace),c=a.ZP.h2.withConfig({componentId:"sc-a3af5335-1"})(_templateObject1(),n=>n.label,n=>n.color,o.U.tablet,o.U.desktop),p=a.ZP.h2.withConfig({componentId:"sc-a3af5335-2"})(_templateObject2(),n=>n.light?"var(--c-soft-cream)":"var(--soft-dark)",r.maxBottomSpace),s=a.ZP.h2.withConfig({componentId:"sc-a3af5335-3"})(_templateObject3(),o.U.tablet),d=a.ZP.h4.withConfig({componentId:"sc-a3af5335-4"})(_templateObject4(),o.U.tablet),m=a.ZP.p.withConfig({componentId:"sc-a3af5335-5"})(_templateObject5(),o.U.tablet),u=a.ZP.p.withConfig({componentId:"sc-a3af5335-6"})(_templateObject6(),o.U.tablet),f=a.ZP.h1.withConfig({componentId:"sc-a3af5335-7"})(_templateObject7(),o.U.tablet,o.U.desktop),g=a.ZP.p.withConfig({componentId:"sc-a3af5335-8"})(_templateObject8(),o.U.desktop);a.ZP.p.withConfig({componentId:"sc-a3af5335-9"})(_templateObject9(),o.U.tablet)},7481:function(n,t,e){"use strict";function paginate(n,t,e,i){let a,o,r=Math.ceil(n/e);if(t<1?t=1:t>r&&(t=r),r<=i)a=1,o=r;else{let n=Math.floor(i/2),e=Math.ceil(i/2)-1;t<=n?(a=1,o=i):t+e>=r?(a=r-i+1,o=r):(a=t-n,o=t+e)}let l=(t-1)*e,c=Array.from(Array(o+1-a).keys()).map(n=>a+n);return{totalItems:n,currentPage:t,pageSize:e,totalPages:r,startPage:a,endPage:o,startIndex:l,endIndex:Math.min(l+e-1,n-1),pages:c}}e.d(t,{Z:function(){return paginate}})}},function(n){n.O(0,[755,291,774,888,179],function(){return n(n.s=3678)}),_N_E=n.O()}]);