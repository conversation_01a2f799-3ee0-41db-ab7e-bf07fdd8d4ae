/**
 * Script pour restaurer le fichier meili-client.js original
 * 
 * Ce script restaure le fichier meili-client.js à partir de la sauvegarde meili-client.js.bak
 * 
 * Pour exécuter ce script:
 * 1. Assurez-vous d'être dans le répertoire racine du projet
 * 2. Exécutez: node restore-original-meili-client.js
 */

const fs = require('fs');
const path = require('path');

// Chemins vers les fichiers
const meiliClientPath = path.join('tpsg-next', 'api', 'meili-client.js');
const meiliClientBackupPath = path.join('tpsg-next', 'api', 'meili-client.js.bak');

// Vérifier si le fichier de sauvegarde existe
if (!fs.existsSync(meiliClientBackupPath)) {
  console.error(`Le fichier de sauvegarde ${meiliClientBackupPath} n'existe pas.`);
  process.exit(1);
}

// Lire le contenu du fichier de sauvegarde
fs.readFile(meiliClientBackupPath, 'utf8', (err, data) => {
  if (err) {
    console.error(`Erreur lors de la lecture du fichier ${meiliClientBackupPath}:`, err);
    return;
  }

  // Écrire le contenu dans le fichier original
  fs.writeFile(meiliClientPath, data, 'utf8', (err) => {
    if (err) {
      console.error(`Erreur lors de l'écriture du fichier ${meiliClientPath}:`, err);
      return;
    }
    console.log(`Le fichier ${meiliClientPath} a été restauré avec succès.`);
  });
});
