/**
 * Script pour réindexer manuellement les données de Strapi vers Meilisearch
 *
 * Pour exécuter ce script :
 * 1. Assurez-vous que Strapi et Meilisearch sont en cours d'exécution
 * 2. Exécutez la commande : node reindex-manual.js
 */

const fetch = require('node-fetch');

// Configuration
const STRAPI_URL = 'http://127.0.0.1:1337';
const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

// Fonction pour récupérer les posts depuis Strapi
async function fetchPostsFromStrapi() {
  try {
    console.log('Récupération des posts depuis Strapi...');
    const response = await fetch(`${STRAPI_URL}/posts?_limit=-1`);

    if (!response.ok) {
      throw new Error(`Erreur lors de la récupération des posts: ${response.status} ${response.statusText}`);
    }

    const posts = await response.json();
    console.log(`${posts.length} posts récupérés depuis Strapi`);
    return posts;
  } catch (error) {
    console.error('Erreur lors de la récupération des posts:', error);
    throw error;
  }
}

// Fonction pour préparer les posts au format attendu par Meilisearch
function preparePosts(posts) {
  console.log('Préparation des posts pour Meilisearch...');

  return posts.map(post => {
    // Extraction du lead s'il existe
    let lead = null;
    if (post.modules && Array.isArray(post.modules)) {
      const leadModule = post.modules.find(module => module.__component === 'module.lead');
      if (leadModule && leadModule.content) {
        lead = leadModule.content;
        // Supprimer le markdown et HTML si nécessaire
        lead = lead.replace(/\*\*(.*?)\*\*/g, '$1'); // Supprimer les **bold**
        lead = lead.replace(/\*(.*?)\*/g, '$1');     // Supprimer les *italic*
        lead = lead.replace(/<[^>]*>/g, '');         // Supprimer les balises HTML
      }
    }

    // Déterminer la route en fonction du type
    let route = '/';
    switch(post.type) {
      case 'podcast':
        if (post.modules) {
          const podcastModule = post.modules.find(module => module.__component === 'module.podcast');
          if (podcastModule && podcastModule.podcast && podcastModule.podcast.slug) {
            route = `/podcasts/${podcastModule.podcast.slug}/${post.slug}`;
          }
        }
        break;
      case 'parcours':
        route = `/parcours-emails/${post.slug}`;
        break;
      case 'webinaire':
        route = `/webinaires/${post.slug}`;
        break;
      case 'formation':
        route = `/article/${post.slug}`;
        if (post.modules) {
          const formationModule = post.modules.find(module => module.__component === 'module.formation');
          if (formationModule && formationModule.link) {
            route = formationModule.link;
          }
        }
        break;
      default:
        route = `/article/${post.slug}`;
    }

    // Nettoyer le corps du texte
    let body = post.body || '';
    body = body.replace(/\*\*(.*?)\*\*/g, '$1'); // Supprimer les **bold**
    body = body.replace(/\*(.*?)\*/g, '$1');     // Supprimer les *italic*
    body = body.replace(/<[^>]*>/g, '');         // Supprimer les balises HTML

    return {
      id: `post-${post.id}`,
      title: post.title,
      slug: post.slug,
      author: post.author ? post.author.fullName : null,
      tags: post.tags ? post.tags.map(tag => tag.name) : [],
      topics: post.topics ? post.topics.map(topic => topic.name) : [],
      type: post.type || null,
      lead: lead,
      body: body,
      date: post.published_at,
      blog: post.blog ? post.blog.slug : null,
      image: post.image,
      route: route,
      cs: post.isCornerStone || false
    };
  });
}

// Fonction pour configurer l'index Meilisearch
async function configureIndex() {
  try {
    console.log('Configuration de l\'index Meilisearch...');

    // Vérifier si l'index existe
    const indexesResponse = await fetch(`${MEILI_URL}/indexes`);
    const indexes = await indexesResponse.json();

    const indexExists = indexes.results && indexes.results.some(index => index.uid === INDEX_NAME);

    // Si l'index n'existe pas, le créer
    if (!indexExists) {
      console.log(`Création de l'index "${INDEX_NAME}"...`);
      const createResponse = await fetch(`${MEILI_URL}/indexes`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          uid: INDEX_NAME
        })
      });

      if (!createResponse.ok) {
        throw new Error(`Erreur lors de la création de l'index: ${createResponse.status} ${createResponse.statusText}`);
      }

      console.log(`Index "${INDEX_NAME}" créé avec succès`);
    }

    // Configurer les attributs filtrables
    console.log('Configuration des attributs filtrables...');
    const filterableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify([
        'title',
        'topics',
        'tags',
        'author',
        'type',
        'blog',
        'cs'
      ])
    });

    if (!filterableResponse.ok) {
      throw new Error(`Erreur lors de la configuration des attributs filtrables: ${filterableResponse.status} ${filterableResponse.statusText}`);
    }

    // Configurer les attributs triables
    console.log('Configuration des attributs triables...');
    const sortableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(['date'])
    });

    if (!sortableResponse.ok) {
      throw new Error(`Erreur lors de la configuration des attributs triables: ${sortableResponse.status} ${sortableResponse.statusText}`);
    }

    // Configurer les attributs de recherche
    console.log('Configuration des attributs de recherche...');
    const searchableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify([
        'title',
        'body',
        'lead'
      ])
    });

    if (!searchableResponse.ok) {
      throw new Error(`Erreur lors de la configuration des attributs de recherche: ${searchableResponse.status} ${searchableResponse.statusText}`);
    }

    console.log('Configuration de l\'index terminée avec succès');
  } catch (error) {
    console.error('Erreur lors de la configuration de l\'index:', error);
    throw error;
  }
}

// Fonction pour indexer les documents dans Meilisearch
async function indexDocuments(documents) {
  try {
    console.log(`Indexation de ${documents.length} documents dans Meilisearch...`);

    const response = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/documents`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(documents)
    });

    if (!response.ok) {
      throw new Error(`Erreur lors de l'indexation des documents: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`Indexation réussie. ID de la tâche: ${result.taskUid}`);
    return result;
  } catch (error) {
    console.error('Erreur lors de l\'indexation des documents:', error);
    throw error;
  }
}

// Fonction principale
async function main() {
  try {
    // Configurer l'index
    await configureIndex();

    // Récupérer les posts depuis Strapi
    const posts = await fetchPostsFromStrapi();

    // Préparer les posts pour Meilisearch
    const documents = preparePosts(posts);

    // Indexer les documents dans Meilisearch
    await indexDocuments(documents);

    console.log('Réindexation terminée avec succès !');
  } catch (error) {
    console.error('Erreur lors de la réindexation:', error);
    process.exit(1);
  }
}

// Exécuter le script
main();
