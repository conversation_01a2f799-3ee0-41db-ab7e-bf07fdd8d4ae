"use strict";(()=>{var e={};e.id=8826,e.ids=[8826,2888],e.modules={9471:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>S,default:()=>c,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>d,reportWebVitals:()=>x,routeModule:()=>h,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>q,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>_});var o=r(7093),s=r(5244),i=r(1323),n=r(779),u=r(4033),l=r(305),p=e([l]);l=(p.then?(await p)():p)[0];let c=(0,i.l)(l,"default"),d=(0,i.l)(l,"getStaticProps"),m=(0,i.l)(l,"getStaticPaths"),g=(0,i.l)(l,"getServerSideProps"),S=(0,i.l)(l,"config"),x=(0,i.l)(l,"reportWebVitals"),_=(0,i.l)(l,"unstable_getStaticProps"),P=(0,i.l)(l,"unstable_getStaticPaths"),b=(0,i.l)(l,"unstable_getStaticParams"),f=(0,i.l)(l,"unstable_getServerProps"),q=(0,i.l)(l,"unstable_getServerSideProps"),h=new o.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/formations/[formation]",pathname:"/formations/[formation]",bundlePath:"",filename:""},components:{App:u.default,Document:n.default},userland:l});a()}catch(e){a(e)}})},305:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>Formation,getServerSideProps:()=>getServerSideProps});var o=r(997),s=r(8657),i=r(9114),n=r(1385);r(635);var u=e([s]);s=(u.then?(await u)():u)[0];let l=i.gql`
    query GetFormation($slug: String!){
        posts(where: {slug: $slug}){
            title
            slug
            published_at
            body
            author {
                fullName
            }
            modules {
                ... on ComponentModuleFormation{
                    __typename
                    speakers {
                        fullName
                    }
                    link
                }
                ... on ComponentModuleLead{
                    __typename
                    content
                }
            }
        }
    }
`;function Formation({post:e}){return o.jsx(o.Fragment,{})}async function getServerSideProps({params:e}){let t=null;try{t=await n.Z.query({query:l,variables:{slug:e.parcours}}).then(e=>e.data.posts[0])}catch(e){return{notFound:!0}}return{props:{post:t}}}a()}catch(e){a(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,9755,4033,779,7113,1077,7620,8672,2832,8657],()=>__webpack_exec__(9471));module.exports=r})();