{"kind": "collectionType", "collectionName": "blogs", "info": {"name": "blog", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false}, "pluginOptions": {}, "attributes": {"blogger": {"unique": true, "model": "author"}, "slug": {"type": "uid"}, "featured": {"type": "component", "repeatable": true, "component": "module.featured"}, "menu": {"type": "component", "repeatable": true, "component": "navigation.nav-url"}, "newsletter": {"type": "text"}, "author_user": {"plugin": "admin", "model": "user"}}}