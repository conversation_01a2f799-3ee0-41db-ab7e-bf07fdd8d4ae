"use strict";exports.id=7482,exports.ids=[7482],exports.modules={8102:(e,t,i)=>{i.d(t,{Z:()=>n});var r=i(5152),o=i.n(r);let a=o()(()=>i.e(7992).then(i.bind(i,7992)),{loadableGenerated:{modules:["..\\components\\shared\\ConvertkitForm\\DynamicForm.js -> ./CKForm"]},ssr:!1}),n=a},3123:(e,t,i)=>{i.d(t,{Z:()=>Related});var r=i(997),o=i(7518),a=i.n(o);i(2558),i(635);var n=i(1664),s=i.n(n),l=i(7467),c=i(1077),d=i(5675),p=i.n(d);function Speakers({speakers:e,options:t}){return(0,r.jsxs)(m,{children:[r.jsx("div",{className:"elt-speakers-pics",children:r.jsx(RenderPictures,{pics:e.pictures})}),(0,r.jsxs)("div",{className:"elt-speakers-text",children:[r.jsx("p",{className:"elt-speakers-label",children:`Orateur${e.pictures.length?"s":""}`}),r.jsx("p",{className:"elt-speakers-names",children:e.names})]})]})}function RenderPictures({pics:e}){return e.map((e,t)=>r.jsx(h,{children:r.jsx(p(),{src:e,sizes:"34px",fill:!0,alt:""})},t))}let m=a().div.withConfig({componentId:"sc-32c56f82-0"})`
  position: relative;
  display: flex;
  .elt-speakers-pics {
    display: flex;
    flex-direction: row;
  }
  .elt-speakers-text {
    color: var(--c-soft-cream);
    font-family: Switzer, "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 0 19px;
  }
  .elt-speakers-label {
    margin: -4px 0 4px 0;
    opacity: 0.72;
  }
  .elt-speakers-names {
    margin: 0;
  }
`,h=a().div.withConfig({componentId:"sc-32c56f82-1"})`
  position: relative;
  width: 34px;
  height: 34px;
  background-color: darkseagreen;
  border: 1px solid white;
  margin-right: -5px;
  border-radius: 32px;
  overflow: hidden;
`;var x=i(4130);function Author({date:e,author:t,options:i={size:"m",theme:"light",direction:"row",showName:!0,showDate:!0,showPicture:!0}}){let o=t?.fullName,a=(0,x.k)(t?.picture),n=i.showDate&&i.showName&&"row"===i.direction?r.jsx("span",{className:"elt-author-dot",children:"•"}):null;return(0,r.jsxs)(g,{size:i.size,theme:i.theme,direction:i.direction,children:[i.showPicture&&r.jsx("div",{className:"elt-author-picture",children:a&&r.jsx(p(),{src:a,sizes:"28px",fill:!0,style:{objectFit:"cover"},alt:""})}),(0,r.jsxs)("div",{className:"elt-author-text",children:[i.showName&&r.jsx("span",{className:"elt-author-name",children:o}),n,i.showDate&&r.jsx("span",{className:"elt-author-date",children:e})]})]})}let g=a().div.withConfig({componentId:"sc-c290df6-0"})`
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;

  .elt-author-picture {
    position: relative;
    aspect-ratio: 1/1;
    height: ${e=>"row"===e.direction?"28px":"34px"};
    
    overflow: hidden;
    border-radius: 34px;
    background-color: var(--c-soft-cream);
  }
  
  .elt-author-text {
    position: relative;
    display: flex;
    flex-direction: ${e=>e.direction};
    align-items: ${e=>"row"===e.direction?"center":"left"};

    color: ${e=>"dark"===e.theme?"var(--c-soft-cream)":"#161616"};
    
    font-family: Switzer, sans-serif;
    font-size: 14px;
    font-weight: 400;
    
    span {
      margin-right: 8px;
    }
    
    .elt-author-date {
      color: ${e=>"row"===e.direction?"inherit":"#848484"};
      text-transform: capitalize;
    }
  }
`;var f=i(2570);function LargeRelatedCard({post:e}){let t=(0,c.mj)(e),i=(0,c.pL)(e),o=(0,c.qt)(e);return r.jsx(f.Z,{link:o,children:(0,r.jsxs)(b,{theme:"dark",children:[(0,r.jsxs)("div",{className:"lrc-content",children:[(0,r.jsxs)("div",{className:"lrc-text",children:[r.jsx("p",{className:"lrc-type",children:e.type}),r.jsx("p",{className:"lrc-title",children:e.title}),r.jsx("p",{className:"lrc-lead",children:t})]}),r.jsx(Speakers,{speakers:i})]}),r.jsx("div",{className:"lrc-image",children:r.jsx(p(),{src:(0,x.k)(e.image),sizes:"30vw",fill:!0,style:u,alt:""})})]})})}let u={objectFit:"cover"},b=a().div.withConfig({componentId:"sc-887c087d-0"})`
  position: relative;
  display: flex;
  flex-direction: column-reverse;
  width: 100%;

  .lrc-image {
    position: relative;
    min-width: 50%;
    aspect-ratio: 16/9;
    background-color: #F9F1E6;
    background-image: url(/images/tpsg-logo.svg);
    background-repeat: no-repeat;
    background-position: center;
  }
  
  border: 1px solid ${e=>"dark"===e.theme?"rgba(250,247,243,0.2)":"rgba(22,22,22,0.2)"};
  
  .lrc-content {
    display: flex;
    height: 360px;
    flex-direction: column;
    justify-content: space-between;
    padding: clamp(1.5rem, 0.112rem + 2.98vw, 2.5rem);
  }
  .lrc-text {
    color: var(--c-soft-cream);
  }
  .lrc-type {
    margin-top: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 14px;
  }
  .lrc-title {
    position: relative;
    color: ${e=>"dark"===e.theme?"#FFFFFF":"#161616"};
    font-family: Stelvio, sans-serif;
    font-weight: 400;
    font-size: clamp(1.75rem, 1.4029850746268657rem + 0.7462686567164178vw, 2rem);
    line-height: clamp(2rem, 1.4794776119402986rem + 1.1194029850746268vw, 2.375rem);
    margin-bottom: -4px;
    margin-top: 6px;
  }
  .lrc-lead {
    margin-top: 12px;
    font-family: Switzer, sans-serif;
    font-size: clamp(0.875rem, 0.53rem + 0.75vw, 1.125rem);
    line-height: clamp(1.375rem, 0.8544776119402986rem + 1.1194029850746268vw, 1.75rem);
    color: #989AA4;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    font-weight: 400;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  
  @media ${l.U.desktop} {
    flex-direction: row;
    justify-content: space-between;
    .lrc-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 40px;
      min-height: 100%;
      width: calc(50% - 24px);
    }
    .lrc-text {
      font-family: Switzer, sans-serif;
    }
    .lrc-title {
      font-family: Stelvio, sans-serif;
      font-size: 32px;
      line-height: 38px;
      font-weight: 500;
      margin-top: 24px;
      margin-bottom: 0;
    }
    .lrc-lead {
      font-size: 16px;
      color: #9B9B9B;
      margin-top: 14px;
      margin-bottom: 40px;
    }
    .lrc-image {
      position: relative;
      min-width: 50%;
      aspect-ratio: 16/9;
    }
    &:hover {
      border-color: var(--brand-color);
      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);
      box-shadow: 0 0 0 1px var(--c-brand-lighter);
      cursor: pointer;
    }
  }
`;var w=i(6382);function VerticalCard({label:e,post:t,options:i={showType:!1,theme:"light"}}){let o=(0,w.S$)(t.date),a=(0,x.k)(t.image),n=(0,c.qt)(t),l={showName:!0,showDate:!1,theme:i.theme,direction:"column",showPicture:!0};return r.jsx(s(),{href:n,children:(0,r.jsxs)(j,{theme:i.theme,children:[i.showType&&r.jsx("p",{children:t.type}),r.jsx("div",{className:"vc-image",children:r.jsx(p(),{src:a,alt:"",sizes:"15vw",fill:!0,style:v.postImage})}),(0,r.jsxs)("div",{className:"vc-content",children:[e&&r.jsx("p",{className:"vc-label",children:e.text}),r.jsx("p",{className:"vc-title",children:t.title}),r.jsx(Author,{author:t.author,date:o,options:l})]})]})})}let v={postImage:{objectFit:"cover"}},j=a().div.withConfig({componentId:"sc-99268a48-0"})`
  width: 100%;
  border: 1px solid ${e=>"dark"===e.theme?"rgba(250,247,243,0.2)":"rgba(22,22,22,0.2)"};

  .vc-image {
    position: relative;
    height: 200px;
    background-size: 80px;
    background-image: url(/images/tpsg-logo.svg);
    background-color: var(--c-soft-cream);
    background-repeat: no-repeat;
    background-position: center;
  }

  .vc-content {
    padding: 26px 24px 24px 24px;
  }

  .vc-label {
    margin-top: 0;
    margin-bottom: 8px;
    font-size: 16px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.72);
  }

  .vc-title {
    margin-top: 0;
    height: clamp(4.5rem, 2.65rem + 2.88vw, 5.25rem);
    margin-bottom: 40px;

    color: ${e=>"dark"===e.theme?"var(--c-soft-cream)":"#161616"};

    font-family: Switzer, "Helvetica Neue", Helvetica, sans-serif;
    font-size: clamp(1.125rem, 0.82rem + 0.48vw, 1.25rem);
    font-weight: ${e=>"dark"===e.theme?400:500};

    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  
  transition: all 300ms ease;

  @media ${l.U.desktop} {
    margin: 2px 2px;
    &:hover {
      transform: translateY(-8px);
      cursor: pointer;
      border: 1px solid var(--c-brand-lighter);
      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);
      box-shadow: 0 0 0 1px var(--c-brand-lighter);
    }
  }
`,k=["Default","vocation","ministry"],N={theme:"dark",showType:!1};function RenderVerticalCard({relatedItem:e,baseTopicNames:t}){let i;t&&(i="Default"===e.section?e.post.topics.filter(e=>t.includes(e.name)).map(e=>e.name).splice(0,2).join(", "):e.origin);let o={text:i,link:""};return r.jsx(VerticalCard,{post:e.post,options:N,label:o})}function Related({items:e,baseTopicNames:t}){if(!e)return null;let i=e.filter(e=>"webinar"===e.section)[0],o=e.filter(e=>k.includes(e.section));return(0,r.jsxs)(y,{children:[r.jsx(C,{children:"Ressources similaires"}),i&&r.jsx(LargeRelatedCard,{post:i.post}),r.jsx("div",{className:"related-posts",children:o&&o.map((e,i)=>r.jsx(RenderVerticalCard,{relatedItem:e,baseTopicNames:t},i))})]})}let y=a().section.withConfig({componentId:"sc-b49488c-0"})`
  position: relative;
  background-color: var(--c-dark-green);
  padding: 72px var(--border-space) 128px var(--border-space);
  
  .related-posts {
    margin-top: 126px;
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-gap: 24px;
  }
  
  @media ${l.U.tablet} {
    .related-posts {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  @media ${l.U.desktop} {
    .related-posts {
      grid-template-columns: repeat(4, 1fr);
    }
  }
`,C=a().h2.withConfig({componentId:"sc-b49488c-1"})`
  color: var(--c-soft-cream);
  font-family: Stelvio, sans-serif;
  font-size: 40px;
  font-weight: 500;
  line-height: 32px;
  width: 70%;
  margin-bottom: 40px;
  
  @media ${l.U.tablet} {
    width: 100%;
    white-space: nowrap;
  }
  @media ${l.U.desktop} {
    font-size: 40px;
  }
`},2350:(e,t,i)=>{i.d(t,{Z:()=>b});var r=i(997),o=i(7518),a=i.n(o),n=i(7467),s=i(2558);function Text({label:e,content:t,noClass:i,addClass:o}){return(0,r.jsxs)(l,{className:i?"":`subheader-item ${o}`,children:[r.jsx("p",{className:"subheader-text_label",children:e}),r.jsx("p",{className:"subheader-text_content",children:t})]})}let l=a().div.withConfig({componentId:"sc-32a8df6a-0"})`
  display: flex;
  flex-direction: column;
  position: relative;
  font-size: 16px;
  min-width: 10%;
  .subheader-text_label {
    font-weight: 600;
    margin-top: 4px;
    margin-bottom: 0;
  }
  .subheader-text_content {
    margin-top: 2px;
    margin-bottom: 0;
    font-weight: 400;
    color: #484848;
  }
  @media ${n.U.tablet} {
    font-size: 20px;
  }
  @media ${n.U.desktop} {
    font-size: 22px;
  }
`;var c=i(2570);i(1077);var d=i(6068);let p=a().div.withConfig({componentId:"sc-bb91c198-0"})`
  display: flex;
  flex-direction: row;
  align-items: center;
`,m=a().div.withConfig({componentId:"sc-bb91c198-1"})`
  display: none;
  flex-direction: row;
  margin-right: 16px;
  margin-left: 10px;

  .author-picture {
    position: relative;
    height: 50px;
    width: 50px;
    border-radius: 25px;
    margin-left: -10px;
    overflow: hidden;
    background-color: #161616;
  }

  @media ${n.U.tablet} {
    display: flex;
  }
`;var h=i(3459);let x=a().div.withConfig({componentId:"sc-9fe33153-0"})`
  position: absolute;
  right: 0;
`;var g=i(1664),f=i.n(g);let u=a().div.withConfig({componentId:"sc-1f2fe2ae-0"})`
  position: absolute;
  display: flex;
  align-items: center;
  right: 0;
  top: 0;
  height: 100%;

  a {
    margin: 0;
    padding: 12px 18px 6px 18px;
    color: #ffffff;
    font-size: 20px;
    line-height: 24px;
    background-color: var(--brand-color);
    /* No border radius as per Jean-Daniel's feedback */
  }

  &:hover {
    opacity: 0.72;
  }
`,SubHeader=({children:e})=>r.jsx(w,{className:"subheader",children:e});SubHeader.Authors=function({label:e,authors:t,addClass:i}){let o=(0,d.o)();if(!t)return r.jsx(r.Fragment,{});t.length-2>0&&t.length;let a=t.filter(e=>e?.fullName).map(e=>e.fullName).splice(0,2).toString().replace(/\,/g,", ");a=a.length>0?a:`aucun ${e} n'est renseign\xe9`;let getAuthorLink=e=>{if(e.url)return e.url;if(e.slug){let t=o.blogs?.some(t=>t.slug===e.slug);if(t)return`/blog/${e.slug}`}return`/recherche?author=${encodeURIComponent(e.fullName)}`},AuthorLink=({author:e,children:t})=>{let i=getAuthorLink(e);return r.jsx(c.Z,{link:i,children:t})},n=t.filter(e=>e?.fullName).map((e,t)=>r.jsx(AuthorLink,{author:e,children:t>0?", "+e.fullName:e.fullName},t));return(0,r.jsxs)(p,{className:`subheader-item ${i}`,children:[r.jsx(m,{children:t.filter(e=>e?.picture).map((e,t)=>r.jsx(AuthorLink,{author:e,children:r.jsx("div",{className:"author-picture",children:r.jsx(s.Z,{imageData:e.picture})},`pic-${t}}`)},t))}),r.jsx(Text,{label:e,content:n,noClass:!0})]})},SubHeader.Text=Text,SubHeader.Social=function({url:e,addClass:t}){return r.jsx(x,{className:`subheader-item ${t}`,children:r.jsx(h.Z,{url:e,inRow:!0})})},SubHeader.LinkButton=function({url:e,text:t}){return r.jsx(u,{children:r.jsx(f(),{href:e,children:t})})};let b=SubHeader,w=a().div.withConfig({componentId:"sc-f4ab0496-0"})`
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;

  width: 100%;

  padding-top: 16px;
  padding-bottom: 16px;
  border-top: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;

  @media ${n.U.tablet} {
    padding-top: 24px;
    padding-bottom: 24px;
  }
  .subheader-item {
    padding-left: 16px;
    margin-right: 48px;
    border-left: 1px solid #dddddd;
  }
  .subheader-item:first-child {
    padding-left: 0;
    border-left: 0;
  }
  .subheader-item:last-child {
    margin-right: 0;
  }

`},3459:(e,t,i)=>{i.d(t,{Z:()=>SocialMedia});var r=i(997),o=i(6158),a=i(7518),n=i.n(a);function SocialMedia({url:e,inRow:t}){return(0,r.jsxs)(s,{className:"social-buttons",inRow:t,children:[r.jsx(o.TwitterShareButton,{url:e,className:"social-button",children:r.jsx(TwitterIcon,{})}),r.jsx(o.FacebookShareButton,{url:e,className:"social-button",children:r.jsx(FacebookIcon,{})})]})}let s=n().div.withConfig({componentId:"sc-bc9d1278-0"})`
  display: flex;
  flex-direction: ${e=>e.inRow?"row":"column"};
  width: ${e=>e.inRow?"auto":"52px"};
  height: ${e=>e.inRow?"52px":"200px"};
  
  svg {
    margin-bottom: ${e=>e.inRow?"auto":"8px"};
    margin-top: ${e=>e.inRow?"4px":"auto"};
    margin-right: 8px;
    width: 32px;
    height: 32px;
  }
  
  .social-button {
    &:hover {
      * {
        stroke: var(--c-brand-light);
      }
    }
  }
`,FacebookIcon=()=>(0,r.jsxs)("svg",{width:"28",height:"28",viewBox:"0 0 28 28",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[r.jsx("path",{d:"M13.7038 24.5377C19.3802 24.5377 23.9819 19.936 23.9819 14.2596C23.9819 8.58321 19.3802 3.98157 13.7038 3.98157C8.02742 3.98157 3.42578 8.58321 3.42578 14.2596C3.42578 19.936 8.02742 24.5377 13.7038 24.5377Z",stroke:"#242424",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),r.jsx("path",{d:"M17.9856 9.97705H16.2726C15.5912 9.97705 14.9376 10.2478 14.4557 10.7296C13.9738 11.2115 13.7031 11.8651 13.7031 12.5466V24.5376",stroke:"#242424",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),r.jsx("path",{d:"M10.2773 15.9727H17.1294",stroke:"#242424",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]}),TwitterIcon=()=>r.jsx("svg",{width:"28",height:"29",viewBox:"0 0 28 29",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M13.7101 10.1847C13.7102 9.20074 14.0491 8.24682 14.6698 7.48331C15.2904 6.7198 16.155 6.19326 17.1182 5.99222C18.0814 5.79118 19.0845 5.92789 19.9587 6.37937C20.833 6.83085 21.5251 7.56957 21.9188 8.47133L25.7012 8.47137L22.2471 11.9255C22.0222 15.3997 20.4835 18.658 17.9435 21.0389C15.4035 23.4199 12.0526 24.7449 8.57112 24.7449C5.1451 24.7449 4.2886 23.4602 4.2886 23.4602C4.2886 23.4602 7.71462 22.1754 9.42762 19.6059C9.42762 19.6059 2.57559 16.1799 4.2886 6.75836C4.2886 6.75836 8.57112 11.0409 13.7087 11.8974L13.7101 10.1847Z",stroke:"#242424",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})})}};