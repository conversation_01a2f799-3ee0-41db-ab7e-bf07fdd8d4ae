import { Feed } from "feed";
import showdown from "showdown";
import { modulesAsObj } from "../utils/components.utils";
import { queries } from "api/gql-queries";
import client from "../api/apollo-client";
import * as fs from "fs";
import { getPostRoute } from "../utils/posts.utils";
import { removeMarkdown } from "../utils/string.utils";

const APP_URL = process.env.NEXT_PUBLIC_URL;
// const FEED_LENGTH = process.env.FEED_LENGTH || 10;

export default function generateFeeds(coreData) {
  generateMainFeed();
  generateBlogFeeds(coreData.blogs);
}

async function generateMainFeed() {
  // Récupère les posts pour le flux principal
  const posts = await client.query({
    query: queries.QUERY_POSTS,
    variables: { limit: 10, sort: "published_at:DESC" }
  }).then(response => {
    return response.data.posts
  })

  const now = new Date()
  // Paramètres du flux principal
  const feedHeader = {
    title: "TPSG",
    id: APP_URL,
    link: APP_URL,
    description: "ToutPourSaGloire.com, c'est des milliers de ressources pour vous aider à mener une vie qui glorifie Dieu",
    language: "fr",
    updated: now,
    copyright: `All rights reserved ${now.getFullYear()}, ToutPourSaGloire`,
    author: {
      name: "ToutPourSaGloire.com",
      email: "<EMAIL>"
    }
  };

  // Initialise le flux RSS
  let feed = new Feed(feedHeader);

  // Ajoute les posts au flux RSS
  posts.forEach(post => {
    feed.addItem( preparePost(post) );
  });

  // Enregistre le flux RSS
  fs.writeFileSync("./public/main-rss.xml", feed.rss2());
}

async function generateBlogFeeds(blogs) {
  let queryOptions = {
    query: queries.QUERY_BLOG_POSTS,
    variables: { limit: 10, sort: "published_at:DESC" }
  }

  for (let blog of blogs) {

    queryOptions.variables.blog = blog.id;
    // Récupère les posts du blog
    const posts = await client.query(queryOptions)
      .then(response => {
        return response.data.posts
      })

    const now = new Date()
    const feedHeader = {
      title: blog.blogger.fullName,
      id: `${APP_URL}/${blog.slug}`,
      link: `${APP_URL}/${blog.slug}`,
      description: `Blog de ${blog.blogger.fullName} sur le site toutpoursagloire.com`,
      language: "fr",
      updated: now,
      copyright: `All rights reserved ${now.getFullYear()}, ToutPourSaGloire`,
      author: {
        name: blog.blogger.fullname,
        email: `${blog.blogger.fullName}@toutpoursagloire.com`
      }
    };

    // Initialise le flux RSS
    let feed = new Feed(feedHeader);

    // Ajoute les posts au flux RSS
    posts.forEach(post => {
      feed.addItem( preparePost(post) );
    });

    let filePath = `./public/${blog.slug}-rss.xml`;

    // Enregistre le flux RSS
    fs.writeFileSync(filePath, feed.rss2());
  }
}

// Prépare les posts pour le flux RSS
function preparePost(post) {

  // Initialise le convertisseur Markdown
  let converter = new showdown.Converter();
  let body = converter.makeHtml(post.body.replace(/`/g, ""));

  if(post.type === "webinaire" || post.type === "podcast") {
    body = getEmbed(post) + body;
  }

  const postModule = modulesAsObj(post.modules);
  let lead = postModule?.lead?.content ? postModule.lead.content : "";
  lead = removeMarkdown(lead);

  return {
    title: post.title,
    // id: post.slug,
    link: `${APP_URL}${getPostRoute(post)}`,
    content: body,
    description: truncate(lead),
    date: new Date(post.published_at)
  }
}

function truncate(str) {
  let res = str;
  const spaceCount = (str.split(" ").length - 1);
  if(spaceCount > 50) {
    res = str.split(" ").splice(0, 50).join(" ");
    res = res + "...";
  }
  return res;
}

function getEmbed(post) {
  const postModules = modulesAsObj(post.modules);
  if(postModules.podcast) {
    return postModules.podcast.embedAudio || postModules.podcast.embedVideo;
  }
  if(postModules.webinar) {
    return postModules.webinar.embedVideo;
  }
  return null;
}
