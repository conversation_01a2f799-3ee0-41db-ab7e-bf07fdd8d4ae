import styled from "styled-components";
import CondImage from "components/shared/condimage";
import { device } from "styles/device";
import Medal from "./Medal";
import { modulesAsObj } from "utils/components.utils";
import Preview from "components/shared/Preview";

export default function BannerFormation({ lastPost }) {
  const lastPostModule = modulesAsObj(lastPost?.modules);
  const youtubeEmbed = lastPostModule?.formation?.youtubeEmbed;
  if (!lastPostModule.formation) return null;

  return (
    <FormationBanner>
      <div className="banner-cover">
        <div className="banner-cover-relative">
          <CondImage imageData={lastPost.image} priority={true} />
        </div>
      </div>
      <BannerCard>
        <div className="banner-center-outside">
          <div className="banner-center-inside">
            <BannerTextWrapper>
              <p className="banner-text-lastformation">Dernière formation</p>
              <h1 className="banner-text-title">{lastPost.title}</h1>
              <p className="banner-text-lead">
                {lastPostModule.lead.content || ""}
              </p>
            </BannerTextWrapper>
            <BannerButtonWrapper>
              <button className="banner-btn">
                {youtubeEmbed && (
                  <Preview youtubeEmbed={youtubeEmbed}>
                    <div>Voir l&apos;aperçu</div>
                  </Preview>
                )}
              </button>

              <div className="mobile-show banner-space-btn"></div>
              <a
                href={lastPostModule.formation.link}
                target="_blank"
                rel="noreferrer"
                className="banner-btn black-btn"
              >
                {"S'inscrire"}
              </a>
            </BannerButtonWrapper>
            <Medal link={lastPostModule.formation.link} />
          </div>
        </div>
      </BannerCard>
    </FormationBanner>
  );
}

const BannerTextWrapper = styled.div`
  margin-bottom: 48px; //place for medal
  .banner-text-lastformation {
    margin: 0 0 12px 0;
    color: #c8c8c8;
    font-size: 16px;
  }

  .banner-text-title {
    font-weight: 500;
    margin: 0px;
    font-size: clamp(28px, 2.5vw, 48px);
  }

  .banner-text-lead {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 30px;
    font-size: 16px;
    color: #888888;
  }

  @media ${device.tablet} {
    width: 70%;
    .banner-text-lead {
      font-size: 24px;
    }

    .banner-text-lastformation {
      font-size: 20px;
    }
  }
`;

const BannerButtonWrapper = styled.div`
  position: absolute;
  display: flex;
  width: 100%;
  height: 31px;

  bottom: 0px;
  right: 0px;

  .banner-space-btn {
    width: 140px;
    display: none;
  }

  .banner-btn {
    //button or link
    font-family: "Stelvio", Arial, sans-serif;
    text-align: center;
    font-size: 14px;
    line-height: 35px;
    width: 100%;

    background-color: #ebebeb;

    margin-left: -1px;
    margin-bottom: -1px;
    border: 1px solid #242424;

    &:hover {
      cursor: pointer;
    }
  }

  .black-btn {
    background-color: #242424;
    color: white;
  }

  @media screen and (min-width: 335px) {
    .banner-space-btn {
      display: flex;
    }
  }

  @media ${device.tablet} {
    position: relative;
    .banner-btn {
      width: auto;
      font-size: 17px;
      padding: 0px 16px;
      margin-right: 24px;
    }

    .banner-space-btn {
      display: none;
    }
  }
`;

const BannerCard = styled.div`
  z-index: 1;
  padding: 20px;
  max-width: 1126px;
  width: 100%;

  .banner-center-outside {
    padding: 8px;
    background-color: #ebebeb;

    .banner-center-inside {
      background-color: #ebebeb;
      position: relative;
      padding: 16px;
      border: 1px solid #242424;
    }
  }

  @media ${device.tablet} {
    width: calc(100% - 50px);
    .banner-center-outside {
      padding: 24px;

      .banner-center-inside {
        border: 10px double #242424;
        padding: 48px;
      }
    }
  }
  @media ${device.desktop} {
    .banner-center-outside {
      padding: 24px;

      .banner-center-inside {
        border: 10px double #242424;
        padding: 64px;
      }
    }
  }
`;

const FormationBanner = styled.div`
  position: relative;
  width: 100%;
  padding: 122px 0px;
  display: flex;
  justify-content: center;
  align-items: center;

  .banner-cover {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .banner-cover-relative {
    position: relative;
    width: 100%;
    height: 100%;
  }
`;
