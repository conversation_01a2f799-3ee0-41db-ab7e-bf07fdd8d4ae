"use strict";

const post = require("../services/post");
const fs = require("fs");

const redirectsFile = ( "./redirects.json" );

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */

module.exports = {

  async issues(ctx) {
    return await strapi.services["post"].issues();
  },

  async relatedPosts(ctx) {
    const post = await strapi.query("post").findOne({id: ctx.query.id});
    if (post) {
      return await strapi.services["post"].related(post)
    }
    return [];
  },

  async updateTopics(ctx) {
    let posts = await strapi.query("post").find({_limit: -1});
    for (const post of posts) {
      if (post.topics.length === 0) {
        let newTopics = post.old_topics.map(oldTopic => oldTopic.topic);
        await strapi.query("post").update({id: post.id}, {topics: newTopics, noTrigger: true});
      }
    }
    return "post topics are updated";
  },

  /**
   * Met à jour le champ created_by de tous les articles du site.
   * @param ctx
   * @return {Promise<string>}
   */
  async updateCreatedBy(ctx) {
    // récupère tous les posts qui appartiennent à un blog
    let posts = await strapi.query("post").find({
      _limit: -1,
      blog_null: false
    });

    const knex = strapi.connections.default;

    for (const post of posts) {
      // on vérifie que le blog possède bien un utilisateur associé
      if (post.blog.author_user) {
        await knex('posts').update({
          created_by: post.blog.author_user
        }).where('id', post.id);
      }
    }
    return "post created_by are updated";
  },

  /**
   * Ajoute Florent en tant qu'auteur des épisodes de 1PVR;
   * @param ctx
   * @return {Promise<string>}
   */
  async check1PVR(ctx) {
    let florentID = await strapi.query("author").findOne({fullName: "Florent Varak"});
    let posts = await strapi.query("post").find({_limit: -1});
    for (const post of posts) {
      let podcast = extractComponent(post.modules, "module.podcast");
      if (!post.author && florentID && podcast?.podcast?.id === 2) {
        await strapi.query("post").update({id: post.id}, {author: florentID})
      }
    }
    return "1PVR author updated";
  },

  /**
   * Met à jour le temps de lecture de tous les articles du site.
   * Ne prends pas en compte les posts qui ne sont pas du type article
   * @param ctx
   * @return {Promise<string>}
   */
  async addReadingTime(ctx) {
    let posts = await strapi.query("post").find({
      _limit: -1,
      type: "article",
      readingTime_null: true
    });
    for (const post of posts) {
      const rt = strapi.config.functions["utils"].getReadingTime(post.body);
      await strapi.query("post").update({id: post.id}, {readingTime: rt});
    }
    return "Reading time field is updtaded on all articles"
  },

  async addBlogs(ctx) {
    let posts = await strapi.query("post").find({_limit: -1});
    for (const post of posts) {
      if (post.author?.id) {
        let blog = await strapi.query("blog").findOne({blogger: post.author.id})
        if (blog) {
          await strapi.query("post").update({id: post.id}, {blog: blog.id})
        }
      }
    }
    return "post blogs are updated";
  },

  async addTags(ctx) {
    // on récupère la liste des tags 'channel' pour pouvoir les ajouter aux posts.
    // pour pouvoir les ajouter au post avec leur ID
    let channels = await strapi.query("tag").search({_q: "channel."});
    let channelsObj = {}
    let errors = {}
    for (const channel of channels) {
      channelsObj[channel.slug] = channel.id;
    }
    // on utilise le terme media pour faire référence aux posts qui sont de type
    // podcast ou webinaire seulement
    let mediaPosts = await strapi.query("post").find({_limit: -1, type_in: ["webinaire", "podcast"]});
    let channelTagId;
    let postTagIds;
    for (const post of mediaPosts) {
      postTagIds = post.tags.map((tag) => tag.id);
      for (const module of post.modules) {
        switch (module.__component) {
          case "module.webinar":
            channelTagId = channelsObj[`channel-${module.webinar.slug}`];
            break;
          case "module.podcast":
            channelTagId = channelsObj[`channel-${module.podcast.slug}`];
            break;
        }
        if (!channelTagId) {
          errors.module = module;
        }
      }
      if (postTagIds) {
        if (postTagIds.indexOf(channelTagId) === -1) {
          postTagIds.push(channelTagId);
        }
      } else {
        postTagIds = [channelTagId];
      }
      await strapi.query("post").update({id: post.id}, {tags: postTagIds, noTrigger: true});
    }
    return {
      message: "Operation complete",
      errors: errors,
    };
  },

  // todo: ce point d'api ne devrait plus être utilisé depuis que les types
  //  sont bien importer lors de la migration, et correctement ajoutés lors
  //  de la création d'un post.

  // et pourtant...
  // Update 22 nov 2022 de la fonction: réutilisation de getMetas pour ajouter le type

  async addType() {
    const posts = await strapi.query("post").find({_limit: -1})
    for (const post of posts) {
      const {type, tags} = await strapi.services["post"].getMetas(post);
      await strapi.query("post").update(
        {id: post.id},
        {tags: tags, type: type, noTrigger: true},
      );
    }
    return "Updating posts types -> done";
  },

  /**
   * Créer un fichier json contenant toutes les redirections et retourne le contenu
   * du fichier.
   * @return {Promise<{}>}
   */
  async redirects() {
    const posts = await strapi.query("post").find({_limit: -1})

    let urlParts;
    let redirects = [];

    for (const post of posts) {

      urlParts = {
        blog: "",
        type: undefined,
        channel: undefined
      }

      // Si le post appartient à un blog, on ajoute le slug du blog à l'URL.
      if (post.blog?.slug) {
        urlParts.blog = post.blog.slug.replace("-", "") + ".";
      }

      if (post.type) {
        urlParts.type = post.type;
        if (post.type === "podcast") {
          let module = extractComponent(post.modules, "module.podcast");
          if(!module?.podcast?.slug) {
            console.log("Podcast error: ", post.slug);
            break;
          }
          urlParts.channel = `${module.podcast.slug}/`;
          urlParts.type += "s";
        }
        if (post.type === "webinaire") {
          urlParts.type += "s";
        }
        if (post.type === "parcours") {
          urlParts.type = "parcours-emails";
        }
      }

      redirects.push({
        "wordpress": `https://${urlParts.blog}toutpoursagloire.com/${post.slug}`,
        "strapi": `${process.env.CLIENT_URL}/${urlParts.type || ""}/${urlParts.channel || ""}${post.slug}`,
        "noBase": `/${urlParts.type || ""}/${urlParts.channel || ""}${post.slug}`
      });
    }

    const jsonData = {
      entries: {},
      updated_at: new Date()
    };

    for (let entry of redirects) {
      jsonData.entries[entry.wordpress] = entry.noBase;
    }

    const jsonString = JSON.stringify(jsonData, null, 2);

    fs.writeFile(redirectsFile, jsonString, (err) => {
      if (err) {
        console.error(err);
      } else {
        console.log("Redirects File updated");
      }
    });

    return jsonData;
  },

  /**
   * Converti toutes les urls qui pointaient vers l'ancien site à l'intérieur des articles
   * par les nouvelles urls.
   */
  async convertOldUrls() {

    const posts = await strapi.query("post").find({_limit: 1});

    const file = fs.readFileSync(redirectsFile, "utf8");
    const redirects = JSON.parse(file)["entries"];

    for(let post of posts) {

      let body = post.body;

      let { content, modified } = rewriteBadUrls(body);

      const urls = content?.match(/\b((https?|ftp|file):\/\/|(www|ftp)\.)[-A-Z0-9+&@#\/%?=~_|$!:,.;]*[A-Z0-9+&@#\/%=~_|$]/ig);


      if(!urls) {
        if(modified) {
          await strapi.query("post").update({id: post.id}, {body: content});
        }
      } else {
        for (let url of urls) {
          url = url.replace(/\/$/, '');
          if (url.includes("toutpoursagloire.com") && redirects[url]) {
            content = content.replace(url, redirects[url]);
            await strapi.query("post").update({id: post.id}, {body: content});
          }
        }
      }
    }
    return "Les urls à l'intérieur des posts on été changées";
  }
};

function rewriteBadUrls(body) {

  const badUrls = body.match(/<\b((https?|ftp|file):\/\/|(www|ftp)\.)[-A-Z0-9+&@#\/%?=~_|$!:,.;]*[A-Z0-9+&@#\/%=~_|$]>/ig);

  if(!badUrls) return {content: body, modified: false};

  for (const badUrl of badUrls) {
    let url = badUrl.substring(1, badUrl.length-1);
    let goodUrl = `[${url}](${url})`;
    body = body.replace(badUrl, goodUrl);
  }
  return {content: body, modified: true};
}

function extractComponent(components, componentName) {
  return components.find(function (component) {
    return component.__component === componentName;
  });
}

function extractTag(tags, slug) {
  return tags.find(function (tag) {
    return tag?.slug === slug;
  });
}
