/**
 * Tri une liste de topics en ajoutant les enfants de chaque topic
 * dans un nouvel attribut "children".
 * @param topics
 * @returns [{}]
 */
function topicSort(topics) {

  const sortedList = []

  const cpyTopics = topics.map((topic) => ( {
    ...topic,
    children: null
  } ));

  cpyTopics.filter((t) => ( t.parent == null || t.id === cpyTopics[0].id ))
    .map((N1Topic) => {
      cpyTopics.filter((t) => t.parent?.id === N1Topic.id).map((N2Topic) => {
        cpyTopics.filter((t) => t.parent?.id === N2Topic.id).map((N3Topic) => {
          N2Topic.children = ( N2Topic.children ? N2Topic.children : [] );
          N2Topic.children.push(N3Topic);
        })
        N2Topic.children && N2Topic.children.sort(dynamicSort("name"));
        N1Topic.children = ( N1Topic.children ? N1Topic.children : [] );
        N1Topic.children.push(N2Topic)
      })
      N1Topic.children && N1Topic.children.sort(dynamicSort("name"));
      sortedList.push(N1Topic);
    });
  return sortedList;
}

const dynamicSort = (property) => {
  let sortOrder = 1;
  if (property[0] === "-") {
    sortOrder = -1;
    property = property.substr(1);
  }
  return function (a, b) {
    let result = ( a[property] < b[property] ) ? -1 : ( a[property] > b[property] ) ? 1 : 0;
    return result * sortOrder;
  }
}

function sortByDate(a,b) {
  return new Date(b.published_at) - new Date(a.published_at);
}

export {
  topicSort,
  dynamicSort,
  sortByDate
}
