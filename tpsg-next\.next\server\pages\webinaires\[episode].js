"use strict";(()=>{var e={};e.id=377,e.ids=[377,2888],e.modules={4492:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>b,default:()=>d,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>c,reportWebVitals:()=>S,routeModule:()=>h,unstable_getServerProps:()=>P,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>q,unstable_getStaticPaths:()=>x,unstable_getStaticProps:()=>_});var s=r(7093),i=r(5244),o=r(1323),p=r(779),l=r(4033),n=r(7393),u=e([n]);n=(u.then?(await u)():u)[0];let d=(0,o.l)(n,"default"),c=(0,o.l)(n,"getStaticProps"),m=(0,o.l)(n,"getStaticPaths"),g=(0,o.l)(n,"getServerSideProps"),b=(0,o.l)(n,"config"),S=(0,o.l)(n,"reportWebVitals"),_=(0,o.l)(n,"unstable_getStaticProps"),x=(0,o.l)(n,"unstable_getStaticPaths"),q=(0,o.l)(n,"unstable_getStaticParams"),P=(0,o.l)(n,"unstable_getServerProps"),y=(0,o.l)(n,"unstable_getServerSideProps"),h=new s.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/webinaires/[episode]",pathname:"/webinaires/[episode]",bundlePath:"",filename:""},components:{App:l.default,Document:p.default},userland:n});a()}catch(e){a(e)}})},7393:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>WebinarEpisode,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var s=r(997),i=r(9114),o=r(1385),p=r(7672),l=r(635),n=r(6303),u=e([n]);n=(u.then?(await u)():u)[0];let d="true"===process.env.LIGHT_BUILD?10:9999;function WebinarEpisode({post:e,relatedPosts:t}){return s.jsx(n.KB,{episode:e,relatedPosts:t,preview:!1})}async function getStaticProps({params:e}){let t=await o.Z.query({query:m,variables:{episodeSlug:e.episode}}).then(e=>e.data.posts[0]);if(!t)return{notFound:!0};let r=await o.Z.query({query:p.o$.QUERY_RELATED,variables:{id:t.id}}).then(e=>e.data.relatedPosts);return{props:{post:t={...t,modules:(0,l.fw)(t.modules)},relatedPosts:r||void 0},revalidate:10}}async function getStaticPaths(){let e=await o.Z.query({query:c,variables:{limit:d}}).then(e=>e.data.posts);return{paths:e.map(e=>({params:{episode:e.slug}})),fallback:!0}}let c=i.gql`
    query WebinarSlugs($limit: Int!){
        posts(where: {type: "webinar"}, limit: $limit){
            slug
        }
    }
`,m=i.gql`
    query WebinarEpisode($episodeSlug: String!){
        posts(where: {slug: $episodeSlug}){
            title
            slug
            type
            published_at
            body
            id
            image {
                url
                provider
            }
            topics {
                name
                slug
            }
            modules {
                ... on ComponentModuleWebinar{
                    __typename
                    webinar {
                        slug,
                    }
                    speakers {
                        fullName
                        slug
                        picture {
                            url
                            provider
                        }
                    }
                    embedVideo
                }
                ... on ComponentModuleLead{
                    __typename
                    content
                }
                ... on ComponentModuleEvent{
                    __typename
                    date
                    url
                }
              ... on ComponentModuleSeo {
                __typename
                metaDescription
                metaTitle
              }
            }
        }
    }
`;a()}catch(e){a(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},6158:e=>{e.exports=require("react-share")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,9755,5152,4033,779,7113,6453,1077,7620,8672,2832,8657,4004,7482,3462,3646,6303],()=>__webpack_exec__(4492));module.exports=r})();