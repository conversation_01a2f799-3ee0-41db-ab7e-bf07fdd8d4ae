@echo off
echo Démarrage du serveur de développement Strapi avec Node.js 14 dans WSL...
echo.

REM Ouvrir WSL dans le répertoire du projet et démarrer le serveur de développement
wsl bash -c "cd $(wslpath '%cd%') && export NVM_DIR=\"$HOME/.nvm\" && [ -s \"$NVM_DIR/nvm.sh\" ] && \. \"$NVM_DIR/nvm.sh\" && echo 'Node.js version:' && node -v && echo 'npm version:' && npm -v && echo 'Démarrage du serveur de développement...' && npm run develop"
