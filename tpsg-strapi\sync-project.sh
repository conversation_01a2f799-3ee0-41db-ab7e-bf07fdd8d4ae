#!/bin/bash
# Script pour synchroniser les fichiers entre Windows et WSL

# Définir les chemins
WINDOWS_PROJECT="/mnt/c/rep/TPSG/tpsg-strapi"
WSL_PROJECT="$HOME/projects/tpsg-strapi"

# Créer le répertoire de projet dans WSL s'il n'existe pas
mkdir -p $WSL_PROJECT

# Fonction pour synchroniser de Windows vers WSL
sync_to_wsl() {
    echo "Synchronisation de Windows vers WSL..."
    rsync -av --exclude="node_modules" --exclude=".git" $WINDOWS_PROJECT/ $WSL_PROJECT/
    echo "Synchronisation terminée!"
}

# Fonction pour synchroniser de WSL vers Windows
sync_to_windows() {
    echo "Synchronisation de WSL vers Windows..."
    rsync -av --exclude="node_modules" --exclude=".git" $WSL_PROJECT/ $WINDOWS_PROJECT/
    echo "Synchronisation terminée!"
}

# Fonction pour installer les dépendances dans WSL
install_deps() {
    echo "Installation des dépendances dans WSL..."
    cd $WSL_PROJECT
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    npm install
    echo "Installation terminée!"
}

# Fonction pour démarrer le serveur de développement dans WSL
start_dev() {
    echo "Démarrage du serveur de développement dans WSL..."
    cd $WSL_PROJECT
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    npm run develop
}

# Menu
echo "=== Synchronisation de projet Windows/WSL ==="
echo "1. Synchroniser de Windows vers WSL"
echo "2. Synchroniser de WSL vers Windows"
echo "3. Installer les dépendances dans WSL"
echo "4. Démarrer le serveur de développement dans WSL"
echo "q. Quitter"
echo -n "Votre choix: "
read choice

case $choice in
    1) sync_to_wsl ;;
    2) sync_to_windows ;;
    3) install_deps ;;
    4) start_dev ;;
    q) exit 0 ;;
    *) echo "Choix invalide!" ;;
esac
