import styled from "styled-components";
import Link from "next/link";
import { device } from "styles/device";
import { withRealSrc } from "../../utils/image-utils";

export default function ListLink({ route, image, text }) {

  // const imgSrc = withRealSrc(image);

  return (
    <Link href={route}>
      <InnerLink>
        <div className={"text-container"}>
          <p className={"ll-text"}>{text}</p>
        </div>
      </InnerLink>
    </Link>
  )
}

const InnerLink = styled.a`
  font-family: Switzer, sans-serif;
  font-weight: 500;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: -1px;
  border-top: 1px solid #CCCAC7;
  border-bottom: 1px solid #CCCAC7;
  padding: 10px 0 10px 4px;
  color: #161616;
  font-size: 18px;
  
  .ll-text {
    margin: 0;
  }

  &:after {
    content: "→";
    position: absolute;
    right: 0;
    line-height: 100%;
    padding-bottom: 4px;
  }
  
  &:hover {
    cursor: pointer;
    color: var(--brand-color);
  }

  @media ${device.tablet} {
    font-size: 22px;
    padding: 12px 0 12px 4px;
  }
`;
