/**
 * Proxy Meilisearch pour adapter les requêtes à Meilisearch v0.27.0
 * 
 * Ce script démarre un serveur proxy qui intercepte les requêtes à Meilisearch
 * et les adapte pour qu'elles soient compatibles avec Meilisearch v0.27.0.
 * 
 * IMPORTANT: Ce script doit être exécuté avant de démarrer l'application Next.js.
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const bodyParser = require('body-parser');

// Configuration
const PORT = 7701;
const MEILI_URL = 'http://127.0.0.1:7700';

const app = express();

// Middleware pour parser le corps des requêtes
app.use(bodyParser.json());

// Fonction pour adapter les paramètres de recherche
function adaptSearchParams(params) {
  if (!params) return params;

  const adaptedParams = { ...params };

  // Convertir page et hitsPerPage en offset et limit
  if (adaptedParams.page !== undefined && adaptedParams.hitsPerPage !== undefined) {
    const page = parseInt(adaptedParams.page) || 1;
    const hitsPerPage = parseInt(adaptedParams.hitsPerPage) || 20;

    adaptedParams.offset = (page - 1) * hitsPerPage;
    adaptedParams.limit = hitsPerPage;

    // Supprimer les paramètres incompatibles
    delete adaptedParams.page;
    delete adaptedParams.hitsPerPage;
  }

  return adaptedParams;
}

// Fonction pour adapter la réponse de Meilisearch
function adaptSearchResponse(response) {
  if (!response) return response;

  const adaptedResponse = { ...response };

  // Ajouter totalHits si estimatedTotalHits est présent
  if (adaptedResponse.estimatedTotalHits !== undefined && adaptedResponse.totalHits === undefined) {
    adaptedResponse.totalHits = adaptedResponse.estimatedTotalHits;
  }

  // Calculer totalPages si nécessaire
  if (adaptedResponse.totalHits !== undefined && adaptedResponse.totalPages === undefined) {
    const limit = adaptedResponse.limit || 20;
    adaptedResponse.totalPages = Math.ceil(adaptedResponse.totalHits / limit);
  }

  return adaptedResponse;
}

// Middleware pour intercepter et adapter les requêtes POST à /indexes/post/search
app.post('/indexes/post/search', async (req, res) => {
  try {
    // Adapter les paramètres de recherche
    const adaptedParams = adaptSearchParams(req.body);

    // Envoyer la requête adaptée à Meilisearch
    const response = await fetch(`${MEILI_URL}/indexes/post/search`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...req.headers
      },
      body: JSON.stringify(adaptedParams)
    });

    // Récupérer la réponse
    const data = await response.json();

    // Adapter la réponse
    const adaptedResponse = adaptSearchResponse(data);

    // Envoyer la réponse adaptée
    res.json(adaptedResponse);
  } catch (error) {
    console.error('Erreur lors de la requête à Meilisearch:', error);
    res.status(500).json({ error: 'Erreur lors de la requête à Meilisearch' });
  }
});

// Proxy pour toutes les autres requêtes
app.use('/', createProxyMiddleware({
  target: MEILI_URL,
  changeOrigin: true,
  pathRewrite: {
    '^/': '/'
  }
}));

// Démarrer le serveur
app.listen(PORT, () => {
  console.log(`Proxy Meilisearch démarré sur le port ${PORT}`);
  console.log(`Toutes les requêtes à http://localhost:${PORT} seront transmises à ${MEILI_URL}`);
  console.log('\nIMPORTANT:');
  console.log(`1. Modifiez la variable d'environnement NEXT_PUBLIC_MEILISEARCH_HOST pour qu'elle pointe vers http://localhost:${PORT}`);
  console.log('2. Redémarrez votre application Next.js');
});
