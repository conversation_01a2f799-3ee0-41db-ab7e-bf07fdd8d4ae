"use strict";(()=>{var e={};e.id=2164,e.ids=[2164,2888],e.modules={8042:(e,t,r)=>{r.r(t),r.d(t,{config:()=>S,default:()=>n,getServerSideProps:()=>m,getStaticPaths:()=>d,getStaticProps:()=>c,reportWebVitals:()=>g,routeModule:()=>h,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>q,unstable_getStaticParams:()=>_,unstable_getStaticPaths:()=>P,unstable_getStaticProps:()=>x});var s={};r.r(s),r.d(s,{default:()=>Sitemap,getServerSideProps:()=>getServerSideProps});var a=r(7093),o=r(5244),i=r(1323),l=r(779),p=r(4033),u=r(723);function Sitemap(){return null}async function getServerSideProps({res:e}){let t="https://preprod.toutpoursagloire.com",r=[`${t}/parcours`,`${t}/categories`,`${t}/podcasts`,`${t}/webinaires`,`${t}/formations`,`${t}/recherche`],s=await u.V.urls(),a=`<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${r.map(e=>`
            <url>
              <loc>${e}</loc>
              <lastmod>${new Date().toISOString()}</lastmod>
              <changefreq>monthly</changefreq>
              <priority>1.0</priority>
            </url>
          `).join("")}
      ${s.map(e=>`
            <url>
              <loc>${t}${e.route}</loc>
              <lastmod>${new Date(e.date).toISOString()}</lastmod>
              <priority>1.0</priority>
            </url>
          `).join("")}
    </urlset>
  `;return e.setHeader("Content-Type","text/xml"),e.write(a),e.end(),{props:{}}}let n=(0,i.l)(s,"default"),c=(0,i.l)(s,"getStaticProps"),d=(0,i.l)(s,"getStaticPaths"),m=(0,i.l)(s,"getServerSideProps"),S=(0,i.l)(s,"config"),g=(0,i.l)(s,"reportWebVitals"),x=(0,i.l)(s,"unstable_getStaticProps"),P=(0,i.l)(s,"unstable_getStaticPaths"),_=(0,i.l)(s,"unstable_getStaticParams"),b=(0,i.l)(s,"unstable_getServerProps"),q=(0,i.l)(s,"unstable_getServerSideProps"),h=new a.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/sitemap.xml",pathname:"/sitemap.xml",bundlePath:"",filename:""},components:{App:p.default,Document:l.default},userland:s})},9114:e=>{e.exports=require("@apollo/client")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},9997:e=>{e.exports=require("meilisearch")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,4033,779,723],()=>__webpack_exec__(8042));module.exports=r})();