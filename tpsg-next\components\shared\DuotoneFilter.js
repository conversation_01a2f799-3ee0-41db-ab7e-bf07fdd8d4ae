import { hexToRgb } from "utils/color";


export default function SvgDuotone({ hexLight, hexDark }) {

  const rgbLight = hexToRgb(hexLight);
  const rgbDark = hexToRgb(hexDark);

  return (
    <svg xmlns="http://www.w3.org/2000/svg" className="svg-filter">
      <filter id="duotone-filter">
        <feColorMatrix
          type="matrix" result="grayscale"
          values="1 0 0 0 0
                          1 0 0 0 0
                          1 0 0 0 0
                          0 0 0 1 0"/>
        <feComponentTransfer colorInterpolationFilters="sRGB" result="duotone">
          <feFuncR type="table" tableValues={`${rgbDark.r / 255} ${rgbLight.r / 255}`}/>
          <feFuncG type="table" tableValues={`${rgbDark.g / 255} ${rgbLight.g / 255}`}/>
          <feFuncB type="table" tableValues={`${rgbDark.b / 255} ${rgbLight.b / 255}`}/>
          <feFuncA type="table" tableValues="0 1"/>
        </feComponentTransfer>
      </filter>
    </svg>
  )
}