
module.exports = {
  /**
   * Retourne la chaine passée en paramètre sans markdown.
   * Seuls les noms des liens sont conservés, pas les url.
   *
   * @param   {String}    text   text à modifier
   * @return  {String}   text débarassé du markdown
   */
  removeMarkdown(text) {
    return text.replace(/(?:__|[*#])|\[(.*?)\]\(.*?\)/gm, "$1");
  },

  /**
   * Retourne la chaine passée en paramètre sans balise html en gardant le contenu.
   *
   * @param   {String}    text   text à modifier
   * @return  {String}   text débarrassé du html
   */
  removeHtml(text) {
    return text.replace(/`|<[^>]*(>|…)/gm, "");
  },

  /**
   * Retourne le temps de lecture approximatif d'un article.
   * @param  {string} text  corps de l'article
   * @return {int}  temps de lecture en minutes
   */
  getReadingTime(text)  {
    const words = text.trim().split(/\s+/).length;
    // En moyenne une personne peut lire 250 mots par minute
    return Math.ceil(words / 250);
  },

  /**
   * Retourne la version HTML d'un texte au format Markdown
   * @param  {string} content  texte à formater
   * @return {string} texte au format HTML
   */
  markdownToHtml(content) {
    return "nothing done";
  },

  /**
   * Retourne l'URL du post passé en paramètre
   * @param entry
   */
  getPostUrl(entry) {
    let url;
    switch(entry.type) {
      case "podcast":
        let podcastModule = entry.modules.find(function (module) {
          return module.__component === "module.podcast";
        });
        url = `/podcasts/${podcastModule?.podcast?.slug}/${entry.slug}`;
        break;
      case "parcours":
        url = `/parcours-emails/${entry.slug}`;
        break;
      case "webinaire":
        url = `/webinaires/${entry.slug}`;
        break;
      case "formation":
        url = `/article/${entry.slug}`;
        let formationModule = entry.modules.find(function (module) {
          return module.__component === "module.formation";
        });
        if(formationModule?.link) {
          url = formationModule?.link;
        }
        break;
      default:
        url = `/article/${entry.slug}`;
    }
  }

}
