import React from "react";

// installed components
import styled from "styled-components";

export default function CircleCTA({ text, link, color = "#000000" }) {

  return (
    <Wrapper>
      <Arrow>
        <img src="/images/icons/arrow.svg"/>
      </Arrow>

      <svg width="120" height="120">
        <path
          fill={"transparent"}
          id="circle-path"
          d="
						M 30, 60
						a 30,30 0 1,1 60,0
						a 30,30 0 1,1 -60,0
						"
        />
        <text fill={color}>
          <textPath xlinkHref="#circle-path">
            {text} {text} {text} {text}
          </textPath>
        </text>
      </svg>

    </Wrapper>
  );
}

const Wrapper = styled.div`

	position: relative;
	height: 120px;
	width: 120px;

	svg {
		font-size: 14px;
		letter-spacing: 2px;
		position: relative;
		z-index: 101;

		:hover {
			cursor: pointer;
			animation-name: rotate;
			animation-duration: 6s;
			animation-iteration-count: infinite;
			animation-timing-function: linear;
		}

		@keyframes rotate {
			from {
				transform: rotate(0deg);
			}
			to {
				transform: rotate(360deg);
			}
		}
	}
`;

const Arrow = styled.div`
	position: absolute;
	height: 100%;
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;

	img {
		width: 32px;
	}

	z-index: 100;
`;
