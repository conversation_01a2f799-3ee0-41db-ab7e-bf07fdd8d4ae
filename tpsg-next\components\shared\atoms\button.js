import styled from "styled-components";
import Link from "next/link";
import { device } from "/styles/device";


const Button = ({ text, url, soft, mobileFullWidth }) => {

  return (
    <Wrapper className={soft ? "soft" : ""}>
      <Link href={url || ""}>
        {text}
      </Link>
    </Wrapper>
  )
}

export default Button


const Wrapper = styled.div`
  position: relative;
  font-family: "Stelvio", sans-serif;
  font-size: 17px;
  font-weight: 400;
  display: inline-block;
  width: 100%;
  color : #f4f4f4;
  border: 1px solid #080808;
  background-color: #080808;
  padding: 12px 18px 6px 18px;
  text-align: center;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
  
  &.soft {
    border: 1px solid #080808;
    background-color: transparent;
    color: #161616;
  }
  
  @media ${device.tablet} {
    width: auto;
    font-size: 17px;
  }
`;
