{"kind": "collectionType", "collectionName": "topics", "info": {"name": "topic", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "unique": true, "required": true}, "slug": {"type": "uid", "targetField": "name", "required": true}, "parent": {"model": "topic"}, "description": {"type": "text"}, "parentSlug": {"type": "string"}, "postCount": {"type": "integer"}}}