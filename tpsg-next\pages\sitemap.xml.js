import { Mei<PERSON><PERSON><PERSON> } from "api/meili-client";

export default function Sitemap() {
  return null
}

export async function getServerSideProps({ res }) {

  const baseUrl = "https://preprod.toutpoursagloire.com";

  const rootPages = [
    `${baseUrl}/parcours`,
    `${baseUrl}/categories`,
    `${baseUrl}/podcasts`,
    `${baseUrl}/webinaires`,
    `${baseUrl}/formations`,
    `${baseUrl}/recherche`,
  ]

  const pages = await MeiliApi.urls();

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${rootPages
    .map((url) => {
      return `
            <url>
              <loc>${url}</loc>
              <lastmod>${new Date().toISOString()}</lastmod>
              <changefreq>monthly</changefreq>
              <priority>1.0</priority>
            </url>
          `;
    })
    .join("")}
      ${pages
    .map((page) => {
      return `
            <url>
              <loc>${baseUrl}${page.route}</loc>
              <lastmod>${new Date(page.date).toISOString()}</lastmod>
              <priority>1.0</priority>
            </url>
          `;
    })
    .join("")}
    </urlset>
  `;

  res.setHeader("Content-Type", "text/xml");
  res.write(sitemap);
  res.end();

  return {
    props: {}
  }
}
