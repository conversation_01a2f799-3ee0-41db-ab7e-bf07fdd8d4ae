import styled from "styled-components";
import { dateForHumans } from "utils/date.utils";
import CondImage from "components/shared/condimage";
import { device } from "styles/device";
import Link from "next/link";
import { getPostRoute } from "../../../utils/posts.utils";

/**
 *
 * @param post
 * @param options
 * @return {JSX.Element}
 * options possibles: showAuthor (boolean), showDate(boolean), dot(object)
 */
export default function SquareVerticalCard({ post, options }) {

  const date = options?.showDate ? dateForHumans(post.date) : null;
  const author = options?.showAuthor ? post.author?.fullName : null;
  const route = getPostRoute(post);

  return (
    <CardWrapper className={"sv-card"}>
      {date && <p className={"sv-card-date"}>{dateForHumans(post.date)}</p>}
      <Link href={route}>
        <div className={"sv-card-image"}>
          <CondImage imageData={post.image}/>
        </div>
      </Link>
      {author && <p className={"sv-card-author"}>{author}</p>}
      <Link href={route}>
        <h3 className={"sv-card-title primary-hover"}>{post.title}</h3>
      </Link>
    </CardWrapper>
  )
}

const CardWrapper = styled.div`
  position: relative;
  overflow: auto;
  width: 100%;

  &:hover {
    .image-glow {
      filter: blur(20px);
    }
    cursor: pointer;
  }
  
  .absolute {
    position: absolute;
  }

  .sv-card-date {
    font-family: "Novela", "Lora", sans-serif;
    font-size: 14px;
    color: #888888;
    margin-bottom: 8px;
    font-weight: 400;
    margin-top: 0;
  }

  .sv-card-image {
    position: relative;
    aspect-ratio: 1/1;
    width: 100%;
    cursor: pointer;
  }

  .sv-card-author {
    font-size: 14px;
    font-family: "Lora", serif;
    font-style: italic;
    color: #888888;
    margin-top: 18px;
    margin-bottom: 0;
  }

  .sv-card-title {
    font-size: 18px;
    font-weight: 500;
    line-height: 115%;
    color: ${props => props.featured ? "#f4f4f4" : "#161616"};
  }

  @media ${device.tablet} {
    .sv-card-title {
      margin-top: 16px;
      margin-bottom: auto;
      font-weight: 500;
      line-height: 30px;
      font-size: 27px;
    }

    .sv-card-date {
      font-size: 18px;
    }
  }

  .image-glow {
    position: absolute;
    width: calc(100% - 48px);
    aspect-ratio: 1/1;
    filter: blur(10px);
  }
`