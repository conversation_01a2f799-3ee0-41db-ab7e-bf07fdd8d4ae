import { BlurPlay } from "components/shared/atoms";
import styled from "styled-components";
import { useState } from "react";
import CondImage from "components/shared/condimage";
import { getYouTubeVideoIdFromUrl } from "utils/string.utils";


const YoutubeIframe = ({ video }) => {
  let videoId = getYouTubeVideoIdFromUrl(video);
  let baseUrl = "https://www.youtube.com/embed/";
  let urlParams = "?&autoplay=1&mute=1&enablejsapi=1";
  let src = baseUrl + videoId + urlParams;
  return (
    <iframe src={src} width={"100%"} height={"100%"} frameBorder={"0"} allow="fullscreen"/>
  )
}

// TODO: donner un nom plus explicite au composant. (usage seulement dans les pages d'épisodes).
export default function VideoPlayer({ video, image }) {

  const [showPlayer, setShowPlayer] = useState(false);
  const videoExists = video && video.length > 0;

  return (
    <VideoContainer
      className={"youtube-player-component"}
      hidePlayer={!videoExists}

    >
      <CondImage imageData={image}/>
      {videoExists && (
        <>
          <ReplayLabel>REPLAY</ReplayLabel>
          <BlurPlay clickAction={() => setShowPlayer(true)}/>
          {showPlayer && <YoutubeIframe video={video}/>}
        </>
      )}
    </VideoContainer>
  );
}

const VideoContainer = styled.div`
  position: relative;

  box-shadow: ${(props) =>
    !props.hidePlayer
      ? "0 16px 48px rgba(60, 60, 60, 0.3);"
      : "none"};
  
  width: 100%;
  height: 100%;
  
  iframe {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }
`;

const ReplayLabel = styled.p`
  position: absolute;
  color: #f6f6f6;
  margin-top: 0;
  top: 22px;
  left: 22px;
  font-size: 22px;
  font-weight: 500;
`;
