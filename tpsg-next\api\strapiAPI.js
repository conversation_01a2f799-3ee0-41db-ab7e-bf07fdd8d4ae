import axios from "axios";

const strapiURL = process.env.NEXT_PUBLIC_STRAPI_URL

const getContent = async (ct, params = {}, one = false) => {
  const url = strapiURL + "/" + ct;
  return await axios.get(url, params)
    .then(function (response) {
      return one ? response.data[0] : response.data
    })
    .catch(error => {
      console.log({ error: error, params: params });
    })
}

export const strapiAPI = {
  getContent
}