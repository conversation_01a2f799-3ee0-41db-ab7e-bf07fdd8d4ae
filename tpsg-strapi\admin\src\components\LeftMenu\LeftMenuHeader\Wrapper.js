import styled from 'styled-components';
import PropTypes from 'prop-types';

import Logo from '../../../assets/images/logo-tpsg.png';

const Wrapper = styled.div`
    background-color: ${(props) => props.theme.main.colors.brand.main};
    padding-left: 2rem;
    height: ${(props) => props.theme.main.sizes.leftMenu.height};

    .leftMenuHeaderLink {
        &:hover {
            text-decoration: none;
        }
    }

    .projectName {
        display: block;
        width: 100%;
        height: ${(props) => props.theme.main.sizes.leftMenu.height};
        //font-size: 2rem;
        letter-spacing: 0.2rem;
        color: $white;

        background-image: url(${Logo});
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 100%;
    }
`;

Wrapper.defaultProps = {
  theme: {
    main: {
      colors: {
        leftMenu: {},
      },
      sizes: {
        header: {},
        leftMenu: {},
      },
    },
  },
};

Wrapper.propTypes = {
  theme: PropTypes.object,
};

export default Wrapper;
