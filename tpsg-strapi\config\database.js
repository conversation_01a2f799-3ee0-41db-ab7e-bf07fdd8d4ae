module.exports = ({ env }) => ({
  defaultConnection: 'default',
  acquireConnectionTimeout: 300000, // 5 minute timeout
  connections: {
    default: {
      connector: 'bookshelf',
      settings: {
        client: 'mysql',
        host: env('DATABASE_HOST'),
        port: env.int('DATABASE_PORT'),
        database: env('DATABASE_NAME'),
        username: env('DATABASE_USERNAME'),
        password: env('DATABASE_PASSWORD'),
        ssl: env.bool('DATABASE_SSL'),
        charset: env('DATABASE_CHARSET', 'utf8mb4')
      },
      options: {
        charset: env('DATABASE_CHARSET', 'utf8mb4'),
        "pool":{
          "min": 0,
          "max": 10,
          "idleTimeoutMillis": 30000,
          "createTimeoutMillis": 30000,
          "acquireTimeoutMillis": 30000
        }
      }
    },
  },
});

