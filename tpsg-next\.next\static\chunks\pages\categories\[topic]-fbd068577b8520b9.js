(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[201],{8667:function(n,t,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/categories/[topic]",function(){return e(3196)}])},9824:function(n,t,e){"use strict";e.d(t,{Z:function(){return TopicHeader}});var i=e(2729),o=e(5893),r=e(9521),a=e(7421),c=e(1664),l=e.n(c);function _templateObject(){let n=(0,i._)(["\n  font-size: 17px;\n  font-weight: 400;\n  letter-spacing: 0.2px;\n  margin-top: 24px;\n  color: #080808;\n  font-family: Switzer, sans-serif;\n  a:hover {\n    color: var(--c-brand-light);\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  margin-top: 50px;\n\n  .back-button {\n    font-size: 18px;\n    font-weight: 500;\n    letter-spacing: 1px;\n    color: #080808;\n\n    &:hover {\n      color: var(--c-brand-light);\n    }\n  }\n\n  .page-title {\n    margin-top: 24px;\n    font-size: 48px;\n    line-height: 100%;\n    font-weight: 500;\n    color: #080808;\n  }\n\n  @media "," {\n    margin-top: 96px;\n    .page-title {\n      font-size: 104px;\n    }\n  }\n"]);return _templateObject1=function(){return n},n}let s={"theologie-systematique":0,"theoligie-biblique":1,"theologie-pratique":2,"theologie-historique":3,ethique:4,"apologetique-et-vision-du-monde":5,ressources:6};function TopicHeader(n){let{topic:t,topicName:e,type:i}=n;return(0,o.jsxs)(d,{children:[t?function(n,t){let e,i;let r="/categories";if(n.parent){let t=n.parent.slug,a=n.parent.name;if(n.parent.parent){let c=n.parent.parent.slug,p=n.parent.parent.name,d="".concat(r,"/#theme-").concat(s[c]||"0");"".concat(r,"/").concat(t),i=(0,o.jsx)(l(),{href:d,children:"".concat(p," / ")}),e=(0,o.jsx)(l(),{href:"/categories/".concat(n.parent.slug),children:"".concat(a," / ")})}else{let n="".concat(r,"#theme-").concat(s[t]||"0");e=(0,o.jsx)(l(),{href:n,children:"".concat(a," / ")})}}return(0,o.jsxs)(p,{children:[(0,o.jsx)(l(),{href:"/categories",children:"Th\xe8me / "}),i&&i,e&&e]})}(t,0):(0,o.jsx)(l(),{href:"/categories",className:"back-button",children:i+" /"}),(0,o.jsx)("h1",{className:"page-title",children:e})]})}let p=r.ZP.p.withConfig({componentId:"sc-c602366e-0"})(_templateObject()),d=r.ZP.header.withConfig({componentId:"sc-c602366e-1"})(_templateObject1(),a.U.desktop)},7326:function(n,t,e){"use strict";e.d(t,{Z:function(){return CornerStoneCard}});var i=e(2729),o=e(5893);e(1664);var r=e(9521),a=e(7421),c=e(6368);e(4871);var l=e(1304),s=e(1261);function _templateObject(){let n=(0,i._)(["\n  position: absolute;\n  width: 100%;\n  filter: blur(12px);\n  aspect-ratio: ",";\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  margin-bottom: 50px;\n  box-shadow: 0 7px 14px 3px rgba(0, 0, 0, 0.29);\n  a {\n    width: 100%;\n    display: flex;\n    justify-content: center;\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  width: 70%;\n  padding-left: 20px;\n  @media "," {\n    width: 100%;\n    padding-left: 0;\n  }\n  @media "," {\n    padding-left: 0;\n    width: 100%;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  position: relative;\n  width: 30%;\n  margin-bottom: 24px;\n  aspect-ratio: ",";\n\n\n  @media "," {\n    width: 100%;\n  }\n  @media "," {\n    width: 80%;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  color: white;\n  background-color: #0F0F0F;\n  padding: 20px 20px;\n\n  width: 100%;\n  display: flex;\n  flex-wrap: wrap;\n\n  .corner-stone-title {\n    padding-top: 8px;\n    font-weight: 400;\n    margin-top: 0;\n    margin-bottom: 8px;\n    font-size: 1.25rem;\n    font-size: clamp(1.25rem, 1.178635147190009rem + 0.35682426404995543vw, 1.5rem);\n    display: -webkit-box;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  @media "," {\n    .corner-stone-title {\n      -webkit-line-clamp:  2!important;\n    }\n  }\n  @media "," {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n"]);return _templateObject4=function(){return n},n}function CornerStoneCard(n){var t,e,i;let{post:r,options:a}=n,{showAuthor:h,showBlur:x,aspectRatio:g=1}=a,b=(null==r?void 0:null===(t=r.cta)||void 0===t?void 0:t.url)||(null==r?void 0:r.link)||((null==r?void 0:null===(e=r.route)||void 0===e?void 0:e.startsWith("/"))?r.route:"/"+(null==r?void 0:r.route));return b?(0,o.jsx)(d,{children:(0,o.jsx)(s.Z,{link:b,children:(0,o.jsxs)(m,{children:[(0,o.jsxs)(f,{aspectRatio:g,children:[x&&(0,o.jsx)(p,{aspectRatio:g,children:(0,o.jsx)(l.Z,{imageData:r.image})}),(0,o.jsx)(l.Z,{imageData:r.image})]}),(0,o.jsxs)(u,{children:[(0,o.jsx)("h2",{className:"corner-stone-title",children:null==r?void 0:r.title}),h&&r.author&&(0,o.jsx)(c.My,{children:(null===(i=r.author)||void 0===i?void 0:i.fullName)?r.author.fullName:r.author})]})]})})}):(0,o.jsx)(o.Fragment,{})}let p=r.ZP.div.withConfig({componentId:"sc-9da25472-0"})(_templateObject(),n=>n.aspectRatio),d=r.ZP.div.withConfig({componentId:"sc-9da25472-1"})(_templateObject1()),u=r.ZP.div.withConfig({componentId:"sc-9da25472-2"})(_templateObject2(),a.U.mini,a.U.desktop),f=r.ZP.div.withConfig({componentId:"sc-9da25472-3"})(_templateObject3(),n=>n.aspectRatio,a.U.desktop,a.U.mini),m=r.ZP.div.withConfig({componentId:"sc-9da25472-4"})(_templateObject4(),a.U.desktop,a.U.mini)},3196:function(n,t,e){"use strict";e.r(t),e.d(t,{__N_SSG:function(){return j},default:function(){return PageTopic}});var i=e(2729),o=e(5893),r=e(9521),a=e(3365),c=e(7915),l=e(7421),s=e(1664),p=e.n(s);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n  display: flex;\n  flex-wrap: wrap;\n  flex-direction: row;\n  overflow: hidden;\n  padding-top: 1px;\n  padding-left: 1px;\n  background-color: #F5F5F5;\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(['\n  position: relative;\n  margin-left: -1px;\n  margin-top: -1px;\n  perspective: 500px;\n  height: 50px;\n  box-sizing: border-box;\n  padding-left: 42px;\n  padding-right: 42px;\n  flex-grow: 2;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 22px;\n  cursor: pointer;\n  background-color: white;\n  overflow: hidden;\n\n  p {\n    color: transparent;\n  }\n\n  &:after {\n    content: "','";\n    display: flex;\n    padding: 4px 0 0 0;\n    position: absolute;\n    background-color: black;\n    color: white;\n    transform-origin: 0% 0% 0;\n    transform: rotateX(90deg);\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    align-items: center;\n    justify-content: center;\n    transition: 600ms cubic-bezier(.74,.41,.17,1.08);\n\n  }\n  &:hover:after {\n    transform: rotateX(0) translateY(0);\n  }\n  &:before {\n    width: 100%;\n    padding: 6px 0 0 0;\n    box-sizing: border-box;\n    content: "','";\n    border: 1px solid black;\n    display: flex;\n    position: absolute;\n    background-color: white;\n    transform-origin: 0% 0% 0;\n    transform: rotateX(0deg);\n    top: 0;\n    left: 0;\n    height: 100%;\n    align-items: center;\n    justify-content: center;\n    transition: 600ms cubic-bezier(.74,.41,.17,1.08);\n  }\n  &:hover:before {\n    transform: rotateX(-90deg);\n  }\n']);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  position: relative;\n  background-color: transparent;\n  margin-left: 1px;\n  height: 50px;\n  flex-grow: 100;\n"]);return _templateObject2=function(){return n},n}function TopicsHorizontalList(n){let{topics:t}=n;return(0,o.jsxs)(d,{children:[t&&t.filter(n=>n.postCount>0).map((n,t)=>(0,o.jsx)(p(),{href:"/categories/".concat(n.slug),children:(0,o.jsx)(u,{text:n.name,children:(0,o.jsx)("p",{children:n.name})})},t)),(0,o.jsx)(f,{})]})}let d=r.ZP.div.withConfig({componentId:"sc-66fa4988-0"})(_templateObject()),u=r.ZP.a.withConfig({componentId:"sc-66fa4988-1"})(_templateObject1(),n=>n.text?n.text+" →":"→",n=>n.text?n.text:""),f=r.ZP.div.withConfig({componentId:"sc-66fa4988-2"})(_templateObject2());var m=e(9824),h=e(3500),x=e(211),g=e(4724),b=e(7326);function _topic_templateObject(){let n=(0,i._)(["\n  position: relative;\n  ul {\n    padding: 0 0 0 0;\n  }\n"]);return _topic_templateObject=function(){return n},n}function _topic_templateObject1(){let n=(0,i._)(["\n  display: grid;\n  grid-gap: 16px;\n  grid-template-columns: repeat(2, 1fr);\n\n  .fa-card {\n    grid-column: 1/3;\n  }\n\n  @media "," {\n    grid-template-columns: repeat(4, 1fr);\n    grid-gap: 32px;\n  }\n"]);return _topic_templateObject1=function(){return n},n}function _topic_templateObject2(){let n=(0,i._)(["\n  position: absolute;\n  width: 100vw;\n  height: 100%;\n  margin: auto;\n  top: 275px;\n  left: 0;\n  background-color: #f4f4f4;\n  z-index: -1;\n\n  @media "," {\n    top: 375px;\n  }\n"]);return _topic_templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  margin-top: 96px;\n\n  .posts-container {\n    display: block;\n\n    @media "," {\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: space-between;\n    }\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  width: 100%;\n  margin-bottom: 64px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n\n  .list-container {\n    padding: 0;\n    width: 100%;\n  }\n  .post-card-li {\n    list-style: none;\n    padding-right: 0;\n  }\n\n  @media "," {\n    width: 66.7%;\n    margin-bottom: 164px;\n    .post-card-li {\n      padding-right: 142px;\n    }\n  }\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n\n  .podcast-platform {\n    display: flex;\n    flex-wrap: wrap;\n    margin-top: 64px;\n    width: 100%;\n    gap: 32px;\n  }\n  @media "," {\n    .podcast-platform {\n      margin-top: 16px;\n      gap: 16px;\n    }\n  }\n  @media "," {\n    width: 33.3%;\n  }\n"]);return _templateObject5=function(){return n},n}var j=!0;function PageTopic(n){var t,e,i,r;let{topics:l,fallback:s}=n,p=(null==s?void 0:null===(t=s.posts)||void 0===t?void 0:t.totalHits)||0,d=(null==s?void 0:null===(e=s.posts)||void 0===e?void 0:e.hits)||[],u=(null===(i=s.cornerStones)||void 0===i?void 0:i.hits)||[],f=(null==d?void 0:d.length)>3?3:0;l=(0,a.Ay)(l);let j=l[0];return(0,o.jsxs)(_,{children:[(0,o.jsx)("section",{className:"site-padding",children:(0,o.jsx)(m.Z,{topic:j,topicName:j.name,type:"theme"})}),(null==d?void 0:d.length)>3&&(0,o.jsxs)(w,{className:"site-padding",children:[(0,o.jsx)(c.F3,{post:d[0],options:{showAuthor:!0,dotColors:{back:"#F4F4F4"}}}),(0,o.jsx)(c.Zo,{post:d[1],options:{showAuthor:!0,dotColors:{back:"#F4F4F4"}}}),(0,o.jsx)(c.Zo,{post:d[2],options:{showAuthor:!0,dotColors:{back:"#F4F4F4"}}})]}),u[0]&&(0,o.jsx)("section",{children:(0,o.jsx)(x.g4,{content:u[0]})}),j.children&&(n=>{let t=!1;for(let e of n)e.postCount>0&&(t=!0);return t})(j.children)&&(0,o.jsxs)("section",{style:{marginTop:"96px"},className:"site-padding",children:[(0,o.jsx)("h2",{children:"Sous th\xe8mes"}),(0,o.jsx)(TopicsHorizontalList,{topics:j.children})]}),(0,o.jsxs)(O,{className:"site-padding",children:[(0,o.jsx)("p",{className:"label-type",children:"Derni\xe8res ressources"}),(0,o.jsxs)("div",{className:"posts-container ",children:[(0,o.jsxs)(k,{children:[(0,o.jsx)("ul",{className:"list-container",children:null==d?void 0:null===(r=d.slice(f))||void 0===r?void 0:r.map((n,t)=>(0,o.jsx)("li",{className:"post-card-li",children:(0,o.jsx)(g.Z,{post:n,options:{showLead:!0,showDate:!0,showAuthor:!0}})},"post-".concat(t)))}),(0,o.jsx)(h.Z,{nbHits:p,baseUrl:"/categories/".concat(j.slug,"/ressources?page="),currentPage:1,options:{postPerPage:15}})]}),(0,o.jsx)(y,{children:(0,o.jsxs)("div",{className:"cornerstone-container",children:[u[1]&&(0,o.jsx)(b.Z,{post:u[1],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}}),u[2]&&(0,o.jsx)(b.Z,{post:u[2],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}})]})})]})]}),u[3]&&(0,o.jsx)("section",{children:(0,o.jsx)(x.g4,{content:u[3]})}),(0,o.jsx)(v,{})]})}let _=r.ZP.div.withConfig({componentId:"sc-f41f31ea-0"})(_topic_templateObject()),w=r.ZP.div.withConfig({componentId:"sc-f41f31ea-1"})(_topic_templateObject1(),l.U.desktop),v=r.ZP.div.withConfig({componentId:"sc-f41f31ea-2"})(_topic_templateObject2(),l.U.desktopXL),O=r.ZP.section.withConfig({componentId:"sc-f41f31ea-3"})(_templateObject3(),l.U.desktop),k=r.ZP.article.withConfig({componentId:"sc-f41f31ea-4"})(_templateObject4(),l.U.desktop),y=r.ZP.div.withConfig({componentId:"sc-f41f31ea-5"})(_templateObject5(),l.U.tablet,l.U.desktop)}},function(n){n.O(0,[755,764,291,915,211,749,774,888,179],function(){return n(n.s=8667)}),_N_E=n.O()}]);