"use strict";exports.id=7113,exports.ids=[7113],exports.modules={2558:(e,o,n)=>{n.d(o,{Z:()=>CondImage});var t=n(997),l=n(9755),a=n.n(l),u=n(4130);function CondImage({imageData:e,preserveAspectRatio:o,addClass:n,priority:l=!1,sizes:r=null}){let s=(0,u.k)(e);return s?o?t.jsx(a(),{src:s,layout:"intrinsic",height:e.height,width:e.width,alt:e.alternativeText||"",priority:l,className:`cond-image ${n}`,sizes:r}):t.jsx(a(),{className:`cond-image ${n}`,src:s,layout:"fill",objectFit:"cover",alt:e.alternativeText||"",priority:l,sizes:r},s):null}},635:(e,o,n)=>{n.d(o,{DG:()=>menuAsObj,MS:()=>getModuleWithShortName,O9:()=>getChannelSlug,fw:()=>modulesAsObj});let t={lead:"ComponentModuleLead",webinar:"ComponentModuleWebinar",podcast:"ComponentModulePodcast",journey:"ComponentModuleEmailJourney",formation:"ComponentModuleFormation",seo:"ComponentModuleSeo"};function getModuleWithShortName(e,o){return e.find(function(e){return e.__typename===t[o]})}function modulesAsObj(e){if(!e)return null;let o={};return e.forEach(e=>{switch(e.__typename){case"ComponentModuleLead":o.lead=e;break;case"ComponentModuleWebinar":o.webinar=e;break;case"ComponentModuleEmailJourney":o.journey=e;break;case"ComponentModuleEvent":o.event=e;break;case"ComponentModuleFormation":o.formation=e;break;case"ComponentModuleSeo":o.seo=e;break;case"ComponentModulePodcast":o.podcast=e}}),o}function menuAsObj(e){if(!e.length)return null;let o={groups:[],singles:[]};return e.forEach(e=>{if(e.label.includes("/")){let n=e.label.split("/")[0];for(let t of(o.groups.some(e=>e.name===n)||o.groups.push({name:n,items:[]}),o.groups))if(t.name===n){t.items.push({label:e.label.split("/")[1],value:e.value,type:e.type});break}}else o.singles.push(e)}),o}function getChannelSlug(e,o){let{webinar:n}=getModuleWithShortName(e.modules,o);return n?.slug||null}}};