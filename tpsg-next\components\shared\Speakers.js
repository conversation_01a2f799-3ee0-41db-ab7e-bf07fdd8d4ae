import styled from "styled-components";
import CondImage from "components/shared/condimage";
import { LineInfo } from "components/shared/atoms";


export default function Speakers({ speakers }) {

  let plusIndicatorValue = speakers.length - 2 > 0 ? speakers.length - 2 : null;

  let names = speakers
    .map(s => s.fullName)
    .splice(0, 2)
    .toString()
    .replaceAll(",", ", ");

  return (
    <SpeakersWrapper>
      <Pictures>
        {speakers.filter(speaker => speaker.picture).map((speaker, key) =>
          <div className={"speaker-picture"} key={`pic-${key}}`}>
            <CondImage imageData={speaker.picture}/>
          </div>
        )}
      </Pictures>
      <LineInfo label={"Orateur(s)"} info={names}/>
    </SpeakersWrapper>
  )
}

const SpeakersWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

const Pictures = styled.div`
  display: flex;
  flex-direction: row;
  margin-right: 16px;
  margin-left: 10px;
  .speaker-picture {
    position: relative;
    height: 50px;
    width: 50px;
    border-radius: 25px;
    margin-left: -10px;
    overflow: hidden;
  }
`

const TextWrapper = styled.div`
  
`;

const Names = styled.p`

`;

const Label = styled.p`
  font-size: 22px;
  font-weight: 500;
`;
