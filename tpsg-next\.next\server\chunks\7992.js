"use strict";exports.id=7992,exports.ids=[7992],exports.modules={7992:(o,t,r)=>{r.r(t),r.d(t,{default:()=>CKForm});var i=r(997),n=r(7518),e=r.n(n);function CKForm({title:o,desc:t,formString:r}){return r?(0,i.jsxs)(a,{children:[o&&i.jsx("h4",{className:"form-title",children:o}),t&&i.jsx("p",{className:"form-desc",children:t}),i.jsx("div",{dangerouslySetInnerHTML:{__html:`${r}`}})]}):i.jsx(i.Fragment,{})}let a=e().div.withConfig({componentId:"sc-4ad002d4-0"})`
  position: relative;
  width: 100%;
  .form-title {
    font-size: 42px;
    margin-top: 0;
    margin-bottom: 16px;
  }
  .form-desc {
    margin-top: 24px;
    margin-bottom: 24px;
    font-weight: 400;
    color: #161616;
    font-size: 22px;
  }
  .formkit-input {
    margin-bottom: 16px;
    background-color: #F0F0F0 !important;
    color: #161616 !important;
    border-radius: 0 !important;
    border: none !important;
    width: 100% !important;
    height: 52px;
    font-size: 18px !important;
    padding-left: 20px !important;
    &::placeholder {
      color: #888888 !important;
    }
  }
  .formkit-submit {
    margin-bottom: 16px;
    color: white !important;
    border: none !important;
    border-radius: 0 !important;
    background-color: #080808 !important;
    width: 100%;
    height: 52px;
    font-size: 18px !important;
    cursor: pointer;
    &:hover {
      background-color: var(--brand-color) !important;
    }
  }
  div {
    padding: 0 !important;
  }
`}};