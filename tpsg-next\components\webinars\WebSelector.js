import { PaginateContext } from "context/PaginateContext";
import { useContext, useState } from "react";
import styled from "styled-components";
import ChevronDown from "../svg/chevron-down";
import { device } from "styles/device";
import { getChannelSlug } from "utils/components.utils";
import { useRouter } from "next/router";

const WebSelector = ({ webinars, wepisodes }) => {
  const router = useRouter();
  const { query } = router;

  const [pageState, setPageState, setPage, nextNumber, setNextNumber] =
    useContext(PaginateContext);
  const [isOpen, setIsOpen] = useState(false);

  // Retourne le nombre de posts pour une chaine donnée
  function getEpisodeFilterCnt(channel) {
    const items = wepisodes.filter(
      (we) => getChannelSlug(we, "webinar") === channel
    );
    return items.length;
  }

  // Change page with webinarType selected in select
  function onMenuChange(option, value) {
    let filteredWebisodesCnt = getEpisodeFilterCnt(value);
    if (option === "activeChannel") {
      setPageState((prevState) => {
        return {
          ...prevState,
          totalItem: value === "tous" ? wepisodes.length : filteredWebisodesCnt,
          maxPage:
            value === "tous"
              ? Math.ceil(wepisodes.length / pageState.postPerPage)
              : Math.ceil(filteredWebisodesCnt / pageState.postPerPage),
          previousPage: null,
          activePage: 1,
          activeChannel: value,
        };
      });
    } else {
      setPageState((prevState) => {
        return {
          ...prevState,
          activeChannel: value,
        };
      });
    }
    setNextNumber(1);
    router.replace(
      {
        pathname: router.pathname,
        query: { ...query, page: 1 },
      },
      undefined,
      { scroll: false }
    );
  }

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const selectDropdownItem = (webinar) => {
    setIsOpen(false);
    onMenuChange("activeChannel", webinar.slug);
  };

  const selectedWebinar =
    webinars.find((webinar) => webinar.slug === pageState.activeChannel)?.name ||
    "Tout";

  return (
    <Wrapper>
      <Dropdown>
        <div className={"dd-header"} onClick={() => toggleDropdown()}>
          <p className={"dd-item"}>{selectedWebinar}</p>
          <ArrowIcon isOpen={isOpen}>
            <ChevronDown />
          </ArrowIcon>
        </div>
        {isOpen && (
          <div className={"dd-list"}>
            <p
              className={"dd-item"}
              onClick={() => selectDropdownItem({ slug: "tous" })}
            >
              Tout
            </p>
            {webinars.map((webinar, key) => (
              <p
                className={"dd-item"}
                key={key}
                onClick={() => selectDropdownItem(webinar)}
              >
                {webinar.name}
              </p>
            ))}
          </div>
        )}
      </Dropdown>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-bottom: 10px;
  margin-top: 50px;

  .menu-post-label {
    margin: 8px 0;
  }

  #menu-post-select {
    max-width: 50%;
    width: max-content;
  }
`;

const Dropdown = styled.div`
  position: relative;
  min-height: 42px;
  //background-color: #FBF0F2;
  font-size: 20px;
  cursor: pointer;
  border: 1px solid black;
  z-index: 2000;

  .dd-list {
    position: absolute;
    background-color: white;
    width: 100%;
    top: 42px;
    box-sizing: content-box;
    margin-left: -1px;
    margin-top: -1px;
    border-bottom: 1px solid black;
    border-left: 1px solid black;
    border-right: 1px solid black;
  }

  .dd-item {
    margin: 0;
    padding: 12px 16px 6px 12px;

    &:hover {
      background-color: #f3e8e8;
    }
  }

  @media ${device.desktop} {
    margin-right: 24px;
  }
`;

const ArrowIcon = styled.div`
  position: absolute;
  height: 42px;
  width: 42px;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(${(props) => (props.isOpen ? "180deg" : "0deg")});
`;

export default WebSelector;
