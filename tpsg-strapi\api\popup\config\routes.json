{"routes": [{"method": "GET", "path": "/popups", "handler": "popup.find", "config": {"policies": []}}, {"method": "GET", "path": "/popups/count", "handler": "popup.count", "config": {"policies": []}}, {"method": "GET", "path": "/popups/:id", "handler": "popup.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/popups", "handler": "popup.create", "config": {"policies": []}}, {"method": "PUT", "path": "/popups/:id", "handler": "popup.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/popups/:id", "handler": "popup.delete", "config": {"policies": []}}]}