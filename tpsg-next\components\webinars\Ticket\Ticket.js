import styled from "styled-components";
import { device } from "styles/device";
import { DisplayCardTitle } from "styles/styled-typography";
import TicketInfo from "./TicketInfo";
import { dateForHumans, hour } from "utils/date.utils";
import SvgDuotone from "components/shared/DuotoneFilter";
import CondImage from "components/shared/condimage";
import { QRCodeSVG } from "qrcode.react";
import ButtonLink from "components/shared/Buttons/ButtonLink";
import { useState } from "react";
import { NotchSvgBlack } from "./svg/NotchSvgBlack";
import { NotchSvgWhite } from "./svg/NotchSvgWhite";
import { NotchSvgBlackMobile } from "./svg/NotchSvgBlackMobile";
import { NotchSvgWhiteMobile } from "./svg/NotchSvgWhiteMobile";
import { useMediaQuery } from "hooks/UseMediaQuery";
import Image from "next/image";
import { modulesAsObj } from "../../../utils/components.utils";

export default function Ticket({ webEvent }) {

  // TornUp = Déchirée, il s'agit de l'animation qui détache le coupon.
  const [isTornUp, setIsTornUp] = useState(false);
  const isMobile = !useMediaQuery({ mediaQuery: device.tablet })

  let { event, webinar } = modulesAsObj(webEvent.modules);

  const speakers = formatedSpeakers(webinar.speakers || []);

  if(event && event.date) {
    event = {
      date: dateForHumans(event.date),
      hour: hour(event.date),
      url: event.url,
      passed: new Date(event.date) < new Date()
    }
  } else {
    event = {
      passed: true
    }
  }

  const Notch = ({ color }) => {
    return (
      <div className={"notch"}>
        <div className={"side-notch"}/>
        <div className={"mid-notch"}>
          {(color === "black" && !isMobile) && (<NotchSvgBlack/>)}
          {(color === "black" && isMobile) && (<NotchSvgBlackMobile/>)}
          {(color === "white" && !isMobile) && (<NotchSvgWhite/>)}
          {(color === "white" && isMobile) && (<NotchSvgWhiteMobile/>)}
        </div>
        <div className={"side-notch"}/>
      </div>
    );
  };

  return (
    <Background>
      <SvgDuotone hexLight={"#f5e3df"} hexDark={"#62187d"}/>
      <CondImage imageData={webEvent.image}/>
      <TicketWrapper>
        <PartOne>
          <Notch color={"black"}/>
          <div className={"ticket-content"}>
            <div>
              <DisplayCardTitle label={!event?.passed ? "Prochain webinaire" : "Dernier webinaire"} color={"#FCAAA1"}>
                {webEvent.title}
              </DisplayCardTitle>
              <div className={"buttons"}>
                <div
                  onMouseEnter={() => setIsTornUp(true)}
                  onMouseLeave={() => setIsTornUp(false)}
                >
                  {!event?.passed && (
                    <ButtonLink
                      type={"orange-register"}
                      text={"JE M'INSCRIS"}
                      url={event?.url || ""}
                    />
                  )}
                </div>
                <div
                  onMouseEnter={() => setIsTornUp(true)}
                  onMouseLeave={() => setIsTornUp(false)}
                >
                  <ButtonLink
                    type={"soft-white"}
                    text={!event?.passed ? "Infos" : "Revoir"}
                    url={`/webinaires/${webEvent.slug}`}
                  />
                </div>
              </div>
            </div>
            <div className={"event-infos"}>
              <TicketInfo label={"Orateur(s)"}>{speakers}</TicketInfo>
              { !event?.passed &&
                  <TicketInfo label={"Date"}>
                    <p>{event.date}</p>
                    <p>{event.hour}</p>
                  </TicketInfo>
              }
            </div>
          </div>
          <div className={"barcode"}>
            <Image
              src={"/images/ornaments/barcode.png"}
              alt={"image de code-barre"}
              className={"barcode"}
              layout={"fill"}
            />
          </div>
          <div className={"dash"}/>
        </PartOne>

        <PartTwo className={isTornUp ? "tornup" : "not-tornup"}>
          <div className={"dash"}/>
          <div className={"ticket-content"}>
            { !event?.passed &&
                <QRCodeSVG
                  className="qr-code"
                  value={event?.url || "https://toutpoursalgoire.com"}
                  size={98}
                  bgColor={"#ececec"}
                />
            }
            <div className={"buttons"}>
              {!event.passed && (
                <ButtonLink
                  type={"orange-register"}
                  text={"JE M'INSCRIS"}
                  url={event?.url || "https://toutpoursagloire.com"}
                />
              )}
              <ButtonLink
                type={"soft-dark"}
                text={!event?.passed ? "Infos" : "Revoir"}
                url={`/webinaires/${webEvent.slug}`}
              />
            </div>
          </div>
          <Notch color={"white"}/>
        </PartTwo>
      </TicketWrapper>
    </Background>
  );
}

const formatedSpeakers = (speakers) => {
  return speakers.map((speaker, key) => (
    <p key={key}>{shortName(speaker.fullName)}</p>
  ));
};

const shortName = (speaker) => {
  return `${speaker.charAt(0)}. ${speaker.split(/(\s+)/)[2]}`;
};


const dashMask = {
  mobile:
      "repeating-linear-gradient(90deg, #000 -1px, #000 8px, transparent 0, transparent 14px)",
  tablet:
      "repeating-linear-gradient(0deg, #000 -1px, #000 5px, transparent 0, transparent 12px)",
};

const Background = styled.div`
  position: relative;
  padding: var(--spacing-l) var(--border-space);
  background-color: #aa2dd6;
`;

const TicketWrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;

  .dash {
    height: 4px;
    width: 100%;
    -webkit-mask: ${dashMask.mobile};
    mask: ${dashMask.tablet};
  }

  .notch {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 30px;
    width: 100%;

    .side-notch {
      width: 100%;
      height: 100%;
    }

    .mid-notch {
      height: 30px;
      min-width: 150px;
    }
  }

  .ticket-content {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }

  @media ${device.tablet} {
    flex-direction: row;
    .dash {
      width: 4px;
      height: 100%;
      -webkit-mask: ${dashMask.tablet};
      mask: ${dashMask.tablet};
    }

    .notch {
      flex-direction: column;
      width: 30px;
      height: 100%;

      .mid-notch {
        /* height: 152px;
        width: 31px; */
        /* margin: -1px 0 -1px 0; */
        margin: -5px 0;
        width: 100%;
        height: 100%;
      }
    }

    .tornup {
      transition: transform 0.5s ease;
      transform: rotate(2.54deg) translate(6px, 8px);
    }

    .not-tornup {
      transition: transform 2s ease;
      transform: rotate(0deg) translate(0px, 0px);
    }
  }
`;

const PartOne = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 16px;
  overflow: hidden;

  .dash {
    margin: -1px 0 0 6px;
    background-color: #242424;
  }

  .side-notch {
    background-color: #242424;
    background-image: url(images/ornaments/noise.png); //no quotes probably fix problem of not loading images
  }

  .mid-notch {
    margin-right: -1px;
  }

  .barcode {
    position: absolute;
    height: 18px;
    transform: rotate(90deg);
    transform-origin: top right;
    bottom: 24px;
    right: 0;
  }

  .ticket-content {
    width: 100%;
    padding: 24px 42px 24px 24px;
    background-color: #242424;
    background-image: url(images/ornaments/noise.png);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .event-infos {
      margin-top: 24px;
      margin-right: 24px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }

    .buttons {
      display: none;
    }
  }

  @media ${device.tablet} {
    flex-direction: row;
    width: 75%;
    .barcode {
      height: 26px;
      transform: rotate(0deg);
      transform-origin: top left;
      top: 0;
      right: 32px;
      width: 160px;
    }

    .dash {
      margin: 7px 0 7px -1px;
    }

    .ticket-content {
      flex-direction: row;
      padding: 72px 32px 72px 42px;

      .event-infos {
        flex-direction: column;
        justify-content: start;
        min-width: 160px;
      }

      .buttons {
        margin-top: 32px;
        display: flex;
      }
    }
  }

  @media ${device.desktop} {
    .barcode {
      width: 200px;
      height: 32px;
      top: 0;
      right: 32px;
      transform: rotate(0deg);
      transform-origin: top left;
    }

    .ticket-content {
      .event-infos {
        min-width: 200px;
      }
    }
  }
`;

const PartTwo = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 16px;
  overflow: hidden;

  .dash {
    margin: 0 0 -1px 6px;
    background-color: #ececec;
  }

  .side-notch {
    background-color: #ececec;
  }

  .ticket-content {
    background-color: #ececec;

    .qr-code {
      display: none;
    }

    .buttons {
      display: flex;
      width: 100%;
      align-items: center;
      justify-content: center;
      padding: 24px;

      .button-link {
        width: 100%;
        text-align: center;
        margin: 0;

        &:first-child {
          border-radius: 8px 0 0 8px;
        }

        &:last-child {
          border-radius: 0 8px 8px 0;
        }
      }
    }
  }

  @media ${device.tablet} {
    flex-direction: row;
    width: 25%;
    .dash {
      margin: 7px -1px 7px 0;
    }

    .notch {
      margin-left: -1px;
    }

    .ticket-content {
      position: relative;
      background-blend-mode: multiply;
      display: flex;
      align-items: center;
      justify-content: center;

      .qr-code {
        display: inherit;
      }

      .buttons {
        display: none;
      }

      .image {
        position: absolute;
        top: 0;
        left: 0;
        width: calc(100% + 30px);
        height: 100%;
        background-size: cover;
        mix-blend-mode: darken;
        -moz-transform: scaleX(-1);
        -o-transform: scaleX(-1);
        -webkit-transform: scaleX(-1);
        transform: scaleX(-1);
        opacity: 0.2;
      }
    }
  }
`;
