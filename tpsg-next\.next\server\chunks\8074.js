"use strict";exports.id=8074,exports.ids=[8074],exports.modules={2333:(t,e,s)=>{s.d(e,{j:()=>topicsPostsfetcher});var r=s(723),i=s(2705);async function topicsPostsfetcher(t,e="",s=15){let o=await r.V.search("",{...(0,i.Rj)(t,s),attributesToRetrieve:["title","slug","type","published_at","author","image","route","date","lead","topics","cs"],filter:[e],sort:["date:desc"],cropLength:100});return o}},1632:(t,e,s)=>{s.d(e,{D:()=>FilterTopicsString});function FilterTopicsString(t){let e="";return t.map((t,s)=>{0!==s&&(e+=" OR "),e+=`topics="${t.name}"`}),e}}};