/**
 * Script pour réindexer manuellement les données de Strapi vers Meilisearch
 *
 * Pour exécuter ce script :
 * 1. Assurez-vous que Strapi et Meilisearch sont en cours d'exécution
 * 2. Exécutez la commande : node scripts/reindex.js
 */

const { MeiliSearch } = require('meilisearch');
const fetch = require('node-fetch');
require('dotenv').config();

// Connexion à Meilisearch
const client = new MeiliSearch({
  host: process.env.MEILI_HOST || 'http://localhost:7700',
  apiKey: process.env.MEILI_ADMIN_KEY || '',
});

async function reindex() {
  try {
    console.log('Démarrage de la réindexation...');

    // Supprimer l'index existant s'il existe
    try {
      await client.deleteIndex('post');
      console.log('Index existant supprimé');
    } catch (error) {
      console.log('Aucun index à supprimer ou erreur lors de la suppression:', error.message);
    }

    // Créer un nouvel index
    await client.createIndex('post');
    console.log('Nouvel index créé');

    // Configurer les attributs filtrables en utilisant fetch directement
    const filterableResponse = await fetch(`${process.env.MEILI_HOST || 'http://localhost:7700'}/indexes/post/settings/filterable-attributes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify([
        'title',
        'topics',
        'tags',
        'author',
        'type',
        'blog',
        'cs',
      ])
    });
    console.log('Attributs filtrables configurés:', filterableResponse.status);

    // Configurer les attributs triables en utilisant fetch directement
    const sortableResponse = await fetch(`${process.env.MEILI_HOST || 'http://localhost:7700'}/indexes/post/settings/sortable-attributes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(['date'])
    });
    console.log('Attributs triables configurés:', sortableResponse.status);

    // Configurer les attributs de recherche en utilisant fetch directement
    const searchableResponse = await fetch(`${process.env.MEILI_HOST || 'http://localhost:7700'}/indexes/post/settings/searchable-attributes`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify([
        'title',
        'body'
      ])
    });
    console.log('Attributs de recherche configurés:', searchableResponse.status);

    console.log('Configuration terminée. Veuillez redémarrer Strapi pour terminer la réindexation.');
  } catch (error) {
    console.error('Erreur lors de la réindexation:', error);
  }
}

// Exécuter le script
reindex()
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  });
