"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[923],{9587:function(n,t,e){e.d(t,{Z:function(){return HorizontalPostCard}});var i=e(2729),a=e(5893),o=e(4218),r=e(1664),l=e.n(r),c=e(9521),s=e(1304),p=e(7421),m=e(6368),d=e(4871),u=e(5158),f=e(279),g=e(3265),b=e(5675),h=e.n(b),x=e(1261);function _templateObject(){let n=(0,i._)(['\n  list-style: none;\n  position: relative;\n  width: 100%;\n\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n\n  &:after {\n    content: "";\n    display: block;\n    width: 100vw;\n    margin: 24px 0;\n    background-color: #40444444;\n    height: 1px;\n  }\n\n  .post-info {\n    width: 65%;\n    padding-right: 10px;\n  }\n  .post-image-container{\n    width: 30%;\n  }\n  .post-image {\n    width: 100%;\n    aspect-ratio: 1/1;\n    position: relative;\n  }\n  .card-topics{\n    position: relative;\n    display: flex;\n    flex-direction: row;\n    align-items: start;\n    justify-content: start;\n    flex-wrap: wrap;\n    margin-top: 16px;\n    height: 32px;\n    width: 100%;\n    overflow: hidden;\n    gap: 8px;\n  }\n  @media screen and (max-width: 320px) {\n    // Little screen only\n    .post-info {\n      width: 100%;\n    }\n  }\n  @media ',' {\n    &:after {\n      content: "";\n      display: block;\n      width: 100%;\n      margin: 37px 0 30px 0;\n\n      background-color: #40444444;\n      height: 1px;\n    }\n    .post-info {\n      width: 75%;\n      padding-right: 10px;\n    }\n    .post-image-container{\n      width: 20%;\n    }\n  }\n']);return _templateObject=function(){return n},n}function HorizontalPostCard(n){let{post:t,options:e}=n,i=!(0,g.a)({mediaQuery:p.U.tablet}),r="",c="",b=(0,d.qt)(t),v=(0,u.tm)(b);if((null==e?void 0:e.showDate)&&(r+=t.date?(0,o.S$)(t.date):(0,o.S$)(t.published_at)),(null==e?void 0:e.showAuthor)&&(null==t?void 0:t.author)){var j;r+=(""!==r?" - ":"")+((null===(j=t.author)||void 0===j?void 0:j.fullName)?t.author.fullName:t.author)}return(null==e?void 0:e.showLead)&&(c=(0,d.mj)(t)),(0,a.jsxs)(w,{children:[(0,a.jsxs)("div",{className:"post-info",children:[(0,a.jsx)(m.My,{children:r}),(0,a.jsxs)(x.Z,{link:b,children:[(0,a.jsxs)(m.kz,{className:"primary-hover",children:[t.title,v&&(0,a.jsx)("span",{children:(0,a.jsx)(h(),{src:"/images/icons/external.svg",alt:"external",fill:!0})})]}),!i&&c&&(0,a.jsx)(m.X0,{children:c})]}),e.showTopics&&(0,a.jsx)("div",{className:"card-topics",children:t.topics&&t.topics.map((n,t)=>(0,a.jsx)(f.Z,{text:n},t))})]}),(0,a.jsx)(l(),{href:b,passHref:!0,className:"post-image-container",children:(0,a.jsx)("div",{className:"post-image",children:(0,a.jsx)(s.Z,{imageData:t.image})})})]})}let w=c.ZP.li.withConfig({componentId:"sc-c8777780-0"})(_templateObject(),p.U.tablet)},6874:function(n,t,e){e.d(t,{Z:function(){return AnimatedList}});var i=e(2729),a=e(5893),o=e(9450),r=e(7294),l=e(9521);function _templateObject(){let n=(0,i._)(["\n  width: 100%;\n  overflow: hidden;\n  scroll-behavior: auto !important;\n  min-height: 100vh;\n  @keyframes backward-list-animation { \n    from { \n      opacity: 1\n    } to {\n      transform: translateY(-300px);\n      opacity: 0;\n    } \n  }\n  @keyframes frontward-list-animation { \n    from { \n      opacity: 1\n    } to { \n      transform: translateY(300px);\n      opacity: 0;\n    } \n  }\n  @keyframes backward-list-animation-reverse { \n    from { \n      opacity: 0;\n      transform: translateY(-300px);\n    } to { \n      opacity: 1;\n      transform: translateY(0);\n    } \n  }\n  @keyframes frontward-list-animation-reverse { \n    from {\n      opacity: 0;\n      transform: translateY(300px);\n    } to { \n      transform: translateY(0);\n      opacity: 1;\n    }\n  }\n  .backward-list {\n    animation: backward-list-animation forwards 500ms;\n  }\n  .frontward-list {\n    animation: frontward-list-animation forwards  500ms;\n  }\n  .backward-list-reverse {\n    animation: backward-list-animation-reverse forwards 500ms;\n  }\n  .frontward-list-reverse {\n    animation: frontward-list-animation-reverse forwards  500ms;\n  }\n  .animation-init{\n    display: none\n  }\n"]);return _templateObject=function(){return n},n}function AnimatedList(n){let{animationTransition:t,setAnimationTransition:e,children:i}=n,[l,s]=(0,r.useContext)(o.b),[p,m]=(0,r.useState)("animation-init");return(0,r.useEffect)(()=>{function doneAnimation(){e(n=>({...n,transitioning:"done"})),s(n=>({...n,loading:!1}))}let n="fetched"===t.transitioning?"-reverse":"";if("init"===t.transitioning&&null===t.direction)m("backward-list-reverse"),doneAnimation();else if("init"===t.transitioning&&null!==t.direction){var i;document.getElementsByTagName("html")[0].style.scrollBehavior="auto",null===(i=document.getElementById("top-animated-list"))||void 0===i||i.scrollIntoView(),document.getElementsByTagName("html")[0].style.scrollBehavior=null,m(t.direction<0?"backward-list".concat(n):"frontward-list".concat(n))}else"fetched"===t.transitioning&&null!==t.direction&&(m(t.direction>0?"backward-list".concat(n):"frontward-list".concat(n)),doneAnimation())},[t,e]),(0,a.jsx)(c,{id:"top-animated-list",children:(0,a.jsx)("div",{className:"animation-list ".concat(p),children:i})})}let c=l.ZP.div.withConfig({componentId:"sc-add37874-0"})(_templateObject())},279:function(n,t,e){var i=e(2729),a=e(5893),o=e(9521),r=e(5158),l=e(1664),c=e.n(l);function _templateObject(){let n=(0,i._)(['\n  position: relative;\n  font-family: Switzer, "Helvetica Neue", Helvetica, "Arial", sans-serif;\n  padding: 5px 11px 5px 11px;\n  font-size: 11px;\n  letter-spacing: 0.2px;\n  border-radius: 100px;\n  text-transform: uppercase;\n  white-space: nowrap;\n  margin-bottom: 8px;\n\n  border: 1px solid #464646;\n  background-color: transparent;\n  color: #464646;\n\n  &:hover {\n    background-color: #242424;\n    color: #f4f4f4;\n  }\n\n  cursor: pointer;\n  ']);return _templateObject=function(){return n},n}t.Z=n=>{let{text:t}=n,e="/categories/"+(0,r.lV)(t);return(0,a.jsx)(c(),{href:e,legacyBehavior:!0,children:(0,a.jsx)(s,{className:"card-label",children:t})})};let s=o.ZP.span.withConfig({componentId:"sc-e62d7975-0"})(_templateObject())},1304:function(n,t,e){e.d(t,{Z:function(){return CondImage}});var i=e(5893),a=e(9755),o=e.n(a),r=e(3071);function CondImage(n){let{imageData:t,preserveAspectRatio:e,addClass:a,priority:l=!1,sizes:c=null}=n,s=(0,r.k)(t);return s?e?(0,i.jsx)(o(),{src:s,layout:"intrinsic",height:t.height,width:t.width,alt:t.alternativeText||"",priority:l,className:"cond-image ".concat(a),sizes:c}):(0,i.jsx)(o(),{className:"cond-image ".concat(a),src:s,layout:"fill",objectFit:"cover",alt:t.alternativeText||"",priority:l,sizes:c},s):null}},1665:function(n,t,e){e.d(t,{Z:function(){return StickyPagination}});var i=e(2729),a=e(5893),o=e(9521),r=e(7421),l=e(7294),c=e(1664),s=e.n(c),p=e(9450);function _templateObject(){let n=(0,i._)(["\n  display: flex;\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  .link-btn {\n    display: block;\n    width: 42px;\n    user-select: none;\n    -webkit-user-drag: none;\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  transform: rotate(-180deg);\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  opacity: 0.5;\n  cursor: default;\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  overflow: hidden;\n  height: 42px;\n  width: 42px;\n  margin-right: 16px;\n  border-radius: 100px;\n  background-color: var(--soft-white);\n  border: 1px solid var(--soft-dark);\n  display: flex;\n  align-items: center;\n  justify-items: center;\n  cursor: pointer;\n  ",";\n  ",";\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  margin: auto;\n  height: 66%;\n  width: 66%;\n\n  svg {\n    height: 100%;\n    width: 100%;\n    transform: scale(1);\n    transition: 350ms;\n\n    path {\n      stroke: var(--soft-white);\n      stroke-width: 0;\n      transition: 350ms;\n    }\n  }\n  ",";\n"]);return _templateObject5=function(){return n},n}function _templateObject6(){let n=(0,i._)(["\n  svg {\n    transform: rotate(45deg) scale(3.2);\n    path {\n      stroke-width: 1px;\n    }\n  }\n"]);return _templateObject6=function(){return n},n}function NavButton(n){let{reverse:t,direction:e,url:i,shallow:o}=n,[r,c,m,u]=(0,l.useContext)(p.b),f=e>=1?r.activePage>=r.maxPage:r.activePage<2;return(0,a.jsx)(d,{children:(0,a.jsx)(s(),{href:"".concat(i,"?page=").concat(u+e),shallow:o,onClick:n=>{f||r.loading?n.preventDefault():n.ctrlKey||m(r.activePage+e)},className:"link-btn",replace:!0,children:(0,a.jsx)(g,{disabled:f,reverse:t,children:(0,a.jsx)(b,{disabled:f,children:(0,a.jsx)(ArrowSvg,{})})})})})}function NavigationButtons(n){let{url:t,shallow:e=!0}=n;return(0,a.jsxs)(m,{children:[(0,a.jsx)(NavButton,{reverse:!0,direction:-1,shallow:e,url:t}),(0,a.jsx)(NavButton,{reverse:!1,direction:1,shallow:e,url:t})]})}e(1163);let m=o.ZP.div.withConfig({componentId:"sc-cf6556c-0"})(_templateObject()),d=o.ZP.div.withConfig({componentId:"sc-cf6556c-1"})(_templateObject1()),u=(0,o.iv)(_templateObject2()),f=(0,o.iv)(_templateObject3()),g=o.ZP.div.withConfig({componentId:"sc-cf6556c-2"})(_templateObject4(),n=>n.disabled&&f,n=>n.reverse?u:null),b=o.ZP.div.withConfig({componentId:"sc-cf6556c-3"})(_templateObject5(),n=>n.disabled?h:null),h=(0,o.iv)(_templateObject6()),ArrowSvg=()=>(0,a.jsx)("svg",{width:"100",height:"100",viewBox:"0 0 32 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M27.4626 7.30763C24.512 5.89173 22.0553 3.57515 20.4805 0.654073L21.7083 -1.04433e-06C23.6383 3.57991 27.0225 6.16306 31 7.09236L32 7.326L32 8.674L31 8.90764C27.0225 9.83694 23.6383 12.4201 21.7083 16L20.4805 15.3459C22.0553 12.4249 24.512 10.1083 27.4626 8.69236L9.78226e-07 8.69236L1.09928e-06 7.30763L27.4626 7.30763Z",fill:"black"})});function AnimatedNumber_templateObject(){let n=(0,i._)(["\n   overflow: hidden;\n   width: ",";\n   @media "," {\n      width: ",";\n   }\n"]);return AnimatedNumber_templateObject=function(){return n},n}function AnimatedNumber_templateObject1(){let n=(0,i._)(["\n   align-items: center;\n   display: flex;\n   flex-direction: column;\n   height: ",";\n   p {\n      padding-top: 3px;\n      font-weight: 700;\n      margin: 0;\n      width: ",";\n      height: ",";\n   \n      color: var(--soft-dark);\n      font-size: ",";\n   }\n   \n   transition: 1000ms cubic-bezier(1, 0.72, 0.15, 1.01);\n   transform: translateY(","px);\n\n\n   @media "," {\n      height: ",";\n      transform: translateY(","px);\n      p {\n         width: ",";\n         height: ",";\n         font-size: ",";\n      }\n   }\n"]);return AnimatedNumber_templateObject1=function(){return n},n}function AnimatedNumber(n){let{activePage:t,totalPage:e,gapSize:i="100px",gapSizeMobile:o="50px"}=n,r=[...Array(e).keys()];return(0,a.jsx)(x,{gapSize:i,gapSizeMobile:o,children:(0,a.jsx)(w,{gap:-(t-1)*i.replace(/\D+/g,""),gapSize:i,gapMobile:-(t-1)*o.replace(/\D+/g,""),gapSizeMobile:o,children:r.map(n=>(0,a.jsx)("div",{className:"number-item",children:(0,a.jsx)("p",{children:n+1})},n))})})}let x=o.ZP.div.withConfig({componentId:"sc-c4f5ad08-0"})(AnimatedNumber_templateObject(),n=>80*n.gapSizeMobile/100,r.U.tablet,n=>80*n.gapSize/100),w=o.ZP.div.withConfig({componentId:"sc-c4f5ad08-1"})(AnimatedNumber_templateObject1(),n=>n.gapSizeMobile,n=>n.gapSizeMobile,n=>n.gapSizeMobile,n=>n.gapSizeMobile,n=>n.gapMobile,r.U.tablet,n=>n.gapSize,n=>n.gap,n=>n.gapSize,n=>n.gapSize,n=>n.gapSize);function PaginateSection_templateObject(){let n=(0,i._)(['\n  max-width: 265px;\n  user-select: none;\n  .pagestate-header {\n    display: flex;\n    justify-content: space-between;\n  }\n  [class*="NavigationButtons__Circle"] {\n    opacity: 0.8;\n  }\n  @media ',' {\n    [class*="NavigationButtons__Circle"] {\n      opacity: 0.5;\n    }\n  }\n']);return PaginateSection_templateObject=function(){return n},n}function PaginateSection(n){let{gapSize:t,gapSizeMobile:e,url:i=""}=n,[o,r,c,s]=(0,l.useContext)(p.b);return(0,a.jsxs)(v,{children:[(0,a.jsxs)("header",{className:"pagestate-header",children:[(0,a.jsx)("p",{className:"podcast-secondary-text mobile-hide",children:"Page ".concat(o.activePage,"/").concat(o.maxPage)}),(0,a.jsx)(NavigationButtons,{url:i,shallow:!0})]}),(0,a.jsx)("main",{className:"pagestate-main",children:(0,a.jsx)(AnimatedNumber,{activePage:s,totalPage:o.maxPage,gapSize:t,gapSizeMobile:e})})]})}let v=o.ZP.section.withConfig({componentId:"sc-fb435624-0"})(PaginateSection_templateObject(),r.U.tablet);var j=e(6368);function StickyPagination_templateObject(){let n=(0,i._)(["\n    width: 100%;\n    height: calc(100% - 350px);\n    //overflow: auto;\n"]);return StickyPagination_templateObject=function(){return n},n}function StickyPagination_templateObject1(){let n=(0,i._)(['\n  position: sticky;\n  top: 0;\n  padding-top: 20px;\n  width: 100%;\n  display: flex;\n  flex-wrap: wrap;\n  flex-direction: column;\n  flex-wrap: wrap;\n\n  .paginate-section-container {\n    display: flex;\n    justify-content: flex-end;\n    align-items: center;\n    flex-wrap: wrap;\n    width: 100%;\n    position: fixed;\n    margin-top: 0;\n    bottom: 0;\n    left: 0;\n    height: 79px;\n    background-color: rgba(244, 244, 244, 0.95);\n\n    @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {\n      background-color: rgba(244, 244, 244, 0.8);\n      -webkit-backdrop-filter: blur(10px);\n      backdrop-filter: blur(10px);\n    }\n\n    section {\n      display: flex;\n      flex-direction: row-reverse;\n    }\n    .pagestate-header div[class*="NavigationButtons__Wrapper"] {\n      margin-right: 24px;\n      width: 140px;\n      display: flex;\n      justify-content: space-around;\n    }\n    .pagestate-main div[class*="AnimatedNumber__Wrapper"] {\n      display: flex;\n      justify-content: start;\n    }\n    .pagestate-main {\n      margin-right: 17px;\n    }\n\n    &:before {\n      content: "";\n      position: absolute;\n      right: 0;\n      top: 0;\n      height: 1px;\n      width: 40%;\n      border-top: 1px solid black;\n    }\n  }\n  @media ',' {\n    @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {\n      -webkit-backdrop-filter: blur(0);\n      backdrop-filter: blur(0);\n    }\n\n    padding-top: 40px;\n    height: 100vh;\n    p {\n      display: block;\n    }\n\n    .paginate-section-container {\n      position: absolute;\n      bottom: 0;\n      height: auto;\n      background-color: transparent;\n      margin-top: 50px;\n      display: block;\n\n      &:before {\n        display: none;\n      }\n\n      section {\n        display: block;\n      }\n\n      .pagestate-header div[class*="NavigationButtons__Wrapper"] {\n        margin-right: 0;\n      }\n\n      .pagestate-main {\n        margin-right: 0;\n      }\n    }\n  }\n']);return StickyPagination_templateObject1=function(){return n},n}function StickyPagination(n){let{url:t,title:e,children:i}=n;return(0,a.jsxs)(k,{children:[(0,a.jsx)("header",{children:(0,a.jsx)(j.GN,{children:e})}),(0,a.jsx)(_,{children:i}),(0,a.jsx)("footer",{className:"paginate-section-container",children:(0,a.jsx)(PaginateSection,{gapSize:"200px",gapSizeMobile:"42px",url:t})})]})}let _=o.ZP.main.withConfig({componentId:"sc-214d6b3-0"})(StickyPagination_templateObject()),k=o.ZP.div.withConfig({componentId:"sc-214d6b3-1"})(StickyPagination_templateObject1(),r.U.tablet)},9450:function(n,t,e){e.d(t,{b:function(){return a}});var i=e(7294);let a=(0,i.createContext)()},3265:function(n,t,e){e.d(t,{a:function(){return useMediaQuery}});var i=e(7294);let useMediaQuery=n=>{let{width:t,mediaQuery:e}=n,[a,o]=(0,i.useState)(!1),r=(0,i.useCallback)(n=>{n.matches?o(!0):o(!1)},[]);return(0,i.useEffect)(()=>{let n=window.matchMedia(t?"(max-width: ".concat(t,"px)"):e);return n.addEventListener("change",r),n.matches&&o(!0),()=>n.removeEventListener("change",r)},[]),a}},6368:function(n,t,e){e.d(t,{DZ:function(){return f},GN:function(){return p},My:function(){return u},NZ:function(){return s},V1:function(){return l},X0:function(){return d},bP:function(){return c},hQ:function(){return g},kz:function(){return m}});var i=e(2729),a=e(9521),o=e(7421);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n  font-weight: 500;\n  color: #161616;\n  font-size: 48px;\n  margin-top:  calc(var(--spacing-l) - 48px * ",");\n  margin-bottom: calc(var(--spacing-l) - 48px * ",");\n  \n  @media "," {\n    font-size: 72px;\n    margin-top:  calc(var(--spacing-l) - 72px * ",");\n    margin-bottom: calc(var(--spacing-l) - 72px * ",");\n  }\n  @media "," {\n    font-size: 104px;\n    margin-top:  calc(var(--spacing-l) - 104px * ",");\n    margin-bottom: calc(var(--spacing-l) - 104px * ",");\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  box-sizing: content-box;\n  color: #ececec;\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: clamp(24px, 1.125rem + 1vw, 32px);\n  font-weight: 400;\n  max-width: 600px;\n  line-height: 105%;\n  &:before {\n    display: block;\n    margin-bottom: 8px;\n    content: '","';\n    color: ",";\n    font-size: 18px;\n  }\n  @media "," {\n    font-size: 32px;\n    margin-right: 24px;\n  }\n  @media "," {\n    font-weight: 400;\n    margin-right: 48px;\n    font-size: clamp(32px, 3.5vw, 48px);\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(['\n  \n  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);\n  \n  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;\n  font-size: var(--title-size);\n  color: ',";\n  font-weight: 400;\n  margin-top: 0;\n  margin-bottom: calc( var(--title-size) * -",");\n  \n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  font-size: 24px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 48px;\n    margin-bottom: 16px;\n    margin-top: 16px;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  font-size: 20px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media "," {\n    font-size: 30px;\n  }\n  span {\n    position: relative;\n    margin-left: 8px;\n    display: inline-block;\n    margin-bottom: -2px;\n    height: 22px;\n    width: 22px;\n  }\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  font-size: 16px;\n  color: #888888;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding-right: 30px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media screen and (max-width:320px){ // Little screen only\n    padding: 0;\n  }\n  @media "," {\n    font-size:20px;\n  }\n"]);return _templateObject5=function(){return n},n}function _templateObject6(){let n=(0,i._)(['\n  font-size: 14px;\n  font-style: italic;\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  color: #7a7a7a;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media '," {\n    font-size: 17px;\n  }\n"]);return _templateObject6=function(){return n},n}function _templateObject7(){let n=(0,i._)(["\n  font-size: 38px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 64px;\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n  @media "," {\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n"]);return _templateObject7=function(){return n},n}function _templateObject8(){let n=(0,i._)(["\n  color: #161616;\n  letter-spacing: 0.04em;\n  margin-top: 48px;\n  text-transform: uppercase;\n  @media "," {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 22px;\n    font-weight: 500;\n    &:hover {\n      color: var(--brand-color);\n    }\n  }\n"]);return _templateObject8=function(){return n},n}function _templateObject9(){let n=(0,i._)(["\n  font-weight: 400;\n  font-size: 26px;\n  margin: 0;\n  color: #161616;\n  @media "," {\n    font-size: 32px;\n  }\n"]);return _templateObject9=function(){return n},n}let r={topSpace:.096,minBottomSpace:.2788,maxBottomSpace:.4711},l=a.ZP.h1.withConfig({componentId:"sc-a3af5335-0"})(_templateObject(),r.topSpace,r.minBottomSpace,o.U.tablet,r.topSpace,r.minBottomSpace,o.U.desktop,r.topSpace,r.minBottomSpace),c=a.ZP.h2.withConfig({componentId:"sc-a3af5335-1"})(_templateObject1(),n=>n.label,n=>n.color,o.U.tablet,o.U.desktop),s=a.ZP.h2.withConfig({componentId:"sc-a3af5335-2"})(_templateObject2(),n=>n.light?"var(--c-soft-cream)":"var(--soft-dark)",r.maxBottomSpace),p=a.ZP.h2.withConfig({componentId:"sc-a3af5335-3"})(_templateObject3(),o.U.tablet),m=a.ZP.h4.withConfig({componentId:"sc-a3af5335-4"})(_templateObject4(),o.U.tablet),d=a.ZP.p.withConfig({componentId:"sc-a3af5335-5"})(_templateObject5(),o.U.tablet),u=a.ZP.p.withConfig({componentId:"sc-a3af5335-6"})(_templateObject6(),o.U.tablet),f=a.ZP.h1.withConfig({componentId:"sc-a3af5335-7"})(_templateObject7(),o.U.tablet,o.U.desktop),g=a.ZP.p.withConfig({componentId:"sc-a3af5335-8"})(_templateObject8(),o.U.desktop);a.ZP.p.withConfig({componentId:"sc-a3af5335-9"})(_templateObject9(),o.U.tablet)},785:function(n,t,e){e.d(t,{DG:function(){return menuAsObj},MS:function(){return getModuleWithShortName},O9:function(){return getChannelSlug},fw:function(){return modulesAsObj}});let i={lead:"ComponentModuleLead",webinar:"ComponentModuleWebinar",podcast:"ComponentModulePodcast",journey:"ComponentModuleEmailJourney",formation:"ComponentModuleFormation",seo:"ComponentModuleSeo"};function getModuleWithShortName(n,t){return n.find(function(n){return n.__typename===i[t]})}function modulesAsObj(n){if(!n)return null;let t={};return n.forEach(n=>{switch(n.__typename){case"ComponentModuleLead":t.lead=n;break;case"ComponentModuleWebinar":t.webinar=n;break;case"ComponentModuleEmailJourney":t.journey=n;break;case"ComponentModuleEvent":t.event=n;break;case"ComponentModuleFormation":t.formation=n;break;case"ComponentModuleSeo":t.seo=n;break;case"ComponentModulePodcast":t.podcast=n}}),t}function menuAsObj(n){if(!n.length)return null;let t={groups:[],singles:[]};return n.forEach(n=>{if(n.label.includes("/")){let e=n.label.split("/")[0];for(let i of(t.groups.some(n=>n.name===e)||t.groups.push({name:e,items:[]}),t.groups))if(i.name===e){i.items.push({label:n.label.split("/")[1],value:n.value,type:n.type});break}}else t.singles.push(n)}),t}function getChannelSlug(n,t){let{webinar:e}=getModuleWithShortName(n.modules,t);return(null==e?void 0:e.slug)||null}},4871:function(n,t,e){e.d(t,{mj:function(){return getPostLead},pL:function(){return getPostSpeakers},qt:function(){return getPostRoute}});var i=e(785),a=e(5158),o=e(3071);function getPostRoute(n){if(n.route)return n.route.startsWith("/")?n.route:"/"+n.route;let t=n.type||"undefined";switch(t){case"article":default:return"/article/".concat(n.slug);case"webinaire":return"/webinaires/".concat(n.slug);case"podcast":let{podcast:e}=(0,i.fw)(n.modules);if(!(null==e?void 0:e.podcast))return null;return"/podcasts/".concat(e.podcast.slug,"/").concat(n.slug);case"formation":var a,o;return(null===(o=n.modules)||void 0===o?void 0:null===(a=o.formation)||void 0===a?void 0:a.link)||"/article/".concat(n.slug);case"parcours":return"/parcours-emails/".concat(n.slug)}}function getPostLead(n){let t="";if(n.lead&&""!==n.lead)t=n.lead;else{var e,o;let r=(0,i.fw)(n.modules);t=(null==r?void 0:null===(e=r.lead)||void 0===e?void 0:e.content)?r.lead.content:(null===(o=n.body)||void 0===o?void 0:o.slice(0,255))||"",t=(0,a.Gq)((0,a.Kd)(t))}return t}function getPostSpeakers(n){let t;let e=(0,i.fw)(n.modules);if((null==e?void 0:e.webinar)&&(t=e.webinar.speakers),(null==e?void 0:e.formation)&&(t=e.formation.speakers),!t)return null;let a={pictures:[],names:[]};for(let n of t)a.pictures.push((0,o.k)(n.picture)),a.names.push("".concat(n.firstName.charAt(0),". ").concat(n.lastName));return{pictures:a.pictures,names:function(n){let t=n.length>2?", ":" et ",e="";for(let[i,a]of n.entries())i>0?(e+=t+a,t=" et "):e+=a;return e}(a.names)}}}}]);