import styled from "styled-components";
import { useContext, useEffect, useState } from "react";
import { device } from "styles/device";
import NavigationButtons from "components/shared/pagination/sticky-pagination/NavigationButtons";
import AnimatedNumber from "components/shared/pagination/sticky-pagination/AnimatedNumber";
import { PaginateContext } from "context/PaginateContext";
import { useRouter } from "next/router";

export default function PaginateSection({ gapSize, gapSizeMobile, url = "" }) {
  const [pageState, setPageState, setPage, nextNumber] =
    useContext(PaginateContext);
    
  return (
    <SectionPaginate>
      <header className="pagestate-header">
        <p className="podcast-secondary-text mobile-hide">{`Page ${pageState.activePage}/${pageState.maxPage}`}</p>
        <NavigationButtons url={url} shallow={true} />
      </header>
      <main className="pagestate-main">
        <AnimatedNumber
          activePage={nextNumber}
          totalPage={pageState.maxPage}
          gapSize={gapSize}
          gapSizeMobile={gapSizeMobile}
        />
      </main>
    </SectionPaginate>
  );
}

const SectionPaginate = styled.section`
  max-width: 265px;
  user-select: none;
  .pagestate-header {
    display: flex;
    justify-content: space-between;
  }
  [class*="NavigationButtons__Circle"] {
    opacity: 0.8;
  }
  @media ${device.tablet} {
    [class*="NavigationButtons__Circle"] {
      opacity: 0.5;
    }
  }
`;
