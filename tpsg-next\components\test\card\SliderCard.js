import Image from "next/image";
import styled from "styled-components";
import { device } from "styles/device";
import Link from "next/link";
import { useRouter } from "next/router";

/**
 *
 * @param {object}  post
 * @param {string}  post.title
 * @param {string}  post.image
 * @param {string}  post.author
 * @param {string}  post.authorImage
 * @param {string}  post.date
 * @param {string}  post.route
 * @param {object}  options
 * @param {string}  options.theme "dark" or "light"
 * @param {boolean} options.invert
 * @return {JSX.Element}
 * @constructor
 */

export default function SliderCard({ post, options }) {

  const router = useRouter();

  if(!post.route) return;

  return (
    <SliderCardWrapper theme={options?.theme} >
      <CardInner className={"sc-inner"} theme={options?.theme} invert={options?.invert}>
        <div className={"sc-image"}>
          <Image
            src={post.image}
            style={imageStyle}
            fill
            alt={""}
            sizes="(max-width: 768px) 80vw, (max-width: 1220px) 33vw, 20vw"/>
        </div>
        <Link href={post.route}>
          <div className={"clickable-area"}></div>
        </Link>
        <div className={"sc-text"}>
          <h2 className="sc-title">{post.title}</h2>
          <Link href={post.authorLink || ""}>
            <div className={"sc-footer"}>
              <div className={"sc-author_image"}>
                { post.authorImage && <Image
                  src={post.authorImage}
                  fill
                  alt={""}
                  style={imageStyle}
                  sizes="40px"/>
                }
              </div>
              <div className="sc-more">
                <p className="sc-author">{post.author}</p>
                <p className="sc-date">{post.date}</p>
              </div>
            </div>
          </Link>
        </div>
      </CardInner>
    </SliderCardWrapper>

  )
}

const imageStyle = {
  objectFit: "cover"
}


const SliderCardWrapper = styled.li`,
  position: relative;
  list-style: none;
  min-width: 292px;
  padding-left: 16px;
  overflow-y: visible;

  &:last-child {
    margin-right: 24px;
  }
  
  &:first-child {
    min-width: calc(292px - 16px + var(--border-space));
    padding-left: var(--border-space);
  }

  @media ${device.desktop} {
    min-width: calc((100% - 24px * 2) / 3);
    padding: 0 !important;
    margin-right: 24px;

    &:last-child {
      margin-right: 0;
    }

    &:first-child {
      min-width: calc((100% - 24px * 2) / 3);
      padding-left: 40px;
    }
  }

  @media (min-width: 1220px) {
    min-width: calc((100% - 26px * 3) / 4);

    &:first-child {
      min-width:  calc((100% - 26px * 3) / 4);
      padding-left: 40px;
    }
  }

`;


const CardInner = styled.div`
  position: relative;
  overflow-y: visible;
  width: 100%;
  border: 1px solid ${(p) => (p.theme === "dark" ?
    "rgba(250,247,243,0.2)" : "rgba(22,22,22,0.2)")}; // TODO: var here

  .clickable-area {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 1;

    &:hover {
      cursor: pointer;
      border: 1px solid var(--c-brand-lighter);
      -webkit-box-shadow: 0 0 0 1px var(--c-brand-ligher);
      box-shadow: 0 0 0 1px var(--c-brand-lighter);
    }
    
  }

  .sc-image {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    background-color: taransparent;
  }

  .sc-text {
    padding: clamp(1.5rem, 0.27rem + 1.92vw, 2rem);
  }

  .sc-title {
    color: ${p => p.theme === "dark" ? "var(--c-soft-cream)" : "#161616"};
    margin: 0;
    font-weight: ${p => p.theme === "dark" ? 400 : 500};
    height: clamp(4.5rem, 2.65rem + 2.88vw, 5.25rem);
    font-family: Switzer, sans-serif;
    font-size: clamp(1.125rem, 0.82rem + 0.48vw, 1.25rem);
    line-height: clamp(1.5rem, 0.88rem + 0.96vw, 1.75rem);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .sc-footer {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-top: clamp(3.5rem, 1.03rem + 3.85vw, 4.5rem);

    z-index: 2;

    &:hover {
      .sc-author {
        color: var(--c-brand-light);
      }
    }
  }

  .sc-author_image {
    position: relative;
    overflow: hidden;
    border-radius: 90px;
    width: clamp(2rem, 0.77rem + 1.92vw, 2.5rem);
    height: clamp(2rem, 0.77rem + 1.92vw, 2.5rem);
    margin-right: 12px;
    background-color: ${p => p.invert ? "var(--c-soft-cream)" : "#161616"};

    img {
      filter: ${p => p.invert ? "invert()" : "none"};
    }
  }

  .sc-more {
    flex-wrap: wrap;
    display: flex;
    flex-direction: column;
    margin-top: -2px;
    gap: 4px;

    .sc-author,
    .sc-date {
      margin: 0;
      font-size: clamp(0.875rem, 0.56rem + 0.48vw, 1rem);
      font-family: Switzer, sans-serif;
    }

    .sc-author {
      color: ${(p) => (p.theme === "dark" ? "#FAF7F3" : "#161616")};
      font-weight: 400;
    }

    .sc-date {
      margin-top: -2px;
      color: #989AA4;
    }
  }

  &:active {
    border: 1px solid var(--c-brand-lighter);
  }

  //transition: all 250ms ease-out;

  @media ${device.desktop} {
    margin: 2px 2px;
  }
`;