const fetch = require('node-fetch');

const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function fixFilterable() {
  try {
    console.log('Configuration des attributs filtrables...');
    
    // Configurer les attributs filtrables
    const response = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'title',
        'topics',
        'tags',
        'author',
        'type',
        'blog',
        'cs'
      ])
    });
    
    if (response.ok) {
      console.log('Attributs filtrables configurés avec succès');
      
      // Vérifier les attributs filtrables
      const settingsResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`);
      const settings = await settingsResponse.json();
      console.log('Attributs filtrables:', settings);
    } else {
      console.error('Erreur lors de la configuration des attributs filtrables:', await response.text());
    }
    
    console.log('Configuration terminée !');
  } catch (error) {
    console.error('Erreur lors de la configuration:', error);
  }
}

fixFilterable();
