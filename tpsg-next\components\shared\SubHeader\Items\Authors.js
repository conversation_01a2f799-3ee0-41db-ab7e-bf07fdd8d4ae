import styled from "styled-components";
import CondImage from "components/shared/condimage";
import { device } from "styles/device";
import Text from "./Text";
import CondLink from "../../CondLink";
import { isBlogger } from "utils/posts.utils";
import { useCoreData } from "context/CoreDataContext"

export default function Authors({ label, authors, addClass }) {
  const coreData = useCoreData()

  if (!authors) return <></>

  let plusIndicatorValue = authors.length - 2 > 0 ? authors.length - 2 : null;

  let names = authors
    .filter(a => a?.fullName)
    .map(s => s.fullName)
    .splice(0, 2)
    .toString()
    .replace(/\,/g, ", ");


  names = names.length > 0 ? names : `aucun ${label} n'est renseigné`;

  // Fonction pour déterminer le lien approprié pour chaque auteur
  const getAuthorLink = (author) => {
    // Si l'auteur a déjà un URL spécifié, l'utiliser
    if (author.url) {
      return author.url;
    }

    // Vérifier si l'auteur est un blogueur
    if (author.slug) {
      // Vérifier directement si l'auteur est un blogueur
      const authorIsBlogger = coreData.blogs?.some(blog => blog.slug === author.slug);
      if (authorIsBlogger) {
        return `/blog/${author.slug}`;
      }
    }

    // Si l'auteur n'est pas un blogueur ou n'a pas de slug, rediriger vers la recherche
    return `/recherche?author=${encodeURIComponent(author.fullName)}`;
  };

  const AuthorLink = ({ author, children }) => {
    const link = getAuthorLink(author);
    return (
      <CondLink link={link}>
        {children}
      </CondLink>
    );
  }

  let Authors = authors.filter(a => a?.fullName).map((a, key) => {
    return (<AuthorLink key={key} author={a}>{key > 0 ? ", " + a.fullName : a.fullName}</AuthorLink>)
  })

  return (
    <Wrapper className={`subheader-item ${addClass}`}>
      <Pictures>
        {authors.filter(a => a?.picture).map((a, k) =>
          <AuthorLink key={k} author={a}>
            <div className={"author-picture"} key={`pic-${k}}`}>
              <CondImage imageData={a.picture}/>
            </div>
          </AuthorLink>
        )}
      </Pictures>
      <Text label={label} content={Authors} noClass/>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

const Pictures = styled.div`
  display: none;
  flex-direction: row;
  margin-right: 16px;
  margin-left: 10px;

  .author-picture {
    position: relative;
    height: 50px;
    width: 50px;
    border-radius: 25px;
    margin-left: -10px;
    overflow: hidden;
    background-color: #161616;
  }

  @media ${device.tablet} {
    display: flex;
  }
`;


