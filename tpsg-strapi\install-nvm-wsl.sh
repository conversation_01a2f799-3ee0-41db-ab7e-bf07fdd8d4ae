#!/bin/bash
# Script pour installer NVM dans WSL

echo "Installation de NVM dans WSL..."
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash

# Recharger le profil pour utiliser NVM immédiatement
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # Charger NVM
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # Charger la complétion bash pour NVM

# Vérifier l'installation de NVM
echo "Vérification de l'installation de NVM..."
nvm --version

echo "Installation de Node.js 14..."
nvm install 14

echo "Utilisation de Node.js 14..."
nvm use 14

echo "Version de Node.js installée :"
node -v

echo "Version de npm installée :"
npm -v

echo "Installation terminée !"
echo "Pour utiliser Node.js 14 dans une nouvelle session WSL, exécutez : nvm use 14"
