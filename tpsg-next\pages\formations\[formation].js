import { RenderMarkdown } from "../../components/shared";
import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import { getModuleWithShortName } from "/utils/components.utils";

// noinspection GraphQLUnresolvedReference
const GET_FORMATION = gql`
    query GetFormation($slug: String!){
        posts(where: {slug: $slug}){
            title
            slug
            published_at
            body
            author {
                fullName
            }
            modules {
                ... on ComponentModuleFormation{
                    __typename
                    speakers {
                        fullName
                    }
                    link
                }
                ... on ComponentModuleLead{
                    __typename
                    content
                }
            }
        }
    }
`


export default function Formation({ post }) {

  return <></>
  const lead = getModuleWithShortName(post.modules, "lead")
  const formation = getModuleWithShortName(post.modules, "formation")

  return (
    <>
      <header>
        <h1>{post.title}</h1>
        {post.author?.fullName && <p>{post.author.fullName}</p>}
      </header>
      <article>
        {lead?.content && <p>{lead.content}</p>}
        <RenderMarkdown content={post.body}/>
      </article>
    </>
  )
}


export async function getServerSideProps({ params }) {

  let post = null;

  try {
    post = await client.query({
      query: GET_FORMATION,
      variables: { "slug": params.parcours }
    }).then(response => {
      return response.data.posts[0]
    })
  } catch(error) {
    return { notFound: true }
  }

  return {
    props: {
      post: post
    }
  }
}

