import styled from "styled-components";
import { device } from "styles/device";
import MediaButton from "components/shared/Buttons/MediaButton";

const PodcastDescriptionData = ({ content }) => {
  return (
    <DescriptionDataWrapper>
      <MainInfo>
        <div className="post-info">
          <p className="post-secondary-text">Épisodes</p>
          <p className="post-info-bold">{content.nbHits}</p>
        </div>
        <div className="post-info">
          <p className="post-secondary-text"><PERSON><PERSON><PERSON>.</p>
          <p className="post-info-bold">{content.post.averageDuration || "inconnu"}</p>
        </div>
      </MainInfo>
      <p className="post-description">
        {content.post.description}
      </p>
      <div className="post-platform">
        {content.post.platforms && content.post.platforms.map((p, k) => (
          <a key={k} href={p.url} target="_blank" rel="noreferrer">
            <MediaButton plateform={p.name}/>
          </a>
        ))}
      </div>
    </DescriptionDataWrapper>
  );
}
export {
  PodcastDescriptionData
}

const DescriptionDataWrapper = styled.div`
  .post-description {
    width: 100%;
    font-size: 16px;
    color: #161616;
  }

  .post-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 24px;
    width: 100%;
    gap: 17px;
  }

  @media ${device.tablet} {
    .post-platform {
      width: 75%;
      margin-top: 38px;
    }

    .post-description {
      font-size: 20px;
      width: 75%;
    }
  }
`;

const MainInfo = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 25px;

  .post-info {
    margin-right: 55px;

    p {
      margin: 0;
    }

    .post-info-bold {
      font-size: 20px;
      font-weight: bold;
    }
  }

  @media ${device.tablet} {
    .post-info {
      .post-info-bold {
        font-size: 32px;
      }
    }
  }
`