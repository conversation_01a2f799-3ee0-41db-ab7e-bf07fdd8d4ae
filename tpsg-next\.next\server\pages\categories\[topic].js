"use strict";(()=>{var e={};e.id=5201,e.ids=[5201,2888],e.modules={7716:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{config:()=>x,default:()=>d,getServerSideProps:()=>u,getStaticPaths:()=>h,getStaticProps:()=>g,reportWebVitals:()=>f,routeModule:()=>v,unstable_getServerProps:()=>j,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>w,unstable_getStaticProps:()=>m});var o=i(7093),s=i(5244),a=i(1323),n=i(779),p=i(4033),l=i(7305),c=e([l]);l=(c.then?(await c)():c)[0];let d=(0,a.l)(l,"default"),g=(0,a.l)(l,"getStaticProps"),h=(0,a.l)(l,"getStaticPaths"),u=(0,a.l)(l,"getServerSideProps"),x=(0,a.l)(l,"config"),f=(0,a.l)(l,"reportWebVitals"),m=(0,a.l)(l,"unstable_getStaticProps"),w=(0,a.l)(l,"unstable_getStaticPaths"),b=(0,a.l)(l,"unstable_getStaticParams"),j=(0,a.l)(l,"unstable_getServerProps"),y=(0,a.l)(l,"unstable_getServerSideProps"),v=new o.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/categories/[topic]",pathname:"/categories/[topic]",bundlePath:"",filename:""},components:{App:p.default,Document:n.default},userland:l});r()}catch(e){r(e)}})},2008:(e,t,i)=>{i.d(t,{Z:()=>TopicsHorizontalList});var r=i(997),o=i(7518),s=i.n(o),a=i(1664),n=i.n(a);function TopicsHorizontalList({topics:e}){return(0,r.jsxs)(p,{children:[e&&e.filter(e=>e.postCount>0).map((e,t)=>r.jsx(n(),{href:`/categories/${e.slug}`,children:r.jsx(l,{text:e.name,children:r.jsx("p",{children:e.name})})},t)),r.jsx(c,{})]})}let p=s().div.withConfig({componentId:"sc-66fa4988-0"})`
  position: relative;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  overflow: hidden;
  padding-top: 1px;
  padding-left: 1px;
  background-color: #F5F5F5;
`,l=s().a.withConfig({componentId:"sc-66fa4988-1"})`
  position: relative;
  margin-left: -1px;
  margin-top: -1px;
  perspective: 500px;
  height: 50px;
  box-sizing: border-box;
  padding-left: 42px;
  padding-right: 42px;
  flex-grow: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  cursor: pointer;
  background-color: white;
  overflow: hidden;

  p {
    color: transparent;
  }

  &:after {
    content: "${e=>e.text?e.text+" →":"→"}";
    display: flex;
    padding: 4px 0 0 0;
    position: absolute;
    background-color: black;
    color: white;
    transform-origin: 0% 0% 0;
    transform: rotateX(90deg);
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    transition: 600ms cubic-bezier(.74,.41,.17,1.08);

  }
  &:hover:after {
    transform: rotateX(0) translateY(0);
  }
  &:before {
    width: 100%;
    padding: 6px 0 0 0;
    box-sizing: border-box;
    content: "${e=>e.text?e.text:""}";
    border: 1px solid black;
    display: flex;
    position: absolute;
    background-color: white;
    transform-origin: 0% 0% 0;
    transform: rotateX(0deg);
    top: 0;
    left: 0;
    height: 100%;
    align-items: center;
    justify-content: center;
    transition: 600ms cubic-bezier(.74,.41,.17,1.08);
  }
  &:hover:before {
    transform: rotateX(-90deg);
  }
`,c=s().div.withConfig({componentId:"sc-66fa4988-2"})`
  position: relative;
  background-color: transparent;
  margin-left: 1px;
  height: 50px;
  flex-grow: 100;
`},7305:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{default:()=>PageTopic,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var o=i(997),s=i(9114),a=i(1385),n=i(7672),p=i(7518),l=i.n(p),c=i(9667),d=i(2832),g=i(7467),h=i(2008),u=i(9353),x=i(465),f=i(2333),m=i(1632),w=i(8657),b=i(2325),j=i(9296),y=i(723),v=e([w]);function PageTopic({topics:e,fallback:t}){let i=t?.posts?.totalHits||0,r=t?.posts?.hits||[],s=t.cornerStones?.hits||[],a=r?.length>3?3:0;e=(0,c.Ay)(e);let n=e[0];return(0,o.jsxs)(P,{children:[o.jsx("section",{className:"site-padding",children:o.jsx(u.Z,{topic:n,topicName:n.name,type:"theme"})}),r?.length>3&&(0,o.jsxs)(S,{className:"site-padding",children:[o.jsx(d.F3,{post:r[0],options:{showAuthor:!0,dotColors:{back:"#F4F4F4"}}}),o.jsx(d.Zo,{post:r[1],options:{showAuthor:!0,dotColors:{back:"#F4F4F4"}}}),o.jsx(d.Zo,{post:r[2],options:{showAuthor:!0,dotColors:{back:"#F4F4F4"}}})]}),s[0]&&o.jsx("section",{children:o.jsx(w.g4,{content:s[0]})}),n.children&&(e=>{let t=!1;for(let i of e)i.postCount>0&&(t=!0);return t})(n.children)&&(0,o.jsxs)("section",{style:{marginTop:"96px"},className:"site-padding",children:[o.jsx("h2",{children:"Sous th\xe8mes"}),o.jsx(h.Z,{topics:n.children})]}),(0,o.jsxs)(k,{className:"site-padding",children:[o.jsx("p",{className:"label-type",children:"Derni\xe8res ressources"}),(0,o.jsxs)("div",{className:"posts-container ",children:[(0,o.jsxs)(_,{children:[o.jsx("ul",{className:"list-container",children:r?.slice(a)?.map((e,t)=>o.jsx("li",{className:"post-card-li",children:o.jsx(b.Z,{post:e,options:{showLead:!0,showDate:!0,showAuthor:!0}})},`post-${t}`))}),o.jsx(x.Z,{nbHits:i,baseUrl:`/categories/${n.slug}/ressources?page=`,currentPage:1,options:{postPerPage:15}})]}),o.jsx(C,{children:(0,o.jsxs)("div",{className:"cornerstone-container",children:[s[1]&&o.jsx(j.Z,{post:s[1],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}}),s[2]&&o.jsx(j.Z,{post:s[2],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}})]})})]})]}),s[3]&&o.jsx("section",{children:o.jsx(w.g4,{content:s[3]})}),o.jsx(q,{})]})}async function getStaticProps({params:e}){let t=await a.Z.query({query:n.o$.QUERY_TOPIC,variables:{slug:e.topic}}).then(e=>e.data.topics[0]),i=[t],r=await a.Z.query({query:F,variables:{id:t.id}}).then(e=>e.data.topicChildren);i.push(...r),i=i.filter((e,t,i)=>i.findIndex(t=>t.id===e.id)===t);let o=(0,m.D)(i),s=await (0,f.j)({},o,15);if(s?.hits?.length===0||0===o.length)return{notFound:!0};let p=`cs=true AND (${o})`,l=await y.V.searchHighlight("",{filter:p,sort:["date:desc"],limit:4});return s.hits=s?.hits?.filter(e=>!l?.hits?.find(t=>t?.route===e?.route)),{revalidate:10,props:{topics:i,fallback:{posts:s,cornerStones:l}}}}async function getStaticPaths(){let e=await a.Z.query({query:$}).then(e=>e.data.topics);return{paths:e.map(e=>({params:{topic:e.slug}})),fallback:!1}}w=(v.then?(await v)():v)[0];let P=l().div.withConfig({componentId:"sc-f41f31ea-0"})`
  position: relative;
  ul {
    padding: 0 0 0 0;
  }
`,S=l().div.withConfig({componentId:"sc-f41f31ea-1"})`
  display: grid;
  grid-gap: 16px;
  grid-template-columns: repeat(2, 1fr);

  .fa-card {
    grid-column: 1/3;
  }

  @media ${g.U.desktop} {
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 32px;
  }
`,q=l().div.withConfig({componentId:"sc-f41f31ea-2"})`
  position: absolute;
  width: 100vw;
  height: 100%;
  margin: auto;
  top: 275px;
  left: 0;
  background-color: #f4f4f4;
  z-index: -1;

  @media ${g.U.desktopXL} {
    top: 375px;
  }
`,k=l().section.withConfig({componentId:"sc-f41f31ea-3"})`
  margin-top: 96px;

  .posts-container {
    display: block;

    @media ${g.U.desktop} {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
`,_=l().article.withConfig({componentId:"sc-f41f31ea-4"})`
  width: 100%;
  margin-bottom: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }
  .post-card-li {
    list-style: none;
    padding-right: 0;
  }

  @media ${g.U.desktop} {
    width: 66.7%;
    margin-bottom: 164px;
    .post-card-li {
      padding-right: 142px;
    }
  }
`,C=l().div.withConfig({componentId:"sc-f41f31ea-5"})`
  position: relative;
  width: 100%;

  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }
  @media ${g.U.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${g.U.desktop} {
    width: 33.3%;
  }
`,$=s.gql`
  query {
    topics {
      slug
    }
  }
`,F=s.gql`
  query TopicChildren($id: ID!) {
    topicChildren(id: $id) {
      name
      id
      slug
      postCount
      parent {
        id
        slug
        name
      }
    }
  }
`;r()}catch(e){r(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},9997:e=>{e.exports=require("meilisearch")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),i=t.X(0,[3181,5016,6859,8450,9755,4033,779,7113,6453,1077,7620,8672,2832,8657,723,5745,4092,142,8074,9296,9353],()=>__webpack_exec__(7716));module.exports=i})();