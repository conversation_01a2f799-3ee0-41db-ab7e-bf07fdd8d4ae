const { MeiliSearch } = require("meilisearch");

// Connexion à Meilisearch
const config = {
  host: process.env.MEILI_HOST || 'http://localhost:7700'
};

// N'ajouter la clé API que si elle est définie
if (process.env.MEILI_ADMIN_KEY) {
  config.apiKey = process.env.MEILI_ADMIN_KEY;
}

const client = new MeiliSearch(config);

async function checkMeilisearchSettings() {
  try {
    console.log("Vérification des paramètres de l'index Meilisearch...");

    // Vérifier si l'index existe
    const indexes = await client.getIndexes();

    if (!indexes || !indexes.results) {
      console.log("Aucun index trouvé ou format de réponse inattendu.");
      console.log("Réponse complète:", JSON.stringify(indexes, null, 2));
      return;
    }

    console.log("Indexes disponibles:", indexes.results.map(index => index.uid));

    const postIndex = indexes.results.find(index => index.uid === "post");

    if (!postIndex) {
      console.error("L'index 'post' n'existe pas.");
      return;
    }

    // Récupérer les paramètres actuels
    const settings = await client.index("post").getSettings();
    console.log("Paramètres actuels de l'index 'post':");
    console.log(JSON.stringify(settings, null, 2));
  } catch (error) {
    console.error("Erreur lors de la vérification des paramètres :", error);
  }
}

// Exécuter la fonction
checkMeilisearchSettings();
