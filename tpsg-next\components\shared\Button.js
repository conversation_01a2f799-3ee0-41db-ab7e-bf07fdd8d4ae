import Link from "next/link";
import styled from "styled-components";

export default function Button({ text, link, external = false }) {
  if (!external) {
    return (
      <Link href={link}>
        <ButtonText>{text}</ButtonText>
      </Link>
    )
  }
  return <></>
}

const ButtonText = styled.a`
    padding: 8px 16px;
    cursor: pointer;
    font-size: 12px;
    background-color: black;
    color: white;
`;