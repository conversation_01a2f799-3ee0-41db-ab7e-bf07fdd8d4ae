/**
 * Script pour restaurer meili-client.js à son état d'origine
 * 
 * Ce script restaure le fichier meili-client.js à son état d'origine
 * en supprimant les modifications apportées par patch-meili-client.js.
 * 
 * Pour exécuter ce script:
 * 1. Assurez-vous d'être dans le répertoire racine du projet
 * 2. Exécutez: node restore-meili-client.js
 */

const fs = require('fs');
const path = require('path');

// Chemin vers le fichier meili-client.js
const meiliClientPath = path.join('tpsg-next', 'api', 'meili-client.js');

// Lire le contenu du fichier
fs.readFile(meiliClientPath, 'utf8', (err, data) => {
  if (err) {
    console.error(`Erreur lors de la lecture du fichier ${meiliClientPath}:`, err);
    return;
  }

  // Fonction search modifiée
  const searchFunctionModified = `const search = async (q = "", params) => {
  // Créer une copie des paramètres pour éviter de modifier l'objet original
  const cleanParams = { ...params };
  
  // Supprimer les paramètres incompatibles avec Meilisearch v0.27.0
  if (cleanParams.page !== undefined) delete cleanParams.page;
  if (cleanParams.hitsPerPage !== undefined) delete cleanParams.hitsPerPage;
  
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "cs", "lead"],
    attributesToHighlight: ["title"],
    ...cleanParams,
  })
}`;

  const searchFunctionOriginal = `const search = async (q = "", params) => {
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "cs", "lead"],
    attributesToHighlight: ["title"],
    ...params,
  })
}`;

  // Fonction searchHighlight modifiée
  const searchHighlightFunctionModified = `const searchHighlight = async (q = "", params) => {
  // Créer une copie des paramètres pour éviter de modifier l'objet original
  const cleanParams = { ...params };
  
  // Supprimer les paramètres incompatibles avec Meilisearch v0.27.0
  if (cleanParams.page !== undefined) delete cleanParams.page;
  if (cleanParams.hitsPerPage !== undefined) delete cleanParams.hitsPerPage;
  
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "body", "lead", "topics", "cs"],
    attributesToHighlight: ["title", "body", "lead"],
    attributesToCrop: ["body", "lead"],
    cropLength: 100,
    ...cleanParams,
  })
}`;

  const searchHighlightFunctionOriginal = `const searchHighlight = async (q = "", params) => {
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "body", "lead", "topics", "cs"],
    attributesToHighlight: ["title", "body", "lead"],
    attributesToCrop: ["body", "lead"],
    cropLength: 100,
    ...params,
  })
}`;

  // Remplacer les fonctions
  let restoredContent = data.replace(searchFunctionModified, searchFunctionOriginal);
  restoredContent = restoredContent.replace(searchHighlightFunctionModified, searchHighlightFunctionOriginal);

  // Écrire le contenu restauré dans le fichier
  fs.writeFile(meiliClientPath, restoredContent, 'utf8', (err) => {
    if (err) {
      console.error(`Erreur lors de l'écriture du fichier ${meiliClientPath}:`, err);
      return;
    }
    console.log(`Le fichier ${meiliClientPath} a été restauré avec succès.`);
  });
});
