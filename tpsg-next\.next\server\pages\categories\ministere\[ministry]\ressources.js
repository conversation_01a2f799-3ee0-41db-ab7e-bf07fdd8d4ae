"use strict";(()=>{var e={};e.id=6063,e.ids=[6063,2888],e.modules={5676:(e,t,s)=>{s.r(t),s.d(t,{config:()=>M,default:()=>w,getServerSideProps:()=>C,getStaticPaths:()=>k,getStaticProps:()=>j,reportWebVitals:()=>$,routeModule:()=>G,unstable_getServerProps:()=>R,unstable_getServerSideProps:()=>A,unstable_getStaticParams:()=>N,unstable_getStaticPaths:()=>I,unstable_getStaticProps:()=>D});var r={};s.r(r),s.d(r,{default:()=>PageMinistryRessources,getServerSideProps:()=>getServerSideProps});var i=s(7093),a=s(5244),o=s(1323),n=s(779),l=s(4033),p=s(997),c=s(1385),u=s(7518),d=s.n(u),g=s(2325),m=s(465),x=s(9114),b=s(7467),h=s(1632);s(635);var f=s(1163),S=s(6689),P=s(2333);function PageMinistryRessources({ministry:e,props:t,fallback:s}){let r=s?.posts?.totalHits||0,i=s?.posts?.hits||[],{page:a}=t,o=(0,f.useRouter)();return((0,S.useEffect)(()=>{1==+a?o.replace({pathname:`/categories/ministere/${e.slug}`},void 0,{scroll:!1}):+a>Math.ceil(r/15)&&o.replace({pathname:`/categories/ministere/${e.slug}/ressources`,query:{page:Math.ceil(r/15)}},void 0,{scroll:!1})},[e.slug,r,a,o]),e)?(0,p.jsxs)(v,{className:"site-padding",children:[p.jsx("h1",{children:e.name}),(0,p.jsxs)(y,{children:[p.jsx("p",{className:"label-type",children:"Derni\xe8res ressources"}),p.jsx("div",{className:"posts-container ",children:p.jsx(_,{children:p.jsx("ul",{className:"list-container",children:i.map((e,t)=>p.jsx(g.Z,{post:e,options:{showLead:!0,showDate:!0,showAuthor:!0}},`post-${t}`))})})})]}),p.jsx(m.Z,{nbHits:r,baseUrl:`/categories/ministere/${e.slug}/ressources?page=`,currentPage:a,options:{postPerPage:15}})]}):null}async function getServerSideProps({query:e,params:t}){let{page:s}=e,r=await c.Z.query({query:q,variables:{slug:t.ministry}}).then(e=>e.data.topicGroups[0]);if(!r)return{notFound:!0};let i=[...r.parent.topics,...r.topics];i=i.filter((e,t,s)=>s.findIndex(t=>t.id===e.id)===t);let a=(0,h.D)(i);if(0===a.length)return{notFound:!0};let o=await (0,P.j)({page:+s},a,15);return{props:{ministry:r,props:{page:+s},fallback:{posts:o}}}}let q=x.gql`
  query Ministry($slug: String!) {
    topicGroups(where: { slug: $slug }) {
      slug
      name
      topics {
        id
        name
        slug
        postCount
      }
      parent {
        id
        name
        slug
        topics {
          id
          name
          postCount
        }
      }
      children {
        id
        slug
        topics {
          id
          name
          slug
          postCount
        }
      }
    }
  }
`,v=d().div.withConfig({componentId:"sc-cbb71b5a-0"})`
  padding-bottom: 50px;
`,y=d().section.withConfig({componentId:"sc-cbb71b5a-1"})`
  margin-top: 96px;
  .posts-container {
    display: block;

    @media ${b.U.desktop} {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
  .label-type{
    font-size: 24px;
  }
`,_=d().article.withConfig({componentId:"sc-cbb71b5a-2"})`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }
`,w=(0,o.l)(r,"default"),j=(0,o.l)(r,"getStaticProps"),k=(0,o.l)(r,"getStaticPaths"),C=(0,o.l)(r,"getServerSideProps"),M=(0,o.l)(r,"config"),$=(0,o.l)(r,"reportWebVitals"),D=(0,o.l)(r,"unstable_getStaticProps"),I=(0,o.l)(r,"unstable_getStaticPaths"),N=(0,o.l)(r,"unstable_getStaticParams"),R=(0,o.l)(r,"unstable_getServerProps"),A=(0,o.l)(r,"unstable_getServerSideProps"),G=new i.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/categories/ministere/[ministry]/ressources",pathname:"/categories/ministere/[ministry]/ressources",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:r})},9114:e=>{e.exports=require("@apollo/client")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},9997:e=>{e.exports=require("meilisearch")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[3181,5016,6859,8450,9755,4033,779,7113,6453,1077,7620,723,5745,4092,142,8074],()=>__webpack_exec__(5676));module.exports=s})();