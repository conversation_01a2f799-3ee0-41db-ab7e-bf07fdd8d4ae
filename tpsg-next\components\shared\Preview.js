import styled from "styled-components";
import { useState } from "react";
import { YoutubeEmbed } from "components/shared/strapi-modules";

/**
 * Affichage un aperçu vidéo (youtube) d'une formation au format plein page.
 * Pour s'afficher correctement, ce composant ne doit pas être placer dans
 * un parent avec un positionnement absolu.
 */
export default function Preview({ youtubeEmbed, style, children }) {

  const [show, setShow] = useState(false);

  return(
    <Wrapper style={style} onClick={() => setShow(show ? false : true)}>
      {
        show &&
        // TODO: Ajouter un spinner, mais tranquil...
        <div className={"fullscreen-preview"}>
          <div className={"video-player"}>
            <YoutubeEmbed autoplay={true} video={youtubeEmbed}/>
          </div>
          <div className={"close-button"} onClick={() => setShow(false)}>
            <p>X</p>
          </div>
        </div>
      }
      <div onClick={() => setShow(true)}>
        {children}
      </div>
    </Wrapper>
  )
}

const Wrapper = styled.div`

  .fullscreen-preview {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 4000;
    background-color: rgba(0, 0, 0, 0.9);
    animation-duration: 600ms;
    animation-name: showIn;
  }

  @keyframes showIn {
    from {
      background-color: transparent;
    }
    to {
      background-color: rgba(0, 0, 0, 0.9);
    }
  }

  .video-player {
    position: relative;
    width: 60%;
    aspect-ratio: 16/10;
  }

  .close-button {
    position: absolute;
    border: 1px solid white;
    border-radius: 48px;
    top: 48px;
    right: var(--border-space);
    height: 32px;
    width: 64px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    p {
      color: white;
      transform: scaleX(1.5);
      font-weight: 400;
      font-size: 18px;
      line-height: 52px;
      margin-top: 8px;
      margin-bottom: 0;
    }
  }
`;