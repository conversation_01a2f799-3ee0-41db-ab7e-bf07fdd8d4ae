"use strict";exports.id=7438,exports.ids=[7438],exports.modules={7438:(e,t,i)=>{i.d(t,{Z:()=>CategoriesHeader});var n=i(997),r=i(7518),a=i.n(r),o=i(7467),s=i(1664),l=i.n(s);function CategoriesHeader({category:e,type:t}){return(0,n.jsxs)(c,{children:[n.jsx("div",{className:"header-color"}),(0,n.jsxs)("div",{className:"content",children:[n.jsx(l(),{href:"/categories",className:"type",children:t+" /"}),(0,n.jsxs)("div",{className:"content-container",children:[n.jsx("div",{className:"title-container",children:n.jsx("h1",{className:"title",children:e?.name})}),n.jsx("div",{className:"description-container",children:n.jsx("p",{className:"description",children:e?.description})})]})]})]})}let c=a().header.withConfig({componentId:"sc-11b6a297-0"})`
  position: relative;
  padding: var(--border-space);
  padding-bottom: 80px;
  padding-top: 60px;

  .type {
    font-size: clamp(1rem, 0.928635147190009rem + 0.35682426404995543vw, 1.25rem);
    font-family: Stelvio, sans-serif;
    margin: 0 0 24px 0;
    color: #FFFFFF;
    font-weight: 500;
    letter-spacing: 1px;
    text-transform: uppercase;
  }
  
  .content{
    position: relative;
  }
  
  .header-color {
    position: absolute;
    overflow: hidden;
    background-color: var(--brand-color) !important;
    width: 100%;
    height: calc(100% + 500px);
    left: 0;
    bottom: 0;
  }
  .content-container {
    display: flex;
    flex-wrap: wrap;
  }
  .content-container {
    margin: 32px 0 0 0;
    .description {
      margin: 0;
      font-size: 18px;
      font-family: "Lora", serif;
      margin: 0;
      font-weight: 500;
      letter-spacing: 0;
      line-height: 24px;
      color: white;
    }
    .title-container {
      overflow-wrap: anywhere;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      flex-direction: column;
      align-items: flex-start;
      .title {
        font-size: clamp(2rem, 1.286rem + 1.905vw, 3rem);
        line-height: clamp(2rem, 1.286rem + 1.905vw, 3rem);
        font-family: Stelvio, sans-serif;
        margin: 0 0 16px 0;
        font-weight: 500;
        letter-spacing: 0;
        color: white; 
      }
    }
  }

  @media ${o.U.tablet} {
    .content-container {
      .description-container {
        width: 40%;
      }
      .title-container {
        width: 60%;
        padding-right: 50px;
        .title {
          margin: 0;
        }
      }
    }
  }
`}};