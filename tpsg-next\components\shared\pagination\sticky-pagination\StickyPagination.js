import styled from "styled-components";
import { device } from "styles/device";
import PaginateSection from "./PaginateSection";
import { PostLittleTitle } from "styles/styled-typography";

export default function StickyPagination({ url, title, children }) {
    
  return (
    <StickyPaginationWrapper>
      <header>
        <PostLittleTitle>{title}</PostLittleTitle>
      </header>

      <MiddleContent>
        {children}
      </MiddleContent>

      <footer className="paginate-section-container">
        <PaginateSection
          gapSize={"200px"}
          gapSizeMobile={"42px"}
          url={url}
        />
      </footer>
    </StickyPaginationWrapper>
  );
}

const MiddleContent = styled.main`
    width: 100%;
    height: calc(100% - 350px);
    //overflow: auto;
`;

const StickyPaginationWrapper = styled.div`
  position: sticky;
  top: 0;
  padding-top: 20px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  flex-wrap: wrap;

  .paginate-section-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    position: fixed;
    margin-top: 0;
    bottom: 0;
    left: 0;
    height: 79px;
    background-color: rgba(244, 244, 244, 0.95);

    @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
      background-color: rgba(244, 244, 244, 0.8);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    section {
      display: flex;
      flex-direction: row-reverse;
    }
    .pagestate-header div[class*="NavigationButtons__Wrapper"] {
      margin-right: 24px;
      width: 140px;
      display: flex;
      justify-content: space-around;
    }
    .pagestate-main div[class*="AnimatedNumber__Wrapper"] {
      display: flex;
      justify-content: start;
    }
    .pagestate-main {
      margin-right: 17px;
    }

    &:before {
      content: "";
      position: absolute;
      right: 0;
      top: 0;
      height: 1px;
      width: 40%;
      border-top: 1px solid black;
    }
  }
  @media ${device.tablet} {
    @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
      -webkit-backdrop-filter: blur(0);
      backdrop-filter: blur(0);
    }

    padding-top: 40px;
    height: 100vh;
    p {
      display: block;
    }

    .paginate-section-container {
      position: absolute;
      bottom: 0;
      height: auto;
      background-color: transparent;
      margin-top: 50px;
      display: block;

      &:before {
        display: none;
      }

      section {
        display: block;
      }

      .pagestate-header div[class*="NavigationButtons__Wrapper"] {
        margin-right: 0;
      }

      .pagestate-main {
        margin-right: 0;
      }
    }
  }
`;
