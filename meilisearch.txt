Option 3 : Supprimer et recréer l'index avec les bons paramètres
Étape 1 : Supprimer l'index existant

Invoke-WebRequest -Uri "http://localhost:7700/indexes/post" -Method DELETE


Étape 2 : Créer un nouvel index avec les attributs triables configurés
Invoke-WebRequest -Uri "http://localhost:7700/indexes" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{
  "uid": "post",
  "primaryKey": "id"
}'

Étape 3 : Configurer les paramètres de l'index dès sa création
Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings" -Method PATCH -Headers @{"Content-Type"="application/json"} -Body '{
  "sortableAttributes": ["date"],
  "filterableAttributes": ["author", "topics", "tags", "type", "blog", "cs"],
  "searchableAttributes": ["title", "body"],
  "pagination": {
    "maxTotalHits": 4000
  }
}'


Je vois que la méthode PATCH n'est pas autorisée pour cet endpoint dans Meilisearch v0.27.0. Essayons avec la méthode POST à la place :
Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{
  "sortableAttributes": ["date"],
  "filterableAttributes": ["author", "topics", "tags", "type", "blog", "cs"],
  "searchableAttributes": ["title", "body"],
  "pagination": {
    "maxTotalHits": 4000
  }
}'

Si cela ne fonctionne pas, essayons avec la méthode PUT :
Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings" -Method PUT -Headers @{"Content-Type"="application/json"} -Body '{
  "sortableAttributes": ["date"],
  "filterableAttributes": ["author", "topics", "tags", "type", "blog", "cs"],
  "searchableAttributes": ["title", "body"],
  "pagination": {
    "maxTotalHits": 4000
  }
}'


    OK
    Si ces méthodes ne fonctionnent pas, nous pouvons essayer de configurer chaque paramètre individuellement avec la méthode POST que nous savons fonctionner :
    Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings/sortable-attributes" -Method POST -Headers @{"Content-Type"="application/json"} -Body '["date"]'

    Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings/filterable-attributes" -Method POST -Headers @{"Content-Type"="application/json"} -Body '["author", "topics", "tags", "type", "blog", "cs"]'

    Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings/searchable-attributes" -Method POST -Headers @{"Content-Type"="application/json"} -Body '["title", "body"]'

    Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings/pagination" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"maxTotalHits": 4000}'


    Il semble que l'endpoint pour la pagination soit différent dans Meilisearch v0.27.0. Essayons une approche différente pour la pagination :
    Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings" -Method PATCH -Headers @{"Content-Type"="application/json"} -Body '{"pagination": {"maxTotalHits": 4000}}'
    Si cela ne fonctionne pas, nous pouvons vérifier les paramètres actuels pour voir comment la pagination est configurée :
    Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings" -Method GET | Select-Object -ExpandProperty Content

    Maintenant, vérifions si les attributs que nous avons configurés ont été correctement appliqués :
    Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings/sortable-attributes" -Method GET | Select-Object -ExpandProperty Content
    Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings/filterable-attributes" -Method GET | Select-Object -ExpandProperty Content
    Invoke-WebRequest -Uri "http://localhost:7700/indexes/post/settings/searchable-attributes" -Method GET | Select-Object -ExpandProperty Content
    Si tout est correct, nous devrions voir les attributs que nous avons configurés dans les réponses. Si ce n'est pas le cas, nous pouvons essayer de les configurer à nouveau.





    // config-meilisearch.js
const fetch = require('node-fetch');

const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function configMeilisearch() {
  try {
    console.log('Configuration de Meilisearch pour compatibilité avec le code existant...');
    
    // 1. Configurer les attributs filtrables
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'title', 'topics', 'tags', 'author', 'type', 'blog', 'cs'
      ])
    });
    
    // 2. Configurer les attributs triables
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['date'])
    });
    
    // 3. Configurer les attributs de recherche
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['title', 'body', 'lead'])
    });
    
    // 4. Configurer les règles de classement pour inclure totalHits
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/ranking-rules`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'words', 'typo', 'proximity', 'attribute', 'sort', 'exactness'
      ])
    });
    
    console.log('Configuration terminée !');
  } catch (error) {
    console.error('Erreur lors de la configuration :', error);
  }
}

configMeilisearch();