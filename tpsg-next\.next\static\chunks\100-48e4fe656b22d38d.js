"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[100],{8100:function(e,n,t){t.d(n,{J$:function(){return x},ZP:function(){return U}});var r,i=t(7294);/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */function __awaiter(e,n,t,r){return new(t||(t=Promise))(function(i,o){function fulfilled(e){try{step(r.next(e))}catch(e){o(e)}}function rejected(e){try{step(r.throw(e))}catch(e){o(e)}}function step(e){var n;e.done?i(e.value):((n=e.value)instanceof t?n:new t(function(e){e(n)})).then(fulfilled,rejected)}step((r=r.apply(e,n||[])).next())})}function __generator(e,n){var t,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:verb(0),throw:verb(1),return:verb(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function verb(o){return function(u){return function(o){if(t)throw TypeError("Generator is already executing.");for(;a;)try{if(t=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=n.call(e,a)}catch(e){o=[6,e],r=0}finally{t=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,u])}}}var noop=function(){},o=noop(),a=Object,isUndefined=function(e){return e===o},isFunction=function(e){return"function"==typeof e},mergeObjects=function(e,n){return a.assign({},e,n)},u="undefined",hasWindow=function(){return typeof window!=u},c=new WeakMap,s=0,stableHash=function(e){var n,t,r=typeof e,i=e&&e.constructor,o=i==Date;if(a(e)!==e||o||i==RegExp)n=o?e.toJSON():"symbol"==r?e.toString():"string"==r?JSON.stringify(e):""+e;else{if(n=c.get(e))return n;if(n=++s+"~",c.set(e,n),i==Array){for(t=0,n="@";t<e.length;t++)n+=stableHash(e[t])+",";c.set(e,n)}if(i==a){n="#";for(var u=a.keys(e).sort();!isUndefined(t=u.pop());)isUndefined(e[t])||(n+=t+":"+stableHash(e[t])+",");c.set(e,n)}}return n},f=!0,l=hasWindow(),d=typeof document!=u,g=l&&window.addEventListener?window.addEventListener.bind(window):noop,v=d?document.addEventListener.bind(document):noop,h=l&&window.removeEventListener?window.removeEventListener.bind(window):noop,b=d?document.removeEventListener.bind(document):noop,p={initFocus:function(e){return v("visibilitychange",e),g("focus",e),function(){b("visibilitychange",e),h("focus",e)}},initReconnect:function(e){var onOnline=function(){f=!0,e()},onOffline=function(){f=!1};return g("online",onOnline),g("offline",onOffline),function(){h("online",onOnline),h("offline",onOffline)}}},m=!hasWindow()||"Deno"in window,y=m?i.useEffect:i.useLayoutEffect,w="undefined"!=typeof navigator&&navigator.connection,C=!m&&w&&(["slow-2g","2g"].includes(w.effectiveType)||w.saveData),serialize=function(e){if(isFunction(e))try{e=e()}catch(n){e=""}var n=[].concat(e),t=(e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?stableHash(e):"")?"$swr$"+e:"";return[e,n,t]},O=new WeakMap,broadcastState=function(e,n,t,r,i,o,a){void 0===a&&(a=!0);var u=O.get(e),c=u[0],s=u[1],f=u[3],l=c[n],d=s[n];if(a&&d)for(var g=0;g<d.length;++g)d[g](t,r,i);return o&&(delete f[n],l&&l[0])?l[0](2).then(function(){return e.get(n)}):e.get(n)},R=0,getTimestamp=function(){return++R},internalMutate=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return __awaiter(void 0,void 0,void 0,function(){var n,t,r,i,a,u,c,s,f,l,d,g,v,h,b,p,m,y,w,C;return __generator(this,function(R){switch(R.label){case 0:if(n=e[0],t=e[1],r=e[2],u=!!isUndefined((a="boolean"==typeof(i=e[3])?{revalidate:i}:i||{}).populateCache)||a.populateCache,c=!1!==a.revalidate,s=!1!==a.rollbackOnError,f=a.optimisticData,d=(l=serialize(t))[0],g=l[2],!d)return[2];if(v=O.get(n)[2],e.length<3)return[2,broadcastState(n,d,n.get(d),o,o,c,!0)];if(h=r,p=getTimestamp(),v[d]=[p,0],m=!isUndefined(f),y=n.get(d),m&&(w=isFunction(f)?f(y):f,n.set(d,w),broadcastState(n,d,w)),isFunction(h))try{h=h(n.get(d))}catch(e){b=e}if(!(h&&isFunction(h.then)))return[3,2];return[4,h.catch(function(e){b=e})];case 1:if(h=R.sent(),p!==v[d][0]){if(b)throw b;return[2,h]}b&&m&&s&&(u=!0,h=y,n.set(d,y)),R.label=2;case 2:return u&&(b||(isFunction(u)&&(h=u(h,y)),n.set(d,h)),n.set(g,mergeObjects(n.get(g),{error:b}))),v[d][1]=getTimestamp(),[4,broadcastState(n,d,h,b,o,c,!!u)];case 3:if(C=R.sent(),b)throw b;return[2,u?C:h]}})})},revalidateAllKeys=function(e,n){for(var t in e)e[t][0]&&e[t][0](n)},initCache=function(e,n){if(!O.has(e)){var t=mergeObjects(p,n),r={},i=internalMutate.bind(o,e),a=noop;if(O.set(e,[r,{},{},{},i]),!m){var u=t.initFocus(setTimeout.bind(o,revalidateAllKeys.bind(o,r,0))),c=t.initReconnect(setTimeout.bind(o,revalidateAllKeys.bind(o,r,1)));a=function(){u&&u(),c&&c(),O.delete(e)}}return[e,i,a]}return[e,O.get(e)[4]]},k=initCache(new Map),E=k[0],S=mergeObjects({onLoadingSlow:noop,onSuccess:noop,onError:noop,onErrorRetry:function(e,n,t,r,i){var o=t.errorRetryCount,a=i.retryCount,u=~~((Math.random()+.5)*(1<<(a<8?a:8)))*t.errorRetryInterval;(isUndefined(o)||!(a>o))&&setTimeout(r,u,i)},onDiscarded:noop,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:C?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:C?5e3:3e3,compare:function(e,n){return stableHash(e)==stableHash(n)},isPaused:function(){return!1},cache:E,mutate:k[1],fallback:{}},{isOnline:function(){return f},isVisible:function(){var e=d&&document.visibilityState;return isUndefined(e)||"hidden"!==e}}),mergeConfigs=function(e,n){var t=mergeObjects(e,n);if(n){var r=e.use,i=e.fallback,o=n.use,a=n.fallback;r&&o&&(t.use=r.concat(o)),i&&a&&(t.fallback=mergeObjects(i,a))}return t},F=(0,i.createContext)({}),useStateWithDeps=function(e,n){var t=(0,i.useState)({})[1],r=(0,i.useRef)(e),o=(0,i.useRef)({data:!1,error:!1,isValidating:!1}),a=(0,i.useCallback)(function(e){var i=!1,a=r.current;for(var u in e)a[u]!==e[u]&&(a[u]=e[u],o.current[u]&&(i=!0));i&&!n.current&&t({})},[]);return y(function(){r.current=e}),[r,o.current,a]},subscribeCallback=function(e,n,t){var r=n[e]||(n[e]=[]);return r.push(t),function(){var e=r.indexOf(t);e>=0&&(r[e]=r[r.length-1],r.pop())}},T={dedupe:!0},x=a.defineProperty(function(e){var n=e.value,t=mergeConfigs((0,i.useContext)(F),n),r=n&&n.provider,a=(0,i.useState)(function(){return r?initCache(r(t.cache||E),n):o})[0];return a&&(t.cache=a[0],t.mutate=a[1]),y(function(){return a?a[2]:o},[]),(0,i.createElement)(F.Provider,mergeObjects(e,{value:t}))},"default",{value:S}),U=(r=function(e,n,t){var r=t.cache,a=t.compare,c=t.fallbackData,s=t.suspense,f=t.revalidateOnMount,l=t.refreshInterval,d=t.refreshWhenHidden,g=t.refreshWhenOffline,v=O.get(r),h=v[0],b=v[1],p=v[2],w=v[3],C=serialize(e),R=C[0],k=C[1],E=C[2],S=(0,i.useRef)(!1),F=(0,i.useRef)(!1),x=(0,i.useRef)(R),U=(0,i.useRef)(n),_=(0,i.useRef)(t),getConfig=function(){return _.current},isActive=function(){return getConfig().isVisible()&&getConfig().isOnline()},patchFetchInfo=function(e){return r.set(E,mergeObjects(r.get(E),e))},I=r.get(R),V=isUndefined(c)?t.fallback[R]:c,j=isUndefined(I)?V:I,A=r.get(E)||{},D=A.error,W=!S.current,shouldRevalidate=function(){return W&&!isUndefined(f)?f:!getConfig().isPaused()&&(s?!isUndefined(j)&&t.revalidateIfStale:isUndefined(j)||t.revalidateIfStale)},L=!!R&&!!n&&(!!A.isValidating||W&&shouldRevalidate()),M=useStateWithDeps({data:j,error:D,isValidating:L},F),P=M[0],H=M[1],N=M[2],z=(0,i.useCallback)(function(e){return __awaiter(void 0,void 0,void 0,function(){var n,i,u,c,s,f,l,d,g,v,h,b,m;return __generator(this,function(y){switch(y.label){case 0:if(n=U.current,!R||!n||F.current||getConfig().isPaused())return[2,!1];c=!0,s=e||{},f=!w[R]||!s.dedupe,l=function(){return!F.current&&R===x.current&&S.current},d=function(){var e=w[R];e&&e[1]===u&&delete w[R]},g={isValidating:!1},v=function(){patchFetchInfo({isValidating:!1}),l()&&N(g)},patchFetchInfo({isValidating:!0}),N({isValidating:!0}),y.label=1;case 1:return y.trys.push([1,3,,4]),f&&(broadcastState(r,R,P.current.data,P.current.error,!0),t.loadingTimeout&&!r.get(R)&&setTimeout(function(){c&&l()&&getConfig().onLoadingSlow(R,t)},t.loadingTimeout),w[R]=[n.apply(void 0,k),getTimestamp()]),i=(m=w[R])[0],u=m[1],[4,i];case 2:if(i=y.sent(),f&&setTimeout(d,t.dedupingInterval),!w[R]||w[R][1]!==u)return f&&l()&&getConfig().onDiscarded(R),[2,!1];if(patchFetchInfo({error:o}),g.error=o,!isUndefined(h=p[R])&&(u<=h[0]||u<=h[1]||0===h[1]))return v(),f&&l()&&getConfig().onDiscarded(R),[2,!1];return a(P.current.data,i)?g.data=P.current.data:g.data=i,a(r.get(R),i)||r.set(R,i),f&&l()&&getConfig().onSuccess(i,R,t),[3,4];case 3:return b=y.sent(),d(),!getConfig().isPaused()&&(patchFetchInfo({error:b}),g.error=b,f&&l()&&(getConfig().onError(b,R,t),("boolean"==typeof t.shouldRetryOnError&&t.shouldRetryOnError||isFunction(t.shouldRetryOnError)&&t.shouldRetryOnError(b))&&isActive()&&getConfig().onErrorRetry(b,R,t,z,{retryCount:(s.retryCount||0)+1,dedupe:!0}))),[3,4];case 4:return c=!1,v(),l()&&f&&broadcastState(r,R,g.data,g.error,!1),[2,!0]}})})},[R]),J=(0,i.useCallback)(internalMutate.bind(o,r,function(){return x.current}),[]);if(y(function(){U.current=n,_.current=t}),y(function(){if(R){var e=R!==x.current,n=z.bind(o,T),t=0,r=subscribeCallback(R,b,function(e,n,t){N(mergeObjects({error:n,isValidating:t},a(P.current.data,e)?o:{data:e}))}),i=subscribeCallback(R,h,function(e){if(0==e){var r=Date.now();getConfig().revalidateOnFocus&&r>t&&isActive()&&(t=r+getConfig().focusThrottleInterval,n())}else if(1==e)getConfig().revalidateOnReconnect&&isActive()&&n();else if(2==e)return z()});return F.current=!1,x.current=R,S.current=!0,e&&N({data:j,error:D,isValidating:L}),shouldRevalidate()&&(isUndefined(j)||m?n():hasWindow()&&typeof window.requestAnimationFrame!=u?window.requestAnimationFrame(n):setTimeout(n,1)),function(){F.current=!0,r(),i()}}},[R,z]),y(function(){var e;function next(){var n=isFunction(l)?l(j):l;n&&-1!==e&&(e=setTimeout(execute,n))}function execute(){!P.current.error&&(d||getConfig().isVisible())&&(g||getConfig().isOnline())?z(T).then(next):next()}return next(),function(){e&&(clearTimeout(e),e=-1)}},[l,d,g,z]),(0,i.useDebugValue)(j),s&&isUndefined(j)&&R)throw U.current=n,_.current=t,F.current=!1,isUndefined(D)?z(T):D;return{mutate:J,get data(){return H.data=!0,j},get error(){return H.error=!0,D},get isValidating(){return H.isValidating=!0,L}}},function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=mergeObjects(S,(0,i.useContext)(F)),o=isFunction(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],a=o[0],u=o[1],c=mergeConfigs(t,o[2]),s=r,f=c.use;if(f)for(var l=f.length;l-- >0;)s=f[l](s);return s(a,u||c.fetcher,c)})}}]);