/**
 * Script pour configurer Meilisearch v0.30.x afin qu'il soit compatible avec le code existant
 *
 * Pour exécuter ce script :
 * 1. Assurez-vous que Meilisearch est en cours d'exécution
 * 2. Exécutez la commande : node config-meilisearch-v0.30.5.js
 */

const fetch = require('node-fetch');

const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function configMeilisearch() {
  try {
    console.log('Configuration de Meilisearch v0.30.x pour compatibilité avec le code existant...');

    // Vérifier si l'index existe
    console.log('Vérification de l\'existence de l\'index...');
    const indexesResponse = await fetch(`${MEILI_URL}/indexes`);
    const indexes = await indexesResponse.json();

    const indexExists = indexes.results && indexes.results.some(index => index.uid === INDEX_NAME);

    if (!indexExists) {
      console.log('L\'index n\'existe pas, création de l\'index...');
      await fetch(`${MEILI_URL}/indexes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uid: INDEX_NAME })
      });
      console.log('Index créé avec succès');
    } else {
      console.log('L\'index existe déjà');
    }

    // Configuration des attributs filtrables
    console.log('Configuration des attributs filtrables...');
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        "title",
        "topics",
        "tags",
        "author",
        "type",
        "blog",
        "cs"
      ])
    });
    console.log('Attributs filtrables configurés');

    // Configuration des attributs triables
    console.log('Configuration des attributs triables...');
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(["date"])
    });
    console.log('Attributs triables configurés');

    // Configuration des attributs recherchables
    console.log('Configuration des attributs recherchables...');
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        "title",
        "body",
        "lead"
      ])
    });
    console.log('Attributs recherchables configurés');

    // Configuration de la pagination
    console.log('Configuration de la pagination...');
    await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/pagination`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        "maxTotalHits": 4000
      })
    });
    console.log('Pagination configurée');

    console.log('\nConfiguration terminée !');
    console.log('\nIMPORTANT:');
    console.log('Meilisearch v0.30.x supporte les paramètres "page" et "hitsPerPage".');
    console.log('Votre code existant devrait fonctionner sans modification.');

  } catch (error) {
    console.error('Erreur lors de la configuration :', error);
  }
}

configMeilisearch();
