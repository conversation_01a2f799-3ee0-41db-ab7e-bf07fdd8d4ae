"use strict";(()=>{var e={};e.id=4745,e.ids=[4745,2888],e.modules={8087:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>m,default:()=>u,getServerSideProps:()=>g,getStaticPaths:()=>h,getStaticProps:()=>d,reportWebVitals:()=>x,routeModule:()=>j,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>w,unstable_getStaticProps:()=>f});var s=r(7093),a=r(5244),o=r(1323),n=r(779),l=r(4033),c=r(1980),p=e([c]);c=(p.then?(await p)():p)[0];let u=(0,o.l)(c,"default"),d=(0,o.l)(c,"getStaticProps"),h=(0,o.l)(c,"getStaticPaths"),g=(0,o.l)(c,"getServerSideProps"),m=(0,o.l)(c,"config"),x=(0,o.l)(c,"reportWebVitals"),f=(0,o.l)(c,"unstable_getStaticProps"),w=(0,o.l)(c,"unstable_getStaticPaths"),y=(0,o.l)(c,"unstable_getStaticParams"),v=(0,o.l)(c,"unstable_getServerProps"),b=(0,o.l)(c,"unstable_getServerSideProps"),j=new s.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/categories/vocation/[vocation]",pathname:"/categories/vocation/[vocation]",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:c});i()}catch(e){i(e)}})},2086:(e,t,r)=>{r.d(t,{Z:()=>SectionMinistries});var i=r(997),s=r(7518),a=r.n(s),o=r(7467),n=r(2556);function SectionMinistries({ministries:e}){if(e?.length!==0)return(0,i.jsxs)(l,{children:[i.jsx("div",{children:i.jsx("p",{className:"label-type",children:"Sous cat\xe9gories associ\xe9es"})}),i.jsx("div",{children:e?.map((e,t)=>i.jsx("div",{children:i.jsx(n.Z,{image:e.cover,text:e.name,route:`/categories/${e.type}/${e.slug}`})},t))})]})}let l=a().div.withConfig({componentId:"sc-67b1d6e9-0"})`
  .ministries {
    margin-left: 38px;
  }
  @media ${o.U.tablet} {
    .ministries {
      margin-left: 50px;
    }
  }
  @media ${o.U.desktop} {
    flex-direction: row;
    .ministries {
      margin-left: 56px;
    }
  }
`},1980:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{default:()=>PageVocation,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var s=r(997),a=r(9114),o=r(1385),n=r(7518),l=r.n(n),c=r(7467),p=r(635),u=r(465),d=r(2325),h=r(2086),g=r(8657),m=r(9296),x=r(1632),f=r(2333),w=r(7438),y=r(723),v=e([g]);function PageVocation({vocation:e,fallback:t}){let r=t?.posts?.totalHits||0,i=t?.posts?.hits,a=t?.cornerStonesFeatured||[];return e?(0,s.jsxs)(P,{children:[s.jsx(w.Z,{category:e,type:"vocation"}),(0,s.jsxs)(q,{children:[e?.children?.length>0&&s.jsx("section",{className:"section-ministries",children:s.jsx(h.Z,{ministries:e.children})}),s.jsx("section",{children:a[0]&&s.jsx(g.g4,{content:a[0]})}),(0,s.jsxs)(b,{className:"site-padding",children:[s.jsx("p",{className:"label-type",children:i?.length>0?"Derni\xe8res ressources":""}),(0,s.jsxs)("div",{className:"posts-container ",children:[(0,s.jsxs)(j,{children:[s.jsx("ul",{className:"list-container",children:i?.map((e,t)=>{let r=p.fw(e.modules);return e.lead||(e.lead=r?.lead),s.jsx("li",{className:"post-card-li",children:s.jsx(d.Z,{post:e,options:{showLead:!0,showDate:!0,showAuthor:!0}})},`post-${t}`)})}),s.jsx(u.Z,{nbHits:r,baseUrl:`/categories/vocation/${e.slug}/ressources?page=`,currentPage:1,options:{postPerPage:15}})]}),s.jsx(S,{children:(0,s.jsxs)("div",{className:"cornerstone-container",children:[a[1]&&s.jsx(m.Z,{post:a[1],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}}),a[2]&&s.jsx(m.Z,{post:a[2],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}})]})})]})]}),s.jsx("section",{children:a[3]&&s.jsx(g.g4,{content:a[3]})})]})]}):null}async function getStaticProps({params:e}){let t=await o.Z.query({query:$,variables:{slug:e.vocation}}).then(e=>e.data.topicGroups[0]);if(!t)return{notFound:!0};let r=[];t?.topics&&r.push(...t.topics),t.children.forEach(e=>{e.topics.forEach(e=>{r.push(e)})}),r=r.filter((e,t,r)=>r.findIndex(t=>t.id===e.id)===t);let i=(0,x.D)(r),s=[],a=[null,null,null,null];if(i.length>0){s=await (0,f.j)({},i,15);let e=i.length>0?" AND ":"",r=`cs=true ${e} (${i})`,n=await y.V.searchHighlight("",{filter:r,sort:["date:desc"],limit:4}),l=t?.featured?.filter(e=>!e.inColumn),c=t?.featured?.filter(e=>e.inColumn),enrichFeaturedWithAuthor=async e=>{if(!e)return null;let t={...e};if(e.postRef?.author?.fullName)return t.author=e.postRef.author.fullName,t;if(e.cta?.url&&!e.author)try{let r=e.cta.url.split("/"),i=r[r.length-1];if(i){let{data:e}=await o.Z.query({query:_,variables:{slug:i}});e?.posts?.length>0&&(t.author=e.posts[0].author?.fullName||null)}}catch(t){console.error("Erreur lors de la r\xe9cup\xe9ration de l'auteur pour:",e.title,t)}return t},p=l||[],u=c||[];try{p=await Promise.all((l||[]).map(enrichFeaturedWithAuthor)),u=await Promise.all((c||[]).map(enrichFeaturedWithAuthor))}catch(e){console.error("Erreur lors de l'enrichissement des featured:",e)}a[0]=p[0]?p[0]:n?.hits[0]?n?.hits.shift():null,a[1]=u[0]?u[0]:n?.hits[0]?n?.hits.shift():null,a[2]=u[1]?u[1]:n?.hits[0]?n?.hits.shift():null,a[3]=p[1]?p[1]:n?.hits[0]?n?.hits.shift():null,s.hits=s?.hits?.filter(e=>!a?.find(t=>t?.route===e?.route||t?.cta?.url===e?.route))}return{props:{vocation:t,fallback:{posts:s,cornerStonesFeatured:a}},revalidate:10}}async function getStaticPaths(){let e=await o.Z.query({query:k}).then(e=>e.data.topicGroups);return{paths:e.map(e=>({params:{vocation:e.slug}})),fallback:!0}}g=(v.then?(await v)():v)[0];let b=l().section.withConfig({componentId:"sc-885a80ed-0"})`
  margin-top: 48px;
  .posts-container {
    display: block;
  }
  @media ${c.U.desktop} {
    margin-top: 96px;
    .posts-container {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
`,j=l().article.withConfig({componentId:"sc-885a80ed-1"})`
  width: 100%;
  margin-bottom: 64px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }
  .post-card-li {
    list-style: none;
    padding-right: 0;
  }

  @media ${c.U.desktop} {
    margin-bottom: 164px;
    width: 66.7%;
    .post-card-li {
      padding-right: 142px;
    }
  }
`,S=l().div.withConfig({componentId:"sc-885a80ed-2"})`
  position: relative;
  width: 100%;
  .cornerstone-container {
    /* position: sticky;
    top: 60px; */
  }
  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }
  @media ${c.U.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${c.U.desktop} {
    width: 33.3%;
  }
`,P=l().div.withConfig({componentId:"sc-885a80ed-3"})`
  .label-type {
    font-size: 16px;
    font-family: Stelvio, sans-serif;
    margin: 0 0 16px 0;

    font-weight: 500;
    letter-spacing: 4%;
    line-height: 32px;
  }
`,q=l().main.withConfig({componentId:"sc-885a80ed-4"})`
  //margin-top: 0;
  .section-ministries {
    margin-top: 64px;
    padding: 0 var(--border-space);
    margin-bottom: 80px;
  }
`,_=a.gql`
  query GetPostAuthorBySlug($slug: String!) {
    posts(where: { slug: $slug }) {
      author {
        fullName
      }
    }
  }
`,k=a.gql`
  query Vocations {
    topicGroups {
      slug
    }
  }
`,$=a.gql`
  query Vocations($slug: String!) {
    topicGroups(where: { slug: $slug }) {
      name
      description
      slug
      cover {
        formats
      }
      topics {
        id
        name
        postCount
      }
      children {
        id
        name
        slug
        type
        topics {
          id
          name
          postCount
        }
      }
      featured {
        title
        description
        inColumn
        image {
          url
          height
          width
          alternativeText
          provider
        }
        cta {
          name
          url
        }
        color {
          foreground
          background
        }
        type
        postRef {
          author {
            fullName
          }
        }
      }
    }
  }
`;i()}catch(e){i(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},9997:e=>{e.exports=require("meilisearch")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,9755,4033,779,7113,6453,1077,7620,8672,2832,8657,723,5745,4092,142,8074,9296,7438,6920],()=>__webpack_exec__(8087));module.exports=r})();