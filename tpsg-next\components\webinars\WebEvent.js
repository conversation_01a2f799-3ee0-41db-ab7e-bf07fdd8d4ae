import styled from "styled-components";
import { dateForHumans } from "utils/date.utils";
import Image from "next/image";
import Link from "next/link";
import { withRealSrc } from "utils/image-utils";

export default function WebEvent({ webEvent }) {
  const eventDate = dateForHumans(webEvent.modules.event.date);
  return (
    <Wrapper className={"site-padding"}>
      <h2>Prochaine webinaire:</h2>
      <h3>{webEvent.title}</h3>
      <p className={"webevent-date"}> Date : {eventDate}</p>
      <div className={"image-wrapper"}>
        <Image src={withRealSrc(webEvent.image)} layout={"fill"}/>
      </div>
      <Link href={`/webinaires/${webEvent.slug}`} >
        <p className={"webevent-cta"}> En savoir plus </p>
      </Link>
      <a href={webEvent.modules.event.url} target="_blank" rel="noopener noreferrer">
        <p className={"webevent-cta"}>INSCRIPTION</p>
      </a>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  padding-top: 24px;
  height: 40vw;
  background-color: orange;
  .webevent-date {
    display: inline-block;
    padding: 12px;
    font-size: 32px;
    color: white;
  }
  .webevent-cta {
    display: inline-block;
    text-align: center;
    height: 50px;
    padding-top: 16px;
    width: 120px;
    color: white;
    border-radius: 100px;
    background-color: green;
    cursor: pointer;
  }
  .image-wrapper {
    position: relative;
    width: 400px;
    height: 200px;
  }
`
