"use strict";

/**
 * Cron config that gives you an opportunity
 * to run scheduled jobs.
 *
 * The cron format consists of:
 * [SECOND (optional)] [MINUTE] [HOUR] [DAY OF MONTH] [MONTH OF YEAR] [DAY OF WEEK]
 *
 * See more details here: https://strapi.io/documentation/developer-docs/latest/setup-deployment-guides/configurations.html#cron-tasks
 */

const fs = require("fs");
const path = require("path");
const filePath = ( "./meilisearch_update.json" );
const meili = require("./meilisearch");

// Store the last update time in memory to avoid file writes during development
let lastUpdateTime = new Date();
let taskRunning = false;

module.exports = {
  // Tâche cron réactivée et adaptée pour Meilisearch v0.27.0
  "*/5 * * * *": async ctx => {
    try {
      if (taskRunning) {
        console.log("cron-task: A task is already running: abort");
        return null;
      }

      console.log("cron-task: No running task: start new update");
      taskRunning = true;

      const index = await meili.getIndex();
      console.log('cron-task: meili.getIndex done');

      if (!index) {
        console.log("cron-task: missing post index");
        taskRunning = false;
        return null;
      }

      /**
       * time {object}
       *
       * now: date du début de la tâche cron
       * lastUpdate: date du dernier passage
       */
      const time = {
        now: new Date(),
        lastUpdate: lastUpdateTime
      }
      console.log('cron-task: time.now', time.now);
      console.log('cron-task: time.lastUpdate', time.lastUpdate);

      // Publication des posts programmés
      await publishScheduledPosts();
      console.log('cron-task: publishScheduledPosts done');

      // Passage en draft pour les popups dont la date de fin est dépassée
      await unpublishPopups();
      console.log('cron-task: unpublishPopups done');

      // Post ajoutés depuis la dernière mise à jour
      const newCronPosts = await strapi.services["cron-post"].find(
        {created_at_gt: time.lastUpdate}
      );
      console.log('cron-task: newCronPosts', newCronPosts);

      // Crée ou met à jour les posts ayant subits des modifications.
      // Note: seuls les posts publiés sont récupérés. la fonction
      // updateDocuments de la lib meilisearch ajoute le post s'il
      // n'existe pas ou le modifie avec les nouvelles valeurs s'il
      // est déjà présent dans l'index.
      if (index) {
        try {
          const updatedPosts = await createOrUpdateDocuments(
            newCronPosts.filter(ncp => ncp.trigger === "update"),
            index
          );

          console.log('cron-task: UPDATED POSTS', updatedPosts);
          console.log("cron-task: ----------");

          // Suppression des posts de l'index
          let resDeleted = await deletedDocuments(
            newCronPosts.filter(ncp => ncp.trigger === "delete"),
            index
          );

          console.log('cron-task: DELETED POSTS', resDeleted);
          console.log("cron-task: ----------");
        } catch (error) {
          console.error("cron-task: Erreur lors de la mise à jour de Meilisearch:", error.message);
        }
      }

      // Update the in-memory timestamp
      lastUpdateTime = new Date();
      console.log("cron-task: New meilisearch update at", lastUpdateTime);

      // Only write to file in production mode to avoid Strapi restarts in development
      if (process.env.NODE_ENV === 'production') {
        setLastUpdateDate();
      }

      taskRunning = false;
    } catch (error) {
      console.error("cron-task: Erreur globale dans la tâche cron:", error.message);
      // S'assurer que taskRunning est remis à false en cas d'erreur
      taskRunning = false;
    }
  },
};

/**
 * Publie les posts qui ont étés programmés pour une date inférieure à la date actuelle
 * @return {Promise<void>}
 */
async function publishScheduledPosts() {
  console.log("cron-task: ----------");
  // On récupère tous les posts qui possèdent une date de programmation, peu importe
  // qu'ils soient déjà publiés ou non
  const scheduledPosts = await strapi.services.post.find({
    _publicationState: "preview", // preview returns both draft and published entries
    // published_at_null: true,
    publish_at_lt: new Date(),
  });
  console.log("cron-task: strapi.services.post.find done");


  if (!scheduledPosts.length) {
    console.log("cron-task: scheduledPosts.length is null");
    return null
  }

  await Promise.all(scheduledPosts.map(post => {
    return strapi.services.post.update(
      { id: post.id },
      {
        _publicationState: "published",
        published_at: post.publish_at,
        publish_at: null,
        noTrigger: true
      }
    )
  }));
}

/**
 * Dépublie les popups dont la date de fin est dépassée
 * @return {Promise<void>}
 */
async function unpublishPopups() {

  const endPopups = await strapi.services.popup.find({
    endDate_lt: new Date(),
  });

  await Promise.all(endPopups.map(popup => {
    return strapi.services.popup.update(
      { id: popup.id},
      { published_at: null}
    )
  }));
}

/**
 * Ajoute les nouveaux posts publiés à l'index de meilisearch.
 * Les posts déjà présents dans l'index sont mis à jour.
 * @param cronPosts
 * @return {Promise<number|*>}
 */
async function createOrUpdateDocuments(cronPosts, index) {
  try {
    const postIds = cronPosts.map((cp) => cp.postId);
    console.log("cron-task: Meilisearch (update/create): ", postIds);
    // Récupération des posts à ajouter ou mettre à jour
    let postsToPublish = await strapi.services.post.find({id_in: postIds});

    if (postsToPublish.length) {
      // Formatage des posts pour l'index
      const preparedPosts = meili.preparePosts(postsToPublish);
      // Ajout et mise à jour des posts
      return await index.updateDocuments(preparedPosts);
    } else {
      return null;
    }
  } catch (error) {
    console.error("cron-task: Erreur lors de la mise à jour des documents dans Meilisearch:", error.message);
    return null;
  }
}


/**
 * Supprime les posts qui n'existent plus de l'index de meilisearch
 * @param cronPosts
 * @param index
 * @return {Promise<*>}
 */
async function deletedDocuments(cronPosts, index) {
  try {
    const postIds = cronPosts.map((cp) => `post-${cp.postId}`);
    console.log("cron-task: Meilisearch (delete): ", postIds)
    if (postIds.length) {
      return await index.deleteDocuments(postIds);
    } else {
      return null;
    }
  } catch (error) {
    console.error("cron-task: Erreur lors de la suppression des documents dans Meilisearch:", error.message);
    return null;
  }
}

/**
 * Récupération de la date de la dernière mise à jour depuis le fichier
 * Cette fonction est maintenue pour la compatibilité avec le mode production
 * @returns {Date}
 */
function getLastUpdateDate() {
  try {
    // Try to read from file first (for when server restarts)
    const fileData = fs.readFileSync(filePath, "utf8");
    const date = JSON.parse(fileData)["last-update"];
    // Update the in-memory timestamp with the file value
    lastUpdateTime = new Date(date);
    return lastUpdateTime;
  } catch (err) {
    console.error("Erreur lors de la lecture du fichier de mise à jour:", err.message);
    // If file doesn't exist or is invalid, return the in-memory timestamp
    return lastUpdateTime;
  }
}

/**
 * Enregistrement de la date de la dernière mise à jour dans le fichier
 * Cette fonction est principalement utilisée en production
 */
function setLastUpdateDate() {
  const currentTime = new Date();
  const uDate = {"last-update": currentTime};
  const uDateJsonString = JSON.stringify(uDate, null, 2);

  // Use writeFileSync to avoid file watching issues
  try {
    fs.writeFileSync(filePath, uDateJsonString);
    console.log("cron-task: Saved meilisearch update timestamp to file at", currentTime);
  } catch (err) {
    console.error("Erreur lors de l'écriture du fichier de mise à jour:", err.message);
  }
}
