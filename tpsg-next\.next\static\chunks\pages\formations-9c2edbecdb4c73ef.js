(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[781],{1981:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/formations",function(){return t(6761)}])},6761:function(n,e,t){"use strict";t.r(e),t.d(e,{__N_SSG:function(){return Z},default:function(){return Formations}});var i=t(2729),a=t(5893),r=t(9521),o=t(785),l=t(1304),L=t(7421);function _templateObject(){let n=(0,i._)(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  position: absolute;\n  width: auto;\n  height: auto;\n  margin: 0;\n  display: none;\n  justify-content: right;\n  justify-self: right;\n  align-self: end;\n  bottom: 65px;\n  right: calc(50% - 50px);\n\n  .star-medal-head {\n    width: 100px;\n    height: 100px;\n    position: absolute;\n    z-index: 3;\n\n    &:hover {\n      cursor: pointer;\n      .star-medal {\n        animation: "," 20s linear infinite;\n      }\n    }\n    .arrow-medal {\n      position: absolute;\n      top: 50%;\n      right: 50%;\n      width: 30%;\n      transform: translate(50%, -50%);\n    }\n  }\n\n  .ribbon-medal {\n    width: 100px;\n    height: 100px;\n    position: absolute;\n    z-index: 2;\n    top: 81px;\n  }\n\n  @media screen and (min-width: 335px) {\n    display: flex; // show medal if screen as min value\n  }\n\n  @media "," {\n    position: absolute;\n    top: 24px;\n    right: 48px;\n    justify-self: center;\n    align-self: center;\n    .star-medal-head {\n      width: 150px;\n      height: 150px;\n    }\n\n    .ribbon-medal {\n      width: 150px;\n      height: 173px;\n      top: 105px;\n    }\n  }\n\n  @media "," {\n    position: absolute;\n    top: 36px;\n    right: 64px;\n    justify-self: center;\n    align-self: center;\n    .star-medal-head {\n      width: 170px;\n      height: 170px;\n    }\n\n    .ribbon-medal {\n      width: 170px;\n      height: 193px;\n      top: 132px;\n    }\n  }\n"]);return _templateObject1=function(){return n},n}function Medal(n){let{link:e}=n;return(0,a.jsxs)(d,{children:[(0,a.jsxs)("a",{className:"star-medal-head",href:e,target:"_blank",rel:"noreferrer",children:[(0,a.jsx)(StarMedalSvg,{}),(0,a.jsx)(ArrowMedalSvg,{})]}),(0,a.jsx)(RibbonMedalSvg,{})]})}t(1664);let s=(0,r.F4)(_templateObject()),d=r.ZP.div.withConfig({componentId:"sc-61e72520-0"})(_templateObject1(),s,L.U.tablet,L.U.desktop),StarMedalSvg=()=>(0,a.jsxs)("svg",{className:"star-medal",viewBox:"0 0 168 168",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("mask",{id:"path-1-inside-1_2187_1109",fill:"white",children:(0,a.jsx)("path",{d:"M82.453 1.02995C83.2539 0.000434127 84.8099 0.000432504 85.6105 1.02995L100.038 19.5852C100.588 20.2921 101.541 20.5473 102.37 20.21L124.148 11.3565C125.356 10.8653 126.703 11.6433 126.882 12.9352L130.098 36.2203C130.22 37.1073 130.918 37.8047 131.805 37.9274L155.089 41.1478C156.381 41.3265 157.159 42.674 156.667 43.8822L147.81 65.6579C147.472 66.4875 147.727 67.4401 148.434 67.9899L166.987 82.4214C168.016 83.2221 168.016 84.7781 166.986 85.5788L148.428 100.01C147.721 100.56 147.466 101.513 147.803 102.342L156.652 124.118C157.143 125.326 156.365 126.674 155.073 126.852L131.787 130.073C130.9 130.196 130.203 130.893 130.08 131.78L126.855 155.065C126.676 156.357 125.329 157.135 124.121 156.644L102.347 147.79C101.517 147.453 100.564 147.708 100.014 148.415L85.5794 166.97C84.7785 168 83.2225 168 82.422 166.97L67.994 148.415C67.4443 147.708 66.4917 147.453 65.6621 147.79L43.8848 156.644C42.6765 157.135 41.3291 156.357 41.1507 155.065L37.9346 131.78C37.8121 130.893 37.1148 130.196 36.2278 130.073L12.9433 126.852C11.6514 126.674 10.8736 125.326 11.3651 124.118L20.2227 102.342C20.5602 101.513 20.3051 100.56 19.5983 100.01L1.04577 85.5788C0.016402 84.7781 0.0166912 83.2221 1.04636 82.4214L19.6043 67.9899C20.3113 67.4401 20.5667 66.4875 20.2296 65.6579L11.3801 43.8822C10.8891 42.674 11.6673 41.3265 12.9593 41.1478L36.245 37.9274C37.1321 37.8047 37.8296 37.1073 37.9524 36.2203L41.1772 12.9352C41.3561 11.6433 42.7038 10.8653 43.9119 11.3565L65.6859 20.21C66.5154 20.5473 67.4681 20.2921 68.0181 19.5852L82.453 1.02995Z"})}),(0,a.jsx)("path",{d:"M82.453 1.02995C83.2539 0.000434127 84.8099 0.000432504 85.6105 1.02995L100.038 19.5852C100.588 20.2921 101.541 20.5473 102.37 20.21L124.148 11.3565C125.356 10.8653 126.703 11.6433 126.882 12.9352L130.098 36.2203C130.22 37.1073 130.918 37.8047 131.805 37.9274L155.089 41.1478C156.381 41.3265 157.159 42.674 156.667 43.8822L147.81 65.6579C147.472 66.4875 147.727 67.4401 148.434 67.9899L166.987 82.4214C168.016 83.2221 168.016 84.7781 166.986 85.5788L148.428 100.01C147.721 100.56 147.466 101.513 147.803 102.342L156.652 124.118C157.143 125.326 156.365 126.674 155.073 126.852L131.787 130.073C130.9 130.196 130.203 130.893 130.08 131.78L126.855 155.065C126.676 156.357 125.329 157.135 124.121 156.644L102.347 147.79C101.517 147.453 100.564 147.708 100.014 148.415L85.5794 166.97C84.7785 168 83.2225 168 82.422 166.97L67.994 148.415C67.4443 147.708 66.4917 147.453 65.6621 147.79L43.8848 156.644C42.6765 157.135 41.3291 156.357 41.1507 155.065L37.9346 131.78C37.8121 130.893 37.1148 130.196 36.2278 130.073L12.9433 126.852C11.6514 126.674 10.8736 125.326 11.3651 124.118L20.2227 102.342C20.5602 101.513 20.3051 100.56 19.5983 100.01L1.04577 85.5788C0.016402 84.7781 0.0166912 83.2221 1.04636 82.4214L19.6043 67.9899C20.3113 67.4401 20.5667 66.4875 20.2296 65.6579L11.3801 43.8822C10.8891 42.674 11.6673 41.3265 12.9593 41.1478L36.245 37.9274C37.1321 37.8047 37.8296 37.1073 37.9524 36.2203L41.1772 12.9352C41.3561 11.6433 42.7038 10.8653 43.9119 11.3565L65.6859 20.21C66.5154 20.5473 67.4681 20.2921 68.0181 19.5852L82.453 1.02995Z",fill:"#1C1C1C"}),(0,a.jsx)("path",{d:"M65.6859 20.21L65.1209 21.5996L65.6859 20.21ZM68.0181 19.5852L69.202 20.5062L68.0181 19.5852ZM41.1772 12.9352L39.6914 12.7294L39.6914 12.7294L41.1772 12.9352ZM43.9119 11.3565L44.4769 9.96696L43.9119 11.3565ZM36.245 37.9274L36.4505 39.4133L36.245 37.9274ZM37.9524 36.2203L39.4383 36.426L37.9524 36.2203ZM11.3801 43.8822L12.7697 43.3175L11.3801 43.8822ZM12.9593 41.1478L12.7538 39.662L12.9593 41.1478ZM19.6043 67.9899L18.6835 66.8058L19.6043 67.9899ZM20.2296 65.6579L18.84 66.2226L20.2296 65.6579ZM1.04577 85.5788L1.96675 84.3949L1.04577 85.5788ZM1.04636 82.4214L1.96717 83.6055L1.04636 82.4214ZM20.2227 102.342L21.6122 102.908L20.2227 102.342ZM19.5983 100.01L18.6773 101.194L19.5983 100.01ZM12.9433 126.852L13.1488 125.367L12.9433 126.852ZM11.3651 124.118L9.97565 123.553L11.3651 124.118ZM37.9346 131.78L36.4487 131.985L37.9346 131.78ZM36.2278 130.073L36.0223 131.559L36.2278 130.073ZM43.8848 156.644L43.3199 155.254L43.3199 155.254L43.8848 156.644ZM41.1507 155.065L42.6365 154.86L41.1507 155.065ZM67.994 148.415L66.8098 149.336L67.994 148.415ZM65.6621 147.79L66.227 149.18L65.6621 147.79ZM85.5794 166.97L86.7634 167.891L85.5794 166.97ZM82.422 166.97L83.6062 166.05L82.422 166.97ZM102.347 147.79L101.782 149.18L102.347 147.79ZM100.014 148.415L98.8305 147.494L100.014 148.415ZM126.855 155.065L125.369 154.859L126.855 155.065ZM124.121 156.644L123.556 158.033L124.121 156.644ZM131.787 130.073L131.582 128.587L131.787 130.073ZM130.08 131.78L131.566 131.986L130.08 131.78ZM156.652 124.118L155.263 124.683L156.652 124.118ZM155.073 126.852L155.279 128.338L155.073 126.852ZM148.428 100.01L149.349 101.194L148.428 100.01ZM147.803 102.342L149.193 101.778L147.803 102.342ZM166.987 82.4214L166.066 83.6054L166.987 82.4214ZM166.986 85.5788L166.065 84.3947L166.986 85.5788ZM147.81 65.6579L149.199 66.2231L147.81 65.6579ZM148.434 67.9899L149.355 66.8059L148.434 67.9899ZM155.089 41.1478L154.884 42.6337L155.089 41.1478ZM156.667 43.8822L158.057 44.4474L156.667 43.8822ZM130.098 36.2203L128.612 36.4255L130.098 36.2203ZM131.805 37.9274L132.01 36.4415L131.805 37.9274ZM124.148 11.3565L124.713 12.746L124.148 11.3565ZM126.882 12.9352L128.368 12.73L126.882 12.9352ZM100.038 19.5852L101.223 18.6644L100.038 19.5852ZM102.37 20.21L101.805 18.8205L102.37 20.21ZM85.6105 1.02995L84.4263 1.95071L85.6105 1.02995ZM82.453 1.02995L81.2691 0.108916L82.453 1.02995ZM84.4263 1.95071L98.8543 20.5059L101.223 18.6644L86.7946 0.10919L84.4263 1.95071ZM102.935 21.5996L124.713 12.746L123.583 9.96693L101.805 18.8205L102.935 21.5996ZM125.396 13.1404L128.612 36.4255L131.584 36.015L128.368 12.73L125.396 13.1404ZM131.599 39.4133L154.884 42.6337L155.295 39.662L132.01 36.4415L131.599 39.4133ZM155.278 43.3171L146.42 65.0927L149.199 66.2231L158.057 44.4474L155.278 43.3171ZM147.513 69.1739L166.066 83.6054L167.908 81.2374L149.355 66.8059L147.513 69.1739ZM166.065 84.3947L147.507 98.8262L149.349 101.194L167.907 86.7629L166.065 84.3947ZM146.413 102.907L155.263 124.683L158.042 123.553L149.193 101.778L146.413 102.907ZM154.868 125.367L131.582 128.587L131.993 131.559L155.279 128.338L154.868 125.367ZM128.594 131.574L125.369 154.859L128.341 155.271L131.566 131.986L128.594 131.574ZM124.686 155.254L102.912 146.401L101.782 149.18L123.556 158.033L124.686 155.254ZM98.8305 147.494L84.3955 166.049L86.7634 167.891L101.198 149.336L98.8305 147.494ZM83.6062 166.05L69.1781 147.494L66.8098 149.336L81.2379 167.891L83.6062 166.05ZM65.0972 146.401L43.3199 155.254L44.4497 158.033L66.227 149.18L65.0972 146.401ZM42.6365 154.86L39.4205 131.575L36.4487 131.985L39.6648 155.27L42.6365 154.86ZM36.4333 128.587L13.1488 125.367L12.7378 128.338L36.0223 131.559L36.4333 128.587ZM12.7545 124.683L21.6122 102.908L18.8333 101.777L9.97565 123.553L12.7545 124.683ZM20.5193 98.8264L1.96675 84.3949L0.124789 86.7628L18.6773 101.194L20.5193 98.8264ZM1.96717 83.6055L20.5251 69.174L18.6835 66.8058L0.125544 81.2373L1.96717 83.6055ZM21.6192 65.0932L12.7697 43.3175L9.99047 44.447L18.84 66.2226L21.6192 65.0932ZM13.1648 42.6337L36.4505 39.4133L36.0395 36.4415L12.7538 39.662L13.1648 42.6337ZM39.4383 36.426L42.663 13.141L39.6914 12.7294L36.4666 36.0145L39.4383 36.426ZM43.3469 12.746L65.1209 21.5996L66.2509 18.8205L44.4769 9.96696L43.3469 12.746ZM69.202 20.5062L83.637 1.95099L81.2691 0.108916L66.8341 18.6641L69.202 20.5062ZM65.1209 21.5996C66.5727 22.1899 68.2398 21.743 69.202 20.5062L66.8341 18.6641C66.6965 18.8411 66.4582 18.9048 66.2509 18.8205L65.1209 21.5996ZM42.663 13.141C42.7078 12.8177 43.0451 12.6233 43.3469 12.746L44.4769 9.96696C42.3626 9.10725 40.0045 10.4688 39.6914 12.7294L42.663 13.141ZM36.4505 39.4133C38.0028 39.1986 39.2233 37.9783 39.4383 36.426L36.4666 36.0145C36.4359 36.2364 36.2614 36.4109 36.0395 36.4415L36.4505 39.4133ZM12.7697 43.3175C12.647 43.0156 12.8415 42.6784 13.1648 42.6337L12.7538 39.662C10.4932 39.9746 9.13115 42.3325 9.99047 44.447L12.7697 43.3175ZM20.5251 69.174C21.7621 68.212 22.2092 66.545 21.6192 65.0932L18.84 66.2226C18.9242 66.4299 18.8604 66.6682 18.6835 66.8058L20.5251 69.174ZM1.96675 84.3949C1.70978 84.195 1.70939 83.806 1.96717 83.6055L0.125544 81.2373C-1.676 82.6383 -1.67697 85.3613 0.124789 86.7628L1.96675 84.3949ZM21.6122 102.908C22.2026 101.456 21.7563 99.7886 20.5193 98.8264L18.6773 101.194C18.8538 101.332 18.9177 101.57 18.8333 101.777L21.6122 102.908ZM13.1488 125.367C12.8261 125.322 12.6316 124.985 12.7545 124.683L9.97565 123.553C9.11566 125.667 10.4767 128.026 12.7378 128.338L13.1488 125.367ZM39.4205 131.575C39.2061 130.022 37.9857 128.802 36.4333 128.587L36.0223 131.559C36.2439 131.589 36.4181 131.764 36.4487 131.985L39.4205 131.575ZM43.3199 155.254C43.0176 155.377 42.6811 155.182 42.6365 154.86L39.6648 155.27C39.9771 157.531 42.3353 158.893 44.4497 158.033L43.3199 155.254ZM69.1781 147.494C68.2161 146.257 66.5489 145.81 65.0972 146.401L66.227 149.18C66.4346 149.095 66.6726 149.159 66.8098 149.336L69.1781 147.494ZM84.3955 166.049C84.195 166.307 83.806 166.307 83.6062 166.05L81.2379 167.891C82.6391 169.693 85.3621 169.693 86.7634 167.891L84.3955 166.049ZM102.912 146.401C101.46 145.81 99.7927 146.257 98.8305 147.494L101.198 149.336C101.336 149.159 101.574 149.095 101.782 149.18L102.912 146.401ZM125.369 154.859C125.325 155.183 124.987 155.377 124.686 155.254L123.556 158.033C125.67 158.893 128.028 157.531 128.341 155.271L125.369 154.859ZM131.582 128.587C130.03 128.802 128.809 130.022 128.594 131.574L131.566 131.986C131.597 131.764 131.771 131.589 131.993 131.559L131.582 128.587ZM155.263 124.683C155.385 124.985 155.191 125.322 154.868 125.367L155.279 128.338C157.539 128.026 158.901 125.668 158.042 123.553L155.263 124.683ZM147.507 98.8262C146.27 99.7882 145.823 101.455 146.413 102.907L149.193 101.778C149.108 101.57 149.172 101.332 149.349 101.194L147.507 98.8262ZM166.066 83.6054C166.323 83.8053 166.323 84.1943 166.065 84.3947L167.907 86.7629C169.708 85.362 169.709 82.639 167.908 81.2374L166.066 83.6054ZM146.42 65.0927C145.83 66.5443 146.276 68.2116 147.513 69.1739L149.355 66.8059C149.179 66.6686 149.115 66.4306 149.199 66.2231L146.42 65.0927ZM154.884 42.6337C155.206 42.6783 155.401 43.0149 155.278 43.3171L158.057 44.4474C158.917 42.3332 157.556 39.9747 155.295 39.662L154.884 42.6337ZM128.612 36.4255C128.826 37.978 130.047 39.1985 131.599 39.4133L132.01 36.4415C131.789 36.4109 131.614 36.2367 131.584 36.015L128.612 36.4255ZM124.713 12.746C125.015 12.6232 125.351 12.8178 125.396 13.1404L128.368 12.73C128.055 10.4688 125.697 9.10734 123.583 9.96693L124.713 12.746ZM98.8543 20.5059C99.8164 21.7432 101.484 22.1898 102.935 21.5996L101.805 18.8205C101.598 18.9049 101.36 18.8409 101.223 18.6644L98.8543 20.5059ZM86.7946 0.10919C85.3934 -1.69284 82.6704 -1.69237 81.2691 0.108916L83.637 1.95099C83.8375 1.69324 84.2265 1.6937 84.4263 1.95071L86.7946 0.10919Z",fill:"#EBEBEB",mask:"url(#path-1-inside-1_2187_1109)"})]}),RibbonMedalSvg=()=>(0,a.jsxs)("svg",{viewBox:"0 0 115 193",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"ribbon-medal",children:[(0,a.jsxs)("g",{filter:"url(#filter0_d_2187_1105)",children:[(0,a.jsx)("path",{d:"M10.1238 0.680664H55.3543V126.7L4.73926 185L10.1238 0.680664Z",fill:"#242424"}),(0,a.jsx)("path",{d:"M105.185 0.680664H59.9543V126.7L110.569 185L105.185 0.680664Z",fill:"#242424"})]}),(0,a.jsx)("defs",{children:(0,a.jsxs)("filter",{id:"filter0_d_2187_1105",x:"0.739258",y:"0.680664",width:"113.83",height:"192.319",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB",children:[(0,a.jsx)("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),(0,a.jsx)("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),(0,a.jsx)("feOffset",{dy:"4"}),(0,a.jsx)("feGaussianBlur",{stdDeviation:"2"}),(0,a.jsx)("feComposite",{in2:"hardAlpha",operator:"out"}),(0,a.jsx)("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}),(0,a.jsx)("feBlend",{mode:"normal",in2:"BackgroundImageFix",result:"effect1_dropShadow_2187_1105"}),(0,a.jsx)("feBlend",{mode:"normal",in:"SourceGraphic",in2:"effect1_dropShadow_2187_1105",result:"shape"})]})})]}),ArrowMedalSvg=()=>(0,a.jsx)("svg",{width:"46",height:"19",viewBox:"0 0 46 19",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"arrow-medal",children:(0,a.jsx)("path",{d:"M45.2351 9.0808L45.6242 10.002L45.5851 8.14404L45.2351 9.0808ZM0.330566 10.0808H45.2351V8.0808H0.330566V10.0808ZM45.2351 9.0808C45.5851 8.14404 45.5853 8.14412 45.5855 8.14419C45.5856 8.14421 45.5857 8.14428 45.5858 8.14432C45.5861 8.1444 45.5862 8.14446 45.5864 8.14451C45.5866 8.14461 45.5867 8.14465 45.5867 8.14464C45.5866 8.14461 45.586 8.14436 45.5847 8.14388C45.5822 8.14292 45.5773 8.14105 45.5702 8.13826C45.5559 8.13267 45.5324 8.12337 45.5001 8.11021C45.4357 8.0839 45.3363 8.04219 45.2058 7.98392C44.9446 7.86734 44.5588 7.68465 44.0774 7.42659C43.1141 6.91023 41.771 6.09372 40.2783 4.90364L39.0315 6.46746C40.6319 7.74344 42.0789 8.62456 43.1325 9.18931C43.6595 9.4718 44.0888 9.67554 44.3906 9.81025C44.5415 9.87762 44.6607 9.92777 44.7443 9.96191C44.7862 9.97898 44.8191 9.99205 44.8427 10.0013C44.8545 10.0059 44.864 10.0095 44.8711 10.0122C44.8747 10.0136 44.8776 10.0147 44.88 10.0156C44.8811 10.016 44.8821 10.0164 44.883 10.0168C44.8835 10.0169 44.8839 10.0171 44.8842 10.0172C44.8844 10.0173 44.8846 10.0174 44.8847 10.0174C44.8849 10.0175 44.8852 10.0176 45.2351 9.0808ZM40.2783 4.90364C38.863 3.7753 38.049 2.55825 37.5899 1.6363C37.36 1.17457 37.2192 0.786951 37.1373 0.522567C37.0964 0.390495 37.0703 0.28962 37.0552 0.226185C37.0477 0.194485 37.043 0.172196 37.0405 0.160102C37.0392 0.154058 37.0386 0.150569 37.0384 0.149733C37.0383 0.149316 37.0384 0.149562 37.0385 0.150485C37.0386 0.150947 37.0387 0.151578 37.0389 0.152379C37.039 0.15278 37.039 0.153224 37.0391 0.15371C37.0392 0.153953 37.0392 0.154351 37.0393 0.154472C37.0393 0.15488 37.0394 0.155298 36.0544 0.328125C35.0695 0.500952 35.0696 0.501392 35.0697 0.501844C35.0697 0.50201 35.0698 0.502473 35.0698 0.502804C35.0699 0.503467 35.0701 0.504174 35.0702 0.504926C35.0705 0.506431 35.0708 0.508114 35.0711 0.509975C35.0718 0.513696 35.0726 0.518128 35.0736 0.523256C35.0755 0.533512 35.0781 0.54656 35.0813 0.562294C35.0878 0.593759 35.0969 0.635998 35.1093 0.688175C35.1341 0.792495 35.1718 0.936774 35.2268 1.1143C35.3367 1.46911 35.5161 1.95846 35.7996 2.52784C36.3674 3.66808 37.3536 5.12974 39.0315 6.46746L40.2783 4.90364ZM45.2351 9.0808C44.8461 8.15958 44.846 8.15964 44.8458 8.1597C44.8458 8.15973 44.8456 8.15979 44.8455 8.15985C44.8452 8.15996 44.8449 8.1601 44.8445 8.16025C44.8438 8.16057 44.8428 8.16097 44.8417 8.16146C44.8393 8.16245 44.8362 8.1638 44.8322 8.1655C44.8242 8.1689 44.813 8.17373 44.7986 8.17994C44.7699 8.19238 44.7286 8.21038 44.6759 8.23372C44.5707 8.28039 44.42 8.34847 44.2333 8.43605C43.8604 8.61101 43.3418 8.86487 42.7552 9.1824C41.5963 9.80962 40.1129 10.7164 38.9713 11.7856L40.3385 13.2453C41.2933 12.351 42.6 11.5405 43.7072 10.9413C44.2535 10.6456 44.737 10.4089 45.0828 10.2467C45.2555 10.1656 45.3933 10.1034 45.4868 10.062C45.5335 10.0413 45.5691 10.0257 45.5924 10.0157C45.6041 10.0106 45.6126 10.007 45.618 10.0047C45.6206 10.0035 45.6225 10.0027 45.6236 10.0023C45.6241 10.0021 45.6244 10.0019 45.6245 10.0019C45.6245 10.0019 45.6245 10.0019 45.6245 10.0019C45.6245 10.0019 45.6244 10.0019 45.6244 10.0019C45.6243 10.002 45.6242 10.002 45.2351 9.0808ZM38.9713 11.7856C37.82 12.8639 36.8627 14.289 36.2067 15.4035C35.8744 15.9681 35.6105 16.4685 35.429 16.829C35.3381 17.0095 35.2676 17.1556 35.2193 17.2578C35.1951 17.3089 35.1764 17.3491 35.1634 17.3773C35.1569 17.3914 35.1519 17.4024 35.1483 17.4103C35.1465 17.4143 35.1451 17.4174 35.144 17.4198C35.1435 17.4209 35.1431 17.4219 35.1427 17.4227C35.1425 17.4231 35.1424 17.4234 35.1423 17.4237C35.1422 17.4238 35.1421 17.424 35.1421 17.4241C35.142 17.4242 35.142 17.4244 36.0544 17.8335C36.9669 18.2426 36.9669 18.2427 36.9668 18.2428C36.9668 18.2428 36.9668 18.2429 36.9668 18.243C36.9667 18.243 36.9667 18.2431 36.9667 18.2431C36.9667 18.243 36.9669 18.2428 36.9671 18.2423C36.9675 18.2414 36.9682 18.2397 36.9693 18.2373C36.9716 18.2324 36.9752 18.2244 36.9803 18.2134C36.9904 18.1914 37.0061 18.1577 37.0271 18.1132C37.0691 18.0243 37.1325 17.893 37.2154 17.7283C37.3815 17.3984 37.6245 16.9375 37.9303 16.418C38.5504 15.3644 39.3934 14.1304 40.3385 13.2453L38.9713 11.7856Z",fill:"#F8F8F3"})});var C=t(5599);function BannerFormation_templateObject(){let n=(0,i._)(["\n  margin-bottom: 48px; //place for medal\n  .banner-text-lastformation {\n    margin: 0 0 12px 0;\n    color: #c8c8c8;\n    font-size: 16px;\n  }\n\n  .banner-text-title {\n    font-weight: 500;\n    margin: 0px;\n    font-size: clamp(28px, 2.5vw, 48px);\n  }\n\n  .banner-text-lead {\n    display: -webkit-box;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    padding-right: 30px;\n    font-size: 16px;\n    color: #888888;\n  }\n\n  @media "," {\n    width: 70%;\n    .banner-text-lead {\n      font-size: 24px;\n    }\n\n    .banner-text-lastformation {\n      font-size: 20px;\n    }\n  }\n"]);return BannerFormation_templateObject=function(){return n},n}function BannerFormation_templateObject1(){let n=(0,i._)(['\n  position: absolute;\n  display: flex;\n  width: 100%;\n  height: 31px;\n\n  bottom: 0px;\n  right: 0px;\n\n  .banner-space-btn {\n    width: 140px;\n    display: none;\n  }\n\n  .banner-btn {\n    //button or link\n    font-family: "Stelvio", Arial, sans-serif;\n    text-align: center;\n    font-size: 14px;\n    line-height: 35px;\n    width: 100%;\n\n    background-color: #ebebeb;\n\n    margin-left: -1px;\n    margin-bottom: -1px;\n    border: 1px solid #242424;\n\n    &:hover {\n      cursor: pointer;\n    }\n  }\n\n  .black-btn {\n    background-color: #242424;\n    color: white;\n  }\n\n  @media screen and (min-width: 335px) {\n    .banner-space-btn {\n      display: flex;\n    }\n  }\n\n  @media '," {\n    position: relative;\n    .banner-btn {\n      width: auto;\n      font-size: 17px;\n      padding: 0px 16px;\n      margin-right: 24px;\n    }\n\n    .banner-space-btn {\n      display: none;\n    }\n  }\n"]);return BannerFormation_templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  z-index: 1;\n  padding: 20px;\n  max-width: 1126px;\n  width: 100%;\n\n  .banner-center-outside {\n    padding: 8px;\n    background-color: #ebebeb;\n\n    .banner-center-inside {\n      background-color: #ebebeb;\n      position: relative;\n      padding: 16px;\n      border: 1px solid #242424;\n    }\n  }\n\n  @media "," {\n    width: calc(100% - 50px);\n    .banner-center-outside {\n      padding: 24px;\n\n      .banner-center-inside {\n        border: 10px double #242424;\n        padding: 48px;\n      }\n    }\n  }\n  @media "," {\n    .banner-center-outside {\n      padding: 24px;\n\n      .banner-center-inside {\n        border: 10px double #242424;\n        padding: 64px;\n      }\n    }\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n  padding: 122px 0px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n  .banner-cover {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n  }\n\n  .banner-cover-relative {\n    position: relative;\n    width: 100%;\n    height: 100%;\n  }\n"]);return _templateObject3=function(){return n},n}function BannerFormation(n){var e;let{lastPost:t}=n,i=(0,o.fw)(null==t?void 0:t.modules),r=null==i?void 0:null===(e=i.formation)||void 0===e?void 0:e.youtubeEmbed;return i.formation?(0,a.jsxs)(x,{children:[(0,a.jsx)("div",{className:"banner-cover",children:(0,a.jsx)("div",{className:"banner-cover-relative",children:(0,a.jsx)(l.Z,{imageData:t.image,priority:!0})})}),(0,a.jsx)(m,{children:(0,a.jsx)("div",{className:"banner-center-outside",children:(0,a.jsxs)("div",{className:"banner-center-inside",children:[(0,a.jsxs)(p,{children:[(0,a.jsx)("p",{className:"banner-text-lastformation",children:"Derni\xe8re formation"}),(0,a.jsx)("h1",{className:"banner-text-title",children:t.title}),(0,a.jsx)("p",{className:"banner-text-lead",children:i.lead.content||""})]}),(0,a.jsxs)(c,{children:[(0,a.jsx)("button",{className:"banner-btn",children:r&&(0,a.jsx)(C.Z,{youtubeEmbed:r,children:(0,a.jsx)("div",{children:"Voir l'aper\xe7u"})})}),(0,a.jsx)("div",{className:"mobile-show banner-space-btn"}),(0,a.jsx)("a",{href:i.formation.link,target:"_blank",rel:"noreferrer",className:"banner-btn black-btn",children:"S'inscrire"})]}),(0,a.jsx)(Medal,{link:i.formation.link})]})})})]}):null}let p=r.ZP.div.withConfig({componentId:"sc-59728b75-0"})(BannerFormation_templateObject(),L.U.tablet),c=r.ZP.div.withConfig({componentId:"sc-59728b75-1"})(BannerFormation_templateObject1(),L.U.tablet),m=r.ZP.div.withConfig({componentId:"sc-59728b75-2"})(_templateObject2(),L.U.tablet,L.U.desktop),x=r.ZP.div.withConfig({componentId:"sc-59728b75-3"})(_templateObject3());var h=t(6368),b=t(5985),f=t(6303),u=t(6268);function formations_templateObject(){let n=(0,i._)([""]);return formations_templateObject=function(){return n},n}var Z=!0;function Formations(n){var e;let{pageContent:t}=n;if(!t)return null;let i=t.sections.filter(n=>n.__typename="ComponentSectionPostSet"),r=null===(e=i.filter(n=>"last-formation"===n.name)[0])||void 0===e?void 0:e.posts[0],l=i.filter(n=>"last-formation"!==n.name);return(0,a.jsxs)(M,{children:[(0,a.jsx)(u.Z,{hexLight:"#FFFFFF",hexDark:"#262424"}),(0,a.jsx)(h.V1,{className:"site-padding",children:"Formations"}),r&&(0,a.jsx)(BannerFormation,{lastPost:r}),(0,a.jsx)("div",{className:"site-padding",children:l.map((n,e)=>(0,a.jsx)(b.Z,{nameSection:n.name,children:n.posts.map((n,e)=>{var t,i,r,l;let L=(0,o.fw)(n.modules),s="";return null==L||null===(t=L.formation)||void 0===t||t.speakers.filter(n=>null==n?void 0:n.fullName).map((n,e)=>{var t;e!==(null==L?void 0:null===(t=L.formation)||void 0===t?void 0:t.speakers.length)-1?s+="".concat(n.fullName," / "):s+="".concat(n.fullName)}),(0,a.jsx)(f.Z,{post:{title:n.title,image:n.image,link:null==L?void 0:null===(i=L.formation)||void 0===i?void 0:i.link,youtubeEmbed:null==L?void 0:null===(r=L.formation)||void 0===r?void 0:r.youtubeEmbed,details:s,lead:null==L?void 0:null===(l=L.lead)||void 0===l?void 0:l.content,postType:"formation"}},e)})},e))})]})}let M=r.ZP.div.withConfig({componentId:"sc-ade24229-0"})(formations_templateObject())}},function(n){n.O(0,[755,21,774,888,179],function(){return n(n.s=1981)}),_N_E=n.O()}]);