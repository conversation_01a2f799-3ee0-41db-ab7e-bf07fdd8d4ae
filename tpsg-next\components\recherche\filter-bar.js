import { useState } from "react";
import styled from "styled-components";
import { device } from "styles/device";

const preFilterMap = {
  "type": "Dan<PERSON>",
  "author": "Par",
  "topics": "Dans"
}

const FilterBar = ({ listState, changeList, changeFilter }) => {

  const [lastClick, setLastClick] = useState(null);

  const handleClick = (listType) => {
    const currentTime = new Date().getTime();
    // Check si le boutton a été clicker les 500 dernières ms
    if (!lastClick || currentTime - lastClick > 500) {
      setLastClick(currentTime);
      changeList(listType);
    }
  }
  
  return (
    <Wrapper>
      <Content filterActive={listState.activeFilter} className={listState.filterOpen ? "removing" : ""}>
        <Buttons>
          <ListButton
            active={listState.activeList === "type" && listState.listOpen}
            onClick={() => handleClick("type")}>
            <p>Type</p>
            <ChevArrow/>
          </ListButton>
          <ListButton
            active={listState.activeList === "author" && listState.listOpen}
            onClick={() => handleClick("author")}>
            <p>Auteur</p>
            <ChevArrow/>
          </ListButton>
          <ListButton
            active={listState.activeList === "topic" && listState.listOpen}
            onClick={() => handleClick("topics")}>
            <p>Thème</p>
            <ChevArrow/>
          </ListButton>
        </Buttons>
        {listState.activeFilter &&
          <SelectedFilter onClick={() => changeFilter(null, null)}>
            <p className={!listState.filterOpen ? "erase" : ""}>
              <span>{preFilterMap[listState.filterType]}: </span>
              {listState.activeFilter}
              <span
                className={!listState.queryTerms.length ? "ready eraser" : "eraser"}>
                <TabArrow/>
              </span>
            </p>
          </SelectedFilter>
        }
      </Content>
    </Wrapper>

  )
}

export default FilterBar;

const Wrapper = styled.div`
  position: relative;
  box-sizing: content-box;
  height: 30px;
  width: 100%;
  overflow: hidden;
  border-top: 1.5px solid black;
  margin-top: -12px;

  .removing {
    transition-delay: 0ms;
  }

  .ready {
    transform: translateX(-12px);
  }

  z-index: 20;

  @media ${device.tablet} {
    height: 52px;
    margin-top: -16px;
    border-top: 2px solid black;
  }
`;

const Content = styled.div`
  position: absolute;
  width: 100%;
  top: ${props => props.filterActive ? "-30px" : "0"};
  left: 0;
  transition: all 550ms cubic-bezier(1, 0.72, 0.15, 1.01);
  transition-delay: 250ms;

  @media ${device.tablet} {
    top: ${props => props.filterActive ? "-52px" : "0"};
  }
`;

const Buttons = styled.div`
  position: relative;
  width: 100%;
  height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;

  @media ${device.tablet} {
    height: 52px;
  }
`;


const ListButton = styled.div`
  position: relative;
  width: calc(33% + 32px);
  height: 100%;
  padding-left: 16px;
  margin-left: -16px;
  border-left: 1.5px solid black;
  cursor: pointer;

  @media ${device.tablet} {
    border-left: 2px solid black;
  }

  &:hover {
    svg {
      path {
        fill: orangered;
      }
    }
  }

  p {
    position: relative;
    margin-top: 8px;
    font-size: 16px;
    font-family: "Stelvio Grotesk", sans-serif;
    font-weight: 500;
  }

  svg {
    position: absolute;
    right: 26px;
    top: 10px;
    transform: rotateX(${p => p.active ? "180deg" : 0}) scale(0.65);
    transition: transform 350ms cubic-bezier(1, 0.72, 0.15, 1.01);
  }

  &:last-child {
    svg {
      right: 10px;
    }
  }

  @media ${device.tablet} {
    p {
      margin-top: 16px;
      font-size: 26px;
    }

    svg {
      top: 22px;
      right: 40px;
      transform: rotateX(${p => p.active ? "180deg" : 0}) scale(1);
    }

    &:last-child {
      svg {
        right: 10px;
      }
    }
  }
`;

const SelectedFilter = styled.div`
  position: absolute;
  top: 30px;
  width: 100%;
  height: 30px;

  &:hover {
    .eraser {
      transform: translateX(-16px);
    }
  }

  .eraser {
    position: absolute;
    top: 2px;
    left: 100%;
    margin-left: 24px;
    display: inline-block;
    height: 100%;
    width: calc(100% + 300px);
    background-color: var(--soft-white);
    transition: transform 200ms ease-out;

    svg {
      margin-top: -5px;
      width: 30px;
    }
  }

  .erase {
    .eraser {
      transition: transform 550ms cubic-bezier(1, 0.72, 0.15, 1.01);
      transform: translateX(-100%);
    }
  }

  p {
    position: relative;
    display: inline-block;
    font-family: "Stelvio Grotesk", Arial, sans-serif;
    font-weight: 500;
    font-size: 16px;
    margin-top: 8px;
    cursor: pointer;

    span {
      font-weight: 700;
    }
  }

  @media ${device.tablet} {
    top: 52px;
    height: 52px;

    .eraser {
      svg {
        margin-top: 0;
        width: auto;
      }
    }

    p {
      margin-top: 16px;
      font-size: 26px;
    }
  }
`;


const TabArrow = () => {
  return (
    <svg width="50" height="20" viewBox="0 0 50 21" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2.51012 10.8755L2.10624 9.96067L2.14632 11.807L2.51012 10.8755ZM49.1318 9.87549L2.51012 9.87549L2.51012 11.8755L49.1318 11.8755L49.1318 9.87549ZM2.51012 10.8755C2.14632 11.807 2.14611 11.8069 2.14591 11.8068C2.14586 11.8068 2.14567 11.8067 2.14557 11.8067C2.14535 11.8066 2.14518 11.8065 2.14505 11.8065C2.14478 11.8064 2.14467 11.8063 2.14471 11.8063C2.1448 11.8064 2.1455 11.8066 2.14681 11.8072C2.14945 11.8082 2.15452 11.8103 2.16198 11.8133C2.1769 11.8194 2.20136 11.8295 2.23488 11.8438C2.30193 11.8724 2.40521 11.9178 2.54093 11.9811C2.81243 12.1078 3.21343 12.3063 3.71381 12.5867C4.71494 13.1477 6.11131 14.0351 7.66351 15.3288L8.94401 13.7925C7.28483 12.4096 5.78437 11.4544 4.69153 10.842C4.14492 10.5357 3.69964 10.3147 3.38661 10.1687C3.23007 10.0956 3.10652 10.0413 3.01981 10.0043C2.97645 9.98578 2.94229 9.97162 2.91782 9.96163C2.90558 9.95663 2.89577 9.95268 2.88843 9.94975C2.88476 9.94829 2.88171 9.94708 2.87929 9.94612C2.87808 9.94564 2.87703 9.94523 2.87613 9.94488C2.87568 9.9447 2.87527 9.94454 2.87491 9.94439C2.87472 9.94432 2.87447 9.94423 2.87438 9.94419C2.87414 9.9441 2.87392 9.94401 2.51012 10.8755ZM7.66351 15.3288C9.13958 16.5591 9.99185 17.8891 10.4743 18.9018C10.7157 19.4087 10.864 19.835 10.9506 20.1274C10.9939 20.2734 11.0216 20.3855 11.0378 20.4569C11.0459 20.4925 11.0511 20.518 11.0539 20.5323C11.0553 20.5394 11.0561 20.5438 11.0564 20.5453C11.0565 20.5461 11.0565 20.5461 11.0564 20.5454C11.0564 20.545 11.0563 20.5445 11.0561 20.5438C11.0561 20.5434 11.056 20.543 11.0559 20.5425C11.0559 20.5423 11.0558 20.5419 11.0558 20.5418C11.0557 20.5414 11.0557 20.541 12.0419 20.3755C13.0281 20.21 13.028 20.2095 13.0279 20.2091C13.0279 20.2089 13.0278 20.2085 13.0278 20.2082C13.0277 20.2075 13.0275 20.2068 13.0274 20.2061C13.0272 20.2046 13.0269 20.2029 13.0266 20.201C13.0259 20.1973 13.0251 20.1928 13.0241 20.1875C13.0222 20.1769 13.0197 20.1634 13.0164 20.1469C13.01 20.1139 13.0007 20.0693 12.9881 20.0139C12.963 19.9032 12.9245 19.7492 12.8682 19.5592C12.7557 19.1795 12.5716 18.6541 12.2799 18.0417C11.6961 16.8162 10.6793 15.2388 8.94401 13.7925L7.66351 15.3288ZM2.51012 10.8755C2.91399 11.7903 2.91412 11.7902 2.91427 11.7902C2.91434 11.7901 2.91451 11.7901 2.91464 11.79C2.91491 11.7899 2.91524 11.7897 2.91562 11.7896C2.91639 11.7892 2.91737 11.7888 2.91857 11.7883C2.92098 11.7872 2.92425 11.7857 2.92838 11.7839C2.93663 11.7802 2.94828 11.775 2.96319 11.7683C2.993 11.7548 3.03582 11.7353 3.09041 11.7099C3.19957 11.6593 3.35598 11.5855 3.54965 11.4905C3.93656 11.3007 4.47456 11.0254 5.08316 10.681C6.28637 10.0002 7.82289 9.01787 9.00335 7.86213L7.60417 6.43304C6.60801 7.40835 5.24771 8.28995 4.09825 8.94034C3.53052 9.26158 3.02822 9.51863 2.66892 9.69486C2.48949 9.78287 2.34631 9.85043 2.24916 9.89547C2.2006 9.91799 2.16359 9.93485 2.13935 9.94581C2.12723 9.95129 2.11831 9.95529 2.11274 9.95778C2.10996 9.95902 2.10801 9.95989 2.10692 9.96037C2.10637 9.96061 2.10604 9.96076 2.10593 9.96081C2.10587 9.96083 2.10587 9.96084 2.10592 9.96081C2.10595 9.9608 2.10603 9.96076 2.10604 9.96076C2.10613 9.96072 2.10624 9.96067 2.51012 10.8755ZM9.00335 7.86213C10.1921 6.69825 11.1815 5.15892 11.8604 3.95298C12.2042 3.3424 12.4772 2.80121 12.6649 2.41154C12.7588 2.21647 12.8316 2.05871 12.8816 1.94846C12.9065 1.89332 12.9258 1.85 12.9391 1.81979C12.9457 1.80468 12.9509 1.79284 12.9545 1.78443C12.9564 1.78022 12.9578 1.77687 12.9589 1.7744C12.9594 1.77316 12.9598 1.77214 12.9602 1.77135C12.9604 1.77095 12.9605 1.77061 12.9606 1.77032C12.9607 1.77018 12.9608 1.77 12.9608 1.76993C12.9609 1.76977 12.9609 1.76963 12.0419 1.37549C11.1228 0.981341 11.1229 0.981223 11.1229 0.98112C11.1229 0.981102 11.123 0.981013 11.123 0.98098C11.123 0.980916 11.123 0.980904 11.123 0.980948C11.123 0.981036 11.1228 0.981343 11.1226 0.981865C11.1222 0.982909 11.1213 0.984814 11.1201 0.987563C11.1177 0.99306 11.1139 1.00193 11.1086 1.014C11.0979 1.03816 11.0815 1.07516 11.0595 1.12374C11.0155 1.22093 10.9494 1.36428 10.8629 1.54385C10.6897 1.90347 10.4364 2.40574 10.1177 2.9718C9.4717 4.11914 8.59202 5.46587 7.60417 6.43304L9.00335 7.86213Z"
        fill="black"/>
      <line x1="1.2666" y1="1.18481" x2="1.2666" y2="20.5662" stroke="black" strokeWidth="2"/>
    </svg>
  )
}

const ChevArrow = () => {
  return (
    <svg width="20" height="12" viewBox="0 0 20 12" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10.2925 10.9345L9.37766 11.3384L11.224 11.2983L10.2925 10.9345ZM9.29248 9.23675L9.29248 10.9345L11.2925 10.9345L11.2925 9.23675L9.29248 9.23675ZM10.2925 10.9345C11.224 11.2983 11.2239 11.2985 11.2238 11.2987C11.2238 11.2988 11.2237 11.299 11.2237 11.2991C11.2236 11.2993 11.2235 11.2994 11.2235 11.2996C11.2234 11.2998 11.2233 11.3 11.2233 11.2999C11.2234 11.2998 11.2236 11.2991 11.2242 11.2978C11.2252 11.2952 11.2273 11.2901 11.2303 11.2826C11.2364 11.2677 11.2465 11.2433 11.2608 11.2097C11.2894 11.1427 11.3348 11.0394 11.3981 10.9037C11.5248 10.6322 11.7233 10.2312 12.0037 9.7308C12.5647 8.72967 13.4521 7.3333 14.7458 5.7811L13.2095 4.5006C11.8266 6.15978 10.8714 7.66024 10.259 8.75309C9.95265 9.2997 9.73174 9.74498 9.58568 10.058C9.51264 10.2146 9.45827 10.3381 9.42127 10.4248C9.40277 10.4682 9.38861 10.5023 9.37862 10.5268C9.37363 10.539 9.36967 10.5489 9.36674 10.5562C9.36528 10.5599 9.36407 10.5629 9.36311 10.5653C9.36264 10.5665 9.36222 10.5676 9.36187 10.5685C9.36169 10.5689 9.36153 10.5694 9.36139 10.5697C9.36132 10.5699 9.36122 10.5702 9.36118 10.5702C9.36109 10.5705 9.361 10.5707 10.2925 10.9345ZM14.7458 5.7811C15.9761 4.30503 17.3061 3.45276 18.3188 2.97033C18.8256 2.72891 19.252 2.58064 19.5444 2.49402C19.6904 2.45075 19.8025 2.42302 19.8739 2.40681C19.9095 2.39871 19.9349 2.39351 19.9493 2.3907C19.9564 2.38929 19.9608 2.38848 19.9623 2.38821C19.9631 2.38807 19.9631 2.38807 19.9624 2.3882C19.962 2.38826 19.9615 2.38835 19.9607 2.38848C19.9604 2.38854 19.96 2.38861 19.9595 2.38869C19.9593 2.38873 19.9589 2.38879 19.9588 2.38881C19.9584 2.38887 19.958 2.38894 19.7925 1.40274C19.627 0.416532 19.6265 0.416603 19.6261 0.416677C19.6259 0.416704 19.6255 0.41678 19.6252 0.416835C19.6245 0.416944 19.6238 0.417063 19.6231 0.41719C19.6216 0.417445 19.6199 0.417736 19.618 0.418064C19.6143 0.418719 19.6098 0.419522 19.6045 0.420481C19.5939 0.422398 19.5804 0.424941 19.5639 0.428177C19.5309 0.434647 19.4863 0.443896 19.4309 0.456473C19.3202 0.481619 19.1662 0.520122 18.9762 0.576414C18.5965 0.688915 18.0711 0.873037 17.4587 1.16472C16.2332 1.7485 14.6558 2.76529 13.2095 4.5006L14.7458 5.7811ZM10.2925 10.9345C11.2073 10.5306 11.2072 10.5305 11.2072 10.5303C11.2071 10.5303 11.2071 10.5301 11.207 10.53C11.2069 10.5297 11.2067 10.5294 11.2066 10.529C11.2062 10.5282 11.2058 10.5273 11.2053 10.526C11.2042 10.5236 11.2027 10.5204 11.2009 10.5162C11.1972 10.508 11.192 10.4963 11.1852 10.4814C11.1718 10.4516 11.1522 10.4088 11.1269 10.3542C11.0763 10.2451 11.0025 10.0886 10.9075 9.89497C10.7177 9.50806 10.4424 8.97006 10.098 8.36146C9.41721 7.15825 8.43486 5.62172 7.27912 4.44126L5.85003 5.84044C6.82534 6.8366 7.70694 8.1969 8.35733 9.34636C8.67857 9.9141 8.93562 10.4164 9.11185 10.7757C9.19986 10.9551 9.26743 11.0983 9.31247 11.1955C9.33498 11.244 9.35185 11.281 9.3628 11.3053C9.36828 11.3174 9.37228 11.3263 9.37477 11.3319C9.37601 11.3347 9.37688 11.3366 9.37736 11.3377C9.37761 11.3382 9.37775 11.3386 9.3778 11.3387C9.37783 11.3387 9.37783 11.3388 9.37781 11.3387C9.37779 11.3387 9.37776 11.3386 9.37775 11.3386C9.37771 11.3385 9.37766 11.3384 10.2925 10.9345ZM7.27912 4.44126C6.11524 3.25249 4.57592 2.26312 3.36998 1.58417C2.75939 1.24041 2.2182 0.967376 1.82853 0.779742C1.63346 0.685808 1.47571 0.612967 1.36545 0.563062C1.31031 0.538102 1.26699 0.518856 1.23678 0.505555C1.22167 0.498904 1.20983 0.493737 1.20142 0.490083C1.19721 0.488256 1.19386 0.486808 1.19139 0.48574C1.19016 0.485207 1.18914 0.484768 1.18834 0.484426C1.18794 0.484255 1.1876 0.484107 1.18731 0.483984C1.18717 0.483923 1.187 0.483848 1.18692 0.483817C1.18677 0.483749 1.18662 0.483687 0.792477 1.40274C0.398334 2.32179 0.398216 2.32173 0.398113 2.32169C0.398096 2.32168 0.398006 2.32164 0.397974 2.32163C0.397909 2.3216 0.397898 2.3216 0.397942 2.32162C0.398031 2.32165 0.398336 2.32179 0.398859 2.32201C0.399902 2.32246 0.401808 2.32329 0.404556 2.32448C0.410053 2.32687 0.418919 2.33073 0.430998 2.33605C0.455158 2.34669 0.492151 2.36311 0.540729 2.3851C0.63792 2.4291 0.781275 2.49525 0.960844 2.58172C1.32047 2.75488 1.82273 3.00826 2.38879 3.32695C3.53614 3.97291 4.88286 4.85259 5.85003 5.84044L7.27912 4.44126Z"
        fill="black"/>
    </svg>
  )
}
