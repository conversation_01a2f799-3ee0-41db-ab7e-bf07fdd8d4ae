"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[291],{9588:function(n,t,e){e.d(t,{YM:function(){return animated_icon},P8:function(){return BlurPlay}});var o=e(2729),r=e(5893),i=e(9521);e(1664);var a=e(7421);function _templateObject(){let n=(0,o._)(['\n  position: relative;\n  font-family: "Stelvio", sans-serif;\n  font-size: 17px;\n  font-weight: 400;\n  display: inline-block;\n  width: 100%;\n  color : #f4f4f4;\n  border: 1px solid #080808;\n  background-color: #080808;\n  padding: 12px 18px 6px 18px;\n  text-align: center;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.8;\n  }\n  \n  &.soft {\n    border: 1px solid #080808;\n    background-color: transparent;\n    color: #161616;\n  }\n  \n  @media '," {\n    width: auto;\n    font-size: 17px;\n  }\n"]);return _templateObject=function(){return n},n}function animated_arrow_templateObject(){let n=(0,o._)(["\n  position: relative;\n  width: 100%;\n  height: 100%;\n\n  &:hover {\n    cursor: pointer;\n    svg {\n      transform: rotate(-45deg);\n      width: 325%;\n      height: 325%;\n      left: -112%;\n      bottom: -112%;\n    }\n    .background-dot {\n      transform: translate3d(-66px, 66px, 0);\n    }\n    .color-dot {\n      transform: translate3d(-16px, 16px, 0);\n    }\n  }\n"]);return animated_arrow_templateObject=function(){return n},n}function _templateObject1(){let n=(0,o._)(["\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 200px;\n  width: 200px;\n  overflow: hidden;\n\n  .background-dot {\n    position: absolute;\n    background-color: white;\n    width: 66px;\n    height: 66px;\n    left: -33px;\n    bottom: -33px;\n    z-index: 10;\n    border-radius: 100px;\n    transition: all 450ms cubic-bezier(0.93, 0.03, 0.23, 0.84);\n  }\n\n  .color-dot {\n    position: absolute;\n    background-color: black;\n    width: 16px;\n    height: 16px;\n    left: 0;\n    bottom: 0;\n    z-index: 10;\n    border-radius: 16px;\n    transition: all 450ms cubic-bezier(0.93, 0.03, 0.23, 0.84);\n  }\n\n  svg {\n    position: absolute;\n    left: -33px;\n    bottom: -33px;\n    z-index: 8;\n    transition: all 450ms cubic-bezier(0.93, 0.03, 0.23, 0.84);\n  }\n"]);return _templateObject1=function(){return n},n}function animated_icon_templateObject(){let n=(0,o._)(["\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n\n  &:hover {\n    cursor: pointer;\n    svg {\n      opacity: 1;\n      //transform: translate3d(25px,-25px, 0);\n    }\n    .background-dot {\n      transform: translate3d(42px, -42px, 0);\n    }\n    .color-dot {\n      width: 60px;\n      height: 60px;\n      transform: translate3d(12px, -12px, 0);\n    }\n  }\n"]);return animated_icon_templateObject=function(){return n},n}function animated_icon_templateObject1(){let n=(0,o._)(["\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 200px;\n  width: 200px;\n  overflow: hidden;\n\n  .background-dot {\n    position: absolute;\n    background-color: ",";\n    width: 66px;\n    height: 66px;\n    left: -33px;\n    bottom: -33px;\n    z-index: 10;\n    border-radius: 100px;\n    transition: all 500ms cubic-bezier(0.6, -0.50, 0.24, 0.91);\n  }\n\n  .color-dot {\n    position: absolute;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: ",";\n    width: 16px;\n    height: 16px;\n    left: 0;\n    bottom: 0;\n    z-index: 10;\n    border-radius: 60px;\n    transition: all 500ms cubic-bezier(0.6, -0.50, 0.24, 0.91);\n  }\n\n  svg {\n    opacity: 0;\n    height: 50%;\n    width: 50%;\n    left: 25px;\n    bottom: 0;\n    z-index: 8;\n    transition: all 500ms cubic-bezier(0.6, -0.50, 0.24, 0.91);\n  }\n"]);return animated_icon_templateObject1=function(){return n},n}i.ZP.div.withConfig({componentId:"sc-e3127c86-0"})(_templateObject(),a.U.tablet),i.ZP.div.withConfig({componentId:"sc-2224e371-0"})(animated_arrow_templateObject()),i.ZP.div.withConfig({componentId:"sc-2224e371-1"})(_templateObject1());var animated_icon=n=>{let{type:t,colors:e}=n;return(0,r.jsx)(l,{children:(0,r.jsxs)(s,{colors:e,children:[(0,r.jsx)("div",{className:"background-dot"}),(0,r.jsx)("div",{className:"color-dot",children:(0,r.jsx)(Icon,{type:t})})]})})};let l=i.ZP.div.withConfig({componentId:"sc-c95cb7b7-0"})(animated_icon_templateObject()),s=i.ZP.div.withConfig({componentId:"sc-c95cb7b7-1"})(animated_icon_templateObject1(),n=>{var t;return(null===(t=n.colors)||void 0===t?void 0:t.back)?n.colors.back:"green"},n=>{var t;return(null===(t=n.colors)||void 0===t?void 0:t.front)||"#080808"}),Icon=n=>{let{type:t}=n;switch(t){case"article":return(0,r.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M25 22V8.00003C25.0017 7.6056 24.9252 7.21475 24.775 6.85002C24.6248 6.4853 24.4039 6.15392 24.125 5.87502C23.8461 5.59611 23.5147 5.3752 23.15 5.22502C22.7853 5.07484 22.3944 4.99837 22 5.00003H5",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M13 13H21",stroke:"white",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M13 17H21",stroke:"white",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M2.20312 10.4995L2.70272 11.0588L3.82147 10.0596L3.32187 9.50026L2.20312 10.4995ZM8 7.99986H8.75V7.99946L8 7.99986ZM8 23.9999H7.25V24.0003L8 23.9999ZM13.2375 21.9999V21.2499H11.5621L12.6781 22.4995L13.2375 21.9999ZM27.2375 21.9999L27.7965 21.4999L27.5729 21.2499H27.2375V21.9999ZM25 26.9999L25.0004 26.2499H25V26.9999ZM11 26.2499C10.5858 26.2499 10.25 26.5856 10.25 26.9999C10.25 27.4141 10.5858 27.7499 11 27.7499V26.2499ZM3.32187 9.50026C3.079 9.22833 2.90585 8.90146 2.81733 8.54777L1.36221 8.91197C1.50976 9.50145 1.79834 10.0462 2.20312 10.4995L3.32187 9.50026ZM2.81733 8.54777C2.7288 8.19408 2.72756 7.82418 2.81369 7.4699L1.35616 7.11552C1.21259 7.70598 1.21467 8.32249 1.36221 8.91197L2.81733 8.54777ZM2.81369 7.4699C2.89983 7.11562 3.07077 6.78758 3.3118 6.51402L2.18634 5.52239C1.78462 5.97833 1.49972 6.52506 1.35616 7.11552L2.81369 7.4699ZM3.3118 6.51402C3.55283 6.24046 3.85673 6.02957 4.19734 5.8995L3.66224 4.4982C3.09455 4.71497 2.58805 5.06646 2.18634 5.52239L3.3118 6.51402ZM4.19734 5.8995C4.53795 5.76944 4.90507 5.72409 5.26709 5.76737L5.44515 4.27798C4.84178 4.20585 4.22992 4.28142 3.66224 4.4982L4.19734 5.8995ZM5.26709 5.76737C5.62911 5.81065 5.97519 5.94126 6.27554 6.14795L7.1259 4.91228C6.62531 4.56779 6.04851 4.35011 5.44515 4.27798L5.26709 5.76737ZM6.27554 6.14795C6.57589 6.35465 6.82151 6.63123 6.99126 6.9539L8.31877 6.25553C8.03585 5.71775 7.62648 5.25677 7.1259 4.91228L6.27554 6.14795ZM6.99126 6.9539C7.16101 7.27658 7.2498 7.63567 7.25 8.00026L8.75 7.99946C8.74967 7.3918 8.60168 6.79332 8.31877 6.25553L6.99126 6.9539ZM7.25 7.99986V23.9999H8.75V7.99986H7.25ZM7.25 24.0003C7.25032 24.6079 7.39832 25.2064 7.68123 25.7442L9.00874 25.0458C8.83899 24.7232 8.75019 24.3641 8.75 23.9995L7.25 24.0003ZM7.68123 25.7442C7.96415 26.282 8.37352 26.743 8.8741 27.0874L9.72446 25.8518C9.42411 25.6451 9.17849 25.3685 9.00874 25.0458L7.68123 25.7442ZM8.8741 27.0874C9.37469 27.4319 9.95149 27.6496 10.5549 27.7217L10.7329 26.2324C10.3709 26.1891 10.0248 26.0585 9.72446 25.8518L8.8741 27.0874ZM10.5549 27.7217C11.1582 27.7939 11.7701 27.7183 12.3378 27.5015L11.8027 26.1002C11.462 26.2303 11.0949 26.2756 10.7329 26.2324L10.5549 27.7217ZM12.3378 27.5015C12.9054 27.2848 13.4119 26.9333 13.8137 26.4773L12.6882 25.4857C12.4472 25.7593 12.1433 25.9702 11.8027 26.1002L12.3378 27.5015ZM13.8137 26.4773C14.2154 26.0214 14.5003 25.4747 14.6438 24.8842L13.1863 24.5298C13.1002 24.8841 12.9292 25.2121 12.6882 25.4857L13.8137 26.4773ZM14.6438 24.8842C14.7874 24.2937 14.7853 23.6772 14.6378 23.0878L13.1827 23.452C13.2712 23.8056 13.2724 24.1755 13.1863 24.5298L14.6438 24.8842ZM14.6378 23.0878C14.4902 22.4983 14.2017 21.9535 13.7969 21.5003L12.6781 22.4995C12.921 22.7714 13.0941 23.0983 13.1827 23.452L14.6378 23.0878ZM13.2375 22.7499H27.2375V21.2499H13.2375V22.7499ZM26.6785 22.4999C26.9681 22.8237 27.1578 23.2244 27.2246 23.6537L28.7068 23.4229C28.5954 22.7074 28.2792 22.0395 27.7965 21.4999L26.6785 22.4999ZM27.2246 23.6537C27.2915 24.0829 27.2326 24.5224 27.0552 24.9189L28.4243 25.5316C28.7201 24.8707 28.8182 24.1383 28.7068 23.4229L27.2246 23.6537ZM27.0552 24.9189C26.8777 25.3155 26.5893 25.6521 26.2246 25.8883L27.0401 27.1473C27.6478 26.7537 28.1286 26.1925 28.4243 25.5316L27.0552 24.9189ZM26.2246 25.8883C25.86 26.1245 25.4348 26.2501 25.0004 26.2499L24.9996 27.7499C25.7237 27.7502 26.4324 27.5409 27.0401 27.1473L26.2246 25.8883ZM25 26.2499H11V27.7499H25V26.2499Z",fill:"white"})]});case"podcast":return(0,r.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("path",{d:"M21 8C21 5.23858 18.7614 3 16 3C13.2386 3 11 5.23858 11 8V16C11 18.7614 13.2386 21 16 21C18.7614 21 21 18.7614 21 16V8Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M16 25V29",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M24.9498 17C24.6904 19.1915 23.6359 21.2116 21.9863 22.6774C20.3367 24.1433 18.2066 24.9529 15.9998 24.9529C13.793 24.9529 11.6629 24.1433 10.0133 22.6774C8.36371 21.2116 7.30925 19.1915 7.0498 17",stroke:"white",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"})]});default:return null}};function BlurPlay_templateObject(){let n=(0,o._)(["\n  position: relative;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n"]);return BlurPlay_templateObject=function(){return n},n}function BlurPlay_templateObject1(){let n=(0,o._)(["\n  position: absolute;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n  height: 80px;\n  width: 80px;\n  border-radius: 60px;\n  background-color: rgba(0, 0, 0, 0.8);\n\n  &:hover {\n    cursor: pointer;\n    transform: scale(0.95);\n  }\n\n  transition: transform 350ms ease-in-out;\n\n  @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {\n    background-color: rgba(0, 0, 0, 0.50);\n    -webkit-backdrop-filter: blur(10px);\n    backdrop-filter: blur(10px);\n  }\n  \n  @media "," {\n    height: 104px;\n    width: 104px;\n  }\n"]);return BlurPlay_templateObject1=function(){return n},n}function _templateObject2(){let n=(0,o._)(["\n  margin-left: 16px;\n  width: 30px;\n  height: 30px;\n  background: white;\n  clip-path: polygon(0 0, 0 100%, 80% 50%);\n  @media "," {\n    height: 36px;\n    width: 36px;\n  }\n"]);return _templateObject2=function(){return n},n}function BlurPlay(n){let{clickAction:t}=n;return(0,r.jsx)(c,{onClick:t,children:(0,r.jsx)(u,{children:(0,r.jsx)(d,{})})})}let c=i.ZP.div.withConfig({componentId:"sc-f3ad2613-0"})(BlurPlay_templateObject()),u=i.ZP.div.withConfig({componentId:"sc-f3ad2613-1"})(BlurPlay_templateObject1(),a.U.desktop),d=i.ZP.div.withConfig({componentId:"sc-f3ad2613-2"})(_templateObject2(),a.U.desktop)},1304:function(n,t,e){e.d(t,{Z:function(){return CondImage}});var o=e(5893),r=e(9755),i=e.n(r),a=e(3071);function CondImage(n){let{imageData:t,preserveAspectRatio:e,addClass:r,priority:l=!1,sizes:s=null}=n,c=(0,a.k)(t);return c?e?(0,o.jsx)(i(),{src:c,layout:"intrinsic",height:t.height,width:t.width,alt:t.alternativeText||"",priority:l,className:"cond-image ".concat(r),sizes:s}):(0,o.jsx)(i(),{className:"cond-image ".concat(r),src:c,layout:"fill",objectFit:"cover",alt:t.alternativeText||"",priority:l,sizes:s},c):null}},4440:function(n,t,e){function getDotColor(n){let t={formation:{front:"#262424",back:"#FFFFFF"},emailJourney:{front:"#AA2DD6",back:"#FFFFFF"},article:{front:"#000000",back:"#FFFFFF"},default:{front:"#000000",back:"#FFFFFF"}};return t[n]?t[n]:t.default}function hexToRgb(n){let t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(n);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null}e.d(t,{Q:function(){return getDotColor},o:function(){return hexToRgb}})},785:function(n,t,e){e.d(t,{DG:function(){return menuAsObj},MS:function(){return getModuleWithShortName},O9:function(){return getChannelSlug},fw:function(){return modulesAsObj}});let o={lead:"ComponentModuleLead",webinar:"ComponentModuleWebinar",podcast:"ComponentModulePodcast",journey:"ComponentModuleEmailJourney",formation:"ComponentModuleFormation",seo:"ComponentModuleSeo"};function getModuleWithShortName(n,t){return n.find(function(n){return n.__typename===o[t]})}function modulesAsObj(n){if(!n)return null;let t={};return n.forEach(n=>{switch(n.__typename){case"ComponentModuleLead":t.lead=n;break;case"ComponentModuleWebinar":t.webinar=n;break;case"ComponentModuleEmailJourney":t.journey=n;break;case"ComponentModuleEvent":t.event=n;break;case"ComponentModuleFormation":t.formation=n;break;case"ComponentModuleSeo":t.seo=n;break;case"ComponentModulePodcast":t.podcast=n}}),t}function menuAsObj(n){if(!n.length)return null;let t={groups:[],singles:[]};return n.forEach(n=>{if(n.label.includes("/")){let e=n.label.split("/")[0];for(let o of(t.groups.some(n=>n.name===e)||t.groups.push({name:e,items:[]}),t.groups))if(o.name===e){o.items.push({label:n.label.split("/")[1],value:n.value,type:n.type});break}}else t.singles.push(n)}),t}function getChannelSlug(n,t){let{webinar:e}=getModuleWithShortName(n.modules,t);return(null==e?void 0:e.slug)||null}},4871:function(n,t,e){e.d(t,{mj:function(){return getPostLead},pL:function(){return getPostSpeakers},qt:function(){return getPostRoute}});var o=e(785),r=e(5158),i=e(3071);function getPostRoute(n){if(n.route)return n.route.startsWith("/")?n.route:"/"+n.route;let t=n.type||"undefined";switch(t){case"article":default:return"/article/".concat(n.slug);case"webinaire":return"/webinaires/".concat(n.slug);case"podcast":let{podcast:e}=(0,o.fw)(n.modules);if(!(null==e?void 0:e.podcast))return null;return"/podcasts/".concat(e.podcast.slug,"/").concat(n.slug);case"formation":var r,i;return(null===(i=n.modules)||void 0===i?void 0:null===(r=i.formation)||void 0===r?void 0:r.link)||"/article/".concat(n.slug);case"parcours":return"/parcours-emails/".concat(n.slug)}}function getPostLead(n){let t="";if(n.lead&&""!==n.lead)t=n.lead;else{var e,i;let a=(0,o.fw)(n.modules);t=(null==a?void 0:null===(e=a.lead)||void 0===e?void 0:e.content)?a.lead.content:(null===(i=n.body)||void 0===i?void 0:i.slice(0,255))||"",t=(0,r.Gq)((0,r.Kd)(t))}return t}function getPostSpeakers(n){let t;let e=(0,o.fw)(n.modules);if((null==e?void 0:e.webinar)&&(t=e.webinar.speakers),(null==e?void 0:e.formation)&&(t=e.formation.speakers),!t)return null;let r={pictures:[],names:[]};for(let n of t)r.pictures.push((0,i.k)(n.picture)),r.names.push("".concat(n.firstName.charAt(0),". ").concat(n.lastName));return{pictures:r.pictures,names:function(n){let t=n.length>2?", ":" et ",e="";for(let[o,r]of n.entries())o>0?(e+=t+r,t=" et "):e+=r;return e}(r.names)}}}}]);