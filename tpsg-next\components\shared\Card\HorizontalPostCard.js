import { dateForHumans } from "/utils/date.utils";
import Link from "next/link";
import styled from "styled-components";
import CondImage from "components/shared/condimage";
import { device } from "styles/device";
import {
  PostCardDescription,
  PostCardDetails,
  PostCardTitle,
} from "styles/styled-typography";
import { getPostLead, getPostRoute } from "utils/posts.utils";
import { isLinkExternal } from "utils/string.utils";
import RoundedLabel from "../atoms/rounded-label";
import { useMediaQuery } from "hooks/UseMediaQuery";
import Image from "next/image";
import CondLink from "../CondLink";

export default function HorizontalPostCard({ post, options }) {
  //boolean
  const isMobile = !useMediaQuery({ mediaQuery: device.tablet })
  let details = "";
  let lead = "";

  let route = getPostRoute(post);
  let external = isLinkExternal(route);

  if (options?.showDate) {
    details += !post.date
      ? dateForHumans(post.published_at)
      : dateForHumans(post.date);
  }
  if (options?.showAuthor) {
    if (post?.author) {
      details += details !== "" ? " - " : "";
      details += post.author?.fullName ? post.author.fullName : post.author;
    }
  }
  if (options?.showLead) {
    lead = getPostLead(post);
  }

  return (
    <CardEpisode>
      <div className="post-info">
        <PostCardDetails>{details}</PostCardDetails>
        <CondLink link={route}>
          <PostCardTitle className={"primary-hover"}>
            {post.title}
            { external &&
                <span>
                  <Image
                    src={"/images/icons/external.svg"}
                    alt={"external"}
                    fill
                  />
                </span>
            }
          </PostCardTitle>
          {!isMobile && lead &&
            <PostCardDescription>
              {lead}
            </PostCardDescription>
          }
        </CondLink>
        {/* TOPICS */}
        {options.showTopics && (
          <div className={"card-topics"}>
            {post.topics && post.topics.map((topic, key) =>
              <RoundedLabel key={key} text={topic} />
            )}
          </div>
        )}

      </div>
      <Link href={route} passHref className="post-image-container">
        <div className="post-image">
          <CondImage imageData={post.image} />
        </div>
      </Link>
    </CardEpisode>
  );
}

const CardEpisode = styled.li`
  list-style: none;
  position: relative;
  width: 100%;

  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  &:after {
    content: "";
    display: block;
    width: 100vw;
    margin: 24px 0;
    background-color: #40444444;
    height: 1px;
  }

  .post-info {
    width: 65%;
    padding-right: 10px;
  }
  .post-image-container{
    width: 30%;
  }
  .post-image {
    width: 100%;
    aspect-ratio: 1/1;
    position: relative;
  }
  .card-topics{
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: start;
    justify-content: start;
    flex-wrap: wrap;
    margin-top: 16px;
    height: 32px;
    width: 100%;
    overflow: hidden;
    gap: 8px;
  }
  @media screen and (max-width: 320px) {
    // Little screen only
    .post-info {
      width: 100%;
    }
  }
  @media ${device.tablet} {
    &:after {
      content: "";
      display: block;
      width: 100%;
      margin: 37px 0 30px 0;

      background-color: #40444444;
      height: 1px;
    }
    .post-info {
      width: 75%;
      padding-right: 10px;
    }
    .post-image-container{
      width: 20%;
    }
  }
`;
