(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[821],{6035:function(e,n,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/blog/[blog]",function(){return t(6543)}])},9487:function(e,n,t){"use strict";t.d(n,{Z:function(){return BlogMenu}});var i=t(2729),o=t(5893),r=t(785),l=t(1664),a=t.n(l),s=t(9521),d=t(7294),c=t(7421),p=t(1304);function _templateObject(){let e=(0,i._)(["\n  position: fixed;\n  bottom: -70px;\n  left: -70px;\n  height: calc(70vh + 140px);\n  width: calc(100vw + 140px);\n  padding: 48px 70px;\n  background-color: rgba(",");\n  backdrop-filter: blur(",");\n  transform: translate3d(",");\n  border-top-right-radius: 70px;\n  transition: all 450ms cubic-bezier(0.58, 0, 0.29, 0.91);\n  z-index: 1800;\n\n  @media "," {\n    grid-row: 1/6;\n    background-color: transparent;\n    position: sticky;\n    display: block;\n    backdrop-filter: inherit;\n    border-top-right-radius: 0;\n    transform: none;\n    top: 46px;\n    left: 0;\n    height: 100vh;\n    width: 240px;\n    padding: 40px 0 0 0;\n    z-index: 50;\n  }\n"]);return _templateObject=function(){return e},e}function _templateObject1(){let e=(0,i._)(['\n  display: flex;\n  flex-direction: row;\n  margin-bottom: 42px;\n  .menu-blogger-picture {\n    position: relative;\n    width: 55px;\n    height: 55px;\n    border-radius: 50px;\n    overflow: hidden;\n  }\n  .menu-blogger-name {\n    margin: 4px 0 0 16px;\n    font-family: "Stelvio", sans-serif;\n    font-size: 24px;\n    line-height: 105%;\n    color: #161616;\n    font-weight: 600;\n  }\n  .menu-blogger-label {\n    margin: 4px 0 0 16px;\n    font-family: "Novela", serif;\n    font-style: italic;\n    font-size: 20px;\n    color: #888888;\n    font-weight: 400;\n  }\n  \n  @media ',' {\n    flex-direction: column;\n    width: 50%;\n    .menu-blogger-name {\n      margin: 16px 0 0 0;\n      font-family: "Stelvio", sans-serif;\n      font-size: 32px;\n      line-height: 105%;\n      color: #161616;\n      font-weight: 600;\n    }\n    .menu-blogger-label {\n      margin: 4px 0 0 0;\n    }\n  }\n']);return _templateObject1=function(){return e},e}function _templateObject2(){let e=(0,i._)(["\n  \n  padding-left: var(--mobile-gap);\n  padding-right: var(--mobile-gap);\n  \n  @media "," {\n    padding-left: var(--tablet-gap);\n    padding-right: var(tablet-gap);\n  }\n  @media "," {\n    transform: none;\n    padding-left: 0;\n    padding-right: 0;\n    top: 0;\n    left: 0;\n  }\n"]);return _templateObject2=function(){return e},e}function _templateObject3(){let e=(0,i._)(['\n  ul {\n    display: table;\n    padding: 0;\n  }\n  li {\n    font-weight: 400;\n    position: relative;\n    font-family: "Stelvio", sans-serif;\n    font-size: 20px;\n    margin-top: 8px;\n    list-style: none;\n  }\n  //transform: translate3d(',");\n  transition: all 800ms cubic-bezier(0.58, 0, 0.29, 0.91);\n\n  @media ",' {\n    a {\n      position: relative;\n      height: 32px;\n      display: table-row;\n      line-height: 24px;\n      border-radius: 32px;\n      z-index: 900;\n      color: black;\n\n      &:hover {\n        color: white;\n\n        &:after {\n          content: "";\n          background-color: black;\n          position: absolute;\n          height: 100%;\n          width: calc(100% + 24px);\n          left: -12px;\n          top: -7px;\n          border-radius: 32px;\n          z-index: -1;\n        }\n      }\n    }\n  }\n']);return _templateObject3=function(){return e},e}function _templateObject4(){let e=(0,i._)(["\n  display: flex;\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  align-items: center;\n  justify-content: center;\n  height: 70px;\n  width: 70px;\n  color: black;\n  border-radius: 70px;\n  p {\n    padding-top: 12px;\n    margin: 16px 16px 0 0;\n    font-size: 32px;\n    transform-origin: center;\n    transform: rotate(",");\n    transition: all 450ms cubic-bezier(0.58, -0.42, 0.29, 0.91);\n  }\n  z-index: 2000;\n  @media "," {\n    display: none;\n  }\n"]);return _templateObject4=function(){return e},e}function _templateObject5(){let e=(0,i._)(["\n  font-weight: 500;\n  letter-spacing: 0.08em;\n  text-transform: uppercase;\n  color: #888888;\n"]);return _templateObject5=function(){return e},e}function RenderGroup(e){let{group:n,blogPath:t}=e;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(h,{children:n.name}),(0,o.jsx)("ul",{children:n.items.map((e,n)=>(0,o.jsx)(RenderLink,{link:e,blogPath:t},n))})]})}function RenderLink(e){let{link:n,blogPath:t}=e;switch(n.type){case"external":return(0,o.jsx)("li",{children:(0,o.jsx)("a",{href:n.value,target:"_blank",rel:"noreferrer noopener",children:n.label})});case"internal":return(0,o.jsx)("li",{children:(0,o.jsx)(a(),{href:n.value,children:n.label})});case"filter":return(0,o.jsx)("li",{children:(0,o.jsx)(a(),{href:{pathname:"/blog/".concat(t,"/filtres"),query:n.value},children:n.label})});default:return(0,o.jsx)(o.Fragment,{})}}function BlogMenu(e){let{data:n}=e,t=(0,r.DG)(n.menu),i=n.slug,[l,s]=(0,d.useState)(!1);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(u,{isOpen:l,className:"blog-menu",children:(0,o.jsxs)(m,{isOpen:l,children:[(0,o.jsxs)(f,{children:[(0,o.jsx)("div",{className:"menu-blogger-picture",children:(0,o.jsx)(p.Z,{imageData:n.blogger.picture})}),(0,o.jsxs)("div",{children:[(0,o.jsx)(a(),{href:"/blog/".concat(i),children:(0,o.jsx)("p",{className:"menu-blogger-name primary-hover",children:n.blogger.fullName})}),(0,o.jsx)("p",{className:"menu-blogger-label",children:"Blog"})]})]}),(0,o.jsxs)(b,{children:[(null==t?void 0:t.groups)&&t.groups.map((e,n)=>(0,o.jsx)(RenderGroup,{group:e,blogPath:i},n)),(null==t?void 0:t.singles.length)>0&&t.singles.map((e,n)=>(0,o.jsx)(RenderLink,{link:e,blogPath:i},n))]})]})}),(0,o.jsx)(g,{isOpen:l,onClick:()=>s(!l),children:(0,o.jsx)("p",{children:"→"})})]})}let u=s.ZP.div.withConfig({componentId:"sc-f4ce5f7f-0"})(_templateObject(),e=>e.isOpen?"236, 236, 236, 0.8":"236, 236, 236, 0.5",e=>e.isOpen?"25px":"15px",e=>e.isOpen?"0,0,0":"-100vw,70vh,0",c.U.desktop),f=s.ZP.div.withConfig({componentId:"sc-f4ce5f7f-1"})(_templateObject1(),c.U.desktop),m=s.ZP.div.withConfig({componentId:"sc-f4ce5f7f-2"})(_templateObject2(),c.U.tablet,c.U.desktop),b=s.ZP.div.withConfig({componentId:"sc-f4ce5f7f-3"})(_templateObject3(),e=>e.isOpen?"0,0,0":"0, -100px, 0",c.U.desktop),g=s.ZP.div.withConfig({componentId:"sc-f4ce5f7f-4"})(_templateObject4(),e=>e.isOpen?"-225deg":"-45deg",c.U.desktop),h=s.ZP.label.withConfig({componentId:"sc-f4ce5f7f-5"})(_templateObject5())},1026:function(e,n,t){"use strict";t.d(n,{Z:function(){return ButtonLink}});var i=t(2729),o=t(5893),r=t(1664),l=t.n(r),a=t(9521),s=t(5158),d=t(4355),c=t.n(d);function _templateObject(){let e=(0,i._)(["\n  margin: 0 16px 0 0;\n  padding: 12px 18px 6px 18px;\n  font-family: Stelvio,sans-serif;\n  font-size: 18px;\n  white-space: nowrap;\n  cursor: pointer;\n  \n  &:hover {\n    opacity: 0.72;\n  }\n"]);return _templateObject=function(){return e},e}function ButtonLink(e){let{text:n,url:t,type:i}=e;return(0,s.tm)(t)?(0,o.jsx)(p,{href:t,className:"button-link ".concat(c()[i]),target:"_blank",rel:"noopener noreferrer",children:n}):(0,o.jsx)(l(),{href:t,children:(0,o.jsx)(p,{className:"button-link ".concat(c()[i]),children:n})})}let p=a.ZP.a.withConfig({componentId:"sc-e62ceebf-0"})(_templateObject())},1809:function(e,n,t){"use strict";var i=t(5152),o=t.n(i);let r=o()(()=>t.e(523).then(t.bind(t,4523)),{loadableGenerated:{webpack:()=>[4523]},ssr:!1});n.Z=r},4012:function(e,n,t){"use strict";t.d(n,{Z:function(){return AuthorBox}});var i=t(2729),o=t(5893),r=t(9521),l=t(5582),a=t(9663),s=t(1304),d=t(7421);function _templateObject(){let e=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  border-top: 1px solid rgba(0, 0, 0, 0.4);\n  padding-top: 24px;\n  \n  @media "," {\n    flex-direction: row;\n    padding: 32px 0 0 0;\n  }\n"]);return _templateObject=function(){return e},e}function _templateObject1(){let e=(0,i._)(["\n  position: relative;\n  flex-shrink: 0;\n  margin: 0 32px 12px 0;\n  height: 60px;\n  width: 60px;\n  border-radius: 60px;\n  overflow: hidden;\n  background-color: white;\n"]);return _templateObject1=function(){return e},e}function _templateObject2(){let e=(0,i._)(["\n  .abox-about__name {\n    font-size: 26px;\n    font-weight: 600;\n  }\n  p {\n    font-size: 22px;\n    line-height: 28px;\n    font-weight: 400;\n    margin: 12px 0 0 0;\n  }\n"]);return _templateObject2=function(){return e},e}function AuthorBox(e){var n,t;let{author:i,blog:r}=e;if(!i)return null;i.about=(null===(n=i.about)||void 0===n?void 0:n.replace(/\\/g,""))||"";let d=r?"/blog/".concat(null===(t=r.blogger)||void 0===t?void 0:t.slug):"/recherche?author=".concat(i.fullName),f="".concat(i.firstName||""," ").concat(i.lastName||"").trim();return(0,o.jsxs)(c,{className:"author-box",children:[(0,o.jsx)(p,{children:(0,o.jsx)("a",{href:d,children:(0,o.jsx)(s.Z,{imageData:i.picture})})}),(0,o.jsxs)(u,{children:[(0,o.jsx)("p",{className:"abox-about__name",children:(0,o.jsx)("a",{href:d,children:f})}),(0,o.jsx)(l.U,{rehypePlugins:[a.Z],children:i.about})]})]})}t(4871),t(1664);let c=r.ZP.div.withConfig({componentId:"sc-9bb0274f-0"})(_templateObject(),d.U.tablet),p=r.ZP.div.withConfig({componentId:"sc-9bb0274f-1"})(_templateObject1()),u=r.ZP.div.withConfig({componentId:"sc-9bb0274f-2"})(_templateObject2())},9413:function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),function(e,n){for(var t in n)Object.defineProperty(e,t,{enumerable:!0,get:n[t]})}(n,{noSSR:function(){return noSSR},default:function(){return dynamic}});let i=t(8754),o=(t(7294),i._(t(132)));function convertModule(e){return{default:(null==e?void 0:e.default)||e}}function noSSR(e,n){return delete n.webpack,delete n.modules,e(n)}function dynamic(e,n){let t=o.default,i={loading:e=>{let{error:n,isLoading:t,pastDelay:i}=e;return null}};e instanceof Promise?i.loader=()=>e:"function"==typeof e?i.loader=e:"object"==typeof e&&(i={...i,...e}),i={...i,...n};let r=i.loader;return(i.loadableGenerated&&(i={...i,...i.loadableGenerated},delete i.loadableGenerated),"boolean"!=typeof i.ssr||i.ssr)?t({...i,loader:()=>null!=r?r().then(convertModule):Promise.resolve(convertModule(()=>null))}):(delete i.webpack,delete i.modules,noSSR(t,i))}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),e.exports=n.default)},7240:function(e,n,t){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"LoadableContext",{enumerable:!0,get:function(){return r}});let i=t(8754),o=i._(t(7294)),r=o.default.createContext(null)},132:function(e,n,t){"use strict";/**
@copyright (c) 2017-present James Kyle <<EMAIL>>
 MIT License
 Permission is hereby granted, free of charge, to any person obtaining
a copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:
 The above copyright notice and this permission notice shall be
included in all copies or substantial portions of the Software.
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE
*/Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"default",{enumerable:!0,get:function(){return d}});let i=t(8754),o=i._(t(7294)),r=t(7240),l=[],a=[],s=!1;function load(e){let n=e(),t={loading:!0,loaded:null,error:null};return t.promise=n.then(e=>(t.loading=!1,t.loaded=e,e)).catch(e=>{throw t.loading=!1,t.error=e,e}),t}let LoadableSubscription=class LoadableSubscription{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:n}=this;e.loading&&("number"==typeof n.delay&&(0===n.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},n.delay)),"number"==typeof n.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},n.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,n){this._loadFn=e,this._opts=n,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}};function Loadable(e){return function(e,n){let t=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},n),i=null;function init(){if(!i){let n=new LoadableSubscription(e,t);i={getCurrentValue:n.getCurrentValue.bind(n),subscribe:n.subscribe.bind(n),retry:n.retry.bind(n),promise:n.promise.bind(n)}}return i.promise()}if(!s){let e=t.webpack?t.webpack():t.modules;e&&a.push(n=>{for(let t of e)if(n.includes(t))return init()})}function LoadableComponent(e,n){!function(){init();let e=o.default.useContext(r.LoadableContext);e&&Array.isArray(t.modules)&&t.modules.forEach(n=>{e(n)})}();let l=o.default.useSyncExternalStore(i.subscribe,i.getCurrentValue,i.getCurrentValue);return o.default.useImperativeHandle(n,()=>({retry:i.retry}),[]),o.default.useMemo(()=>{var n;return l.loading||l.error?o.default.createElement(t.loading,{isLoading:l.loading,pastDelay:l.pastDelay,timedOut:l.timedOut,error:l.error,retry:i.retry}):l.loaded?o.default.createElement((n=l.loaded)&&n.default?n.default:n,e):null},[e,l])}return LoadableComponent.preload=()=>init(),LoadableComponent.displayName="LoadableComponent",o.default.forwardRef(LoadableComponent)}(load,e)}function flushInitializers(e,n){let t=[];for(;e.length;){let i=e.pop();t.push(i(n))}return Promise.all(t).then(()=>{if(e.length)return flushInitializers(e,n)})}Loadable.preloadAll=()=>new Promise((e,n)=>{flushInitializers(l).then(e,n)}),Loadable.preloadReady=e=>(void 0===e&&(e=[]),new Promise(n=>{let res=()=>(s=!0,n());flushInitializers(a,e).then(res,res)})),window.__NEXT_PRELOADREADY=Loadable.preloadReady;let d=Loadable},6543:function(e,n,t){"use strict";t.r(n),t.d(n,{__N_SSG:function(){return v},default:function(){return Blog}});var i=t(2729),o=t(5893),r=t(9521),l=t(9487),a=t(7421),s=t(5158),d=t(7915);t(1026);var c=t(1809),p=t(4012),u=t(211),f=t(3071),m=t(5675),b=t.n(m);function _templateObject(){let e=(0,i._)(["\n  \n  margin: 96px var(--border-space) 32px var(--border-space);\n  \n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  \n  .text-content {\n    margin-left: 16px;\n  }\n  \n  .blog-header-name {\n    font-family: Stelvio, sans-serif;\n    font-size: 24px;\n    line-height: 25px;\n    font-weight: 500;\n    margin: 0;\n  }\n  \n  .blog-header-label {\n    font-family: Lora, serif;\n    font-style: italic;\n    margin: 0;\n    font-size: 17px;\n    line-height: 20px;\n  }\n  \n  @media "," {\n    display: none;\n  }\n"]);return _templateObject=function(){return e},e}function _templateObject1(){let e=(0,i._)(["\n  position: relative;\n  height: 54px;\n  border-radius: 54px;\n  background-color: #0070f3;\n  overflow: hidden;\n  aspect-ratio: 1/1;\n"]);return _templateObject1=function(){return e},e}function BlogHeader(e){let{blog:n}=e,t=(0,f.k)(n.blogger.picture);return(0,o.jsxs)(h,{children:[(0,o.jsx)(x,{children:(0,o.jsx)(b(),{fill:!0,sizes:"100px",style:g.picture,src:t,alt:""})}),(0,o.jsxs)("div",{className:"text-content",children:[(0,o.jsx)("p",{className:"blog-header-name",children:n.blogger.fullName}),(0,o.jsx)("p",{className:"blog-header-label",children:"Blog"})]})]})}let g={picture:{objectFit:"cover"}},h=r.ZP.div.withConfig({componentId:"sc-c602c9c9-0"})(_templateObject(),a.U.desktop),x=r.ZP.div.withConfig({componentId:"sc-c602c9c9-1"})(_templateObject1());var _=t(4256),j=t(2962);function _blog_templateObject(){let e=(0,i._)(["\n  padding-left: var(--border-space);\n  padding-right: var(--border-space);\n  display: flex;\n  flex-direction: column-reverse;\n  margin-bottom: 128px;\n  margin-top: 128px;\n\n  .author-box {\n    margin-bottom: 40px;\n  }\n\n  @media "," {\n    flex-direction: row;\n    .author-box {\n      padding-left: 40px;\n    }\n  }\n"]);return _blog_templateObject=function(){return e},e}function _blog_templateObject1(){let e=(0,i._)(["\n  position: relative;\n  height: 64px;\n  @media "," {\n    height: 164px;\n  }\n"]);return _blog_templateObject1=function(){return e},e}function _templateObject2(){let e=(0,i._)(["\n  border-top: 1px solid rgba(0, 0, 0, 0.4);\n  padding-right: 40px;\n  padding-top: 32px;\n  @media "," {\n    min-width: 40%;\n    border-right: 1px solid rgba(0, 0, 0, 0.4);\n  }\n"]);return _templateObject2=function(){return e},e}function _templateObject3(){let e=(0,i._)(["\n  position: relative;\n  height: 40px;\n  background: ",";\n  z-index: 100;\n"]);return _templateObject3=function(){return e},e}function _templateObject4(){let e=(0,i._)(["\n  position: relative;\n  height: 64px;\n"]);return _templateObject4=function(){return e},e}function _templateObject5(){let e=(0,i._)(["\n  position: relative;\n  background-color: var(--soft-white);\n  @media "," {\n    .blog-menu {\n      margin-left: var(--border-space);\n    }\n  }\n  @media "," {\n    .blog-menu {\n      margin-left: 114px;\n    }\n  }\n"]);return _templateObject5=function(){return e},e}function _templateObject6(){let e=(0,i._)(["\n  position: relative;\n"]);return _templateObject6=function(){return e},e}function _templateObject7(){let e=(0,i._)(["\n  position: relative;\n  top: 48px;\n  left: 0;\n  width: 100%;\n  @media "," {\n    margin-top: -100vh;\n  }\n"]);return _templateObject7=function(){return e},e}function _templateObject8(){let e=(0,i._)(["\n  position: relative;\n  display: flex;\n  margin-top: 48px;\n  margin-left: var(--border-space);\n  .button-link {\n    width: calc(100% - var(--border-space));\n    text-align: center;\n  }\n  @media "," {\n    .button-link {\n      width: auto;\n      text-align: left;\n    }\n  }\n  @media "," {\n    margin-left: calc(25% + 24px);\n  }\n"]);return _templateObject8=function(){return e},e}function _templateObject9(){let e=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n  margin-right: var(--border-space);\n  margin-left: var(--border-space);\n\n  .main-element {\n    display: flex;\n    width: 100%;\n  }\n\n  .sub-row {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 18px;\n\n    @media "," {\n      display: flex;\n      flex-direction: row;\n      gap: 18px;\n    }\n  }\n\n  @media "," {\n    margin-left: calc(var(--border-space) - 24px);\n  }\n  @media "," {\n    margin-left: 25%;\n  }\n"]);return _templateObject9=function(){return e},e}function _templateObject10(){let e=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n  margin-right: var(--border-space);\n\n  .fa-card {\n    position: relative;\n    max-width: 100%;\n    margin-left: 24px;\n    margin-bottom: 40px;\n  }\n  .sv-card {\n    margin-left: 24px;\n    margin-bottom: 40px;\n    max-width: calc(50% - 24px);\n  }\n  .svf-card {\n    margin-left: 24px;\n    margin-bottom: 40px;\n    //max-width: calc(50% - 24px);\n    max-width: 100%;\n  }\n  @media "," {\n    margin-left: calc(var(--border-space) - 24px);\n    .fa-card {\n      width: calc(66.3% - 24px);\n    }\n    .sv-card {\n      width: calc(33.3% - 24px);\n    }\n    .svf-card {\n      width: calc(33.3% - 24px);\n    }\n  }\n  @media "," {\n    margin-left: 25%;\n  }\n"]);return _templateObject10=function(){return e},e}function _templateObject11(){let e=(0,i._)(["\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  background: transparent;\n  margin-top: 126px;\n  z-index: 300;\n\n  .blog-featured-image {\n    position: relative;\n    width: 100%;\n    aspect-ratio: 16/10;\n  }\n  .blog-featured-text {\n    background-color: ",";\n    position: relative;\n    padding: var(--border-space);\n    width: 100%;\n    min-height: 400px;\n  }\n  .blog-featured-title {\n    font-size: 46px;\n    margin-top: 24px;\n    margin-bottom: 0;\n  }\n  .blog-featured-desc {\n    font-size: 18px;\n  }\n  .blog-featured-type {\n    text-transform: uppercase;\n    letter-spacing: 0.5px;\n    font-size: 18px;\n  }\n  .blog-featured-buttons {\n    margin-top: 40px;\n    margin-bottom: 40px;\n  }\n\n  @media "," {\n    flex-direction: row;\n    .blog-featured-image {\n      width: 50%;\n      height: 100%;\n      aspect-ratio: inherit;\n    }\n    .blog-featured-text {\n      position: relative;\n      padding: 52px;\n      width: 50%;\n      height: 100%;\n    }\n  }\n  @media "," {\n    padding-left: calc(25% + 24px);\n    .blog-featured-image {\n      width: calc(50% - 48px);\n      min-height: 400px;\n      max-height: 100%;\n      height: inherit;\n    }\n    .blog-featured-text {\n      width: calc(50% + 48px);\n      height: 100%;\n    }\n  }\n"]);return _templateObject11=function(){return e},e}let w={showAuthor:!1,dotColors:{back:"#ffffff"}};var v=!0;function Blog(e){var n,t;let{blog:i,posts:r}=e;if(!i||r.length<10)return(0,o.jsx)(o.Fragment,{});let a=null===(n=i.featured)||void 0===n?void 0:n.filter(e=>!e.inColumn),f=a.length>0?a[0]:null,m=a.length>1?a[1]:null,b=null===(t=i.featured)||void 0===t?void 0:t.filter(e=>e.inColumn),g=b.length>0?b[0]:null,h=b.length>1?b[1]:null;return(0,o.jsxs)(Z,{children:[(0,o.jsx)(j.PB,{title:"TPSG - Blog de ".concat(i.blogger.fullName),description:(0,s.Kd)(i.blogger.about).slice(0,160)}),(0,o.jsx)(BlogHeader,{blog:i}),(0,o.jsxs)(L,{children:[(0,o.jsx)(l.Z,{data:i}),(0,o.jsxs)(N,{children:[(0,o.jsxs)(z,{children:[(0,o.jsx)("div",{className:"main-element",children:(0,o.jsx)(d.F3,{post:r[0],options:w})}),(0,o.jsxs)("div",{className:"sub-row",children:[(0,o.jsx)(d.Zo,{post:r[1],options:w}),(0,o.jsx)(d.Zo,{post:r[2],options:w}),(0,o.jsx)(d.Zo,{post:r[3],options:w}),(0,o.jsx)(d.Zo,{post:r[4],options:w})]})]}),f&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(C,{direction:"top"}),(0,o.jsx)(u.g4,{content:f}),(0,o.jsx)(C,{direction:"bot"})]}),(0,o.jsx)(P,{}),(0,o.jsxs)(B,{children:[(0,o.jsx)(d.Zo,{post:r[5],options:w}),(0,o.jsx)(d.Zo,{post:r[6],options:w}),g&&(0,o.jsx)(d.h3,{item:g}),(0,o.jsx)(d.Zo,{post:r[8],options:w}),(0,o.jsx)(d.Zo,{post:r[9],options:w}),h&&(0,o.jsx)(d.h3,{item:h})]})]}),(0,o.jsx)(I,{children:(0,o.jsx)(_.Ty,{theme:"dark",text:"Tous mes articles",link:"/blog/".concat(i.slug,"/filtres")})})]}),m&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(k,{}),(0,o.jsx)(u.g4,{content:m})]}),(0,o.jsxs)(O,{children:[(0,o.jsx)(y,{children:(0,o.jsx)(c.Z,{formString:i.newsletter,title:"Ma newsletter"})}),(0,o.jsx)(p.Z,{author:i.blogger,blog:i})]})]})}let O=r.ZP.div.withConfig({componentId:"sc-17fa9b26-0"})(_blog_templateObject(),a.U.desktop),k=r.ZP.div.withConfig({componentId:"sc-17fa9b26-1"})(_blog_templateObject1(),a.U.desktop),y=r.ZP.div.withConfig({componentId:"sc-17fa9b26-2"})(_templateObject2(),a.U.desktop),C=r.ZP.div.withConfig({componentId:"sc-17fa9b26-3"})(_templateObject3(),e=>"bot"===e.direction?"linear-gradient(#FAF7F3, rgba(250, 247, 243, 0))":"linear-gradient(rgba(250, 247, 243, 0), #FAF7F3)"),P=r.ZP.div.withConfig({componentId:"sc-17fa9b26-4"})(_templateObject4()),Z=r.ZP.div.withConfig({componentId:"sc-17fa9b26-5"})(_templateObject5(),a.U.desktop,a.U.desktopXL),L=r.ZP.div.withConfig({componentId:"sc-17fa9b26-6"})(_templateObject6()),N=r.ZP.div.withConfig({componentId:"sc-17fa9b26-7"})(_templateObject7(),a.U.desktop),I=r.ZP.div.withConfig({componentId:"sc-17fa9b26-8"})(_templateObject8(),a.U.tablet,a.U.desktop),z=r.ZP.div.withConfig({componentId:"sc-17fa9b26-9"})(_templateObject9(),a.U.desktop,a.U.tablet,a.U.desktop),B=r.ZP.div.withConfig({componentId:"sc-17fa9b26-10"})(_templateObject10(),a.U.tablet,a.U.desktop);r.ZP.div.withConfig({componentId:"sc-17fa9b26-11"})(_templateObject11(),e=>e.backgroundColor,a.U.tablet,a.U.desktop)},4355:function(e){e.exports={"soft-white":"ButtonLink_soft-white__ep2DH","glow-white":"ButtonLink_glow-white__46aUW","soft-dark":"ButtonLink_soft-dark__NLNNQ","glow-dark":"ButtonLink_glow-dark__p2EZ7","rounded-glow-white":"ButtonLink_rounded-glow-white__XCQ_D ButtonLink_glow-white__46aUW","rounded-soft-white":"ButtonLink_rounded-soft-white__W4BQi ButtonLink_soft-white__ep2DH","rounded-glow-dark":"ButtonLink_rounded-glow-dark__D1hGV ButtonLink_glow-dark__p2EZ7","rounded-soft-dark":"ButtonLink_rounded-soft-dark__6gKMz ButtonLink_soft-dark__NLNNQ","orange-register":"ButtonLink_orange-register__aIov2"}},5152:function(e,n,t){e.exports=t(9413)}},function(e){e.O(0,[755,764,962,291,915,211,774,888,179],function(){return e(e.s=6035)}),_N_E=e.O()}]);