
import styled from "styled-components";
import client from "../api/apollo-client";
import { gql } from "@apollo/client";
import { RenderMarkdown } from "/components/shared";
import Image from "next/image";
import { withRealSrc } from "../utils/image-utils";
import { device } from "../styles/device";
import { NextSeo } from "next-seo";

export default function Page({ content }) {

  if(!content) return;

  return (
    <PageWrapper>
      <NextSeo
        title={content.metas?.metaTitle || null}
        description={content.metas?.metaDescription || null}
      />
      <header>
        { content.cover &&
            <Cover>
              <Image
                src={withRealSrc(content.cover)}
                alt={content.cover.alternativeText || ""}
                style={{ objectFit: "cover" }}
                fill
              />
              <Shadow/>
            </Cover>
        }
      </header>
      <section>
        <h1 className={"page-title"}>{content.title}</h1>
        {content.blocks && content.blocks.map((block, key) => renderBlock(block))}
      </section>
    </PageWrapper>
  )
}


const renderBlock = (block) => {
  switch (block.__typename) {
  case "ComponentBlockText":
    return renderTextBlock(block);
  default:
    return null;
  }
}

const renderTextBlock = (block) => {
  return(
    <BlockText>
      <RenderMarkdown content={block.content}/>
    </BlockText>
  )
}


const PageWrapper = styled.div`
  section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    margin-left: var(--border-space);
    margin-right: var(--border-space);
  }
  
  .page-title {
    grid-column: 1/3;
    margin-top: 80px;
    font-family: Stelvio, sans-serif;
    font-size: 52px;
    margin-right: 0;
  }
  
  @media ${device.desktop} {
    h1 {
      grid-column: 1;
      margin-right: 120px;
    }
  }
`

const Cover = styled.div`
  margin-top: -80px;
  position: relative;
  width: 100%;
  height: 50vh;
`;

const Shadow = styled.div`
  position: absolute;
  top: 0;
  width: 100%;
  height: 120px;
  background: linear-gradient(180deg, rgba(0,0,0,1) -40%, rgba(0,0,0,0) 100%);
`;

const BlockText = styled.div`
  display: flex;
  grid-column: 1/3;
  margin-bottom: 126px;
  
  @media ${device.desktop} {
    grid-column: 2;
    margin-top: 40px;
    margin-left: -80px;
    align-items: center;
    justify-content: center;
  }
`;

export async function getStaticProps({ params }) {
  const page = await client
    .query({
      query: QUERY_PAGE,
      variables: { slug: params.page },
    })
    .then((response) => {
      return response.data.pages[0];
    });

  if (!page) {
    return {
      notFound: true,
    };
  }

  return {
    props: {
      content: page
    },
    revalidate: 20,
  };
}

export async function getStaticPaths() {
  const { data } = await client.query({ query: QUERY_PAGE_SLUGS });
  return {
    paths: data.pages.map((page) => ({
      params: { page: page.slug }
    })),
    fallback: true
  }
}

const QUERY_PAGE_SLUGS = gql`
    query PageSlugs {pages{slug}}
`;

const QUERY_PAGE = gql`
    query Page($slug: String!) {
        pages(where: { slug: $slug }) {
            title
            cover {
                url
                provider
                alternativeText
            }
            blocks {
                ... on ComponentBlockText {
                    __typename
                    content
                }
            }
            metas {
                metaTitle
                metaDescription
            }
        }
    }
`;



