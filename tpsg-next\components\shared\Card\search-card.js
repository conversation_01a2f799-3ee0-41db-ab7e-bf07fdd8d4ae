import styled from "styled-components";
import { device } from "/styles/device";
import Link from "next/link";
import RoundedLabel from "components/shared/atoms/rounded-label";
import CondImage from "components/shared/condimage";

const SearchCard = ({ post }) => {
  return (
    <Link href={post.route}>
      <Wrapper>
        <div className="card-text">
          {/* TITLE + TYPE & AUTHOR */}
          <div className={"card-top"}>
            <div className="card-text">
              <p className={"card-title primary-hover"}>
                {post.title}
              </p>
              <div className={"card-details"}>
                <p className="card-type">
                  {post.type}<span className={"dot-separator"}>・</span>
                </p>
                <p className="card-author"> {post.author}</p>
              </div>
            </div>
          </div>
          {/* COVER */}
          <div className={"card-post-cover"}>
            <CondImage imageData={post.image}/>
          </div>
          {/* TOPICS */}
          <div className={"card-topics"}>
            {post.topics && post.topics.map((topic, key) =>
              <RoundedLabel key={key} text={topic}/>
            )}
          </div>
        </div>
      </Wrapper>
    </Link>
  )
}

export default SearchCard

const Wrapper = styled.div`
  position: relative;
  border-bottom: 1px solid rgba(38, 38, 38, 0.15);
  padding-top: 30px;
  padding-bottom: 30px;
  max-width: 500px;

  .card-top {
    position: relative;
    min-height: 80px;
    max-width: calc(100% - 79px);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  .card-post-cover {
    position: absolute;
    overflow: hidden;
    right: 0;
    top: 30px;
    min-width: 75px;
    max-width: 75px;
    min-height: 75px;
    max-height: 75px;
    background-color: white;
    object-fit: cover;
  }

  .card-title {
    font-size: 22px;
    font-family: Stelvio, Arial, sans-serif;
    line-height: 25px;
    font-weight: 600;
    margin: 0 12px 0 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* number of lines to show */
    line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .card-label {
    margin-right: 8px;
  }

  .card-details {
    display: flex;
    flex-direction: row;
    font-size: 14px;

    p {
      text-transform: capitalize;
      font-family: Arial, sans-serif;
      line-height: 25px;
      margin: 0;
      color: #757575;
    }
  }

  .dot-separator {
    margin: 0 2px 0 2px;
  }

  .card-topics {
    width: 100%;
    margin-top: 4px;
    padding-top: 6px;
    height: 28px;
    white-space: nowrap;
    overflow-x: scroll;

    &::-webkit-scrollbar {
      display: none;
    }

    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }

  @media ${device.tablet} {
    max-width: 100%;
    width: 100%;
    padding-top: 36px;
    padding-bottom: 36px;

    .card-top {
      margin-left: 16.6%;
      width: 50%;
    }

    .card-title {
      font-size: 26px;
      line-height: 30px;
      margin-left: 24px;
    }

    .card-details {
      margin-left: 24px;
      font-size: 16px;
      font-family: Stelvio, Arial, sans-serif;
      margin-bottom: 4px;
    }

    .card-post-cover {
      left: 0;
      top: 0;
      right: unset;
      max-width: unset;
      max-height: unset;
      width: 16.6%;
      height: 100%;
      min-width: 16.6%;
    }

    .card-topics {
      margin-left: calc(16.6% + 24px);
    }

  }
`;
