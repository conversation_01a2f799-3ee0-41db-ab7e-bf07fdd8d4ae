(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[745],{3487:function(n,t,e){(window.__NEXT_P=window.__NEXT_P||[]).push(["/categories/vocation/[vocation]",function(){return e(8348)}])},7326:function(n,t,e){"use strict";e.d(t,{Z:function(){return CornerStoneCard}});var i=e(2729),o=e(5893);e(1664);var r=e(9521),a=e(7421),l=e(6368);e(4871);var c=e(1304),s=e(1261);function _templateObject(){let n=(0,i._)(["\n  position: absolute;\n  width: 100%;\n  filter: blur(12px);\n  aspect-ratio: ",";\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  width: 100%;\n  display: flex;\n  justify-content: center;\n  margin-bottom: 50px;\n  box-shadow: 0 7px 14px 3px rgba(0, 0, 0, 0.29);\n  a {\n    width: 100%;\n    display: flex;\n    justify-content: center;\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  width: 70%;\n  padding-left: 20px;\n  @media "," {\n    width: 100%;\n    padding-left: 0;\n  }\n  @media "," {\n    padding-left: 0;\n    width: 100%;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  position: relative;\n  width: 30%;\n  margin-bottom: 24px;\n  aspect-ratio: ",";\n\n\n  @media "," {\n    width: 100%;\n  }\n  @media "," {\n    width: 80%;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  color: white;\n  background-color: #0F0F0F;\n  padding: 20px 20px;\n\n  width: 100%;\n  display: flex;\n  flex-wrap: wrap;\n\n  .corner-stone-title {\n    padding-top: 8px;\n    font-weight: 400;\n    margin-top: 0;\n    margin-bottom: 8px;\n    font-size: 1.25rem;\n    font-size: clamp(1.25rem, 1.178635147190009rem + 0.35682426404995543vw, 1.5rem);\n    display: -webkit-box;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  @media "," {\n    .corner-stone-title {\n      -webkit-line-clamp:  2!important;\n    }\n  }\n  @media "," {\n    display: flex;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n"]);return _templateObject4=function(){return n},n}function CornerStoneCard(n){var t,e,i;let{post:r,options:a}=n,{showAuthor:x,showBlur:h,aspectRatio:g=1}=a,j=(null==r?void 0:null===(t=r.cta)||void 0===t?void 0:t.url)||(null==r?void 0:r.link)||((null==r?void 0:null===(e=r.route)||void 0===e?void 0:e.startsWith("/"))?r.route:"/"+(null==r?void 0:r.route));return j?(0,o.jsx)(p,{children:(0,o.jsx)(s.Z,{link:j,children:(0,o.jsxs)(f,{children:[(0,o.jsxs)(u,{aspectRatio:g,children:[h&&(0,o.jsx)(d,{aspectRatio:g,children:(0,o.jsx)(c.Z,{imageData:r.image})}),(0,o.jsx)(c.Z,{imageData:r.image})]}),(0,o.jsxs)(m,{children:[(0,o.jsx)("h2",{className:"corner-stone-title",children:null==r?void 0:r.title}),x&&r.author&&(0,o.jsx)(l.My,{children:(null===(i=r.author)||void 0===i?void 0:i.fullName)?r.author.fullName:r.author})]})]})})}):(0,o.jsx)(o.Fragment,{})}let d=r.ZP.div.withConfig({componentId:"sc-9da25472-0"})(_templateObject(),n=>n.aspectRatio),p=r.ZP.div.withConfig({componentId:"sc-9da25472-1"})(_templateObject1()),m=r.ZP.div.withConfig({componentId:"sc-9da25472-2"})(_templateObject2(),a.U.mini,a.U.desktop),u=r.ZP.div.withConfig({componentId:"sc-9da25472-3"})(_templateObject3(),n=>n.aspectRatio,a.U.desktop,a.U.mini),f=r.ZP.div.withConfig({componentId:"sc-9da25472-4"})(_templateObject4(),a.U.desktop,a.U.mini)},2140:function(n,t,e){"use strict";e.d(t,{Z:function(){return ListLink}});var i=e(2729),o=e(5893),r=e(9521),a=e(1664),l=e.n(a),c=e(7421);function _templateObject(){let n=(0,i._)(['\n  font-family: Switzer, sans-serif;\n  font-weight: 500;\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  margin-top: -1px;\n  border-top: 1px solid #CCCAC7;\n  border-bottom: 1px solid #CCCAC7;\n  padding: 10px 0 10px 4px;\n  color: #161616;\n  font-size: 18px;\n  \n  .ll-text {\n    margin: 0;\n  }\n\n  &:after {\n    content: "→";\n    position: absolute;\n    right: 0;\n    line-height: 100%;\n    padding-bottom: 4px;\n  }\n  \n  &:hover {\n    cursor: pointer;\n    color: var(--brand-color);\n  }\n\n  @media '," {\n    font-size: 22px;\n    padding: 12px 0 12px 4px;\n  }\n"]);return _templateObject=function(){return n},n}function ListLink(n){let{route:t,image:e,text:i}=n;return(0,o.jsx)(l(),{href:t,children:(0,o.jsx)(s,{children:(0,o.jsx)("div",{className:"text-container",children:(0,o.jsx)("p",{className:"ll-text",children:i})})})})}let s=r.ZP.a.withConfig({componentId:"sc-1bebecf-0"})(_templateObject(),c.U.tablet)},2929:function(n,t,e){"use strict";e.d(t,{Z:function(){return CategoriesHeader}});var i=e(2729),o=e(5893),r=e(9521),a=e(7421),l=e(1664),c=e.n(l);function _templateObject(){let n=(0,i._)(['\n  position: relative;\n  padding: var(--border-space);\n  padding-bottom: 80px;\n  padding-top: 60px;\n\n  .type {\n    font-size: clamp(1rem, 0.928635147190009rem + 0.35682426404995543vw, 1.25rem);\n    font-family: Stelvio, sans-serif;\n    margin: 0 0 24px 0;\n    color: #FFFFFF;\n    font-weight: 500;\n    letter-spacing: 1px;\n    text-transform: uppercase;\n  }\n  \n  .content{\n    position: relative;\n  }\n  \n  .header-color {\n    position: absolute;\n    overflow: hidden;\n    background-color: var(--brand-color) !important;\n    width: 100%;\n    height: calc(100% + 500px);\n    left: 0;\n    bottom: 0;\n  }\n  .content-container {\n    display: flex;\n    flex-wrap: wrap;\n  }\n  .content-container {\n    margin: 32px 0 0 0;\n    .description {\n      margin: 0;\n      font-size: 18px;\n      font-family: "Lora", serif;\n      margin: 0;\n      font-weight: 500;\n      letter-spacing: 0;\n      line-height: 24px;\n      color: white;\n    }\n    .title-container {\n      overflow-wrap: anywhere;\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: flex-start;\n      flex-direction: column;\n      align-items: flex-start;\n      .title {\n        font-size: clamp(2rem, 1.286rem + 1.905vw, 3rem);\n        line-height: clamp(2rem, 1.286rem + 1.905vw, 3rem);\n        font-family: Stelvio, sans-serif;\n        margin: 0 0 16px 0;\n        font-weight: 500;\n        letter-spacing: 0;\n        color: white; \n      }\n    }\n  }\n\n  @media '," {\n    .content-container {\n      .description-container {\n        width: 40%;\n      }\n      .title-container {\n        width: 60%;\n        padding-right: 50px;\n        .title {\n          margin: 0;\n        }\n      }\n    }\n  }\n"]);return _templateObject=function(){return n},n}function CategoriesHeader(n){let{category:t,type:e}=n;return(0,o.jsxs)(s,{children:[(0,o.jsx)("div",{className:"header-color"}),(0,o.jsxs)("div",{className:"content",children:[(0,o.jsx)(c(),{href:"/categories",className:"type",children:e+" /"}),(0,o.jsxs)("div",{className:"content-container",children:[(0,o.jsx)("div",{className:"title-container",children:(0,o.jsx)("h1",{className:"title",children:null==t?void 0:t.name})}),(0,o.jsx)("div",{className:"description-container",children:(0,o.jsx)("p",{className:"description",children:null==t?void 0:t.description})})]})]})]})}let s=r.ZP.header.withConfig({componentId:"sc-11b6a297-0"})(_templateObject(),a.U.tablet)},8348:function(n,t,e){"use strict";e.r(t),e.d(t,{__N_SSG:function(){return x},default:function(){return PageVocation}});var i=e(2729),o=e(5893),r=e(9521),a=e(7421),l=e(785),c=e(3500),s=e(4724),d=e(2140);function _templateObject(){let n=(0,i._)(["\n  .ministries {\n    margin-left: 38px;\n  }\n  @media "," {\n    .ministries {\n      margin-left: 50px;\n    }\n  }\n  @media "," {\n    flex-direction: row;\n    .ministries {\n      margin-left: 56px;\n    }\n  }\n"]);return _templateObject=function(){return n},n}function SectionMinistries(n){let{ministries:t}=n;if((null==t?void 0:t.length)!==0)return(0,o.jsxs)(p,{children:[(0,o.jsx)("div",{children:(0,o.jsx)("p",{className:"label-type",children:"Sous cat\xe9gories associ\xe9es"})}),(0,o.jsx)("div",{children:null==t?void 0:t.map((n,t)=>(0,o.jsx)("div",{children:(0,o.jsx)(d.Z,{image:n.cover,text:n.name,route:"/categories/".concat(n.type,"/").concat(n.slug)})},t))})]})}let p=r.ZP.div.withConfig({componentId:"sc-67b1d6e9-0"})(_templateObject(),a.U.tablet,a.U.desktop);var m=e(211),u=e(7326),f=e(2929);function _vocation_templateObject(){let n=(0,i._)(["\n  margin-top: 48px;\n  .posts-container {\n    display: block;\n  }\n  @media "," {\n    margin-top: 96px;\n    .posts-container {\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: space-between;\n    }\n  }\n"]);return _vocation_templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  width: 100%;\n  margin-bottom: 64px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n\n  .list-container {\n    padding: 0;\n    width: 100%;\n  }\n  .post-card-li {\n    list-style: none;\n    padding-right: 0;\n  }\n\n  @media "," {\n    margin-bottom: 164px;\n    width: 66.7%;\n    .post-card-li {\n      padding-right: 142px;\n    }\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  position: relative;\n  width: 100%;\n  .cornerstone-container {\n    /* position: sticky;\n    top: 60px; */\n  }\n  .podcast-platform {\n    display: flex;\n    flex-wrap: wrap;\n    margin-top: 64px;\n    width: 100%;\n    gap: 32px;\n  }\n  @media "," {\n    .podcast-platform {\n      margin-top: 16px;\n      gap: 16px;\n    }\n  }\n  @media "," {\n    width: 33.3%;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  .label-type {\n    font-size: 16px;\n    font-family: Stelvio, sans-serif;\n    margin: 0 0 16px 0;\n\n    font-weight: 500;\n    letter-spacing: 4%;\n    line-height: 32px;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  //margin-top: 0;\n  .section-ministries {\n    margin-top: 64px;\n    padding: 0 var(--border-space);\n    margin-bottom: 80px;\n  }\n"]);return _templateObject4=function(){return n},n}var x=!0;function PageVocation(n){var t,e,i;let{vocation:r,fallback:a}=n,d=(null==a?void 0:null===(t=a.posts)||void 0===t?void 0:t.totalHits)||0,p=null==a?void 0:null===(e=a.posts)||void 0===e?void 0:e.hits,x=(null==a?void 0:a.cornerStonesFeatured)||[];return r?(0,o.jsxs)(v,{children:[(0,o.jsx)(f.Z,{category:r,type:"vocation"}),(0,o.jsxs)(b,{children:[(null==r?void 0:null===(i=r.children)||void 0===i?void 0:i.length)>0&&(0,o.jsx)("section",{className:"section-ministries",children:(0,o.jsx)(SectionMinistries,{ministries:r.children})}),(0,o.jsx)("section",{children:x[0]&&(0,o.jsx)(m.g4,{content:x[0]})}),(0,o.jsxs)(h,{className:"site-padding",children:[(0,o.jsx)("p",{className:"label-type",children:(null==p?void 0:p.length)>0?"Derni\xe8res ressources":""}),(0,o.jsxs)("div",{className:"posts-container ",children:[(0,o.jsxs)(g,{children:[(0,o.jsx)("ul",{className:"list-container",children:null==p?void 0:p.map((n,t)=>{let e=(0,l.fw)(n.modules);return n.lead||(n.lead=null==e?void 0:e.lead),(0,o.jsx)("li",{className:"post-card-li",children:(0,o.jsx)(s.Z,{post:n,options:{showLead:!0,showDate:!0,showAuthor:!0}})},"post-".concat(t))})}),(0,o.jsx)(c.Z,{nbHits:d,baseUrl:"/categories/vocation/".concat(r.slug,"/ressources?page="),currentPage:1,options:{postPerPage:15}})]}),(0,o.jsx)(j,{children:(0,o.jsxs)("div",{className:"cornerstone-container",children:[x[1]&&(0,o.jsx)(u.Z,{post:x[1],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}}),x[2]&&(0,o.jsx)(u.Z,{post:x[2],options:{showAuthor:!0,showBlur:!0,aspectRatio:16/9}})]})})]})]}),(0,o.jsx)("section",{children:x[3]&&(0,o.jsx)(m.g4,{content:x[3]})})]})]}):null}let h=r.ZP.section.withConfig({componentId:"sc-885a80ed-0"})(_vocation_templateObject(),a.U.desktop),g=r.ZP.article.withConfig({componentId:"sc-885a80ed-1"})(_templateObject1(),a.U.desktop),j=r.ZP.div.withConfig({componentId:"sc-885a80ed-2"})(_templateObject2(),a.U.tablet,a.U.desktop),v=r.ZP.div.withConfig({componentId:"sc-885a80ed-3"})(_templateObject3()),b=r.ZP.main.withConfig({componentId:"sc-885a80ed-4"})(_templateObject4())}},function(n){n.O(0,[755,764,291,915,211,749,774,888,179],function(){return n(n.s=3487)}),_N_E=n.O()}]);