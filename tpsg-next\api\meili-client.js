import { MeiliSearch } from "meilisearch";

const MeiliClient = new MeiliSearch({
  host: process.env.NEXT_PUBLIC_MEILISEARCH_HOST,
  apiKey: process.env.NEXT_PUBLIC_MEILISEARCH_KEY
})

const index = MeiliClient.index("post")

const instantSearch = async (q) => {
  return await index.search(q, {
    attributesToCrop: ["body"],
    cropLength: 200,
    limit: 5,
  })
}

const search = async (q = "", params) => {
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "cs", "lead"],
    attributesToHighlight: ["title"],
    ...params,
  })
}

const searchHighlight = async (q = "", params) => {
  return await index.search(q, {
    attributesToRetrieve: ["title", "author", "type", "image", "route", "slug", "date", "body", "lead", "topics", "cs"],
    attributesToHighlight: ["title", "body", "lead"],
    attributesToCrop: ["body", "lead"],
    cropLength: 100,
    ...params,
  })
}

const urls = async () => {
  return await index.getDocuments({
    attributesToRetrieve: ["route", "date"],
    limit: 9999
  })
}

export const MeiliApi = {
  instantSearch,
  search,
  searchHighlight,
  urls
}
