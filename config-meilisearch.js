/**
 * Script pour configurer Meilisearch v0.27.0 afin qu'il soit compatible avec le code existant
 *
 * Pour exécuter ce script :
 * 1. Assurez-vous que Meilisearch est en cours d'exécution
 * 2. Exécutez la commande : node config-meilisearch.js
 *
 * Note importante : Meilisearch v0.27.0 ne supporte pas les paramètres 'page' et 'hitsPerPage'.
 * Il faut utiliser 'offset' et 'limit' à la place.
 */

const fetch = require('node-fetch');

const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function configMeilisearch() {
  try {
    console.log('Configuration de Meilisearch pour compatibilité avec le code existant...');

    // 0. Vérifier si l'index existe, sinon le créer
    console.log('Vérification de l\'existence de l\'index...');
    try {
      const indexResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}`);
      if (indexResponse.status === 404) {
        console.log('L\'index n\'existe pas, création...');
        const createIndexResponse = await fetch(`${MEILI_URL}/indexes`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            uid: INDEX_NAME,
            primaryKey: 'id'
          })
        });
        console.log(`Index créé: ${createIndexResponse.status}`);
      } else {
        console.log('L\'index existe déjà.');
      }
    } catch (error) {
      console.error('Erreur lors de la vérification/création de l\'index:', error);
    }

    // 1. Configurer les attributs filtrables
    console.log('Configuration des attributs filtrables...');
    const filterableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'title', 'topics', 'tags', 'author', 'type', 'blog', 'cs'
      ])
    });
    console.log(`Attributs filtrables configurés: ${filterableResponse.status}`);

    // 2. Configurer les attributs triables
    console.log('Configuration des attributs triables...');
    const sortableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['date'])
    });
    console.log(`Attributs triables configurés: ${sortableResponse.status}`);

    // 3. Configurer les attributs de recherche
    console.log('Configuration des attributs de recherche...');
    const searchableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['title', 'body', 'lead'])
    });
    console.log(`Attributs de recherche configurés: ${searchableResponse.status}`);

    // 4. Configurer les règles de classement
    console.log('Configuration des règles de classement...');
    const rankingResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/ranking-rules`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'words', 'typo', 'proximity', 'attribute', 'sort', 'exactness'
      ])
    });
    console.log(`Règles de classement configurées: ${rankingResponse.status}`);

    // 5. Configurer la pagination (maxTotalHits)
    console.log('Configuration de la pagination...');
    try {
      const paginationResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/pagination`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          maxTotalHits: 4000
        })
      });
      console.log(`Pagination configurée: ${paginationResponse.status}`);
    } catch (error) {
      console.log('Note: La configuration de pagination peut ne pas être supportée dans cette version de Meilisearch');
    }

    console.log('\nConfiguration terminée !');
    console.log('\nIMPORTANT:');
    console.log('Meilisearch v0.27.0 ne supporte pas les paramètres "page" et "hitsPerPage".');
    console.log('Vous devez utiliser "offset" et "limit" à la place.');
    console.log('Pour adapter votre code sans le modifier, vous pouvez:');
    console.log('1. Modifier meili-client.js pour supprimer les paramètres incompatibles avant d\'envoyer la requête');
    console.log('2. Ou créer un proxy qui transforme les requêtes avant de les envoyer à Meilisearch');

  } catch (error) {
    console.error('Erreur lors de la configuration :', error);
  }
}

configMeilisearch();
