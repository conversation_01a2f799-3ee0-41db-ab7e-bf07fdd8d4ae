import { Fragment, useEffect, useState } from "react";
import styled from "styled-components";
import BigInput from "components/recherche/big-input";
import FilterBar from "components/recherche/filter-bar";
import FilterList from "components/recherche/filter-list";
import { useCoreData } from "context/CoreDataContext";


function getInitialState(initQuery){
  return {
    activeList: initQuery?.filter?.type ? initQuery.filter.type : "type", // liste en cours d'affichage
    activeFilter: initQuery?.filter?.value, // filtre choisit par l'utilisateur
    filterType: initQuery?.filter?.type, // type du filtre sélectionné
    queryTerms: initQuery?.terms ? initQuery.terms : "", // termes contenus dans l'input
    listOpen: false, // état d'ouverture du panneau contenant les listes
    filterOpen: !!initQuery?.filter?.type, // état d'ouverture de la barre contenant le filtre
    listVisible: false // état de visibilité de la liste à afficher
  }
}

const SearchTool = ({ setQuery, initQuery, autoFocus = false }) => {
  let coreData = useCoreData()

  coreData.types = [
    { name: "Podcast", displayName: "Podcasts" },
    { name: "Formation", displayName: "Formations" },
    { name: "Article", displayName: "Articles" },
    { name: "Webinaire", displayName: "Webinaires" },
    { name: "Parcours", displayName: "Parcours" },
  ]

  const [state, setState] = useState(getInitialState(initQuery))

  /*
  * Retourne si une liste doit être affichée à l'écran ou pas.
  * */
  const displayList = (listName) => {
    return state.activeList === listName && state.listVisible
  }

  /*
  * Envois les paramètres de la recherche quand le filtre
  * ou les termes de la recherche ont été mis à jour.
  * */
  useEffect(() => {
    if (state !== getInitialState(initQuery)) {
      setQuery({
        terms: state.queryTerms,
        filter: { value: state.activeFilter, type: state.filterType },
        page: 0
      });
    }
  }, [state.activeFilter, state.queryTerms])


  useEffect(() => {
    setTimeout(() => {
      setState(prevState => {
        return {
          ...prevState,
          listVisible: state.listOpen,
        }
      })
    }, state.listOpen ? 350 : 0);
  }, [state.listOpen]);

  return (
    <Fragment>
      <BigInput
        changeFilter={changeFilter}
        listState={state}
        changeQueryTerms={changeQueryTerms}
        autoFocus={autoFocus}
      />
      <FilterBar
        setListState={setState}
        listState={state}
        changeList={changeList}
        changeFilter={changeFilter}/>
      <ListsWrapper open={state.listOpen}>
        <StickyBackground open={state.listOpen}/>
        <div className={state.listVisible ? "lists visible" : "lists"}>
          {displayList("author") &&
            <FilterList
              separator={true}
              changeFilter={changeFilter}
              haveDisplayName={true}
              data={coreData.authors.map(entry => (
                {
                  displayName: entry.lastName + " " + entry.firstName,
                  fullName: entry.fullName
                }
              ) )}
              fieldName={"fullName"}/>
          }
          {displayList("topics") &&
            <FilterList
              separator={true}
              changeFilter={changeFilter}
              data={coreData.topics}
              fieldName={"name"}/>
          }
          {displayList("type") &&
            <FilterList
              separator={false}
              changeFilter={changeFilter}
              data={coreData.types}
              haveDisplayName={true}
              fieldName={"name"}/>
          }
        </div>
      </ListsWrapper>
    </Fragment>
  )

  function closeList() {
    window.scrollTo({ top: 0 });
    setState(prevState => {
      return { ...prevState, listOpen: false }
    })
  }

  function changeList(list) {
    if (state.activeList === list && state.listOpen) {
      closeList();
      return;
    }
    setState(prevState => {
      return { ...prevState, activeList: list, listOpen: true }
    })
  }

  function changeFilter(value, type = state.activeList, curentTerms = false) {
    if (!value) {
      setState(prevState => {
        return {
          ...prevState,
          queryTerms: curentTerms ? curentTerms : prevState.queryTerms,
          filterOpen: false
        }
      })
    }
    setTimeout(() => {
      setState(prevState => {
        return {
          ...prevState,
          activeFilter: value,
          filterType: type,
          filterOpen: value !== false
        }
      })
    }, value ? 0 : 250);
    closeList();
  }

  function changeQueryTerms(value) {
    setState(prevState => {
      return { ...prevState, queryTerms: value }
    })
  }
}

export default SearchTool;

const ListsWrapper = styled.div`
  position: relative;
  width: 100%;

  div {
    width: 100%;
    //background-color: #EF4523;
    //background-color: black;
  }
`;

const StickyBackground = styled.div`
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  height: ${props => props.open ? "100" : "0"}vh;
  transform-origin: top;
  background-color: #161616;
  //background-color: #E9EDFB;
  //background-color: #F45D3C;
  transform: ${props => props.open ? "scaleY(1)" : "scaleY(0)"};
  transition: all 650ms cubic-bezier(1, 0.72, 0.15, 1.01);
  z-index: 10;
`;
