@echo off
echo Demarrage de Strapi avec Node.js 14...
echo.

REM Verifier si NVM est installe
where nvm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo NVM n'est pas installe ou n'est pas dans le PATH.
    echo Tentative d'utilisation directe de Node.js 14...
    goto :USE_NODE14_DIRECT
)

REM Utiliser NVM pour activer Node.js 14
echo Activation de Node.js 14 via NVM...
call nvm use 14
if %ERRORLEVEL% NEQ 0 (
    echo Erreur lors de l'activation de Node.js 14 via NVM.
    goto :USE_NODE14_DIRECT
)

goto :START_STRAPI

:USE_NODE14_DIRECT
echo Tentative d'utilisation directe de Node.js 14...
if exist "C:\Program Files\nvm\v14.21.3\node.exe" (
    set "PATH=C:\Program Files\nvm\v14.21.3;%PATH%"
) else if exist "C:\nvm\v14.21.3\node.exe" (
    set "PATH=C:\nvm\v14.21.3;%PATH%"
) else (
    echo Impossible de trouver Node.js 14.
    echo Veuillez installer Node.js 14 ou configurer NVM correctement.
    pause
    exit /b 1
)

:START_STRAPI
echo.
echo Version de Node.js utilisee:
node -v
echo.
echo Version de npm utilisee:
npm -v
echo.
echo Demarrage du serveur Strapi...
echo.

REM Demarrer Strapi
npm run develop

pause
