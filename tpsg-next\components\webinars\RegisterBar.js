import styled from "styled-components";
import { device } from "styles/device";

export default function RegisterBar({ children }) {
  return (
    <Wrapper>
      <div className={"register-content site-padding"}>
        {children}
      </div>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  border-top: 1px #ffffff solid;
  background-color: rgba(241, 241, 241, 0.9);

  @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
    background-color: rgba(221, 221, 221, 0.45);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
  }

  z-index: 1000;

  .register-content {
    position: relative;
  }

  /* Ensure the SubHeader inside RegisterBar has proper alignment */
  .subheader {
    border-top: none;
    border-bottom: none;
    padding-top: 12px;
    padding-bottom: 12px;
  }
`;
