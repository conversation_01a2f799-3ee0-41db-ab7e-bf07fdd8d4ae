import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import styled from "styled-components";
import { modulesAsObj } from "utils/components.utils";
import BannerFormation from "components/formation/BannerFormation";
import { PageTitle } from "styles/styled-typography";
import GridCardSection from "components/shared/section/GridCardSection";
import GridCard from "components/shared/Card/GridCard";
import SvgDuotone from "components/shared/DuotoneFilter";

export default function Formations({ pageContent }) {

  if (!pageContent) return null;

  const allPostSections = pageContent.sections.filter(
    (b) => ( b.__typename = "ComponentSectionPostSet" )
  );

  const lastFormation = allPostSections.filter(section => section.name === "last-formation")[0]?.posts[0];
  const postSections = allPostSections.filter(section => section.name !== "last-formation");

  return (
    <PageWrapper>
      <SvgDuotone hexLight={"#FFFFFF"} hexDark={"#262424"}/>
      <PageTitle className={"site-padding"}>Formations</PageTitle>

      {lastFormation && (
        <BannerFormation
          lastPost={lastFormation}
        />
      )}

      <div className="site-padding">
        {postSections.map((postSection, index) => (
          <GridCardSection key={index} nameSection={postSection.name}>
            {postSection.posts.map((post, index) => {
              const modules = modulesAsObj(post.modules);
              let details = "";
              modules?.formation?.speakers.filter(s => s?.fullName).map((s, index) => {
                if (index !== modules?.formation?.speakers.length - 1) {
                  details += `${s.fullName} / `;
                } else {
                  details += `${s.fullName}`;
                }
              });
              return (
                <GridCard key={index} post={{
                  title: post.title,
                  image: post.image,
                  link: modules?.formation?.link,
                  youtubeEmbed: modules?.formation?.youtubeEmbed,
                  details: details,
                  lead: modules?.lead?.content,
                  postType: "formation"
                }}/>
              );
            })}
          </GridCardSection>
        ))}
      </div>
    </PageWrapper>
  );
}

const PageWrapper = styled.div``;

export async function getStaticProps() {

  const { formation } = await client
    .query({ query: QUERY_FORMATIONS })
    .then((response) => {
      return response.data;
    });

  return {
    props: {
      pageContent: formation,
    },
    revalidate: 10,
  };
}

const QUERY_FORMATIONS = gql`
    query GetFormation {
        formation {
            sections {
                ... on ComponentSectionPostSet {
                    __typename
                    name
                    posts {
                        title
                        image {
                            url
                            alternativeText
                            caption
                            provider
                        }
                        modules {
                            ... on ComponentModuleFormation {
                                __typename
                                speakers {
                                    fullName
                                }
                                link
                                youtubeEmbed
                            }
                            ... on ComponentModuleLead {
                                __typename
                                content
                            }
                        }
                    }
                }
            }
        }
    }
`;
