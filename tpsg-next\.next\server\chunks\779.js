"use strict";exports.id=779,exports.ids=[779],exports.modules={779:(e,a,t)=>{t.r(a),t.d(a,{default:()=>MyDocument});var i=t(997),r=t(6859),o=t.n(r),l=t(7518),d=t(7467);let p=l.createGlobalStyle`

  // TODO: Renommer les variables correctement
  :root {
    
    /**
     * COLORS
     */
    --soft-white: #FAF7F3;
    --c-cream: #FFEBD8;
    --c-soft-cream: #F9F1E6;
    --c-cream-A80: rgba(255, 235, 216, 0.8);
    --c-cream-A40: rgba(255, 235, 216, 0.4);
    --c-cream-A20: rgba(255, 235, 216, 0.2);
    --soft-dark: #161616;
    //--blue-dark: #081921;
    --blue-dark: #081D21;
    --c--blue-medium: #1C373C;
    --c-dark-green: #081D21;
    --brand-color: #EE5131;
    --c-brand-light: #F3673B;
    --c-brand-lighter: #FA7051;

    --mobile-gap: 24px;
    --tablet-gap: 60px;
    --desktop-gap: 80px;
    --desktopxl-gap: 96px;
    --max-page-width: 1380px;

    --border-space: 24px; // valeur des marges de la page;
    --spacing-l: 48px;

    @media ${d.U.tablet} {
      --border-space: 60px;
      --spacing-l: 52px;
    }

    @media ${d.U.desktop} {
      --border-space: 64px;
      --spacing-l: 96px;
    }

    @media ${d.U.desktopXL} {
      --border-space: calc((100vw + 32px - var(--max-page-width)) / 2);
      --spacing-l: 96px;
    }

    // Spacing
    --fluid-space-m: clamp(3rem, 0.22rem + 5.97vw, 5rem);
  }

  body {
    width: 100%;
    overflow-x: hidden;
    //overflow-y: scroll;
    margin: auto;
    padding-top: 80px;
    background-color: var(--soft-white);
    font-family: Stelvio, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  }

  html {
    width: 100%;
    background-color: white;
    //background-color: var(--soft-white);
    overscroll-behavior-y: none;
  }

  .site-padding {
    padding-left: var(--mobile-gap);
    padding-right: var(--mobile-gap);
    @media ${d.U.tablet} {
      padding-left: var(--tablet-gap);
      padding-right: var(--tablet-gap);
    }
    @media ${d.U.desktop} {
      padding-left: var(--desktop-gap);
      padding-right: var(--desktop-gap);
    }
    @media ${d.U.desktopXL} {
      max-width: 1380px;
      margin: auto;
      padding-left: 32px;
      padding-right: 32px;
    }
  }

  .site-padding-left {
    padding-left: var(--mobile-gap) !important;
    @media ${d.U.tablet} {
      padding-left: var(--tablet-gap) !important;
    }
    @media ${d.U.desktop} {
      padding-left: var(--desktop-gap) !important;
    }
    @media ${d.U.desktopXL} {
      padding-left: calc(32px + (100vw - 1380px) / 2) !important;
    }
  }

  .site-padding-right {
    padding-right: var(--mobile-gap) !important;
    @media ${d.U.tablet} {
      padding-right: var(--tablet-gap) !important;
    }
    @media ${d.U.desktop} {
      padding-right: var(--desktop-gap) !important;
    }
    @media ${d.U.desktopXL} {
      padding-right: calc(32px + (100vw - 1380px) / 2) !important;
    }
  }

  .site-margin {
    margin-left: var(--mobile-gap);
    margin-right: var(--mobile-gap);
    @media ${d.U.tablet} {
      margin-left: var(--mobile-gap);
      margin-right: var(--mobile-gap);
    }
    @media ${d.U.desktop} {
      margin-left: var(--desktop-gap);
      margin-right: var(--desktop-gap);
    }
    @media ${d.U.desktopXL} {
      max-width: var(--max-page-width);
      margin: auto;
    }
  }

  .fw-background {
    &:before {
      content: '';
      position: absolute;
      width: 105vw;
      height: 100%;
      top: 0;
      left: -24px;
      @media ${d.U.tablet} {
        left: -60px;
      }
      @media ${d.U.desktop} {
        left: -80px;
      }
      @media ${d.U.desktopXL} {
        left: -96px;
      }
      background-color: #080808;
      z-index: -1;
    }
  }


  .mobile-hide {
    display: none;
    @media ${d.U.tablet} {
      display: inherit;
    }
  }

  .mobile-hide_flex {
    display: none !important;
    @media ${d.U.tablet} {
      display: flex !important;
    }
  }

  .tablet-hide_flex {
    display: none !important;
    @media ${d.U.desktop} {
      display: flex !important;
    }
  }

  .svg-filter {
    height: 0;
    left: -9999em;
    margin: 0;
    padding: 0;
    position: absolute;
    width: 0;
  }
/* 
  .with-duotone {
    filter: url('#duotone-filter');
  } */

  .no-select {
    * {
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }
  }

  .no-scroll {
    overflow: hidden;
  }
  
  .primary-hover:hover {
		  color: var(--brand-color) !important;
  }
`;let MyDocument=class MyDocument extends o(){static async getInitialProps(e){let a=new l.ServerStyleSheet,t=e.renderPage;try{e.renderPage=()=>t({enhanceApp:e=>t=>a.collectStyles((0,i.jsxs)("html",{lang:"fr",children:[i.jsx(p,{}),i.jsx(e,{...t})]}))});let r=await o().getInitialProps(e);return{...r,styles:(0,i.jsxs)(i.Fragment,{children:[r.styles,a.getStyleElement()]})}}finally{a.seal()}}render(){return(0,i.jsxs)(r.Html,{lang:"fr",children:[(0,i.jsxs)(r.Head,{children:[i.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),i.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),i.jsx("link",{href:"https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap",rel:"stylesheet"})]}),(0,i.jsxs)("body",{children:[i.jsx(r.Main,{}),i.jsx(r.NextScript,{})]})]})}}}};