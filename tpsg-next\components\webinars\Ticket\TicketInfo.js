import styled from "styled-components";
import { device } from "styles/device";


const TicketInfo = styled.div`
  &:before {
    display: block;
    margin: 0;
    font-size: 12px;
    font-family: "Stelvio Grotesk", sans-serif;
    color: #707070;
    text-transform: uppercase;
    content: '${props => props.label ? props.label : ""}';
  }

  p {
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 16px;
    color: #D3D3D3;
    font-family: "Stelvio Grotesk", sans-serif;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.04em;
  }

  @media ${device.tablet} {
    margin-top: 24px;
    &:before {
      font-size: 13px;
    }

    p {
      margin-top: 4px;
      font-size: 18px;
    }
  }
  @media ${device.desktop} {
    margin-top: 24px;
    &:before {
      font-size: 14px;
    }
    p {
      font-size: 22px;
    }
  }
`

export default TicketInfo;


