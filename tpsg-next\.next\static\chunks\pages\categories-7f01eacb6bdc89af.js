(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[161],{5190:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/categories",function(){return t(8361)}])},2140:function(n,e,t){"use strict";t.d(e,{Z:function(){return ListLink}});var i=t(2729),o=t(5893),r=t(9521),a=t(1664),s=t.n(a),c=t(7421);function _templateObject(){let n=(0,i._)(['\n  font-family: Switzer, sans-serif;\n  font-weight: 500;\n  position: relative;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  margin-top: -1px;\n  border-top: 1px solid #CCCAC7;\n  border-bottom: 1px solid #CCCAC7;\n  padding: 10px 0 10px 4px;\n  color: #161616;\n  font-size: 18px;\n  \n  .ll-text {\n    margin: 0;\n  }\n\n  &:after {\n    content: "→";\n    position: absolute;\n    right: 0;\n    line-height: 100%;\n    padding-bottom: 4px;\n  }\n  \n  &:hover {\n    cursor: pointer;\n    color: var(--brand-color);\n  }\n\n  @media '," {\n    font-size: 22px;\n    padding: 12px 0 12px 4px;\n  }\n"]);return _templateObject=function(){return n},n}function ListLink(n){let{route:e,image:t,text:i}=n;return(0,o.jsx)(s(),{href:e,children:(0,o.jsx)(l,{children:(0,o.jsx)("div",{className:"text-container",children:(0,o.jsx)("p",{className:"ll-text",children:i})})})})}let l=r.ZP.a.withConfig({componentId:"sc-1bebecf-0"})(_templateObject(),c.U.tablet)},8361:function(n,e,t){"use strict";t.r(e),t.d(e,{__N_SSG:function(){return j},default:function(){return Categories}});var i=t(2729),o=t(5893),r=t(9521),a=t(3365),s=t(6368),c=t(7421),l=t(2140);function _templateObject(){let n=(0,i._)(["\n  .ministries {\n    margin-left: 24px;\n  }\n  @media "," {\n    .ministries {\n      margin-left: 40px;\n    }\n  }\n  @media "," {\n    flex-direction: row;\n  }\n"]);return _templateObject=function(){return n},n}let renderChildren=n=>(0,o.jsx)("div",{className:"ministries",children:null==n?void 0:n.map((n,e)=>(0,o.jsx)(l.Z,{image:n.cover,text:n.name,route:"/categories/".concat(n.type,"/").concat(n.slug)},e))});function SectionVocations(n){let{groups:e}=n,t=e.filter(n=>n.children.length>0);return(0,o.jsx)(p,{children:(0,o.jsx)("div",{children:null==t?void 0:t.map((n,e)=>(0,o.jsxs)("div",{children:[(0,o.jsx)(l.Z,{image:n.cover,text:n.name,route:"/categories/vocation/".concat(n.slug)}),n.children&&renderChildren(n.children)]},e))})})}let p=r.ZP.section.withConfig({componentId:"sc-156c3f87-0"})(_templateObject(),c.U.tablet,c.U.desktop);var d=t(1664),m=t.n(d),u=t(131),f=t(7294);function MainList_templateObject(){let n=(0,i._)(["\n  display: none;\n\n  @media "," {\n    display: initial;\n    padding: 0;\n    grid-column: 1 / span 4;\n    top: 0;\n    height: 80vh;\n    position: -webkit-sticky;\n    position: sticky;\n  }\n\n  @media "," {\n    visibility: visible;\n  }\n  \n  .anchor-list {\n    position: relative;\n    font-family: Stelvio, sans-serif;\n    font-size: 20px;\n    font-weight: 400;\n\n    .list-number {\n      display: inline-block;\n      color: #f1f1e8;\n      width: 40px;\n    }\n\n    ul {\n      margin-top: 64px;\n      padding: 0;\n    }\n\n    li {\n      font-family: Switzer, sans-serif;\n      list-style: none;\n      color: rgba(249, 246, 241, 0.6);\n      margin: 16px 0 0 0;\n      padding: 0;\n    }\n  }\n"]);return MainList_templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  position: relative;\n  color: rgba(249, 246, 241, 0.85);\n  mix-blend-mode: exclusion;\n  grid-column: 1 / span 12;\n\n  @media "," {\n    margin-top: 64px;\n    padding: 0;\n    grid-column: 5 / span 8;\n  }\n\n  h3 {\n    font-size: 32px;\n    margin-bottom: 16px;\n    font-weight: 500;\n\n    @media "," {\n      font-size: clamp(24px, 3vw, 38px);\n      max-width: 45%;\n      margin-top: 32px;\n    }\n  }\n\n  .section-marker {\n    position: absolute;\n    margin-top: 360px;\n    height: 100%;\n  }\n\n  .main-topic-wrapper {\n    scroll-margin-top: 64px;\n    @media "," {\n      display: flex;\n      flex-direction: row;\n      justify-content: space-between;\n      border-top: 1px solid rgba(248, 248, 243, 0.18);\n    }\n  }\n\n  .topic-list-wrapper {\n    ul {\n      margin: 0;\n      padding: 0;\n    }\n\n    li {\n      font-family: Switzer, sans-serif;\n      list-style: none;\n\n      span {\n        float: right;\n      }\n\n      //border-bottom: 1px solid rgba(248, 248, 243, 0.2);\n      &:hover {\n        color: var(--c-brand-lighter)\n      }\n    }\n\n    li:last-child {\n      border: none;\n      margin-bottom: 8px;\n    }\n\n    .topic-lvl-2 {\n      padding-top: 24px;\n      padding-bottom: 16px;\n      font-size: 20px;\n      font-weight: 600;\n    }\n\n    .topic-lvl-3 {\n      padding: 12px 0;\n      font-size: 18px;\n      font-weight: 400;\n      color: rgba(249, 246, 241, 0.9);\n    }\n\n    @media "," {\n      width: calc(50% - 40px);\n      margin: 16px 0;\n    }\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  position: absolute;\n  width: 80%;\n  aspect-ratio: 1/1;\n  left: 25vw;\n  bottom: -10%;\n  z-index: -1;\n  transition: all 450ms ease-in-out;\n  border-radius: 100%;\n  overflow: hidden;\n\n  .number-container {\n    position: relative;\n    margin-top: 11%;\n    margin-left: ",";\n    height: 78%;\n    width: 546%;\n    left: ","%;\n    box-sizing: border-box;\n    transform-origin: right;\n    transition-duration: 650ms;\n    transition-timing-function: cubic-bezier(0.79, 0.43, 0.38, 0.99);\n  }\n\n  svg {\n    position: relative;\n    height: 100%;\n    width: 100%;\n\n    path {\n      stroke: none;\n      fill: #1C373C;\n    }\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  position: relative;\n  padding: 64px var(--border-space) 128px var(--border-space);\n  display: grid;\n  background-color: var(--blue-dark);\n  grid-template-columns: repeat(",", 1fr);\n  column-gap: ","px;\n\n  @media "," {\n    grid-template-columns: repeat(",", 1fr);\n    column-gap: ","px;\n  }\n"]);return _templateObject3=function(){return n},n}let NumbersSVG=()=>(0,o.jsxs)("svg",{width:"1400",height:"200",viewBox:"0 0 1400 200",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,o.jsx)("path",{opacity:"0.01",fillRule:"evenodd",clipRule:"evenodd",d:"M0 0H1400V200H0V0Z",fill:"#D9D9D9"}),(0,o.jsx)("path",{d:"M104.18 14H119V186H94.9798V48.01L93.7504 47.7147C92.1206 50.9085 89.0265 53.3544 85.2076 55.2186C81.3947 57.0797 76.9229 58.3295 72.6351 59.1661C68.3516 60.002 64.2771 60.4206 61.2715 60.6299C59.7694 60.7346 58.5363 60.7868 57.6798 60.8129C57.4165 60.821 57.1889 60.8265 57 60.8303V39.134C58.3775 39.3073 59.5765 39.3072 60.7392 39.3072H60.7533C81.8748 39.3072 99.2009 31.1452 104.18 14Z",stroke:"#333333"}),(0,o.jsx)("path",{d:"M307.465 139.468L307.463 139.469L278.462 165.653L277.208 166.786H278.901H362V186H237V172.371L285.019 130.996C297.746 120.074 309.18 110.181 317.424 99.9347C325.68 89.674 330.78 79.0069 330.78 66.5286C330.78 55.7471 327.763 47.6555 322.003 42.2665C316.251 36.8842 307.865 34.2957 297.332 34.2957C284.372 34.2957 274.277 40.2097 268.059 49.4299C261.972 58.4548 259.623 70.6087 261.857 83.424H239.874C236.414 64.086 240.475 46.7423 250.577 34.2405C260.768 21.6292 277.168 13.8678 298.412 14.0017H298.416C316.323 14.0017 330.72 18.9621 340.633 27.8708C350.539 36.7725 356.037 49.6782 356.037 65.7188C356.037 80.6471 350.018 93.604 340.923 105.58C331.819 117.568 319.671 128.527 307.465 139.468Z",stroke:"#333333"}),(0,o.jsx)("path",{d:"M537.102 97.0419V97.042L535.845 97.7305L537.189 98.2275V98.2276L537.192 98.2286L537.205 98.2333L537.258 98.2539C537.306 98.2727 537.38 98.3014 537.477 98.3405C537.669 98.4186 537.955 98.5379 538.32 98.7003C539.051 99.0254 540.1 99.5225 541.359 100.209C543.88 101.582 547.24 103.708 550.599 106.714C557.311 112.722 564 122.229 564 136.304C564 155.287 555.789 167.667 543.516 175.334C531.199 183.027 514.745 186 498.278 186C478.288 186 462.801 180.794 452.282 170.906C441.89 161.137 436.255 146.711 436 127.948H459.82C460.039 140.679 462.84 150.207 469.02 156.568C475.311 163.041 484.973 166.106 498.542 166.106C509.253 166.106 519.455 165.252 526.986 161.09C530.77 158.998 533.883 156.07 536.043 152.01C538.2 147.955 539.388 142.809 539.388 136.304V136.293V136.282C539.186 130.293 537.774 125.571 535.424 121.856C533.073 118.14 529.809 115.474 525.971 113.559C518.325 109.746 508.342 108.887 498.56 108.622H498.551H498.542C493.519 108.622 489.209 109.096 485.187 109.594L485.435 87.4912C489.254 87.9632 493.536 88.1982 498.278 88.1982C507.556 88.1982 516.562 87.1411 523.264 83.2406C526.628 81.2824 529.413 78.6068 531.352 74.9985C533.29 71.3936 534.364 66.8934 534.364 61.3108V61.299V61.2872C533.96 50.117 529.846 43.2038 523.28 39.1257C516.773 35.0837 507.957 33.8935 498.278 33.8935C485.889 33.8935 477.269 36.8306 471.706 43.261C466.254 49.5621 463.857 59.0859 463.529 72.0526H439.706C440.074 52.7432 445.131 38.3144 454.775 28.6823C464.528 18.9415 479.078 14 498.542 14C516.598 14 531.577 17.2391 542.094 24.7437C552.572 32.2208 558.714 43.9999 558.976 61.3207V61.3253C559.235 73.0239 553.791 81.9435 548.24 87.9633C545.467 90.9719 542.675 93.246 540.577 94.7673C539.53 95.5275 538.656 96.0987 538.047 96.4788C537.742 96.6688 537.504 96.8108 537.342 96.9048C537.261 96.9518 537.2 96.9867 537.159 97.0096L537.115 97.0349L537.105 97.0407L537.102 97.0418V97.0419Z",stroke:"#333333"}),(0,o.jsx)("path",{d:"M721.05 146.213V145.563H720.4H627V119.491L714.655 14H745.325V125.308V125.958H745.975H772V145.563H745.975H745.325V146.213V186H721.05V146.213ZM721.05 40.8575V39.0562L719.9 40.4419L649.774 124.892L648.889 125.958H650.275H720.4H721.05V125.308V40.8575Z",stroke:"#333333"}),(0,o.jsx)("path",{d:"M862.967 82.4934L862.746 84.5929L864.106 82.9826V82.9824L864.107 82.9814L864.112 82.9747L864.139 82.9446C864.146 82.9364 864.155 82.9268 864.164 82.9161C864.186 82.8908 864.215 82.8584 864.251 82.8195C864.351 82.7082 864.504 82.5424 864.71 82.3301C865.122 81.9054 865.745 81.2942 866.582 80.5586C868.255 79.0873 870.781 77.1192 874.174 75.1492C880.96 71.2109 891.222 67.261 905.104 67.261C923.291 67.261 938 72.8944 948.161 82.9902C958.322 93.0854 964 107.708 964 125.819C964 144.186 957.792 159.214 946.791 169.652C935.788 180.093 919.93 185.998 900.53 186C873.755 185.461 857.857 176.001 848.533 163.814C839.393 151.87 836.501 137.215 836 125.514L860.03 129.618C861.376 151.351 878.851 165.672 900.793 165.672C912.25 165.672 921.986 161.975 928.861 155.283C935.74 148.587 939.701 138.948 939.701 127.171C939.701 115.126 935.673 105.216 928.662 98.3188C921.652 91.4227 911.713 87.5887 899.985 87.5887C884.341 87.5887 871.273 94.5556 864.865 106.987L837.647 102.566C837.661 102.438 837.676 102.292 837.694 102.128C837.768 101.457 837.874 100.483 838.011 99.2461C838.283 96.7735 838.673 93.2558 839.14 89.0421C840.075 80.6146 841.321 69.4031 842.567 58.2C843.813 46.997 845.059 35.8024 845.994 27.4086L847.123 17.2675L847.439 14.4268L847.487 14H953.493V33.2463H868.731H868.148L868.086 33.8285L862.967 82.4934Z",stroke:"#333333"}),(0,o.jsx)("path",{d:"M1237.5 34.7492V35.2492H1238H1334.27C1304.28 70.6968 1285.77 130.921 1284.97 185.993L1284.96 186.5H1285.47H1310.14H1310.64V186C1310.64 122.767 1329.65 72.393 1366.28 35.0996L1366.3 35.0799L1366.32 35.0582L1367.39 33.6931L1367.5 33.5571V33.3841V14V13.5H1367H1238H1237.5V14V34.7492Z",stroke:"#333333"}),(0,o.jsx)("path",{d:"M1063.85 88.4617H1064.15L1064.34 88.2332C1071.86 79.3816 1086.45 70.9725 1107.91 70.9725C1140.51 70.9725 1164 96.8831 1164 129.149C1164 163.443 1136.91 186 1103.7 186C1079.63 186 1062.87 175.836 1052.04 160.342C1041.19 144.817 1036.26 123.896 1036 102.381C1036 79.3952 1040.41 57.2838 1051.29 40.9384C1062.15 24.631 1079.49 14 1105.54 14C1129.69 14 1153.39 24.5765 1161.67 47.0575L1136.86 52.5961C1132.83 39.8889 1119.75 33.897 1106.86 33.897C1091.75 33.897 1080.61 40.0155 1073.26 49.6201C1065.93 59.2033 1062.41 72.2073 1062.41 85.9558V87.8107V88.4617H1063.05H1063.85ZM1064.78 129.149C1064.78 151.277 1081.74 166.103 1102.9 166.103C1113.6 166.103 1122.75 162.541 1129.22 156.11C1135.68 149.678 1139.44 140.427 1139.44 129.149C1139.44 117.607 1135.62 108.092 1129.02 101.461C1122.42 94.8306 1113.07 91.1345 1102.11 91.1345C1091.4 91.1345 1082.06 94.8326 1075.4 101.458C1068.73 108.087 1064.78 117.601 1064.78 129.149Z",stroke:"#333333"})]}),N1Theme=n=>{let{topic:e,id:t}=n;return(0,o.jsxs)("div",{id:"theme-".concat(t),className:"main-topic-wrapper",children:[(0,o.jsx)("h3",{children:e.name}),(0,o.jsx)(N2List,{topics:e.children})]})},N2List=n=>{let{topics:e}=n;return(0,o.jsx)("div",{className:"topic-list-wrapper",children:(0,o.jsx)("div",{children:e.map((n,e)=>n.postCount>0&&(0,o.jsx)(N2Theme,{topic:n},e))})})},N2Theme=n=>{var e;let{topic:t}=n;return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("li",{className:"topic-lvl-2",children:(0,o.jsxs)(m(),{href:"/categories/".concat(t.slug),children:[t.name," ",(0,o.jsx)("span",{children:t.postCount})]})}),(null===(e=t.children)||void 0===e?void 0:e.length)>0&&(0,o.jsx)("ul",{children:t.children.map((n,e)=>n.postCount>0&&(0,o.jsx)("li",{className:"topic-lvl-3",children:(0,o.jsxs)(m(),{href:"/categories/".concat(n.slug),children:[n.name," ",(0,o.jsx)("span",{children:n.postCount})]})},e))})]})};function MainList(n){let{topics:e}=n,[t,i]=(0,f.useState)(0),[r,a]=(0,u.YD)(),[s,c]=(0,u.YD)(),[l,p]=(0,u.YD)(),[d,m]=(0,u.YD)(),[C,v]=(0,u.YD)(),[j,w]=(0,u.YD)(),[_,V]=(0,u.YD)(),getRef=n=>{switch(n){case 0:return r;case 1:return s;case 2:return l;case 3:return d;case 4:return C;case 5:return j;case 6:return _;default:return null}};return(0,f.useEffect)(()=>{let n=t;a&&(n=0),c&&(n=1),p&&(n=2),m&&(n=3),v&&(n=4),w&&(n=5),V&&(n=6),i(n)},[t,a,c,p,m,v,w,V]),(0,o.jsxs)(b,{children:[(0,o.jsxs)(h,{children:[(0,o.jsx)("div",{className:"anchor-list",children:(0,o.jsx)("ul",{children:e.map((n,e)=>(0,o.jsx)("li",{children:(0,o.jsxs)("a",{href:"#theme-".concat(e),children:[(0,o.jsx)("span",{className:"list-number",children:"".concat(e+1)}),n.name]})},e))})}),(0,o.jsx)(x,{position:t,children:(0,o.jsx)("div",{className:"number-container",children:(0,o.jsx)(NumbersSVG,{})})})]}),(0,o.jsx)(g,{children:e.map((n,e)=>(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"section-marker",ref:getRef(e)}),(0,o.jsx)(N1Theme,{topic:n,id:e})]},e))})]})}let h=r.ZP.div.withConfig({componentId:"sc-ec7745a2-0"})(MainList_templateObject(),c.U.desktop,c.U.desktop),g=r.ZP.div.withConfig({componentId:"sc-ec7745a2-1"})(_templateObject1(),c.U.desktop,c.U.desktop,c.U.desktop,c.U.desktop),x=r.ZP.div.withConfig({componentId:"sc-ec7745a2-2"})(_templateObject2(),n=>(n.position,"11%"),n=>-(78*n.position)),b=r.ZP.div.withConfig({componentId:"sc-ec7745a2-3"})(_templateObject3(),n=>n.col?n.col:2,n=>n.gutter?n.gutter:0,c.U.desktop,n=>n.col?n.col:12,n=>n.gutter?64:0);var C=t(2053),v=t(2962);function categories_templateObject(){let n=(0,i._)(["\n  position: relative;\n  height: auto;\n  margin-bottom: 48px;\n  \n  display: flex;\n  flex-direction: column;\n  \n  .description {\n    font-family: Switzer, sans-serif;\n    margin-top: 64px;\n    font-size: 16px;\n  }\n  \n  @media "," {\n    margin-bottom: 96px;\n    flex-direction: row;\n    .description {\n      position: sticky;\n      top: 80px;\n      margin-top: 0;\n      max-height: 200px;\n      .title {\n        margin-top: -8px;\n      }\n      margin-bottom: 14px;\n      width: 33%;\n      line-height: 23px;\n      color: #161616;\n    }\n    .list {\n      width: 66%;\n      margin-right: 80px;\n    }\n  }\n"]);return categories_templateObject=function(){return n},n}function categories_templateObject1(){let n=(0,i._)(["\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 600px;\n  background-color: #F9F1E6;\n  \n  .search-section-text {\n    text-align: center;\n    font-family: Stelvio, sans-serif;\n    font-size: 32px;\n    font-weight: 500;\n    margin-bottom: 32px;\n  }\n"]);return categories_templateObject1=function(){return n},n}var j=!0;function Categories(n){let{groups:e,topics:t}=n;return t=(0,a.Ay)(t),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(v.PB,{title:"TPSG - Th\xe8mes",description:""}),(0,o.jsxs)("div",{className:"site-padding",children:[(0,o.jsx)("header",{children:(0,o.jsx)(s.V1,{children:"Th\xe8mes"})}),(0,o.jsxs)(w,{children:[(0,o.jsx)("div",{className:"list",children:(0,o.jsx)(SectionVocations,{groups:e})}),(0,o.jsxs)("div",{className:"description",children:[(0,o.jsx)("h2",{className:"title",children:"Vocations"}),(0,o.jsx)("p",{children:"Glorifier Dieu dans tous les aspects de notre vie c’est le glorifier dans chacune de nos vocations. Cela veut dire assumer les responsabilit\xe9s qu’il nous confie l\xe0 o\xf9 il nous place: dans notre famille, dans notre \xe9glise, dans notre vie sociale. Concr\xe8tement, glorifier Dieu c’est \xeatre le p\xe8re, le fr\xe8re et le coll\xe8gue que Dieu m’appelle \xe0 \xeatre."})]})]})]}),(0,o.jsx)("section",{children:(0,o.jsx)(MainList,{topics:t})}),(0,o.jsxs)(_,{children:[(0,o.jsxs)("p",{className:"search-section-text",children:["Vous n'avez pas trouv\xe9 ce que vous cherchiez? ",(0,o.jsx)("br",{}),"Essayez notre outil de recherche"]}),(0,o.jsx)(C.Z,{link:"/recherche",text:"Rechercher",theme:"dark"})]})]})}let w=r.ZP.section.withConfig({componentId:"sc-b2cbc2b2-0"})(categories_templateObject(),c.U.desktop),_=r.ZP.section.withConfig({componentId:"sc-b2cbc2b2-1"})(categories_templateObject1())},6368:function(n,e,t){"use strict";t.d(e,{DZ:function(){return f},GN:function(){return p},My:function(){return u},NZ:function(){return l},V1:function(){return s},X0:function(){return m},bP:function(){return c},hQ:function(){return h},kz:function(){return d}});var i=t(2729),o=t(9521),r=t(7421);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n  font-weight: 500;\n  color: #161616;\n  font-size: 48px;\n  margin-top:  calc(var(--spacing-l) - 48px * ",");\n  margin-bottom: calc(var(--spacing-l) - 48px * ",");\n  \n  @media "," {\n    font-size: 72px;\n    margin-top:  calc(var(--spacing-l) - 72px * ",");\n    margin-bottom: calc(var(--spacing-l) - 72px * ",");\n  }\n  @media "," {\n    font-size: 104px;\n    margin-top:  calc(var(--spacing-l) - 104px * ",");\n    margin-bottom: calc(var(--spacing-l) - 104px * ",");\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  box-sizing: content-box;\n  color: #ececec;\n  margin-top: 0;\n  margin-bottom: 0;\n  font-size: clamp(24px, 1.125rem + 1vw, 32px);\n  font-weight: 400;\n  max-width: 600px;\n  line-height: 105%;\n  &:before {\n    display: block;\n    margin-bottom: 8px;\n    content: '","';\n    color: ",";\n    font-size: 18px;\n  }\n  @media "," {\n    font-size: 32px;\n    margin-right: 24px;\n  }\n  @media "," {\n    font-weight: 400;\n    margin-right: 48px;\n    font-size: clamp(32px, 3.5vw, 48px);\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(['\n  \n  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);\n  \n  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;\n  font-size: var(--title-size);\n  color: ',";\n  font-weight: 400;\n  margin-top: 0;\n  margin-bottom: calc( var(--title-size) * -",");\n  \n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  font-size: 24px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 48px;\n    margin-bottom: 16px;\n    margin-top: 16px;\n  }\n"]);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  font-size: 20px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media "," {\n    font-size: 30px;\n  }\n  span {\n    position: relative;\n    margin-left: 8px;\n    display: inline-block;\n    margin-bottom: -2px;\n    height: 22px;\n    width: 22px;\n  }\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  font-size: 16px;\n  color: #888888;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  padding-right: 30px;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media screen and (max-width:320px){ // Little screen only\n    padding: 0;\n  }\n  @media "," {\n    font-size:20px;\n  }\n"]);return _templateObject5=function(){return n},n}function _templateObject6(){let n=(0,i._)(['\n  font-size: 14px;\n  font-style: italic;\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  color: #7a7a7a;\n  margin-top: 0;\n  margin-bottom: 10px;\n  @media '," {\n    font-size: 17px;\n  }\n"]);return _templateObject6=function(){return n},n}function _templateObject7(){let n=(0,i._)(["\n  font-size: 38px;\n  font-weight: 500;\n  line-height: 110%;\n  margin-top: 0;\n  margin-bottom: 16px;\n  @media "," {\n    font-size: 64px;\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n  @media "," {\n    margin-bottom: 24px;\n    margin-top: 24px;\n  }\n"]);return _templateObject7=function(){return n},n}function _templateObject8(){let n=(0,i._)(["\n  color: #161616;\n  letter-spacing: 0.04em;\n  margin-top: 48px;\n  text-transform: uppercase;\n  @media "," {\n    margin-top: 0;\n    margin-bottom: 0;\n    font-size: 22px;\n    font-weight: 500;\n    &:hover {\n      color: var(--brand-color);\n    }\n  }\n"]);return _templateObject8=function(){return n},n}function _templateObject9(){let n=(0,i._)(["\n  font-weight: 400;\n  font-size: 26px;\n  margin: 0;\n  color: #161616;\n  @media "," {\n    font-size: 32px;\n  }\n"]);return _templateObject9=function(){return n},n}let a={topSpace:.096,minBottomSpace:.2788,maxBottomSpace:.4711},s=o.ZP.h1.withConfig({componentId:"sc-a3af5335-0"})(_templateObject(),a.topSpace,a.minBottomSpace,r.U.tablet,a.topSpace,a.minBottomSpace,r.U.desktop,a.topSpace,a.minBottomSpace),c=o.ZP.h2.withConfig({componentId:"sc-a3af5335-1"})(_templateObject1(),n=>n.label,n=>n.color,r.U.tablet,r.U.desktop),l=o.ZP.h2.withConfig({componentId:"sc-a3af5335-2"})(_templateObject2(),n=>n.light?"var(--c-soft-cream)":"var(--soft-dark)",a.maxBottomSpace),p=o.ZP.h2.withConfig({componentId:"sc-a3af5335-3"})(_templateObject3(),r.U.tablet),d=o.ZP.h4.withConfig({componentId:"sc-a3af5335-4"})(_templateObject4(),r.U.tablet),m=o.ZP.p.withConfig({componentId:"sc-a3af5335-5"})(_templateObject5(),r.U.tablet),u=o.ZP.p.withConfig({componentId:"sc-a3af5335-6"})(_templateObject6(),r.U.tablet),f=o.ZP.h1.withConfig({componentId:"sc-a3af5335-7"})(_templateObject7(),r.U.tablet,r.U.desktop),h=o.ZP.p.withConfig({componentId:"sc-a3af5335-8"})(_templateObject8(),r.U.desktop);o.ZP.p.withConfig({componentId:"sc-a3af5335-9"})(_templateObject9(),r.U.tablet)},131:function(n,e,t){"use strict";t.d(e,{YD:function(){return useInView}});var i=t(7294);function _extends(){return(_extends=Object.assign||function(n){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=t[i])}return n}).apply(this,arguments)}function _setPrototypeOf(n,e){return(_setPrototypeOf=Object.setPrototypeOf||function(n,e){return n.__proto__=e,n})(n,e)}var o=new Map,r=new WeakMap,a=0,s=void 0;function observe(n,e,t,i){if(void 0===t&&(t={}),void 0===i&&(i=s),void 0===window.IntersectionObserver&&void 0!==i){var c=n.getBoundingClientRect();return e(i,{isIntersecting:i,target:n,intersectionRatio:"number"==typeof t.threshold?t.threshold:0,time:0,boundingClientRect:c,intersectionRect:c,rootBounds:c}),function(){}}var l=function(n){var e=Object.keys(n).sort().filter(function(e){return void 0!==n[e]}).map(function(e){var t;return e+"_"+("root"===e?(t=n.root)?(r.has(t)||(a+=1,r.set(t,a.toString())),r.get(t)):"0":n[e])}).toString(),t=o.get(e);if(!t){var i,s=new Map,c=new IntersectionObserver(function(e){e.forEach(function(e){var t,o=e.isIntersecting&&i.some(function(n){return e.intersectionRatio>=n});n.trackVisibility&&void 0===e.isVisible&&(e.isVisible=o),null==(t=s.get(e.target))||t.forEach(function(n){n(o,e)})})},n);i=c.thresholds||(Array.isArray(n.threshold)?n.threshold:[n.threshold||0]),t={id:e,observer:c,elements:s},o.set(e,t)}return t}(t),p=l.id,d=l.observer,m=l.elements,u=m.get(n)||[];return m.has(n)||m.set(n,u),u.push(e),d.observe(n),function(){u.splice(u.indexOf(e),1),0===u.length&&(m.delete(n),d.unobserve(n)),0===m.size&&(d.disconnect(),o.delete(p))}}var c=["children","as","triggerOnce","threshold","root","rootMargin","onChange","skip","trackVisibility","delay","initialInView","fallbackInView"];function isPlainChildren(n){return"function"!=typeof n.children}var l=function(n){function InView(e){var t;return(t=n.call(this,e)||this).node=null,t._unobserveCb=null,t.handleNode=function(n){!t.node||(t.unobserve(),n||t.props.triggerOnce||t.props.skip||t.setState({inView:!!t.props.initialInView,entry:void 0})),t.node=n||null,t.observeNode()},t.handleChange=function(n,e){n&&t.props.triggerOnce&&t.unobserve(),isPlainChildren(t.props)||t.setState({inView:n,entry:e}),t.props.onChange&&t.props.onChange(n,e)},t.state={inView:!!e.initialInView,entry:void 0},t}InView.prototype=Object.create(n.prototype),InView.prototype.constructor=InView,_setPrototypeOf(InView,n);var e=InView.prototype;return e.componentDidUpdate=function(n){(n.rootMargin!==this.props.rootMargin||n.root!==this.props.root||n.threshold!==this.props.threshold||n.skip!==this.props.skip||n.trackVisibility!==this.props.trackVisibility||n.delay!==this.props.delay)&&(this.unobserve(),this.observeNode())},e.componentWillUnmount=function(){this.unobserve(),this.node=null},e.observeNode=function(){if(this.node&&!this.props.skip){var n=this.props,e=n.threshold,t=n.root,i=n.rootMargin,o=n.trackVisibility,r=n.delay,a=n.fallbackInView;this._unobserveCb=observe(this.node,this.handleChange,{threshold:e,root:t,rootMargin:i,trackVisibility:o,delay:r},a)}},e.unobserve=function(){this._unobserveCb&&(this._unobserveCb(),this._unobserveCb=null)},e.render=function(){if(!isPlainChildren(this.props)){var n=this.state,e=n.inView,t=n.entry;return this.props.children({inView:e,entry:t,ref:this.handleNode})}var o=this.props,r=o.children,a=o.as,s=function(n,e){if(null==n)return{};var t,i,o={},r=Object.keys(n);for(i=0;i<r.length;i++)t=r[i],e.indexOf(t)>=0||(o[t]=n[t]);return o}(o,c);return i.createElement(a||"div",_extends({ref:this.handleNode},s),r)},InView}(i.Component);function useInView(n){var e=void 0===n?{}:n,t=e.threshold,o=e.delay,r=e.trackVisibility,a=e.rootMargin,s=e.root,c=e.triggerOnce,l=e.skip,p=e.initialInView,d=e.fallbackInView,m=i.useRef(),u=i.useState({inView:!!p}),f=u[0],h=u[1],g=i.useCallback(function(n){void 0!==m.current&&(m.current(),m.current=void 0),!l&&n&&(m.current=observe(n,function(n,e){h({inView:n,entry:e}),e.isIntersecting&&c&&m.current&&(m.current(),m.current=void 0)},{root:s,rootMargin:a,threshold:t,trackVisibility:r,delay:o},d))},[Array.isArray(t)?t.toString():t,s,a,c,l,r,d,o]);(0,i.useEffect)(function(){m.current||!f.entry||c||l||h({inView:!!p})});var x=[g,f.inView,f.entry];return x.ref=x[0],x.inView=x[1],x.entry=x[2],x}l.displayName="InView",l.defaultProps={threshold:0,triggerOnce:!1,initialInView:!1}}},function(n){n.O(0,[962,774,888,179],function(){return n(n.s=5190)}),_N_E=n.O()}]);