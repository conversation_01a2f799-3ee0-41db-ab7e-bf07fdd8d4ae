"use strict";exports.id=5745,exports.ids=[5745],exports.modules={6035:(e,a,t)=>{t.d(a,{a:()=>useMediaQuery});var s=t(6689);let useMediaQuery=({width:e,mediaQuery:a})=>{let[t,r]=(0,s.useState)(!1),i=(0,s.useCallback)(e=>{e.matches?r(!0):r(!1)},[]);return(0,s.useEffect)(()=>{let t=window.matchMedia(e?`(max-width: ${e}px)`:a);return t.addEventListener("change",i),t.matches&&r(!0),()=>t.removeEventListener("change",i)},[]),t}},2705:(e,a,t)=>{t.d(a,{Rj:()=>getMeiliParamsPaginate});let getMeiliParamsPaginate=(e,a,t="default")=>{let s=[],r=1;for(let a in e)switch(a){case"type":case"author":s.push(""+a+' = "'+e[a]+'"');break;case"topic":case"tag":case"serie":s.push(""+a+'s = "'+e[a]+'"');break;case"blog":s.push("blog = "+e.blog);break;case"pageTitle":s=e.pageTitle;break;case"page":r=+e.page>1?+e.page:1}return{filter:s,page:r,hitsPerPage:a}}}};