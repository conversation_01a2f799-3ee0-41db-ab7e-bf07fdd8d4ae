"use strict";(()=>{var e={};e.id=4820,e.ids=[4820,2888],e.modules={8826:(e,r,t)=>{t.r(r),t.d(r,{config:()=>x,default:()=>p,getServerSideProps:()=>d,getStaticPaths:()=>c,getStaticProps:()=>n,reportWebVitals:()=>_,routeModule:()=>q,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>m,unstable_getStaticParams:()=>P,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>g});var s=t(7093),a=t(5244),o=t(1323),i=t(779),l=t(4033),u=t(2534);let p=(0,o.l)(u,"default"),n=(0,o.l)(u,"getStaticProps"),c=(0,o.l)(u,"getStaticPaths"),d=(0,o.l)(u,"getServerSideProps"),x=(0,o.l)(u,"config"),_=(0,o.l)(u,"reportWebVitals"),g=(0,o.l)(u,"unstable_getStaticProps"),S=(0,o.l)(u,"unstable_getStaticPaths"),P=(0,o.l)(u,"unstable_getStaticParams"),b=(0,o.l)(u,"unstable_getServerProps"),m=(0,o.l)(u,"unstable_getServerSideProps"),q=new s.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},components:{App:l.default,Document:i.default},userland:u})},9114:e=>{e.exports=require("@apollo/client")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var r=require("../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[3181,5016,6859,8450,2534,4033,779],()=>__webpack_exec__(8826));module.exports=t})();