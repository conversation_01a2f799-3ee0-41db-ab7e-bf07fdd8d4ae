{"routes": [{"method": "GET", "path": "/posts/issues", "handler": "post.issues", "config": {"policies": []}}, {"method": "GET", "path": "/posts/update-created-by", "handler": "post.updateCreated<PERSON>y", "config": {"policies": []}}, {"method": "GET", "path": "/posts/convert-old-urls", "handler": "post.convertOldUrls", "config": {"policies": []}}, {"method": "GET", "path": "/posts/add-reading-time", "handler": "post.addReadingTime", "config": {"policies": []}}, {"method": "GET", "path": "/posts/redirects", "handler": "post.redirects", "config": {"policies": []}}, {"method": "GET", "path": "/posts/related-posts", "handler": "post.relatedPosts", "config": {"policies": []}}, {"method": "GET", "path": "/posts/add-type", "handler": "post.addType", "config": {"policies": []}}, {"method": "GET", "path": "/posts/update-topics", "handler": "post.updateTopics", "config": {"policies": []}}, {"method": "GET", "path": "/posts/add-tags", "handler": "post.addTags", "config": {"policies": []}}, {"method": "GET", "path": "/posts/check-1pvr", "handler": "post.check1PVR", "config": {"policies": []}}, {"method": "GET", "path": "/posts/add-blogs", "handler": "post.addBlogs", "config": {"policies": []}}, {"method": "GET", "path": "/posts", "handler": "post.find", "config": {"policies": []}}, {"method": "GET", "path": "/posts/count", "handler": "post.count", "config": {"policies": []}}, {"method": "GET", "path": "/posts/:id", "handler": "post.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/posts", "handler": "post.create", "config": {"policies": []}}, {"method": "PUT", "path": "/posts/:id", "handler": "post.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/posts/:id", "handler": "post.delete", "config": {"policies": []}}]}