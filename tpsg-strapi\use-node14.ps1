# Script pour utiliser Node.js 14 dans PowerShell
# À exécuter dans le répertoire du projet

# Sauvegarder le PATH actuel
$oldPath = $env:PATH

# Vérifier si NVM est installé dans le nouveau chemin (C:\nvm)
if (Test-Path "C:\nvm\nvm.exe") {
    Write-Host "NVM trouvé dans C:\nvm" -ForegroundColor Green

    # Utiliser Node.js 14 via NVM
    Write-Host "Activation de Node.js 14..." -ForegroundColor Yellow
    & "C:\nvm\nvm.exe" use 14.21.3

    # Vérifier si la commande a réussi
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Erreur lors de l'activation de Node.js 14 via NVM." -ForegroundColor Red

        # Solution alternative: définir directement le PATH
        Write-Host "Tentative de solution alternative..." -ForegroundColor Yellow
        if (Test-Path "C:\nvm\v14.21.3\node.exe") {
            $env:PATH = "C:\nvm\v14.21.3;$env:PATH"
            Write-Host "Node.js 14 activé via PATH." -ForegroundColor Green
        } else {
            Write-Host "Impossible de trouver Node.js 14." -ForegroundColor Red
            exit 1
        }
    }
}
# Vérifier si NVM est installé dans l'ancien chemin (C:\Program Files\nvm)
elseif (Test-Path "C:\Program Files\nvm\nvm.exe") {
    Write-Host "NVM trouvé dans C:\Program Files\nvm" -ForegroundColor Yellow

    # Solution alternative: définir directement le PATH
    Write-Host "Utilisation de la solution alternative (PATH direct)..." -ForegroundColor Yellow
    if (Test-Path "C:\Program Files\nvm\v14.21.3\node.exe") {
        $env:PATH = "C:\Program Files\nvm\v14.21.3;$env:PATH"
        Write-Host "Node.js 14 activé via PATH." -ForegroundColor Green
    } else {
        Write-Host "Impossible de trouver Node.js 14." -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "NVM n'est pas installé ou n'est pas dans un chemin connu." -ForegroundColor Red
    exit 1
}

# Afficher les versions
Write-Host "Version de Node.js:" -ForegroundColor Cyan
node -v
Write-Host "Version de npm:" -ForegroundColor Cyan
npm -v

Write-Host "`nVous pouvez maintenant exécuter vos commandes npm." -ForegroundColor Green
Write-Host "Quand vous aurez terminé, vous pourrez restaurer le PATH original en exécutant:" -ForegroundColor Yellow
Write-Host '$env:PATH = "' + $oldPath + '"' -ForegroundColor Cyan
