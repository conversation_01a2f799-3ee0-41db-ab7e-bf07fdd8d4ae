# TPSG Frontend

1. [Pages](#1-pages)
2. [RSS](#2-rss)
3. [Recherche](#3-recherche)
4. [Preview](#4-preview)
5. [CoreData.json](#5-coredatajson)
6. [Markdown & Embeds](#6-markdown--embeds)

Rappel à propos des termes SSG / SSR / ISR / CSR:
https://nextjs.org/learn/foundations/how-nextjs-works/rendering

## 1) Pages

Ce tableau liste quelles pages sont en ISR et lesquelles utilisent Meilisearch pour récupérer les données à
afficher.

| page                                    | type | meilisearch |
|-----------------------------------------|------|-------------|
| /                                       | ISR  |             |
| (slug)                                  | ISR  |             |
| /article/(slug)                         | ISR  |             |
| /blog/(slug)                            | ISR  |             |
| /blog/(slug)/filtres                    |      | ✓           |
| /categories                             | ISR  |             |
| /categories/(slug)                      | ISR  | ✓           |
| /categories/(slug)/ressources           |      | ✓           |
| /categories/vocation/(slug)             | ISR  | ✓           |
| /categories/vocation/(slug)/ressources  |      | ✓           |
| /categories/ministere/(slug)            | ISR  | ✓           |
| /categories/ministere/(slug)/ressources | ISR  | ✓           |
| /formation                              | ISR  |             |
| /parcours-email                         | ISR  |             |
| /parcours-email/(slug)                  | ISR  |             |
| /podcasts                               |      |             |
| /podcasts/(slug)                        | ISR  | ✓           |
| /podcasts/(slug)/(ep-slug)              | ISR  |             |
| /webinaires                             | ISR  |             |
| /webinaires/(slug)                      | ISR  |             |
| /preview                                |      |             |
| /recherche                              |      | ✓           |
| /preview                                |      |             |

Le site ayant été créé avec une version de next.js inférieure à la version 13, il n'utilise donc pas le nouveau système de routage. La documentation peut se trouver à cette adresse:
[Next.js page router](https://nextjs.org/docs/pages/building-your-application/routing/pages-and-layouts)

## 2) RSS

Les différents flux sont générés à la racine du dossier `/public/`. Le code permettant de les produire se trouve dans le
dossier `/services/feeds.js`. Leur mise à jour est déclenchée par une visite sur la page d'accueil.

## 3) Recherche

Tous les résultats qu'affiche la page de recherche sont récupérés en utilisant l'API de Meilisearch. Ces résultats sont
divisés en deux parties distinctes: la liste principale et les corners stones.

> Les corners stones sont des ressources (post) qui possèdent une importance supérieure et qui doivent donc apparaître
> plus fréquemment sur le site. Dans l'index de Meilisearch ces ressources sont identifiés par la présence du
> champ `cs=true`

Lors du premier affichage de la page, les données à afficher sont récupérées côté serveur.
Pour cela, on utilise les paramètres présents dans l'URL afin de créer les deux requêtes (liste principale et corner
stones) qui seront exécutées par Meilisearch.

Lors des requêtes suivantes, les données sont récupérées directement côté client.

> **Particularité à noter.**
>
> Il n'est pas possible d'utiliser plusieurs critères de recherche en même temps. Les requêtes sont donc constituées du
> texte présent dans la barre de recherche et/ou d'un des filtres (auteurs, thèmes, types). Il n'est par exemple, pas
> possible de rechercher un article d'un auteur particulier dans un thème précis. Ce fonctionnement résulte d'un choix
> de
> départ dans la conception, visant à réduire la possibilité d'avoir des pages sans résultat afin de prévenir une
> possible sensation de vide chez l'utilisateur.

### Formation d'une nouvelle requête

En haut de page est déclaré l'état query:

```jsx
const [query, setQuery] = useState(initialQuery);
```

Cet état contient la requête initiale qui a été formée du côté serveur en prenant les paramètres présent dans l'URL. Le
fait d'initialiser `query` avec les valeurs de cette requête initiale nous permet d'éviter un rendu supplémentaire
inutile lors du déclenchement du hook d'effet écoutant les changements sur cet objet.

Structure et exemple de l'objet `query`:

```js
{
  terms:  "bible"
  filter: {
    value: "raphael-charrier"
    type: "authors"
  }
  page: 1
}
```

Sa mise à jour se passe par les composants `SearchTool` et `SearchPaginate`.
`SearchTool` contient l'input et les listes d'auteurs, de thèmes et de types de ressources.
Chaque fois que l'utilisateur valide sa saisie dans l'input ou sélectionne un élément dans une des différentes listes,
l'objet `query` est mis à jour, ce qui déclenche le hook d'effet lié.
Pour chaque mise à jour, on actualise les paramètres présents dans l'URL et on récupère les nouvelles données à
afficher. Ces données sont stockées dans un Hook d'état:

```jsx
const [results, setResults] = useState(initialResults);
```

> Les paramètres ajoutés dans l'URL permettent simplement de pouvoir partager ou sauvegarder le lien d'une recherche.
> Aucun rechargement de la page n'est provoqué la modification de l'URL.

Afin de ne pas afficher les ressources issues des requêtes des corner-stones dans la liste principale, on vérifie les
slugs qui sont présents dans les deux listes. En supprimant ainsi les posts de la liste principale, on réduit le nombre
de posts présents. Ce comportement devrait donc être corrigé par la suite afin que la liste puisse toujours afficher 10
éléments

**TODO**:

- Exclure les posts provenant de la récupération des corner-stones pendant, et non après la requête des posts de la
  liste principale. On pourra ajouter un nouveau paramètre dans le hook d'état `query` qui servira à stocker les ids des
  posts qui feront partie de la liste des corner-stones.

## 4) Preview

Les posts (articles, épisodes de webinaires ou podcast) peuvent être prévisualisés sur le front grâce à des liens
générés depuis Strapi.

Une URL de preview contient deux paramètres:

- le slug du post à récupérer
- la clé secrète permettant de protéger l'accès

La clé secrète fait partie des variables d'environnements de Strapi et Next. Elle donc constante.

Dans la requête permettant de retrouver le post associé au slug, on utilise le paramètre `_publicationState: "preview"`.
Le mot clef `preview` (bien qu'il puisse faire croire que seules les ressources en draft seront retournées) permet de
récupérer, et les posts déjà publiés, et les posts encore en draft.

```graphql
query PostPreview($slug: String!) {
  posts(where: { slug: $slug, _publicationState: "preview" }) {
    author {
        name
      # [...]
    }
    # [...]
  }
}
```

### Affichage

Sur le site, le contenu de chaque type de post est rendu à l'intérieur d'un composant particulier. Cela nous a évité
d'avoir à créer une page de prévisualisation pour chaque type de post. Ces composants, en plus du post en question,
acceptent un paramètre `preview` (booléen) permanent de définir s'il est nécessaire d'ajouter les éléments relatifs au
mode preview dans la méthode de rendu.

```jsx
switch (props.post.type) {
  case "article":
    return <ArticleLayout post={props.post} preview={true}/>;
  case "podcast":
    return <PodcastLayout episode={props.post} preview={true}/>;
  case "webinaire":
  // [...]
}
```

Le rendu conditionnel du mode preview ce fait avec l'ajout du composant ci-dessous:

```jsx
{ preview && <PreviewButton url={route}/> }
```

## 5) CoreData
Les `coredata` contiennent les listes suivantes:
- authors
- blogs
- topics
- podcasts
- topicGroups (ministères et vocations)

Les informations contenues dans ces listes permettent de fournir les données nécessaires au fonctionnement de certains composants dont:

**SearchTool** (`components/recherche/search-tool.js`) qui l'utilise afin de créer les listes de filtres (authors et
topics).

**HeaderMenu** (`components/layout/Header/HeaderMenu`) qui l'utilise afin de générer les différentes entrés du menu
(podcasts / topicGroups / blogs)

Dorénavant, au lieu d'un fichier qui est régulièrement mis à jour et stocké sur le serveur (méthode employée avant mai 2025), nous tirons profit du Data Cache de NextJS. Voir la [documentation](https://nextjs.org/docs/13/app/building-your-application/caching#data-cache).

Nous utilisons un contexte, `CoreDataContext` et son `ContextProvider` pour permettre l'accès à ces données dans toute l'application sans en refaire la requête à chaque fois. Ce contexte est un component parent du `Layout` (voir `tpsg-next/components/layout/index.js`) qui récupère les données en faisant appel à une API (`toutpoursagloire.com/api/coredata`) dans le backend de Next. Next s'occupe de revalider le cache toutes les heures.

Pour avoir accès à cette fonctionnalité de Next, il nous fallait faire usage du `App Router`. C'est pourquoi, en parallèle du `Page Router` (où les pages sont dans le dossier `/pages`), nous avons le dossier `/app` (pour le `App Router`), dans lequel se trouve le code pour l'API `coredata`. Voir la [documentation](https://nextjs.org/docs/13/app) pour plus d'infos.

Donc pour accèder à `coredata` dans l'application, il faut procéder de la manière suivante:

```js
import { useCoreData } from "context/CoreDataContext"

export default function MyComponent() {
  const coreData = useCoreData()
  // OU s'il ne faut que certains éléments de coreData:
  const { blogs } = useCoreData()

  // ...
}
```

(Voir par exemple `tpsg-next/components/layout/Header/HeaderDropDown.js`)


## 6) Markdown & Embeds

Sur Strapi, le contenu des posts est rédigé et sauvegardé au format Markdown. Sur le front, pour la conversion au format HTML, nous utilisons la librairie `react-markdown`.
À l'heure actuelle, il existe deux composants utilisant cette librairie pour afficher des données au format markdown:
- MDPost `/components/shared/post/md-body.js`
- RenderMarkdown `/components/shared/RenderMarkdown.js`

> TODO: Ces deux composants font sensiblement la même chose et devraient être fusionnés par la suite.

`react-markdown` permet d'utiliser des composants pour rendre certaines balises. Nous avons donc utilisé cette technique afin de gérer l'affichage des embeds à l'intérieur des posts. Pour ça, les embeds sont placés dans des blocks codes depuis l'éditeur (accent grave simple).

```jsx
// components/shared/RenderMarkdown.js
<ReactMarkdown
  components={{
    "code": codeComponent,
  }}
  rehypePlugins={[rehypeRaw]}>
</ReactMarkdown>
```

```jsx
// components/shared/RenderMarkdown.js
const codeComponent = ({ children }) => {
  return (
    <CookieWall style={codeComponentStyles}>
      <InnerHTML html={children[0]}/>
    </CookieWall>  )
}
```

`codeComponent` qui sert à insérer le contenu de l'iframe dans l'html généré par `react-markdown` ne retourne pas las balise code, seulement son contenu.

Le composant CookieWall (voir `components/shared/CookieWall.jsx`) permet d'afficher le contenu qui se trouve à l'intérieur, uniquement si le visiteur a autorisé les cookies tiers. Dans le cas où il ne les aurait pas autorisés, un message est affiché à la place, proposant d'accepter ce type de cookies afin de débloquer l'affichage du lecteur.

Exemple d'Iframe intégré au post sur Strapi:

```markdown
## Title
Lorem ipsum ...

`<iframe loading="lazy" class="youtube-player" width="640" height="..." ></iframe>`

### Subtitle
```
