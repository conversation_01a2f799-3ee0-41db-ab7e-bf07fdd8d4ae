import styled from "styled-components";
import { modulesAsObj } from "utils/components.utils";
import { dateForHumans } from "/utils/date.utils";
import { withRealSrc } from "utils/image-utils";
import { getPostRoute } from "utils/posts.utils";
import { sortByDate } from "utils/list.utils";

import SliderCard from "components/test/card/SliderCard";
import Carousel from "components/test/Carousel";
import SectionHeader from "components/shared/section/SectionHeader";
import { device } from "styles/device";


function RenderEpisode(post, index) {

  if(!post.author) return null;

  const cardOptions = {
    theme: "dark",
    invert: true
  }
  return (
    <SliderCard key={index} post={post} options={cardOptions}/>
  );
}

function sortEpisodes(list) {

  let latestFromChannels = {};

  let ahead = [];
  let behind;

  let i=0;

  for (const ep of list) {
    if (!latestFromChannels[ep.author]) {
      latestFromChannels[ep.author] = ep
    } else {
      let saveEpDate = new Date(latestFromChannels[ep.author].published_at);
      let epDate = new Date(ep.published_at);
      if (saveEpDate < epDate) {
        latestFromChannels[ep.author] = ep;
        latestFromChannels[ep.author].index = i;
      }
    }
    i++;
  }

  for (const [key, value] of Object.entries(latestFromChannels)) {
    ahead.push(latestFromChannels[key]);
    delete list[value.index];
  }

  ahead = ahead.sort(sortByDate);
  behind = list.sort(sortByDate).splice(0,4);

  return ahead.concat(behind)
}

function formatPost(post) {
  const { podcast } = modulesAsObj(post.modules);
  return {
    title: post.title,
    author: podcast.podcast.name,
    authorLink: "/podcasts/" + podcast.podcast.slug,
    image: withRealSrc(post.image),
    authorImage: withRealSrc(podcast.podcast.logoSmall),
    date: dateForHumans(post["published_at"]),
    published_at: post.published_at,
    route: getPostRoute(post)
  }
}

export default function SectionPodcasts({ data }) {

  let { posts } = data;
  posts = posts.map((post) => formatPost(post));

  posts = sortEpisodes(posts);

  return (
    <Section>
      <SectionHeader
        title={"Podcasts"}
        supTitle={"Derniers épisodes"}
        margin
        light/>

      <Carousel options={{ seeMoreUrl: "/recherche?type=Podcast&page=1", seeMoreText: "Voir tout" }}>
        { posts.map((post, index) => RenderEpisode(post, index) )}
      </Carousel>
    </Section>
  )
}

const Section = styled.section`
  position: relative;
  background-color: var(--blue-dark);
  padding: 56px 0 0 0;
  @media ${ device.tablet } {
    padding: 128px 0 0 0 ;
  }
  @media ${ device.desktop } {
    padding: 164px 0 0 0;
  }
`;
