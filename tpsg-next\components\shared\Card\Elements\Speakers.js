import styled from "styled-components";
import Image from "next/image";

/**
 *
 * @param {Object}  speakers
 * @param {string}  options.direction [vertical,horizontal]
 * @param {string}  options.size      [s,m]
 * @param {string}  options.theme     [dark,light]
 * @return {JSX.Element}
 * @constructor
 */

export default function Speakers({ speakers, options }) {

  return(
    <Wrapper>
      <div className={"elt-speakers-pics"}>
        <RenderPictures pics={speakers.pictures}/>
      </div>
      <div className={"elt-speakers-text"}>
        <p className={"elt-speakers-label"}>{`Orateur${speakers.pictures.length ? "s" : ""}`}</p>
        <p className={"elt-speakers-names"}>{speakers.names}</p>
      </div>
    </Wrapper>
  )
}

function RenderPictures({ pics }) {
  return pics.map((pic, key) => (
    <Picture key={key}>
      <Image
        src={pic}
        sizes={"34px"}
        fill
        alt={""}/>
    </Picture>
  ))
}


const Wrapper = styled.div`
  position: relative;
  display: flex;
  .elt-speakers-pics {
    display: flex;
    flex-direction: row;
  }
  .elt-speakers-text {
    color: var(--c-soft-cream);
    font-family: Switzer, "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 0 19px;
  }
  .elt-speakers-label {
    margin: -4px 0 4px 0;
    opacity: 0.72;
  }
  .elt-speakers-names {
    margin: 0;
  }
`;

const Picture = styled.div`
  position: relative;
  width: 34px;
  height: 34px;
  background-color: darkseagreen;
  border: 1px solid white;
  margin-right: -5px;
  border-radius: 32px;
  overflow: hidden;
`;