"use strict";exports.id=8672,exports.ids=[8672],exports.modules={8672:(e,r,o)=>{o.d(r,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var t=o(997),a=o(7518),s=o.n(a),i=o(7199),n=o(1664),p=o.n(n);let __WEBPACK_DEFAULT_EXPORT__=({text:e})=>{let r="/categories/"+(0,i.lV)(e);return t.jsx(p(),{href:r,legacyBehavior:!0,children:t.jsx(c,{className:"card-label",children:e})})},c=s().span.withConfig({componentId:"sc-e62d7975-0"})`
  position: relative;
  font-family: <PERSON><PERSON><PERSON>, "Helvetica Neue", Helvetica, "Arial", sans-serif;
  padding: 5px 11px 5px 11px;
  font-size: 11px;
  letter-spacing: 0.2px;
  border-radius: 100px;
  text-transform: uppercase;
  white-space: nowrap;
  margin-bottom: 8px;

  border: 1px solid #464646;
  background-color: transparent;
  color: #464646;

  &:hover {
    background-color: #242424;
    color: #f4f4f4;
  }

  cursor: pointer;
  `}};