const {MeiliSearch} = require("meilisearch")
const {performance} = require("perf_hooks");

const client = new MeiliSearch({
  host: process.env.MEILI_HOST,
  apiKey: process.env.MEILI_ADMIN_KEY,
})


/**
 * Crée un nouvel index sur Meilisearch dans le cas où il n'aurait pas encore été créé.
 * <PERSON>rs de la création, on invoque la fonction addExistingDocuments qui nous permet d'initialiser
 * l'index avec l'ensemble des posts déjà présents sur Strapi.
 *
 * @return {Promise<{message: string}>}
 */
const createIndex = async () => {
  try {
    const indexes = await client.getIndexes();
    const exist = indexes.results.filter(index => index.uid === "post").length

    // post index doesn't exist. It needs to be created. Existing documents
    // must be added.
    if (!exist) {
      console.log("No index found -> Creating new post index");
      await client.createIndex("post");
      console.log("Start adding existing documents...");
      const sTime = performance.now();
      await addExistingDocuments();
      const eTime = performance.now();
      console.log("Documents added in: ", Math.round(eTime - sTime), "ms");
      // Configuration des attributs filtrables
      await client.index("post").updateFilterableAttributes([
        "title",
        "topics",
        "tags",
        "author",
        "type",
        "blog",
        "cs",
      ]);

      // Configuration des attributs triables
      await client.index("post").updateSortableAttributes(["date"]);

      // Configuration des attributs de recherche
      await client.index("post").updateSearchableAttributes([
        "title",
        "body"
      ]);

      // Configuration de la pagination - cette méthode peut ne pas être disponible dans v0.27.0
      try {
        await client.index("post").updateSettings({
          "pagination": {
            "maxTotalHits": 4000
          }
        });
      } catch (error) {
        console.log("Impossible de configurer la pagination, cette fonctionnalité peut ne pas être disponible dans cette version de Meilisearch");
      }
      return {message: "init successful"};
    } else {
      return {message: "index already created"};
    }
  } catch (error) {
    console.error("Erreur lors de la création de l'index Meilisearch:", error.message);
    console.log("Vérifiez que Meilisearch est bien démarré sur", process.env.MEILI_HOST);
    return {message: "error creating index", error: error.message};
  }
}


/**
 * Retourne l'index permettant d'ajouter ou mettre à jour des posts sur l'instance de meilisearch
 * @return {Object | null}
 */
const getIndex = async () => {
  try {
    const indexes = await client.getIndexes();
    const exist = indexes.results.filter(index => index.uid === "post").length
    return exist ? client.index("post") : null;
  } catch (error) {
    console.error("Erreur lors de la connexion à Meilisearch:", error.message);
    console.log("Vérifiez que Meilisearch est bien démarré sur", process.env.MEILI_HOST);
    // Retourner null en cas d'erreur pour éviter que la tâche cron ne plante
    return null;
  }
}


/**
 * Converti un ensemble de posts au bon format. Cette fonction doit être utilisée sur tous
 * les posts avant qu'ils soient envoyées sur Meilisearch
 *
 * @param entries tableau de posts
 * @return {*}
 */
const preparePosts = (entries) => {
  return entries.map((entry) => {
    let lead = entry.modules.find(function (module) {
      return module.__component === "module.lead";
    });

    if (lead) {
      lead = strapi.config.functions["utils"].removeMarkdown(lead.content);
      lead = strapi.config.functions["utils"].removeHtml(lead);
    }

    let route = "/";

    switch(entry.type) {
      case "podcast":
        let podcastModule = entry.modules.find(function (module) {
          return module.__component === "module.podcast";
        });
        route = `/podcasts/${podcastModule?.podcast?.slug}/${entry.slug}`;
        break;
      case "parcours":
        route = `/parcours-emails/${entry.slug}`;
        break;
      case "webinaire":
        route = `/webinaires/${entry.slug}`;
        break;
      case "formation":
        route = `/article/${entry.slug}`;
        let formationModule = entry.modules.find(function (module) {
          return module.__component === "module.formation";
        });
        if(formationModule?.link) {
          route = formationModule?.link;
        }
        break;
      default:
        route = `/article/${entry.slug}`;
    }

    let body = strapi.config.functions["utils"].removeMarkdown(entry.body);
    body = strapi.config.functions["utils"].removeHtml(body);

    return {
      id: `post-${entry.id}`,
      title: entry.title,
      slug: entry.slug,
      author: entry.author?.fullName || null,
      tags: entry.tags.map((tag) => tag.name),
      topics: entry.topics.map((topic) => topic.name),
      type: entry.type || null,
      lead: lead || null,
      body: body || null,
      date: entry.published_at,
      blog: entry.blog?.slug || null,
      image: entry.image || null,
      route: route,
      cs: entry.isCornerStone
    }
  })
}

/**
 * Envoi l'ensemble des posts déjà présents sur Strapi vers l'index 'post'
 * Cette fonction ne doit être utilisée que lors de la création de l'index.
 * @return {Promise<void>}
 */
async function addExistingDocuments() {
  try {
    const posts = await strapi.services.post.find({_limit: -1});
    const preparedPosts = preparePosts(posts);
    const postCreation = await client.index("post").addDocuments(preparedPosts);
    console.log("existing posts added to meilisearch");
  } catch (error) {
    console.error("Erreur lors de l'ajout des documents à Meilisearch:", error.message);
    console.log("Vérifiez que Meilisearch est bien démarré sur", process.env.MEILI_HOST);
  }
}

module.exports = {
  preparePosts,
  createIndex,
  getIndex
}
