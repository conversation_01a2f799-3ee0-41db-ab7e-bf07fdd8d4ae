import styled from "styled-components";
import { useEffect, useState } from "react";
import client from "api/apollo-client";
import { QUERY_POPUPS } from "api/gql-queries";
import useLocalStorage from "hooks/useLocalStorage";
import { dateDiffInDays } from "utils/date.utils";
import BigCta from "../shared/atoms/Buttons/BigCta";
import Image from "next/image";
import { withRealSrc } from "../../utils/image-utils";
import { device } from "../../styles/device";
import ButtonClose from "../shared/Buttons/ButtonClose";

// Nombre de jours minimums avant d'afficher à nouveau le popup
const DAY_SHOW_PU = 1;

/**
	* Retourne Faux si popup n'existe pas
	* Retourne Vrai si :
	* - utilisateur n'à pas déjà clicker sur le cta
	* - si la date de fin n'est pas passer et que date de début franchi
	* - si la dernière fois que la popup a été fermer avec la croix correspond à plus de 'DAY_SHOW_PU' jours
	*/
function shouldShow(pu) {
  if (!pu) {
    return false;
  }
  let now = new Date();
  const start = pu?.startDate ? new Date(pu?.startDate) : now;
  const end = new Date(pu?.endDate);
  const lview = pu?.lastView && new Date(pu?.lastView);

  return (!pu?.engaged &&
						now < end &&
						now >= start &&
						(!lview || dateDiffInDays(lview, now) >= DAY_SHOW_PU)
  );
}

export default function Popup() {


  const [storedPopup, setStoredPopup] = useLocalStorage("tpsg-pu", null);
  const [showPopup, setShowPopup] = useState(false);

  useEffect(() => {

    async function updatePopup() {

      // Récupération des nouveaux popups
      const allPopups = await client.query({
        query: QUERY_POPUPS,
      }).then(response => {
        return response?.data?.popups
      });

      const now = new Date();
      const newPopup = allPopups?.filter(e => now < new Date(e.endDate))[0];

      if (newPopup?.id && newPopup.id !== storedPopup?.id) {
        // Le popup n'est pas dans le store, donc on l'ajoute
        setStoredPopup({ ...newPopup, lastView: null, engaged: false });
        setShowPopup(shouldShow(storedPopup))
      } else {
        setShowPopup(shouldShow(storedPopup));
      }
    }

    updatePopup();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  function closePopup(engaged = false) {
    setStoredPopup({ ...storedPopup, lastView: new Date(), engaged: engaged });
    setShowPopup(false);
  }

  if (!showPopup) return;

  return (
    <Wrapper>
      <div className={"pu-inner-border"}>
        <div className={"pu-content"}>
          <div className={"pu-image-container"}>
            {storedPopup.image?.url && (
              <Image
                fill
                sizes={"500px"}
                style={imageStyle}
                src={withRealSrc(storedPopup.image)}
                alt={""}
              />
            )}
          </div>
          <div className={"pu-right"}>
            <div className={"pu-text"}>
              <p className={"pu-title"}>{storedPopup.title}</p>
              <p className={"pu-body"}>{storedPopup.body}</p>
            </div>
            <BigCta
              onClickFunction={() => closePopup(true)}
              text={storedPopup.button.name}
              link={storedPopup.button.url}
            />
          </div>
        </div>
      </div>
      <div className={"pu-close"}>
        <ButtonClose handleClick={() => closePopup()}/>
      </div>
    </Wrapper>
  )
};

const imageStyle = {
  objectFit: "contain"
}


const Wrapper = styled.div`
  position: fixed;
  bottom: 0;
  right: 0;
  padding: 24px;
  width: calc(100% - 32px);
  max-height: 100%;
  margin: 16px;
  background: linear-gradient(85.13deg, #081D21 15.79%, #1d2b30 104.33%);
  z-index: 9999;

  .pu-content {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .pu-image-container {
    position: relative;
    width: 100%;
    max-height: 40vh;
    aspect-ratio: 16/9;
    margin-top: 66px;
    /* background-color: var(--c-soft-cream); */
  }

  .pu-inner-border {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background-image: none;
  }

  .pu-right {
    position: relative;
    margin-top: 42px;
    width: 100%;
    display: flex;
    align-self: start;
    flex-direction: column;
    justify-content: space-between;
    color: var(--c-soft-cream);
  }

  .pu-title {
    font-family: Stelvio, sans-serif;
    color: var(--c-soft-cream);
    font-size: 28px;
    line-height: 32px;
    margin: 0;
  }

  .pu-body {
    font-family: Switzer, sans-serif;
    margin-top: 6px;
    font-weight: 400;
    opacity: 0.9;
    color: var(--c-soft-cream);
    font-size: 14px;
  }

  button {
    justify-self: flex-end;
    min-width: 80px;
    min-height: 40px;
    margin-right: 24px;
    margin-top: 42px;
  }

  .pu-close {
    position: absolute;
    top: 24px;
    right: 24px;
  }

  @media ${device.desktop} {
    top: 0;
    left: 0;
    width: calc(100% - 2 * (var(--border-space) - 24px));
    margin: calc(var(--border-space) - 24px);

    .pu-inner-border {
      background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333C41FF' stroke-width='2' stroke-dasharray='4%2c 4' stroke-dashoffset='38' stroke-linecap='butt'/%3e%3c/svg%3e");
    }

    .pu-image-container {
      width: 50%;
      margin-top: 0;
      max-height: inherit;
    }

    .pu-content {
      margin: 0 8%;
      width: 100%;
      flex-direction: row;
      align-items: center;
    }

    .pu-title {
      font-size: 32px;
      line-height: 36px;
    }

    .pu-right {
      width: 50%;
      margin-top: 0;
      margin-left: 42px;
      aspect-ratio: 16/9;
    }

    .pu-body {
      margin-top: 24px;
      font-size: 16px;
    }

    .pu-button {
      margin-top: 0;
    }

    .pu-close {
      top: 42px;
      right: 42px;
    }

  }
`;
