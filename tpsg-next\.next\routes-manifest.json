{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/article/[article]", "regex": "^/article/([^/]+?)(?:/)?$", "routeKeys": {"nxtParticle": "nxtParticle"}, "namedRegex": "^/article/(?<nxtParticle>[^/]+?)(?:/)?$"}, {"page": "/blog/[blog]", "regex": "^/blog/([^/]+?)(?:/)?$", "routeKeys": {"nxtPblog": "nxtPblog"}, "namedRegex": "^/blog/(?<nxtPblog>[^/]+?)(?:/)?$"}, {"page": "/blog/[blog]/filtres", "regex": "^/blog/([^/]+?)/filtres(?:/)?$", "routeKeys": {"nxtPblog": "nxtPblog"}, "namedRegex": "^/blog/(?<nxtPblog>[^/]+?)/filtres(?:/)?$"}, {"page": "/categories/ministere/[ministry]", "regex": "^/categories/ministere/([^/]+?)(?:/)?$", "routeKeys": {"nxtPministry": "nxtPministry"}, "namedRegex": "^/categories/ministere/(?<nxtPministry>[^/]+?)(?:/)?$"}, {"page": "/categories/ministere/[ministry]/ressources", "regex": "^/categories/ministere/([^/]+?)/ressources(?:/)?$", "routeKeys": {"nxtPministry": "nxtPministry"}, "namedRegex": "^/categories/ministere/(?<nxtPministry>[^/]+?)/ressources(?:/)?$"}, {"page": "/categories/vocation/[vocation]", "regex": "^/categories/vocation/([^/]+?)(?:/)?$", "routeKeys": {"nxtPvocation": "nxtPvocation"}, "namedRegex": "^/categories/vocation/(?<nxtPvocation>[^/]+?)(?:/)?$"}, {"page": "/categories/vocation/[vocation]/ressources", "regex": "^/categories/vocation/([^/]+?)/ressources(?:/)?$", "routeKeys": {"nxtPvocation": "nxtPvocation"}, "namedRegex": "^/categories/vocation/(?<nxtPvocation>[^/]+?)/ressources(?:/)?$"}, {"page": "/categories/[topic]", "regex": "^/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPtopic": "nxtPtopic"}, "namedRegex": "^/categories/(?<nxtPtopic>[^/]+?)(?:/)?$"}, {"page": "/categories/[topic]/ressources", "regex": "^/categories/([^/]+?)/ressources(?:/)?$", "routeKeys": {"nxtPtopic": "nxtPtopic"}, "namedRegex": "^/categories/(?<nxtPtopic>[^/]+?)/ressources(?:/)?$"}, {"page": "/formations/[formation]", "regex": "^/formations/([^/]+?)(?:/)?$", "routeKeys": {"nxtPformation": "nxtPformation"}, "namedRegex": "^/formations/(?<nxtPformation>[^/]+?)(?:/)?$"}, {"page": "/parcours-emails/[parcours]", "regex": "^/parcours\\-emails/([^/]+?)(?:/)?$", "routeKeys": {"nxtPparcours": "nxtPparcours"}, "namedRegex": "^/parcours\\-emails/(?<nxtPparcours>[^/]+?)(?:/)?$"}, {"page": "/podcasts/[podcast]", "regex": "^/podcasts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPpodcast": "nxtPpodcast"}, "namedRegex": "^/podcasts/(?<nxtPpodcast>[^/]+?)(?:/)?$"}, {"page": "/podcasts/[podcast]/[episode]", "regex": "^/podcasts/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPpodcast": "nxtPpodcast", "nxtPepisode": "nxtPepisode"}, "namedRegex": "^/podcasts/(?<nxtPpodcast>[^/]+?)/(?<nxtPepisode>[^/]+?)(?:/)?$"}, {"page": "/webinaires/[episode]", "regex": "^/webinaires/([^/]+?)(?:/)?$", "routeKeys": {"nxtPepisode": "nxtPepisode"}, "namedRegex": "^/webinaires/(?<nxtPepisode>[^/]+?)(?:/)?$"}, {"page": "/[page]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPpage": "nxtPpage"}, "namedRegex": "^/(?<nxtPpage>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/categories", "regex": "^/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/categories(?:/)?$"}, {"page": "/formations", "regex": "^/formations(?:/)?$", "routeKeys": {}, "namedRegex": "^/formations(?:/)?$"}, {"page": "/parcours-emails", "regex": "^/parcours\\-emails(?:/)?$", "routeKeys": {}, "namedRegex": "^/parcours\\-emails(?:/)?$"}, {"page": "/podcasts", "regex": "^/podcasts(?:/)?$", "routeKeys": {}, "namedRegex": "^/podcasts(?:/)?$"}, {"page": "/preview", "regex": "^/preview(?:/)?$", "routeKeys": {}, "namedRegex": "^/preview(?:/)?$"}, {"page": "/recherche", "regex": "^/recherche(?:/)?$", "routeKeys": {}, "namedRegex": "^/recherche(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/webinaires", "regex": "^/webinaires(?:/)?$", "routeKeys": {}, "namedRegex": "^/webinaires(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "contentTypeHeader": "text/x-component"}, "rewrites": []}