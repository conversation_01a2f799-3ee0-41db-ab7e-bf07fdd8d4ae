import { gql } from "@apollo/client";

export const POST_TYPE_MODULES = gql`
  fragment postTypeModules on Post {
    modules {
      ... on ComponentModulePodcast {
        podcast {
          slug
          name
        }
      }
      ... on ComponentModuleFormation {
        __typename
        speakers {
          fullName
        }
        link
        youtubeEmbed
      }
    }
  }
`;

export const fragments = {
  CORE_POST_FIELDS: gql`
    fragment CorePostFields on Post {
      id
      title
      body
      slug
      type
      readingTime
      author {
        fullName
        firstName
        lastName
        about
        slug
        picture {
          url
          width
          height
          provider
          alternativeText
        }
      }
      published_at
      image {
        url
        provider
        alternativeText
        caption
        width
        height
      }
      serie {
        id
        name
      }
      topics {
        name
        slug
      }
      blog {
        blogger {
          fullName
          slug
        }
      }
      modules {
        __typename
        ... on ComponentModuleLead {
          content
        }
        ... on ComponentModuleSeo {
          metaDescription
          metaTitle
        }
      }
    }
  `,
};

const FULL_POST_FRAGMENT = gql`
  fragment fullPostFragment on Post {
    id
    title
    slug
    type
    published_at
    body
    author {
      fullName
      picture {
        url
        provider
      }
    }
    image {
      url
      height
      width
      alternativeText
      provider
    }
    topics {
      name
    }
    modules {
      ... on ComponentModuleWebinar {
        __typename
        webinar {
          slug
          name
        }
        embedVideo
        speakers {
          fullName
          firstName
          lastName
          picture {
            url
            provider
            size
          }
        }
      }
      ... on ComponentModulePodcast {
        __typename
        podcast {
          slug
          name
        }
        embedAudio
        embedVideo
      }
      ... on ComponentModuleLead {
        __typename
        content
      }
    }
  }
`;

export const queries = {
  QUERY_TOPIC: gql`
    query MainTopic($slug: String!) {
      topics(where: { slug: $slug }) {
        name
        slug
        id
        postCount
        description
        parent {
          slug
          name
          parent {
            slug
            name
          }
        }
      }
    }
  `,
  QUERY_TOPIC_GROUP: gql`
    query TopicGroup($slug: String!) {
      topicGroups(where: { slug: $slug }) {
        name
        description
        cover {
          formats
        }
        topics {
          id
          postCount
        }
        featured {
          title
          description
          inColumn
          image {
            url
            width
            height
            provider
            caption
            alternativeText
          }
        }
      }
    }
  `,
  QUERY_TOPICS_POSTS: gql`
    query Posts($topicIds: [ID], $offset: Int!) {
      posts(
        limit: 20
        start: $offset
        where: { topics: { id_in: $topicIds } }
      ) {
        title
        slug
        published_at
        image {
          url
          height
          width
          provider
          caption
          alternativeText
        }
        author {
          fullName
        }
        topics {
          slug
        }
        modules {
          __typename
          ... on ComponentModulePodcast {
            podcast {
              name
              slug
            }
          }
        }
        type
      }
    }
  `,
  QUERY_RELATED: gql`
    query GetRelated($id: ID!) {
      relatedPosts(id: $id) {
        section
        score
        origin
        post {
          id
          title
          slug
          type
          published_at
          author {
            fullName
            picture {
              url
              provider
            }
          }
          image {
            url
            height
            width
            alternativeText
            provider
          }
          topics {
            name
          }
          modules {
            __typename
            ... on ComponentModuleWebinar {
              webinar {
                slug
                name
              }
              speakers {
                fullName
                firstName
                lastName
                picture {
                  url
                  provider
                  size
                }
              }
            }
            ... on ComponentModulePodcast {
              podcast {
                slug
                name
              }
            }
            ... on ComponentModuleLead {
              content
            }
          }
        }
      }
    }
  `,
  QUERY_BLOG_POSTS: gql`
    ${FULL_POST_FRAGMENT}
    query BlogPosts($blog: ID!, $limit: Int!, $sort: String!) {
      posts(limit: $limit, where: { blog: $blog }, sort: $sort) {
        ...fullPostFragment
      }
    }
  `,
  QUERY_POSTS: gql`
    ${FULL_POST_FRAGMENT}
    query Posts($limit: Int!, $sort: String!) {
      posts(limit: $limit, sort: $sort) {
        ...fullPostFragment
      }
    }
  `,
};

export const CORE_POST_SET = gql`
  fragment CorePostSet on Post {
    title
    slug
    type
    published_at
    topics {
      name
    }
    image {
      url
      width
      height
      alternativeText
      provider
    }
    author {
      fullName
      picture {
        url
        formats
      }
    }
    modules {
      __typename
      ... on ComponentModulePodcast {
        podcast {
          slug
          name
        }
      }
      ... on ComponentModuleFormation {
        speakers {
          fullName
        }
        link
        youtubeEmbed
      }
    }
  }
`;

export const QUERY_POPUPS = gql`
  query Popups {
    popups(sort: "published_at:desc") {
      id
      title
      body
      image {
        url
        provider
      }
      startDate
      endDate
      published_at
      button {
        name
        url
      }
    }
  }
`;
