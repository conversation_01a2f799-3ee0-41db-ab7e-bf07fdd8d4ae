import styled from "styled-components";
import Link from "next/link"

export default function TopicsHorizontalList({ topics }) {

  return (
    <ListWrapper>
      {topics && topics.filter(topic => topic.postCount > 0).map((topic, key) => (
        <Link key={key} href={`/categories/${topic.slug}`}>
          <Element text={topic.name}><p>{topic.name}</p></Element>
        </Link>
      ))}
      <ListEnd/>
    </ListWrapper>
  )
}

const ListWrapper = styled.div`
  position: relative;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  overflow: hidden;
  padding-top: 1px;
  padding-left: 1px;
  background-color: #F5F5F5;
`;

const Element = styled.a`
  position: relative;
  margin-left: -1px;
  margin-top: -1px;
  perspective: 500px;
  height: 50px;
  box-sizing: border-box;
  padding-left: 42px;
  padding-right: 42px;
  flex-grow: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
  cursor: pointer;
  background-color: white;
  overflow: hidden;

  p {
    color: transparent;
  }

  &:after {
    content: "${props => props.text ? props.text + " →" : "→"}";
    display: flex;
    padding: 4px 0 0 0;
    position: absolute;
    background-color: black;
    color: white;
    transform-origin: 0% 0% 0;
    transform: rotateX(90deg);
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    transition: 600ms cubic-bezier(.74,.41,.17,1.08);

  }
  &:hover:after {
    transform: rotateX(0) translateY(0);
  }
  &:before {
    width: 100%;
    padding: 6px 0 0 0;
    box-sizing: border-box;
    content: "${props => props.text ? props.text : ""}";
    border: 1px solid black;
    display: flex;
    position: absolute;
    background-color: white;
    transform-origin: 0% 0% 0;
    transform: rotateX(0deg);
    top: 0;
    left: 0;
    height: 100%;
    align-items: center;
    justify-content: center;
    transition: 600ms cubic-bezier(.74,.41,.17,1.08);
  }
  &:hover:before {
    transform: rotateX(-90deg);
  }
`

const ListEnd = styled.div`
  position: relative;
  background-color: transparent;
  margin-left: 1px;
  height: 50px;
  flex-grow: 100;
`
