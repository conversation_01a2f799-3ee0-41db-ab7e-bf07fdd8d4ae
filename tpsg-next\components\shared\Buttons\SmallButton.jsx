import styled from "styled-components";

export default function SmallButton({ text, action, theme="light" }) {

  return (
    <StlSmallButton onClick={action} className={theme}>
      {text}
    </StlSmallButton>
  )
}


const StlSmallButton = styled.div`
  padding: 6px 20px;
  font-family: Switzer, sans-serif;
  font-weight: 400;
  border-radius: 30px;
  cursor: pointer;
  letter-spacing: 0.4px;
  font-size: 16px;
  
  &.light {
    color: black;
    background-color: white;
  }
  
  &.dark {
    color: white;
    background-color: var(--c-dark-green);
  }
`;