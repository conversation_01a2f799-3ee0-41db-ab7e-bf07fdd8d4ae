#!/bin/bash
# Script pour tester l'installation de packages npm dans WSL

# Charger NVM
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Afficher les versions
echo "Node.js version:"
node -v
echo "npm version:"
npm -v

# Aller dans le répertoire du projet
cd /mnt/c/rep/TPSG/tpsg-strapi

# Vérifier les permissions
echo "Permissions du répertoire:"
ls -la

# Tester l'installation d'un package
echo "Installation d'un package test..."
npm install --no-save chalk

echo "Test terminé!"
