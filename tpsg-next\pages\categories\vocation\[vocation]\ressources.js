import client from "api/apollo-client";
import styled from "styled-components";
import { gql } from "@apollo/client";
import { device } from "styles/device";
import { modulesAsObj } from "utils/components.utils";
import HorizontalReversePostCard from "components/shared/Card/HorizontalReversePostCard";
import SSRPaginate from "components/shared/pagination/ssr-paginate";
import { FilterTopicsString } from "utils/filterSearchString";
import { useEffect } from "react";
import { useRouter } from "next/router";
import { topicsPostsfetcher } from "utils/fetcher";

const postPerPage = 15; // Number items per page

export default function PageMinistryRessources({ vocation, props, fallback }) {
  const nbHits = fallback?.posts?.totalHits || 0;
  const posts = fallback?.posts?.hits || [];
  let { page } = props;
  const router = useRouter();

  useEffect(() => {
    if (+page === 1) {
      router.replace(
        {
          pathname: `/categories/vocation/${vocation.slug}`,
        },
        undefined,
        { scroll: false }
      );
    } else if (+page > Math.ceil(nbHits / postPerPage)) {
      router.replace(
        {
          pathname: `/categories/vocation/${vocation.slug}/ressources`,
          query: { page: Math.ceil(nbHits / postPerPage) }
        },
        undefined,
        { scroll: false }
      );
    }
  },[vocation.slug, nbHits, page, router]);

  if (!vocation) return null;

  return (
    <Wrapper className={"site-padding"}>
      <h1>{vocation.name}</h1>
      <SectionPosts>
        <p className="label-type">Dernières ressources</p>
        <div className="posts-container ">
          <LeftContent>
            <ul className={"list-container"}>
              {posts.map((post, key) => {
                return (
                  <HorizontalReversePostCard
                    post={post}
                    key={`post-${key}`}
                    options={{
                      showLead: true,
                      showDate: true,
                      showAuthor: true
                    }}
                  />
                );
              })}
            </ul>
          </LeftContent>
        </div>
      </SectionPosts>
      <SSRPaginate
        nbHits={nbHits}
        baseUrl={`/categories/vocation/${vocation.slug}/ressources?page=`}
        currentPage={page}
        options={{
          postPerPage: postPerPage,
        }}
      />
    </Wrapper>
  );
}

export async function getServerSideProps({ query, params }) {

  let { page } = query;

  const vocation = await client
    .query({
      query: QUERY_VOCATION,
      variables: { slug: params.vocation },
    })
    .then((response) => {
      return response.data.topicGroups[0];
    });

  if (!vocation) {
    return {
      notFound: true,
    };
  }

  // Put all topic into an array
  let topics = [
    ...vocation.topics,
  ];

  vocation.children.forEach((ministry) => {
    ministry.topics.forEach((topic) => {
      topics.push(topic);
    });
  });

  // Clear duplicate topics
  topics = topics.filter(
    (topic, index, self) =>
      self.findIndex((topic2) => topic2.id === topic.id) === index
  );

  //fetch posts
  const filterTopicsString = FilterTopicsString(topics);
  if (filterTopicsString.length === 0) {
    return {
      notFound: true,
    }
  }

  const posts = await topicsPostsfetcher({ page: +page }, filterTopicsString, postPerPage);
  
  return {
    props: {
      vocation,
      props: {
        page: +page
      },
      fallback: {
        posts,
      },
    },
  };
}

const QUERY_VOCATION = gql`
  query Vocations($slug: String!) {
    topicGroups(where: { slug: $slug }) {
      slug
      name
      topics {
        id
        name
        slug
        postCount
      }
      children {
        id
        slug
        topics {
          id
          name
          slug
          postCount
        }
      }
    }
  }
`;

const Wrapper = styled.div`
  padding-bottom: 50px;
`;

const SectionPosts = styled.section`
  margin-top: 96px;
  .posts-container {
    display: block;

    @media ${device.desktop} {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
  .label-type{
    font-size: 24px;
  }
`;

const LeftContent = styled.article`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }
`;
