{"routes": [{"method": "GET", "path": "/topics/init", "handler": "topic.init", "config": {"policies": []}}, {"method": "GET", "path": "/topics/init-post-count", "handler": "topic.initPostCount", "config": {"policies": []}}, {"method": "GET", "path": "/topics/children", "handler": "topic.children", "config": {"policies": []}}, {"method": "GET", "path": "/topics", "handler": "topic.find", "config": {"policies": []}}, {"method": "GET", "path": "/topics/count", "handler": "topic.count", "config": {"policies": []}}, {"method": "GET", "path": "/topics/:id", "handler": "topic.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/topics", "handler": "topic.create", "config": {"policies": []}}, {"method": "PUT", "path": "/topics/:id", "handler": "topic.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/topics/:id", "handler": "topic.delete", "config": {"policies": []}}]}