import { NextSeo } from "next-seo";
import { withRealSrc } from "../../../utils/image-utils";
import Link from "next/link";
import { PostTitle, PostType } from "../../../styles/styled-typography";
import RoundedLabel from "../../shared/atoms/rounded-label";
import { CookieWall, RenderMarkdown } from "../../shared";
import VideoPlayer from "../../shared/VideoPlayer";
import SubHeader from "../../shared/SubHeader/SubHeader";
import MediaButton from "../../shared/Buttons/MediaButton";
import styled from "styled-components";
import { device } from "../../../styles/device";
import { dateForHumans } from "../../../utils/date.utils";
import Related from "../../shared/Related";

export default function PodcastLayout({ episode, relatedPosts }) {
  if (!episode) return <></>;

  const date = dateForHumans(episode.published_at);

  const { route } = episode;
  const { lead, podcast: podcastModule, seo } = episode.modules;

  const platforms = podcastModule?.podcast?.platforms;

  const bannerAuthor = {
    fullName: podcastModule?.podcast.name || "unknown",
    picture: podcastModule?.podcast.logoSmall,
    url: `/podcasts/${podcastModule?.podcast.slug}`,
  };

  return (
    <>
      <PageWrapper className={"site-padding"}>
        <NextSeo
          title={seo?.metaTitle || episode.title}
          description={seo?.metaDescription || null}
          openGraph={{
            title: seo?.metaTitle || episode.title,
            description: seo?.metaDescription || null,
            url: `https://toutpoursagloire.com${route}`,
            images: [
              {
                url: withRealSrc(episode.image),
                alt: episode.image?.alternativeText || "",
              },
            ],
          }}
          twitter={{
            site: "@t_p_s_g",
            cardType: "summary_large_image",
          }}
        />
        <header>
          <div className={"header-text-container"}>
            <Link href={`/podcasts/${podcastModule.podcast.slug}`}>
              <PostType>{podcastModule.podcast.name}</PostType>
            </Link>
            <PostTitle>{episode.title}</PostTitle>
            <div>
              {episode.topics &&
                episode.topics.map((x, key) => (
                  <RoundedLabel key={key} text={x.name} />
                ))}
            </div>
          </div>
          <div className={"header-player-cover"}>
            <CookieWall>
              <VideoPlayer
                video={podcastModule.embedVideo}
                image={episode.image}
              />
            </CookieWall>
          </div>
        </header>

        <SubHeader>
          <SubHeader.Text label={"Publié le"} content={date} addClass={""} />
          <SubHeader.Authors
            label={"Podcast"}
            authors={[bannerAuthor] || false}
            addClass={""}
          />
          <SubHeader.Social
            url={`https://toutpoursagloire.com/podcasts/${podcastModule?.podcast.slug}/${episode.slug}`}
            addClass={"mobile-hide_flex"}
          />
        </SubHeader>

        <MainContent>
          <LeftContent>
            {lead?.content && (
              <section>
                <RenderMarkdown content={lead.content} />
              </section>
            )}
            <section>
              <RenderMarkdown content={episode.body} />
            </section>
          </LeftContent>
          <RightContent>
            <div className="right-content-sticky">
              {podcastModule?.embedAudio && (
                <RenderMarkdown
                  content={`<p>${podcastModule.embedAudio}</p>`}
                />
              )}
              <div className="podcast-platform">
                {platforms &&
                  platforms.map((p, k) => (
                    <a key={k} href={p.url} target="_blank" rel="noreferrer">
                      <MediaButton plateform={p.name} />
                    </a>
                  ))}
              </div>
            </div>
          </RightContent>
        </MainContent>
      </PageWrapper>
      <Related items={relatedPosts} />
    </>
  );
}

const PageWrapper = styled.div`
  padding-bottom: 70px;

  header {
    display: flex;
    flex-direction: column;
    margin-bottom: 48px;
  }

  .header-player-cover {
    width: 100%;
    aspect-ratio: 16 / 10;
  }

  .card-label {
    display: none;
    margin: 8px 8px 0 0;
  }

  .video-player {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
  }

  .header-img-container {
    position: relative;
    width: 50%;
    aspect-ratio: 16 / 10;
  }

  @media ${device.tablet} {
    .card-label {
      display: inline-block;
    }

    .header-player-cover {
      margin-top: 48px;
    }
  }
  @media ${device.desktop} {
    header {
      margin-top: 90px;
      margin-bottom: 70px;
      flex-direction: row;
      justify-content: space-between;
    }

    .header-text-container {
      position: relative;
      width: 50%;
      aspect-ratio: 16 / 10;
      padding-top: 24px;
      padding-left: 16px;
    }

    .header-player-cover {
      margin-top: 0;
      width: calc(50% - 32px);
    }
  }
`;

const RightContent = styled.div`
  position: relative;
  width: 100%;

  .right-content-sticky {
    position: sticky;
    top: 50px;
  }

  .podcast-platform {
    display: flex;
    flex-wrap: wrap;
    margin-top: 64px;
    width: 100%;
    gap: 32px;
  }

  @media ${device.tablet} {
    .podcast-platform {
      margin-top: 16px;
      gap: 16px;
    }
  }
  @media ${device.desktop} {
    border-left: 1px solid #dddddd;
    width: 40%;
    padding-left: 32px;
  }
`;

const MainContent = styled.main`
  margin-top: 70px;
  display: block;

  @media ${device.desktop} {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
`;

const LeftContent = styled.article`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media ${device.desktop} {
    width: 50%;
  }
`;
