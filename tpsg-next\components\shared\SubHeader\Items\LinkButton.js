import styled from "styled-components";
import Link from "next/link";

export default function LinkButton({ url, text }) {

  return (
    <Wrapper>
      <Link href={url}>
        {text}
      </Link>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  position: absolute;
  display: flex;
  align-items: center;
  right: 0;
  top: 0;
  height: 100%;

  a {
    margin: 0;
    padding: 12px 18px 6px 18px;
    color: #ffffff;
    font-size: 20px;
    line-height: 24px;
    background-color: var(--brand-color);
    /* No border radius as per <PERSON><PERSON><PERSON>'s feedback */
  }

  &:hover {
    opacity: 0.72;
  }
`;
