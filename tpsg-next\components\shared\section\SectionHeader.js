import styled from "styled-components";
import { SectionTitle } from "styles/styled-typography";
import { device } from "styles/device";

export default function SectionHeader({ title, subTitle, supTitle, light, margin }) {
  return (
    <Wrapper margin={margin} className={"section-header"} light={light}>
      {supTitle && <p>{supTitle}</p>}
      <SectionTitle light={light}>{title}</SectionTitle>
      {subTitle && <p>{subTitle}</p>}
    </Wrapper>
  )
}

const Wrapper = styled.div`
  position: relative;
  margin-left: ${p => p.margin ? "var(--border-space)" : 0};
  margin-bottom: var(--fluid-space-m);

  p {
    font-size: 16px;
    font-weight: 400;
    font-family: <PERSON><PERSON><PERSON>, "Helvetica Neue", Helvetica, sans-serif;
    color: ${p => p.light ? "rgba(250,247,243,0.48)" : "rgba(22,22,22,0.48)"};
    margin-bottom: 8px;
  }
  
  @media ${device.tablet} {
    p {
      font-size: 18px;
    }
  }
  
  @media ${device.desktop} {
    p {
      font-size: 20px;
    }
  }
`;