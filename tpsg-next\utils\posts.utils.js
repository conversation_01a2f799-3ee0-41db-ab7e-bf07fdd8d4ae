import { modulesAsObj } from "./components.utils";
import { removeHtml, removeMarkdown } from "./string.utils";
import { withRealSrc } from "./image-utils";

//TODO: Vérifier les appels à cette fonction.
// OGérer les cas des parcours email.
function getPostRoute(post) {

  if (post.route) {
    // Correction: éviter les doubles slashes
    // Si la route commence déjà par "/", la retourner telle quelle
    if (post.route.startsWith("/")) {
      return post.route;
    }
    // Sinon, ajouter le "/" au début
    return "/" + post.route;
  }

  const type = post.type || "undefined"

  switch (type) {
    case "article":
      return `/article/${post.slug}`
    case "webinaire":
      return `/webinaires/${post.slug}`
    case "podcast":
      const { podcast } = modulesAsObj(post.modules)
      if (!podcast?.podcast) {
        return null
      }
      return `/podcasts/${podcast.podcast.slug}/${post.slug}`
    case "formation":
      let route = post.modules?.formation?.link
      return route ? route : `/article/${post.slug}`
    case "parcours":
      // TODO: Faire des trucs ici parce que ça va pas hein...
      return `/parcours-emails/${post.slug}`
    // const { journey } = journey.link;
    // return journey.link
    default:
      return `/article/${post.slug}`;
  }
}

// TODO: On ne devrait jamais avoir à utiliser le body pour obtenur le lead de l'article.
//  Le Lead est normalement déjà créé de cette façon quand l'article est créé sur Meilisearch.
//  Récupérer le corps des articles sur Strapi pour garnir les listes est trop lourd.
function getPostLead(post) {
  let lead = "";
  if (post.lead && post.lead !== "") {
    // Meilisearch Data
    lead = post.lead;
  } else {
    // Strapi Data
    const postModule = modulesAsObj(post.modules);
    lead = postModule?.lead?.content ? postModule.lead.content : post.body?.slice(0, 255) || "";
    lead = removeHtml(removeMarkdown(lead));
  }
  return lead;
}

function getPostSpeakers(post) {

  const modules = modulesAsObj(post.modules);
  let speakers;

  if (modules?.webinar) {
    speakers = modules.webinar.speakers;
  }
  if (modules?.formation) {
    speakers = modules.formation.speakers;
  }

  if (!speakers) return null;

  let res = {
    pictures: [],
    names: [],
  }

  for (const speaker of speakers) {
    res.pictures.push(withRealSrc(speaker.picture));
    res.names.push(`${speaker.firstName.charAt(0)}. ${speaker.lastName}`)
  }

  return {
    pictures: res.pictures,
    names: speakersNamesToString(res.names)
  }
}

function speakersNamesToString(names) {
  let separator = names.length > 2 ? ", " : " et "
  let res = "";

  for (const [i, name] of names.entries()) {
    if (i > 0) {
      res += separator + name;
      separator = " et ";
    } else {
      res += name
    }
  }
  return res;
}

export {
  getPostRoute,
  getPostLead,
  getPostSpeakers
}
