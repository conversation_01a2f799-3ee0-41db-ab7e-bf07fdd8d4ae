exports.id=4033,exports.ids=[4033],exports.modules={1385:(e,t,o)=>{"use strict";o.d(t,{Z:()=>n});var r=o(9114);let i=new r.ApolloClient({uri:"http://127.0.0.1:1337/graphql",cache:new r.InMemoryCache({addTypename:!1}),defaultOptions:{query:{fetchPolicy:"no-cache"}}}),n=i},7672:(e,t,o)=>{"use strict";o.d(t,{G6:()=>l,Pq:()=>i,iT:()=>s,o$:()=>a});var r=o(9114);r.gql`
  fragment postTypeModules on Post {
    modules {
      ... on ComponentModulePodcast {
        podcast {
          slug
          name
        }
      }
      ... on ComponentModuleFormation {
        __typename
        speakers {
          fullName
        }
        link
        youtubeEmbed
      }
    }
  }
`;let i={CORE_POST_FIELDS:r.gql`
    fragment CorePostFields on Post {
      id
      title
      body
      slug
      type
      readingTime
      author {
        fullName
        firstName
        lastName
        about
        slug
        picture {
          url
          width
          height
          provider
          alternativeText
        }
      }
      published_at
      image {
        url
        provider
        alternativeText
        caption
        width
        height
      }
      serie {
        id
        name
      }
      topics {
        name
        slug
      }
      blog {
        blogger {
          fullName
          slug
        }
      }
      modules {
        __typename
        ... on ComponentModuleLead {
          content
        }
        ... on ComponentModuleSeo {
          metaDescription
          metaTitle
        }
      }
    }
  `},n=r.gql`
  fragment fullPostFragment on Post {
    id
    title
    slug
    type
    published_at
    body
    author {
      fullName
      picture {
        url
        provider
      }
    }
    image {
      url
      height
      width
      alternativeText
      provider
    }
    topics {
      name
    }
    modules {
      ... on ComponentModuleWebinar {
        __typename
        webinar {
          slug
          name
        }
        embedVideo
        speakers {
          fullName
          firstName
          lastName
          picture {
            url
            provider
            size
          }
        }
      }
      ... on ComponentModulePodcast {
        __typename
        podcast {
          slug
          name
        }
        embedAudio
        embedVideo
      }
      ... on ComponentModuleLead {
        __typename
        content
      }
    }
  }
`,a={QUERY_TOPIC:r.gql`
    query MainTopic($slug: String!) {
      topics(where: { slug: $slug }) {
        name
        slug
        id
        postCount
        description
        parent {
          slug
          name
          parent {
            slug
            name
          }
        }
      }
    }
  `,QUERY_TOPIC_GROUP:r.gql`
    query TopicGroup($slug: String!) {
      topicGroups(where: { slug: $slug }) {
        name
        description
        cover {
          formats
        }
        topics {
          id
          postCount
        }
        featured {
          title
          description
          inColumn
          image {
            url
            width
            height
            provider
            caption
            alternativeText
          }
        }
      }
    }
  `,QUERY_TOPICS_POSTS:r.gql`
    query Posts($topicIds: [ID], $offset: Int!) {
      posts(
        limit: 20
        start: $offset
        where: { topics: { id_in: $topicIds } }
      ) {
        title
        slug
        published_at
        image {
          url
          height
          width
          provider
          caption
          alternativeText
        }
        author {
          fullName
        }
        topics {
          slug
        }
        modules {
          __typename
          ... on ComponentModulePodcast {
            podcast {
              name
              slug
            }
          }
        }
        type
      }
    }
  `,QUERY_RELATED:r.gql`
    query GetRelated($id: ID!) {
      relatedPosts(id: $id) {
        section
        score
        origin
        post {
          id
          title
          slug
          type
          published_at
          author {
            fullName
            picture {
              url
              provider
            }
          }
          image {
            url
            height
            width
            alternativeText
            provider
          }
          topics {
            name
          }
          modules {
            __typename
            ... on ComponentModuleWebinar {
              webinar {
                slug
                name
              }
              speakers {
                fullName
                firstName
                lastName
                picture {
                  url
                  provider
                  size
                }
              }
            }
            ... on ComponentModulePodcast {
              podcast {
                slug
                name
              }
            }
            ... on ComponentModuleLead {
              content
            }
          }
        }
      }
    }
  `,QUERY_BLOG_POSTS:r.gql`
    ${n}
    query BlogPosts($blog: ID!, $limit: Int!, $sort: String!) {
      posts(limit: $limit, where: { blog: $blog }, sort: $sort) {
        ...fullPostFragment
      }
    }
  `,QUERY_POSTS:r.gql`
    ${n}
    query Posts($limit: Int!, $sort: String!) {
      posts(limit: $limit, sort: $sort) {
        ...fullPostFragment
      }
    }
  `},s=r.gql`
  fragment CorePostSet on Post {
    title
    slug
    type
    published_at
    topics {
      name
    }
    image {
      url
      width
      height
      alternativeText
      provider
    }
    author {
      fullName
      picture {
        url
        formats
      }
    }
    modules {
      __typename
      ... on ComponentModulePodcast {
        podcast {
          slug
          name
        }
      }
      ... on ComponentModuleFormation {
        speakers {
          fullName
        }
        link
        youtubeEmbed
      }
    }
  }
`,l=r.gql`
  query Popups {
    popups(sort: "published_at:desc") {
      id
      title
      body
      image {
        url
        provider
      }
      startDate
      endDate
      published_at
      button {
        name
        url
      }
    }
  }
`},361:(e,t,o)=>{"use strict";o.d(t,{Z:()=>AnimatedTextButton});var r=o(997),i=o(7518),n=o.n(i),a=o(2570);function AnimatedTextButton({text:e,link:t,onClickFunction:o,theme:i}){function Button(){return r.jsx(s,{className:"animated-text-button",text:e,onClick:o?()=>o():null,light:"light"===i,children:r.jsx("p",{children:e})})}return t?r.jsx(a.Z,{link:t,children:r.jsx(Button,{})}):r.jsx(Button,{})}let s=n().button.withConfig({componentId:"sc-9eb99ee0-0"})`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 24px 0 24px;
  background-color: transparent;
  border: 1px solid ${e=>e.light?"var(--c-soft-cream)":"black"};
  border-radius: 100px;
  font-family: Switzer, serif;
  font-size: 16px;
  cursor: pointer;
  overflow: hidden;
  isolation: isolate;
  line-height: 32px;

  p {
    color: ${e=>e.light?"var(--c-soft-cream)":"black"};
    margin: 0;
    height: 100%;
    transition: transform 350ms ease-out;
  }
  
  &:after {
    position: absolute;
    content: '${e=>e.text}';
    color: ${e=>e.light?"#081921":"var(--c-soft-cream)"};
    background-color: ${e=>e.light?"var(--c-soft-cream)":"black"};
    border-radius: 100px;
    top: 100%;
    height: 100%;
    width: 100%;
    transition: transform 350ms ease-out;
  }
  
  &:hover {
    p {
      transform: translateY(-100%);
    }
    &:after {
      transform: translateY(-100%);
    }
  }
`},73:(e,t,o)=>{"use strict";o.d(t,{Z_:()=>AnimatedArrowButton,Ty:()=>c.Z,Yz:()=>SmallButton});var r=o(997),i=o(7518),n=o.n(i);function SmallButton({text:e,action:t,theme:o="light"}){return r.jsx(a,{onClick:t,className:o,children:e})}let a=n().div.withConfig({componentId:"sc-1dc5b33c-0"})`
  padding: 6px 20px;
  font-family: Switzer, sans-serif;
  font-weight: 400;
  border-radius: 30px;
  cursor: pointer;
  letter-spacing: 0.4px;
  font-size: 16px;
  
  &.light {
    color: black;
    background-color: white;
  }
  
  &.dark {
    color: white;
    background-color: var(--c-dark-green);
  }
`,long_arrow=()=>r.jsx("div",{className:"icn-arrow",children:r.jsx("svg",{width:"24",height:"8",viewBox:"0 0 24 8",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M17.7277 7.80001C18.0477 7.11734 18.3571 6.52001 18.6557 6.00801C18.9757 5.49601 19.2851 5.06935 19.5837 4.72801H0.927734V3.38401H19.5837C19.2851 3.02134 18.9757 2.58401 18.6557 2.07201C18.3571 1.56001 18.0477 0.973345 17.7277 0.312012H18.8477C20.1917 1.86935 21.5997 3.02134 23.0717 3.76801V4.34401C21.5997 5.06934 20.1917 6.22134 18.8477 7.80001H17.7277Z",fill:"#F6F4F3"})})});var s=o(7467);function AnimatedArrowButton({reverse:e,onClickFunction:t,theme:o,disabled:i}){return(0,r.jsxs)(l,{theme:o,reverse:e,disabled:i,onClick:e=>t(e),children:[r.jsx(long_arrow,{}),r.jsx(long_arrow,{})]})}let l=n().button.withConfig({componentId:"sc-e439b357-0"})`
  padding: 0;
  position: relative;
  border-radius: 32px;
  background-color: transparent;
  border: 1px solid ${e=>"dark"===e.theme?"var(--c-soft-cream)":"#161616"};

  color: ${e=>"dark"===e.theme?"var(--c-soft-cream)":"#161616"};

  overflow: hidden;
  box-sizing: content-box;
  height: 32px;
  width: 48px;
  
  opacity: ${e=>e.disabled?.5:1};

  isolation: isolate;

  .icn-arrow {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    border-radius: 32px;
  }

  .icn-arrow:first-child {
    transform: rotate(${e=>e.reverse?"180deg":"0"});

    * {
      fill: ${e=>"dark"===e.theme?"var(--c-soft-cream)":"var(--soft-dark)"};
    }
  }

  .icn-arrow:last-child {
    position: absolute;
    top: 0;
    background-color: ${e=>"dark"===e.theme?"var(--c-soft-cream)":"var(--soft-dark)"};
    transform: rotate(${e=>e.reverse?"180deg":"0"}) translateX(-100%);

    transition: transform 350ms ease-out;

    * {
      fill: ${e=>"dark"===e.theme?"var(--soft-dark)":"var(--c-soft-cream)"};
    }
  }

  &:active {
    border-color: var(--brand-color);
    * {
      fill: var(--brand-color);
    }
  }

  @media ${s.U.desktop} {
    &:active {
      border-color: ${e=>"dark"===e.theme?"var(--c-soft-cream)":"var(--soft-dark)"};

      * {
        fill: black;
      }
    }

    &:hover {
      .icn-arrow:last-child {
        transform: rotate(${e=>e.reverse?"180deg":"0"}) translateX(${e=>e.disabled?"-100%":"0"});
      }

      cursor: ${e=>e.disabled?"default":"pointer"};
    }
  }
`;var c=o(361)},2570:(e,t,o)=>{"use strict";o.d(t,{Z:()=>CondLink});var r=o(997),i=o(7199),n=o(1664),a=o.n(n);function CondLink({link:e,children:t}){return(0,i.tm)(e)?r.jsx("a",{target:"_blank",rel:"noreferrer",href:e,children:t}):r.jsx(a(),{href:e,children:t})}},677:(e,t,o)=>{"use strict";o.d(t,{Z:()=>BigCta});var r=o(997),i=o(7518),n=o.n(i),a=o(1664),s=o.n(a),l=o(7199),c=o(7467);function BigCta({text:e,link:t,onClickFunction:o,theme:i,outline:n=!1,fullWidth:a=!0}){let c=r.jsx(d,{fullWidth:a,text:e,theme:i,textColor:n?"dark"===i?"var(--c-dark-green)":"var(--c-soft-cream)":"dark"===i?"var(--c-soft-cream)":"var(--c-dark-green)",backgroundColor:n?"transparent":"dark"===i?"var(--c-dark-green)":"var(--c-soft-cream)",className:"cta-big",outline:n,children:r.jsx("p",{children:e})});return(0,l.tm)(t)?r.jsx("a",{href:t,rel:"noopener noreferrer",onClick:o||null,target:"_blank",children:c}):r.jsx(s(),{href:t,onClick:o||null,children:c})}let d=n().button.withConfig({componentId:"sc-a61d8ff1-0"})`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 40px;
  border-radius: 100px;
  border: 1px solid ${e=>e.outline?e.textColor:"transparent"};
  width: ${e=>e.fullWidth?"100%":"auto"};

  /* Corrects font smoothing for webkit */
  -webkit-font-smoothing: inherit;
  -moz-osx-font-smoothing: inherit;

  /* Corrects inability to style clickable \`input\` types in iOS */
  -webkit-appearance: none;
  
  background-color: ${e=>e.backgroundColor};
  
  p {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
    font-family: Switzer, sans-serif;
    color: ${e=>e.textColor};
  }
  
  cursor: pointer;
  
  &:hover {
    background-color: var(--c-brand-lighter);
    P {
      color: var(--c-soft-cream);
    }
  }
  
  @media ${c.U.tablet} {
    width: inherit;
  }
`},5322:(e,t,o)=>{"use strict";o.d(t,{Z:()=>__WEBPACK_DEFAULT_EXPORT__});var r=o(997);let __WEBPACK_DEFAULT_EXPORT__=()=>r.jsx("svg",{width:"20",height:"11",viewBox:"0 0 20 11",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{d:"M19 1L10 10L1 1",stroke:"black",strokeWidth:"1.4",strokeLinecap:"round",strokeLinejoin:"round"})})},6068:(e,t,o)=>{"use strict";o.d(t,{F:()=>CoreDataProvider,o:()=>useCoreData});var r=o(997),i=o(6689);let n=(0,i.createContext)(null);function CoreDataProvider({children:e}){let[t,o]=(0,i.useState)({authors:[],blogs:[],topics:[],podcasts:[],topicGroups:[]});return(0,i.useEffect)(()=>{let fetchCoreData=async()=>{let e=await fetch("/api/coredata"),t=await e.json();o(t)};fetchCoreData().catch(console.error)},[]),t&&r.jsx(n.Provider,{value:t,children:e})}function useCoreData(){let e=(0,i.useContext)(n);if(null===e)throw Error("useCoreData must be used within a CoreDataProvider");return e}},4033:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>_app});var r=o(997),i=o(6068),n=o(7518),a=o.n(n),s=o(6689);let l=(0,s.createContext)({}),HeaderProvider=({children:e})=>{let[t,o]=(0,s.useState)({showMenu:!1,dropDownOpen:!1,dropDownKey:"blogs"});return r.jsx(l.Provider,{value:{headerState:t,toggleMenu:(e=!1)=>{let r=document.body;r.classList.toggle("no-scroll",!t.showMenu),e?(o({...t,showMenu:!1}),r.classList.toggle("no-scroll",!1)):o({...t,showMenu:!t.showMenu})},onDropDownButtonClick:e=>{t.dropDownKey!==e?o(t=>({...t,dropDownOpen:!0,dropDownKey:e})):o(o=>({...o,dropDownOpen:!t.dropDownOpen,dropDownKey:e}))},onDropDownClickOutside:()=>{o(e=>({...e,dropDownOpen:!1}))}},children:e})};var c=o(1664),d=o.n(c),p=o(5675),u=o.n(p);function Logo({white:e}){let{toggleMenu:t}=(0,s.useContext)(l);return r.jsx(d(),{href:"/",children:r.jsx(h,{onClick:()=>t(!0),children:r.jsx(u(),{src:!0===e?"/images/tpsg-logo-white.svg":"/images/tpsg-logo.svg",alt:"LOGO_TPSG",sizes:"100px",fill:!0})})})}let h=a().div.withConfig({componentId:"sc-4bff2d1c-0"})`
  position: relative;
  height: 25px;
  width: calc(25px * 2.09);
  margin-left: var(--border-space);
`;var m=o(7467);function MenuButtons({invert:e}){let{headerState:t,toggleMenu:o,onDropDownClickOutside:i}=(0,s.useContext)(l);return(0,r.jsxs)(g,{invert:e,menuOpen:t.showMenu,children:[r.jsx(d(),{href:"/recherche",children:r.jsx(x,{onClick:()=>{o(!0),i()},children:r.jsx(SearchIcon,{})})}),r.jsx(x,{onClick:()=>{o(),i()},children:r.jsx(MenuIcon,{})})]})}let g=a().div.withConfig({componentId:"sc-fcd954af-0"})`
  position: relative;
  z-index: 9999;
  display: flex;
  align-items: center;
  height: 101%;
  
  padding-right: var(--border-space);
  padding-left: 32px;
  flex-direction: row;
  gap: 8px;
  
  //border-bottom: ${e=>e.menuOpen?"1px solid #39474D":"1px solid transparent"};
  transition-delay: 300ms;
  
  svg {
    path {
      fill: ${e=>e.menuOpen||e.invert?"var(--c-cream)":"black"};
      transition: fill 450ms ease-in-out;
    }
  }
  
  .menu-icon {
    display: flex;
    flex-direction: column;
    align-items: end;
    gap: ${e=>e.menuOpen?5:3}px;
    transition: all 250ms ease-in-out;
    width: 32px;
    hr {
      transform-origin: right;
      color: ${e=>e.menuOpen||e.invert?"var(--c-cream)":"black"};
      border: 1.2px solid ${e=>e.menuOpen||e.invert?"var(--c-cream)":"black"};
      margin: 0;
      transition: all 250ms ease-in-out;
    }
    hr:nth-child(1) {
      width: ${e=>e.menuOpen?24:27}px;
      transform: ${e=>e.menuOpen?"rotate(-35deg)":"rotate(0)"};
    }
    hr:nth-child(2){
      width: ${e=>e.menuOpen?0:21}px;
      opacity: ${e=>e.menuOpen?0:1};
    }
    hr:nth-child(3){
      width: 24px;
      transform: ${e=>e.menuOpen?"rotate(35deg)":"rotate(0)"};
    }
  }
  
  @media ${m.U.desktop} {
    //margin-right: 15px;
  }
`,x=a().button.withConfig({componentId:"sc-fcd954af-1"})`
  outline: none;
  border: none;
  display: block;
  background-color: transparent;
  cursor: pointer;
  height: 48px;
  width: 48px;
  
  &:hover {
    * {
      path {
        fill: var(--brand-color);
      }
    }
    * {
      hr {
        color: var(--brand-color);
        border-color: var(--brand-color);
      }
    }
  }
`,SearchIcon=()=>r.jsx("svg",{width:"20",height:"19",viewBox:"0 0 20 19",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M15.2104 8.31546C15.2104 11.7473 12.4283 14.5294 8.99641 14.5294C5.56455 14.5294 2.78247 11.7473 2.78247 8.31546C2.78247 4.8836 5.56455 2.10152 8.99641 2.10152C12.4283 2.10152 15.2104 4.8836 15.2104 8.31546ZM14.0588 14.7844C12.6637 15.8776 10.9062 16.5294 8.99641 16.5294C4.45998 16.5294 0.782471 12.8519 0.782471 8.31546C0.782471 3.77903 4.45998 0.101524 8.99641 0.101524C13.5328 0.101524 17.2104 3.77903 17.2104 8.31546C17.2104 10.2213 16.5612 11.9756 15.4721 13.3693L19.6182 17.5154L18.204 18.9296L14.0588 14.7844Z",fill:"#161616"})}),MenuIcon=()=>(0,r.jsxs)("div",{className:"menu-icon",children:[r.jsx("hr",{}),r.jsx("hr",{}),r.jsx("hr",{})]});function NavigationBar({invert:e}){let t=(0,s.useRef)(null),{headerState:o,onDropDownButtonClick:i,onDropDownClickOutside:n}=(0,s.useContext)(l);return(0,s.useEffect)(()=>{let handleClickOutside=e=>{t.current&&!t.current.contains(e.target)&&o.dropDownOpen&&(n(),e.stopPropagation())};return document&&document.addEventListener("click",handleClickOutside,!0),()=>{document&&document.removeEventListener("click",handleClickOutside,!0)}},[o]),(0,r.jsxs)(f,{menuOpen:o.showMenu,invert:e,children:[r.jsx("hr",{className:"animated-buttons-line"}),r.jsx(Logo,{white:e}),(0,r.jsxs)(b,{invert:e,ref:t,children:[(0,r.jsxs)("ul",{children:[r.jsx("li",{onClick:()=>i("blogs"),children:"Blogs"}),r.jsx("li",{onClick:()=>n(),children:r.jsx(d(),{href:"/categories",children:"Th\xe8mes"})}),r.jsx("li",{onClick:()=>n(),children:r.jsx(d(),{href:"/formations",children:"Formations"})}),r.jsx("li",{onClick:()=>i("podcasts"),children:"Podcasts"}),r.jsx("li",{onClick:()=>n(),children:r.jsx(d(),{href:"/webinaires",children:"Webinaires"})}),r.jsx("li",{onClick:()=>n(),className:"highlight-top-menu",children:r.jsx(d(),{href:"/soutenir",children:"Soutenir"})})]}),r.jsx("div",{className:"nav-v-separator"}),r.jsx(MenuButtons,{invert:e})]}),r.jsx("div",{className:"animated-background"})]})}let f=a().div.withConfig({componentId:"sc-bc67f75e-0"})`
  display: flex;
  width: 100%;
  height: 80px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(0,0,0,0.20);

  .animated-buttons-line {
    position: absolute;
    right: 0;
    top: 70px;
    z-index: 2100;
    border: ${e=>e.menuOpen?"1px solid #1C2E33":"1px solid transparent"};
    width: 100%;
    transition: all 600ms ease-in-out;

    @media ${m.U.desktop} {
      right: 15px;
      width: ${e=>e.menuOpen?"calc(var(--border-space) + 98px)":"0"};
    }
  }
  
  .animated-background {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: var(--blue-dark);
    transform: ${e=>e.menuOpen?"translateY(0)":"translateY(-100%)"};
    transition: transform 450ms ease-in-out;
    z-index: 2100;

    @media ${m.U.desktop} {
      display: none;
    }
  }
  

`,b=a().div.withConfig({componentId:"sc-bc67f75e-1"})`

  position: relative;
  height: 100%;
  display: flex;
  align-items: center;

  font-family: "Switzer", "Helvetica Neue", Helvetica, sans-serif;
  font-size: 16px;
  font-weight: 500;

  ul li {
    list-style: none;
    display: none;
    @media ${m.U.desktop} {
      display: inline;
    }
  }
  li {
    margin-left: 32px;
    cursor: pointer;
    color: ${e=>e.invert?"var(--soft-white)":"black"};
  }
  
  .highlight-top-menu {
    color: ${e=>e.invert?"var(--soft-white)":"var(--brand-color)"};
  }
  
  .nav-v-separator {
    height: 100%;
    margin: 0 0 0 32px;
    width: 0;
    border-left: 1px solid rgba(0,0,0,0.2);
  }
  
`;var v=o(9667);function HeaderDropDown(){let{blogs:e,podcasts:t}=(0,i.o)(),{headerState:o}=(0,s.useContext)(l),n={blogs:e.map(e=>({name:e.blogger.fullName,slug:e.slug,lastName:e.blogger.lastName})).sort((0,v.IQ)("lastName")),podcasts:t.sort((0,v.IQ)("name"))};return n.blogs&&n.podcasts?(0,r.jsxs)(w,{show:o.dropDownOpen,blog:"blogs"===o.dropDownKey,podcast:"podcasts"===o.dropDownKey,podcastHeight:34*n.podcasts.length+80,blogHeight:34*n.blogs.length+80,children:[r.jsx("ul",{className:"dd-blogs",children:n.blogs.map((e,t)=>r.jsx("li",{children:r.jsx(d(),{href:`/blog/${e.slug}`,children:e.name})},t))}),r.jsx("ul",{className:"dd-podcasts",children:n.podcasts.map((e,t)=>r.jsx("li",{children:r.jsx(d(),{href:`/podcasts/${e.slug}`,children:e.name})},t))})]}):null}let w=a().div.withConfig({componentId:"sc-ff9b963e-0"})`
  position: relative;
  margin-top: -1px;
  width: 100%;
  height: ${e=>e.show?e.podcast?e.podcastHeight:e.blogHeight:0}px;
  //background: linear-gradient(56.8deg, #081921 18.37%, rgba(8, 25, 33, 0.8) 100.63%);
  background-color: var(--blue-dark);
  transition: all 350ms ease-out;
  overflow: hidden;
  z-index: 2;

  //backdrop-filter: blur(12px);
  //
  //-webkit-backface-visibility: hidden; // Safari shit here
  //-moz-backface-visibility: hidden; // Safari shit here
  //-webkit-transform: translate3d(0, 0, 0); // Safari shit here
  //-moz-transform: translate3d(0, 0, 0); // Safari shit here

  .dd-podcasts {
    margin: 0 0 0 calc(100% - (445px + var(--border-space)));
    &:before {
      top: ${e=>e.show&&e.podcast?-10:-24}px;
    }
    li {
      opacity: ${e=>e.blog?0:1};
    }
  }

  .dd-blogs {
    margin: 0 0 0 calc(100% - (727px + var(--border-space)));
    &:before {
      top: ${e=>e.show&&e.blog?-10:-24}px;
    }
    li {
      opacity: ${e=>e.blog?1:0};
    }
  }

  ul {
    position: absolute;
    padding: 28px 0;

    &:before {
      position: absolute;
      left: 5px;
      content: "";
      display: block;
      width: 20px;
      height: 20px;
      background-color: var(--soft-white);
      transform: rotate(45deg);
      transition: all 350ms ease-out;
    }
  }

  li {
    font-family: "Switzer", sans-serif;
    font-size: 16px;
    margin-top: 14px;
    color: #F9F1E6;
    list-style: none;
    transition: opacity 250ms ease-out;

    &:hover {
      color: var(--c-brand-lighter);
      cursor: pointer;
    }
  }

`;function Blogs({title:e,data:t}){let{toggleMenu:o}=(0,s.useContext)(l);return r.jsx(k,{"aria-label":e,children:t.sort((0,v.IQ)("lastName")).map((e,t)=>r.jsx("li",{children:r.jsx(d(),{href:e.route,children:r.jsx("p",{onClick:()=>o(),children:e.name})})},t))})}let k=a().ul.withConfig({componentId:"sc-918972e8-0"})`
  grid-row: 2;
  grid-column: 1/3;
  margin-bottom: 64px;
  padding: 0;

  :before {
    display: block;
    position: relative;
    content: attr(aria-label);
    font-family: "Switzer", serif;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 24px;
    color: var(--c-cream-A80);
  }

  li {
    list-style: none;
    margin-top: -1px;
    border-top: 1px solid #1C2E33;
    border-bottom: 1px solid #1C2E33;
  }

  p {
    font-family: "Switzer", serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    letter-spacing: 0.5px;
    color: var(--c-cream);

    &:before {
      margin-right: 24px;
      left: 0;
      top: 0;
      content: "";
      display: inline-block;
      width: 14px;
      height: 14px;
      background-color: var(--c-cream-A20);

      border-radius: 14px;
    }

    &:hover {
      cursor: pointer;

      &:before {
        background-color: #fa7050;
      }
    }
  }

  @media ${m.U.desktop} {
    width: 110%;
    li {
      width: 110%;
    }
  }
`;var j=o(5322);function Vocations_Blogs({title:e,data:t,row:o}){let[i,n]=(0,s.useState)(""),{toggleMenu:a}=(0,s.useContext)(l);return r.jsx(y,{"aria-label":e,row:o,children:t.map((e,t)=>(0,r.jsxs)("li",{className:"parent-list",children:[(0,r.jsxs)(C,{children:[r.jsx(d(),{href:e.route,children:r.jsx("p",{onClick:()=>a(),children:e.name})}),r.jsx("div",{onClick:()=>n(e.name===i?null:e.name),className:"chevron-icon",children:r.jsx(j.Z,{})})]}),r.jsx($,{className:"hidden-list",itemCount:e.children.length,show:i!==`${e.name}`,children:e.children.map((e,t)=>r.jsx("li",{children:r.jsx(C,{children:r.jsx(d(),{href:e.route,children:r.jsx("p",{onClick:()=>a(),children:e.name})})})},"hidden-list-"+t))})]},t))})}let y=a().ul.withConfig({componentId:"sc-421560ba-0"})`
  grid-row: 1;
  grid-column: 1/3;
  margin-bottom: 64px;
  padding: 0;
  list-style: none;

  :before{
    display: block;
    position: relative;
    content:attr(aria-label);
    font-family: "Switzer", serif;
    text-transform: uppercase;
    font-size: 14px;
    letter-spacing: 0.5px;
    font-weight: 400;
    margin-bottom: 24px;
    color: var(--c-cream-A40);
  }

  .li {
    list-style: none;
  }
`,C=a().div.withConfig({componentId:"sc-421560ba-1"})`
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: -1px;
  border-top: 1px solid #1C2E33;
  border-bottom: 1px solid #1C2E33;

  p {
    font-family: "Switzer", "Helvetica Neue", Helvetica, sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: var(--c-cream);

    &:before {
      margin-right: 24px;
      left: 0;
      top: 0;
      content: "";
      display: inline-block;
      width: 14px;
      height: 14px;
      background-color: var(--c-cream-A20);
      border-radius: 14px;
    }

    &:hover {
      cursor: pointer;

      &:before {
        background-color: #fa7051;
      }
    }
  }

  .chevron-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 48px;
    width: 32px;
    border-radius: 26px;

    svg {
      width: 14px;
      height: 14px;

      path {
        stroke: var(--c-cream);
      }
    }

    &:hover {
      cursor: pointer;

      path {
        stroke: #fa7051;
        stroke-width: 2.4;
      }
    }
  }
  
  @media ${m.U.desktop} {
    width: 110%;
  }
`,$=a().ul.withConfig({componentId:"sc-421560ba-2"})`
  height: ${e=>e.show?0:57*e.itemCount}px;
  transition: 350ms ease-in-out;
  padding-left: 38px;
  overflow: hidden;
  list-style: none;
  @media ${m.U.desktop} {
    width: 110%;
  }
`;var N=o(361);let S=[{name:"Articles",route:"/recherche"},{name:"Th\xe8mes",route:"/categories"},{name:"Formations",route:"/formations"},{name:"Parcours e-mails",route:"/parcours-emails"},{name:"Podcasts",route:"/podcasts"},{name:"Webinaires",route:"/webinaires"}],setChildren=e=>e.map(e=>({route:`/categories/${e.type}/${e.slug}`,name:e.name}));function HeaderMenu(){let{headerState:e,toggleMenu:t}=(0,s.useContext)(l),{blogs:o,topicGroups:n}=(0,i.o)(),a=n.filter(e=>e.children?.length>0).map(e=>({route:`/categories/${e.type}/${e.slug}`,name:e.name,children:setChildren(e.children)})),c=o.map(e=>({route:`/blog/${e.slug}`,name:e.blogger.fullName,lastName:e.blogger.lastName}));return(0,r.jsxs)(D,{isOpen:e.showMenu,children:[r.jsx("div",{className:"dark-filter"}),(0,r.jsxs)(P,{isOpen:e.showMenu,children:[r.jsx(_,{children:r.jsx("ul",{children:S.map((e,o)=>r.jsx(d(),{href:e.route,children:r.jsx(O,{onClick:()=>t(),children:e.name})},o))})}),(0,r.jsxs)("div",{className:"menu-buttons",children:[r.jsx(N.Z,{theme:"light",text:"Nous soutenir",link:"/soutenir",onClickFunction:()=>t()}),r.jsx(N.Z,{theme:"light",text:"\xc0 propos",link:"/a-propos",onClickFunction:()=>t()})]})]}),(0,r.jsxs)(z,{isOpen:e.showMenu,children:[r.jsx(Vocations_Blogs,{title:"vocations",data:a}),r.jsx(Blogs,{title:"blogs",data:c})]})]})}let D=a().div.withConfig({componentId:"sc-f4dd8104-0"})`
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  visibility: ${e=>e.isOpen?"visible":"hidden"};
  transition-delay: ${e=>e.isOpen?"0ms":"600ms"};
  z-index: 2000;


  // Hide Scrollbar
  &::-webkit-scrollbar {
    display: none; /* Safari and Chrome */
  }

  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  overflow-y: scroll;

  .dark-filter {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background-color: orangered;
    opacity: ${e=>e.isOpen?"1":"0"};
    transition: opacity 800ms;
  }
`,P=a().div.withConfig({componentId:"sc-f4dd8104-1"})`
  position: relative;
  background-color: var(--blue-dark);
  width: 100vw;
  height: 100vh;
  padding: 126px var(--border-space) 64px 0;
  clip-path: inset(0 0 ${e=>e.isOpen?"0%":"100%"} 0);
  transition: clip-path 600ms cubic-bezier(0.93, 0.03, 0.23, 0.84);

  @media ${m.U.desktop} {
    position: absolute;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-auto-rows: max-content;
    column-gap: 24px;
    padding: 126px 0;
    height: 100vh;
    width: 50%;
    left: 0;
    top: 0;
    clip-path: inset(0 ${e=>e.isOpen?"0%":"100%"} 0 0);

    .menu-buttons {
      grid-column: 1/4;
      margin-top: 64px;
    }
  }

  .menu-buttons {
    position: relative;
    display: flex;
    flex-direction: row-reverse;
    gap: 16px;
  }
`,_=a().div.withConfig({componentId:"sc-f4dd8104-2"})`
  position: relative;
  grid-column: 2/4;
  height: 100%;
  ul {
    padding: 0;
  }
`,O=a().li.withConfig({componentId:"sc-f4dd8104-3"})`
  position: relative;
  width: 100%;
  text-align: right;
  list-style: none;
  font-size: 32px;
  margin-top: 20px;
  font-weight: 400;
  color: var(--c-cream);
  white-space: nowrap;

  @media ${m.U.tablet} {
    font-size: 40px;
    margin-bottom: 16px;
    &:hover {
      color: var(--c-brand-lighter);
      cursor: pointer;
    }
  }
`,z=a().div.withConfig({componentId:"sc-f4dd8104-4"})`
  position: relative;
  width: 100%;
  background-color: var(--blue-dark);
  padding: 64px var(--border-space);
  clip-path: inset(${e=>e.isOpen?"0%":"100%"} 0 0 0);
  transition: clip-path 600ms cubic-bezier(0.93, 0.03, 0.23, 0.84);

  @media ${m.U.desktop} {
    position: absolute;
    right: 0;
    top: 0;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    padding: 126px var(--border-space) 126px 0;
    height: 100vh;
    width: 51%;
    overflow-y: scroll;
    clip-path: inset(0 0 0 ${e=>e.isOpen?"0%":"100%"});
  }
`;var I=o(1163),T=o.n(I);function Header(){let{pathname:e}=(0,I.useRouter)(),[t,o]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{if(e.includes("ressources")){o(!1);return}e.includes("/[page]")||e.includes("[vocation]")||e.includes("[ministry]")||e.includes("[]")?o(!0):o(!1)},[e]),r.jsx(HeaderProvider,{children:(0,r.jsxs)(E,{invert:t,children:[r.jsx(NavigationBar,{invert:t}),r.jsx(HeaderDropDown,{}),r.jsx(HeaderMenu,{})]})})}let E=a().div.withConfig({componentId:"sc-6f0c4967-0"})`
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  z-index: 1400;
  background-color: "transparent";
`;function Footer(){return(0,r.jsxs)(M,{children:[r.jsx("p",{className:"footer-mission",children:"Nous existons pour vous aider \xe0 voir comme Dieu voit pour vivre comme Dieu veut"}),(0,r.jsxs)("div",{className:"footer-links",children:[r.jsx(d(),{href:"/contact",children:"Contact"}),r.jsx(d(),{href:"/soutenir",children:"Nous soutenir"}),r.jsx("hr",{}),r.jsx(d(),{href:"/a-propos",children:"\xc0 propos"}),r.jsx(d(),{href:"/ce-que-nous-croyons",children:"Ce que nous croyons"}),r.jsx(d(),{href:"/equipe-tpsg",children:"\xc9quipe"}),r.jsx("hr",{}),r.jsx(d(),{href:"/formations#evenements-tpsg",children:"Camp TPSG"}),r.jsx(d(),{href:"https://toutpoursagloire.myspreadshop.fr/",children:"Shop"}),r.jsx("hr",{}),r.jsx(d(),{href:"/mentions-legales",children:"Mentions l\xe9gales"})]})]})}let M=a().footer.withConfig({componentId:"sc-3177e1d6-0"})`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 16px;

  padding: 40px var(--border-space) 120px var(--border-space);

  background-color: var(--c-dark-green);
  color: #E8E8E5;

  .footer-mission {
    grid-column: 1 / span 2;
    grid-row: 1;
    font-size: 36px;
    line-height: 36px;
    margin: 0 0 96px 0;
  }

  a {
    display: block;
    font-family: sans-serif, "Helvetica Neue", Helvetica, Arial;
    margin: 0 0 8px 0;
    line-height: 24px;
    opacity: 0.8;
    font-weight: 400;
  }

  a:hover {
    text-decoration: underline;
  }

  hr {
    border-color: transparent;
    height: 24px;
  }

  @media ${m.U.tablet} {
    grid-template-columns: repeat(8, 1fr);
    padding-top: 80px;
    padding-bottom: 80px;
    .footer-mission {
      grid-column: 1 / span 6;
      grid-row: 1;
      font-size: 48px;
      line-height: 48px;
      margin: 0;
    }

    .footer-links {
      grid-column: 11 / span 2;
    }
  }

  @media ${m.U.desktop} {
    grid-template-columns: repeat(12, 1fr);
    padding-top: 80px;
    padding-bottom: 80px;
    .footer-mission {
      grid-column: 1 / span 6;
      font-size: 48px;
      line-height: 48px;
    }

    .footer-links {
      grid-column: 11 / span 2;
    }
  }
`;var U=o(1385),L=o(7672),F=o(6382),B=o(677),q=o(4130);function ButtonClose({handleClick:e}){return r.jsx(A,{onClick:e,children:r.jsx("p",{children:"+"})})}let A=a().div.withConfig({componentId:"sc-44a20ba0-0"})`
		
		height: 28px;
		border-radius: 16px;
		padding: 1px 22px 0 18px;
		background-color: var(--c-soft-cream);
		
		cursor: pointer;
		
		p {
				margin: 0;
				font-size: 24px;
				line-height: 32px;
				transform: rotate(45deg);
				color: black;
		}
		
		@media ${m.U.desktop} {
				&:hover {
						background-color: var(--c-brand-lighter);
						p {
								color: var(--c-soft-cream);
						}
				}
		}
`;function Popup(){let[e,t]=function(e,t){let[o,r]=(0,s.useState)(()=>{if("undefined"==typeof localStorage)return t;try{let o=localStorage.getItem(e);return o?JSON.parse(o):t}catch(e){return console.log(e),t}});return[o,t=>{try{let i=t instanceof Function?t(o):t;r(i),"undefined"!=typeof localStorage&&localStorage.setItem(e,JSON.stringify(i))}catch(e){console.log(e)}}]}("tpsg-pu",null),[o,i]=(0,s.useState)(!1);function closePopup(o=!1){t({...e,lastView:new Date,engaged:o}),i(!1)}if((0,s.useEffect)(()=>{(async function(){let o=await U.Z.query({query:L.G6}).then(e=>e?.data?.popups),r=new Date,n=o?.filter(e=>r<new Date(e.endDate))[0];n?.id&&n.id!==e?.id&&t({...n,lastView:null,engaged:!1}),i(function(e){if(!e)return!1;let t=new Date,o=e?.startDate?new Date(e?.startDate):t,r=new Date(e?.endDate),i=e?.lastView&&new Date(e?.lastView);return!e?.engaged&&t<r&&t>=o&&(!i||(0,F.LG)(i,t)>=1)}(e))})()},[]),o)return(0,r.jsxs)(R,{children:[r.jsx("div",{className:"pu-inner-border",children:(0,r.jsxs)("div",{className:"pu-content",children:[r.jsx("div",{className:"pu-image-container",children:e.image?.url&&r.jsx(u(),{fill:!0,sizes:"500px",style:H,src:(0,q.k)(e.image),alt:""})}),(0,r.jsxs)("div",{className:"pu-right",children:[(0,r.jsxs)("div",{className:"pu-text",children:[r.jsx("p",{className:"pu-title",children:e.title}),r.jsx("p",{className:"pu-body",children:e.body})]}),r.jsx(B.Z,{onClickFunction:()=>closePopup(!0),text:e.button.name,link:e.button.url})]})]})}),r.jsx("div",{className:"pu-close",children:r.jsx(ButtonClose,{handleClick:()=>closePopup()})})]})}let H={objectFit:"contain"},R=a().div.withConfig({componentId:"sc-c883c5c9-0"})`
  position: fixed;
  bottom: 0;
  right: 0;
  padding: 24px;
  width: calc(100% - 32px);
  max-height: 100%;
  margin: 16px;
  background: linear-gradient(85.13deg, #081D21 15.79%, #1d2b30 104.33%);
  z-index: 9999;

  .pu-content {
    position: relative;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .pu-image-container {
    position: relative;
    width: 100%;
    max-height: 40vh;
    aspect-ratio: 16/9;
    margin-top: 66px;
    /* background-color: var(--c-soft-cream); */
  }

  .pu-inner-border {
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    background-image: none;
  }

  .pu-right {
    position: relative;
    margin-top: 42px;
    width: 100%;
    display: flex;
    align-self: start;
    flex-direction: column;
    justify-content: space-between;
    color: var(--c-soft-cream);
  }

  .pu-title {
    font-family: Stelvio, sans-serif;
    color: var(--c-soft-cream);
    font-size: 28px;
    line-height: 32px;
    margin: 0;
  }

  .pu-body {
    font-family: Switzer, sans-serif;
    margin-top: 6px;
    font-weight: 400;
    opacity: 0.9;
    color: var(--c-soft-cream);
    font-size: 14px;
  }

  button {
    justify-self: flex-end;
    min-width: 80px;
    min-height: 40px;
    margin-right: 24px;
    margin-top: 42px;
  }

  .pu-close {
    position: absolute;
    top: 24px;
    right: 24px;
  }

  @media ${m.U.desktop} {
    top: 0;
    left: 0;
    width: calc(100% - 2 * (var(--border-space) - 24px));
    margin: calc(var(--border-space) - 24px);

    .pu-inner-border {
      background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333C41FF' stroke-width='2' stroke-dasharray='4%2c 4' stroke-dashoffset='38' stroke-linecap='butt'/%3e%3c/svg%3e");
    }

    .pu-image-container {
      width: 50%;
      margin-top: 0;
      max-height: inherit;
    }

    .pu-content {
      margin: 0 8%;
      width: 100%;
      flex-direction: row;
      align-items: center;
    }

    .pu-title {
      font-size: 32px;
      line-height: 36px;
    }

    .pu-right {
      width: 50%;
      margin-top: 0;
      margin-left: 42px;
      aspect-ratio: 16/9;
    }

    .pu-body {
      margin-top: 24px;
      font-size: 16px;
    }

    .pu-button {
      margin-top: 0;
    }

    .pu-close {
      top: 42px;
      right: 42px;
    }

  }
`;var Y=o(5515);function Toggle({isChecked:e,formKey:t,handleValueChange:o,label:i}){let[n,a]=(0,s.useState)(e);return(0,r.jsxs)(V,{checked:n,children:[r.jsx("div",{className:"toggle-button",onClick:()=>void(o(t,!n),a(!n)),children:r.jsx("div",{className:"inner-dot"})}),r.jsx("p",{className:"toggle-label",children:i})]})}let V=a().div.withConfig({componentId:"sc-d9158d9d-0"})`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;

  .toggle-button {
    height: 22px;
    width: 38px;
    border: 1.5px solid var(--c-dark-green);
    background-color: ${e=>e.checked?"var(--c-dark-green)":"transparent"};
    border-radius: 21px;
    cursor: pointer;
  }
  
  .inner-dot {
    height: 15px;
    width: 15px;
    background-color: ${e=>e.checked?"white":"var(--c-dark-green)"};
    border-radius: 15px;
    margin-top: 2px;
    margin-left: 2px;
    transform: translateX(${e=>e.checked?"100%":"0%"});
    transition: all 350ms;
  }
  
  .toggle-label {
    margin: 0;
    padding-top: 4px;
    font-size: 16px;
    line-height: 21px;
    font-weight: 500;
    letter-spacing: 0.4px;
  }
`;var Z=o(73);let useFormData=e=>{let[t,o]=(0,s.useState)({...e});return[t,(e,r)=>{o({...t,[e]:r})},o]};function CookieBanner(){let e=(0,I.useRouter)(),[t,o]=(0,Y.useCookies)(["preferences"]),[i,n]=(0,s.useState)(!1),[a,l]=useFormData({essentials:!0,analytics:!0,medias:!0,set:!0});return((0,s.useEffect)(()=>{n(!t.preferences?.set)},[t]),i&&"/cookies"!==e.asPath)?(console.log(e.asPath),r.jsx(G,{children:(0,r.jsxs)(K,{children:[(0,r.jsxs)("div",{className:"cb-top",children:[r.jsx("p",{className:"cb-title",children:"Param\xe8tre des cookies"}),(0,r.jsxs)("div",{className:"cb-entries",children:[(0,r.jsxs)("div",{className:"cb-entry",children:[r.jsx(Toggle,{isChecked:!0,formKey:"essentials",handleValueChange:l,label:"N\xe9cessaires"}),(0,r.jsxs)("p",{className:"cb-entry-desc",children:["Certains cookies sont n\xe9cessaires au fonctionnement minimal du site toutpoursagloire.com comme :",(0,r.jsxs)("ul",{children:[(0,r.jsxs)("li",{children:["Vos pr\xe9f\xe9rences d’acceptation ou de rejet des cookies"," "]}),r.jsx("li",{children:"\xc9ventuellement des informations techniques pour le bon affichage des pages"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(Toggle,{isChecked:!0,formKey:"analytics",handleValueChange:l,label:"Statistiques"}),r.jsx("p",{className:"cb-entry-desc",children:"Ces cookies sont utiles pour nous permettre de vous fournir des ressources (articles, podcasts, webinaires, …) toujours plus adapt\xe9es et pertinentes. Nous utilisons Google Analytics pour \xe7a."})]}),(0,r.jsxs)("div",{children:[r.jsx(Toggle,{isChecked:!0,formKey:"medias",handleValueChange:l,label:"Provenance de tiers"}),(0,r.jsxs)("p",{className:"cb-entry-desc",children:["Il s’agit du reste des cookies venant de services externes \xe0 toutpoursagloire.com mais pour autant utiles pour une exp\xe9rience compl\xe8te comme :",(0,r.jsxs)("ul",{children:[r.jsx("li",{children:"Les services multim\xe9dias pour les podcast et webinaires (YouTube, Spotify, SoundCloud…)"}),r.jsx("li",{children:"Les services de communication pour les formulaires mails (ConvertKit)"}),r.jsx("li",{children:"Les services de remont\xe9e de bug pour l'am\xe9lioration continue du site (Marker.io)"})]})]})]})]})]}),(0,r.jsxs)("div",{className:"cb-bottom",children:[r.jsx("p",{className:"cb-details-btn",children:r.jsx(d(),{href:"/cookies",children:"Voir les d\xe9tails"})}),r.jsx(Z.Yz,{text:"Valider",theme:"dark",action:()=>void(o("preferences",a,{sameSite:"strict",path:"/",expires:new Date(Date.now()+31536e6)}),T().reload())})]})]})})):r.jsx(r.Fragment,{})}let G=a().div.withConfig({componentId:"sc-dbd6c5f7-0"})`
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(8, 29, 33, 0.96);
`,K=a().div.withConfig({componentId:"sc-dbd6c5f7-1"})`
  position: absolute;
  bottom: 0;
  left: 0;
  overflow: scroll;
  max-width: 500px;
  max-height: 66%;
  box-shadow: 0 4px 16px rgba(23, 22, 22, 0.2);
  background-color: var(--soft-white);

  p {
    font-family: Switzer, sans-serif;
    font-size: 14px;
    ul {
      margin-top: 8px;
      padding-left: 20px;
    }
  }

  .cb-top {
    padding: 32px;
  }

  .cb-title {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 18px;
    font-family: Switzer, sans-serif;
    font-weight: 600;
  }

  .cb-entries {
    margin-top: 32px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .cb-bottom {
    position: sticky;
    bottom: 0;
    width: 100%;
    padding: 32px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background-color: var(--c-soft-cream);
  }

  .cb-details-btn {
    margin: 0;
    color: #242424;
    text-decoration: underline;
    font-family: Switzer, sans-serif;
    font-weight: 500;
    letter-spacing: 0.4px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      color: var(--brand-color);
    }
  }

  @media ${m.U.tablet} {
    min-width: 500px;
  }

  @media ${m.U.tablet} {
    position: relative;
    max-height: 100%;
  }
`;function Layout({children:e}){return(0,r.jsxs)(i.F,{children:[r.jsx(Header,{}),e,r.jsx(Footer,{}),r.jsx(CookieBanner,{}),r.jsx(Popup,{})]})}o(6764);var Q=o(968),W=o.n(Q),X=o(4298),J=o.n(X);let _app=function({Component:e,pageProps:t}){let getParams=(e,t)=>{let o=t.reduce((t,o)=>(e[o]&&""!==e[o]&&"page"===o&&1!==Number(e.page)&&t.push(`${o}=${e[o]}`),t),[]);return o.length?`?${o.join("&")}`:""};return r.jsx(Y.CookiesProvider,{children:(0,r.jsxs)(Layout,{children:[(0,r.jsxs)(W(),{children:[r.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0"}),r.jsx("link",{rel:"canonical",href:(()=>{let{pathname:e,query:t}=(0,I.useRouter)(),o="";switch(e){case"/article/[article]":o=`/article/${t.article}`;break;case"/blog/[blog]":o=`/blog/${t.blog}`;break;case"/blog/[blog]/filtres":o=`/blog/${t.blog}/filtres${getParams(t,["topic","type","page"])}`;break;case"/categories":o="/categories";break;case"/categories/[topic]":o=`/categories/${t.topic}`;break;case"/categories/[topic]/ressources":o=`/categories/${t.topic}/ressources${getParams(t,["page"])}`;break;case"/categories/ministere/[ministry]":o=`/categories/ministere/${t.ministry}`;break;case"/categories/ministere/[ministry]/ressources":o=`/categories/ministere/${t.ministry}/ressources${getParams(t,["page"])}`;break;case"/categories/vocation/[vocation]":o=`/categories/vocation/${t.vocation}`;break;case"/categories/vocation/[vocation]/ressources":o=`/categories/vocation/${t.vocation}/ressources${getParams(t,["page"])}`;break;case"/formations":o="/formations";break;case"/formations/[formation]":o=`/formations/${t.formation}`;break;case"/parcours-emails":o="/parcours-emails";break;case"/parcours-emails/[parcours]":o=`/parcours-emails/${t.parcours}`;break;case"/podcasts":o="/podcasts";break;case"/podcasts/[podcast]":o=`/podcasts/${t.podcast}${getParams(t,["page"])}`;break;case"/podcasts/[podcast]/[episode]":o=`/podcasts/${t.podcast}/${t.episode}`;break;case"/webinaires":o=`/webinaires${getParams(t,["page"])}`;break;case"/webinaires/[episode]":o=`/webinaires/${t.episode}${getParams(t,["page"])}`;break;case"/[page]":o=`/${t.page}`;break;default:o=""}return new URL(o,"https://toutpoursagloire.com").href})()}),r.jsx("link",{rel:"icon",href:"/favicon.png"})]}),r.jsx(e,{...t}),function(){let[e,t]=(0,Y.useCookies)(["preferences"]);return(0,r.jsxs)(r.Fragment,{children:[e.preferences?.analytics&&(0,r.jsxs)(r.Fragment,{children:[r.jsx(J(),{async:!0,src:"https://www.googletagmanager.com/gtag/js?id=",id:"ga-url-script"}),r.jsx(J(),{id:"ga-analytics-script",children:`window.dataLayer = window.dataLayer || [];
              function gtag(){ dataLayer.push(arguments); }
              gtag("js", new Date());
              gtag("config", "");
            `})]}),e.preferences?.medias&&r.jsx(r.Fragment,{children:r.jsx(J(),{id:"marker-io-config",children:`window.markerConfig = {
                project: "647f03e6a572cb7307800759",
                source: "snippet" };`})})]})}()]})})}},7467:(e,t,o)=>{"use strict";o.d(t,{U:()=>i});let r={mini:"320px",tablet:"744px",desktop:"1024px",desktopXL:"1441px"},i={mini:`(max-width: ${r.mini})`,tablet:`(min-width: ${r.tablet})`,desktop:`(min-width: ${r.desktop})`,desktopXL:`(min-width: ${r.desktopXL})`}},6382:(e,t,o)=>{"use strict";o.d(t,{LG:()=>dateDiffInDays,S$:()=>dateForHumans,xO:()=>hour});var r=o(1635),i=o.n(r);function dateForHumans(e){return i()(e).locale("fr").format("DD MMM YYYY")}function hour(e){return i()(e).locale("fr").format("HH:mm")}function dateDiffInDays(e,t){let o=toUTC(e),r=toUTC(t);return Math.floor(Math.abs(r-o)/864e5)}function toUTC(e){return Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds())}o(7688),i().locale("fr")},4130:(e,t,o)=>{"use strict";function withRealSrc(e){return e?.provider==="local"?"http://localhost:1337"+e.url:e?.url?e.url:void 0}o.d(t,{k:()=>withRealSrc})},9667:(e,t,o)=>{"use strict";function topicSort(e){let t=[],o=e.map(e=>({...e,children:null}));return o.filter(e=>null==e.parent||e.id===o[0].id).map(e=>{o.filter(t=>t.parent?.id===e.id).map(t=>{o.filter(e=>e.parent?.id===t.id).map(e=>{t.children=t.children?t.children:[],t.children.push(e)}),t.children&&t.children.sort(dynamicSort("name")),e.children=e.children?e.children:[],e.children.push(t)}),e.children&&e.children.sort(dynamicSort("name")),t.push(e)}),t}o.d(t,{Ay:()=>topicSort,IQ:()=>dynamicSort,df:()=>sortByDate});let dynamicSort=e=>{let t=1;return"-"===e[0]&&(t=-1,e=e.substr(1)),function(o,r){return(o[e]<r[e]?-1:o[e]>r[e]?1:0)*t}};function sortByDate(e,t){return new Date(t.published_at)-new Date(e.published_at)}},7199:(e,t,o)=>{"use strict";function slugify(e){e=(e=e.replace(/^\s+|\s+$/g,"")).toLowerCase();let t="\xc1\xc4\xc2\xc0\xc3\xc5Č\xc7ĆĎ\xc9Ě\xcb\xc8\xcaẼĔȆ\xcd\xcc\xce\xcfŇ\xd1\xd3\xd6\xd2\xd4\xd5\xd8ŘŔŠŤ\xdaŮ\xdc\xd9\xdb\xddŸŽ\xe1\xe4\xe2\xe0\xe3\xe5č\xe7ćď\xe9ě\xeb\xe8\xeaẽĕȇ\xed\xec\xee\xefň\xf1\xf3\xf6\xf2\xf4\xf5\xf8\xf0řŕšť\xfaů\xfc\xf9\xfb\xfd\xffž\xfe\xdeĐđ\xdf\xc6a\xb7/_,:;";for(let o=0,r=t.length;o<r;o++)e=e.replace(RegExp(t.charAt(o),"g"),"AAAAAACCCDEEEEEEEEIIIINNOOOOOORRSTUUUUUYYZaaaaaacccdeeeeeeeeiiiinnooooooorrstuuuuuyyzbBDdBAa------".charAt(o));return e=e.replace(/[^a-z0-9 -]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-")}function removeMarkdown(e){return e.replace(/(?:_|[*#])|\[(.*?)\]\(.*?\)/gm,"$1")}function removeHtml(e){return e.replace(/`|<[^>]*(>|…)/gm,"")}function isLinkExternal(e){return e.includes(".")}o.d(t,{Gq:()=>removeHtml,Kd:()=>removeMarkdown,gc:()=>getYouTubeVideoIdFromUrl,k5:()=>removeLastBackSlash,lV:()=>slugify,tm:()=>isLinkExternal});let getYouTubeVideoIdFromUrl=e=>void 0!==(e=e.split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/))[2]?e[2].split(/[^0-9a-z_\-]/i)[0]:e[0];function removeLastBackSlash(e){return e.replace(/^\\/gm,"")}},6764:()=>{}};