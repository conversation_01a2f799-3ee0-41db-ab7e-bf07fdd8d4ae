import React, {useState} from 'react';
import PropTypes from 'prop-types';
import {isEmpty} from 'lodash';
import {Label, InputDescription, InputErrors} from 'strapi-helper-plugin';
import OutlineEditor from '../OutlineEditor';
import styled from 'styled-components';

import { faExpandAlt, faTimesCircle } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

const Wysiwyg = ({
     inputDescription,
     errors,
     label,
     name,
     noErrorsDescription,
     onChange,
     value
}) => {


  let spacer = !isEmpty(inputDescription) ? <div style={{height: '.4rem'}}/> : <div/>;

  if (!noErrorsDescription && !isEmpty(errors)) {
    spacer = <div/>;
  }

  const [fullScreen, setFullScreen] = useState(false);

  const handleFullScreen = () => {
      setFullScreen(!fullScreen);
  }


  return (
    <div
        style={{
          marginBottom: '1.6rem',
          fontSize: '1.3rem',
          fontFamily: 'Lato',
        }}>
      <Label htmlFor={name} message={label} style={{marginBottom: 10}}/>

      <FullScreenButton
        fullScreen={fullScreen}
        onClick={() => handleFullScreen()}>
        { fullScreen?
          <FontAwesomeIcon icon={faTimesCircle} size="2x" color={'#000'} />
          :
          <FontAwesomeIcon icon={faExpandAlt}/>
        }
      </FullScreenButton>

      <OutlineEditor fullScreen={fullScreen} name={name} onChange={onChange} value={value}/>

      <InputDescription
        message={inputDescription}
        style={!isEmpty(inputDescription) ? {marginTop: '1.4rem'} : {}}
      />
      <InputErrors errors={(!noErrorsDescription && errors) || []} name={name}/>
      {spacer}
    </div>
  );
};

const FullScreenButton = styled.span`

  width: 22px;
  height: 22px;
  border-radius: 30px;
  margin-left: 16px;
  background-color: white;
 
  &:hover {
    cursor: pointer;
  }
  
  ${props => (props.fullScreen && '' +
    'position: fixed;' +
    'z-index: 1060;' +
    'right: 64px;' +
    'top: 48px;' +
    'display: flex;' +
    'justify-content: center;' +
    'align-items: center;'
  )};
`;

Wysiwyg.defaultProps = {
  errors: [],
  inputDescription: null,
  label: '',
  noErrorsDescription: false,
  value: '',
};

Wysiwyg.propTypes = {
  errors: PropTypes.array,
  inputDescription: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.func,
    PropTypes.shape({
      id: PropTypes.string,
      params: PropTypes.object,
    }),
  ]),
  label: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.func,
    PropTypes.shape({
      id: PropTypes.string,
      params: PropTypes.object,
    }),
  ]),
  name: PropTypes.string.isRequired,
  noErrorsDescription: PropTypes.bool,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.string,
};

export default Wysiwyg;