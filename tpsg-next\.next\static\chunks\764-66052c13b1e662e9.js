(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[764],{669:function(e,t,n){"use strict";var r=n(7294);function _extends(){return(_extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var i=["html","dangerouslySetInnerHTML"];t.Z=function(e){var t=e.html,n=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,i),o=(0,r.useRef)(null);return(0,r.useEffect)(function(){if(!t||!o.current)throw Error("html prop cant't be null");var e=document.createRange().createContextualFragment(t);o.current.innerHTML="",o.current.appendChild(e)},[t,o]),r.createElement("div",_extends({},n,{ref:o}))}},5125:function(e){"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,isArray=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},isPlainObject=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),o=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!o)return!1;for(r in e);return void 0===r||t.call(e,r)},setProperty=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},getProperty=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(i)return i(e,n).value}return e[n]};e.exports=function extend(){var e,t,n,r,i,o,a=arguments[0],s=1,l=arguments.length,c=!1;for("boolean"==typeof a&&(c=a,a=arguments[1]||{},s=2),(null==a||"object"!=typeof a&&"function"!=typeof a)&&(a={});s<l;++s)if(e=arguments[s],null!=e)for(t in e)n=getProperty(a,t),a!==(r=getProperty(e,t))&&(c&&r&&(isPlainObject(r)||(i=isArray(r)))?(i?(i=!1,o=n&&isArray(n)?n:[]):o=n&&isPlainObject(n)?n:{},setProperty(a,{name:t,newValue:extend(c,o,r)})):void 0!==r&&setProperty(a,{name:t,newValue:r}));return a}},2954:function(e){var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,a=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,s=/^[;\s]*/,l=/^\s+|\s+$/g;function trim(e){return e?e.replace(l,""):""}e.exports=function(e,l){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];l=l||{};var c=1,u=1;function updatePosition(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");u=~r?e.length-r:u+e.length}function position(){var e={line:c,column:u};return function(t){return t.position=new Position(e),match(r),t}}function Position(e){this.start=e,this.end={line:c,column:u},this.source=l.source}Position.prototype.content=e;var h=[];function error(t){var n=Error(l.source+":"+c+":"+u+": "+t);if(n.reason=t,n.filename=l.source,n.line=c,n.column=u,n.source=e,l.silent)h.push(n);else throw n}function match(t){var n=t.exec(e);if(n){var r=n[0];return updatePosition(r),e=e.slice(r.length),n}}function comments(e){var t;for(e=e||[];t=comment();)!1!==t&&e.push(t);return e}function comment(){var t=position();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return error("End of comment missing");var r=e.slice(2,n-2);return u+=2,updatePosition(r),e=e.slice(n),u+=2,t({type:"comment",comment:r})}}return match(r),function(){var e,n=[];for(comments(n);e=function(){var e=position(),n=match(i);if(n){if(comment(),!match(o))return error("property missing ':'");var r=match(a),l=e({type:"declaration",property:trim(n[0].replace(t,"")),value:r?trim(r[0].replace(t,"")):""});return match(s),l}}();)!1!==e&&(n.push(e),comments(n));return n}()}},9221:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),o="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;o?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(2954))},8139:function(e){var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,a=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,s=/^[;\s]*/,l=/^\s+|\s+$/g;function trim(e){return e?e.replace(l,""):""}e.exports=function(e,l){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];l=l||{};var c=1,u=1;function updatePosition(e){var t=e.match(n);t&&(c+=t.length);var r=e.lastIndexOf("\n");u=~r?e.length-r:u+e.length}function position(){var e={line:c,column:u};return function(t){return t.position=new Position(e),match(r),t}}function Position(e){this.start=e,this.end={line:c,column:u},this.source=l.source}Position.prototype.content=e;var h=[];function error(t){var n=Error(l.source+":"+c+":"+u+": "+t);if(n.reason=t,n.filename=l.source,n.line=c,n.column=u,n.source=e,l.silent)h.push(n);else throw n}function match(t){var n=t.exec(e);if(n){var r=n[0];return updatePosition(r),e=e.slice(r.length),n}}function comments(e){var t;for(e=e||[];t=comment();)!1!==t&&e.push(t);return e}function comment(){var t=position();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return error("End of comment missing");var r=e.slice(2,n-2);return u+=2,updatePosition(r),e=e.slice(n),u+=2,t({type:"comment",comment:r})}}return match(r),function(){var e,n=[];for(comments(n);e=function(){var e=position(),n=match(i);if(n){if(comment(),!match(o))return error("property missing ':'");var r=match(a),l=e({type:"declaration",property:trim(n[0].replace(t,"")),value:r?trim(r[0].replace(t,"")):""});return match(s),l}}();)!1!==e&&(n.push(e),comments(n));return n}()}},1515:function(e,t,n){"use strict";let{DOCUMENT_MODE:r}=n(6152),i="html",o=["+//silmaril//dtd html pro v0r11 19970101//","-//as//dtd html 3.0 aswedit + extensions//","-//advasoft ltd//dtd html 3.0 aswedit + extensions//","-//ietf//dtd html 2.0 level 1//","-//ietf//dtd html 2.0 level 2//","-//ietf//dtd html 2.0 strict level 1//","-//ietf//dtd html 2.0 strict level 2//","-//ietf//dtd html 2.0 strict//","-//ietf//dtd html 2.0//","-//ietf//dtd html 2.1e//","-//ietf//dtd html 3.0//","-//ietf//dtd html 3.2 final//","-//ietf//dtd html 3.2//","-//ietf//dtd html 3//","-//ietf//dtd html level 0//","-//ietf//dtd html level 1//","-//ietf//dtd html level 2//","-//ietf//dtd html level 3//","-//ietf//dtd html strict level 0//","-//ietf//dtd html strict level 1//","-//ietf//dtd html strict level 2//","-//ietf//dtd html strict level 3//","-//ietf//dtd html strict//","-//ietf//dtd html//","-//metrius//dtd metrius presentational//","-//microsoft//dtd internet explorer 2.0 html strict//","-//microsoft//dtd internet explorer 2.0 html//","-//microsoft//dtd internet explorer 2.0 tables//","-//microsoft//dtd internet explorer 3.0 html strict//","-//microsoft//dtd internet explorer 3.0 html//","-//microsoft//dtd internet explorer 3.0 tables//","-//netscape comm. corp.//dtd html//","-//netscape comm. corp.//dtd strict html//","-//o'reilly and associates//dtd html 2.0//","-//o'reilly and associates//dtd html extended 1.0//","-//o'reilly and associates//dtd html extended relaxed 1.0//","-//sq//dtd html 2.0 hotmetal + extensions//","-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//","-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//","-//spyglass//dtd html 2.0 extended//","-//sun microsystems corp.//dtd hotjava html//","-//sun microsystems corp.//dtd hotjava strict html//","-//w3c//dtd html 3 1995-03-24//","-//w3c//dtd html 3.2 draft//","-//w3c//dtd html 3.2 final//","-//w3c//dtd html 3.2//","-//w3c//dtd html 3.2s draft//","-//w3c//dtd html 4.0 frameset//","-//w3c//dtd html 4.0 transitional//","-//w3c//dtd html experimental 19960712//","-//w3c//dtd html experimental 970421//","-//w3c//dtd w3 html//","-//w3o//dtd w3 html 3.0//","-//webtechs//dtd mozilla html 2.0//","-//webtechs//dtd mozilla html//"],a=o.concat(["-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"]),s=["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"],l=["-//w3c//dtd xhtml 1.0 frameset//","-//w3c//dtd xhtml 1.0 transitional//"],c=l.concat(["-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"]);function enquoteDoctypeId(e){let t=-1!==e.indexOf('"')?"'":'"';return t+e+t}function hasPrefix(e,t){for(let n=0;n<t.length;n++)if(0===e.indexOf(t[n]))return!0;return!1}t.isConforming=function(e){return e.name===i&&null===e.publicId&&(null===e.systemId||"about:legacy-compat"===e.systemId)},t.getDocumentMode=function(e){if(e.name!==i)return r.QUIRKS;let t=e.systemId;if(t&&"http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd"===t.toLowerCase())return r.QUIRKS;let n=e.publicId;if(null!==n){if(n=n.toLowerCase(),s.indexOf(n)>-1)return r.QUIRKS;let e=null===t?a:o;if(hasPrefix(n,e))return r.QUIRKS;if(hasPrefix(n,e=null===t?l:c))return r.LIMITED_QUIRKS}return r.NO_QUIRKS},t.serializeContent=function(e,t,n){let r="!DOCTYPE ";return e&&(r+=e),t?r+=" PUBLIC "+enquoteDoctypeId(t):n&&(r+=" SYSTEM"),null!==n&&(r+=" "+enquoteDoctypeId(n)),r}},1734:function(e){"use strict";e.exports={controlCharacterInInputStream:"control-character-in-input-stream",noncharacterInInputStream:"noncharacter-in-input-stream",surrogateInInputStream:"surrogate-in-input-stream",nonVoidHtmlElementStartTagWithTrailingSolidus:"non-void-html-element-start-tag-with-trailing-solidus",endTagWithAttributes:"end-tag-with-attributes",endTagWithTrailingSolidus:"end-tag-with-trailing-solidus",unexpectedSolidusInTag:"unexpected-solidus-in-tag",unexpectedNullCharacter:"unexpected-null-character",unexpectedQuestionMarkInsteadOfTagName:"unexpected-question-mark-instead-of-tag-name",invalidFirstCharacterOfTagName:"invalid-first-character-of-tag-name",unexpectedEqualsSignBeforeAttributeName:"unexpected-equals-sign-before-attribute-name",missingEndTagName:"missing-end-tag-name",unexpectedCharacterInAttributeName:"unexpected-character-in-attribute-name",unknownNamedCharacterReference:"unknown-named-character-reference",missingSemicolonAfterCharacterReference:"missing-semicolon-after-character-reference",unexpectedCharacterAfterDoctypeSystemIdentifier:"unexpected-character-after-doctype-system-identifier",unexpectedCharacterInUnquotedAttributeValue:"unexpected-character-in-unquoted-attribute-value",eofBeforeTagName:"eof-before-tag-name",eofInTag:"eof-in-tag",missingAttributeValue:"missing-attribute-value",missingWhitespaceBetweenAttributes:"missing-whitespace-between-attributes",missingWhitespaceAfterDoctypePublicKeyword:"missing-whitespace-after-doctype-public-keyword",missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers:"missing-whitespace-between-doctype-public-and-system-identifiers",missingWhitespaceAfterDoctypeSystemKeyword:"missing-whitespace-after-doctype-system-keyword",missingQuoteBeforeDoctypePublicIdentifier:"missing-quote-before-doctype-public-identifier",missingQuoteBeforeDoctypeSystemIdentifier:"missing-quote-before-doctype-system-identifier",missingDoctypePublicIdentifier:"missing-doctype-public-identifier",missingDoctypeSystemIdentifier:"missing-doctype-system-identifier",abruptDoctypePublicIdentifier:"abrupt-doctype-public-identifier",abruptDoctypeSystemIdentifier:"abrupt-doctype-system-identifier",cdataInHtmlContent:"cdata-in-html-content",incorrectlyOpenedComment:"incorrectly-opened-comment",eofInScriptHtmlCommentLikeText:"eof-in-script-html-comment-like-text",eofInDoctype:"eof-in-doctype",nestedComment:"nested-comment",abruptClosingOfEmptyComment:"abrupt-closing-of-empty-comment",eofInComment:"eof-in-comment",incorrectlyClosedComment:"incorrectly-closed-comment",eofInCdata:"eof-in-cdata",absenceOfDigitsInNumericCharacterReference:"absence-of-digits-in-numeric-character-reference",nullCharacterReference:"null-character-reference",surrogateCharacterReference:"surrogate-character-reference",characterReferenceOutsideUnicodeRange:"character-reference-outside-unicode-range",controlCharacterReference:"control-character-reference",noncharacterCharacterReference:"noncharacter-character-reference",missingWhitespaceBeforeDoctypeName:"missing-whitespace-before-doctype-name",missingDoctypeName:"missing-doctype-name",invalidCharacterSequenceAfterDoctypeName:"invalid-character-sequence-after-doctype-name",duplicateAttribute:"duplicate-attribute",nonConformingDoctype:"non-conforming-doctype",missingDoctype:"missing-doctype",misplacedDoctype:"misplaced-doctype",endTagWithoutMatchingOpenElement:"end-tag-without-matching-open-element",closingOfElementWithOpenChildElements:"closing-of-element-with-open-child-elements",disallowedContentInNoscriptInHead:"disallowed-content-in-noscript-in-head",openElementsLeftAfterEof:"open-elements-left-after-eof",abandonedHeadElementChild:"abandoned-head-element-child",misplacedStartTagForHeadElement:"misplaced-start-tag-for-head-element",nestedNoscriptInHead:"nested-noscript-in-head",eofInElementThatCanContainOnlyText:"eof-in-element-that-can-contain-only-text"}},8779:function(e,t,n){"use strict";let r=n(5763),i=n(6152),o=i.TAG_NAMES,a=i.NAMESPACES,s=i.ATTRS,l={TEXT_HTML:"text/html",APPLICATION_XML:"application/xhtml+xml"},c={attributename:"attributeName",attributetype:"attributeType",basefrequency:"baseFrequency",baseprofile:"baseProfile",calcmode:"calcMode",clippathunits:"clipPathUnits",diffuseconstant:"diffuseConstant",edgemode:"edgeMode",filterunits:"filterUnits",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",limitingconeangle:"limitingConeAngle",markerheight:"markerHeight",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textlength:"textLength",viewbox:"viewBox",viewtarget:"viewTarget",xchannelselector:"xChannelSelector",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"},u={"xlink:actuate":{prefix:"xlink",name:"actuate",namespace:a.XLINK},"xlink:arcrole":{prefix:"xlink",name:"arcrole",namespace:a.XLINK},"xlink:href":{prefix:"xlink",name:"href",namespace:a.XLINK},"xlink:role":{prefix:"xlink",name:"role",namespace:a.XLINK},"xlink:show":{prefix:"xlink",name:"show",namespace:a.XLINK},"xlink:title":{prefix:"xlink",name:"title",namespace:a.XLINK},"xlink:type":{prefix:"xlink",name:"type",namespace:a.XLINK},"xml:base":{prefix:"xml",name:"base",namespace:a.XML},"xml:lang":{prefix:"xml",name:"lang",namespace:a.XML},"xml:space":{prefix:"xml",name:"space",namespace:a.XML},xmlns:{prefix:"",name:"xmlns",namespace:a.XMLNS},"xmlns:xlink":{prefix:"xmlns",name:"xlink",namespace:a.XMLNS}},h=t.SVG_TAG_NAMES_ADJUSTMENT_MAP={altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient",textpath:"textPath"},p={[o.B]:!0,[o.BIG]:!0,[o.BLOCKQUOTE]:!0,[o.BODY]:!0,[o.BR]:!0,[o.CENTER]:!0,[o.CODE]:!0,[o.DD]:!0,[o.DIV]:!0,[o.DL]:!0,[o.DT]:!0,[o.EM]:!0,[o.EMBED]:!0,[o.H1]:!0,[o.H2]:!0,[o.H3]:!0,[o.H4]:!0,[o.H5]:!0,[o.H6]:!0,[o.HEAD]:!0,[o.HR]:!0,[o.I]:!0,[o.IMG]:!0,[o.LI]:!0,[o.LISTING]:!0,[o.MENU]:!0,[o.META]:!0,[o.NOBR]:!0,[o.OL]:!0,[o.P]:!0,[o.PRE]:!0,[o.RUBY]:!0,[o.S]:!0,[o.SMALL]:!0,[o.SPAN]:!0,[o.STRONG]:!0,[o.STRIKE]:!0,[o.SUB]:!0,[o.SUP]:!0,[o.TABLE]:!0,[o.TT]:!0,[o.U]:!0,[o.UL]:!0,[o.VAR]:!0};t.causesExit=function(e){let t=e.tagName,n=t===o.FONT&&(null!==r.getTokenAttr(e,s.COLOR)||null!==r.getTokenAttr(e,s.SIZE)||null!==r.getTokenAttr(e,s.FACE));return!!n||p[t]},t.adjustTokenMathMLAttrs=function(e){for(let t=0;t<e.attrs.length;t++)if("definitionurl"===e.attrs[t].name){e.attrs[t].name="definitionURL";break}},t.adjustTokenSVGAttrs=function(e){for(let t=0;t<e.attrs.length;t++){let n=c[e.attrs[t].name];n&&(e.attrs[t].name=n)}},t.adjustTokenXMLAttrs=function(e){for(let t=0;t<e.attrs.length;t++){let n=u[e.attrs[t].name];n&&(e.attrs[t].prefix=n.prefix,e.attrs[t].name=n.name,e.attrs[t].namespace=n.namespace)}},t.adjustTokenSVGTagName=function(e){let t=h[e.tagName];t&&(e.tagName=t)},t.isIntegrationPoint=function(e,t,n,r){return!!((!r||r===a.HTML)&&function(e,t,n){if(t===a.MATHML&&e===o.ANNOTATION_XML){for(let e=0;e<n.length;e++)if(n[e].name===s.ENCODING){let t=n[e].value.toLowerCase();return t===l.TEXT_HTML||t===l.APPLICATION_XML}}return t===a.SVG&&(e===o.FOREIGN_OBJECT||e===o.DESC||e===o.TITLE)}(e,t,n))||(!r||r===a.MATHML)&&t===a.MATHML&&(e===o.MI||e===o.MO||e===o.MN||e===o.MS||e===o.MTEXT)}},6152:function(e,t){"use strict";let n=t.NAMESPACES={HTML:"http://www.w3.org/1999/xhtml",MATHML:"http://www.w3.org/1998/Math/MathML",SVG:"http://www.w3.org/2000/svg",XLINK:"http://www.w3.org/1999/xlink",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"};t.ATTRS={TYPE:"type",ACTION:"action",ENCODING:"encoding",PROMPT:"prompt",NAME:"name",COLOR:"color",FACE:"face",SIZE:"size"},t.DOCUMENT_MODE={NO_QUIRKS:"no-quirks",QUIRKS:"quirks",LIMITED_QUIRKS:"limited-quirks"};let r=t.TAG_NAMES={A:"a",ADDRESS:"address",ANNOTATION_XML:"annotation-xml",APPLET:"applet",AREA:"area",ARTICLE:"article",ASIDE:"aside",B:"b",BASE:"base",BASEFONT:"basefont",BGSOUND:"bgsound",BIG:"big",BLOCKQUOTE:"blockquote",BODY:"body",BR:"br",BUTTON:"button",CAPTION:"caption",CENTER:"center",CODE:"code",COL:"col",COLGROUP:"colgroup",DD:"dd",DESC:"desc",DETAILS:"details",DIALOG:"dialog",DIR:"dir",DIV:"div",DL:"dl",DT:"dt",EM:"em",EMBED:"embed",FIELDSET:"fieldset",FIGCAPTION:"figcaption",FIGURE:"figure",FONT:"font",FOOTER:"footer",FOREIGN_OBJECT:"foreignObject",FORM:"form",FRAME:"frame",FRAMESET:"frameset",H1:"h1",H2:"h2",H3:"h3",H4:"h4",H5:"h5",H6:"h6",HEAD:"head",HEADER:"header",HGROUP:"hgroup",HR:"hr",HTML:"html",I:"i",IMG:"img",IMAGE:"image",INPUT:"input",IFRAME:"iframe",KEYGEN:"keygen",LABEL:"label",LI:"li",LINK:"link",LISTING:"listing",MAIN:"main",MALIGNMARK:"malignmark",MARQUEE:"marquee",MATH:"math",MENU:"menu",META:"meta",MGLYPH:"mglyph",MI:"mi",MO:"mo",MN:"mn",MS:"ms",MTEXT:"mtext",NAV:"nav",NOBR:"nobr",NOFRAMES:"noframes",NOEMBED:"noembed",NOSCRIPT:"noscript",OBJECT:"object",OL:"ol",OPTGROUP:"optgroup",OPTION:"option",P:"p",PARAM:"param",PLAINTEXT:"plaintext",PRE:"pre",RB:"rb",RP:"rp",RT:"rt",RTC:"rtc",RUBY:"ruby",S:"s",SCRIPT:"script",SECTION:"section",SELECT:"select",SOURCE:"source",SMALL:"small",SPAN:"span",STRIKE:"strike",STRONG:"strong",STYLE:"style",SUB:"sub",SUMMARY:"summary",SUP:"sup",TABLE:"table",TBODY:"tbody",TEMPLATE:"template",TEXTAREA:"textarea",TFOOT:"tfoot",TD:"td",TH:"th",THEAD:"thead",TITLE:"title",TR:"tr",TRACK:"track",TT:"tt",U:"u",UL:"ul",SVG:"svg",VAR:"var",WBR:"wbr",XMP:"xmp"};t.SPECIAL_ELEMENTS={[n.HTML]:{[r.ADDRESS]:!0,[r.APPLET]:!0,[r.AREA]:!0,[r.ARTICLE]:!0,[r.ASIDE]:!0,[r.BASE]:!0,[r.BASEFONT]:!0,[r.BGSOUND]:!0,[r.BLOCKQUOTE]:!0,[r.BODY]:!0,[r.BR]:!0,[r.BUTTON]:!0,[r.CAPTION]:!0,[r.CENTER]:!0,[r.COL]:!0,[r.COLGROUP]:!0,[r.DD]:!0,[r.DETAILS]:!0,[r.DIR]:!0,[r.DIV]:!0,[r.DL]:!0,[r.DT]:!0,[r.EMBED]:!0,[r.FIELDSET]:!0,[r.FIGCAPTION]:!0,[r.FIGURE]:!0,[r.FOOTER]:!0,[r.FORM]:!0,[r.FRAME]:!0,[r.FRAMESET]:!0,[r.H1]:!0,[r.H2]:!0,[r.H3]:!0,[r.H4]:!0,[r.H5]:!0,[r.H6]:!0,[r.HEAD]:!0,[r.HEADER]:!0,[r.HGROUP]:!0,[r.HR]:!0,[r.HTML]:!0,[r.IFRAME]:!0,[r.IMG]:!0,[r.INPUT]:!0,[r.LI]:!0,[r.LINK]:!0,[r.LISTING]:!0,[r.MAIN]:!0,[r.MARQUEE]:!0,[r.MENU]:!0,[r.META]:!0,[r.NAV]:!0,[r.NOEMBED]:!0,[r.NOFRAMES]:!0,[r.NOSCRIPT]:!0,[r.OBJECT]:!0,[r.OL]:!0,[r.P]:!0,[r.PARAM]:!0,[r.PLAINTEXT]:!0,[r.PRE]:!0,[r.SCRIPT]:!0,[r.SECTION]:!0,[r.SELECT]:!0,[r.SOURCE]:!0,[r.STYLE]:!0,[r.SUMMARY]:!0,[r.TABLE]:!0,[r.TBODY]:!0,[r.TD]:!0,[r.TEMPLATE]:!0,[r.TEXTAREA]:!0,[r.TFOOT]:!0,[r.TH]:!0,[r.THEAD]:!0,[r.TITLE]:!0,[r.TR]:!0,[r.TRACK]:!0,[r.UL]:!0,[r.WBR]:!0,[r.XMP]:!0},[n.MATHML]:{[r.MI]:!0,[r.MO]:!0,[r.MN]:!0,[r.MS]:!0,[r.MTEXT]:!0,[r.ANNOTATION_XML]:!0},[n.SVG]:{[r.TITLE]:!0,[r.FOREIGN_OBJECT]:!0,[r.DESC]:!0}}},4284:function(e,t){"use strict";let n=[65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111];t.REPLACEMENT_CHARACTER="�",t.CODE_POINTS={EOF:-1,NULL:0,TABULATION:9,CARRIAGE_RETURN:13,LINE_FEED:10,FORM_FEED:12,SPACE:32,EXCLAMATION_MARK:33,QUOTATION_MARK:34,NUMBER_SIGN:35,AMPERSAND:38,APOSTROPHE:39,HYPHEN_MINUS:45,SOLIDUS:47,DIGIT_0:48,DIGIT_9:57,SEMICOLON:59,LESS_THAN_SIGN:60,EQUALS_SIGN:61,GREATER_THAN_SIGN:62,QUESTION_MARK:63,LATIN_CAPITAL_A:65,LATIN_CAPITAL_F:70,LATIN_CAPITAL_X:88,LATIN_CAPITAL_Z:90,RIGHT_SQUARE_BRACKET:93,GRAVE_ACCENT:96,LATIN_SMALL_A:97,LATIN_SMALL_F:102,LATIN_SMALL_X:120,LATIN_SMALL_Z:122,REPLACEMENT_CHARACTER:65533},t.CODE_POINT_SEQUENCES={DASH_DASH_STRING:[45,45],DOCTYPE_STRING:[68,79,67,84,89,80,69],CDATA_START_STRING:[91,67,68,65,84,65,91],SCRIPT_STRING:[115,99,114,105,112,116],PUBLIC_STRING:[80,85,66,76,73,67],SYSTEM_STRING:[83,89,83,84,69,77]},t.isSurrogate=function(e){return e>=55296&&e<=57343},t.isSurrogatePair=function(e){return e>=56320&&e<=57343},t.getSurrogatePairCodePoint=function(e,t){return(e-55296)*1024+9216+t},t.isControlCodePoint=function(e){return 32!==e&&10!==e&&13!==e&&9!==e&&12!==e&&e>=1&&e<=31||e>=127&&e<=159},t.isUndefinedCodePoint=function(e){return e>=64976&&e<=65007||n.indexOf(e)>-1}},3843:function(e,t,n){"use strict";let r=n(1704);e.exports=class extends r{constructor(e,t){super(e),this.posTracker=null,this.onParseError=t.onParseError}_setErrorLocation(e){e.startLine=e.endLine=this.posTracker.line,e.startCol=e.endCol=this.posTracker.col,e.startOffset=e.endOffset=this.posTracker.offset}_reportError(e){let t={code:e,startLine:-1,startCol:-1,startOffset:-1,endLine:-1,endCol:-1,endOffset:-1};this._setErrorLocation(t),this.onParseError(t)}_getOverriddenMethods(e){return{_err(t){e._reportError(t)}}}}},2232:function(e,t,n){"use strict";let r=n(3843),i=n(50),o=n(6110),a=n(1704);e.exports=class extends r{constructor(e,t){super(e,t),this.opts=t,this.ctLoc=null,this.locBeforeToken=!1}_setErrorLocation(e){this.ctLoc&&(e.startLine=this.ctLoc.startLine,e.startCol=this.ctLoc.startCol,e.startOffset=this.ctLoc.startOffset,e.endLine=this.locBeforeToken?this.ctLoc.startLine:this.ctLoc.endLine,e.endCol=this.locBeforeToken?this.ctLoc.startCol:this.ctLoc.endCol,e.endOffset=this.locBeforeToken?this.ctLoc.startOffset:this.ctLoc.endOffset)}_getOverriddenMethods(e,t){return{_bootstrap(n,r){t._bootstrap.call(this,n,r),a.install(this.tokenizer,i,e.opts),a.install(this.tokenizer,o)},_processInputToken(n){e.ctLoc=n.location,t._processInputToken.call(this,n)},_err(t,n){e.locBeforeToken=n&&n.beforeToken,e._reportError(t)}}}}},3288:function(e,t,n){"use strict";let r=n(3843),i=n(7930),o=n(1704);e.exports=class extends r{constructor(e,t){super(e,t),this.posTracker=o.install(e,i),this.lastErrOffset=-1}_reportError(e){this.lastErrOffset!==this.posTracker.offset&&(this.lastErrOffset=this.posTracker.offset,super._reportError(e))}}},50:function(e,t,n){"use strict";let r=n(3843),i=n(3288),o=n(1704);e.exports=class extends r{constructor(e,t){super(e,t);let n=o.install(e.preprocessor,i,t);this.posTracker=n.posTracker}}},1077:function(e,t,n){"use strict";let r=n(1704);e.exports=class extends r{constructor(e,t){super(e),this.onItemPop=t.onItemPop}_getOverriddenMethods(e,t){return{pop(){e.onItemPop(this.current),t.pop.call(this)},popAllUpToHtmlElement(){for(let t=this.stackTop;t>0;t--)e.onItemPop(this.items[t]);t.popAllUpToHtmlElement.call(this)},remove(n){e.onItemPop(this.current),t.remove.call(this,n)}}}}},452:function(e,t,n){"use strict";let r=n(1704),i=n(5763),o=n(6110),a=n(1077),s=n(6152),l=s.TAG_NAMES;e.exports=class extends r{constructor(e){super(e),this.parser=e,this.treeAdapter=this.parser.treeAdapter,this.posTracker=null,this.lastStartTagToken=null,this.lastFosterParentingLocation=null,this.currentToken=null}_setStartLocation(e){let t=null;this.lastStartTagToken&&((t=Object.assign({},this.lastStartTagToken.location)).startTag=this.lastStartTagToken.location),this.treeAdapter.setNodeSourceCodeLocation(e,t)}_setEndLocation(e,t){let n=this.treeAdapter.getNodeSourceCodeLocation(e);if(n&&t.location){let n=t.location,r=this.treeAdapter.getTagName(e),o=t.type===i.END_TAG_TOKEN&&r===t.tagName,a={};o?(a.endTag=Object.assign({},n),a.endLine=n.endLine,a.endCol=n.endCol,a.endOffset=n.endOffset):(a.endLine=n.startLine,a.endCol=n.startCol,a.endOffset=n.startOffset),this.treeAdapter.updateNodeSourceCodeLocation(e,a)}}_getOverriddenMethods(e,t){return{_bootstrap(n,i){t._bootstrap.call(this,n,i),e.lastStartTagToken=null,e.lastFosterParentingLocation=null,e.currentToken=null;let s=r.install(this.tokenizer,o);e.posTracker=s.posTracker,r.install(this.openElements,a,{onItemPop:function(t){e._setEndLocation(t,e.currentToken)}})},_runParsingLoop(n){t._runParsingLoop.call(this,n);for(let t=this.openElements.stackTop;t>=0;t--)e._setEndLocation(this.openElements.items[t],e.currentToken)},_processTokenInForeignContent(n){e.currentToken=n,t._processTokenInForeignContent.call(this,n)},_processToken(n){e.currentToken=n,t._processToken.call(this,n);let r=n.type===i.END_TAG_TOKEN&&(n.tagName===l.HTML||n.tagName===l.BODY&&this.openElements.hasInScope(l.BODY));if(r)for(let t=this.openElements.stackTop;t>=0;t--){let r=this.openElements.items[t];if(this.treeAdapter.getTagName(r)===n.tagName){e._setEndLocation(r,n);break}}},_setDocumentType(e){t._setDocumentType.call(this,e);let n=this.treeAdapter.getChildNodes(this.document),r=n.length;for(let t=0;t<r;t++){let r=n[t];if(this.treeAdapter.isDocumentTypeNode(r)){this.treeAdapter.setNodeSourceCodeLocation(r,e.location);break}}},_attachElementToTree(n){e._setStartLocation(n),e.lastStartTagToken=null,t._attachElementToTree.call(this,n)},_appendElement(n,r){e.lastStartTagToken=n,t._appendElement.call(this,n,r)},_insertElement(n,r){e.lastStartTagToken=n,t._insertElement.call(this,n,r)},_insertTemplate(n){e.lastStartTagToken=n,t._insertTemplate.call(this,n);let r=this.treeAdapter.getTemplateContent(this.openElements.current);this.treeAdapter.setNodeSourceCodeLocation(r,null)},_insertFakeRootElement(){t._insertFakeRootElement.call(this),this.treeAdapter.setNodeSourceCodeLocation(this.openElements.current,null)},_appendCommentNode(e,n){t._appendCommentNode.call(this,e,n);let r=this.treeAdapter.getChildNodes(n),i=r[r.length-1];this.treeAdapter.setNodeSourceCodeLocation(i,e.location)},_findFosterParentingLocation(){return e.lastFosterParentingLocation=t._findFosterParentingLocation.call(this),e.lastFosterParentingLocation},_insertCharacters(n){t._insertCharacters.call(this,n);let r=this._shouldFosterParentOnInsertion(),i=r&&e.lastFosterParentingLocation.parent||this.openElements.currentTmplContent||this.openElements.current,o=this.treeAdapter.getChildNodes(i),a=r&&e.lastFosterParentingLocation.beforeElement?o.indexOf(e.lastFosterParentingLocation.beforeElement)-1:o.length-1,s=o[a],l=this.treeAdapter.getNodeSourceCodeLocation(s);if(l){let{endLine:e,endCol:t,endOffset:r}=n.location;this.treeAdapter.updateNodeSourceCodeLocation(s,{endLine:e,endCol:t,endOffset:r})}else this.treeAdapter.setNodeSourceCodeLocation(s,n.location)}}}}},6110:function(e,t,n){"use strict";let r=n(1704),i=n(5763),o=n(7930);e.exports=class extends r{constructor(e){super(e),this.tokenizer=e,this.posTracker=r.install(e.preprocessor,o),this.currentAttrLocation=null,this.ctLoc=null}_getCurrentLocation(){return{startLine:this.posTracker.line,startCol:this.posTracker.col,startOffset:this.posTracker.offset,endLine:-1,endCol:-1,endOffset:-1}}_attachCurrentAttrLocationInfo(){this.currentAttrLocation.endLine=this.posTracker.line,this.currentAttrLocation.endCol=this.posTracker.col,this.currentAttrLocation.endOffset=this.posTracker.offset;let e=this.tokenizer.currentToken,t=this.tokenizer.currentAttr;e.location.attrs||(e.location.attrs=Object.create(null)),e.location.attrs[t.name]=this.currentAttrLocation}_getOverriddenMethods(e,t){let n={_createStartTagToken(){t._createStartTagToken.call(this),this.currentToken.location=e.ctLoc},_createEndTagToken(){t._createEndTagToken.call(this),this.currentToken.location=e.ctLoc},_createCommentToken(){t._createCommentToken.call(this),this.currentToken.location=e.ctLoc},_createDoctypeToken(n){t._createDoctypeToken.call(this,n),this.currentToken.location=e.ctLoc},_createCharacterToken(n,r){t._createCharacterToken.call(this,n,r),this.currentCharacterToken.location=e.ctLoc},_createEOFToken(){t._createEOFToken.call(this),this.currentToken.location=e._getCurrentLocation()},_createAttr(n){t._createAttr.call(this,n),e.currentAttrLocation=e._getCurrentLocation()},_leaveAttrName(n){t._leaveAttrName.call(this,n),e._attachCurrentAttrLocationInfo()},_leaveAttrValue(n){t._leaveAttrValue.call(this,n),e._attachCurrentAttrLocationInfo()},_emitCurrentToken(){let n=this.currentToken.location;this.currentCharacterToken&&(this.currentCharacterToken.location.endLine=n.startLine,this.currentCharacterToken.location.endCol=n.startCol,this.currentCharacterToken.location.endOffset=n.startOffset),this.currentToken.type===i.EOF_TOKEN?(n.endLine=n.startLine,n.endCol=n.startCol,n.endOffset=n.startOffset):(n.endLine=e.posTracker.line,n.endCol=e.posTracker.col+1,n.endOffset=e.posTracker.offset+1),t._emitCurrentToken.call(this)},_emitCurrentCharacterToken(){let n=this.currentCharacterToken&&this.currentCharacterToken.location;n&&-1===n.endOffset&&(n.endLine=e.posTracker.line,n.endCol=e.posTracker.col,n.endOffset=e.posTracker.offset),t._emitCurrentCharacterToken.call(this)}};return Object.keys(i.MODE).forEach(r=>{let o=i.MODE[r];n[o]=function(n){e.ctLoc=e._getCurrentLocation(),t[o].call(this,n)}}),n}}},7930:function(e,t,n){"use strict";let r=n(1704);e.exports=class extends r{constructor(e){super(e),this.preprocessor=e,this.isEol=!1,this.lineStartPos=0,this.droppedBufferSize=0,this.offset=0,this.col=0,this.line=1}_getOverriddenMethods(e,t){return{advance(){let n=this.pos+1,r=this.html[n];return e.isEol&&(e.isEol=!1,e.line++,e.lineStartPos=n),("\n"===r||"\r"===r&&"\n"!==this.html[n+1])&&(e.isEol=!0),e.col=n-e.lineStartPos+1,e.offset=e.droppedBufferSize+n,t.advance.call(this)},retreat(){t.retreat.call(this),e.isEol=!1,e.col=this.pos-e.lineStartPos+1},dropParsedChunk(){let n=this.pos;t.dropParsedChunk.call(this);let r=n-this.pos;e.lineStartPos-=r,e.droppedBufferSize+=r,e.offset=e.droppedBufferSize+this.pos}}}}},2484:function(e){"use strict";let FormattingElementList=class FormattingElementList{constructor(e){this.length=0,this.entries=[],this.treeAdapter=e,this.bookmark=null}_getNoahArkConditionCandidates(e){let t=[];if(this.length>=3){let n=this.treeAdapter.getAttrList(e).length,r=this.treeAdapter.getTagName(e),i=this.treeAdapter.getNamespaceURI(e);for(let e=this.length-1;e>=0;e--){let o=this.entries[e];if(o.type===FormattingElementList.MARKER_ENTRY)break;let a=o.element,s=this.treeAdapter.getAttrList(a),l=this.treeAdapter.getTagName(a)===r&&this.treeAdapter.getNamespaceURI(a)===i&&s.length===n;l&&t.push({idx:e,attrs:s})}}return t.length<3?[]:t}_ensureNoahArkCondition(e){let t=this._getNoahArkConditionCandidates(e),n=t.length;if(n){let r=this.treeAdapter.getAttrList(e),i=r.length,o=Object.create(null);for(let e=0;e<i;e++){let t=r[e];o[t.name]=t.value}for(let e=0;e<i;e++)for(let r=0;r<n;r++){let i=t[r].attrs[e];if(o[i.name]!==i.value&&(t.splice(r,1),n--),t.length<3)return}for(let e=n-1;e>=2;e--)this.entries.splice(t[e].idx,1),this.length--}}insertMarker(){this.entries.push({type:FormattingElementList.MARKER_ENTRY}),this.length++}pushElement(e,t){this._ensureNoahArkCondition(e),this.entries.push({type:FormattingElementList.ELEMENT_ENTRY,element:e,token:t}),this.length++}insertElementAfterBookmark(e,t){let n=this.length-1;for(;n>=0&&this.entries[n]!==this.bookmark;n--);this.entries.splice(n+1,0,{type:FormattingElementList.ELEMENT_ENTRY,element:e,token:t}),this.length++}removeEntry(e){for(let t=this.length-1;t>=0;t--)if(this.entries[t]===e){this.entries.splice(t,1),this.length--;break}}clearToLastMarker(){for(;this.length;){let e=this.entries.pop();if(this.length--,e.type===FormattingElementList.MARKER_ENTRY)break}}getElementEntryInScopeWithTagName(e){for(let t=this.length-1;t>=0;t--){let n=this.entries[t];if(n.type===FormattingElementList.MARKER_ENTRY)break;if(this.treeAdapter.getTagName(n.element)===e)return n}return null}getElementEntry(e){for(let t=this.length-1;t>=0;t--){let n=this.entries[t];if(n.type===FormattingElementList.ELEMENT_ENTRY&&n.element===e)return n}return null}};FormattingElementList.MARKER_ENTRY="MARKER_ENTRY",FormattingElementList.ELEMENT_ENTRY="ELEMENT_ENTRY",e.exports=FormattingElementList},7045:function(e,t,n){"use strict";let r=n(5763),i=n(6519),o=n(2484),a=n(452),s=n(2232),l=n(1704),c=n(7296),u=n(8904),h=n(1515),p=n(8779),f=n(1734),d=n(4284),m=n(6152),T=m.TAG_NAMES,g=m.NAMESPACES,E=m.ATTRS,_={scriptingEnabled:!0,sourceCodeLocationInfo:!1,onParseError:null,treeAdapter:c},A="hidden",k="INITIAL_MODE",C="BEFORE_HTML_MODE",y="BEFORE_HEAD_MODE",N="IN_HEAD_MODE",S="IN_HEAD_NO_SCRIPT_MODE",I="AFTER_HEAD_MODE",b="IN_BODY_MODE",O="TEXT_MODE",x="IN_TABLE_MODE",R="IN_TABLE_TEXT_MODE",v="IN_CAPTION_MODE",L="IN_COLUMN_GROUP_MODE",P="IN_TABLE_BODY_MODE",M="IN_ROW_MODE",D="IN_CELL_MODE",w="IN_SELECT_MODE",H="IN_SELECT_IN_TABLE_MODE",F="IN_TEMPLATE_MODE",B="AFTER_BODY_MODE",U="IN_FRAMESET_MODE",z="AFTER_FRAMESET_MODE",G="AFTER_AFTER_BODY_MODE",K="AFTER_AFTER_FRAMESET_MODE",j={[T.TR]:M,[T.TBODY]:P,[T.THEAD]:P,[T.TFOOT]:P,[T.CAPTION]:v,[T.COLGROUP]:L,[T.TABLE]:x,[T.BODY]:b,[T.FRAMESET]:U},V={[T.CAPTION]:x,[T.COLGROUP]:x,[T.TBODY]:x,[T.TFOOT]:x,[T.THEAD]:x,[T.COL]:L,[T.TR]:P,[T.TD]:M,[T.TH]:M},Y={[k]:{[r.CHARACTER_TOKEN]:tokenInInitialMode,[r.NULL_CHARACTER_TOKEN]:tokenInInitialMode,[r.WHITESPACE_CHARACTER_TOKEN]:ignoreToken,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:function(e,t){e._setDocumentType(t);let n=t.forceQuirks?m.DOCUMENT_MODE.QUIRKS:h.getDocumentMode(t);h.isConforming(t)||e._err(f.nonConformingDoctype),e.treeAdapter.setDocumentMode(e.document,n),e.insertionMode=C},[r.START_TAG_TOKEN]:tokenInInitialMode,[r.END_TAG_TOKEN]:tokenInInitialMode,[r.EOF_TOKEN]:tokenInInitialMode},[C]:{[r.CHARACTER_TOKEN]:tokenBeforeHtml,[r.NULL_CHARACTER_TOKEN]:tokenBeforeHtml,[r.WHITESPACE_CHARACTER_TOKEN]:ignoreToken,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){t.tagName===T.HTML?(e._insertElement(t,g.HTML),e.insertionMode=y):tokenBeforeHtml(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;(n===T.HTML||n===T.HEAD||n===T.BODY||n===T.BR)&&tokenBeforeHtml(e,t)},[r.EOF_TOKEN]:tokenBeforeHtml},[y]:{[r.CHARACTER_TOKEN]:tokenBeforeHead,[r.NULL_CHARACTER_TOKEN]:tokenBeforeHead,[r.WHITESPACE_CHARACTER_TOKEN]:ignoreToken,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:misplacedDoctype,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.HTML?startTagInBody(e,t):n===T.HEAD?(e._insertElement(t,g.HTML),e.headElement=e.openElements.current,e.insertionMode=N):tokenBeforeHead(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.HEAD||n===T.BODY||n===T.HTML||n===T.BR?tokenBeforeHead(e,t):e._err(f.endTagWithoutMatchingOpenElement)},[r.EOF_TOKEN]:tokenBeforeHead},[N]:{[r.CHARACTER_TOKEN]:tokenInHead,[r.NULL_CHARACTER_TOKEN]:tokenInHead,[r.WHITESPACE_CHARACTER_TOKEN]:insertCharacters,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:misplacedDoctype,[r.START_TAG_TOKEN]:startTagInHead,[r.END_TAG_TOKEN]:endTagInHead,[r.EOF_TOKEN]:tokenInHead},[S]:{[r.CHARACTER_TOKEN]:tokenInHeadNoScript,[r.NULL_CHARACTER_TOKEN]:tokenInHeadNoScript,[r.WHITESPACE_CHARACTER_TOKEN]:insertCharacters,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:misplacedDoctype,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.HTML?startTagInBody(e,t):n===T.BASEFONT||n===T.BGSOUND||n===T.HEAD||n===T.LINK||n===T.META||n===T.NOFRAMES||n===T.STYLE?startTagInHead(e,t):n===T.NOSCRIPT?e._err(f.nestedNoscriptInHead):tokenInHeadNoScript(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.NOSCRIPT?(e.openElements.pop(),e.insertionMode=N):n===T.BR?tokenInHeadNoScript(e,t):e._err(f.endTagWithoutMatchingOpenElement)},[r.EOF_TOKEN]:tokenInHeadNoScript},[I]:{[r.CHARACTER_TOKEN]:tokenAfterHead,[r.NULL_CHARACTER_TOKEN]:tokenAfterHead,[r.WHITESPACE_CHARACTER_TOKEN]:insertCharacters,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:misplacedDoctype,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.HTML?startTagInBody(e,t):n===T.BODY?(e._insertElement(t,g.HTML),e.framesetOk=!1,e.insertionMode=b):n===T.FRAMESET?(e._insertElement(t,g.HTML),e.insertionMode=U):n===T.BASE||n===T.BASEFONT||n===T.BGSOUND||n===T.LINK||n===T.META||n===T.NOFRAMES||n===T.SCRIPT||n===T.STYLE||n===T.TEMPLATE||n===T.TITLE?(e._err(f.abandonedHeadElementChild),e.openElements.push(e.headElement),startTagInHead(e,t),e.openElements.remove(e.headElement)):n===T.HEAD?e._err(f.misplacedStartTagForHeadElement):tokenAfterHead(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.BODY||n===T.HTML||n===T.BR?tokenAfterHead(e,t):n===T.TEMPLATE?endTagInHead(e,t):e._err(f.endTagWithoutMatchingOpenElement)},[r.EOF_TOKEN]:tokenAfterHead},[b]:{[r.CHARACTER_TOKEN]:characterInBody,[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:whitespaceCharacterInBody,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:startTagInBody,[r.END_TAG_TOKEN]:endTagInBody,[r.EOF_TOKEN]:eofInBody},[O]:{[r.CHARACTER_TOKEN]:insertCharacters,[r.NULL_CHARACTER_TOKEN]:insertCharacters,[r.WHITESPACE_CHARACTER_TOKEN]:insertCharacters,[r.COMMENT_TOKEN]:ignoreToken,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:ignoreToken,[r.END_TAG_TOKEN]:function(e,t){t.tagName===T.SCRIPT&&(e.pendingScript=e.openElements.current),e.openElements.pop(),e.insertionMode=e.originalInsertionMode},[r.EOF_TOKEN]:function(e,t){e._err(f.eofInElementThatCanContainOnlyText),e.openElements.pop(),e.insertionMode=e.originalInsertionMode,e._processToken(t)}},[x]:{[r.CHARACTER_TOKEN]:characterInTable,[r.NULL_CHARACTER_TOKEN]:characterInTable,[r.WHITESPACE_CHARACTER_TOKEN]:characterInTable,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:startTagInTable,[r.END_TAG_TOKEN]:endTagInTable,[r.EOF_TOKEN]:eofInBody},[R]:{[r.CHARACTER_TOKEN]:function(e,t){e.pendingCharacterTokens.push(t),e.hasNonWhitespacePendingCharacterToken=!0},[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:function(e,t){e.pendingCharacterTokens.push(t)},[r.COMMENT_TOKEN]:tokenInTableText,[r.DOCTYPE_TOKEN]:tokenInTableText,[r.START_TAG_TOKEN]:tokenInTableText,[r.END_TAG_TOKEN]:tokenInTableText,[r.EOF_TOKEN]:tokenInTableText},[v]:{[r.CHARACTER_TOKEN]:characterInBody,[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:whitespaceCharacterInBody,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.CAPTION||n===T.COL||n===T.COLGROUP||n===T.TBODY||n===T.TD||n===T.TFOOT||n===T.TH||n===T.THEAD||n===T.TR?e.openElements.hasInTableScope(T.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(T.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=x,e._processToken(t)):startTagInBody(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.CAPTION||n===T.TABLE?e.openElements.hasInTableScope(T.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(T.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=x,n===T.TABLE&&e._processToken(t)):n!==T.BODY&&n!==T.COL&&n!==T.COLGROUP&&n!==T.HTML&&n!==T.TBODY&&n!==T.TD&&n!==T.TFOOT&&n!==T.TH&&n!==T.THEAD&&n!==T.TR&&endTagInBody(e,t)},[r.EOF_TOKEN]:eofInBody},[L]:{[r.CHARACTER_TOKEN]:tokenInColumnGroup,[r.NULL_CHARACTER_TOKEN]:tokenInColumnGroup,[r.WHITESPACE_CHARACTER_TOKEN]:insertCharacters,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.HTML?startTagInBody(e,t):n===T.COL?(e._appendElement(t,g.HTML),t.ackSelfClosing=!0):n===T.TEMPLATE?startTagInHead(e,t):tokenInColumnGroup(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.COLGROUP?e.openElements.currentTagName===T.COLGROUP&&(e.openElements.pop(),e.insertionMode=x):n===T.TEMPLATE?endTagInHead(e,t):n!==T.COL&&tokenInColumnGroup(e,t)},[r.EOF_TOKEN]:eofInBody},[P]:{[r.CHARACTER_TOKEN]:characterInTable,[r.NULL_CHARACTER_TOKEN]:characterInTable,[r.WHITESPACE_CHARACTER_TOKEN]:characterInTable,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.TR?(e.openElements.clearBackToTableBodyContext(),e._insertElement(t,g.HTML),e.insertionMode=M):n===T.TH||n===T.TD?(e.openElements.clearBackToTableBodyContext(),e._insertFakeElement(T.TR),e.insertionMode=M,e._processToken(t)):n===T.CAPTION||n===T.COL||n===T.COLGROUP||n===T.TBODY||n===T.TFOOT||n===T.THEAD?e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=x,e._processToken(t)):startTagInTable(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.TBODY||n===T.TFOOT||n===T.THEAD?e.openElements.hasInTableScope(n)&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=x):n===T.TABLE?e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=x,e._processToken(t)):(n!==T.BODY&&n!==T.CAPTION&&n!==T.COL&&n!==T.COLGROUP||n!==T.HTML&&n!==T.TD&&n!==T.TH&&n!==T.TR)&&endTagInTable(e,t)},[r.EOF_TOKEN]:eofInBody},[M]:{[r.CHARACTER_TOKEN]:characterInTable,[r.NULL_CHARACTER_TOKEN]:characterInTable,[r.WHITESPACE_CHARACTER_TOKEN]:characterInTable,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.TH||n===T.TD?(e.openElements.clearBackToTableRowContext(),e._insertElement(t,g.HTML),e.insertionMode=D,e.activeFormattingElements.insertMarker()):n===T.CAPTION||n===T.COL||n===T.COLGROUP||n===T.TBODY||n===T.TFOOT||n===T.THEAD||n===T.TR?e.openElements.hasInTableScope(T.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=P,e._processToken(t)):startTagInTable(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.TR?e.openElements.hasInTableScope(T.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=P):n===T.TABLE?e.openElements.hasInTableScope(T.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=P,e._processToken(t)):n===T.TBODY||n===T.TFOOT||n===T.THEAD?(e.openElements.hasInTableScope(n)||e.openElements.hasInTableScope(T.TR))&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=P,e._processToken(t)):(n!==T.BODY&&n!==T.CAPTION&&n!==T.COL&&n!==T.COLGROUP||n!==T.HTML&&n!==T.TD&&n!==T.TH)&&endTagInTable(e,t)},[r.EOF_TOKEN]:eofInBody},[D]:{[r.CHARACTER_TOKEN]:characterInBody,[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:whitespaceCharacterInBody,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.CAPTION||n===T.COL||n===T.COLGROUP||n===T.TBODY||n===T.TD||n===T.TFOOT||n===T.TH||n===T.THEAD||n===T.TR?(e.openElements.hasInTableScope(T.TD)||e.openElements.hasInTableScope(T.TH))&&(e._closeTableCell(),e._processToken(t)):startTagInBody(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.TD||n===T.TH?e.openElements.hasInTableScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=M):n===T.TABLE||n===T.TBODY||n===T.TFOOT||n===T.THEAD||n===T.TR?e.openElements.hasInTableScope(n)&&(e._closeTableCell(),e._processToken(t)):n!==T.BODY&&n!==T.CAPTION&&n!==T.COL&&n!==T.COLGROUP&&n!==T.HTML&&endTagInBody(e,t)},[r.EOF_TOKEN]:eofInBody},[w]:{[r.CHARACTER_TOKEN]:insertCharacters,[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:insertCharacters,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:startTagInSelect,[r.END_TAG_TOKEN]:endTagInSelect,[r.EOF_TOKEN]:eofInBody},[H]:{[r.CHARACTER_TOKEN]:insertCharacters,[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:insertCharacters,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.CAPTION||n===T.TABLE||n===T.TBODY||n===T.TFOOT||n===T.THEAD||n===T.TR||n===T.TD||n===T.TH?(e.openElements.popUntilTagNamePopped(T.SELECT),e._resetInsertionMode(),e._processToken(t)):startTagInSelect(e,t)},[r.END_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.CAPTION||n===T.TABLE||n===T.TBODY||n===T.TFOOT||n===T.THEAD||n===T.TR||n===T.TD||n===T.TH?e.openElements.hasInTableScope(n)&&(e.openElements.popUntilTagNamePopped(T.SELECT),e._resetInsertionMode(),e._processToken(t)):endTagInSelect(e,t)},[r.EOF_TOKEN]:eofInBody},[F]:{[r.CHARACTER_TOKEN]:characterInBody,[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:whitespaceCharacterInBody,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;if(n===T.BASE||n===T.BASEFONT||n===T.BGSOUND||n===T.LINK||n===T.META||n===T.NOFRAMES||n===T.SCRIPT||n===T.STYLE||n===T.TEMPLATE||n===T.TITLE)startTagInHead(e,t);else{let r=V[n]||b;e._popTmplInsertionMode(),e._pushTmplInsertionMode(r),e.insertionMode=r,e._processToken(t)}},[r.END_TAG_TOKEN]:function(e,t){t.tagName===T.TEMPLATE&&endTagInHead(e,t)},[r.EOF_TOKEN]:eofInTemplate},[B]:{[r.CHARACTER_TOKEN]:tokenAfterBody,[r.NULL_CHARACTER_TOKEN]:tokenAfterBody,[r.WHITESPACE_CHARACTER_TOKEN]:whitespaceCharacterInBody,[r.COMMENT_TOKEN]:function(e,t){e._appendCommentNode(t,e.openElements.items[0])},[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){t.tagName===T.HTML?startTagInBody(e,t):tokenAfterBody(e,t)},[r.END_TAG_TOKEN]:function(e,t){t.tagName===T.HTML?e.fragmentContext||(e.insertionMode=G):tokenAfterBody(e,t)},[r.EOF_TOKEN]:stopParsing},[U]:{[r.CHARACTER_TOKEN]:ignoreToken,[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:insertCharacters,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.HTML?startTagInBody(e,t):n===T.FRAMESET?e._insertElement(t,g.HTML):n===T.FRAME?(e._appendElement(t,g.HTML),t.ackSelfClosing=!0):n===T.NOFRAMES&&startTagInHead(e,t)},[r.END_TAG_TOKEN]:function(e,t){t.tagName!==T.FRAMESET||e.openElements.isRootHtmlElementCurrent()||(e.openElements.pop(),e.fragmentContext||e.openElements.currentTagName===T.FRAMESET||(e.insertionMode=z))},[r.EOF_TOKEN]:stopParsing},[z]:{[r.CHARACTER_TOKEN]:ignoreToken,[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:insertCharacters,[r.COMMENT_TOKEN]:appendComment,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.HTML?startTagInBody(e,t):n===T.NOFRAMES&&startTagInHead(e,t)},[r.END_TAG_TOKEN]:function(e,t){t.tagName===T.HTML&&(e.insertionMode=K)},[r.EOF_TOKEN]:stopParsing},[G]:{[r.CHARACTER_TOKEN]:tokenAfterAfterBody,[r.NULL_CHARACTER_TOKEN]:tokenAfterAfterBody,[r.WHITESPACE_CHARACTER_TOKEN]:whitespaceCharacterInBody,[r.COMMENT_TOKEN]:appendCommentToDocument,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){t.tagName===T.HTML?startTagInBody(e,t):tokenAfterAfterBody(e,t)},[r.END_TAG_TOKEN]:tokenAfterAfterBody,[r.EOF_TOKEN]:stopParsing},[K]:{[r.CHARACTER_TOKEN]:ignoreToken,[r.NULL_CHARACTER_TOKEN]:ignoreToken,[r.WHITESPACE_CHARACTER_TOKEN]:whitespaceCharacterInBody,[r.COMMENT_TOKEN]:appendCommentToDocument,[r.DOCTYPE_TOKEN]:ignoreToken,[r.START_TAG_TOKEN]:function(e,t){let n=t.tagName;n===T.HTML?startTagInBody(e,t):n===T.NOFRAMES&&startTagInHead(e,t)},[r.END_TAG_TOKEN]:ignoreToken,[r.EOF_TOKEN]:stopParsing}};function callAdoptionAgency(e,t){let n,r;for(let i=0;i<8&&((r=e.activeFormattingElements.getElementEntryInScopeWithTagName(t.tagName))?e.openElements.contains(r.element)?e.openElements.hasInScope(t.tagName)||(r=null):(e.activeFormattingElements.removeEntry(r),r=null):genericEndTagInBody(e,t),n=r);i++){let t=function(e,t){let n=null;for(let r=e.openElements.stackTop;r>=0;r--){let i=e.openElements.items[r];if(i===t.element)break;e._isSpecialElement(i)&&(n=i)}return n||(e.openElements.popUntilElementPopped(t.element),e.activeFormattingElements.removeEntry(t)),n}(e,n);if(!t)break;e.activeFormattingElements.bookmark=n;let r=function(e,t,n){let r=t,i=e.openElements.getCommonAncestor(t);for(let o=0,a=i;a!==n;o++,a=i){i=e.openElements.getCommonAncestor(a);let n=e.activeFormattingElements.getElementEntry(a),s=n&&o>=3,l=!n||s;l?(s&&e.activeFormattingElements.removeEntry(n),e.openElements.remove(a)):(a=function(e,t){let n=e.treeAdapter.getNamespaceURI(t.element),r=e.treeAdapter.createElement(t.token.tagName,n,t.token.attrs);return e.openElements.replace(t.element,r),t.element=r,r}(e,n),r===t&&(e.activeFormattingElements.bookmark=n),e.treeAdapter.detachNode(r),e.treeAdapter.appendChild(a,r),r=a)}return r}(e,t,n.element),i=e.openElements.getCommonAncestor(n.element);e.treeAdapter.detachNode(r),function(e,t,n){if(e._isElementCausesFosterParenting(t))e._fosterParentElement(n);else{let r=e.treeAdapter.getTagName(t),i=e.treeAdapter.getNamespaceURI(t);r===T.TEMPLATE&&i===g.HTML&&(t=e.treeAdapter.getTemplateContent(t)),e.treeAdapter.appendChild(t,n)}}(e,i,r),function(e,t,n){let r=e.treeAdapter.getNamespaceURI(n.element),i=n.token,o=e.treeAdapter.createElement(i.tagName,r,i.attrs);e._adoptNodes(t,o),e.treeAdapter.appendChild(t,o),e.activeFormattingElements.insertElementAfterBookmark(o,n.token),e.activeFormattingElements.removeEntry(n),e.openElements.remove(n.element),e.openElements.insertAfter(t,o)}(e,t,n)}}function ignoreToken(){}function misplacedDoctype(e){e._err(f.misplacedDoctype)}function appendComment(e,t){e._appendCommentNode(t,e.openElements.currentTmplContent||e.openElements.current)}function appendCommentToDocument(e,t){e._appendCommentNode(t,e.document)}function insertCharacters(e,t){e._insertCharacters(t)}function stopParsing(e){e.stopped=!0}function tokenInInitialMode(e,t){e._err(f.missingDoctype,{beforeToken:!0}),e.treeAdapter.setDocumentMode(e.document,m.DOCUMENT_MODE.QUIRKS),e.insertionMode=C,e._processToken(t)}function tokenBeforeHtml(e,t){e._insertFakeRootElement(),e.insertionMode=y,e._processToken(t)}function tokenBeforeHead(e,t){e._insertFakeElement(T.HEAD),e.headElement=e.openElements.current,e.insertionMode=N,e._processToken(t)}function startTagInHead(e,t){let n=t.tagName;n===T.HTML?startTagInBody(e,t):n===T.BASE||n===T.BASEFONT||n===T.BGSOUND||n===T.LINK||n===T.META?(e._appendElement(t,g.HTML),t.ackSelfClosing=!0):n===T.TITLE?e._switchToTextParsing(t,r.MODE.RCDATA):n===T.NOSCRIPT?e.options.scriptingEnabled?e._switchToTextParsing(t,r.MODE.RAWTEXT):(e._insertElement(t,g.HTML),e.insertionMode=S):n===T.NOFRAMES||n===T.STYLE?e._switchToTextParsing(t,r.MODE.RAWTEXT):n===T.SCRIPT?e._switchToTextParsing(t,r.MODE.SCRIPT_DATA):n===T.TEMPLATE?(e._insertTemplate(t,g.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1,e.insertionMode=F,e._pushTmplInsertionMode(F)):n===T.HEAD?e._err(f.misplacedStartTagForHeadElement):tokenInHead(e,t)}function endTagInHead(e,t){let n=t.tagName;n===T.HEAD?(e.openElements.pop(),e.insertionMode=I):n===T.BODY||n===T.BR||n===T.HTML?tokenInHead(e,t):n===T.TEMPLATE&&e.openElements.tmplCount>0?(e.openElements.generateImpliedEndTagsThoroughly(),e.openElements.currentTagName!==T.TEMPLATE&&e._err(f.closingOfElementWithOpenChildElements),e.openElements.popUntilTagNamePopped(T.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e._popTmplInsertionMode(),e._resetInsertionMode()):e._err(f.endTagWithoutMatchingOpenElement)}function tokenInHead(e,t){e.openElements.pop(),e.insertionMode=I,e._processToken(t)}function tokenInHeadNoScript(e,t){let n=t.type===r.EOF_TOKEN?f.openElementsLeftAfterEof:f.disallowedContentInNoscriptInHead;e._err(n),e.openElements.pop(),e.insertionMode=N,e._processToken(t)}function tokenAfterHead(e,t){e._insertFakeElement(T.BODY),e.insertionMode=b,e._processToken(t)}function whitespaceCharacterInBody(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t)}function characterInBody(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t),e.framesetOk=!1}function addressStartTagInBody(e,t){e.openElements.hasInButtonScope(T.P)&&e._closePElement(),e._insertElement(t,g.HTML)}function preStartTagInBody(e,t){e.openElements.hasInButtonScope(T.P)&&e._closePElement(),e._insertElement(t,g.HTML),e.skipNextNewLine=!0,e.framesetOk=!1}function bStartTagInBody(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,g.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}function appletStartTagInBody(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,g.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1}function areaStartTagInBody(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,g.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}function paramStartTagInBody(e,t){e._appendElement(t,g.HTML),t.ackSelfClosing=!0}function noembedStartTagInBody(e,t){e._switchToTextParsing(t,r.MODE.RAWTEXT)}function optgroupStartTagInBody(e,t){e.openElements.currentTagName===T.OPTION&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,g.HTML)}function rbStartTagInBody(e,t){e.openElements.hasInScope(T.RUBY)&&e.openElements.generateImpliedEndTags(),e._insertElement(t,g.HTML)}function genericStartTagInBody(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,g.HTML)}function startTagInBody(e,t){let n=t.tagName;switch(n.length){case 1:n===T.I||n===T.S||n===T.B||n===T.U?bStartTagInBody(e,t):n===T.P?addressStartTagInBody(e,t):n===T.A?function(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(T.A);n&&(callAdoptionAgency(e,t),e.openElements.remove(n.element),e.activeFormattingElements.removeEntry(n)),e._reconstructActiveFormattingElements(),e._insertElement(t,g.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t):genericStartTagInBody(e,t);break;case 2:n===T.DL||n===T.OL||n===T.UL?addressStartTagInBody(e,t):n===T.H1||n===T.H2||n===T.H3||n===T.H4||n===T.H5||n===T.H6?function(e,t){e.openElements.hasInButtonScope(T.P)&&e._closePElement();let n=e.openElements.currentTagName;(n===T.H1||n===T.H2||n===T.H3||n===T.H4||n===T.H5||n===T.H6)&&e.openElements.pop(),e._insertElement(t,g.HTML)}(e,t):n===T.LI||n===T.DD||n===T.DT?function(e,t){e.framesetOk=!1;let n=t.tagName;for(let t=e.openElements.stackTop;t>=0;t--){let r=e.openElements.items[t],i=e.treeAdapter.getTagName(r),o=null;if(n===T.LI&&i===T.LI?o=T.LI:(n===T.DD||n===T.DT)&&(i===T.DD||i===T.DT)&&(o=i),o){e.openElements.generateImpliedEndTagsWithExclusion(o),e.openElements.popUntilTagNamePopped(o);break}if(i!==T.ADDRESS&&i!==T.DIV&&i!==T.P&&e._isSpecialElement(r))break}e.openElements.hasInButtonScope(T.P)&&e._closePElement(),e._insertElement(t,g.HTML)}(e,t):n===T.EM||n===T.TT?bStartTagInBody(e,t):n===T.BR?areaStartTagInBody(e,t):n===T.HR?(e.openElements.hasInButtonScope(T.P)&&e._closePElement(),e._appendElement(t,g.HTML),e.framesetOk=!1,t.ackSelfClosing=!0):n===T.RB?rbStartTagInBody(e,t):n===T.RT||n===T.RP?(e.openElements.hasInScope(T.RUBY)&&e.openElements.generateImpliedEndTagsWithExclusion(T.RTC),e._insertElement(t,g.HTML)):n!==T.TH&&n!==T.TD&&n!==T.TR&&genericStartTagInBody(e,t);break;case 3:n===T.DIV||n===T.DIR||n===T.NAV?addressStartTagInBody(e,t):n===T.PRE?preStartTagInBody(e,t):n===T.BIG?bStartTagInBody(e,t):n===T.IMG||n===T.WBR?areaStartTagInBody(e,t):n===T.XMP?(e.openElements.hasInButtonScope(T.P)&&e._closePElement(),e._reconstructActiveFormattingElements(),e.framesetOk=!1,e._switchToTextParsing(t,r.MODE.RAWTEXT)):n===T.SVG?(e._reconstructActiveFormattingElements(),p.adjustTokenSVGAttrs(t),p.adjustTokenXMLAttrs(t),t.selfClosing?e._appendElement(t,g.SVG):e._insertElement(t,g.SVG),t.ackSelfClosing=!0):n===T.RTC?rbStartTagInBody(e,t):n!==T.COL&&genericStartTagInBody(e,t);break;case 4:n===T.HTML?0===e.openElements.tmplCount&&e.treeAdapter.adoptAttributes(e.openElements.items[0],t.attrs):n===T.BASE||n===T.LINK||n===T.META?startTagInHead(e,t):n===T.BODY?function(e,t){let n=e.openElements.tryPeekProperlyNestedBodyElement();n&&0===e.openElements.tmplCount&&(e.framesetOk=!1,e.treeAdapter.adoptAttributes(n,t.attrs))}(e,t):n===T.MAIN||n===T.MENU?addressStartTagInBody(e,t):n===T.FORM?function(e,t){let n=e.openElements.tmplCount>0;e.formElement&&!n||(e.openElements.hasInButtonScope(T.P)&&e._closePElement(),e._insertElement(t,g.HTML),n||(e.formElement=e.openElements.current))}(e,t):n===T.CODE||n===T.FONT?bStartTagInBody(e,t):n===T.NOBR?(e._reconstructActiveFormattingElements(),e.openElements.hasInScope(T.NOBR)&&(callAdoptionAgency(e,t),e._reconstructActiveFormattingElements()),e._insertElement(t,g.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)):n===T.AREA?areaStartTagInBody(e,t):n===T.MATH?(e._reconstructActiveFormattingElements(),p.adjustTokenMathMLAttrs(t),p.adjustTokenXMLAttrs(t),t.selfClosing?e._appendElement(t,g.MATHML):e._insertElement(t,g.MATHML),t.ackSelfClosing=!0):n===T.MENU?(e.openElements.hasInButtonScope(T.P)&&e._closePElement(),e._insertElement(t,g.HTML)):n!==T.HEAD&&genericStartTagInBody(e,t);break;case 5:n===T.STYLE||n===T.TITLE?startTagInHead(e,t):n===T.ASIDE?addressStartTagInBody(e,t):n===T.SMALL?bStartTagInBody(e,t):n===T.TABLE?(e.treeAdapter.getDocumentMode(e.document)!==m.DOCUMENT_MODE.QUIRKS&&e.openElements.hasInButtonScope(T.P)&&e._closePElement(),e._insertElement(t,g.HTML),e.framesetOk=!1,e.insertionMode=x):n===T.EMBED?areaStartTagInBody(e,t):n===T.INPUT?function(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,g.HTML);let n=r.getTokenAttr(t,E.TYPE);n&&n.toLowerCase()===A||(e.framesetOk=!1),t.ackSelfClosing=!0}(e,t):n===T.PARAM||n===T.TRACK?paramStartTagInBody(e,t):n===T.IMAGE?(t.tagName=T.IMG,areaStartTagInBody(e,t)):n!==T.FRAME&&n!==T.TBODY&&n!==T.TFOOT&&n!==T.THEAD&&genericStartTagInBody(e,t);break;case 6:n===T.SCRIPT?startTagInHead(e,t):n===T.CENTER||n===T.FIGURE||n===T.FOOTER||n===T.HEADER||n===T.HGROUP||n===T.DIALOG?addressStartTagInBody(e,t):n===T.BUTTON?(e.openElements.hasInScope(T.BUTTON)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(T.BUTTON)),e._reconstructActiveFormattingElements(),e._insertElement(t,g.HTML),e.framesetOk=!1):n===T.STRIKE||n===T.STRONG?bStartTagInBody(e,t):n===T.APPLET||n===T.OBJECT?appletStartTagInBody(e,t):n===T.KEYGEN?areaStartTagInBody(e,t):n===T.SOURCE?paramStartTagInBody(e,t):n===T.IFRAME?(e.framesetOk=!1,e._switchToTextParsing(t,r.MODE.RAWTEXT)):n===T.SELECT?(e._reconstructActiveFormattingElements(),e._insertElement(t,g.HTML),e.framesetOk=!1,e.insertionMode===x||e.insertionMode===v||e.insertionMode===P||e.insertionMode===M||e.insertionMode===D?e.insertionMode=H:e.insertionMode=w):n===T.OPTION?optgroupStartTagInBody(e,t):genericStartTagInBody(e,t);break;case 7:n===T.BGSOUND?startTagInHead(e,t):n===T.DETAILS||n===T.ADDRESS||n===T.ARTICLE||n===T.SECTION||n===T.SUMMARY?addressStartTagInBody(e,t):n===T.LISTING?preStartTagInBody(e,t):n===T.MARQUEE?appletStartTagInBody(e,t):n===T.NOEMBED?noembedStartTagInBody(e,t):n!==T.CAPTION&&genericStartTagInBody(e,t);break;case 8:n===T.BASEFONT?startTagInHead(e,t):n===T.FRAMESET?function(e,t){let n=e.openElements.tryPeekProperlyNestedBodyElement();e.framesetOk&&n&&(e.treeAdapter.detachNode(n),e.openElements.popAllUpToHtmlElement(),e._insertElement(t,g.HTML),e.insertionMode=U)}(e,t):n===T.FIELDSET?addressStartTagInBody(e,t):n===T.TEXTAREA?(e._insertElement(t,g.HTML),e.skipNextNewLine=!0,e.tokenizer.state=r.MODE.RCDATA,e.originalInsertionMode=e.insertionMode,e.framesetOk=!1,e.insertionMode=O):n===T.TEMPLATE?startTagInHead(e,t):n===T.NOSCRIPT?e.options.scriptingEnabled?noembedStartTagInBody(e,t):genericStartTagInBody(e,t):n===T.OPTGROUP?optgroupStartTagInBody(e,t):n!==T.COLGROUP&&genericStartTagInBody(e,t);break;case 9:n===T.PLAINTEXT?(e.openElements.hasInButtonScope(T.P)&&e._closePElement(),e._insertElement(t,g.HTML),e.tokenizer.state=r.MODE.PLAINTEXT):genericStartTagInBody(e,t);break;case 10:n===T.BLOCKQUOTE||n===T.FIGCAPTION?addressStartTagInBody(e,t):genericStartTagInBody(e,t);break;default:genericStartTagInBody(e,t)}}function addressEndTagInBody(e,t){let n=t.tagName;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n))}function appletEndTagInBody(e,t){let n=t.tagName;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker())}function genericEndTagInBody(e,t){let n=t.tagName;for(let t=e.openElements.stackTop;t>0;t--){let r=e.openElements.items[t];if(e.treeAdapter.getTagName(r)===n){e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilElementPopped(r);break}if(e._isSpecialElement(r))break}}function endTagInBody(e,t){let n=t.tagName;switch(n.length){case 1:n===T.A||n===T.B||n===T.I||n===T.S||n===T.U?callAdoptionAgency(e,t):n===T.P?(e.openElements.hasInButtonScope(T.P)||e._insertFakeElement(T.P),e._closePElement()):genericEndTagInBody(e,t);break;case 2:n===T.DL||n===T.UL||n===T.OL?addressEndTagInBody(e,t):n===T.LI?e.openElements.hasInListItemScope(T.LI)&&(e.openElements.generateImpliedEndTagsWithExclusion(T.LI),e.openElements.popUntilTagNamePopped(T.LI)):n===T.DD||n===T.DT?function(e,t){let n=t.tagName;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilTagNamePopped(n))}(e,t):n===T.H1||n===T.H2||n===T.H3||n===T.H4||n===T.H5||n===T.H6?e.openElements.hasNumberedHeaderInScope()&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilNumberedHeaderPopped()):n===T.BR?(e._reconstructActiveFormattingElements(),e._insertFakeElement(T.BR),e.openElements.pop(),e.framesetOk=!1):n===T.EM||n===T.TT?callAdoptionAgency(e,t):genericEndTagInBody(e,t);break;case 3:n===T.BIG?callAdoptionAgency(e,t):n===T.DIR||n===T.DIV||n===T.NAV||n===T.PRE?addressEndTagInBody(e,t):genericEndTagInBody(e,t);break;case 4:n===T.BODY?e.openElements.hasInScope(T.BODY)&&(e.insertionMode=B):n===T.HTML?e.openElements.hasInScope(T.BODY)&&(e.insertionMode=B,e._processToken(t)):n===T.FORM?function(e){let t=e.openElements.tmplCount>0,n=e.formElement;t||(e.formElement=null),(n||t)&&e.openElements.hasInScope(T.FORM)&&(e.openElements.generateImpliedEndTags(),t?e.openElements.popUntilTagNamePopped(T.FORM):e.openElements.remove(n))}(e,t):n===T.CODE||n===T.FONT||n===T.NOBR?callAdoptionAgency(e,t):n===T.MAIN||n===T.MENU?addressEndTagInBody(e,t):genericEndTagInBody(e,t);break;case 5:n===T.ASIDE?addressEndTagInBody(e,t):n===T.SMALL?callAdoptionAgency(e,t):genericEndTagInBody(e,t);break;case 6:n===T.CENTER||n===T.FIGURE||n===T.FOOTER||n===T.HEADER||n===T.HGROUP||n===T.DIALOG?addressEndTagInBody(e,t):n===T.APPLET||n===T.OBJECT?appletEndTagInBody(e,t):n===T.STRIKE||n===T.STRONG?callAdoptionAgency(e,t):genericEndTagInBody(e,t);break;case 7:n===T.ADDRESS||n===T.ARTICLE||n===T.DETAILS||n===T.SECTION||n===T.SUMMARY||n===T.LISTING?addressEndTagInBody(e,t):n===T.MARQUEE?appletEndTagInBody(e,t):genericEndTagInBody(e,t);break;case 8:n===T.FIELDSET?addressEndTagInBody(e,t):n===T.TEMPLATE?endTagInHead(e,t):genericEndTagInBody(e,t);break;case 10:n===T.BLOCKQUOTE||n===T.FIGCAPTION?addressEndTagInBody(e,t):genericEndTagInBody(e,t);break;default:genericEndTagInBody(e,t)}}function eofInBody(e,t){e.tmplInsertionModeStackTop>-1?eofInTemplate(e,t):e.stopped=!0}function characterInTable(e,t){let n=e.openElements.currentTagName;n===T.TABLE||n===T.TBODY||n===T.TFOOT||n===T.THEAD||n===T.TR?(e.pendingCharacterTokens=[],e.hasNonWhitespacePendingCharacterToken=!1,e.originalInsertionMode=e.insertionMode,e.insertionMode=R,e._processToken(t)):tokenInTable(e,t)}function startTagInTable(e,t){let n=t.tagName;switch(n.length){case 2:n===T.TD||n===T.TH||n===T.TR?(e.openElements.clearBackToTableContext(),e._insertFakeElement(T.TBODY),e.insertionMode=P,e._processToken(t)):tokenInTable(e,t);break;case 3:n===T.COL?(e.openElements.clearBackToTableContext(),e._insertFakeElement(T.COLGROUP),e.insertionMode=L,e._processToken(t)):tokenInTable(e,t);break;case 4:n===T.FORM?e.formElement||0!==e.openElements.tmplCount||(e._insertElement(t,g.HTML),e.formElement=e.openElements.current,e.openElements.pop()):tokenInTable(e,t);break;case 5:n===T.TABLE?e.openElements.hasInTableScope(T.TABLE)&&(e.openElements.popUntilTagNamePopped(T.TABLE),e._resetInsertionMode(),e._processToken(t)):n===T.STYLE?startTagInHead(e,t):n===T.TBODY||n===T.TFOOT||n===T.THEAD?(e.openElements.clearBackToTableContext(),e._insertElement(t,g.HTML),e.insertionMode=P):n===T.INPUT?function(e,t){let n=r.getTokenAttr(t,E.TYPE);n&&n.toLowerCase()===A?e._appendElement(t,g.HTML):tokenInTable(e,t),t.ackSelfClosing=!0}(e,t):tokenInTable(e,t);break;case 6:n===T.SCRIPT?startTagInHead(e,t):tokenInTable(e,t);break;case 7:n===T.CAPTION?(e.openElements.clearBackToTableContext(),e.activeFormattingElements.insertMarker(),e._insertElement(t,g.HTML),e.insertionMode=v):tokenInTable(e,t);break;case 8:n===T.COLGROUP?(e.openElements.clearBackToTableContext(),e._insertElement(t,g.HTML),e.insertionMode=L):n===T.TEMPLATE?startTagInHead(e,t):tokenInTable(e,t);break;default:tokenInTable(e,t)}}function endTagInTable(e,t){let n=t.tagName;n===T.TABLE?e.openElements.hasInTableScope(T.TABLE)&&(e.openElements.popUntilTagNamePopped(T.TABLE),e._resetInsertionMode()):n===T.TEMPLATE?endTagInHead(e,t):n!==T.BODY&&n!==T.CAPTION&&n!==T.COL&&n!==T.COLGROUP&&n!==T.HTML&&n!==T.TBODY&&n!==T.TD&&n!==T.TFOOT&&n!==T.TH&&n!==T.THEAD&&n!==T.TR&&tokenInTable(e,t)}function tokenInTable(e,t){let n=e.fosterParentingEnabled;e.fosterParentingEnabled=!0,e._processTokenInBodyMode(t),e.fosterParentingEnabled=n}function tokenInTableText(e,t){let n=0;if(e.hasNonWhitespacePendingCharacterToken)for(;n<e.pendingCharacterTokens.length;n++)tokenInTable(e,e.pendingCharacterTokens[n]);else for(;n<e.pendingCharacterTokens.length;n++)e._insertCharacters(e.pendingCharacterTokens[n]);e.insertionMode=e.originalInsertionMode,e._processToken(t)}function tokenInColumnGroup(e,t){e.openElements.currentTagName===T.COLGROUP&&(e.openElements.pop(),e.insertionMode=x,e._processToken(t))}function startTagInSelect(e,t){let n=t.tagName;n===T.HTML?startTagInBody(e,t):n===T.OPTION?(e.openElements.currentTagName===T.OPTION&&e.openElements.pop(),e._insertElement(t,g.HTML)):n===T.OPTGROUP?(e.openElements.currentTagName===T.OPTION&&e.openElements.pop(),e.openElements.currentTagName===T.OPTGROUP&&e.openElements.pop(),e._insertElement(t,g.HTML)):n===T.INPUT||n===T.KEYGEN||n===T.TEXTAREA||n===T.SELECT?e.openElements.hasInSelectScope(T.SELECT)&&(e.openElements.popUntilTagNamePopped(T.SELECT),e._resetInsertionMode(),n!==T.SELECT&&e._processToken(t)):(n===T.SCRIPT||n===T.TEMPLATE)&&startTagInHead(e,t)}function endTagInSelect(e,t){let n=t.tagName;if(n===T.OPTGROUP){let t=e.openElements.items[e.openElements.stackTop-1],n=t&&e.treeAdapter.getTagName(t);e.openElements.currentTagName===T.OPTION&&n===T.OPTGROUP&&e.openElements.pop(),e.openElements.currentTagName===T.OPTGROUP&&e.openElements.pop()}else n===T.OPTION?e.openElements.currentTagName===T.OPTION&&e.openElements.pop():n===T.SELECT&&e.openElements.hasInSelectScope(T.SELECT)?(e.openElements.popUntilTagNamePopped(T.SELECT),e._resetInsertionMode()):n===T.TEMPLATE&&endTagInHead(e,t)}function eofInTemplate(e,t){e.openElements.tmplCount>0?(e.openElements.popUntilTagNamePopped(T.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e._popTmplInsertionMode(),e._resetInsertionMode(),e._processToken(t)):e.stopped=!0}function tokenAfterBody(e,t){e.insertionMode=b,e._processToken(t)}function tokenAfterAfterBody(e,t){e.insertionMode=b,e._processToken(t)}e.exports=class{constructor(e){this.options=u(_,e),this.treeAdapter=this.options.treeAdapter,this.pendingScript=null,this.options.sourceCodeLocationInfo&&l.install(this,a),this.options.onParseError&&l.install(this,s,{onParseError:this.options.onParseError})}parse(e){let t=this.treeAdapter.createDocument();return this._bootstrap(t,null),this.tokenizer.write(e,!0),this._runParsingLoop(null),t}parseFragment(e,t){t||(t=this.treeAdapter.createElement(T.TEMPLATE,g.HTML,[]));let n=this.treeAdapter.createElement("documentmock",g.HTML,[]);this._bootstrap(n,t),this.treeAdapter.getTagName(t)===T.TEMPLATE&&this._pushTmplInsertionMode(F),this._initTokenizerForFragmentParsing(),this._insertFakeRootElement(),this._resetInsertionMode(),this._findFormInFragmentContext(),this.tokenizer.write(e,!0),this._runParsingLoop(null);let r=this.treeAdapter.getFirstChild(n),i=this.treeAdapter.createDocumentFragment();return this._adoptNodes(r,i),i}_bootstrap(e,t){this.tokenizer=new r(this.options),this.stopped=!1,this.insertionMode=k,this.originalInsertionMode="",this.document=e,this.fragmentContext=t,this.headElement=null,this.formElement=null,this.openElements=new i(this.document,this.treeAdapter),this.activeFormattingElements=new o(this.treeAdapter),this.tmplInsertionModeStack=[],this.tmplInsertionModeStackTop=-1,this.currentTmplInsertionMode=null,this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1}_err(){}_runParsingLoop(e){for(;!this.stopped;){this._setupTokenizerCDATAMode();let t=this.tokenizer.getNextToken();if(t.type===r.HIBERNATION_TOKEN)break;if(this.skipNextNewLine&&(this.skipNextNewLine=!1,t.type===r.WHITESPACE_CHARACTER_TOKEN&&"\n"===t.chars[0])){if(1===t.chars.length)continue;t.chars=t.chars.substr(1)}if(this._processInputToken(t),e&&this.pendingScript)break}}runParsingLoopForCurrentChunk(e,t){if(this._runParsingLoop(t),t&&this.pendingScript){let e=this.pendingScript;this.pendingScript=null,t(e);return}e&&e()}_setupTokenizerCDATAMode(){let e=this._getAdjustedCurrentElement();this.tokenizer.allowCDATA=e&&e!==this.document&&this.treeAdapter.getNamespaceURI(e)!==g.HTML&&!this._isIntegrationPoint(e)}_switchToTextParsing(e,t){this._insertElement(e,g.HTML),this.tokenizer.state=t,this.originalInsertionMode=this.insertionMode,this.insertionMode=O}switchToPlaintextParsing(){this.insertionMode=O,this.originalInsertionMode=b,this.tokenizer.state=r.MODE.PLAINTEXT}_getAdjustedCurrentElement(){return 0===this.openElements.stackTop&&this.fragmentContext?this.fragmentContext:this.openElements.current}_findFormInFragmentContext(){let e=this.fragmentContext;do{if(this.treeAdapter.getTagName(e)===T.FORM){this.formElement=e;break}e=this.treeAdapter.getParentNode(e)}while(e)}_initTokenizerForFragmentParsing(){if(this.treeAdapter.getNamespaceURI(this.fragmentContext)===g.HTML){let e=this.treeAdapter.getTagName(this.fragmentContext);e===T.TITLE||e===T.TEXTAREA?this.tokenizer.state=r.MODE.RCDATA:e===T.STYLE||e===T.XMP||e===T.IFRAME||e===T.NOEMBED||e===T.NOFRAMES||e===T.NOSCRIPT?this.tokenizer.state=r.MODE.RAWTEXT:e===T.SCRIPT?this.tokenizer.state=r.MODE.SCRIPT_DATA:e===T.PLAINTEXT&&(this.tokenizer.state=r.MODE.PLAINTEXT)}}_setDocumentType(e){let t=e.name||"",n=e.publicId||"",r=e.systemId||"";this.treeAdapter.setDocumentType(this.document,t,n,r)}_attachElementToTree(e){if(this._shouldFosterParentOnInsertion())this._fosterParentElement(e);else{let t=this.openElements.currentTmplContent||this.openElements.current;this.treeAdapter.appendChild(t,e)}}_appendElement(e,t){let n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n)}_insertElement(e,t){let n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n),this.openElements.push(n)}_insertFakeElement(e){let t=this.treeAdapter.createElement(e,g.HTML,[]);this._attachElementToTree(t),this.openElements.push(t)}_insertTemplate(e){let t=this.treeAdapter.createElement(e.tagName,g.HTML,e.attrs),n=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(t,n),this._attachElementToTree(t),this.openElements.push(t)}_insertFakeRootElement(){let e=this.treeAdapter.createElement(T.HTML,g.HTML,[]);this.treeAdapter.appendChild(this.openElements.current,e),this.openElements.push(e)}_appendCommentNode(e,t){let n=this.treeAdapter.createCommentNode(e.data);this.treeAdapter.appendChild(t,n)}_insertCharacters(e){if(this._shouldFosterParentOnInsertion())this._fosterParentText(e.chars);else{let t=this.openElements.currentTmplContent||this.openElements.current;this.treeAdapter.insertText(t,e.chars)}}_adoptNodes(e,t){for(let n=this.treeAdapter.getFirstChild(e);n;n=this.treeAdapter.getFirstChild(e))this.treeAdapter.detachNode(n),this.treeAdapter.appendChild(t,n)}_shouldProcessTokenInForeignContent(e){let t=this._getAdjustedCurrentElement();if(!t||t===this.document)return!1;let n=this.treeAdapter.getNamespaceURI(t);if(n===g.HTML||this.treeAdapter.getTagName(t)===T.ANNOTATION_XML&&n===g.MATHML&&e.type===r.START_TAG_TOKEN&&e.tagName===T.SVG)return!1;let i=e.type===r.CHARACTER_TOKEN||e.type===r.NULL_CHARACTER_TOKEN||e.type===r.WHITESPACE_CHARACTER_TOKEN,o=e.type===r.START_TAG_TOKEN&&e.tagName!==T.MGLYPH&&e.tagName!==T.MALIGNMARK;return!((o||i)&&this._isIntegrationPoint(t,g.MATHML)||(e.type===r.START_TAG_TOKEN||i)&&this._isIntegrationPoint(t,g.HTML))&&e.type!==r.EOF_TOKEN}_processToken(e){Y[this.insertionMode][e.type](this,e)}_processTokenInBodyMode(e){Y[b][e.type](this,e)}_processTokenInForeignContent(e){e.type===r.CHARACTER_TOKEN?(this._insertCharacters(e),this.framesetOk=!1):e.type===r.NULL_CHARACTER_TOKEN?(e.chars=d.REPLACEMENT_CHARACTER,this._insertCharacters(e)):e.type===r.WHITESPACE_CHARACTER_TOKEN?insertCharacters(this,e):e.type===r.COMMENT_TOKEN?appendComment(this,e):e.type===r.START_TAG_TOKEN?function(e,t){if(p.causesExit(t)&&!e.fragmentContext){for(;e.treeAdapter.getNamespaceURI(e.openElements.current)!==g.HTML&&!e._isIntegrationPoint(e.openElements.current);)e.openElements.pop();e._processToken(t)}else{let n=e._getAdjustedCurrentElement(),r=e.treeAdapter.getNamespaceURI(n);r===g.MATHML?p.adjustTokenMathMLAttrs(t):r===g.SVG&&(p.adjustTokenSVGTagName(t),p.adjustTokenSVGAttrs(t)),p.adjustTokenXMLAttrs(t),t.selfClosing?e._appendElement(t,r):e._insertElement(t,r),t.ackSelfClosing=!0}}(this,e):e.type===r.END_TAG_TOKEN&&function(e,t){for(let n=e.openElements.stackTop;n>0;n--){let r=e.openElements.items[n];if(e.treeAdapter.getNamespaceURI(r)===g.HTML){e._processToken(t);break}if(e.treeAdapter.getTagName(r).toLowerCase()===t.tagName){e.openElements.popUntilElementPopped(r);break}}}(this,e)}_processInputToken(e){this._shouldProcessTokenInForeignContent(e)?this._processTokenInForeignContent(e):this._processToken(e),e.type===r.START_TAG_TOKEN&&e.selfClosing&&!e.ackSelfClosing&&this._err(f.nonVoidHtmlElementStartTagWithTrailingSolidus)}_isIntegrationPoint(e,t){let n=this.treeAdapter.getTagName(e),r=this.treeAdapter.getNamespaceURI(e),i=this.treeAdapter.getAttrList(e);return p.isIntegrationPoint(n,r,i,t)}_reconstructActiveFormattingElements(){let e=this.activeFormattingElements.length;if(e){let t=e,n=null;do if(t--,(n=this.activeFormattingElements.entries[t]).type===o.MARKER_ENTRY||this.openElements.contains(n.element)){t++;break}while(t>0);for(let r=t;r<e;r++)n=this.activeFormattingElements.entries[r],this._insertElement(n.token,this.treeAdapter.getNamespaceURI(n.element)),n.element=this.openElements.current}}_closeTableCell(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=M}_closePElement(){this.openElements.generateImpliedEndTagsWithExclusion(T.P),this.openElements.popUntilTagNamePopped(T.P)}_resetInsertionMode(){for(let e=this.openElements.stackTop,t=!1;e>=0;e--){let n=this.openElements.items[e];0===e&&(t=!0,this.fragmentContext&&(n=this.fragmentContext));let r=this.treeAdapter.getTagName(n),i=j[r];if(i){this.insertionMode=i;break}if(t||r!==T.TD&&r!==T.TH){if(t||r!==T.HEAD){if(r===T.SELECT){this._resetInsertionModeForSelect(e);break}if(r===T.TEMPLATE){this.insertionMode=this.currentTmplInsertionMode;break}if(r===T.HTML){this.insertionMode=this.headElement?I:y;break}else if(t){this.insertionMode=b;break}}else{this.insertionMode=N;break}}else{this.insertionMode=D;break}}}_resetInsertionModeForSelect(e){if(e>0)for(let t=e-1;t>0;t--){let e=this.openElements.items[t],n=this.treeAdapter.getTagName(e);if(n===T.TEMPLATE)break;if(n===T.TABLE){this.insertionMode=H;return}}this.insertionMode=w}_pushTmplInsertionMode(e){this.tmplInsertionModeStack.push(e),this.tmplInsertionModeStackTop++,this.currentTmplInsertionMode=e}_popTmplInsertionMode(){this.tmplInsertionModeStack.pop(),this.tmplInsertionModeStackTop--,this.currentTmplInsertionMode=this.tmplInsertionModeStack[this.tmplInsertionModeStackTop]}_isElementCausesFosterParenting(e){let t=this.treeAdapter.getTagName(e);return t===T.TABLE||t===T.TBODY||t===T.TFOOT||t===T.THEAD||t===T.TR}_shouldFosterParentOnInsertion(){return this.fosterParentingEnabled&&this._isElementCausesFosterParenting(this.openElements.current)}_findFosterParentingLocation(){let e={parent:null,beforeElement:null};for(let t=this.openElements.stackTop;t>=0;t--){let n=this.openElements.items[t],r=this.treeAdapter.getTagName(n),i=this.treeAdapter.getNamespaceURI(n);if(r===T.TEMPLATE&&i===g.HTML){e.parent=this.treeAdapter.getTemplateContent(n);break}if(r===T.TABLE){e.parent=this.treeAdapter.getParentNode(n),e.parent?e.beforeElement=n:e.parent=this.openElements.items[t-1];break}}return e.parent||(e.parent=this.openElements.items[0]),e}_fosterParentElement(e){let t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertBefore(t.parent,e,t.beforeElement):this.treeAdapter.appendChild(t.parent,e)}_fosterParentText(e){let t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertTextBefore(t.parent,e,t.beforeElement):this.treeAdapter.insertText(t.parent,e)}_isSpecialElement(e){let t=this.treeAdapter.getTagName(e),n=this.treeAdapter.getNamespaceURI(e);return m.SPECIAL_ELEMENTS[n][t]}}},6519:function(e,t,n){"use strict";let r=n(6152),i=r.TAG_NAMES,o=r.NAMESPACES;function isImpliedEndTagRequired(e){switch(e.length){case 1:return e===i.P;case 2:return e===i.RB||e===i.RP||e===i.RT||e===i.DD||e===i.DT||e===i.LI;case 3:return e===i.RTC;case 6:return e===i.OPTION;case 8:return e===i.OPTGROUP}return!1}function isScopingElement(e,t){switch(e.length){case 2:if(e===i.TD||e===i.TH)return t===o.HTML;if(e===i.MI||e===i.MO||e===i.MN||e===i.MS)return t===o.MATHML;break;case 4:if(e===i.HTML)return t===o.HTML;if(e===i.DESC)return t===o.SVG;break;case 5:if(e===i.TABLE)return t===o.HTML;if(e===i.MTEXT)return t===o.MATHML;if(e===i.TITLE)return t===o.SVG;break;case 6:return(e===i.APPLET||e===i.OBJECT)&&t===o.HTML;case 7:return(e===i.CAPTION||e===i.MARQUEE)&&t===o.HTML;case 8:return e===i.TEMPLATE&&t===o.HTML;case 13:return e===i.FOREIGN_OBJECT&&t===o.SVG;case 14:return e===i.ANNOTATION_XML&&t===o.MATHML}return!1}e.exports=class{constructor(e,t){this.stackTop=-1,this.items=[],this.current=e,this.currentTagName=null,this.currentTmplContent=null,this.tmplCount=0,this.treeAdapter=t}_indexOf(e){let t=-1;for(let n=this.stackTop;n>=0;n--)if(this.items[n]===e){t=n;break}return t}_isInTemplate(){return this.currentTagName===i.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===o.HTML}_updateCurrentElement(){this.current=this.items[this.stackTop],this.currentTagName=this.current&&this.treeAdapter.getTagName(this.current),this.currentTmplContent=this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):null}push(e){this.items[++this.stackTop]=e,this._updateCurrentElement(),this._isInTemplate()&&this.tmplCount++}pop(){this.stackTop--,this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this._updateCurrentElement()}replace(e,t){let n=this._indexOf(e);this.items[n]=t,n===this.stackTop&&this._updateCurrentElement()}insertAfter(e,t){let n=this._indexOf(e)+1;this.items.splice(n,0,t),n===++this.stackTop&&this._updateCurrentElement()}popUntilTagNamePopped(e){for(;this.stackTop>-1;){let t=this.currentTagName,n=this.treeAdapter.getNamespaceURI(this.current);if(this.pop(),t===e&&n===o.HTML)break}}popUntilElementPopped(e){for(;this.stackTop>-1;){let t=this.current;if(this.pop(),t===e)break}}popUntilNumberedHeaderPopped(){for(;this.stackTop>-1;){let e=this.currentTagName,t=this.treeAdapter.getNamespaceURI(this.current);if(this.pop(),e===i.H1||e===i.H2||e===i.H3||e===i.H4||e===i.H5||e===i.H6&&t===o.HTML)break}}popUntilTableCellPopped(){for(;this.stackTop>-1;){let e=this.currentTagName,t=this.treeAdapter.getNamespaceURI(this.current);if(this.pop(),e===i.TD||e===i.TH&&t===o.HTML)break}}popAllUpToHtmlElement(){this.stackTop=0,this._updateCurrentElement()}clearBackToTableContext(){for(;this.currentTagName!==i.TABLE&&this.currentTagName!==i.TEMPLATE&&this.currentTagName!==i.HTML||this.treeAdapter.getNamespaceURI(this.current)!==o.HTML;)this.pop()}clearBackToTableBodyContext(){for(;this.currentTagName!==i.TBODY&&this.currentTagName!==i.TFOOT&&this.currentTagName!==i.THEAD&&this.currentTagName!==i.TEMPLATE&&this.currentTagName!==i.HTML||this.treeAdapter.getNamespaceURI(this.current)!==o.HTML;)this.pop()}clearBackToTableRowContext(){for(;this.currentTagName!==i.TR&&this.currentTagName!==i.TEMPLATE&&this.currentTagName!==i.HTML||this.treeAdapter.getNamespaceURI(this.current)!==o.HTML;)this.pop()}remove(e){for(let t=this.stackTop;t>=0;t--)if(this.items[t]===e){this.items.splice(t,1),this.stackTop--,this._updateCurrentElement();break}}tryPeekProperlyNestedBodyElement(){let e=this.items[1];return e&&this.treeAdapter.getTagName(e)===i.BODY?e:null}contains(e){return this._indexOf(e)>-1}getCommonAncestor(e){let t=this._indexOf(e);return--t>=0?this.items[t]:null}isRootHtmlElementCurrent(){return 0===this.stackTop&&this.currentTagName===i.HTML}hasInScope(e){for(let t=this.stackTop;t>=0;t--){let n=this.treeAdapter.getTagName(this.items[t]),r=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&r===o.HTML)break;if(isScopingElement(n,r))return!1}return!0}hasNumberedHeaderInScope(){for(let e=this.stackTop;e>=0;e--){let t=this.treeAdapter.getTagName(this.items[e]),n=this.treeAdapter.getNamespaceURI(this.items[e]);if((t===i.H1||t===i.H2||t===i.H3||t===i.H4||t===i.H5||t===i.H6)&&n===o.HTML)break;if(isScopingElement(t,n))return!1}return!0}hasInListItemScope(e){for(let t=this.stackTop;t>=0;t--){let n=this.treeAdapter.getTagName(this.items[t]),r=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&r===o.HTML)break;if((n===i.UL||n===i.OL)&&r===o.HTML||isScopingElement(n,r))return!1}return!0}hasInButtonScope(e){for(let t=this.stackTop;t>=0;t--){let n=this.treeAdapter.getTagName(this.items[t]),r=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&r===o.HTML)break;if(n===i.BUTTON&&r===o.HTML||isScopingElement(n,r))return!1}return!0}hasInTableScope(e){for(let t=this.stackTop;t>=0;t--){let n=this.treeAdapter.getTagName(this.items[t]),r=this.treeAdapter.getNamespaceURI(this.items[t]);if(r===o.HTML){if(n===e)break;if(n===i.TABLE||n===i.TEMPLATE||n===i.HTML)return!1}}return!0}hasTableBodyContextInTableScope(){for(let e=this.stackTop;e>=0;e--){let t=this.treeAdapter.getTagName(this.items[e]),n=this.treeAdapter.getNamespaceURI(this.items[e]);if(n===o.HTML){if(t===i.TBODY||t===i.THEAD||t===i.TFOOT)break;if(t===i.TABLE||t===i.HTML)return!1}}return!0}hasInSelectScope(e){for(let t=this.stackTop;t>=0;t--){let n=this.treeAdapter.getTagName(this.items[t]),r=this.treeAdapter.getNamespaceURI(this.items[t]);if(r===o.HTML){if(n===e)break;if(n!==i.OPTION&&n!==i.OPTGROUP)return!1}}return!0}generateImpliedEndTags(){for(;isImpliedEndTagRequired(this.currentTagName);)this.pop()}generateImpliedEndTagsThoroughly(){for(;function(e){switch(e.length){case 1:return e===i.P;case 2:return e===i.RB||e===i.RP||e===i.RT||e===i.DD||e===i.DT||e===i.LI||e===i.TD||e===i.TH||e===i.TR;case 3:return e===i.RTC;case 5:return e===i.TBODY||e===i.TFOOT||e===i.THEAD;case 6:return e===i.OPTION;case 7:return e===i.CAPTION;case 8:return e===i.OPTGROUP||e===i.COLGROUP}return!1}(this.currentTagName);)this.pop()}generateImpliedEndTagsWithExclusion(e){for(;isImpliedEndTagRequired(this.currentTagName)&&this.currentTagName!==e;)this.pop()}}},5763:function(e,t,n){"use strict";let r=n(7118),i=n(4284),o=n(5482),a=n(1734),s=i.CODE_POINTS,l=i.CODE_POINT_SEQUENCES,c={128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376},u="DATA_STATE",h="RCDATA_STATE",p="RAWTEXT_STATE",f="SCRIPT_DATA_STATE",d="PLAINTEXT_STATE",m="TAG_OPEN_STATE",T="END_TAG_OPEN_STATE",g="TAG_NAME_STATE",E="RCDATA_LESS_THAN_SIGN_STATE",_="RCDATA_END_TAG_OPEN_STATE",A="RCDATA_END_TAG_NAME_STATE",k="RAWTEXT_LESS_THAN_SIGN_STATE",C="RAWTEXT_END_TAG_OPEN_STATE",y="RAWTEXT_END_TAG_NAME_STATE",N="SCRIPT_DATA_LESS_THAN_SIGN_STATE",S="SCRIPT_DATA_END_TAG_OPEN_STATE",I="SCRIPT_DATA_END_TAG_NAME_STATE",b="SCRIPT_DATA_ESCAPE_START_STATE",O="SCRIPT_DATA_ESCAPE_START_DASH_STATE",x="SCRIPT_DATA_ESCAPED_STATE",R="SCRIPT_DATA_ESCAPED_DASH_STATE",v="SCRIPT_DATA_ESCAPED_DASH_DASH_STATE",L="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN_STATE",P="SCRIPT_DATA_ESCAPED_END_TAG_OPEN_STATE",M="SCRIPT_DATA_ESCAPED_END_TAG_NAME_STATE",D="SCRIPT_DATA_DOUBLE_ESCAPE_START_STATE",w="SCRIPT_DATA_DOUBLE_ESCAPED_STATE",H="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_STATE",F="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH_STATE",B="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN_STATE",U="SCRIPT_DATA_DOUBLE_ESCAPE_END_STATE",z="BEFORE_ATTRIBUTE_NAME_STATE",G="ATTRIBUTE_NAME_STATE",K="AFTER_ATTRIBUTE_NAME_STATE",j="BEFORE_ATTRIBUTE_VALUE_STATE",V="ATTRIBUTE_VALUE_DOUBLE_QUOTED_STATE",Y="ATTRIBUTE_VALUE_SINGLE_QUOTED_STATE",W="ATTRIBUTE_VALUE_UNQUOTED_STATE",Q="AFTER_ATTRIBUTE_VALUE_QUOTED_STATE",q="SELF_CLOSING_START_TAG_STATE",X="BOGUS_COMMENT_STATE",$="MARKUP_DECLARATION_OPEN_STATE",J="COMMENT_START_STATE",Z="COMMENT_START_DASH_STATE",ee="COMMENT_STATE",et="COMMENT_LESS_THAN_SIGN_STATE",en="COMMENT_LESS_THAN_SIGN_BANG_STATE",er="COMMENT_LESS_THAN_SIGN_BANG_DASH_STATE",ei="COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH_STATE",eo="COMMENT_END_DASH_STATE",ea="COMMENT_END_STATE",es="COMMENT_END_BANG_STATE",el="DOCTYPE_STATE",ec="BEFORE_DOCTYPE_NAME_STATE",eu="DOCTYPE_NAME_STATE",eh="AFTER_DOCTYPE_NAME_STATE",ep="AFTER_DOCTYPE_PUBLIC_KEYWORD_STATE",ef="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER_STATE",ed="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED_STATE",em="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED_STATE",eT="AFTER_DOCTYPE_PUBLIC_IDENTIFIER_STATE",eg="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS_STATE",eE="AFTER_DOCTYPE_SYSTEM_KEYWORD_STATE",e_="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER_STATE",eA="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED_STATE",ek="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED_STATE",eC="AFTER_DOCTYPE_SYSTEM_IDENTIFIER_STATE",ey="BOGUS_DOCTYPE_STATE",eN="CDATA_SECTION_STATE",eS="CDATA_SECTION_BRACKET_STATE",eI="CDATA_SECTION_END_STATE",eb="CHARACTER_REFERENCE_STATE",eO="NAMED_CHARACTER_REFERENCE_STATE",ex="AMBIGUOS_AMPERSAND_STATE",eR="NUMERIC_CHARACTER_REFERENCE_STATE",ev="HEXADEMICAL_CHARACTER_REFERENCE_START_STATE",eL="DECIMAL_CHARACTER_REFERENCE_START_STATE",eP="HEXADEMICAL_CHARACTER_REFERENCE_STATE",eM="DECIMAL_CHARACTER_REFERENCE_STATE",eD="NUMERIC_CHARACTER_REFERENCE_END_STATE";function isWhitespace(e){return e===s.SPACE||e===s.LINE_FEED||e===s.TABULATION||e===s.FORM_FEED}function isAsciiDigit(e){return e>=s.DIGIT_0&&e<=s.DIGIT_9}function isAsciiUpper(e){return e>=s.LATIN_CAPITAL_A&&e<=s.LATIN_CAPITAL_Z}function isAsciiLower(e){return e>=s.LATIN_SMALL_A&&e<=s.LATIN_SMALL_Z}function isAsciiLetter(e){return isAsciiLower(e)||isAsciiUpper(e)}function isAsciiAlphaNumeric(e){return isAsciiLetter(e)||isAsciiDigit(e)}function isAsciiUpperHexDigit(e){return e>=s.LATIN_CAPITAL_A&&e<=s.LATIN_CAPITAL_F}function isAsciiLowerHexDigit(e){return e>=s.LATIN_SMALL_A&&e<=s.LATIN_SMALL_F}function toChar(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-=65536)>>>10&1023|55296)+String.fromCharCode(56320|1023&e)}function toAsciiLowerChar(e){return String.fromCharCode(e+32)}function findNamedEntityTreeBranch(e,t){let n=o[++e],r=++e,i=r+n-1;for(;r<=i;){let e=r+i>>>1,a=o[e];if(a<t)r=e+1;else{if(!(a>t))return o[e+n];i=e-1}}return -1}let Tokenizer=class Tokenizer{constructor(){this.preprocessor=new r,this.tokenQueue=[],this.allowCDATA=!1,this.state=u,this.returnState="",this.charRefCode=-1,this.tempBuff=[],this.lastStartTagName="",this.consumedAfterSnapshot=-1,this.active=!1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr=null}_err(){}_errOnNextCodePoint(e){this._consume(),this._err(e),this._unconsume()}getNextToken(){for(;!this.tokenQueue.length&&this.active;){this.consumedAfterSnapshot=0;let e=this._consume();this._ensureHibernation()||this[this.state](e)}return this.tokenQueue.shift()}write(e,t){this.active=!0,this.preprocessor.write(e,t)}insertHtmlAtCurrentPos(e){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(e)}_ensureHibernation(){if(this.preprocessor.endOfChunkHit){for(;this.consumedAfterSnapshot>0;this.consumedAfterSnapshot--)this.preprocessor.retreat();return this.active=!1,this.tokenQueue.push({type:Tokenizer.HIBERNATION_TOKEN}),!0}return!1}_consume(){return this.consumedAfterSnapshot++,this.preprocessor.advance()}_unconsume(){this.consumedAfterSnapshot--,this.preprocessor.retreat()}_reconsumeInState(e){this.state=e,this._unconsume()}_consumeSequenceIfMatch(e,t,n){let r,i=0,o=!0,a=e.length,l=0,c=t;for(;l<a;l++)if(l>0&&(c=this._consume(),i++),c===s.EOF||c!==(r=e[l])&&(n||c!==r+32)){o=!1;break}if(!o)for(;i--;)this._unconsume();return o}_isTempBufferEqualToScriptString(){if(this.tempBuff.length!==l.SCRIPT_STRING.length)return!1;for(let e=0;e<this.tempBuff.length;e++)if(this.tempBuff[e]!==l.SCRIPT_STRING[e])return!1;return!0}_createStartTagToken(){this.currentToken={type:Tokenizer.START_TAG_TOKEN,tagName:"",selfClosing:!1,ackSelfClosing:!1,attrs:[]}}_createEndTagToken(){this.currentToken={type:Tokenizer.END_TAG_TOKEN,tagName:"",selfClosing:!1,attrs:[]}}_createCommentToken(){this.currentToken={type:Tokenizer.COMMENT_TOKEN,data:""}}_createDoctypeToken(e){this.currentToken={type:Tokenizer.DOCTYPE_TOKEN,name:e,forceQuirks:!1,publicId:null,systemId:null}}_createCharacterToken(e,t){this.currentCharacterToken={type:e,chars:t}}_createEOFToken(){this.currentToken={type:Tokenizer.EOF_TOKEN}}_createAttr(e){this.currentAttr={name:e,value:""}}_leaveAttrName(e){null===Tokenizer.getTokenAttr(this.currentToken,this.currentAttr.name)?this.currentToken.attrs.push(this.currentAttr):this._err(a.duplicateAttribute),this.state=e}_leaveAttrValue(e){this.state=e}_emitCurrentToken(){this._emitCurrentCharacterToken();let e=this.currentToken;this.currentToken=null,e.type===Tokenizer.START_TAG_TOKEN?this.lastStartTagName=e.tagName:e.type===Tokenizer.END_TAG_TOKEN&&(e.attrs.length>0&&this._err(a.endTagWithAttributes),e.selfClosing&&this._err(a.endTagWithTrailingSolidus)),this.tokenQueue.push(e)}_emitCurrentCharacterToken(){this.currentCharacterToken&&(this.tokenQueue.push(this.currentCharacterToken),this.currentCharacterToken=null)}_emitEOFToken(){this._createEOFToken(),this._emitCurrentToken()}_appendCharToCurrentCharacterToken(e,t){this.currentCharacterToken&&this.currentCharacterToken.type!==e&&this._emitCurrentCharacterToken(),this.currentCharacterToken?this.currentCharacterToken.chars+=t:this._createCharacterToken(e,t)}_emitCodePoint(e){let t=Tokenizer.CHARACTER_TOKEN;isWhitespace(e)?t=Tokenizer.WHITESPACE_CHARACTER_TOKEN:e===s.NULL&&(t=Tokenizer.NULL_CHARACTER_TOKEN),this._appendCharToCurrentCharacterToken(t,toChar(e))}_emitSeveralCodePoints(e){for(let t=0;t<e.length;t++)this._emitCodePoint(e[t])}_emitChars(e){this._appendCharToCurrentCharacterToken(Tokenizer.CHARACTER_TOKEN,e)}_matchNamedCharacterReference(e){let t=null,n=1,r=findNamedEntityTreeBranch(0,e);for(this.tempBuff.push(e);r>-1;){let e=o[r],i=e<7,a=i&&1&e;a&&(t=2&e?[o[++r],o[++r]]:[o[++r]],n=0);let l=this._consume();if(this.tempBuff.push(l),n++,l===s.EOF)break;r=i?4&e?findNamedEntityTreeBranch(r,l):-1:l===e?++r:-1}for(;n--;)this.tempBuff.pop(),this._unconsume();return t}_isCharacterReferenceInAttribute(){return this.returnState===V||this.returnState===Y||this.returnState===W}_isCharacterReferenceAttributeQuirk(e){if(!e&&this._isCharacterReferenceInAttribute()){let e=this._consume();return this._unconsume(),e===s.EQUALS_SIGN||isAsciiAlphaNumeric(e)}return!1}_flushCodePointsConsumedAsCharacterReference(){if(this._isCharacterReferenceInAttribute())for(let e=0;e<this.tempBuff.length;e++)this.currentAttr.value+=toChar(this.tempBuff[e]);else this._emitSeveralCodePoints(this.tempBuff);this.tempBuff=[]}[u](e){this.preprocessor.dropParsedChunk(),e===s.LESS_THAN_SIGN?this.state=m:e===s.AMPERSAND?(this.returnState=u,this.state=eb):e===s.NULL?(this._err(a.unexpectedNullCharacter),this._emitCodePoint(e)):e===s.EOF?this._emitEOFToken():this._emitCodePoint(e)}[h](e){this.preprocessor.dropParsedChunk(),e===s.AMPERSAND?(this.returnState=h,this.state=eb):e===s.LESS_THAN_SIGN?this.state=E:e===s.NULL?(this._err(a.unexpectedNullCharacter),this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?this._emitEOFToken():this._emitCodePoint(e)}[p](e){this.preprocessor.dropParsedChunk(),e===s.LESS_THAN_SIGN?this.state=k:e===s.NULL?(this._err(a.unexpectedNullCharacter),this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?this._emitEOFToken():this._emitCodePoint(e)}[f](e){this.preprocessor.dropParsedChunk(),e===s.LESS_THAN_SIGN?this.state=N:e===s.NULL?(this._err(a.unexpectedNullCharacter),this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?this._emitEOFToken():this._emitCodePoint(e)}[d](e){this.preprocessor.dropParsedChunk(),e===s.NULL?(this._err(a.unexpectedNullCharacter),this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?this._emitEOFToken():this._emitCodePoint(e)}[m](e){e===s.EXCLAMATION_MARK?this.state=$:e===s.SOLIDUS?this.state=T:isAsciiLetter(e)?(this._createStartTagToken(),this._reconsumeInState(g)):e===s.QUESTION_MARK?(this._err(a.unexpectedQuestionMarkInsteadOfTagName),this._createCommentToken(),this._reconsumeInState(X)):e===s.EOF?(this._err(a.eofBeforeTagName),this._emitChars("<"),this._emitEOFToken()):(this._err(a.invalidFirstCharacterOfTagName),this._emitChars("<"),this._reconsumeInState(u))}[T](e){isAsciiLetter(e)?(this._createEndTagToken(),this._reconsumeInState(g)):e===s.GREATER_THAN_SIGN?(this._err(a.missingEndTagName),this.state=u):e===s.EOF?(this._err(a.eofBeforeTagName),this._emitChars("</"),this._emitEOFToken()):(this._err(a.invalidFirstCharacterOfTagName),this._createCommentToken(),this._reconsumeInState(X))}[g](e){isWhitespace(e)?this.state=z:e===s.SOLIDUS?this.state=q:e===s.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):isAsciiUpper(e)?this.currentToken.tagName+=toAsciiLowerChar(e):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentToken.tagName+=i.REPLACEMENT_CHARACTER):e===s.EOF?(this._err(a.eofInTag),this._emitEOFToken()):this.currentToken.tagName+=toChar(e)}[E](e){e===s.SOLIDUS?(this.tempBuff=[],this.state=_):(this._emitChars("<"),this._reconsumeInState(h))}[_](e){isAsciiLetter(e)?(this._createEndTagToken(),this._reconsumeInState(A)):(this._emitChars("</"),this._reconsumeInState(h))}[A](e){if(isAsciiUpper(e))this.currentToken.tagName+=toAsciiLowerChar(e),this.tempBuff.push(e);else if(isAsciiLower(e))this.currentToken.tagName+=toChar(e),this.tempBuff.push(e);else{if(this.lastStartTagName===this.currentToken.tagName){if(isWhitespace(e)){this.state=z;return}if(e===s.SOLIDUS){this.state=q;return}if(e===s.GREATER_THAN_SIGN){this.state=u,this._emitCurrentToken();return}}this._emitChars("</"),this._emitSeveralCodePoints(this.tempBuff),this._reconsumeInState(h)}}[k](e){e===s.SOLIDUS?(this.tempBuff=[],this.state=C):(this._emitChars("<"),this._reconsumeInState(p))}[C](e){isAsciiLetter(e)?(this._createEndTagToken(),this._reconsumeInState(y)):(this._emitChars("</"),this._reconsumeInState(p))}[y](e){if(isAsciiUpper(e))this.currentToken.tagName+=toAsciiLowerChar(e),this.tempBuff.push(e);else if(isAsciiLower(e))this.currentToken.tagName+=toChar(e),this.tempBuff.push(e);else{if(this.lastStartTagName===this.currentToken.tagName){if(isWhitespace(e)){this.state=z;return}if(e===s.SOLIDUS){this.state=q;return}if(e===s.GREATER_THAN_SIGN){this._emitCurrentToken(),this.state=u;return}}this._emitChars("</"),this._emitSeveralCodePoints(this.tempBuff),this._reconsumeInState(p)}}[N](e){e===s.SOLIDUS?(this.tempBuff=[],this.state=S):e===s.EXCLAMATION_MARK?(this.state=b,this._emitChars("<!")):(this._emitChars("<"),this._reconsumeInState(f))}[S](e){isAsciiLetter(e)?(this._createEndTagToken(),this._reconsumeInState(I)):(this._emitChars("</"),this._reconsumeInState(f))}[I](e){if(isAsciiUpper(e))this.currentToken.tagName+=toAsciiLowerChar(e),this.tempBuff.push(e);else if(isAsciiLower(e))this.currentToken.tagName+=toChar(e),this.tempBuff.push(e);else{if(this.lastStartTagName===this.currentToken.tagName){if(isWhitespace(e)){this.state=z;return}if(e===s.SOLIDUS){this.state=q;return}if(e===s.GREATER_THAN_SIGN){this._emitCurrentToken(),this.state=u;return}}this._emitChars("</"),this._emitSeveralCodePoints(this.tempBuff),this._reconsumeInState(f)}}[b](e){e===s.HYPHEN_MINUS?(this.state=O,this._emitChars("-")):this._reconsumeInState(f)}[O](e){e===s.HYPHEN_MINUS?(this.state=v,this._emitChars("-")):this._reconsumeInState(f)}[x](e){e===s.HYPHEN_MINUS?(this.state=R,this._emitChars("-")):e===s.LESS_THAN_SIGN?this.state=L:e===s.NULL?(this._err(a.unexpectedNullCharacter),this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?(this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken()):this._emitCodePoint(e)}[R](e){e===s.HYPHEN_MINUS?(this.state=v,this._emitChars("-")):e===s.LESS_THAN_SIGN?this.state=L:e===s.NULL?(this._err(a.unexpectedNullCharacter),this.state=x,this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?(this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken()):(this.state=x,this._emitCodePoint(e))}[v](e){e===s.HYPHEN_MINUS?this._emitChars("-"):e===s.LESS_THAN_SIGN?this.state=L:e===s.GREATER_THAN_SIGN?(this.state=f,this._emitChars(">")):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.state=x,this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?(this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken()):(this.state=x,this._emitCodePoint(e))}[L](e){e===s.SOLIDUS?(this.tempBuff=[],this.state=P):isAsciiLetter(e)?(this.tempBuff=[],this._emitChars("<"),this._reconsumeInState(D)):(this._emitChars("<"),this._reconsumeInState(x))}[P](e){isAsciiLetter(e)?(this._createEndTagToken(),this._reconsumeInState(M)):(this._emitChars("</"),this._reconsumeInState(x))}[M](e){if(isAsciiUpper(e))this.currentToken.tagName+=toAsciiLowerChar(e),this.tempBuff.push(e);else if(isAsciiLower(e))this.currentToken.tagName+=toChar(e),this.tempBuff.push(e);else{if(this.lastStartTagName===this.currentToken.tagName){if(isWhitespace(e)){this.state=z;return}if(e===s.SOLIDUS){this.state=q;return}if(e===s.GREATER_THAN_SIGN){this._emitCurrentToken(),this.state=u;return}}this._emitChars("</"),this._emitSeveralCodePoints(this.tempBuff),this._reconsumeInState(x)}}[D](e){isWhitespace(e)||e===s.SOLIDUS||e===s.GREATER_THAN_SIGN?(this.state=this._isTempBufferEqualToScriptString()?w:x,this._emitCodePoint(e)):isAsciiUpper(e)?(this.tempBuff.push(e+32),this._emitCodePoint(e)):isAsciiLower(e)?(this.tempBuff.push(e),this._emitCodePoint(e)):this._reconsumeInState(x)}[w](e){e===s.HYPHEN_MINUS?(this.state=H,this._emitChars("-")):e===s.LESS_THAN_SIGN?(this.state=B,this._emitChars("<")):e===s.NULL?(this._err(a.unexpectedNullCharacter),this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?(this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken()):this._emitCodePoint(e)}[H](e){e===s.HYPHEN_MINUS?(this.state=F,this._emitChars("-")):e===s.LESS_THAN_SIGN?(this.state=B,this._emitChars("<")):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.state=w,this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?(this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken()):(this.state=w,this._emitCodePoint(e))}[F](e){e===s.HYPHEN_MINUS?this._emitChars("-"):e===s.LESS_THAN_SIGN?(this.state=B,this._emitChars("<")):e===s.GREATER_THAN_SIGN?(this.state=f,this._emitChars(">")):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.state=w,this._emitChars(i.REPLACEMENT_CHARACTER)):e===s.EOF?(this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken()):(this.state=w,this._emitCodePoint(e))}[B](e){e===s.SOLIDUS?(this.tempBuff=[],this.state=U,this._emitChars("/")):this._reconsumeInState(w)}[U](e){isWhitespace(e)||e===s.SOLIDUS||e===s.GREATER_THAN_SIGN?(this.state=this._isTempBufferEqualToScriptString()?x:w,this._emitCodePoint(e)):isAsciiUpper(e)?(this.tempBuff.push(e+32),this._emitCodePoint(e)):isAsciiLower(e)?(this.tempBuff.push(e),this._emitCodePoint(e)):this._reconsumeInState(w)}[z](e){isWhitespace(e)||(e===s.SOLIDUS||e===s.GREATER_THAN_SIGN||e===s.EOF?this._reconsumeInState(K):e===s.EQUALS_SIGN?(this._err(a.unexpectedEqualsSignBeforeAttributeName),this._createAttr("="),this.state=G):(this._createAttr(""),this._reconsumeInState(G)))}[G](e){isWhitespace(e)||e===s.SOLIDUS||e===s.GREATER_THAN_SIGN||e===s.EOF?(this._leaveAttrName(K),this._unconsume()):e===s.EQUALS_SIGN?this._leaveAttrName(j):isAsciiUpper(e)?this.currentAttr.name+=toAsciiLowerChar(e):e===s.QUOTATION_MARK||e===s.APOSTROPHE||e===s.LESS_THAN_SIGN?(this._err(a.unexpectedCharacterInAttributeName),this.currentAttr.name+=toChar(e)):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentAttr.name+=i.REPLACEMENT_CHARACTER):this.currentAttr.name+=toChar(e)}[K](e){isWhitespace(e)||(e===s.SOLIDUS?this.state=q:e===s.EQUALS_SIGN?this.state=j:e===s.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInTag),this._emitEOFToken()):(this._createAttr(""),this._reconsumeInState(G)))}[j](e){isWhitespace(e)||(e===s.QUOTATION_MARK?this.state=V:e===s.APOSTROPHE?this.state=Y:e===s.GREATER_THAN_SIGN?(this._err(a.missingAttributeValue),this.state=u,this._emitCurrentToken()):this._reconsumeInState(W))}[V](e){e===s.QUOTATION_MARK?this.state=Q:e===s.AMPERSAND?(this.returnState=V,this.state=eb):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentAttr.value+=i.REPLACEMENT_CHARACTER):e===s.EOF?(this._err(a.eofInTag),this._emitEOFToken()):this.currentAttr.value+=toChar(e)}[Y](e){e===s.APOSTROPHE?this.state=Q:e===s.AMPERSAND?(this.returnState=Y,this.state=eb):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentAttr.value+=i.REPLACEMENT_CHARACTER):e===s.EOF?(this._err(a.eofInTag),this._emitEOFToken()):this.currentAttr.value+=toChar(e)}[W](e){isWhitespace(e)?this._leaveAttrValue(z):e===s.AMPERSAND?(this.returnState=W,this.state=eb):e===s.GREATER_THAN_SIGN?(this._leaveAttrValue(u),this._emitCurrentToken()):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentAttr.value+=i.REPLACEMENT_CHARACTER):e===s.QUOTATION_MARK||e===s.APOSTROPHE||e===s.LESS_THAN_SIGN||e===s.EQUALS_SIGN||e===s.GRAVE_ACCENT?(this._err(a.unexpectedCharacterInUnquotedAttributeValue),this.currentAttr.value+=toChar(e)):e===s.EOF?(this._err(a.eofInTag),this._emitEOFToken()):this.currentAttr.value+=toChar(e)}[Q](e){isWhitespace(e)?this._leaveAttrValue(z):e===s.SOLIDUS?this._leaveAttrValue(q):e===s.GREATER_THAN_SIGN?(this._leaveAttrValue(u),this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInTag),this._emitEOFToken()):(this._err(a.missingWhitespaceBetweenAttributes),this._reconsumeInState(z))}[q](e){e===s.GREATER_THAN_SIGN?(this.currentToken.selfClosing=!0,this.state=u,this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInTag),this._emitEOFToken()):(this._err(a.unexpectedSolidusInTag),this._reconsumeInState(z))}[X](e){e===s.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===s.EOF?(this._emitCurrentToken(),this._emitEOFToken()):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentToken.data+=i.REPLACEMENT_CHARACTER):this.currentToken.data+=toChar(e)}[$](e){this._consumeSequenceIfMatch(l.DASH_DASH_STRING,e,!0)?(this._createCommentToken(),this.state=J):this._consumeSequenceIfMatch(l.DOCTYPE_STRING,e,!1)?this.state=el:this._consumeSequenceIfMatch(l.CDATA_START_STRING,e,!0)?this.allowCDATA?this.state=eN:(this._err(a.cdataInHtmlContent),this._createCommentToken(),this.currentToken.data="[CDATA[",this.state=X):this._ensureHibernation()||(this._err(a.incorrectlyOpenedComment),this._createCommentToken(),this._reconsumeInState(X))}[J](e){e===s.HYPHEN_MINUS?this.state=Z:e===s.GREATER_THAN_SIGN?(this._err(a.abruptClosingOfEmptyComment),this.state=u,this._emitCurrentToken()):this._reconsumeInState(ee)}[Z](e){e===s.HYPHEN_MINUS?this.state=ea:e===s.GREATER_THAN_SIGN?(this._err(a.abruptClosingOfEmptyComment),this.state=u,this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInComment),this._emitCurrentToken(),this._emitEOFToken()):(this.currentToken.data+="-",this._reconsumeInState(ee))}[ee](e){e===s.HYPHEN_MINUS?this.state=eo:e===s.LESS_THAN_SIGN?(this.currentToken.data+="<",this.state=et):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentToken.data+=i.REPLACEMENT_CHARACTER):e===s.EOF?(this._err(a.eofInComment),this._emitCurrentToken(),this._emitEOFToken()):this.currentToken.data+=toChar(e)}[et](e){e===s.EXCLAMATION_MARK?(this.currentToken.data+="!",this.state=en):e===s.LESS_THAN_SIGN?this.currentToken.data+="!":this._reconsumeInState(ee)}[en](e){e===s.HYPHEN_MINUS?this.state=er:this._reconsumeInState(ee)}[er](e){e===s.HYPHEN_MINUS?this.state=ei:this._reconsumeInState(eo)}[ei](e){e!==s.GREATER_THAN_SIGN&&e!==s.EOF&&this._err(a.nestedComment),this._reconsumeInState(ea)}[eo](e){e===s.HYPHEN_MINUS?this.state=ea:e===s.EOF?(this._err(a.eofInComment),this._emitCurrentToken(),this._emitEOFToken()):(this.currentToken.data+="-",this._reconsumeInState(ee))}[ea](e){e===s.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===s.EXCLAMATION_MARK?this.state=es:e===s.HYPHEN_MINUS?this.currentToken.data+="-":e===s.EOF?(this._err(a.eofInComment),this._emitCurrentToken(),this._emitEOFToken()):(this.currentToken.data+="--",this._reconsumeInState(ee))}[es](e){e===s.HYPHEN_MINUS?(this.currentToken.data+="--!",this.state=eo):e===s.GREATER_THAN_SIGN?(this._err(a.incorrectlyClosedComment),this.state=u,this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInComment),this._emitCurrentToken(),this._emitEOFToken()):(this.currentToken.data+="--!",this._reconsumeInState(ee))}[el](e){isWhitespace(e)?this.state=ec:e===s.GREATER_THAN_SIGN?this._reconsumeInState(ec):e===s.EOF?(this._err(a.eofInDoctype),this._createDoctypeToken(null),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):(this._err(a.missingWhitespaceBeforeDoctypeName),this._reconsumeInState(ec))}[ec](e){isWhitespace(e)||(isAsciiUpper(e)?(this._createDoctypeToken(toAsciiLowerChar(e)),this.state=eu):e===s.NULL?(this._err(a.unexpectedNullCharacter),this._createDoctypeToken(i.REPLACEMENT_CHARACTER),this.state=eu):e===s.GREATER_THAN_SIGN?(this._err(a.missingDoctypeName),this._createDoctypeToken(null),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===s.EOF?(this._err(a.eofInDoctype),this._createDoctypeToken(null),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):(this._createDoctypeToken(toChar(e)),this.state=eu))}[eu](e){isWhitespace(e)?this.state=eh:e===s.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):isAsciiUpper(e)?this.currentToken.name+=toAsciiLowerChar(e):e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentToken.name+=i.REPLACEMENT_CHARACTER):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):this.currentToken.name+=toChar(e)}[eh](e){!isWhitespace(e)&&(e===s.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):this._consumeSequenceIfMatch(l.PUBLIC_STRING,e,!1)?this.state=ep:this._consumeSequenceIfMatch(l.SYSTEM_STRING,e,!1)?this.state=eE:this._ensureHibernation()||(this._err(a.invalidCharacterSequenceAfterDoctypeName),this.currentToken.forceQuirks=!0,this._reconsumeInState(ey)))}[ep](e){isWhitespace(e)?this.state=ef:e===s.QUOTATION_MARK?(this._err(a.missingWhitespaceAfterDoctypePublicKeyword),this.currentToken.publicId="",this.state=ed):e===s.APOSTROPHE?(this._err(a.missingWhitespaceAfterDoctypePublicKeyword),this.currentToken.publicId="",this.state=em):e===s.GREATER_THAN_SIGN?(this._err(a.missingDoctypePublicIdentifier),this.currentToken.forceQuirks=!0,this.state=u,this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):(this._err(a.missingQuoteBeforeDoctypePublicIdentifier),this.currentToken.forceQuirks=!0,this._reconsumeInState(ey))}[ef](e){isWhitespace(e)||(e===s.QUOTATION_MARK?(this.currentToken.publicId="",this.state=ed):e===s.APOSTROPHE?(this.currentToken.publicId="",this.state=em):e===s.GREATER_THAN_SIGN?(this._err(a.missingDoctypePublicIdentifier),this.currentToken.forceQuirks=!0,this.state=u,this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):(this._err(a.missingQuoteBeforeDoctypePublicIdentifier),this.currentToken.forceQuirks=!0,this._reconsumeInState(ey)))}[ed](e){e===s.QUOTATION_MARK?this.state=eT:e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentToken.publicId+=i.REPLACEMENT_CHARACTER):e===s.GREATER_THAN_SIGN?(this._err(a.abruptDoctypePublicIdentifier),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):this.currentToken.publicId+=toChar(e)}[em](e){e===s.APOSTROPHE?this.state=eT:e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentToken.publicId+=i.REPLACEMENT_CHARACTER):e===s.GREATER_THAN_SIGN?(this._err(a.abruptDoctypePublicIdentifier),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):this.currentToken.publicId+=toChar(e)}[eT](e){isWhitespace(e)?this.state=eg:e===s.GREATER_THAN_SIGN?(this.state=u,this._emitCurrentToken()):e===s.QUOTATION_MARK?(this._err(a.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),this.currentToken.systemId="",this.state=eA):e===s.APOSTROPHE?(this._err(a.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),this.currentToken.systemId="",this.state=ek):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):(this._err(a.missingQuoteBeforeDoctypeSystemIdentifier),this.currentToken.forceQuirks=!0,this._reconsumeInState(ey))}[eg](e){isWhitespace(e)||(e===s.GREATER_THAN_SIGN?(this._emitCurrentToken(),this.state=u):e===s.QUOTATION_MARK?(this.currentToken.systemId="",this.state=eA):e===s.APOSTROPHE?(this.currentToken.systemId="",this.state=ek):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):(this._err(a.missingQuoteBeforeDoctypeSystemIdentifier),this.currentToken.forceQuirks=!0,this._reconsumeInState(ey)))}[eE](e){isWhitespace(e)?this.state=e_:e===s.QUOTATION_MARK?(this._err(a.missingWhitespaceAfterDoctypeSystemKeyword),this.currentToken.systemId="",this.state=eA):e===s.APOSTROPHE?(this._err(a.missingWhitespaceAfterDoctypeSystemKeyword),this.currentToken.systemId="",this.state=ek):e===s.GREATER_THAN_SIGN?(this._err(a.missingDoctypeSystemIdentifier),this.currentToken.forceQuirks=!0,this.state=u,this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):(this._err(a.missingQuoteBeforeDoctypeSystemIdentifier),this.currentToken.forceQuirks=!0,this._reconsumeInState(ey))}[e_](e){isWhitespace(e)||(e===s.QUOTATION_MARK?(this.currentToken.systemId="",this.state=eA):e===s.APOSTROPHE?(this.currentToken.systemId="",this.state=ek):e===s.GREATER_THAN_SIGN?(this._err(a.missingDoctypeSystemIdentifier),this.currentToken.forceQuirks=!0,this.state=u,this._emitCurrentToken()):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):(this._err(a.missingQuoteBeforeDoctypeSystemIdentifier),this.currentToken.forceQuirks=!0,this._reconsumeInState(ey)))}[eA](e){e===s.QUOTATION_MARK?this.state=eC:e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentToken.systemId+=i.REPLACEMENT_CHARACTER):e===s.GREATER_THAN_SIGN?(this._err(a.abruptDoctypeSystemIdentifier),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):this.currentToken.systemId+=toChar(e)}[ek](e){e===s.APOSTROPHE?this.state=eC:e===s.NULL?(this._err(a.unexpectedNullCharacter),this.currentToken.systemId+=i.REPLACEMENT_CHARACTER):e===s.GREATER_THAN_SIGN?(this._err(a.abruptDoctypeSystemIdentifier),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this.state=u):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):this.currentToken.systemId+=toChar(e)}[eC](e){isWhitespace(e)||(e===s.GREATER_THAN_SIGN?(this._emitCurrentToken(),this.state=u):e===s.EOF?(this._err(a.eofInDoctype),this.currentToken.forceQuirks=!0,this._emitCurrentToken(),this._emitEOFToken()):(this._err(a.unexpectedCharacterAfterDoctypeSystemIdentifier),this._reconsumeInState(ey)))}[ey](e){e===s.GREATER_THAN_SIGN?(this._emitCurrentToken(),this.state=u):e===s.NULL?this._err(a.unexpectedNullCharacter):e===s.EOF&&(this._emitCurrentToken(),this._emitEOFToken())}[eN](e){e===s.RIGHT_SQUARE_BRACKET?this.state=eS:e===s.EOF?(this._err(a.eofInCdata),this._emitEOFToken()):this._emitCodePoint(e)}[eS](e){e===s.RIGHT_SQUARE_BRACKET?this.state=eI:(this._emitChars("]"),this._reconsumeInState(eN))}[eI](e){e===s.GREATER_THAN_SIGN?this.state=u:e===s.RIGHT_SQUARE_BRACKET?this._emitChars("]"):(this._emitChars("]]"),this._reconsumeInState(eN))}[eb](e){this.tempBuff=[s.AMPERSAND],e===s.NUMBER_SIGN?(this.tempBuff.push(e),this.state=eR):isAsciiAlphaNumeric(e)?this._reconsumeInState(eO):(this._flushCodePointsConsumedAsCharacterReference(),this._reconsumeInState(this.returnState))}[eO](e){let t=this._matchNamedCharacterReference(e);if(this._ensureHibernation())this.tempBuff=[s.AMPERSAND];else if(t){let e=this.tempBuff[this.tempBuff.length-1]===s.SEMICOLON;this._isCharacterReferenceAttributeQuirk(e)||(e||this._errOnNextCodePoint(a.missingSemicolonAfterCharacterReference),this.tempBuff=t),this._flushCodePointsConsumedAsCharacterReference(),this.state=this.returnState}else this._flushCodePointsConsumedAsCharacterReference(),this.state=ex}[ex](e){isAsciiAlphaNumeric(e)?this._isCharacterReferenceInAttribute()?this.currentAttr.value+=toChar(e):this._emitCodePoint(e):(e===s.SEMICOLON&&this._err(a.unknownNamedCharacterReference),this._reconsumeInState(this.returnState))}[eR](e){this.charRefCode=0,e===s.LATIN_SMALL_X||e===s.LATIN_CAPITAL_X?(this.tempBuff.push(e),this.state=ev):this._reconsumeInState(eL)}[ev](e){isAsciiDigit(e)||isAsciiUpperHexDigit(e)||isAsciiLowerHexDigit(e)?this._reconsumeInState(eP):(this._err(a.absenceOfDigitsInNumericCharacterReference),this._flushCodePointsConsumedAsCharacterReference(),this._reconsumeInState(this.returnState))}[eL](e){isAsciiDigit(e)?this._reconsumeInState(eM):(this._err(a.absenceOfDigitsInNumericCharacterReference),this._flushCodePointsConsumedAsCharacterReference(),this._reconsumeInState(this.returnState))}[eP](e){isAsciiUpperHexDigit(e)?this.charRefCode=16*this.charRefCode+e-55:isAsciiLowerHexDigit(e)?this.charRefCode=16*this.charRefCode+e-87:isAsciiDigit(e)?this.charRefCode=16*this.charRefCode+e-48:e===s.SEMICOLON?this.state=eD:(this._err(a.missingSemicolonAfterCharacterReference),this._reconsumeInState(eD))}[eM](e){isAsciiDigit(e)?this.charRefCode=10*this.charRefCode+e-48:e===s.SEMICOLON?this.state=eD:(this._err(a.missingSemicolonAfterCharacterReference),this._reconsumeInState(eD))}[eD](){if(this.charRefCode===s.NULL)this._err(a.nullCharacterReference),this.charRefCode=s.REPLACEMENT_CHARACTER;else if(this.charRefCode>1114111)this._err(a.characterReferenceOutsideUnicodeRange),this.charRefCode=s.REPLACEMENT_CHARACTER;else if(i.isSurrogate(this.charRefCode))this._err(a.surrogateCharacterReference),this.charRefCode=s.REPLACEMENT_CHARACTER;else if(i.isUndefinedCodePoint(this.charRefCode))this._err(a.noncharacterCharacterReference);else if(i.isControlCodePoint(this.charRefCode)||this.charRefCode===s.CARRIAGE_RETURN){this._err(a.controlCharacterReference);let e=c[this.charRefCode];e&&(this.charRefCode=e)}this.tempBuff=[this.charRefCode],this._flushCodePointsConsumedAsCharacterReference(),this._reconsumeInState(this.returnState)}};Tokenizer.CHARACTER_TOKEN="CHARACTER_TOKEN",Tokenizer.NULL_CHARACTER_TOKEN="NULL_CHARACTER_TOKEN",Tokenizer.WHITESPACE_CHARACTER_TOKEN="WHITESPACE_CHARACTER_TOKEN",Tokenizer.START_TAG_TOKEN="START_TAG_TOKEN",Tokenizer.END_TAG_TOKEN="END_TAG_TOKEN",Tokenizer.COMMENT_TOKEN="COMMENT_TOKEN",Tokenizer.DOCTYPE_TOKEN="DOCTYPE_TOKEN",Tokenizer.EOF_TOKEN="EOF_TOKEN",Tokenizer.HIBERNATION_TOKEN="HIBERNATION_TOKEN",Tokenizer.MODE={DATA:u,RCDATA:h,RAWTEXT:p,SCRIPT_DATA:f,PLAINTEXT:d},Tokenizer.getTokenAttr=function(e,t){for(let n=e.attrs.length-1;n>=0;n--)if(e.attrs[n].name===t)return e.attrs[n].value;return null},e.exports=Tokenizer},5482:function(e){"use strict";e.exports=new Uint16Array([4,52,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,106,303,412,810,1432,1701,1796,1987,2114,2360,2420,2484,3170,3251,4140,4393,4575,4610,5106,5512,5728,6117,6274,6315,6345,6427,6516,7002,7910,8733,9323,9870,10170,10631,10893,11318,11386,11467,12773,13092,14474,14922,15448,15542,16419,17666,18166,18611,19004,19095,19298,19397,4,16,69,77,97,98,99,102,103,108,109,110,111,112,114,115,116,117,140,150,158,169,176,194,199,210,216,222,226,242,256,266,283,294,108,105,103,5,198,1,59,148,1,198,80,5,38,1,59,156,1,38,99,117,116,101,5,193,1,59,167,1,193,114,101,118,101,59,1,258,4,2,105,121,182,191,114,99,5,194,1,59,189,1,194,59,1,1040,114,59,3,55349,56580,114,97,118,101,5,192,1,59,208,1,192,112,104,97,59,1,913,97,99,114,59,1,256,100,59,1,10835,4,2,103,112,232,237,111,110,59,1,260,102,59,3,55349,56632,112,108,121,70,117,110,99,116,105,111,110,59,1,8289,105,110,103,5,197,1,59,264,1,197,4,2,99,115,272,277,114,59,3,55349,56476,105,103,110,59,1,8788,105,108,100,101,5,195,1,59,292,1,195,109,108,5,196,1,59,301,1,196,4,8,97,99,101,102,111,114,115,117,321,350,354,383,388,394,400,405,4,2,99,114,327,336,107,115,108,97,115,104,59,1,8726,4,2,118,119,342,345,59,1,10983,101,100,59,1,8966,121,59,1,1041,4,3,99,114,116,362,369,379,97,117,115,101,59,1,8757,110,111,117,108,108,105,115,59,1,8492,97,59,1,914,114,59,3,55349,56581,112,102,59,3,55349,56633,101,118,101,59,1,728,99,114,59,1,8492,109,112,101,113,59,1,8782,4,14,72,79,97,99,100,101,102,104,105,108,111,114,115,117,442,447,456,504,542,547,569,573,577,616,678,784,790,796,99,121,59,1,1063,80,89,5,169,1,59,454,1,169,4,3,99,112,121,464,470,497,117,116,101,59,1,262,4,2,59,105,476,478,1,8914,116,97,108,68,105,102,102,101,114,101,110,116,105,97,108,68,59,1,8517,108,101,121,115,59,1,8493,4,4,97,101,105,111,514,520,530,535,114,111,110,59,1,268,100,105,108,5,199,1,59,528,1,199,114,99,59,1,264,110,105,110,116,59,1,8752,111,116,59,1,266,4,2,100,110,553,560,105,108,108,97,59,1,184,116,101,114,68,111,116,59,1,183,114,59,1,8493,105,59,1,935,114,99,108,101,4,4,68,77,80,84,591,596,603,609,111,116,59,1,8857,105,110,117,115,59,1,8854,108,117,115,59,1,8853,105,109,101,115,59,1,8855,111,4,2,99,115,623,646,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,1,8754,101,67,117,114,108,121,4,2,68,81,658,671,111,117,98,108,101,81,117,111,116,101,59,1,8221,117,111,116,101,59,1,8217,4,4,108,110,112,117,688,701,736,753,111,110,4,2,59,101,696,698,1,8759,59,1,10868,4,3,103,105,116,709,717,722,114,117,101,110,116,59,1,8801,110,116,59,1,8751,111,117,114,73,110,116,101,103,114,97,108,59,1,8750,4,2,102,114,742,745,59,1,8450,111,100,117,99,116,59,1,8720,110,116,101,114,67,108,111,99,107,119,105,115,101,67,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,1,8755,111,115,115,59,1,10799,99,114,59,3,55349,56478,112,4,2,59,67,803,805,1,8915,97,112,59,1,8781,4,11,68,74,83,90,97,99,101,102,105,111,115,834,850,855,860,865,888,903,916,921,1011,1415,4,2,59,111,840,842,1,8517,116,114,97,104,100,59,1,10513,99,121,59,1,1026,99,121,59,1,1029,99,121,59,1,1039,4,3,103,114,115,873,879,883,103,101,114,59,1,8225,114,59,1,8609,104,118,59,1,10980,4,2,97,121,894,900,114,111,110,59,1,270,59,1,1044,108,4,2,59,116,910,912,1,8711,97,59,1,916,114,59,3,55349,56583,4,2,97,102,927,998,4,2,99,109,933,992,114,105,116,105,99,97,108,4,4,65,68,71,84,950,957,978,985,99,117,116,101,59,1,180,111,4,2,116,117,964,967,59,1,729,98,108,101,65,99,117,116,101,59,1,733,114,97,118,101,59,1,96,105,108,100,101,59,1,732,111,110,100,59,1,8900,102,101,114,101,110,116,105,97,108,68,59,1,8518,4,4,112,116,117,119,1021,1026,1048,1249,102,59,3,55349,56635,4,3,59,68,69,1034,1036,1041,1,168,111,116,59,1,8412,113,117,97,108,59,1,8784,98,108,101,4,6,67,68,76,82,85,86,1065,1082,1101,1189,1211,1236,111,110,116,111,117,114,73,110,116,101,103,114,97,108,59,1,8751,111,4,2,116,119,1089,1092,59,1,168,110,65,114,114,111,119,59,1,8659,4,2,101,111,1107,1141,102,116,4,3,65,82,84,1117,1124,1136,114,114,111,119,59,1,8656,105,103,104,116,65,114,114,111,119,59,1,8660,101,101,59,1,10980,110,103,4,2,76,82,1149,1177,101,102,116,4,2,65,82,1158,1165,114,114,111,119,59,1,10232,105,103,104,116,65,114,114,111,119,59,1,10234,105,103,104,116,65,114,114,111,119,59,1,10233,105,103,104,116,4,2,65,84,1199,1206,114,114,111,119,59,1,8658,101,101,59,1,8872,112,4,2,65,68,1218,1225,114,114,111,119,59,1,8657,111,119,110,65,114,114,111,119,59,1,8661,101,114,116,105,99,97,108,66,97,114,59,1,8741,110,4,6,65,66,76,82,84,97,1264,1292,1299,1352,1391,1408,114,114,111,119,4,3,59,66,85,1276,1278,1283,1,8595,97,114,59,1,10515,112,65,114,114,111,119,59,1,8693,114,101,118,101,59,1,785,101,102,116,4,3,82,84,86,1310,1323,1334,105,103,104,116,86,101,99,116,111,114,59,1,10576,101,101,86,101,99,116,111,114,59,1,10590,101,99,116,111,114,4,2,59,66,1345,1347,1,8637,97,114,59,1,10582,105,103,104,116,4,2,84,86,1362,1373,101,101,86,101,99,116,111,114,59,1,10591,101,99,116,111,114,4,2,59,66,1384,1386,1,8641,97,114,59,1,10583,101,101,4,2,59,65,1399,1401,1,8868,114,114,111,119,59,1,8615,114,114,111,119,59,1,8659,4,2,99,116,1421,1426,114,59,3,55349,56479,114,111,107,59,1,272,4,16,78,84,97,99,100,102,103,108,109,111,112,113,115,116,117,120,1466,1470,1478,1489,1515,1520,1525,1536,1544,1593,1609,1617,1650,1664,1668,1677,71,59,1,330,72,5,208,1,59,1476,1,208,99,117,116,101,5,201,1,59,1487,1,201,4,3,97,105,121,1497,1503,1512,114,111,110,59,1,282,114,99,5,202,1,59,1510,1,202,59,1,1069,111,116,59,1,278,114,59,3,55349,56584,114,97,118,101,5,200,1,59,1534,1,200,101,109,101,110,116,59,1,8712,4,2,97,112,1550,1555,99,114,59,1,274,116,121,4,2,83,86,1563,1576,109,97,108,108,83,113,117,97,114,101,59,1,9723,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,1,9643,4,2,103,112,1599,1604,111,110,59,1,280,102,59,3,55349,56636,115,105,108,111,110,59,1,917,117,4,2,97,105,1624,1640,108,4,2,59,84,1631,1633,1,10869,105,108,100,101,59,1,8770,108,105,98,114,105,117,109,59,1,8652,4,2,99,105,1656,1660,114,59,1,8496,109,59,1,10867,97,59,1,919,109,108,5,203,1,59,1675,1,203,4,2,105,112,1683,1689,115,116,115,59,1,8707,111,110,101,110,116,105,97,108,69,59,1,8519,4,5,99,102,105,111,115,1713,1717,1722,1762,1791,121,59,1,1060,114,59,3,55349,56585,108,108,101,100,4,2,83,86,1732,1745,109,97,108,108,83,113,117,97,114,101,59,1,9724,101,114,121,83,109,97,108,108,83,113,117,97,114,101,59,1,9642,4,3,112,114,117,1770,1775,1781,102,59,3,55349,56637,65,108,108,59,1,8704,114,105,101,114,116,114,102,59,1,8497,99,114,59,1,8497,4,12,74,84,97,98,99,100,102,103,111,114,115,116,1822,1827,1834,1848,1855,1877,1882,1887,1890,1896,1978,1984,99,121,59,1,1027,5,62,1,59,1832,1,62,109,109,97,4,2,59,100,1843,1845,1,915,59,1,988,114,101,118,101,59,1,286,4,3,101,105,121,1863,1869,1874,100,105,108,59,1,290,114,99,59,1,284,59,1,1043,111,116,59,1,288,114,59,3,55349,56586,59,1,8921,112,102,59,3,55349,56638,101,97,116,101,114,4,6,69,70,71,76,83,84,1915,1933,1944,1953,1959,1971,113,117,97,108,4,2,59,76,1925,1927,1,8805,101,115,115,59,1,8923,117,108,108,69,113,117,97,108,59,1,8807,114,101,97,116,101,114,59,1,10914,101,115,115,59,1,8823,108,97,110,116,69,113,117,97,108,59,1,10878,105,108,100,101,59,1,8819,99,114,59,3,55349,56482,59,1,8811,4,8,65,97,99,102,105,111,115,117,2005,2012,2026,2032,2036,2049,2073,2089,82,68,99,121,59,1,1066,4,2,99,116,2018,2023,101,107,59,1,711,59,1,94,105,114,99,59,1,292,114,59,1,8460,108,98,101,114,116,83,112,97,99,101,59,1,8459,4,2,112,114,2055,2059,102,59,1,8461,105,122,111,110,116,97,108,76,105,110,101,59,1,9472,4,2,99,116,2079,2083,114,59,1,8459,114,111,107,59,1,294,109,112,4,2,68,69,2097,2107,111,119,110,72,117,109,112,59,1,8782,113,117,97,108,59,1,8783,4,14,69,74,79,97,99,100,102,103,109,110,111,115,116,117,2144,2149,2155,2160,2171,2189,2194,2198,2209,2245,2307,2329,2334,2341,99,121,59,1,1045,108,105,103,59,1,306,99,121,59,1,1025,99,117,116,101,5,205,1,59,2169,1,205,4,2,105,121,2177,2186,114,99,5,206,1,59,2184,1,206,59,1,1048,111,116,59,1,304,114,59,1,8465,114,97,118,101,5,204,1,59,2207,1,204,4,3,59,97,112,2217,2219,2238,1,8465,4,2,99,103,2225,2229,114,59,1,298,105,110,97,114,121,73,59,1,8520,108,105,101,115,59,1,8658,4,2,116,118,2251,2281,4,2,59,101,2257,2259,1,8748,4,2,103,114,2265,2271,114,97,108,59,1,8747,115,101,99,116,105,111,110,59,1,8898,105,115,105,98,108,101,4,2,67,84,2293,2300,111,109,109,97,59,1,8291,105,109,101,115,59,1,8290,4,3,103,112,116,2315,2320,2325,111,110,59,1,302,102,59,3,55349,56640,97,59,1,921,99,114,59,1,8464,105,108,100,101,59,1,296,4,2,107,109,2347,2352,99,121,59,1,1030,108,5,207,1,59,2358,1,207,4,5,99,102,111,115,117,2372,2386,2391,2397,2414,4,2,105,121,2378,2383,114,99,59,1,308,59,1,1049,114,59,3,55349,56589,112,102,59,3,55349,56641,4,2,99,101,2403,2408,114,59,3,55349,56485,114,99,121,59,1,1032,107,99,121,59,1,1028,4,7,72,74,97,99,102,111,115,2436,2441,2446,2452,2467,2472,2478,99,121,59,1,1061,99,121,59,1,1036,112,112,97,59,1,922,4,2,101,121,2458,2464,100,105,108,59,1,310,59,1,1050,114,59,3,55349,56590,112,102,59,3,55349,56642,99,114,59,3,55349,56486,4,11,74,84,97,99,101,102,108,109,111,115,116,2508,2513,2520,2562,2585,2981,2986,3004,3011,3146,3167,99,121,59,1,1033,5,60,1,59,2518,1,60,4,5,99,109,110,112,114,2532,2538,2544,2548,2558,117,116,101,59,1,313,98,100,97,59,1,923,103,59,1,10218,108,97,99,101,116,114,102,59,1,8466,114,59,1,8606,4,3,97,101,121,2570,2576,2582,114,111,110,59,1,317,100,105,108,59,1,315,59,1,1051,4,2,102,115,2591,2907,116,4,10,65,67,68,70,82,84,85,86,97,114,2614,2663,2672,2728,2735,2760,2820,2870,2888,2895,4,2,110,114,2620,2633,103,108,101,66,114,97,99,107,101,116,59,1,10216,114,111,119,4,3,59,66,82,2644,2646,2651,1,8592,97,114,59,1,8676,105,103,104,116,65,114,114,111,119,59,1,8646,101,105,108,105,110,103,59,1,8968,111,4,2,117,119,2679,2692,98,108,101,66,114,97,99,107,101,116,59,1,10214,110,4,2,84,86,2699,2710,101,101,86,101,99,116,111,114,59,1,10593,101,99,116,111,114,4,2,59,66,2721,2723,1,8643,97,114,59,1,10585,108,111,111,114,59,1,8970,105,103,104,116,4,2,65,86,2745,2752,114,114,111,119,59,1,8596,101,99,116,111,114,59,1,10574,4,2,101,114,2766,2792,101,4,3,59,65,86,2775,2777,2784,1,8867,114,114,111,119,59,1,8612,101,99,116,111,114,59,1,10586,105,97,110,103,108,101,4,3,59,66,69,2806,2808,2813,1,8882,97,114,59,1,10703,113,117,97,108,59,1,8884,112,4,3,68,84,86,2829,2841,2852,111,119,110,86,101,99,116,111,114,59,1,10577,101,101,86,101,99,116,111,114,59,1,10592,101,99,116,111,114,4,2,59,66,2863,2865,1,8639,97,114,59,1,10584,101,99,116,111,114,4,2,59,66,2881,2883,1,8636,97,114,59,1,10578,114,114,111,119,59,1,8656,105,103,104,116,97,114,114,111,119,59,1,8660,115,4,6,69,70,71,76,83,84,2922,2936,2947,2956,2962,2974,113,117,97,108,71,114,101,97,116,101,114,59,1,8922,117,108,108,69,113,117,97,108,59,1,8806,114,101,97,116,101,114,59,1,8822,101,115,115,59,1,10913,108,97,110,116,69,113,117,97,108,59,1,10877,105,108,100,101,59,1,8818,114,59,3,55349,56591,4,2,59,101,2992,2994,1,8920,102,116,97,114,114,111,119,59,1,8666,105,100,111,116,59,1,319,4,3,110,112,119,3019,3110,3115,103,4,4,76,82,108,114,3030,3058,3070,3098,101,102,116,4,2,65,82,3039,3046,114,114,111,119,59,1,10229,105,103,104,116,65,114,114,111,119,59,1,10231,105,103,104,116,65,114,114,111,119,59,1,10230,101,102,116,4,2,97,114,3079,3086,114,114,111,119,59,1,10232,105,103,104,116,97,114,114,111,119,59,1,10234,105,103,104,116,97,114,114,111,119,59,1,10233,102,59,3,55349,56643,101,114,4,2,76,82,3123,3134,101,102,116,65,114,114,111,119,59,1,8601,105,103,104,116,65,114,114,111,119,59,1,8600,4,3,99,104,116,3154,3158,3161,114,59,1,8466,59,1,8624,114,111,107,59,1,321,59,1,8810,4,8,97,99,101,102,105,111,115,117,3188,3192,3196,3222,3227,3237,3243,3248,112,59,1,10501,121,59,1,1052,4,2,100,108,3202,3213,105,117,109,83,112,97,99,101,59,1,8287,108,105,110,116,114,102,59,1,8499,114,59,3,55349,56592,110,117,115,80,108,117,115,59,1,8723,112,102,59,3,55349,56644,99,114,59,1,8499,59,1,924,4,9,74,97,99,101,102,111,115,116,117,3271,3276,3283,3306,3422,3427,4120,4126,4137,99,121,59,1,1034,99,117,116,101,59,1,323,4,3,97,101,121,3291,3297,3303,114,111,110,59,1,327,100,105,108,59,1,325,59,1,1053,4,3,103,115,119,3314,3380,3415,97,116,105,118,101,4,3,77,84,86,3327,3340,3365,101,100,105,117,109,83,112,97,99,101,59,1,8203,104,105,4,2,99,110,3348,3357,107,83,112,97,99,101,59,1,8203,83,112,97,99,101,59,1,8203,101,114,121,84,104,105,110,83,112,97,99,101,59,1,8203,116,101,100,4,2,71,76,3389,3405,114,101,97,116,101,114,71,114,101,97,116,101,114,59,1,8811,101,115,115,76,101,115,115,59,1,8810,76,105,110,101,59,1,10,114,59,3,55349,56593,4,4,66,110,112,116,3437,3444,3460,3464,114,101,97,107,59,1,8288,66,114,101,97,107,105,110,103,83,112,97,99,101,59,1,160,102,59,1,8469,4,13,59,67,68,69,71,72,76,78,80,82,83,84,86,3492,3494,3517,3536,3578,3657,3685,3784,3823,3860,3915,4066,4107,1,10988,4,2,111,117,3500,3510,110,103,114,117,101,110,116,59,1,8802,112,67,97,112,59,1,8813,111,117,98,108,101,86,101,114,116,105,99,97,108,66,97,114,59,1,8742,4,3,108,113,120,3544,3552,3571,101,109,101,110,116,59,1,8713,117,97,108,4,2,59,84,3561,3563,1,8800,105,108,100,101,59,3,8770,824,105,115,116,115,59,1,8708,114,101,97,116,101,114,4,7,59,69,70,71,76,83,84,3600,3602,3609,3621,3631,3637,3650,1,8815,113,117,97,108,59,1,8817,117,108,108,69,113,117,97,108,59,3,8807,824,114,101,97,116,101,114,59,3,8811,824,101,115,115,59,1,8825,108,97,110,116,69,113,117,97,108,59,3,10878,824,105,108,100,101,59,1,8821,117,109,112,4,2,68,69,3666,3677,111,119,110,72,117,109,112,59,3,8782,824,113,117,97,108,59,3,8783,824,101,4,2,102,115,3692,3724,116,84,114,105,97,110,103,108,101,4,3,59,66,69,3709,3711,3717,1,8938,97,114,59,3,10703,824,113,117,97,108,59,1,8940,115,4,6,59,69,71,76,83,84,3739,3741,3748,3757,3764,3777,1,8814,113,117,97,108,59,1,8816,114,101,97,116,101,114,59,1,8824,101,115,115,59,3,8810,824,108,97,110,116,69,113,117,97,108,59,3,10877,824,105,108,100,101,59,1,8820,101,115,116,101,100,4,2,71,76,3795,3812,114,101,97,116,101,114,71,114,101,97,116,101,114,59,3,10914,824,101,115,115,76,101,115,115,59,3,10913,824,114,101,99,101,100,101,115,4,3,59,69,83,3838,3840,3848,1,8832,113,117,97,108,59,3,10927,824,108,97,110,116,69,113,117,97,108,59,1,8928,4,2,101,105,3866,3881,118,101,114,115,101,69,108,101,109,101,110,116,59,1,8716,103,104,116,84,114,105,97,110,103,108,101,4,3,59,66,69,3900,3902,3908,1,8939,97,114,59,3,10704,824,113,117,97,108,59,1,8941,4,2,113,117,3921,3973,117,97,114,101,83,117,4,2,98,112,3933,3952,115,101,116,4,2,59,69,3942,3945,3,8847,824,113,117,97,108,59,1,8930,101,114,115,101,116,4,2,59,69,3963,3966,3,8848,824,113,117,97,108,59,1,8931,4,3,98,99,112,3981,4e3,4045,115,101,116,4,2,59,69,3990,3993,3,8834,8402,113,117,97,108,59,1,8840,99,101,101,100,115,4,4,59,69,83,84,4015,4017,4025,4037,1,8833,113,117,97,108,59,3,10928,824,108,97,110,116,69,113,117,97,108,59,1,8929,105,108,100,101,59,3,8831,824,101,114,115,101,116,4,2,59,69,4056,4059,3,8835,8402,113,117,97,108,59,1,8841,105,108,100,101,4,4,59,69,70,84,4080,4082,4089,4100,1,8769,113,117,97,108,59,1,8772,117,108,108,69,113,117,97,108,59,1,8775,105,108,100,101,59,1,8777,101,114,116,105,99,97,108,66,97,114,59,1,8740,99,114,59,3,55349,56489,105,108,100,101,5,209,1,59,4135,1,209,59,1,925,4,14,69,97,99,100,102,103,109,111,112,114,115,116,117,118,4170,4176,4187,4205,4212,4217,4228,4253,4259,4292,4295,4316,4337,4346,108,105,103,59,1,338,99,117,116,101,5,211,1,59,4185,1,211,4,2,105,121,4193,4202,114,99,5,212,1,59,4200,1,212,59,1,1054,98,108,97,99,59,1,336,114,59,3,55349,56594,114,97,118,101,5,210,1,59,4226,1,210,4,3,97,101,105,4236,4241,4246,99,114,59,1,332,103,97,59,1,937,99,114,111,110,59,1,927,112,102,59,3,55349,56646,101,110,67,117,114,108,121,4,2,68,81,4272,4285,111,117,98,108,101,81,117,111,116,101,59,1,8220,117,111,116,101,59,1,8216,59,1,10836,4,2,99,108,4301,4306,114,59,3,55349,56490,97,115,104,5,216,1,59,4314,1,216,105,4,2,108,109,4323,4332,100,101,5,213,1,59,4330,1,213,101,115,59,1,10807,109,108,5,214,1,59,4344,1,214,101,114,4,2,66,80,4354,4380,4,2,97,114,4360,4364,114,59,1,8254,97,99,4,2,101,107,4372,4375,59,1,9182,101,116,59,1,9140,97,114,101,110,116,104,101,115,105,115,59,1,9180,4,9,97,99,102,104,105,108,111,114,115,4413,4422,4426,4431,4435,4438,4448,4471,4561,114,116,105,97,108,68,59,1,8706,121,59,1,1055,114,59,3,55349,56595,105,59,1,934,59,1,928,117,115,77,105,110,117,115,59,1,177,4,2,105,112,4454,4467,110,99,97,114,101,112,108,97,110,101,59,1,8460,102,59,1,8473,4,4,59,101,105,111,4481,4483,4526,4531,1,10939,99,101,100,101,115,4,4,59,69,83,84,4498,4500,4507,4519,1,8826,113,117,97,108,59,1,10927,108,97,110,116,69,113,117,97,108,59,1,8828,105,108,100,101,59,1,8830,109,101,59,1,8243,4,2,100,112,4537,4543,117,99,116,59,1,8719,111,114,116,105,111,110,4,2,59,97,4555,4557,1,8759,108,59,1,8733,4,2,99,105,4567,4572,114,59,3,55349,56491,59,1,936,4,4,85,102,111,115,4585,4594,4599,4604,79,84,5,34,1,59,4592,1,34,114,59,3,55349,56596,112,102,59,1,8474,99,114,59,3,55349,56492,4,12,66,69,97,99,101,102,104,105,111,114,115,117,4636,4642,4650,4681,4704,4763,4767,4771,5047,5069,5081,5094,97,114,114,59,1,10512,71,5,174,1,59,4648,1,174,4,3,99,110,114,4658,4664,4668,117,116,101,59,1,340,103,59,1,10219,114,4,2,59,116,4675,4677,1,8608,108,59,1,10518,4,3,97,101,121,4689,4695,4701,114,111,110,59,1,344,100,105,108,59,1,342,59,1,1056,4,2,59,118,4710,4712,1,8476,101,114,115,101,4,2,69,85,4722,4748,4,2,108,113,4728,4736,101,109,101,110,116,59,1,8715,117,105,108,105,98,114,105,117,109,59,1,8651,112,69,113,117,105,108,105,98,114,105,117,109,59,1,10607,114,59,1,8476,111,59,1,929,103,104,116,4,8,65,67,68,70,84,85,86,97,4792,4840,4849,4905,4912,4972,5022,5040,4,2,110,114,4798,4811,103,108,101,66,114,97,99,107,101,116,59,1,10217,114,111,119,4,3,59,66,76,4822,4824,4829,1,8594,97,114,59,1,8677,101,102,116,65,114,114,111,119,59,1,8644,101,105,108,105,110,103,59,1,8969,111,4,2,117,119,4856,4869,98,108,101,66,114,97,99,107,101,116,59,1,10215,110,4,2,84,86,4876,4887,101,101,86,101,99,116,111,114,59,1,10589,101,99,116,111,114,4,2,59,66,4898,4900,1,8642,97,114,59,1,10581,108,111,111,114,59,1,8971,4,2,101,114,4918,4944,101,4,3,59,65,86,4927,4929,4936,1,8866,114,114,111,119,59,1,8614,101,99,116,111,114,59,1,10587,105,97,110,103,108,101,4,3,59,66,69,4958,4960,4965,1,8883,97,114,59,1,10704,113,117,97,108,59,1,8885,112,4,3,68,84,86,4981,4993,5004,111,119,110,86,101,99,116,111,114,59,1,10575,101,101,86,101,99,116,111,114,59,1,10588,101,99,116,111,114,4,2,59,66,5015,5017,1,8638,97,114,59,1,10580,101,99,116,111,114,4,2,59,66,5033,5035,1,8640,97,114,59,1,10579,114,114,111,119,59,1,8658,4,2,112,117,5053,5057,102,59,1,8477,110,100,73,109,112,108,105,101,115,59,1,10608,105,103,104,116,97,114,114,111,119,59,1,8667,4,2,99,104,5087,5091,114,59,1,8475,59,1,8625,108,101,68,101,108,97,121,101,100,59,1,10740,4,13,72,79,97,99,102,104,105,109,111,113,115,116,117,5134,5150,5157,5164,5198,5203,5259,5265,5277,5283,5374,5380,5385,4,2,67,99,5140,5146,72,99,121,59,1,1065,121,59,1,1064,70,84,99,121,59,1,1068,99,117,116,101,59,1,346,4,5,59,97,101,105,121,5176,5178,5184,5190,5195,1,10940,114,111,110,59,1,352,100,105,108,59,1,350,114,99,59,1,348,59,1,1057,114,59,3,55349,56598,111,114,116,4,4,68,76,82,85,5216,5227,5238,5250,111,119,110,65,114,114,111,119,59,1,8595,101,102,116,65,114,114,111,119,59,1,8592,105,103,104,116,65,114,114,111,119,59,1,8594,112,65,114,114,111,119,59,1,8593,103,109,97,59,1,931,97,108,108,67,105,114,99,108,101,59,1,8728,112,102,59,3,55349,56650,4,2,114,117,5289,5293,116,59,1,8730,97,114,101,4,4,59,73,83,85,5306,5308,5322,5367,1,9633,110,116,101,114,115,101,99,116,105,111,110,59,1,8851,117,4,2,98,112,5329,5347,115,101,116,4,2,59,69,5338,5340,1,8847,113,117,97,108,59,1,8849,101,114,115,101,116,4,2,59,69,5358,5360,1,8848,113,117,97,108,59,1,8850,110,105,111,110,59,1,8852,99,114,59,3,55349,56494,97,114,59,1,8902,4,4,98,99,109,112,5395,5420,5475,5478,4,2,59,115,5401,5403,1,8912,101,116,4,2,59,69,5411,5413,1,8912,113,117,97,108,59,1,8838,4,2,99,104,5426,5468,101,101,100,115,4,4,59,69,83,84,5440,5442,5449,5461,1,8827,113,117,97,108,59,1,10928,108,97,110,116,69,113,117,97,108,59,1,8829,105,108,100,101,59,1,8831,84,104,97,116,59,1,8715,59,1,8721,4,3,59,101,115,5486,5488,5507,1,8913,114,115,101,116,4,2,59,69,5498,5500,1,8835,113,117,97,108,59,1,8839,101,116,59,1,8913,4,11,72,82,83,97,99,102,104,105,111,114,115,5536,5546,5552,5567,5579,5602,5607,5655,5695,5701,5711,79,82,78,5,222,1,59,5544,1,222,65,68,69,59,1,8482,4,2,72,99,5558,5563,99,121,59,1,1035,121,59,1,1062,4,2,98,117,5573,5576,59,1,9,59,1,932,4,3,97,101,121,5587,5593,5599,114,111,110,59,1,356,100,105,108,59,1,354,59,1,1058,114,59,3,55349,56599,4,2,101,105,5613,5631,4,2,114,116,5619,5627,101,102,111,114,101,59,1,8756,97,59,1,920,4,2,99,110,5637,5647,107,83,112,97,99,101,59,3,8287,8202,83,112,97,99,101,59,1,8201,108,100,101,4,4,59,69,70,84,5668,5670,5677,5688,1,8764,113,117,97,108,59,1,8771,117,108,108,69,113,117,97,108,59,1,8773,105,108,100,101,59,1,8776,112,102,59,3,55349,56651,105,112,108,101,68,111,116,59,1,8411,4,2,99,116,5717,5722,114,59,3,55349,56495,114,111,107,59,1,358,4,14,97,98,99,100,102,103,109,110,111,112,114,115,116,117,5758,5789,5805,5823,5830,5835,5846,5852,5921,5937,6089,6095,6101,6108,4,2,99,114,5764,5774,117,116,101,5,218,1,59,5772,1,218,114,4,2,59,111,5781,5783,1,8607,99,105,114,59,1,10569,114,4,2,99,101,5796,5800,121,59,1,1038,118,101,59,1,364,4,2,105,121,5811,5820,114,99,5,219,1,59,5818,1,219,59,1,1059,98,108,97,99,59,1,368,114,59,3,55349,56600,114,97,118,101,5,217,1,59,5844,1,217,97,99,114,59,1,362,4,2,100,105,5858,5905,101,114,4,2,66,80,5866,5892,4,2,97,114,5872,5876,114,59,1,95,97,99,4,2,101,107,5884,5887,59,1,9183,101,116,59,1,9141,97,114,101,110,116,104,101,115,105,115,59,1,9181,111,110,4,2,59,80,5913,5915,1,8899,108,117,115,59,1,8846,4,2,103,112,5927,5932,111,110,59,1,370,102,59,3,55349,56652,4,8,65,68,69,84,97,100,112,115,5955,5985,5996,6009,6026,6033,6044,6075,114,114,111,119,4,3,59,66,68,5967,5969,5974,1,8593,97,114,59,1,10514,111,119,110,65,114,114,111,119,59,1,8645,111,119,110,65,114,114,111,119,59,1,8597,113,117,105,108,105,98,114,105,117,109,59,1,10606,101,101,4,2,59,65,6017,6019,1,8869,114,114,111,119,59,1,8613,114,114,111,119,59,1,8657,111,119,110,97,114,114,111,119,59,1,8661,101,114,4,2,76,82,6052,6063,101,102,116,65,114,114,111,119,59,1,8598,105,103,104,116,65,114,114,111,119,59,1,8599,105,4,2,59,108,6082,6084,1,978,111,110,59,1,933,105,110,103,59,1,366,99,114,59,3,55349,56496,105,108,100,101,59,1,360,109,108,5,220,1,59,6115,1,220,4,9,68,98,99,100,101,102,111,115,118,6137,6143,6148,6152,6166,6250,6255,6261,6267,97,115,104,59,1,8875,97,114,59,1,10987,121,59,1,1042,97,115,104,4,2,59,108,6161,6163,1,8873,59,1,10982,4,2,101,114,6172,6175,59,1,8897,4,3,98,116,121,6183,6188,6238,97,114,59,1,8214,4,2,59,105,6194,6196,1,8214,99,97,108,4,4,66,76,83,84,6209,6214,6220,6231,97,114,59,1,8739,105,110,101,59,1,124,101,112,97,114,97,116,111,114,59,1,10072,105,108,100,101,59,1,8768,84,104,105,110,83,112,97,99,101,59,1,8202,114,59,3,55349,56601,112,102,59,3,55349,56653,99,114,59,3,55349,56497,100,97,115,104,59,1,8874,4,5,99,101,102,111,115,6286,6292,6298,6303,6309,105,114,99,59,1,372,100,103,101,59,1,8896,114,59,3,55349,56602,112,102,59,3,55349,56654,99,114,59,3,55349,56498,4,4,102,105,111,115,6325,6330,6333,6339,114,59,3,55349,56603,59,1,926,112,102,59,3,55349,56655,99,114,59,3,55349,56499,4,9,65,73,85,97,99,102,111,115,117,6365,6370,6375,6380,6391,6405,6410,6416,6422,99,121,59,1,1071,99,121,59,1,1031,99,121,59,1,1070,99,117,116,101,5,221,1,59,6389,1,221,4,2,105,121,6397,6402,114,99,59,1,374,59,1,1067,114,59,3,55349,56604,112,102,59,3,55349,56656,99,114,59,3,55349,56500,109,108,59,1,376,4,8,72,97,99,100,101,102,111,115,6445,6450,6457,6472,6477,6501,6505,6510,99,121,59,1,1046,99,117,116,101,59,1,377,4,2,97,121,6463,6469,114,111,110,59,1,381,59,1,1047,111,116,59,1,379,4,2,114,116,6483,6497,111,87,105,100,116,104,83,112,97,99,101,59,1,8203,97,59,1,918,114,59,1,8488,112,102,59,1,8484,99,114,59,3,55349,56501,4,16,97,98,99,101,102,103,108,109,110,111,112,114,115,116,117,119,6550,6561,6568,6612,6622,6634,6645,6672,6699,6854,6870,6923,6933,6963,6974,6983,99,117,116,101,5,225,1,59,6559,1,225,114,101,118,101,59,1,259,4,6,59,69,100,105,117,121,6582,6584,6588,6591,6600,6609,1,8766,59,3,8766,819,59,1,8767,114,99,5,226,1,59,6598,1,226,116,101,5,180,1,59,6607,1,180,59,1,1072,108,105,103,5,230,1,59,6620,1,230,4,2,59,114,6628,6630,1,8289,59,3,55349,56606,114,97,118,101,5,224,1,59,6643,1,224,4,2,101,112,6651,6667,4,2,102,112,6657,6663,115,121,109,59,1,8501,104,59,1,8501,104,97,59,1,945,4,2,97,112,6678,6692,4,2,99,108,6684,6688,114,59,1,257,103,59,1,10815,5,38,1,59,6697,1,38,4,2,100,103,6705,6737,4,5,59,97,100,115,118,6717,6719,6724,6727,6734,1,8743,110,100,59,1,10837,59,1,10844,108,111,112,101,59,1,10840,59,1,10842,4,7,59,101,108,109,114,115,122,6753,6755,6758,6762,6814,6835,6848,1,8736,59,1,10660,101,59,1,8736,115,100,4,2,59,97,6770,6772,1,8737,4,8,97,98,99,100,101,102,103,104,6790,6793,6796,6799,6802,6805,6808,6811,59,1,10664,59,1,10665,59,1,10666,59,1,10667,59,1,10668,59,1,10669,59,1,10670,59,1,10671,116,4,2,59,118,6821,6823,1,8735,98,4,2,59,100,6830,6832,1,8894,59,1,10653,4,2,112,116,6841,6845,104,59,1,8738,59,1,197,97,114,114,59,1,9084,4,2,103,112,6860,6865,111,110,59,1,261,102,59,3,55349,56658,4,7,59,69,97,101,105,111,112,6886,6888,6891,6897,6900,6904,6908,1,8776,59,1,10864,99,105,114,59,1,10863,59,1,8778,100,59,1,8779,115,59,1,39,114,111,120,4,2,59,101,6917,6919,1,8776,113,59,1,8778,105,110,103,5,229,1,59,6931,1,229,4,3,99,116,121,6941,6946,6949,114,59,3,55349,56502,59,1,42,109,112,4,2,59,101,6957,6959,1,8776,113,59,1,8781,105,108,100,101,5,227,1,59,6972,1,227,109,108,5,228,1,59,6981,1,228,4,2,99,105,6989,6997,111,110,105,110,116,59,1,8755,110,116,59,1,10769,4,16,78,97,98,99,100,101,102,105,107,108,110,111,112,114,115,117,7036,7041,7119,7135,7149,7155,7219,7224,7347,7354,7463,7489,7786,7793,7814,7866,111,116,59,1,10989,4,2,99,114,7047,7094,107,4,4,99,101,112,115,7058,7064,7073,7080,111,110,103,59,1,8780,112,115,105,108,111,110,59,1,1014,114,105,109,101,59,1,8245,105,109,4,2,59,101,7088,7090,1,8765,113,59,1,8909,4,2,118,119,7100,7105,101,101,59,1,8893,101,100,4,2,59,103,7113,7115,1,8965,101,59,1,8965,114,107,4,2,59,116,7127,7129,1,9141,98,114,107,59,1,9142,4,2,111,121,7141,7146,110,103,59,1,8780,59,1,1073,113,117,111,59,1,8222,4,5,99,109,112,114,116,7167,7181,7188,7193,7199,97,117,115,4,2,59,101,7176,7178,1,8757,59,1,8757,112,116,121,118,59,1,10672,115,105,59,1,1014,110,111,117,59,1,8492,4,3,97,104,119,7207,7210,7213,59,1,946,59,1,8502,101,101,110,59,1,8812,114,59,3,55349,56607,103,4,7,99,111,115,116,117,118,119,7241,7262,7288,7305,7328,7335,7340,4,3,97,105,117,7249,7253,7258,112,59,1,8898,114,99,59,1,9711,112,59,1,8899,4,3,100,112,116,7270,7275,7281,111,116,59,1,10752,108,117,115,59,1,10753,105,109,101,115,59,1,10754,4,2,113,116,7294,7300,99,117,112,59,1,10758,97,114,59,1,9733,114,105,97,110,103,108,101,4,2,100,117,7318,7324,111,119,110,59,1,9661,112,59,1,9651,112,108,117,115,59,1,10756,101,101,59,1,8897,101,100,103,101,59,1,8896,97,114,111,119,59,1,10509,4,3,97,107,111,7362,7436,7458,4,2,99,110,7368,7432,107,4,3,108,115,116,7377,7386,7394,111,122,101,110,103,101,59,1,10731,113,117,97,114,101,59,1,9642,114,105,97,110,103,108,101,4,4,59,100,108,114,7411,7413,7419,7425,1,9652,111,119,110,59,1,9662,101,102,116,59,1,9666,105,103,104,116,59,1,9656,107,59,1,9251,4,2,49,51,7442,7454,4,2,50,52,7448,7451,59,1,9618,59,1,9617,52,59,1,9619,99,107,59,1,9608,4,2,101,111,7469,7485,4,2,59,113,7475,7478,3,61,8421,117,105,118,59,3,8801,8421,116,59,1,8976,4,4,112,116,119,120,7499,7504,7517,7523,102,59,3,55349,56659,4,2,59,116,7510,7512,1,8869,111,109,59,1,8869,116,105,101,59,1,8904,4,12,68,72,85,86,98,100,104,109,112,116,117,118,7549,7571,7597,7619,7655,7660,7682,7708,7715,7721,7728,7750,4,4,76,82,108,114,7559,7562,7565,7568,59,1,9559,59,1,9556,59,1,9558,59,1,9555,4,5,59,68,85,100,117,7583,7585,7588,7591,7594,1,9552,59,1,9574,59,1,9577,59,1,9572,59,1,9575,4,4,76,82,108,114,7607,7610,7613,7616,59,1,9565,59,1,9562,59,1,9564,59,1,9561,4,7,59,72,76,82,104,108,114,7635,7637,7640,7643,7646,7649,7652,1,9553,59,1,9580,59,1,9571,59,1,9568,59,1,9579,59,1,9570,59,1,9567,111,120,59,1,10697,4,4,76,82,108,114,7670,7673,7676,7679,59,1,9557,59,1,9554,59,1,9488,59,1,9484,4,5,59,68,85,100,117,7694,7696,7699,7702,7705,1,9472,59,1,9573,59,1,9576,59,1,9516,59,1,9524,105,110,117,115,59,1,8863,108,117,115,59,1,8862,105,109,101,115,59,1,8864,4,4,76,82,108,114,7738,7741,7744,7747,59,1,9563,59,1,9560,59,1,9496,59,1,9492,4,7,59,72,76,82,104,108,114,7766,7768,7771,7774,7777,7780,7783,1,9474,59,1,9578,59,1,9569,59,1,9566,59,1,9532,59,1,9508,59,1,9500,114,105,109,101,59,1,8245,4,2,101,118,7799,7804,118,101,59,1,728,98,97,114,5,166,1,59,7812,1,166,4,4,99,101,105,111,7824,7829,7834,7846,114,59,3,55349,56503,109,105,59,1,8271,109,4,2,59,101,7841,7843,1,8765,59,1,8909,108,4,3,59,98,104,7855,7857,7860,1,92,59,1,10693,115,117,98,59,1,10184,4,2,108,109,7872,7885,108,4,2,59,101,7879,7881,1,8226,116,59,1,8226,112,4,3,59,69,101,7894,7896,7899,1,8782,59,1,10926,4,2,59,113,7905,7907,1,8783,59,1,8783,4,15,97,99,100,101,102,104,105,108,111,114,115,116,117,119,121,7942,8021,8075,8080,8121,8126,8157,8279,8295,8430,8446,8485,8491,8707,8726,4,3,99,112,114,7950,7956,8007,117,116,101,59,1,263,4,6,59,97,98,99,100,115,7970,7972,7977,7984,7998,8003,1,8745,110,100,59,1,10820,114,99,117,112,59,1,10825,4,2,97,117,7990,7994,112,59,1,10827,112,59,1,10823,111,116,59,1,10816,59,3,8745,65024,4,2,101,111,8013,8017,116,59,1,8257,110,59,1,711,4,4,97,101,105,117,8031,8046,8056,8061,4,2,112,114,8037,8041,115,59,1,10829,111,110,59,1,269,100,105,108,5,231,1,59,8054,1,231,114,99,59,1,265,112,115,4,2,59,115,8069,8071,1,10828,109,59,1,10832,111,116,59,1,267,4,3,100,109,110,8088,8097,8104,105,108,5,184,1,59,8095,1,184,112,116,121,118,59,1,10674,116,5,162,2,59,101,8112,8114,1,162,114,100,111,116,59,1,183,114,59,3,55349,56608,4,3,99,101,105,8134,8138,8154,121,59,1,1095,99,107,4,2,59,109,8146,8148,1,10003,97,114,107,59,1,10003,59,1,967,114,4,7,59,69,99,101,102,109,115,8174,8176,8179,8258,8261,8268,8273,1,9675,59,1,10691,4,3,59,101,108,8187,8189,8193,1,710,113,59,1,8791,101,4,2,97,100,8200,8223,114,114,111,119,4,2,108,114,8210,8216,101,102,116,59,1,8634,105,103,104,116,59,1,8635,4,5,82,83,97,99,100,8235,8238,8241,8246,8252,59,1,174,59,1,9416,115,116,59,1,8859,105,114,99,59,1,8858,97,115,104,59,1,8861,59,1,8791,110,105,110,116,59,1,10768,105,100,59,1,10991,99,105,114,59,1,10690,117,98,115,4,2,59,117,8288,8290,1,9827,105,116,59,1,9827,4,4,108,109,110,112,8305,8326,8376,8400,111,110,4,2,59,101,8313,8315,1,58,4,2,59,113,8321,8323,1,8788,59,1,8788,4,2,109,112,8332,8344,97,4,2,59,116,8339,8341,1,44,59,1,64,4,3,59,102,108,8352,8354,8358,1,8705,110,59,1,8728,101,4,2,109,120,8365,8371,101,110,116,59,1,8705,101,115,59,1,8450,4,2,103,105,8382,8395,4,2,59,100,8388,8390,1,8773,111,116,59,1,10861,110,116,59,1,8750,4,3,102,114,121,8408,8412,8417,59,3,55349,56660,111,100,59,1,8720,5,169,2,59,115,8424,8426,1,169,114,59,1,8471,4,2,97,111,8436,8441,114,114,59,1,8629,115,115,59,1,10007,4,2,99,117,8452,8457,114,59,3,55349,56504,4,2,98,112,8463,8474,4,2,59,101,8469,8471,1,10959,59,1,10961,4,2,59,101,8480,8482,1,10960,59,1,10962,100,111,116,59,1,8943,4,7,100,101,108,112,114,118,119,8507,8522,8536,8550,8600,8697,8702,97,114,114,4,2,108,114,8516,8519,59,1,10552,59,1,10549,4,2,112,115,8528,8532,114,59,1,8926,99,59,1,8927,97,114,114,4,2,59,112,8545,8547,1,8630,59,1,10557,4,6,59,98,99,100,111,115,8564,8566,8573,8587,8592,8596,1,8746,114,99,97,112,59,1,10824,4,2,97,117,8579,8583,112,59,1,10822,112,59,1,10826,111,116,59,1,8845,114,59,1,10821,59,3,8746,65024,4,4,97,108,114,118,8610,8623,8663,8672,114,114,4,2,59,109,8618,8620,1,8631,59,1,10556,121,4,3,101,118,119,8632,8651,8656,113,4,2,112,115,8639,8645,114,101,99,59,1,8926,117,99,99,59,1,8927,101,101,59,1,8910,101,100,103,101,59,1,8911,101,110,5,164,1,59,8670,1,164,101,97,114,114,111,119,4,2,108,114,8684,8690,101,102,116,59,1,8630,105,103,104,116,59,1,8631,101,101,59,1,8910,101,100,59,1,8911,4,2,99,105,8713,8721,111,110,105,110,116,59,1,8754,110,116,59,1,8753,108,99,116,121,59,1,9005,4,19,65,72,97,98,99,100,101,102,104,105,106,108,111,114,115,116,117,119,122,8773,8778,8783,8821,8839,8854,8887,8914,8930,8944,9036,9041,9058,9197,9227,9258,9281,9297,9305,114,114,59,1,8659,97,114,59,1,10597,4,4,103,108,114,115,8793,8799,8805,8809,103,101,114,59,1,8224,101,116,104,59,1,8504,114,59,1,8595,104,4,2,59,118,8816,8818,1,8208,59,1,8867,4,2,107,108,8827,8834,97,114,111,119,59,1,10511,97,99,59,1,733,4,2,97,121,8845,8851,114,111,110,59,1,271,59,1,1076,4,3,59,97,111,8862,8864,8880,1,8518,4,2,103,114,8870,8876,103,101,114,59,1,8225,114,59,1,8650,116,115,101,113,59,1,10871,4,3,103,108,109,8895,8902,8907,5,176,1,59,8900,1,176,116,97,59,1,948,112,116,121,118,59,1,10673,4,2,105,114,8920,8926,115,104,116,59,1,10623,59,3,55349,56609,97,114,4,2,108,114,8938,8941,59,1,8643,59,1,8642,4,5,97,101,103,115,118,8956,8986,8989,8996,9001,109,4,3,59,111,115,8965,8967,8983,1,8900,110,100,4,2,59,115,8975,8977,1,8900,117,105,116,59,1,9830,59,1,9830,59,1,168,97,109,109,97,59,1,989,105,110,59,1,8946,4,3,59,105,111,9009,9011,9031,1,247,100,101,5,247,2,59,111,9020,9022,1,247,110,116,105,109,101,115,59,1,8903,110,120,59,1,8903,99,121,59,1,1106,99,4,2,111,114,9048,9053,114,110,59,1,8990,111,112,59,1,8973,4,5,108,112,116,117,119,9070,9076,9081,9130,9144,108,97,114,59,1,36,102,59,3,55349,56661,4,5,59,101,109,112,115,9093,9095,9109,9116,9122,1,729,113,4,2,59,100,9102,9104,1,8784,111,116,59,1,8785,105,110,117,115,59,1,8760,108,117,115,59,1,8724,113,117,97,114,101,59,1,8865,98,108,101,98,97,114,119,101,100,103,101,59,1,8966,110,4,3,97,100,104,9153,9160,9172,114,114,111,119,59,1,8595,111,119,110,97,114,114,111,119,115,59,1,8650,97,114,112,111,111,110,4,2,108,114,9184,9190,101,102,116,59,1,8643,105,103,104,116,59,1,8642,4,2,98,99,9203,9211,107,97,114,111,119,59,1,10512,4,2,111,114,9217,9222,114,110,59,1,8991,111,112,59,1,8972,4,3,99,111,116,9235,9248,9252,4,2,114,121,9241,9245,59,3,55349,56505,59,1,1109,108,59,1,10742,114,111,107,59,1,273,4,2,100,114,9264,9269,111,116,59,1,8945,105,4,2,59,102,9276,9278,1,9663,59,1,9662,4,2,97,104,9287,9292,114,114,59,1,8693,97,114,59,1,10607,97,110,103,108,101,59,1,10662,4,2,99,105,9311,9315,121,59,1,1119,103,114,97,114,114,59,1,10239,4,18,68,97,99,100,101,102,103,108,109,110,111,112,113,114,115,116,117,120,9361,9376,9398,9439,9444,9447,9462,9495,9531,9585,9598,9614,9659,9755,9771,9792,9808,9826,4,2,68,111,9367,9372,111,116,59,1,10871,116,59,1,8785,4,2,99,115,9382,9392,117,116,101,5,233,1,59,9390,1,233,116,101,114,59,1,10862,4,4,97,105,111,121,9408,9414,9430,9436,114,111,110,59,1,283,114,4,2,59,99,9421,9423,1,8790,5,234,1,59,9428,1,234,108,111,110,59,1,8789,59,1,1101,111,116,59,1,279,59,1,8519,4,2,68,114,9453,9458,111,116,59,1,8786,59,3,55349,56610,4,3,59,114,115,9470,9472,9482,1,10906,97,118,101,5,232,1,59,9480,1,232,4,2,59,100,9488,9490,1,10902,111,116,59,1,10904,4,4,59,105,108,115,9505,9507,9515,9518,1,10905,110,116,101,114,115,59,1,9191,59,1,8467,4,2,59,100,9524,9526,1,10901,111,116,59,1,10903,4,3,97,112,115,9539,9544,9564,99,114,59,1,275,116,121,4,3,59,115,118,9554,9556,9561,1,8709,101,116,59,1,8709,59,1,8709,112,4,2,49,59,9571,9583,4,2,51,52,9577,9580,59,1,8196,59,1,8197,1,8195,4,2,103,115,9591,9594,59,1,331,112,59,1,8194,4,2,103,112,9604,9609,111,110,59,1,281,102,59,3,55349,56662,4,3,97,108,115,9622,9635,9640,114,4,2,59,115,9629,9631,1,8917,108,59,1,10723,117,115,59,1,10865,105,4,3,59,108,118,9649,9651,9656,1,949,111,110,59,1,949,59,1,1013,4,4,99,115,117,118,9669,9686,9716,9747,4,2,105,111,9675,9680,114,99,59,1,8790,108,111,110,59,1,8789,4,2,105,108,9692,9696,109,59,1,8770,97,110,116,4,2,103,108,9705,9710,116,114,59,1,10902,101,115,115,59,1,10901,4,3,97,101,105,9724,9729,9734,108,115,59,1,61,115,116,59,1,8799,118,4,2,59,68,9741,9743,1,8801,68,59,1,10872,112,97,114,115,108,59,1,10725,4,2,68,97,9761,9766,111,116,59,1,8787,114,114,59,1,10609,4,3,99,100,105,9779,9783,9788,114,59,1,8495,111,116,59,1,8784,109,59,1,8770,4,2,97,104,9798,9801,59,1,951,5,240,1,59,9806,1,240,4,2,109,114,9814,9822,108,5,235,1,59,9820,1,235,111,59,1,8364,4,3,99,105,112,9834,9838,9843,108,59,1,33,115,116,59,1,8707,4,2,101,111,9849,9859,99,116,97,116,105,111,110,59,1,8496,110,101,110,116,105,97,108,101,59,1,8519,4,12,97,99,101,102,105,106,108,110,111,112,114,115,9896,9910,9914,9921,9954,9960,9967,9989,9994,10027,10036,10164,108,108,105,110,103,100,111,116,115,101,113,59,1,8786,121,59,1,1092,109,97,108,101,59,1,9792,4,3,105,108,114,9929,9935,9950,108,105,103,59,1,64259,4,2,105,108,9941,9945,103,59,1,64256,105,103,59,1,64260,59,3,55349,56611,108,105,103,59,1,64257,108,105,103,59,3,102,106,4,3,97,108,116,9975,9979,9984,116,59,1,9837,105,103,59,1,64258,110,115,59,1,9649,111,102,59,1,402,4,2,112,114,1e4,10005,102,59,3,55349,56663,4,2,97,107,10011,10016,108,108,59,1,8704,4,2,59,118,10022,10024,1,8916,59,1,10969,97,114,116,105,110,116,59,1,10765,4,2,97,111,10042,10159,4,2,99,115,10048,10155,4,6,49,50,51,52,53,55,10062,10102,10114,10135,10139,10151,4,6,50,51,52,53,54,56,10076,10083,10086,10093,10096,10099,5,189,1,59,10081,1,189,59,1,8531,5,188,1,59,10091,1,188,59,1,8533,59,1,8537,59,1,8539,4,2,51,53,10108,10111,59,1,8532,59,1,8534,4,3,52,53,56,10122,10129,10132,5,190,1,59,10127,1,190,59,1,8535,59,1,8540,53,59,1,8536,4,2,54,56,10145,10148,59,1,8538,59,1,8541,56,59,1,8542,108,59,1,8260,119,110,59,1,8994,99,114,59,3,55349,56507,4,17,69,97,98,99,100,101,102,103,105,106,108,110,111,114,115,116,118,10206,10217,10247,10254,10268,10273,10358,10363,10374,10380,10385,10406,10458,10464,10470,10497,10610,4,2,59,108,10212,10214,1,8807,59,1,10892,4,3,99,109,112,10225,10231,10244,117,116,101,59,1,501,109,97,4,2,59,100,10239,10241,1,947,59,1,989,59,1,10886,114,101,118,101,59,1,287,4,2,105,121,10260,10265,114,99,59,1,285,59,1,1075,111,116,59,1,289,4,4,59,108,113,115,10283,10285,10288,10308,1,8805,59,1,8923,4,3,59,113,115,10296,10298,10301,1,8805,59,1,8807,108,97,110,116,59,1,10878,4,4,59,99,100,108,10318,10320,10324,10345,1,10878,99,59,1,10921,111,116,4,2,59,111,10332,10334,1,10880,4,2,59,108,10340,10342,1,10882,59,1,10884,4,2,59,101,10351,10354,3,8923,65024,115,59,1,10900,114,59,3,55349,56612,4,2,59,103,10369,10371,1,8811,59,1,8921,109,101,108,59,1,8503,99,121,59,1,1107,4,4,59,69,97,106,10395,10397,10400,10403,1,8823,59,1,10898,59,1,10917,59,1,10916,4,4,69,97,101,115,10416,10419,10434,10453,59,1,8809,112,4,2,59,112,10426,10428,1,10890,114,111,120,59,1,10890,4,2,59,113,10440,10442,1,10888,4,2,59,113,10448,10450,1,10888,59,1,8809,105,109,59,1,8935,112,102,59,3,55349,56664,97,118,101,59,1,96,4,2,99,105,10476,10480,114,59,1,8458,109,4,3,59,101,108,10489,10491,10494,1,8819,59,1,10894,59,1,10896,5,62,6,59,99,100,108,113,114,10512,10514,10527,10532,10538,10545,1,62,4,2,99,105,10520,10523,59,1,10919,114,59,1,10874,111,116,59,1,8919,80,97,114,59,1,10645,117,101,115,116,59,1,10876,4,5,97,100,101,108,115,10557,10574,10579,10599,10605,4,2,112,114,10563,10570,112,114,111,120,59,1,10886,114,59,1,10616,111,116,59,1,8919,113,4,2,108,113,10586,10592,101,115,115,59,1,8923,108,101,115,115,59,1,10892,101,115,115,59,1,8823,105,109,59,1,8819,4,2,101,110,10616,10626,114,116,110,101,113,113,59,3,8809,65024,69,59,3,8809,65024,4,10,65,97,98,99,101,102,107,111,115,121,10653,10658,10713,10718,10724,10760,10765,10786,10850,10875,114,114,59,1,8660,4,4,105,108,109,114,10668,10674,10678,10684,114,115,112,59,1,8202,102,59,1,189,105,108,116,59,1,8459,4,2,100,114,10690,10695,99,121,59,1,1098,4,3,59,99,119,10703,10705,10710,1,8596,105,114,59,1,10568,59,1,8621,97,114,59,1,8463,105,114,99,59,1,293,4,3,97,108,114,10732,10748,10754,114,116,115,4,2,59,117,10741,10743,1,9829,105,116,59,1,9829,108,105,112,59,1,8230,99,111,110,59,1,8889,114,59,3,55349,56613,115,4,2,101,119,10772,10779,97,114,111,119,59,1,10533,97,114,111,119,59,1,10534,4,5,97,109,111,112,114,10798,10803,10809,10839,10844,114,114,59,1,8703,116,104,116,59,1,8763,107,4,2,108,114,10816,10827,101,102,116,97,114,114,111,119,59,1,8617,105,103,104,116,97,114,114,111,119,59,1,8618,102,59,3,55349,56665,98,97,114,59,1,8213,4,3,99,108,116,10858,10863,10869,114,59,3,55349,56509,97,115,104,59,1,8463,114,111,107,59,1,295,4,2,98,112,10881,10887,117,108,108,59,1,8259,104,101,110,59,1,8208,4,15,97,99,101,102,103,105,106,109,110,111,112,113,115,116,117,10925,10936,10958,10977,10990,11001,11039,11045,11101,11192,11220,11226,11237,11285,11299,99,117,116,101,5,237,1,59,10934,1,237,4,3,59,105,121,10944,10946,10955,1,8291,114,99,5,238,1,59,10953,1,238,59,1,1080,4,2,99,120,10964,10968,121,59,1,1077,99,108,5,161,1,59,10975,1,161,4,2,102,114,10983,10986,59,1,8660,59,3,55349,56614,114,97,118,101,5,236,1,59,10999,1,236,4,4,59,105,110,111,11011,11013,11028,11034,1,8520,4,2,105,110,11019,11024,110,116,59,1,10764,116,59,1,8749,102,105,110,59,1,10716,116,97,59,1,8489,108,105,103,59,1,307,4,3,97,111,112,11053,11092,11096,4,3,99,103,116,11061,11065,11088,114,59,1,299,4,3,101,108,112,11073,11076,11082,59,1,8465,105,110,101,59,1,8464,97,114,116,59,1,8465,104,59,1,305,102,59,1,8887,101,100,59,1,437,4,5,59,99,102,111,116,11113,11115,11121,11136,11142,1,8712,97,114,101,59,1,8453,105,110,4,2,59,116,11129,11131,1,8734,105,101,59,1,10717,100,111,116,59,1,305,4,5,59,99,101,108,112,11154,11156,11161,11179,11186,1,8747,97,108,59,1,8890,4,2,103,114,11167,11173,101,114,115,59,1,8484,99,97,108,59,1,8890,97,114,104,107,59,1,10775,114,111,100,59,1,10812,4,4,99,103,112,116,11202,11206,11211,11216,121,59,1,1105,111,110,59,1,303,102,59,3,55349,56666,97,59,1,953,114,111,100,59,1,10812,117,101,115,116,5,191,1,59,11235,1,191,4,2,99,105,11243,11248,114,59,3,55349,56510,110,4,5,59,69,100,115,118,11261,11263,11266,11271,11282,1,8712,59,1,8953,111,116,59,1,8949,4,2,59,118,11277,11279,1,8948,59,1,8947,59,1,8712,4,2,59,105,11291,11293,1,8290,108,100,101,59,1,297,4,2,107,109,11305,11310,99,121,59,1,1110,108,5,239,1,59,11316,1,239,4,6,99,102,109,111,115,117,11332,11346,11351,11357,11363,11380,4,2,105,121,11338,11343,114,99,59,1,309,59,1,1081,114,59,3,55349,56615,97,116,104,59,1,567,112,102,59,3,55349,56667,4,2,99,101,11369,11374,114,59,3,55349,56511,114,99,121,59,1,1112,107,99,121,59,1,1108,4,8,97,99,102,103,104,106,111,115,11404,11418,11433,11438,11445,11450,11455,11461,112,112,97,4,2,59,118,11413,11415,1,954,59,1,1008,4,2,101,121,11424,11430,100,105,108,59,1,311,59,1,1082,114,59,3,55349,56616,114,101,101,110,59,1,312,99,121,59,1,1093,99,121,59,1,1116,112,102,59,3,55349,56668,99,114,59,3,55349,56512,4,23,65,66,69,72,97,98,99,100,101,102,103,104,106,108,109,110,111,112,114,115,116,117,118,11515,11538,11544,11555,11560,11721,11780,11818,11868,12136,12160,12171,12203,12208,12246,12275,12327,12509,12523,12569,12641,12732,12752,4,3,97,114,116,11523,11528,11532,114,114,59,1,8666,114,59,1,8656,97,105,108,59,1,10523,97,114,114,59,1,10510,4,2,59,103,11550,11552,1,8806,59,1,10891,97,114,59,1,10594,4,9,99,101,103,109,110,112,113,114,116,11580,11586,11594,11600,11606,11624,11627,11636,11694,117,116,101,59,1,314,109,112,116,121,118,59,1,10676,114,97,110,59,1,8466,98,100,97,59,1,955,103,4,3,59,100,108,11615,11617,11620,1,10216,59,1,10641,101,59,1,10216,59,1,10885,117,111,5,171,1,59,11634,1,171,114,4,8,59,98,102,104,108,112,115,116,11655,11657,11669,11673,11677,11681,11685,11690,1,8592,4,2,59,102,11663,11665,1,8676,115,59,1,10527,115,59,1,10525,107,59,1,8617,112,59,1,8619,108,59,1,10553,105,109,59,1,10611,108,59,1,8610,4,3,59,97,101,11702,11704,11709,1,10923,105,108,59,1,10521,4,2,59,115,11715,11717,1,10925,59,3,10925,65024,4,3,97,98,114,11729,11734,11739,114,114,59,1,10508,114,107,59,1,10098,4,2,97,107,11745,11758,99,4,2,101,107,11752,11755,59,1,123,59,1,91,4,2,101,115,11764,11767,59,1,10635,108,4,2,100,117,11774,11777,59,1,10639,59,1,10637,4,4,97,101,117,121,11790,11796,11811,11815,114,111,110,59,1,318,4,2,100,105,11802,11807,105,108,59,1,316,108,59,1,8968,98,59,1,123,59,1,1083,4,4,99,113,114,115,11828,11832,11845,11864,97,59,1,10550,117,111,4,2,59,114,11840,11842,1,8220,59,1,8222,4,2,100,117,11851,11857,104,97,114,59,1,10599,115,104,97,114,59,1,10571,104,59,1,8626,4,5,59,102,103,113,115,11880,11882,12008,12011,12031,1,8804,116,4,5,97,104,108,114,116,11895,11913,11935,11947,11996,114,114,111,119,4,2,59,116,11905,11907,1,8592,97,105,108,59,1,8610,97,114,112,111,111,110,4,2,100,117,11925,11931,111,119,110,59,1,8637,112,59,1,8636,101,102,116,97,114,114,111,119,115,59,1,8647,105,103,104,116,4,3,97,104,115,11959,11974,11984,114,114,111,119,4,2,59,115,11969,11971,1,8596,59,1,8646,97,114,112,111,111,110,115,59,1,8651,113,117,105,103,97,114,114,111,119,59,1,8621,104,114,101,101,116,105,109,101,115,59,1,8907,59,1,8922,4,3,59,113,115,12019,12021,12024,1,8804,59,1,8806,108,97,110,116,59,1,10877,4,5,59,99,100,103,115,12043,12045,12049,12070,12083,1,10877,99,59,1,10920,111,116,4,2,59,111,12057,12059,1,10879,4,2,59,114,12065,12067,1,10881,59,1,10883,4,2,59,101,12076,12079,3,8922,65024,115,59,1,10899,4,5,97,100,101,103,115,12095,12103,12108,12126,12131,112,112,114,111,120,59,1,10885,111,116,59,1,8918,113,4,2,103,113,12115,12120,116,114,59,1,8922,103,116,114,59,1,10891,116,114,59,1,8822,105,109,59,1,8818,4,3,105,108,114,12144,12150,12156,115,104,116,59,1,10620,111,111,114,59,1,8970,59,3,55349,56617,4,2,59,69,12166,12168,1,8822,59,1,10897,4,2,97,98,12177,12198,114,4,2,100,117,12184,12187,59,1,8637,4,2,59,108,12193,12195,1,8636,59,1,10602,108,107,59,1,9604,99,121,59,1,1113,4,5,59,97,99,104,116,12220,12222,12227,12235,12241,1,8810,114,114,59,1,8647,111,114,110,101,114,59,1,8990,97,114,100,59,1,10603,114,105,59,1,9722,4,2,105,111,12252,12258,100,111,116,59,1,320,117,115,116,4,2,59,97,12267,12269,1,9136,99,104,101,59,1,9136,4,4,69,97,101,115,12285,12288,12303,12322,59,1,8808,112,4,2,59,112,12295,12297,1,10889,114,111,120,59,1,10889,4,2,59,113,12309,12311,1,10887,4,2,59,113,12317,12319,1,10887,59,1,8808,105,109,59,1,8934,4,8,97,98,110,111,112,116,119,122,12345,12359,12364,12421,12446,12467,12474,12490,4,2,110,114,12351,12355,103,59,1,10220,114,59,1,8701,114,107,59,1,10214,103,4,3,108,109,114,12373,12401,12409,101,102,116,4,2,97,114,12382,12389,114,114,111,119,59,1,10229,105,103,104,116,97,114,114,111,119,59,1,10231,97,112,115,116,111,59,1,10236,105,103,104,116,97,114,114,111,119,59,1,10230,112,97,114,114,111,119,4,2,108,114,12433,12439,101,102,116,59,1,8619,105,103,104,116,59,1,8620,4,3,97,102,108,12454,12458,12462,114,59,1,10629,59,3,55349,56669,117,115,59,1,10797,105,109,101,115,59,1,10804,4,2,97,98,12480,12485,115,116,59,1,8727,97,114,59,1,95,4,3,59,101,102,12498,12500,12506,1,9674,110,103,101,59,1,9674,59,1,10731,97,114,4,2,59,108,12517,12519,1,40,116,59,1,10643,4,5,97,99,104,109,116,12535,12540,12548,12561,12564,114,114,59,1,8646,111,114,110,101,114,59,1,8991,97,114,4,2,59,100,12556,12558,1,8651,59,1,10605,59,1,8206,114,105,59,1,8895,4,6,97,99,104,105,113,116,12583,12589,12594,12597,12614,12635,113,117,111,59,1,8249,114,59,3,55349,56513,59,1,8624,109,4,3,59,101,103,12606,12608,12611,1,8818,59,1,10893,59,1,10895,4,2,98,117,12620,12623,59,1,91,111,4,2,59,114,12630,12632,1,8216,59,1,8218,114,111,107,59,1,322,5,60,8,59,99,100,104,105,108,113,114,12660,12662,12675,12680,12686,12692,12698,12705,1,60,4,2,99,105,12668,12671,59,1,10918,114,59,1,10873,111,116,59,1,8918,114,101,101,59,1,8907,109,101,115,59,1,8905,97,114,114,59,1,10614,117,101,115,116,59,1,10875,4,2,80,105,12711,12716,97,114,59,1,10646,4,3,59,101,102,12724,12726,12729,1,9667,59,1,8884,59,1,9666,114,4,2,100,117,12739,12746,115,104,97,114,59,1,10570,104,97,114,59,1,10598,4,2,101,110,12758,12768,114,116,110,101,113,113,59,3,8808,65024,69,59,3,8808,65024,4,14,68,97,99,100,101,102,104,105,108,110,111,112,115,117,12803,12809,12893,12908,12914,12928,12933,12937,13011,13025,13032,13049,13052,13069,68,111,116,59,1,8762,4,4,99,108,112,114,12819,12827,12849,12887,114,5,175,1,59,12825,1,175,4,2,101,116,12833,12836,59,1,9794,4,2,59,101,12842,12844,1,10016,115,101,59,1,10016,4,2,59,115,12855,12857,1,8614,116,111,4,4,59,100,108,117,12869,12871,12877,12883,1,8614,111,119,110,59,1,8615,101,102,116,59,1,8612,112,59,1,8613,107,101,114,59,1,9646,4,2,111,121,12899,12905,109,109,97,59,1,10793,59,1,1084,97,115,104,59,1,8212,97,115,117,114,101,100,97,110,103,108,101,59,1,8737,114,59,3,55349,56618,111,59,1,8487,4,3,99,100,110,12945,12954,12985,114,111,5,181,1,59,12952,1,181,4,4,59,97,99,100,12964,12966,12971,12976,1,8739,115,116,59,1,42,105,114,59,1,10992,111,116,5,183,1,59,12983,1,183,117,115,4,3,59,98,100,12995,12997,13e3,1,8722,59,1,8863,4,2,59,117,13006,13008,1,8760,59,1,10794,4,2,99,100,13017,13021,112,59,1,10971,114,59,1,8230,112,108,117,115,59,1,8723,4,2,100,112,13038,13044,101,108,115,59,1,8871,102,59,3,55349,56670,59,1,8723,4,2,99,116,13058,13063,114,59,3,55349,56514,112,111,115,59,1,8766,4,3,59,108,109,13077,13079,13087,1,956,116,105,109,97,112,59,1,8888,97,112,59,1,8888,4,24,71,76,82,86,97,98,99,100,101,102,103,104,105,106,108,109,111,112,114,115,116,117,118,119,13142,13165,13217,13229,13247,13330,13359,13414,13420,13508,13513,13579,13602,13626,13631,13762,13767,13855,13936,13995,14214,14285,14312,14432,4,2,103,116,13148,13152,59,3,8921,824,4,2,59,118,13158,13161,3,8811,8402,59,3,8811,824,4,3,101,108,116,13173,13200,13204,102,116,4,2,97,114,13181,13188,114,114,111,119,59,1,8653,105,103,104,116,97,114,114,111,119,59,1,8654,59,3,8920,824,4,2,59,118,13210,13213,3,8810,8402,59,3,8810,824,105,103,104,116,97,114,114,111,119,59,1,8655,4,2,68,100,13235,13241,97,115,104,59,1,8879,97,115,104,59,1,8878,4,5,98,99,110,112,116,13259,13264,13270,13275,13308,108,97,59,1,8711,117,116,101,59,1,324,103,59,3,8736,8402,4,5,59,69,105,111,112,13287,13289,13293,13298,13302,1,8777,59,3,10864,824,100,59,3,8779,824,115,59,1,329,114,111,120,59,1,8777,117,114,4,2,59,97,13316,13318,1,9838,108,4,2,59,115,13325,13327,1,9838,59,1,8469,4,2,115,117,13336,13344,112,5,160,1,59,13342,1,160,109,112,4,2,59,101,13352,13355,3,8782,824,59,3,8783,824,4,5,97,101,111,117,121,13371,13385,13391,13407,13411,4,2,112,114,13377,13380,59,1,10819,111,110,59,1,328,100,105,108,59,1,326,110,103,4,2,59,100,13399,13401,1,8775,111,116,59,3,10861,824,112,59,1,10818,59,1,1085,97,115,104,59,1,8211,4,7,59,65,97,100,113,115,120,13436,13438,13443,13466,13472,13478,13494,1,8800,114,114,59,1,8663,114,4,2,104,114,13450,13454,107,59,1,10532,4,2,59,111,13460,13462,1,8599,119,59,1,8599,111,116,59,3,8784,824,117,105,118,59,1,8802,4,2,101,105,13484,13489,97,114,59,1,10536,109,59,3,8770,824,105,115,116,4,2,59,115,13503,13505,1,8708,59,1,8708,114,59,3,55349,56619,4,4,69,101,115,116,13523,13527,13563,13568,59,3,8807,824,4,3,59,113,115,13535,13537,13559,1,8817,4,3,59,113,115,13545,13547,13551,1,8817,59,3,8807,824,108,97,110,116,59,3,10878,824,59,3,10878,824,105,109,59,1,8821,4,2,59,114,13574,13576,1,8815,59,1,8815,4,3,65,97,112,13587,13592,13597,114,114,59,1,8654,114,114,59,1,8622,97,114,59,1,10994,4,3,59,115,118,13610,13612,13623,1,8715,4,2,59,100,13618,13620,1,8956,59,1,8954,59,1,8715,99,121,59,1,1114,4,7,65,69,97,100,101,115,116,13647,13652,13656,13661,13665,13737,13742,114,114,59,1,8653,59,3,8806,824,114,114,59,1,8602,114,59,1,8229,4,4,59,102,113,115,13675,13677,13703,13725,1,8816,116,4,2,97,114,13684,13691,114,114,111,119,59,1,8602,105,103,104,116,97,114,114,111,119,59,1,8622,4,3,59,113,115,13711,13713,13717,1,8816,59,3,8806,824,108,97,110,116,59,3,10877,824,4,2,59,115,13731,13734,3,10877,824,59,1,8814,105,109,59,1,8820,4,2,59,114,13748,13750,1,8814,105,4,2,59,101,13757,13759,1,8938,59,1,8940,105,100,59,1,8740,4,2,112,116,13773,13778,102,59,3,55349,56671,5,172,3,59,105,110,13787,13789,13829,1,172,110,4,4,59,69,100,118,13800,13802,13806,13812,1,8713,59,3,8953,824,111,116,59,3,8949,824,4,3,97,98,99,13820,13823,13826,59,1,8713,59,1,8951,59,1,8950,105,4,2,59,118,13836,13838,1,8716,4,3,97,98,99,13846,13849,13852,59,1,8716,59,1,8958,59,1,8957,4,3,97,111,114,13863,13892,13899,114,4,4,59,97,115,116,13874,13876,13883,13888,1,8742,108,108,101,108,59,1,8742,108,59,3,11005,8421,59,3,8706,824,108,105,110,116,59,1,10772,4,3,59,99,101,13907,13909,13914,1,8832,117,101,59,1,8928,4,2,59,99,13920,13923,3,10927,824,4,2,59,101,13929,13931,1,8832,113,59,3,10927,824,4,4,65,97,105,116,13946,13951,13971,13982,114,114,59,1,8655,114,114,4,3,59,99,119,13961,13963,13967,1,8603,59,3,10547,824,59,3,8605,824,103,104,116,97,114,114,111,119,59,1,8603,114,105,4,2,59,101,13990,13992,1,8939,59,1,8941,4,7,99,104,105,109,112,113,117,14011,14036,14060,14080,14085,14090,14106,4,4,59,99,101,114,14021,14023,14028,14032,1,8833,117,101,59,1,8929,59,3,10928,824,59,3,55349,56515,111,114,116,4,2,109,112,14045,14050,105,100,59,1,8740,97,114,97,108,108,101,108,59,1,8742,109,4,2,59,101,14067,14069,1,8769,4,2,59,113,14075,14077,1,8772,59,1,8772,105,100,59,1,8740,97,114,59,1,8742,115,117,4,2,98,112,14098,14102,101,59,1,8930,101,59,1,8931,4,3,98,99,112,14114,14157,14171,4,4,59,69,101,115,14124,14126,14130,14133,1,8836,59,3,10949,824,59,1,8840,101,116,4,2,59,101,14141,14144,3,8834,8402,113,4,2,59,113,14151,14153,1,8840,59,3,10949,824,99,4,2,59,101,14164,14166,1,8833,113,59,3,10928,824,4,4,59,69,101,115,14181,14183,14187,14190,1,8837,59,3,10950,824,59,1,8841,101,116,4,2,59,101,14198,14201,3,8835,8402,113,4,2,59,113,14208,14210,1,8841,59,3,10950,824,4,4,103,105,108,114,14224,14228,14238,14242,108,59,1,8825,108,100,101,5,241,1,59,14236,1,241,103,59,1,8824,105,97,110,103,108,101,4,2,108,114,14254,14269,101,102,116,4,2,59,101,14263,14265,1,8938,113,59,1,8940,105,103,104,116,4,2,59,101,14279,14281,1,8939,113,59,1,8941,4,2,59,109,14291,14293,1,957,4,3,59,101,115,14301,14303,14308,1,35,114,111,59,1,8470,112,59,1,8199,4,9,68,72,97,100,103,105,108,114,115,14332,14338,14344,14349,14355,14369,14376,14408,14426,97,115,104,59,1,8877,97,114,114,59,1,10500,112,59,3,8781,8402,97,115,104,59,1,8876,4,2,101,116,14361,14365,59,3,8805,8402,59,3,62,8402,110,102,105,110,59,1,10718,4,3,65,101,116,14384,14389,14393,114,114,59,1,10498,59,3,8804,8402,4,2,59,114,14399,14402,3,60,8402,105,101,59,3,8884,8402,4,2,65,116,14414,14419,114,114,59,1,10499,114,105,101,59,3,8885,8402,105,109,59,3,8764,8402,4,3,65,97,110,14440,14445,14468,114,114,59,1,8662,114,4,2,104,114,14452,14456,107,59,1,10531,4,2,59,111,14462,14464,1,8598,119,59,1,8598,101,97,114,59,1,10535,4,18,83,97,99,100,101,102,103,104,105,108,109,111,112,114,115,116,117,118,14512,14515,14535,14560,14597,14603,14618,14643,14657,14662,14701,14741,14747,14769,14851,14877,14907,14916,59,1,9416,4,2,99,115,14521,14531,117,116,101,5,243,1,59,14529,1,243,116,59,1,8859,4,2,105,121,14541,14557,114,4,2,59,99,14548,14550,1,8858,5,244,1,59,14555,1,244,59,1,1086,4,5,97,98,105,111,115,14572,14577,14583,14587,14591,115,104,59,1,8861,108,97,99,59,1,337,118,59,1,10808,116,59,1,8857,111,108,100,59,1,10684,108,105,103,59,1,339,4,2,99,114,14609,14614,105,114,59,1,10687,59,3,55349,56620,4,3,111,114,116,14626,14630,14640,110,59,1,731,97,118,101,5,242,1,59,14638,1,242,59,1,10689,4,2,98,109,14649,14654,97,114,59,1,10677,59,1,937,110,116,59,1,8750,4,4,97,99,105,116,14672,14677,14693,14698,114,114,59,1,8634,4,2,105,114,14683,14687,114,59,1,10686,111,115,115,59,1,10683,110,101,59,1,8254,59,1,10688,4,3,97,101,105,14709,14714,14719,99,114,59,1,333,103,97,59,1,969,4,3,99,100,110,14727,14733,14736,114,111,110,59,1,959,59,1,10678,117,115,59,1,8854,112,102,59,3,55349,56672,4,3,97,101,108,14755,14759,14764,114,59,1,10679,114,112,59,1,10681,117,115,59,1,8853,4,7,59,97,100,105,111,115,118,14785,14787,14792,14831,14837,14841,14848,1,8744,114,114,59,1,8635,4,4,59,101,102,109,14802,14804,14817,14824,1,10845,114,4,2,59,111,14811,14813,1,8500,102,59,1,8500,5,170,1,59,14822,1,170,5,186,1,59,14829,1,186,103,111,102,59,1,8886,114,59,1,10838,108,111,112,101,59,1,10839,59,1,10843,4,3,99,108,111,14859,14863,14873,114,59,1,8500,97,115,104,5,248,1,59,14871,1,248,108,59,1,8856,105,4,2,108,109,14884,14893,100,101,5,245,1,59,14891,1,245,101,115,4,2,59,97,14901,14903,1,8855,115,59,1,10806,109,108,5,246,1,59,14914,1,246,98,97,114,59,1,9021,4,12,97,99,101,102,104,105,108,109,111,114,115,117,14948,14992,14996,15033,15038,15068,15090,15189,15192,15222,15427,15441,114,4,4,59,97,115,116,14959,14961,14976,14989,1,8741,5,182,2,59,108,14968,14970,1,182,108,101,108,59,1,8741,4,2,105,108,14982,14986,109,59,1,10995,59,1,11005,59,1,8706,121,59,1,1087,114,4,5,99,105,109,112,116,15009,15014,15019,15024,15027,110,116,59,1,37,111,100,59,1,46,105,108,59,1,8240,59,1,8869,101,110,107,59,1,8241,114,59,3,55349,56621,4,3,105,109,111,15046,15057,15063,4,2,59,118,15052,15054,1,966,59,1,981,109,97,116,59,1,8499,110,101,59,1,9742,4,3,59,116,118,15076,15078,15087,1,960,99,104,102,111,114,107,59,1,8916,59,1,982,4,2,97,117,15096,15119,110,4,2,99,107,15103,15115,107,4,2,59,104,15110,15112,1,8463,59,1,8462,118,59,1,8463,115,4,9,59,97,98,99,100,101,109,115,116,15140,15142,15148,15151,15156,15168,15171,15179,15184,1,43,99,105,114,59,1,10787,59,1,8862,105,114,59,1,10786,4,2,111,117,15162,15165,59,1,8724,59,1,10789,59,1,10866,110,5,177,1,59,15177,1,177,105,109,59,1,10790,119,111,59,1,10791,59,1,177,4,3,105,112,117,15200,15208,15213,110,116,105,110,116,59,1,10773,102,59,3,55349,56673,110,100,5,163,1,59,15220,1,163,4,10,59,69,97,99,101,105,110,111,115,117,15244,15246,15249,15253,15258,15334,15347,15367,15416,15421,1,8826,59,1,10931,112,59,1,10935,117,101,59,1,8828,4,2,59,99,15264,15266,1,10927,4,6,59,97,99,101,110,115,15280,15282,15290,15299,15303,15329,1,8826,112,112,114,111,120,59,1,10935,117,114,108,121,101,113,59,1,8828,113,59,1,10927,4,3,97,101,115,15311,15319,15324,112,112,114,111,120,59,1,10937,113,113,59,1,10933,105,109,59,1,8936,105,109,59,1,8830,109,101,4,2,59,115,15342,15344,1,8242,59,1,8473,4,3,69,97,115,15355,15358,15362,59,1,10933,112,59,1,10937,105,109,59,1,8936,4,3,100,102,112,15375,15378,15404,59,1,8719,4,3,97,108,115,15386,15392,15398,108,97,114,59,1,9006,105,110,101,59,1,8978,117,114,102,59,1,8979,4,2,59,116,15410,15412,1,8733,111,59,1,8733,105,109,59,1,8830,114,101,108,59,1,8880,4,2,99,105,15433,15438,114,59,3,55349,56517,59,1,968,110,99,115,112,59,1,8200,4,6,102,105,111,112,115,117,15462,15467,15472,15478,15485,15491,114,59,3,55349,56622,110,116,59,1,10764,112,102,59,3,55349,56674,114,105,109,101,59,1,8279,99,114,59,3,55349,56518,4,3,97,101,111,15499,15520,15534,116,4,2,101,105,15506,15515,114,110,105,111,110,115,59,1,8461,110,116,59,1,10774,115,116,4,2,59,101,15528,15530,1,63,113,59,1,8799,116,5,34,1,59,15540,1,34,4,21,65,66,72,97,98,99,100,101,102,104,105,108,109,110,111,112,114,115,116,117,120,15586,15609,15615,15620,15796,15855,15893,15931,15977,16001,16039,16183,16204,16222,16228,16285,16312,16318,16363,16408,16416,4,3,97,114,116,15594,15599,15603,114,114,59,1,8667,114,59,1,8658,97,105,108,59,1,10524,97,114,114,59,1,10511,97,114,59,1,10596,4,7,99,100,101,110,113,114,116,15636,15651,15656,15664,15687,15696,15770,4,2,101,117,15642,15646,59,3,8765,817,116,101,59,1,341,105,99,59,1,8730,109,112,116,121,118,59,1,10675,103,4,4,59,100,101,108,15675,15677,15680,15683,1,10217,59,1,10642,59,1,10661,101,59,1,10217,117,111,5,187,1,59,15694,1,187,114,4,11,59,97,98,99,102,104,108,112,115,116,119,15721,15723,15727,15739,15742,15746,15750,15754,15758,15763,15767,1,8594,112,59,1,10613,4,2,59,102,15733,15735,1,8677,115,59,1,10528,59,1,10547,115,59,1,10526,107,59,1,8618,112,59,1,8620,108,59,1,10565,105,109,59,1,10612,108,59,1,8611,59,1,8605,4,2,97,105,15776,15781,105,108,59,1,10522,111,4,2,59,110,15788,15790,1,8758,97,108,115,59,1,8474,4,3,97,98,114,15804,15809,15814,114,114,59,1,10509,114,107,59,1,10099,4,2,97,107,15820,15833,99,4,2,101,107,15827,15830,59,1,125,59,1,93,4,2,101,115,15839,15842,59,1,10636,108,4,2,100,117,15849,15852,59,1,10638,59,1,10640,4,4,97,101,117,121,15865,15871,15886,15890,114,111,110,59,1,345,4,2,100,105,15877,15882,105,108,59,1,343,108,59,1,8969,98,59,1,125,59,1,1088,4,4,99,108,113,115,15903,15907,15914,15927,97,59,1,10551,100,104,97,114,59,1,10601,117,111,4,2,59,114,15922,15924,1,8221,59,1,8221,104,59,1,8627,4,3,97,99,103,15939,15966,15970,108,4,4,59,105,112,115,15950,15952,15957,15963,1,8476,110,101,59,1,8475,97,114,116,59,1,8476,59,1,8477,116,59,1,9645,5,174,1,59,15975,1,174,4,3,105,108,114,15985,15991,15997,115,104,116,59,1,10621,111,111,114,59,1,8971,59,3,55349,56623,4,2,97,111,16007,16028,114,4,2,100,117,16014,16017,59,1,8641,4,2,59,108,16023,16025,1,8640,59,1,10604,4,2,59,118,16034,16036,1,961,59,1,1009,4,3,103,110,115,16047,16167,16171,104,116,4,6,97,104,108,114,115,116,16063,16081,16103,16130,16143,16155,114,114,111,119,4,2,59,116,16073,16075,1,8594,97,105,108,59,1,8611,97,114,112,111,111,110,4,2,100,117,16093,16099,111,119,110,59,1,8641,112,59,1,8640,101,102,116,4,2,97,104,16112,16120,114,114,111,119,115,59,1,8644,97,114,112,111,111,110,115,59,1,8652,105,103,104,116,97,114,114,111,119,115,59,1,8649,113,117,105,103,97,114,114,111,119,59,1,8605,104,114,101,101,116,105,109,101,115,59,1,8908,103,59,1,730,105,110,103,100,111,116,115,101,113,59,1,8787,4,3,97,104,109,16191,16196,16201,114,114,59,1,8644,97,114,59,1,8652,59,1,8207,111,117,115,116,4,2,59,97,16214,16216,1,9137,99,104,101,59,1,9137,109,105,100,59,1,10990,4,4,97,98,112,116,16238,16252,16257,16278,4,2,110,114,16244,16248,103,59,1,10221,114,59,1,8702,114,107,59,1,10215,4,3,97,102,108,16265,16269,16273,114,59,1,10630,59,3,55349,56675,117,115,59,1,10798,105,109,101,115,59,1,10805,4,2,97,112,16291,16304,114,4,2,59,103,16298,16300,1,41,116,59,1,10644,111,108,105,110,116,59,1,10770,97,114,114,59,1,8649,4,4,97,99,104,113,16328,16334,16339,16342,113,117,111,59,1,8250,114,59,3,55349,56519,59,1,8625,4,2,98,117,16348,16351,59,1,93,111,4,2,59,114,16358,16360,1,8217,59,1,8217,4,3,104,105,114,16371,16377,16383,114,101,101,59,1,8908,109,101,115,59,1,8906,105,4,4,59,101,102,108,16394,16396,16399,16402,1,9657,59,1,8885,59,1,9656,116,114,105,59,1,10702,108,117,104,97,114,59,1,10600,59,1,8478,4,19,97,98,99,100,101,102,104,105,108,109,111,112,113,114,115,116,117,119,122,16459,16466,16472,16572,16590,16672,16687,16746,16844,16850,16924,16963,16988,17115,17121,17154,17206,17614,17656,99,117,116,101,59,1,347,113,117,111,59,1,8218,4,10,59,69,97,99,101,105,110,112,115,121,16494,16496,16499,16513,16518,16531,16536,16556,16564,16569,1,8827,59,1,10932,4,2,112,114,16505,16508,59,1,10936,111,110,59,1,353,117,101,59,1,8829,4,2,59,100,16524,16526,1,10928,105,108,59,1,351,114,99,59,1,349,4,3,69,97,115,16544,16547,16551,59,1,10934,112,59,1,10938,105,109,59,1,8937,111,108,105,110,116,59,1,10771,105,109,59,1,8831,59,1,1089,111,116,4,3,59,98,101,16582,16584,16587,1,8901,59,1,8865,59,1,10854,4,7,65,97,99,109,115,116,120,16606,16611,16634,16642,16646,16652,16668,114,114,59,1,8664,114,4,2,104,114,16618,16622,107,59,1,10533,4,2,59,111,16628,16630,1,8600,119,59,1,8600,116,5,167,1,59,16640,1,167,105,59,1,59,119,97,114,59,1,10537,109,4,2,105,110,16659,16665,110,117,115,59,1,8726,59,1,8726,116,59,1,10038,114,4,2,59,111,16679,16682,3,55349,56624,119,110,59,1,8994,4,4,97,99,111,121,16697,16702,16716,16739,114,112,59,1,9839,4,2,104,121,16708,16713,99,121,59,1,1097,59,1,1096,114,116,4,2,109,112,16724,16729,105,100,59,1,8739,97,114,97,108,108,101,108,59,1,8741,5,173,1,59,16744,1,173,4,2,103,109,16752,16770,109,97,4,3,59,102,118,16762,16764,16767,1,963,59,1,962,59,1,962,4,8,59,100,101,103,108,110,112,114,16788,16790,16795,16806,16817,16828,16832,16838,1,8764,111,116,59,1,10858,4,2,59,113,16801,16803,1,8771,59,1,8771,4,2,59,69,16812,16814,1,10910,59,1,10912,4,2,59,69,16823,16825,1,10909,59,1,10911,101,59,1,8774,108,117,115,59,1,10788,97,114,114,59,1,10610,97,114,114,59,1,8592,4,4,97,101,105,116,16860,16883,16891,16904,4,2,108,115,16866,16878,108,115,101,116,109,105,110,117,115,59,1,8726,104,112,59,1,10803,112,97,114,115,108,59,1,10724,4,2,100,108,16897,16900,59,1,8739,101,59,1,8995,4,2,59,101,16910,16912,1,10922,4,2,59,115,16918,16920,1,10924,59,3,10924,65024,4,3,102,108,112,16932,16938,16958,116,99,121,59,1,1100,4,2,59,98,16944,16946,1,47,4,2,59,97,16952,16954,1,10692,114,59,1,9023,102,59,3,55349,56676,97,4,2,100,114,16970,16985,101,115,4,2,59,117,16978,16980,1,9824,105,116,59,1,9824,59,1,8741,4,3,99,115,117,16996,17028,17089,4,2,97,117,17002,17015,112,4,2,59,115,17009,17011,1,8851,59,3,8851,65024,112,4,2,59,115,17022,17024,1,8852,59,3,8852,65024,117,4,2,98,112,17035,17062,4,3,59,101,115,17043,17045,17048,1,8847,59,1,8849,101,116,4,2,59,101,17056,17058,1,8847,113,59,1,8849,4,3,59,101,115,17070,17072,17075,1,8848,59,1,8850,101,116,4,2,59,101,17083,17085,1,8848,113,59,1,8850,4,3,59,97,102,17097,17099,17112,1,9633,114,4,2,101,102,17106,17109,59,1,9633,59,1,9642,59,1,9642,97,114,114,59,1,8594,4,4,99,101,109,116,17131,17136,17142,17148,114,59,3,55349,56520,116,109,110,59,1,8726,105,108,101,59,1,8995,97,114,102,59,1,8902,4,2,97,114,17160,17172,114,4,2,59,102,17167,17169,1,9734,59,1,9733,4,2,97,110,17178,17202,105,103,104,116,4,2,101,112,17188,17197,112,115,105,108,111,110,59,1,1013,104,105,59,1,981,115,59,1,175,4,5,98,99,109,110,112,17218,17351,17420,17423,17427,4,9,59,69,100,101,109,110,112,114,115,17238,17240,17243,17248,17261,17267,17279,17285,17291,1,8834,59,1,10949,111,116,59,1,10941,4,2,59,100,17254,17256,1,8838,111,116,59,1,10947,117,108,116,59,1,10945,4,2,69,101,17273,17276,59,1,10955,59,1,8842,108,117,115,59,1,10943,97,114,114,59,1,10617,4,3,101,105,117,17299,17335,17339,116,4,3,59,101,110,17308,17310,17322,1,8834,113,4,2,59,113,17317,17319,1,8838,59,1,10949,101,113,4,2,59,113,17330,17332,1,8842,59,1,10955,109,59,1,10951,4,2,98,112,17345,17348,59,1,10965,59,1,10963,99,4,6,59,97,99,101,110,115,17366,17368,17376,17385,17389,17415,1,8827,112,112,114,111,120,59,1,10936,117,114,108,121,101,113,59,1,8829,113,59,1,10928,4,3,97,101,115,17397,17405,17410,112,112,114,111,120,59,1,10938,113,113,59,1,10934,105,109,59,1,8937,105,109,59,1,8831,59,1,8721,103,59,1,9834,4,13,49,50,51,59,69,100,101,104,108,109,110,112,115,17455,17462,17469,17476,17478,17481,17496,17509,17524,17530,17536,17548,17554,5,185,1,59,17460,1,185,5,178,1,59,17467,1,178,5,179,1,59,17474,1,179,1,8835,59,1,10950,4,2,111,115,17487,17491,116,59,1,10942,117,98,59,1,10968,4,2,59,100,17502,17504,1,8839,111,116,59,1,10948,115,4,2,111,117,17516,17520,108,59,1,10185,98,59,1,10967,97,114,114,59,1,10619,117,108,116,59,1,10946,4,2,69,101,17542,17545,59,1,10956,59,1,8843,108,117,115,59,1,10944,4,3,101,105,117,17562,17598,17602,116,4,3,59,101,110,17571,17573,17585,1,8835,113,4,2,59,113,17580,17582,1,8839,59,1,10950,101,113,4,2,59,113,17593,17595,1,8843,59,1,10956,109,59,1,10952,4,2,98,112,17608,17611,59,1,10964,59,1,10966,4,3,65,97,110,17622,17627,17650,114,114,59,1,8665,114,4,2,104,114,17634,17638,107,59,1,10534,4,2,59,111,17644,17646,1,8601,119,59,1,8601,119,97,114,59,1,10538,108,105,103,5,223,1,59,17664,1,223,4,13,97,98,99,100,101,102,104,105,111,112,114,115,119,17694,17709,17714,17737,17742,17749,17754,17860,17905,17957,17964,18090,18122,4,2,114,117,17700,17706,103,101,116,59,1,8982,59,1,964,114,107,59,1,9140,4,3,97,101,121,17722,17728,17734,114,111,110,59,1,357,100,105,108,59,1,355,59,1,1090,111,116,59,1,8411,108,114,101,99,59,1,8981,114,59,3,55349,56625,4,4,101,105,107,111,17764,17805,17836,17851,4,2,114,116,17770,17786,101,4,2,52,102,17777,17780,59,1,8756,111,114,101,59,1,8756,97,4,3,59,115,118,17795,17797,17802,1,952,121,109,59,1,977,59,1,977,4,2,99,110,17811,17831,107,4,2,97,115,17818,17826,112,112,114,111,120,59,1,8776,105,109,59,1,8764,115,112,59,1,8201,4,2,97,115,17842,17846,112,59,1,8776,105,109,59,1,8764,114,110,5,254,1,59,17858,1,254,4,3,108,109,110,17868,17873,17901,100,101,59,1,732,101,115,5,215,3,59,98,100,17884,17886,17898,1,215,4,2,59,97,17892,17894,1,8864,114,59,1,10801,59,1,10800,116,59,1,8749,4,3,101,112,115,17913,17917,17953,97,59,1,10536,4,4,59,98,99,102,17927,17929,17934,17939,1,8868,111,116,59,1,9014,105,114,59,1,10993,4,2,59,111,17945,17948,3,55349,56677,114,107,59,1,10970,97,59,1,10537,114,105,109,101,59,1,8244,4,3,97,105,112,17972,17977,18082,100,101,59,1,8482,4,7,97,100,101,109,112,115,116,17993,18051,18056,18059,18066,18072,18076,110,103,108,101,4,5,59,100,108,113,114,18009,18011,18017,18032,18035,1,9653,111,119,110,59,1,9663,101,102,116,4,2,59,101,18026,18028,1,9667,113,59,1,8884,59,1,8796,105,103,104,116,4,2,59,101,18045,18047,1,9657,113,59,1,8885,111,116,59,1,9708,59,1,8796,105,110,117,115,59,1,10810,108,117,115,59,1,10809,98,59,1,10701,105,109,101,59,1,10811,101,122,105,117,109,59,1,9186,4,3,99,104,116,18098,18111,18116,4,2,114,121,18104,18108,59,3,55349,56521,59,1,1094,99,121,59,1,1115,114,111,107,59,1,359,4,2,105,111,18128,18133,120,116,59,1,8812,104,101,97,100,4,2,108,114,18143,18154,101,102,116,97,114,114,111,119,59,1,8606,105,103,104,116,97,114,114,111,119,59,1,8608,4,18,65,72,97,98,99,100,102,103,104,108,109,111,112,114,115,116,117,119,18204,18209,18214,18234,18250,18268,18292,18308,18319,18343,18379,18397,18413,18504,18547,18553,18584,18603,114,114,59,1,8657,97,114,59,1,10595,4,2,99,114,18220,18230,117,116,101,5,250,1,59,18228,1,250,114,59,1,8593,114,4,2,99,101,18241,18245,121,59,1,1118,118,101,59,1,365,4,2,105,121,18256,18265,114,99,5,251,1,59,18263,1,251,59,1,1091,4,3,97,98,104,18276,18281,18287,114,114,59,1,8645,108,97,99,59,1,369,97,114,59,1,10606,4,2,105,114,18298,18304,115,104,116,59,1,10622,59,3,55349,56626,114,97,118,101,5,249,1,59,18317,1,249,4,2,97,98,18325,18338,114,4,2,108,114,18332,18335,59,1,8639,59,1,8638,108,107,59,1,9600,4,2,99,116,18349,18374,4,2,111,114,18355,18369,114,110,4,2,59,101,18363,18365,1,8988,114,59,1,8988,111,112,59,1,8975,114,105,59,1,9720,4,2,97,108,18385,18390,99,114,59,1,363,5,168,1,59,18395,1,168,4,2,103,112,18403,18408,111,110,59,1,371,102,59,3,55349,56678,4,6,97,100,104,108,115,117,18427,18434,18445,18470,18475,18494,114,114,111,119,59,1,8593,111,119,110,97,114,114,111,119,59,1,8597,97,114,112,111,111,110,4,2,108,114,18457,18463,101,102,116,59,1,8639,105,103,104,116,59,1,8638,117,115,59,1,8846,105,4,3,59,104,108,18484,18486,18489,1,965,59,1,978,111,110,59,1,965,112,97,114,114,111,119,115,59,1,8648,4,3,99,105,116,18512,18537,18542,4,2,111,114,18518,18532,114,110,4,2,59,101,18526,18528,1,8989,114,59,1,8989,111,112,59,1,8974,110,103,59,1,367,114,105,59,1,9721,99,114,59,3,55349,56522,4,3,100,105,114,18561,18566,18572,111,116,59,1,8944,108,100,101,59,1,361,105,4,2,59,102,18579,18581,1,9653,59,1,9652,4,2,97,109,18590,18595,114,114,59,1,8648,108,5,252,1,59,18601,1,252,97,110,103,108,101,59,1,10663,4,15,65,66,68,97,99,100,101,102,108,110,111,112,114,115,122,18643,18648,18661,18667,18847,18851,18857,18904,18909,18915,18931,18937,18943,18949,18996,114,114,59,1,8661,97,114,4,2,59,118,18656,18658,1,10984,59,1,10985,97,115,104,59,1,8872,4,2,110,114,18673,18679,103,114,116,59,1,10652,4,7,101,107,110,112,114,115,116,18695,18704,18711,18720,18742,18754,18810,112,115,105,108,111,110,59,1,1013,97,112,112,97,59,1,1008,111,116,104,105,110,103,59,1,8709,4,3,104,105,114,18728,18732,18735,105,59,1,981,59,1,982,111,112,116,111,59,1,8733,4,2,59,104,18748,18750,1,8597,111,59,1,1009,4,2,105,117,18760,18766,103,109,97,59,1,962,4,2,98,112,18772,18791,115,101,116,110,101,113,4,2,59,113,18784,18787,3,8842,65024,59,3,10955,65024,115,101,116,110,101,113,4,2,59,113,18803,18806,3,8843,65024,59,3,10956,65024,4,2,104,114,18816,18822,101,116,97,59,1,977,105,97,110,103,108,101,4,2,108,114,18834,18840,101,102,116,59,1,8882,105,103,104,116,59,1,8883,121,59,1,1074,97,115,104,59,1,8866,4,3,101,108,114,18865,18884,18890,4,3,59,98,101,18873,18875,18880,1,8744,97,114,59,1,8891,113,59,1,8794,108,105,112,59,1,8942,4,2,98,116,18896,18901,97,114,59,1,124,59,1,124,114,59,3,55349,56627,116,114,105,59,1,8882,115,117,4,2,98,112,18923,18927,59,3,8834,8402,59,3,8835,8402,112,102,59,3,55349,56679,114,111,112,59,1,8733,116,114,105,59,1,8883,4,2,99,117,18955,18960,114,59,3,55349,56523,4,2,98,112,18966,18981,110,4,2,69,101,18973,18977,59,3,10955,65024,59,3,8842,65024,110,4,2,69,101,18988,18992,59,3,10956,65024,59,3,8843,65024,105,103,122,97,103,59,1,10650,4,7,99,101,102,111,112,114,115,19020,19026,19061,19066,19072,19075,19089,105,114,99,59,1,373,4,2,100,105,19032,19055,4,2,98,103,19038,19043,97,114,59,1,10847,101,4,2,59,113,19050,19052,1,8743,59,1,8793,101,114,112,59,1,8472,114,59,3,55349,56628,112,102,59,3,55349,56680,59,1,8472,4,2,59,101,19081,19083,1,8768,97,116,104,59,1,8768,99,114,59,3,55349,56524,4,14,99,100,102,104,105,108,109,110,111,114,115,117,118,119,19125,19146,19152,19157,19173,19176,19192,19197,19202,19236,19252,19269,19286,19291,4,3,97,105,117,19133,19137,19142,112,59,1,8898,114,99,59,1,9711,112,59,1,8899,116,114,105,59,1,9661,114,59,3,55349,56629,4,2,65,97,19163,19168,114,114,59,1,10234,114,114,59,1,10231,59,1,958,4,2,65,97,19182,19187,114,114,59,1,10232,114,114,59,1,10229,97,112,59,1,10236,105,115,59,1,8955,4,3,100,112,116,19210,19215,19230,111,116,59,1,10752,4,2,102,108,19221,19225,59,3,55349,56681,117,115,59,1,10753,105,109,101,59,1,10754,4,2,65,97,19242,19247,114,114,59,1,10233,114,114,59,1,10230,4,2,99,113,19258,19263,114,59,3,55349,56525,99,117,112,59,1,10758,4,2,112,116,19275,19281,108,117,115,59,1,10756,114,105,59,1,9651,101,101,59,1,8897,101,100,103,101,59,1,8896,4,8,97,99,101,102,105,111,115,117,19316,19335,19349,19357,19362,19367,19373,19379,99,4,2,117,121,19323,19332,116,101,5,253,1,59,19330,1,253,59,1,1103,4,2,105,121,19341,19346,114,99,59,1,375,59,1,1099,110,5,165,1,59,19355,1,165,114,59,3,55349,56630,99,121,59,1,1111,112,102,59,3,55349,56682,99,114,59,3,55349,56526,4,2,99,109,19385,19389,121,59,1,1102,108,5,255,1,59,19395,1,255,4,10,97,99,100,101,102,104,105,111,115,119,19419,19426,19441,19446,19462,19467,19472,19480,19486,19492,99,117,116,101,59,1,378,4,2,97,121,19432,19438,114,111,110,59,1,382,59,1,1079,111,116,59,1,380,4,2,101,116,19452,19458,116,114,102,59,1,8488,97,59,1,950,114,59,3,55349,56631,99,121,59,1,1078,103,114,97,114,114,59,1,8669,112,102,59,3,55349,56683,99,114,59,3,55349,56527,4,2,106,110,19498,19501,59,1,8205,106,59,1,8204])},7118:function(e,t,n){"use strict";let r=n(4284),i=n(1734),o=r.CODE_POINTS;e.exports=class{constructor(){this.html=null,this.pos=-1,this.lastGapPos=-1,this.lastCharPos=-1,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=65536}_err(){}_addGap(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos}_processSurrogate(e){if(this.pos!==this.lastCharPos){let t=this.html.charCodeAt(this.pos+1);if(r.isSurrogatePair(t))return this.pos++,this._addGap(),r.getSurrogatePairCodePoint(e,t)}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,o.EOF;return this._err(i.surrogateInInputStream),e}dropParsedChunk(){this.pos>this.bufferWaterline&&(this.lastCharPos-=this.pos,this.html=this.html.substring(this.pos),this.pos=0,this.lastGapPos=-1,this.gapStack=[])}write(e,t){this.html?this.html+=e:this.html=e,this.lastCharPos=this.html.length-1,this.endOfChunkHit=!1,this.lastChunkWritten=t}insertHtmlAtCurrentPos(e){this.html=this.html.substring(0,this.pos+1)+e+this.html.substring(this.pos+1,this.html.length),this.lastCharPos=this.html.length-1,this.endOfChunkHit=!1}advance(){if(this.pos++,this.pos>this.lastCharPos)return this.endOfChunkHit=!this.lastChunkWritten,o.EOF;let e=this.html.charCodeAt(this.pos);if(this.skipNextNewLine&&e===o.LINE_FEED)return this.skipNextNewLine=!1,this._addGap(),this.advance();if(e===o.CARRIAGE_RETURN)return this.skipNextNewLine=!0,o.LINE_FEED;this.skipNextNewLine=!1,r.isSurrogate(e)&&(e=this._processSurrogate(e));let t=e>31&&e<127||e===o.LINE_FEED||e===o.CARRIAGE_RETURN||e>159&&e<64976;return t||this._checkForProblematicCharacters(e),e}_checkForProblematicCharacters(e){r.isControlCodePoint(e)?this._err(i.controlCharacterInInputStream):r.isUndefinedCodePoint(e)&&this._err(i.noncharacterInInputStream)}retreat(){this.pos===this.lastGapPos&&(this.lastGapPos=this.gapStack.pop(),this.pos--),this.pos--}}},7296:function(e,t,n){"use strict";let{DOCUMENT_MODE:r}=n(6152);t.createDocument=function(){return{nodeName:"#document",mode:r.NO_QUIRKS,childNodes:[]}},t.createDocumentFragment=function(){return{nodeName:"#document-fragment",childNodes:[]}},t.createElement=function(e,t,n){return{nodeName:e,tagName:e,attrs:n,namespaceURI:t,childNodes:[],parentNode:null}},t.createCommentNode=function(e){return{nodeName:"#comment",data:e,parentNode:null}};let createTextNode=function(e){return{nodeName:"#text",value:e,parentNode:null}},i=t.appendChild=function(e,t){e.childNodes.push(t),t.parentNode=e},o=t.insertBefore=function(e,t,n){let r=e.childNodes.indexOf(n);e.childNodes.splice(r,0,t),t.parentNode=e};t.setTemplateContent=function(e,t){e.content=t},t.getTemplateContent=function(e){return e.content},t.setDocumentType=function(e,t,n,r){let o=null;for(let t=0;t<e.childNodes.length;t++)if("#documentType"===e.childNodes[t].nodeName){o=e.childNodes[t];break}o?(o.name=t,o.publicId=n,o.systemId=r):i(e,{nodeName:"#documentType",name:t,publicId:n,systemId:r})},t.setDocumentMode=function(e,t){e.mode=t},t.getDocumentMode=function(e){return e.mode},t.detachNode=function(e){if(e.parentNode){let t=e.parentNode.childNodes.indexOf(e);e.parentNode.childNodes.splice(t,1),e.parentNode=null}},t.insertText=function(e,t){if(e.childNodes.length){let n=e.childNodes[e.childNodes.length-1];if("#text"===n.nodeName){n.value+=t;return}}i(e,createTextNode(t))},t.insertTextBefore=function(e,t,n){let r=e.childNodes[e.childNodes.indexOf(n)-1];r&&"#text"===r.nodeName?r.value+=t:o(e,createTextNode(t),n)},t.adoptAttributes=function(e,t){let n=[];for(let t=0;t<e.attrs.length;t++)n.push(e.attrs[t].name);for(let r=0;r<t.length;r++)-1===n.indexOf(t[r].name)&&e.attrs.push(t[r])},t.getFirstChild=function(e){return e.childNodes[0]},t.getChildNodes=function(e){return e.childNodes},t.getParentNode=function(e){return e.parentNode},t.getAttrList=function(e){return e.attrs},t.getTagName=function(e){return e.tagName},t.getNamespaceURI=function(e){return e.namespaceURI},t.getTextNodeContent=function(e){return e.value},t.getCommentNodeContent=function(e){return e.data},t.getDocumentTypeNodeName=function(e){return e.name},t.getDocumentTypeNodePublicId=function(e){return e.publicId},t.getDocumentTypeNodeSystemId=function(e){return e.systemId},t.isTextNode=function(e){return"#text"===e.nodeName},t.isCommentNode=function(e){return"#comment"===e.nodeName},t.isDocumentTypeNode=function(e){return"#documentType"===e.nodeName},t.isElementNode=function(e){return!!e.tagName},t.setNodeSourceCodeLocation=function(e,t){e.sourceCodeLocation=t},t.getNodeSourceCodeLocation=function(e){return e.sourceCodeLocation},t.updateNodeSourceCodeLocation=function(e,t){e.sourceCodeLocation=Object.assign(e.sourceCodeLocation,t)}},8904:function(e){"use strict";e.exports=function(e,t){return[e,t=t||Object.create(null)].reduce((e,t)=>(Object.keys(t).forEach(n=>{e[n]=t[n]}),e),Object.create(null))}},1704:function(e){"use strict";let Mixin=class Mixin{constructor(e){let t={},n=this._getOverriddenMethods(this,t);for(let r of Object.keys(n))"function"==typeof n[r]&&(t[r]=e[r],e[r]=n[r])}_getOverriddenMethods(){throw Error("Not implemented")}};Mixin.install=function(e,t,n){e.__mixins||(e.__mixins=[]);for(let n=0;n<e.__mixins.length;n++)if(e.__mixins[n].constructor===t)return e.__mixins[n];let r=new t(e,n);return e.__mixins.push(r),r},e.exports=Mixin},7848:function(e,t,n){var r=n(8139);e.exports=function(e,t){var n,i,o,a=null;if(!e||"string"!=typeof e)return a;for(var s=r(e),l="function"==typeof t,c=0,u=s.length;c<u;c++)i=(n=s[c]).property,o=n.value,l?t(i,o,n):o&&(a||(a={}),a[i]=o);return a}},5668:function(e,t,n){"use strict";function parse(e){for(var t,n,r=[],i=String(e||""),o=i.indexOf(","),a=0;!t;)-1===o&&(o=i.length,t=!0),((n=i.slice(a,o).trim())||!t)&&r.push(n),a=o+1,o=i.indexOf(",",a);return r}function stringify(e,t){var n=t||{};return""===e[e.length-1]&&(e=e.concat("")),e.join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}n.d(t,{P:function(){return stringify},Q:function(){return parse}})},4345:function(e,t,n){"use strict";function ok(){}function unreachable(){}n.d(t,{ok:function(){return ok},t1:function(){return unreachable}})},7962:function(e,t,n){"use strict";n.d(t,{B:function(){return toString}});let r={};function toString(e,t){let n=t||r,i="boolean"!=typeof n.includeImageAlt||n.includeImageAlt,o="boolean"!=typeof n.includeHtml||n.includeHtml;return one(e,i,o)}function one(e,t,n){if(e&&"object"==typeof e){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return all(e.children,t,n)}return Array.isArray(e)?all(e,t,n):""}function all(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=one(e[i],t,n);return r.join("")}},3402:function(e,t,n){"use strict";n.d(t,{w:function(){return o}});var r=n(2761),i=n(5459);let o={tokenize:function(e,t,n){return function(t){return(0,i.xz)(t)?(0,r.f)(e,after,"linePrefix")(t):after(t)};function after(e){return null===e||(0,i.Ch)(e)?t(e):n(e)}},partial:!0}},2761:function(e,t,n){"use strict";n.d(t,{f:function(){return factorySpace}});var r=n(5459);function factorySpace(e,t,n,i){let o=i?i-1:Number.POSITIVE_INFINITY,a=0;return function(i){return(0,r.xz)(i)?(e.enter(n),function prefix(i){return(0,r.xz)(i)&&a++<o?(e.consume(i),prefix):(e.exit(n),t(i))}(i)):t(i)}}},5459:function(e,t,n){"use strict";n.d(t,{AF:function(){return s},Av:function(){return asciiControl},B8:function(){return u},Ch:function(){return markdownLineEnding},H$:function(){return i},Xh:function(){return c},jv:function(){return r},n9:function(){return o},pY:function(){return a},sR:function(){return l},xz:function(){return markdownSpace},z3:function(){return markdownLineEndingOrSpace}});let r=regexCheck(/[A-Za-z]/),i=regexCheck(/[\dA-Za-z]/),o=regexCheck(/[#-'*+\--9=?A-Z^-~]/);function asciiControl(e){return null!==e&&(e<32||127===e)}let a=regexCheck(/\d/),s=regexCheck(/[\dA-Fa-f]/),l=regexCheck(/[!-/:-@[-`{-~]/);function markdownLineEnding(e){return null!==e&&e<-2}function markdownLineEndingOrSpace(e){return null!==e&&(e<0||32===e)}function markdownSpace(e){return -2===e||-1===e||32===e}let c=regexCheck(/\p{P}|\p{S}/u),u=regexCheck(/\s/);function regexCheck(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},2888:function(e,t,n){"use strict";function splice(e,t,n,r){let i;let o=e.length,a=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);a<r.length;)(i=r.slice(a,a+1e4)).unshift(t,0),e.splice(...i),a+=1e4,t+=1e4}function push(e,t){return e.length>0?(splice(e,e.length,0,t),e):t}n.d(t,{V:function(){return push},d:function(){return splice}})},2987:function(e,t,n){"use strict";n.d(t,{r:function(){return classifyCharacter}});var r=n(5459);function classifyCharacter(e){return null===e||(0,r.z3)(e)||(0,r.B8)(e)?1:(0,r.Xh)(e)?2:void 0}},4663:function(e,t,n){"use strict";n.d(t,{W:function(){return combineExtensions}});var r=n(2888);let i={}.hasOwnProperty;function combineExtensions(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let o;let a=i.call(e,n)?e[n]:void 0,s=a||(e[n]={}),l=t[n];if(l)for(o in l){i.call(s,o)||(s[o]=[]);let e=l[o];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.d)(e,0,0,i)}(s[o],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},1098:function(e,t,n){"use strict";function normalizeIdentifier(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{d:function(){return normalizeIdentifier}})},3233:function(e,t,n){"use strict";function resolveAll(e,t,n){let r=[],i=-1;for(;++i<e.length;){let o=e[i].resolveAll;o&&!r.includes(o)&&(t=o(t,n),r.push(o))}return t}n.d(t,{C:function(){return resolveAll}})},1634:function(e,t,n){"use strict";n.d(t,{dy:function(){return f},YP:function(){return d}});let Schema=class Schema{constructor(e,t,n){this.property=e,this.normal=t,n&&(this.space=n)}};function merge(e,t){let n={},r={},i=-1;for(;++i<e.length;)Object.assign(n,e[i].property),Object.assign(r,e[i].normal);return new Schema(n,r,t)}Schema.prototype.property={},Schema.prototype.normal={},Schema.prototype.space=null;var r=n(3859),i=n(5729);let o={}.hasOwnProperty;function create(e){let t;let n={},a={};for(t in e.properties)if(o.call(e.properties,t)){let o=e.properties[t],s=new i.I(t,e.transform(e.attributes||{},t),o,e.space);e.mustUseProperty&&e.mustUseProperty.includes(t)&&(s.mustUseProperty=!0),n[t]=s,a[(0,r.F)(t)]=t,a[(0,r.F)(s.attribute)]=t}return new Schema(n,a,e.space)}let a=create({space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase(),properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),s=create({space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase(),properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function caseSensitiveTransform(e,t){return t in e?e[t]:t}function caseInsensitiveTransform(e,t){return caseSensitiveTransform(e,t.toLowerCase())}let l=create({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:caseInsensitiveTransform,properties:{xmlns:null,xmlnsXLink:null}});var c=n(7312);let u=create({transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase(),properties:{ariaActiveDescendant:null,ariaAtomic:c.booleanish,ariaAutoComplete:null,ariaBusy:c.booleanish,ariaChecked:c.booleanish,ariaColCount:c.number,ariaColIndex:c.number,ariaColSpan:c.number,ariaControls:c.spaceSeparated,ariaCurrent:null,ariaDescribedBy:c.spaceSeparated,ariaDetails:null,ariaDisabled:c.booleanish,ariaDropEffect:c.spaceSeparated,ariaErrorMessage:null,ariaExpanded:c.booleanish,ariaFlowTo:c.spaceSeparated,ariaGrabbed:c.booleanish,ariaHasPopup:null,ariaHidden:c.booleanish,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:c.spaceSeparated,ariaLevel:c.number,ariaLive:null,ariaModal:c.booleanish,ariaMultiLine:c.booleanish,ariaMultiSelectable:c.booleanish,ariaOrientation:null,ariaOwns:c.spaceSeparated,ariaPlaceholder:null,ariaPosInSet:c.number,ariaPressed:c.booleanish,ariaReadOnly:c.booleanish,ariaRelevant:null,ariaRequired:c.booleanish,ariaRoleDescription:c.spaceSeparated,ariaRowCount:c.number,ariaRowIndex:c.number,ariaRowSpan:c.number,ariaSelected:c.booleanish,ariaSetSize:c.number,ariaSort:null,ariaValueMax:c.number,ariaValueMin:c.number,ariaValueNow:c.number,ariaValueText:null,role:null}}),h=create({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:caseInsensitiveTransform,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:c.commaSeparated,acceptCharset:c.spaceSeparated,accessKey:c.spaceSeparated,action:null,allow:null,allowFullScreen:c.boolean,allowPaymentRequest:c.boolean,allowUserMedia:c.boolean,alt:null,as:null,async:c.boolean,autoCapitalize:null,autoComplete:c.spaceSeparated,autoFocus:c.boolean,autoPlay:c.boolean,capture:c.boolean,charSet:null,checked:c.boolean,cite:null,className:c.spaceSeparated,cols:c.number,colSpan:null,content:null,contentEditable:c.booleanish,controls:c.boolean,controlsList:c.spaceSeparated,coords:c.number|c.commaSeparated,crossOrigin:null,data:null,dateTime:null,decoding:null,default:c.boolean,defer:c.boolean,dir:null,dirName:null,disabled:c.boolean,download:c.overloadedBoolean,draggable:c.booleanish,encType:null,enterKeyHint:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:c.boolean,formTarget:null,headers:c.spaceSeparated,height:c.number,hidden:c.boolean,high:c.number,href:null,hrefLang:null,htmlFor:c.spaceSeparated,httpEquiv:c.spaceSeparated,id:null,imageSizes:null,imageSrcSet:null,inputMode:null,integrity:null,is:null,isMap:c.boolean,itemId:null,itemProp:c.spaceSeparated,itemRef:c.spaceSeparated,itemScope:c.boolean,itemType:c.spaceSeparated,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:c.boolean,low:c.number,manifest:null,max:null,maxLength:c.number,media:null,method:null,min:null,minLength:c.number,multiple:c.boolean,muted:c.boolean,name:null,nonce:null,noModule:c.boolean,noValidate:c.boolean,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforePrint:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:c.boolean,optimum:c.number,pattern:null,ping:c.spaceSeparated,placeholder:null,playsInline:c.boolean,poster:null,preload:null,readOnly:c.boolean,referrerPolicy:null,rel:c.spaceSeparated,required:c.boolean,reversed:c.boolean,rows:c.number,rowSpan:c.number,sandbox:c.spaceSeparated,scope:null,scoped:c.boolean,seamless:c.boolean,selected:c.boolean,shape:null,size:c.number,sizes:null,slot:null,span:c.number,spellCheck:c.booleanish,src:null,srcDoc:null,srcLang:null,srcSet:null,start:c.number,step:null,style:null,tabIndex:c.number,target:null,title:null,translate:null,type:null,typeMustMatch:c.boolean,useMap:null,value:c.booleanish,width:c.number,wrap:null,align:null,aLink:null,archive:c.spaceSeparated,axis:null,background:null,bgColor:null,border:c.number,borderColor:null,bottomMargin:c.number,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:c.boolean,declare:c.boolean,event:null,face:null,frame:null,frameBorder:null,hSpace:c.number,leftMargin:c.number,link:null,longDesc:null,lowSrc:null,marginHeight:c.number,marginWidth:c.number,noResize:c.boolean,noHref:c.boolean,noShade:c.boolean,noWrap:c.boolean,object:null,profile:null,prompt:null,rev:null,rightMargin:c.number,rules:null,scheme:null,scrolling:c.booleanish,standby:null,summary:null,text:null,topMargin:c.number,valueType:null,version:null,vAlign:null,vLink:null,vSpace:c.number,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:c.boolean,disableRemotePlayback:c.boolean,prefix:null,property:null,results:c.number,security:null,unselectable:null}}),p=create({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:caseSensitiveTransform,properties:{about:c.commaOrSpaceSeparated,accentHeight:c.number,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:c.number,amplitude:c.number,arabicForm:null,ascent:c.number,attributeName:null,attributeType:null,azimuth:c.number,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:c.number,by:null,calcMode:null,capHeight:c.number,className:c.spaceSeparated,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:c.number,diffuseConstant:c.number,direction:null,display:null,dur:null,divisor:c.number,dominantBaseline:null,download:c.boolean,dx:null,dy:null,edgeMode:null,editable:null,elevation:c.number,enableBackground:null,end:null,event:null,exponent:c.number,externalResourcesRequired:null,fill:null,fillOpacity:c.number,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:c.commaSeparated,g2:c.commaSeparated,glyphName:c.commaSeparated,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:c.number,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:c.number,horizOriginX:c.number,horizOriginY:c.number,id:null,ideographic:c.number,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:c.number,k:c.number,k1:c.number,k2:c.number,k3:c.number,k4:c.number,kernelMatrix:c.commaOrSpaceSeparated,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:c.number,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:c.number,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:c.number,overlineThickness:c.number,paintOrder:null,panose1:null,path:null,pathLength:c.number,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:c.spaceSeparated,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:c.number,pointsAtY:c.number,pointsAtZ:c.number,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:c.commaOrSpaceSeparated,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:c.commaOrSpaceSeparated,rev:c.commaOrSpaceSeparated,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:c.commaOrSpaceSeparated,requiredFeatures:c.commaOrSpaceSeparated,requiredFonts:c.commaOrSpaceSeparated,requiredFormats:c.commaOrSpaceSeparated,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:c.number,specularExponent:c.number,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:c.number,strikethroughThickness:c.number,string:null,stroke:null,strokeDashArray:c.commaOrSpaceSeparated,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:c.number,strokeOpacity:c.number,strokeWidth:null,style:null,surfaceScale:c.number,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:c.commaOrSpaceSeparated,tabIndex:c.number,tableValues:null,target:null,targetX:c.number,targetY:c.number,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:c.commaOrSpaceSeparated,to:null,transform:null,u1:null,u2:null,underlinePosition:c.number,underlineThickness:c.number,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:c.number,values:null,vAlphabetic:c.number,vMathematical:c.number,vectorEffect:null,vHanging:c.number,vIdeographic:c.number,version:null,vertAdvY:c.number,vertOriginX:c.number,vertOriginY:c.number,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:c.number,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),f=merge([s,a,l,u,h],"html"),d=merge([s,a,l,u,p],"svg")},1588:function(e,t,n){"use strict";n.d(t,{s:function(){return find}});var r=n(3859),i=n(5729),o=n(9255);let a=/^data[-\w.:]+$/i,s=/-[a-z]/g,l=/[A-Z]/g;function find(e,t){let n=(0,r.F)(t),c=t,u=o.k;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&a.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(s,camelcase);c="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!s.test(e)){let n=e.replace(l,kebab);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}u=i.I}return new u(c,t)}function kebab(e){return"-"+e.toLowerCase()}function camelcase(e){return e.charAt(1).toUpperCase()}},3880:function(e,t,n){"use strict";n.d(t,{D:function(){return r}});let r={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"}},3859:function(e,t,n){"use strict";function normalize(e){return e.toLowerCase()}n.d(t,{F:function(){return normalize}})},5729:function(e,t,n){"use strict";n.d(t,{I:function(){return DefinedInfo}});var r=n(9255),i=n(7312);let o=Object.keys(i);let DefinedInfo=class DefinedInfo extends r.k{constructor(e,t,n,r){var a,s;let l=-1;if(super(e,t),r&&(this.space=r),"number"==typeof n)for(;++l<o.length;){let e=o[l];a=o[l],(s=(n&i[e])===i[e])&&(this[a]=s)}}};DefinedInfo.prototype.defined=!0},9255:function(e,t,n){"use strict";n.d(t,{k:function(){return Info}});let Info=class Info{constructor(e,t){this.property=e,this.attribute=t}};Info.prototype.space=null,Info.prototype.boolean=!1,Info.prototype.booleanish=!1,Info.prototype.overloadedBoolean=!1,Info.prototype.number=!1,Info.prototype.commaSeparated=!1,Info.prototype.spaceSeparated=!1,Info.prototype.commaOrSpaceSeparated=!1,Info.prototype.mustUseProperty=!1,Info.prototype.defined=!1},7312:function(e,t,n){"use strict";n.r(t),n.d(t,{boolean:function(){return i},booleanish:function(){return o},commaOrSpaceSeparated:function(){return u},commaSeparated:function(){return c},number:function(){return s},overloadedBoolean:function(){return a},spaceSeparated:function(){return l}});let r=0,i=increment(),o=increment(),a=increment(),s=increment(),l=increment(),c=increment(),u=increment();function increment(){return 2**++r}},5582:function(e,t,n){"use strict";n.d(t,{U:function(){return Markdown}});var r={};n.r(r),n.d(r,{attentionMarkers:function(){return ey},contentInitial:function(){return eg},disable:function(){return eN},document:function(){return eT},flow:function(){return e_},flowInitial:function(){return eE},insideSpan:function(){return eC},string:function(){return eA},text:function(){return ek}});var i=n(4345),o=n(5668);let a=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,s=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,l={};function lib_name(e,t){let n=(t||l).jsx?s:a;return n.test(e)}let c=/[ \t\n\f\r]/g;function empty(e){return""===e.replace(c,"")}var u=n(1634),h=n(1588),p=n(3880),f=n(342),d=n(9221),m=d.default||d;point("end");let T=point("start");function point(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function lib_point(e){return index(e&&e.line)+":"+index(e&&e.column)}function lib_position(e){return lib_point(e&&e.start)+"-"+lib_point(e&&e.end)}function index(e){return e&&"number"==typeof e?e:1}let VFileMessage=class VFileMessage extends Error{constructor(e,t,n){var r;super(),"string"==typeof t&&(n=t,t=void 0);let i="",o={},a=!1;if(t&&(o="line"in t&&"column"in t?{place:t}:"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?i=e:!o.cause&&e&&(a=!0,i=e.message,o.cause=e),!o.ruleId&&!o.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?o.ruleId=n:(o.source=n.slice(0,e),o.ruleId=n.slice(e+1))}if(!o.place&&o.ancestors&&o.ancestors){let e=o.ancestors[o.ancestors.length-1];e&&(o.place=e.position)}let s=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=s?s.line:void 0,this.name=((r=o.place)&&"object"==typeof r?"position"in r||"type"in r?lib_position(r.position):"start"in r||"end"in r?lib_position(r):"line"in r||"column"in r?lib_point(r):"":"")||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=a&&o.cause&&"string"==typeof o.cause.stack?o.cause.stack:"",this.actual,this.expected,this.note,this.url}};VFileMessage.prototype.file="",VFileMessage.prototype.name="",VFileMessage.prototype.reason="",VFileMessage.prototype.message="",VFileMessage.prototype.stack="",VFileMessage.prototype.column=void 0,VFileMessage.prototype.line=void 0,VFileMessage.prototype.ancestors=void 0,VFileMessage.prototype.cause=void 0,VFileMessage.prototype.fatal=void 0,VFileMessage.prototype.place=void 0,VFileMessage.prototype.ruleId=void 0,VFileMessage.prototype.source=void 0;let g={}.hasOwnProperty,E=new Map,_=/[A-Z]/g,A=/-([a-z])/g,k=new Set(["table","tbody","thead","tfoot","tr"]),C=new Set(["td","th"]),y="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function one(e,t,n){return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(i=u.YP,e.schema=i),e.ancestors.push(t);let a=findComponentFromName(e,t.tagName,!1),s=function(e,t){let n,r;let i={};for(r in t.properties)if("children"!==r&&g.call(t.properties,r)){let a=function(e,t,n){let r=(0,h.s)(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?(0,o.P)(n):(0,f.P)(n)),"style"===r.property){let t="object"==typeof n?n:function(e,t){let n={};try{m(t,function(e,t){let r=e;"--"!==r.slice(0,2)&&("-ms-"===r.slice(0,4)&&(r="ms-"+r.slice(4)),r=r.replace(A,toCamel)),n[r]=t})}catch(t){if(!e.ignoreInvalidStyle){let n=new VFileMessage("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:t,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=y+"#cannot-parse-style-attribute",n}}return n}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t;let n={};for(t in e)g.call(e,t)&&(n[function(e){let t=e.replace(_,toDash);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?p.D[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(a){let[r,o]=a;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof o&&C.has(t.tagName)?n=o:i[r]=o}}if(n){let t=i.style||(i.style={});t["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n}return i}(e,t),l=createChildren(e,t);return k.has(t.tagName)&&(l=l.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&empty(e.value):empty(e))})),addNode(e,s,a,t),addChildren(s,l),e.ancestors.pop(),e.schema=r,e.create(t,a,s,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree,r=n.body[0];return(0,i.ok)("ExpressionStatement"===r.type),e.evaluater.evaluateExpression(r.expression)}crashEstree(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,o=r;"svg"===t.name&&"html"===r.space&&(o=u.YP,e.schema=o),e.ancestors.push(t);let a=null===t.name?e.Fragment:findComponentFromName(e,t.name,!0),s=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type){if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree,o=t.body[0];(0,i.ok)("ExpressionStatement"===o.type);let a=o.expression;(0,i.ok)("ObjectExpression"===a.type);let s=a.properties[0];(0,i.ok)("SpreadElement"===s.type),Object.assign(n,e.evaluater.evaluateExpression(s.argument))}else crashEstree(e,t.position)}else{let o;let a=r.name;if(r.value&&"object"==typeof r.value){if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree,n=t.body[0];(0,i.ok)("ExpressionStatement"===n.type),o=e.evaluater.evaluateExpression(n.expression)}else crashEstree(e,t.position)}else o=null===r.value||r.value;n[a]=o}return n}(e,t),l=createChildren(e,t);return addNode(e,s,a,t),addChildren(s,l),e.ancestors.pop(),e.schema=r,e.create(t,a,s,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);crashEstree(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return addChildren(r,createChildren(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?t.value:void 0}function addNode(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function addChildren(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function createChildren(e,t){let n=[],r=-1,i=e.passKeys?new Map:E;for(;++r<t.children.length;){let o;let a=t.children[r];if(e.passKeys){let e="element"===a.type?a.tagName:"mdxJsxFlowElement"===a.type||"mdxJsxTextElement"===a.type?a.name:void 0;if(e){let t=i.get(e)||0;o=e+"-"+t,i.set(e,t+1)}}let s=one(e,a,o);void 0!==s&&n.push(s)}return n}function findComponentFromName(e,t,n){let r;if(n){if(t.includes(".")){let e;let n=t.split("."),o=-1;for(;++o<n.length;){let t=lib_name(n[o])?{type:"Identifier",name:n[o]}:{type:"Literal",value:n[o]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(o&&"Literal"===t.type),optional:!1}:t}(0,i.ok)(e,"always a result"),r=e}else r=lib_name(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t}}else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return g.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);crashEstree(e)}function crashEstree(e,t){let n=new VFileMessage("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=y+"#cannot-handle-mdx-estrees-without-createevaluater",n}function toCamel(e,t){return t.toUpperCase()}function toDash(e){return"-"+e.toLowerCase()}let N={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var S=n(5893),I=n(7962),b=n(2888);let SpliceBuffer=class SpliceBuffer{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){let r=t||0;this.setCursor(Math.trunc(e));let i=this.right.splice(this.right.length-r,Number.POSITIVE_INFINITY);return n&&chunkedPush(this.left,n),i.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),chunkedPush(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),chunkedPush(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length)){if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);chunkedPush(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);chunkedPush(this.left,t.reverse())}}}};function chunkedPush(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function subtokenize(e){let t,n,r,i,o,a,s;let l={},c=-1,u=new SpliceBuffer(e);for(;++c<u.length;){for(;(c in l);)c=l[c];if(t=u.get(c),c&&"chunkFlow"===t[1].type&&"listItemPrefix"===u.get(c-1)[1].type&&((r=0)<(a=t[1]._tokenizer.events).length&&"lineEndingBlank"===a[r][1].type&&(r+=2),r<a.length&&"content"===a[r][1].type))for(;++r<a.length&&"content"!==a[r][1].type;)"chunkText"===a[r][1].type&&(a[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(l,function(e,t){let n,r;let i=e.get(t)[1],o=e.get(t)[2],a=t-1,s=[],l=i._tokenizer||o.parser[i.contentType](i.start),c=l.events,u=[],h={},p=-1,f=i,d=0,m=0,T=[m];for(;f;){for(;e.get(++a)[1]!==f;);s.push(a),!f._tokenizer&&(n=o.sliceStream(f),f.next||n.push(null),r&&l.defineSkip(f.start),f._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(n),f._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),r=f,f=f.next}for(f=i;++p<c.length;)"exit"===c[p][0]&&"enter"===c[p-1][0]&&c[p][1].type===c[p-1][1].type&&c[p][1].start.line!==c[p][1].end.line&&(m=p+1,T.push(m),f._tokenizer=void 0,f.previous=void 0,f=f.next);for(l.events=[],f?(f._tokenizer=void 0,f.previous=void 0):T.pop(),p=T.length;p--;){let t=c.slice(T[p],T[p+1]),n=s.pop();u.push([n,n+t.length-1]),e.splice(n,2,t)}for(u.reverse(),p=-1;++p<u.length;)h[d+u[p][0]]=d+u[p][1],d+=u[p][1]-u[p][0]-1;return h}(u,c)),c=l[c],s=!0);else if(t[1]._container){for(r=c,n=void 0;r--;)if("lineEnding"===(i=u.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(u.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else break;n&&(t[1].end=Object.assign({},u.get(n)[1].start),(o=u.slice(n,c)).unshift(t),u.splice(n,c-n+1,o))}}return(0,b.d)(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!s}var O=n(4663),x=n(2761),R=n(5459);let v={tokenize:function(e){let t;let n=e.attempt(this.parser.constructs.contentInitial,function(t){if(null===t){e.consume(t);return}return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,x.f)(e,n,"linePrefix")},function(n){return e.enter("paragraph"),function lineStart(n){let r=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=r),t=r,function data(t){if(null===t){e.exit("chunkText"),e.exit("paragraph"),e.consume(t);return}return(0,R.Ch)(t)?(e.consume(t),e.exit("chunkText"),lineStart):(e.consume(t),data)}(n)}(n)});return n}},L={tokenize:function(e){let t,n,r;let i=this,o=[],a=0;return start;function start(t){if(a<o.length){let n=o[a];return i.containerState=n[1],e.attempt(n[0].continuation,documentContinue,checkNewContainers)(t)}return checkNewContainers(t)}function documentContinue(e){if(a++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&closeFlow();let r=i.events.length,o=r;for(;o--;)if("exit"===i.events[o][0]&&"chunkFlow"===i.events[o][1].type){n=i.events[o][1].end;break}exitContainers(a);let s=r;for(;s<i.events.length;)i.events[s][1].end=Object.assign({},n),s++;return(0,b.d)(i.events,o+1,0,i.events.slice(r)),i.events.length=s,checkNewContainers(e)}return start(e)}function checkNewContainers(n){if(a===o.length){if(!t)return documentContinued(n);if(t.currentConstruct&&t.currentConstruct.concrete)return flowStart(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(P,thereIsANewContainer,thereIsNoNewContainer)(n)}function thereIsANewContainer(e){return t&&closeFlow(),exitContainers(a),documentContinued(e)}function thereIsNoNewContainer(e){return i.parser.lazy[i.now().line]=a!==o.length,r=i.now().offset,flowStart(e)}function documentContinued(t){return i.containerState={},e.attempt(P,containerContinue,flowStart)(t)}function containerContinue(e){return a++,o.push([i.currentConstruct,i.containerState]),documentContinued(e)}function flowStart(r){if(null===r){t&&closeFlow(),exitContainers(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{contentType:"flow",previous:n,_tokenizer:t}),function flowContinue(t){if(null===t){writeToChild(e.exit("chunkFlow"),!0),exitContainers(0),e.consume(t);return}return(0,R.Ch)(t)?(e.consume(t),writeToChild(e.exit("chunkFlow")),a=0,i.interrupt=void 0,start):(e.consume(t),flowContinue)}(r)}function writeToChild(e,o){let s=i.sliceStream(e);if(o&&s.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(s),i.parser.lazy[e.start.line]){let e,n,o=t.events.length;for(;o--;)if(t.events[o][1].start.offset<r&&(!t.events[o][1].end||t.events[o][1].end.offset>r))return;let s=i.events.length,l=s;for(;l--;)if("exit"===i.events[l][0]&&"chunkFlow"===i.events[l][1].type){if(e){n=i.events[l][1].end;break}e=!0}for(exitContainers(a),o=s;o<i.events.length;)i.events[o][1].end=Object.assign({},n),o++;(0,b.d)(i.events,l+1,0,i.events.slice(s)),i.events.length=o}}function exitContainers(t){let n=o.length;for(;n-- >t;){let t=o[n];i.containerState=t[1],t[0].exit.call(i,e)}o.length=t}function closeFlow(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},P={tokenize:function(e,t,n){return(0,x.f)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var M=n(3402);let D={tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),chunkInside(t)};function chunkInside(t){return null===t?contentEnd(t):(0,R.Ch)(t)?e.check(w,contentContinue,contentEnd)(t):(e.consume(t),chunkInside)}function contentEnd(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function contentContinue(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,chunkInside}},resolve:function(e){return subtokenize(e),e}},w={tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,x.f)(e,prefixed,"linePrefix")};function prefixed(i){if(null===i||(0,R.Ch)(i))return n(i);let o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}},partial:!0},H={tokenize:function(e){let t=this,n=e.attempt(M.w,function(r){if(null===r){e.consume(r);return}return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n},e.attempt(this.parser.constructs.flowInitial,afterConstruct,(0,x.f)(e,e.attempt(this.parser.constructs.flow,afterConstruct,e.attempt(D,afterConstruct)),"linePrefix")));return n;function afterConstruct(r){if(null===r){e.consume(r);return}return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n}}},F={resolveAll:createResolver()},B=initializeFactory("string"),U=initializeFactory("text");function initializeFactory(e){return{tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,start,notText);return start;function start(e){return atBreak(e)?i(e):notText(e)}function notText(e){if(null===e){t.consume(e);return}return t.enter("data"),t.consume(e),data}function data(e){return atBreak(e)?(t.exit("data"),i(e)):(t.consume(e),data)}function atBreak(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}},resolveAll:createResolver("text"===e?resolveAllLineSuffixes:void 0)}}function createResolver(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function resolveAllLineSuffixes(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r;let i=e[n-1][1],o=t.sliceStream(i),a=o.length,s=-1,l=0;for(;a--;){let e=o[a];if("string"==typeof e){for(s=e.length;32===e.charCodeAt(s-1);)l++,s--;if(s)break;s=-1}else if(-2===e)r=!0,l++;else if(-1===e);else{a++;break}}if(l){let o={type:n===e.length||r||l<2?"lineSuffix":"hardBreakTrailing",start:{line:i.end.line,column:i.end.column-l,offset:i.end.offset-l,_index:i.start._index+a,_bufferIndex:a?s:i.start._bufferIndex+s},end:Object.assign({},i.end)};i.end=Object.assign({},o.start),i.start.offset===i.end.offset?Object.assign(i,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}var z=n(3233);let G={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(o){return e.enter("thematicBreak"),r=o,function atBreak(o){return o===r?(e.enter("thematicBreakSequence"),function sequence(t){return t===r?(e.consume(t),i++,sequence):(e.exit("thematicBreakSequence"),(0,R.xz)(t)?(0,x.f)(e,atBreak,"whitespace")(t):atBreak(t))}(o)):i>=3&&(null===o||(0,R.Ch)(o))?(e.exit("thematicBreak"),t(o)):n(o)}(o)}}},K={name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],o=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,a=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,R.pY)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(G,n,atMarker)(t):atMarker(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function inside(t){return(0,R.pY)(t)&&++a<10?(e.consume(t),inside):(!r.interrupt||a<2)&&(r.containerState.marker?t===r.containerState.marker:41===t||46===t)?(e.exit("listItemValue"),atMarker(t)):n(t)}(t)}return n(t)};function atMarker(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(M.w,r.interrupt?n:onBlank,e.attempt(j,endOfPrefix,otherPrefix))}function onBlank(e){return r.containerState.initialBlankLine=!0,o++,endOfPrefix(e)}function otherPrefix(t){return(0,R.xz)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),endOfPrefix):n(t)}function endOfPrefix(n){return r.containerState.size=o+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}},continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(M.w,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,x.f)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,R.xz)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,notInCurrentItem(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(V,t,notInCurrentItem)(n))});function notInCurrentItem(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,x.f)(e,e.attempt(K,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)}},j={tokenize:function(e,t,n){let r=this;return(0,x.f)(e,function(e){let i=r.events[r.events.length-1];return!(0,R.xz)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)},partial:!0},V={tokenize:function(e,t,n){let r=this;return(0,x.f)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)},partial:!0},Y={name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),after}return n(t)};function after(n){return(0,R.xz)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}},continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,R.xz)(t)?(0,x.f)(e,contBefore,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):contBefore(t)};function contBefore(r){return e.attempt(Y,t,n)(r)}}},exit:function(e){e.exit("blockQuote")}};function factoryDestination(e,t,n,r,i,o,a,s,l){let c=l||Number.POSITIVE_INFINITY,u=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(o),e.consume(t),e.exit(o),enclosedBefore):null===t||32===t||41===t||(0,R.Av)(t)?n(t):(e.enter(r),e.enter(a),e.enter(s),e.enter("chunkString",{contentType:"string"}),raw(t))};function enclosedBefore(n){return 62===n?(e.enter(o),e.consume(n),e.exit(o),e.exit(i),e.exit(r),t):(e.enter(s),e.enter("chunkString",{contentType:"string"}),enclosed(n))}function enclosed(t){return 62===t?(e.exit("chunkString"),e.exit(s),enclosedBefore(t)):null===t||60===t||(0,R.Ch)(t)?n(t):(e.consume(t),92===t?enclosedEscape:enclosed)}function enclosedEscape(t){return 60===t||62===t||92===t?(e.consume(t),enclosed):enclosed(t)}function raw(i){return!u&&(null===i||41===i||(0,R.z3)(i))?(e.exit("chunkString"),e.exit(s),e.exit(a),e.exit(r),t(i)):u<c&&40===i?(e.consume(i),u++,raw):41===i?(e.consume(i),u--,raw):null===i||32===i||40===i||(0,R.Av)(i)?n(i):(e.consume(i),92===i?rawEscape:raw)}function rawEscape(t){return 40===t||41===t||92===t?(e.consume(t),raw):raw(t)}}function factoryLabel(e,t,n,r,i,o){let a;let s=this,l=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(o),atBreak};function atBreak(c){return l>999||null===c||91===c||93===c&&!a||94===c&&!l&&"_hiddenFootnoteSupport"in s.parser.constructs?n(c):93===c?(e.exit(o),e.enter(i),e.consume(c),e.exit(i),e.exit(r),t):(0,R.Ch)(c)?(e.enter("lineEnding"),e.consume(c),e.exit("lineEnding"),atBreak):(e.enter("chunkString",{contentType:"string"}),labelInside(c))}function labelInside(t){return null===t||91===t||93===t||(0,R.Ch)(t)||l++>999?(e.exit("chunkString"),atBreak(t)):(e.consume(t),a||(a=!(0,R.xz)(t)),92===t?labelEscape:labelInside)}function labelEscape(t){return 91===t||92===t||93===t?(e.consume(t),l++,labelInside):labelInside(t)}}function factoryTitle(e,t,n,r,i,o){let a;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),a=40===t?41:t,begin):n(t)};function begin(n){return n===a?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(o),atBreak(n))}function atBreak(t){return t===a?(e.exit(o),begin(a)):null===t?n(t):(0,R.Ch)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,x.f)(e,atBreak,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),inside(t))}function inside(t){return t===a||null===t||(0,R.Ch)(t)?(e.exit("chunkString"),atBreak(t)):(e.consume(t),92===t?escape:inside)}function escape(t){return t===a||92===t?(e.consume(t),inside):inside(t)}}function factoryWhitespace(e,t){let n;return function start(r){return(0,R.Ch)(r)?(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),n=!0,start):(0,R.xz)(r)?(0,x.f)(e,start,n?"linePrefix":"lineSuffix")(r):t(r)}}var W=n(1098);let Q={tokenize:function(e,t,n){return function(t){return(0,R.z3)(t)?factoryWhitespace(e,beforeMarker)(t):n(t)};function beforeMarker(t){return factoryTitle(e,titleAfter,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function titleAfter(t){return(0,R.xz)(t)?(0,x.f)(e,titleAfterOptionalWhitespace,"whitespace")(t):titleAfterOptionalWhitespace(t)}function titleAfterOptionalWhitespace(e){return null===e||(0,R.Ch)(e)?t(e):n(e)}},partial:!0},q={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,x.f)(e,afterPrefix,"linePrefix",5)(t)};function afterPrefix(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function atBreak(t){return null===t?after(t):(0,R.Ch)(t)?e.attempt(X,atBreak,after)(t):(e.enter("codeFlowValue"),function inside(t){return null===t||(0,R.Ch)(t)?(e.exit("codeFlowValue"),atBreak(t)):(e.consume(t),inside)}(t))}(t):n(t)}function after(n){return e.exit("codeIndented"),t(n)}}},X={tokenize:function(e,t,n){let r=this;return furtherStart;function furtherStart(t){return r.parser.lazy[r.now().line]?n(t):(0,R.Ch)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),furtherStart):(0,x.f)(e,afterPrefix,"linePrefix",5)(t)}function afterPrefix(e){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):(0,R.Ch)(e)?furtherStart(e):n(e)}},partial:!0},$={name:"setextUnderline",tokenize:function(e,t,n){let r;let i=this;return function(t){let o,a=i.events.length;for(;a--;)if("lineEnding"!==i.events[a][1].type&&"linePrefix"!==i.events[a][1].type&&"content"!==i.events[a][1].type){o="paragraph"===i.events[a][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||o)?(e.enter("setextHeadingLine"),r=t,e.enter("setextHeadingLineSequence"),function inside(t){return t===r?(e.consume(t),inside):(e.exit("setextHeadingLineSequence"),(0,R.xz)(t)?(0,x.f)(e,after,"lineSuffix")(t):after(t))}(t)):n(t)};function after(r){return null===r||(0,R.Ch)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}},resolveTo:function(e,t){let n,r,i,o=e.length;for(;o--;)if("enter"===e[o][0]){if("content"===e[o][1].type){n=o;break}"paragraph"===e[o][1].type&&(r=o)}else"content"===e[o][1].type&&e.splice(o,1),i||"definition"!==e[o][1].type||(i=o);let a={type:"setextHeading",start:Object.assign({},e[r][1].start),end:Object.assign({},e[e.length-1][1].end)};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",a,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end=Object.assign({},e[i][1].end)):e[n][1]=a,e.push(["exit",a,t]),e}},J=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Z=["pre","script","style","textarea"],ee={tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(M.w,t,n)}},partial:!0},et={tokenize:function(e,t,n){let r=this;return function(t){return(0,R.Ch)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),after):n(t)};function after(e){return r.parser.lazy[r.now().line]?n(e):t(e)}},partial:!0},en={tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),lineStart)};function lineStart(e){return r.parser.lazy[r.now().line]?n(e):t(e)}},partial:!0},er={name:"codeFenced",tokenize:function(e,t,n){let r;let i=this,o={tokenize:function(e,t,n){let o=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),start};function start(t){return e.enter("codeFencedFence"),(0,R.xz)(t)?(0,x.f)(e,beforeSequenceClose,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):beforeSequenceClose(t)}function beforeSequenceClose(t){return t===r?(e.enter("codeFencedFenceSequence"),function sequenceClose(t){return t===r?(o++,e.consume(t),sequenceClose):o>=s?(e.exit("codeFencedFenceSequence"),(0,R.xz)(t)?(0,x.f)(e,sequenceCloseAfter,"whitespace")(t):sequenceCloseAfter(t)):n(t)}(t)):n(t)}function sequenceCloseAfter(r){return null===r||(0,R.Ch)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}},partial:!0},a=0,s=0;return function(t){return function(t){let o=i.events[i.events.length-1];return a=o&&"linePrefix"===o[1].type?o[2].sliceSerialize(o[1],!0).length:0,r=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function sequenceOpen(t){return t===r?(s++,e.consume(t),sequenceOpen):s<3?n(t):(e.exit("codeFencedFenceSequence"),(0,R.xz)(t)?(0,x.f)(e,infoBefore,"whitespace")(t):infoBefore(t))}(t)}(t)};function infoBefore(o){return null===o||(0,R.Ch)(o)?(e.exit("codeFencedFence"),i.interrupt?t(o):e.check(en,atNonLazyBreak,after)(o)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function info(t){return null===t||(0,R.Ch)(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),infoBefore(t)):(0,R.xz)(t)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,x.f)(e,metaBefore,"whitespace")(t)):96===t&&t===r?n(t):(e.consume(t),info)}(o))}function metaBefore(t){return null===t||(0,R.Ch)(t)?infoBefore(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function meta(t){return null===t||(0,R.Ch)(t)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),infoBefore(t)):96===t&&t===r?n(t):(e.consume(t),meta)}(t))}function atNonLazyBreak(t){return e.attempt(o,after,contentBefore)(t)}function contentBefore(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),contentStart}function contentStart(t){return a>0&&(0,R.xz)(t)?(0,x.f)(e,beforeContentChunk,"linePrefix",a+1)(t):beforeContentChunk(t)}function beforeContentChunk(t){return null===t||(0,R.Ch)(t)?e.check(en,atNonLazyBreak,after)(t):(e.enter("codeFlowValue"),function contentChunk(t){return null===t||(0,R.Ch)(t)?(e.exit("codeFlowValue"),beforeContentChunk(t)):(e.consume(t),contentChunk)}(t))}function after(n){return e.exit("codeFenced"),t(n)}},concrete:!0},ei=document.createElement("i");function decodeNamedCharacterReference(e){let t="&"+e+";";ei.innerHTML=t;let n=ei.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let eo={name:"characterReference",tokenize:function(e,t,n){let r,i;let o=this,a=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),open};function open(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),numeric):(e.enter("characterReferenceValue"),r=31,i=R.H$,value(t))}function numeric(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=R.AF,value):(e.enter("characterReferenceValue"),r=7,i=R.pY,value(t))}function value(s){if(59===s&&a){let r=e.exit("characterReferenceValue");return i!==R.H$||decodeNamedCharacterReference(o.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(s),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(s)}return i(s)&&a++<r?(e.consume(s),value):n(s)}}},ea={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),inside};function inside(r){return(0,R.sR)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},es={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,x.f)(e,t,"linePrefix")}}},el={name:"labelEnd",tokenize:function(e,t,n){let r,i;let o=this,a=o.events.length;for(;a--;)if(("labelImage"===o.events[a][1].type||"labelLink"===o.events[a][1].type)&&!o.events[a][1]._balanced){r=o.events[a][1];break}return function(t){return r?r._inactive?labelEndNok(t):(i=o.parser.defined.includes((0,W.d)(o.sliceSerialize({start:r.end,end:o.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),after):n(t)};function after(t){return 40===t?e.attempt(ec,labelEndOk,i?labelEndOk:labelEndNok)(t):91===t?e.attempt(eu,labelEndOk,i?referenceNotFull:labelEndNok)(t):i?labelEndOk(t):labelEndNok(t)}function referenceNotFull(t){return e.attempt(eh,labelEndOk,labelEndNok)(t)}function labelEndOk(e){return t(e)}function labelEndNok(e){return r._balanced=!0,n(e)}},resolveTo:function(e,t){let n,r,i,o,a=e.length,s=0;for(;a--;)if(n=e[a][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[a][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[a][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=a,"labelLink"!==n.type)){s=2;break}}else"labelEnd"===n.type&&(i=a);let l={type:"labelLink"===e[r][1].type?"link":"image",start:Object.assign({},e[r][1].start),end:Object.assign({},e[e.length-1][1].end)},c={type:"label",start:Object.assign({},e[r][1].start),end:Object.assign({},e[i][1].end)},u={type:"labelText",start:Object.assign({},e[r+s+2][1].end),end:Object.assign({},e[i-2][1].start)};return o=[["enter",l,t],["enter",c,t]],o=(0,b.V)(o,e.slice(r+1,r+s+3)),o=(0,b.V)(o,[["enter",u,t]]),o=(0,b.V)(o,(0,z.C)(t.parser.constructs.insideSpan.null,e.slice(r+s+4,i-3),t)),o=(0,b.V)(o,[["exit",u,t],e[i-2],e[i-1],["exit",c,t]]),o=(0,b.V)(o,e.slice(i+1)),o=(0,b.V)(o,[["exit",l,t]]),(0,b.d)(e,r,e.length,o),e},resolveAll:function(e){let t=-1;for(;++t<e.length;){let n=e[t][1];("labelImage"===n.type||"labelLink"===n.type||"labelEnd"===n.type)&&(e.splice(t+1,"labelImage"===n.type?4:2),n.type="data",t++)}return e}},ec={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),resourceBefore};function resourceBefore(t){return(0,R.z3)(t)?factoryWhitespace(e,resourceOpen)(t):resourceOpen(t)}function resourceOpen(t){return 41===t?resourceEnd(t):factoryDestination(e,resourceDestinationAfter,resourceDestinationMissing,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function resourceDestinationAfter(t){return(0,R.z3)(t)?factoryWhitespace(e,resourceBetween)(t):resourceEnd(t)}function resourceDestinationMissing(e){return n(e)}function resourceBetween(t){return 34===t||39===t||40===t?factoryTitle(e,resourceTitleAfter,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):resourceEnd(t)}function resourceTitleAfter(t){return(0,R.z3)(t)?factoryWhitespace(e,resourceEnd)(t):resourceEnd(t)}function resourceEnd(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},eu={tokenize:function(e,t,n){let r=this;return function(t){return factoryLabel.call(r,e,referenceFullAfter,referenceFullMissing,"reference","referenceMarker","referenceString")(t)};function referenceFullAfter(e){return r.parser.defined.includes((0,W.d)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function referenceFullMissing(e){return n(e)}}},eh={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),referenceCollapsedOpen};function referenceCollapsedOpen(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},ep={name:"labelStartImage",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),open};function open(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),after):n(t)}function after(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}},resolveAll:el.resolveAll};var ef=n(2987);let ed={name:"attention",tokenize:function(e,t){let n;let r=this.parser.constructs.attentionMarkers.null,i=this.previous,o=(0,ef.r)(i);return function(a){return n=a,e.enter("attentionSequence"),function inside(a){if(a===n)return e.consume(a),inside;let s=e.exit("attentionSequence"),l=(0,ef.r)(a),c=!l||2===l&&o||r.includes(a),u=!o||2===o&&l||r.includes(i);return s._open=!!(42===n?c:c&&(o||!u)),s._close=!!(42===n?u:u&&(l||!c)),t(a)}(a)}},resolveAll:function(e,t){let n,r,i,o,a,s,l,c,u=-1;for(;++u<e.length;)if("enter"===e[u][0]&&"attentionSequence"===e[u][1].type&&e[u][1]._close){for(n=u;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[u][1]).charCodeAt(0)){if((e[n][1]._close||e[u][1]._open)&&(e[u][1].end.offset-e[u][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[u][1].end.offset-e[u][1].start.offset)%3))continue;s=e[n][1].end.offset-e[n][1].start.offset>1&&e[u][1].end.offset-e[u][1].start.offset>1?2:1;let h=Object.assign({},e[n][1].end),p=Object.assign({},e[u][1].start);movePoint(h,-s),movePoint(p,s),o={type:s>1?"strongSequence":"emphasisSequence",start:h,end:Object.assign({},e[n][1].end)},a={type:s>1?"strongSequence":"emphasisSequence",start:Object.assign({},e[u][1].start),end:p},i={type:s>1?"strongText":"emphasisText",start:Object.assign({},e[n][1].end),end:Object.assign({},e[u][1].start)},r={type:s>1?"strong":"emphasis",start:Object.assign({},o.start),end:Object.assign({},a.end)},e[n][1].end=Object.assign({},o.start),e[u][1].start=Object.assign({},a.end),l=[],e[n][1].end.offset-e[n][1].start.offset&&(l=(0,b.V)(l,[["enter",e[n][1],t],["exit",e[n][1],t]])),l=(0,b.V)(l,[["enter",r,t],["enter",o,t],["exit",o,t],["enter",i,t]]),l=(0,b.V)(l,(0,z.C)(t.parser.constructs.insideSpan.null,e.slice(n+1,u),t)),l=(0,b.V)(l,[["exit",i,t],["enter",a,t],["exit",a,t],["exit",r,t]]),e[u][1].end.offset-e[u][1].start.offset?(c=2,l=(0,b.V)(l,[["enter",e[u][1],t],["exit",e[u][1],t]])):c=0,(0,b.d)(e,n-1,u-n+3,l),u=n+l.length-c-2;break}}for(u=-1;++u<e.length;)"attentionSequence"===e[u][1].type&&(e[u][1].type="data");return e}};function movePoint(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let em={name:"labelStartLink",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),after};function after(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}},resolveAll:el.resolveAll},eT={42:K,43:K,45:K,48:K,49:K,50:K,51:K,52:K,53:K,54:K,55:K,56:K,57:K,62:Y},eg={91:{name:"definition",tokenize:function(e,t,n){let r;let i=this;return function(t){return e.enter("definition"),factoryLabel.call(i,e,labelAfter,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)};function labelAfter(t){return(r=(0,W.d)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),markerAfter):n(t)}function markerAfter(t){return(0,R.z3)(t)?factoryWhitespace(e,destinationBefore)(t):destinationBefore(t)}function destinationBefore(t){return factoryDestination(e,destinationAfter,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function destinationAfter(t){return e.attempt(Q,after,after)(t)}function after(t){return(0,R.xz)(t)?(0,x.f)(e,afterWhitespace,"whitespace")(t):afterWhitespace(t)}function afterWhitespace(o){return null===o||(0,R.Ch)(o)?(e.exit("definition"),i.parser.defined.push(r),t(o)):n(o)}}}},eE={[-2]:q,[-1]:q,32:q},e_={35:{name:"headingAtx",tokenize:function(e,t,n){let r=0;return function(i){return e.enter("atxHeading"),e.enter("atxHeadingSequence"),function sequenceOpen(i){return 35===i&&r++<6?(e.consume(i),sequenceOpen):null===i||(0,R.z3)(i)?(e.exit("atxHeadingSequence"),function atBreak(n){return 35===n?(e.enter("atxHeadingSequence"),function sequenceFurther(t){return 35===t?(e.consume(t),sequenceFurther):(e.exit("atxHeadingSequence"),atBreak(t))}(n)):null===n||(0,R.Ch)(n)?(e.exit("atxHeading"),t(n)):(0,R.xz)(n)?(0,x.f)(e,atBreak,"whitespace")(n):(e.enter("atxHeadingText"),function data(t){return null===t||35===t||(0,R.z3)(t)?(e.exit("atxHeadingText"),atBreak(t)):(e.consume(t),data)}(n))}(i)):n(i)}(i)}},resolve:function(e,t){let n,r,i=e.length-2,o=3;return"whitespace"===e[3][1].type&&(o+=2),i-2>o&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(o===i-1||i-4>o&&"whitespace"===e[i-2][1].type)&&(i-=o+1===i?2:4),i>o&&(n={type:"atxHeadingText",start:e[o][1].start,end:e[i][1].end},r={type:"chunkText",start:e[o][1].start,end:e[i][1].end,contentType:"text"},(0,b.d)(e,o,i-o+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e}},42:G,45:[$,G],60:{name:"htmlFlow",tokenize:function(e,t,n){let r,i,o,a,s;let l=this;return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),open};function open(a){return 33===a?(e.consume(a),declarationOpen):47===a?(e.consume(a),i=!0,tagCloseStart):63===a?(e.consume(a),r=3,l.interrupt?t:continuationDeclarationInside):(0,R.jv)(a)?(e.consume(a),o=String.fromCharCode(a),tagName):n(a)}function declarationOpen(i){return 45===i?(e.consume(i),r=2,commentOpenInside):91===i?(e.consume(i),r=5,a=0,cdataOpenInside):(0,R.jv)(i)?(e.consume(i),r=4,l.interrupt?t:continuationDeclarationInside):n(i)}function commentOpenInside(r){return 45===r?(e.consume(r),l.interrupt?t:continuationDeclarationInside):n(r)}function cdataOpenInside(r){let i="CDATA[";return r===i.charCodeAt(a++)?(e.consume(r),a===i.length)?l.interrupt?t:continuation:cdataOpenInside:n(r)}function tagCloseStart(t){return(0,R.jv)(t)?(e.consume(t),o=String.fromCharCode(t),tagName):n(t)}function tagName(a){if(null===a||47===a||62===a||(0,R.z3)(a)){let s=47===a,c=o.toLowerCase();return!s&&!i&&Z.includes(c)?(r=1,l.interrupt?t(a):continuation(a)):J.includes(o.toLowerCase())?(r=6,s)?(e.consume(a),basicSelfClosing):l.interrupt?t(a):continuation(a):(r=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(a):i?function completeClosingTagAfter(t){return(0,R.xz)(t)?(e.consume(t),completeClosingTagAfter):completeEnd(t)}(a):completeAttributeNameBefore(a))}return 45===a||(0,R.H$)(a)?(e.consume(a),o+=String.fromCharCode(a),tagName):n(a)}function basicSelfClosing(r){return 62===r?(e.consume(r),l.interrupt?t:continuation):n(r)}function completeAttributeNameBefore(t){return 47===t?(e.consume(t),completeEnd):58===t||95===t||(0,R.jv)(t)?(e.consume(t),completeAttributeName):(0,R.xz)(t)?(e.consume(t),completeAttributeNameBefore):completeEnd(t)}function completeAttributeName(t){return 45===t||46===t||58===t||95===t||(0,R.H$)(t)?(e.consume(t),completeAttributeName):completeAttributeNameAfter(t)}function completeAttributeNameAfter(t){return 61===t?(e.consume(t),completeAttributeValueBefore):(0,R.xz)(t)?(e.consume(t),completeAttributeNameAfter):completeAttributeNameBefore(t)}function completeAttributeValueBefore(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),s=t,completeAttributeValueQuoted):(0,R.xz)(t)?(e.consume(t),completeAttributeValueBefore):function completeAttributeValueUnquoted(t){return null===t||34===t||39===t||47===t||60===t||61===t||62===t||96===t||(0,R.z3)(t)?completeAttributeNameAfter(t):(e.consume(t),completeAttributeValueUnquoted)}(t)}function completeAttributeValueQuoted(t){return t===s?(e.consume(t),s=null,completeAttributeValueQuotedAfter):null===t||(0,R.Ch)(t)?n(t):(e.consume(t),completeAttributeValueQuoted)}function completeAttributeValueQuotedAfter(e){return 47===e||62===e||(0,R.xz)(e)?completeAttributeNameBefore(e):n(e)}function completeEnd(t){return 62===t?(e.consume(t),completeAfter):n(t)}function completeAfter(t){return null===t||(0,R.Ch)(t)?continuation(t):(0,R.xz)(t)?(e.consume(t),completeAfter):n(t)}function continuation(t){return 45===t&&2===r?(e.consume(t),continuationCommentInside):60===t&&1===r?(e.consume(t),continuationRawTagOpen):62===t&&4===r?(e.consume(t),continuationClose):63===t&&3===r?(e.consume(t),continuationDeclarationInside):93===t&&5===r?(e.consume(t),continuationCdataInside):(0,R.Ch)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(ee,continuationAfter,continuationStart)(t)):null===t||(0,R.Ch)(t)?(e.exit("htmlFlowData"),continuationStart(t)):(e.consume(t),continuation)}function continuationStart(t){return e.check(et,continuationStartNonLazy,continuationAfter)(t)}function continuationStartNonLazy(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),continuationBefore}function continuationBefore(t){return null===t||(0,R.Ch)(t)?continuationStart(t):(e.enter("htmlFlowData"),continuation(t))}function continuationCommentInside(t){return 45===t?(e.consume(t),continuationDeclarationInside):continuation(t)}function continuationRawTagOpen(t){return 47===t?(e.consume(t),o="",continuationRawEndTag):continuation(t)}function continuationRawEndTag(t){if(62===t){let n=o.toLowerCase();return Z.includes(n)?(e.consume(t),continuationClose):continuation(t)}return(0,R.jv)(t)&&o.length<8?(e.consume(t),o+=String.fromCharCode(t),continuationRawEndTag):continuation(t)}function continuationCdataInside(t){return 93===t?(e.consume(t),continuationDeclarationInside):continuation(t)}function continuationDeclarationInside(t){return 62===t?(e.consume(t),continuationClose):45===t&&2===r?(e.consume(t),continuationDeclarationInside):continuation(t)}function continuationClose(t){return null===t||(0,R.Ch)(t)?(e.exit("htmlFlowData"),continuationAfter(t)):(e.consume(t),continuationClose)}function continuationAfter(n){return e.exit("htmlFlow"),t(n)}},resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},concrete:!0},61:$,95:G,96:er,126:er},eA={38:eo,92:ea},ek={[-5]:es,[-4]:es,[-3]:es,33:ep,38:eo,42:ed,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),open};function open(t){return(0,R.jv)(t)?(e.consume(t),schemeOrEmailAtext):64===t?n(t):emailAtext(t)}function schemeOrEmailAtext(t){return 43===t||45===t||46===t||(0,R.H$)(t)?(r=1,function schemeInsideOrEmailAtext(t){return 58===t?(e.consume(t),r=0,urlInside):(43===t||45===t||46===t||(0,R.H$)(t))&&r++<32?(e.consume(t),schemeInsideOrEmailAtext):(r=0,emailAtext(t))}(t)):emailAtext(t)}function urlInside(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,R.Av)(r)?n(r):(e.consume(r),urlInside)}function emailAtext(t){return 64===t?(e.consume(t),emailAtSignOrDot):(0,R.n9)(t)?(e.consume(t),emailAtext):n(t)}function emailAtSignOrDot(i){return(0,R.H$)(i)?function emailLabel(i){return 46===i?(e.consume(i),r=0,emailAtSignOrDot):62===i?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(i),e.exit("autolinkMarker"),e.exit("autolink"),t):function emailValue(t){if((45===t||(0,R.H$)(t))&&r++<63){let n=45===t?emailValue:emailLabel;return e.consume(t),n}return n(t)}(i)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,o;let a=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),open};function open(t){return 33===t?(e.consume(t),declarationOpen):47===t?(e.consume(t),tagCloseStart):63===t?(e.consume(t),instruction):(0,R.jv)(t)?(e.consume(t),tagOpen):n(t)}function declarationOpen(t){return 45===t?(e.consume(t),commentOpenInside):91===t?(e.consume(t),i=0,cdataOpenInside):(0,R.jv)(t)?(e.consume(t),declaration):n(t)}function commentOpenInside(t){return 45===t?(e.consume(t),commentEnd):n(t)}function comment(t){return null===t?n(t):45===t?(e.consume(t),commentClose):(0,R.Ch)(t)?(o=comment,lineEndingBefore(t)):(e.consume(t),comment)}function commentClose(t){return 45===t?(e.consume(t),commentEnd):comment(t)}function commentEnd(e){return 62===e?end(e):45===e?commentClose(e):comment(e)}function cdataOpenInside(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?cdata:cdataOpenInside):n(t)}function cdata(t){return null===t?n(t):93===t?(e.consume(t),cdataClose):(0,R.Ch)(t)?(o=cdata,lineEndingBefore(t)):(e.consume(t),cdata)}function cdataClose(t){return 93===t?(e.consume(t),cdataEnd):cdata(t)}function cdataEnd(t){return 62===t?end(t):93===t?(e.consume(t),cdataEnd):cdata(t)}function declaration(t){return null===t||62===t?end(t):(0,R.Ch)(t)?(o=declaration,lineEndingBefore(t)):(e.consume(t),declaration)}function instruction(t){return null===t?n(t):63===t?(e.consume(t),instructionClose):(0,R.Ch)(t)?(o=instruction,lineEndingBefore(t)):(e.consume(t),instruction)}function instructionClose(e){return 62===e?end(e):instruction(e)}function tagCloseStart(t){return(0,R.jv)(t)?(e.consume(t),tagClose):n(t)}function tagClose(t){return 45===t||(0,R.H$)(t)?(e.consume(t),tagClose):function tagCloseBetween(t){return(0,R.Ch)(t)?(o=tagCloseBetween,lineEndingBefore(t)):(0,R.xz)(t)?(e.consume(t),tagCloseBetween):end(t)}(t)}function tagOpen(t){return 45===t||(0,R.H$)(t)?(e.consume(t),tagOpen):47===t||62===t||(0,R.z3)(t)?tagOpenBetween(t):n(t)}function tagOpenBetween(t){return 47===t?(e.consume(t),end):58===t||95===t||(0,R.jv)(t)?(e.consume(t),tagOpenAttributeName):(0,R.Ch)(t)?(o=tagOpenBetween,lineEndingBefore(t)):(0,R.xz)(t)?(e.consume(t),tagOpenBetween):end(t)}function tagOpenAttributeName(t){return 45===t||46===t||58===t||95===t||(0,R.H$)(t)?(e.consume(t),tagOpenAttributeName):function tagOpenAttributeNameAfter(t){return 61===t?(e.consume(t),tagOpenAttributeValueBefore):(0,R.Ch)(t)?(o=tagOpenAttributeNameAfter,lineEndingBefore(t)):(0,R.xz)(t)?(e.consume(t),tagOpenAttributeNameAfter):tagOpenBetween(t)}(t)}function tagOpenAttributeValueBefore(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,tagOpenAttributeValueQuoted):(0,R.Ch)(t)?(o=tagOpenAttributeValueBefore,lineEndingBefore(t)):(0,R.xz)(t)?(e.consume(t),tagOpenAttributeValueBefore):(e.consume(t),tagOpenAttributeValueUnquoted)}function tagOpenAttributeValueQuoted(t){return t===r?(e.consume(t),r=void 0,tagOpenAttributeValueQuotedAfter):null===t?n(t):(0,R.Ch)(t)?(o=tagOpenAttributeValueQuoted,lineEndingBefore(t)):(e.consume(t),tagOpenAttributeValueQuoted)}function tagOpenAttributeValueUnquoted(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,R.z3)(t)?tagOpenBetween(t):(e.consume(t),tagOpenAttributeValueUnquoted)}function tagOpenAttributeValueQuotedAfter(e){return 47===e||62===e||(0,R.z3)(e)?tagOpenBetween(e):n(e)}function end(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function lineEndingBefore(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),lineEndingAfter}function lineEndingAfter(t){return(0,R.xz)(t)?(0,x.f)(e,lineEndingAfterPrefix,"linePrefix",a.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):lineEndingAfterPrefix(t)}function lineEndingAfterPrefix(t){return e.enter("htmlTextData"),o(t)}}}],91:em,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),after};function after(r){return(0,R.Ch)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},ea],93:el,95:ed,96:{name:"codeText",tokenize:function(e,t,n){let r,i,o=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function sequenceOpen(t){return 96===t?(e.consume(t),o++,sequenceOpen):(e.exit("codeTextSequence"),between(t))}(t)};function between(a){return null===a?n(a):32===a?(e.enter("space"),e.consume(a),e.exit("space"),between):96===a?(i=e.enter("codeTextSequence"),r=0,function sequenceClose(n){return 96===n?(e.consume(n),r++,sequenceClose):r===o?(e.exit("codeTextSequence"),e.exit("codeText"),t(n)):(i.type="codeTextData",data(n))}(a)):(0,R.Ch)(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),between):(e.enter("codeTextData"),data(a))}function data(t){return null===t||32===t||96===t||(0,R.Ch)(t)?(e.exit("codeTextData"),between(t)):(e.consume(t),data)}},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type}}},eC={null:[ed,F]},ey={null:[42,95]},eN={null:[]},eS=/[\0\t\n\r]/g;function decodeNumericCharacterReference(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let eI=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function decode(e,t,n){if(t)return t;let r=n.charCodeAt(0);if(35===r){let e=n.charCodeAt(1),t=120===e||88===e;return decodeNumericCharacterReference(n.slice(t?2:1),t?16:10)}return decodeNamedCharacterReference(n)||e}function lib_stringifyPosition(e){return e&&"object"==typeof e?"position"in e||"type"in e?unist_util_stringify_position_lib_position(e.position):"start"in e||"end"in e?unist_util_stringify_position_lib_position(e):"line"in e||"column"in e?unist_util_stringify_position_lib_point(e):"":""}function unist_util_stringify_position_lib_point(e){return lib_index(e&&e.line)+":"+lib_index(e&&e.column)}function unist_util_stringify_position_lib_position(e){return unist_util_stringify_position_lib_point(e&&e.start)+"-"+unist_util_stringify_position_lib_point(e&&e.end)}function lib_index(e){return e&&"number"==typeof e?e:1}let eb={}.hasOwnProperty;function mdast_util_from_markdown_lib_point(e){return{line:e.line,column:e.column,offset:e.offset}}function defaultOnError(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+lib_stringifyPosition({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+lib_stringifyPosition({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+lib_stringifyPosition({start:t.start,end:t.end})+") is still open")}function remarkParse(e){let t=this;t.parser=function(n){var i,o;let a,s,l,c;return"string"!=typeof(i={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(o=i,i=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:opener(link),autolinkProtocol:onenterdata,autolinkEmail:onenterdata,atxHeading:opener(heading),blockQuote:opener(function(){return{type:"blockquote",children:[]}}),characterEscape:onenterdata,characterReference:onenterdata,codeFenced:opener(codeFlow),codeFencedFenceInfo:buffer,codeFencedFenceMeta:buffer,codeIndented:opener(codeFlow,buffer),codeText:opener(function(){return{type:"inlineCode",value:""}},buffer),codeTextData:onenterdata,data:onenterdata,codeFlowValue:onenterdata,definition:opener(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:buffer,definitionLabelString:buffer,definitionTitleString:buffer,emphasis:opener(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:opener(hardBreak),hardBreakTrailing:opener(hardBreak),htmlFlow:opener(html,buffer),htmlFlowData:onenterdata,htmlText:opener(html,buffer),htmlTextData:onenterdata,image:opener(function(){return{type:"image",title:null,url:"",alt:null}}),label:buffer,link:opener(link),listItem:opener(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){if(this.data.expectingFirstListItemValue){let t=this.stack[this.stack.length-2];t.start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0}},listOrdered:opener(list,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:opener(list),paragraph:opener(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:buffer,resourceDestinationString:buffer,resourceTitleString:buffer,setextHeading:opener(heading),strong:opener(function(){return{type:"strong",children:[]}}),thematicBreak:opener(function(){return{type:"thematicBreak"}})},exit:{atxHeading:closer(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];if(!t.depth){let n=this.sliceSerialize(e).length;t.depth=n}},autolink:closer(),autolinkEmail:function(e){onexitdata.call(this,e);let t=this.stack[this.stack.length-1];t.url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){onexitdata.call(this,e);let t=this.stack[this.stack.length-1];t.url=this.sliceSerialize(e)},blockQuote:closer(),characterEscapeValue:onexitdata,characterReferenceMarkerHexadecimal:onexitcharacterreferencemarker,characterReferenceMarkerNumeric:onexitcharacterreferencemarker,characterReferenceValue:function(e){let t;let n=this.sliceSerialize(e),r=this.data.characterReferenceType;if(r)t=decodeNumericCharacterReference(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0;else{let e=decodeNamedCharacterReference(n);t=e}let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){let t=this.stack.pop();t.position.end=mdast_util_from_markdown_lib_point(e.end)},codeFenced:closer(function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.lang=e},codeFencedFenceMeta:function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.meta=e},codeFlowValue:onexitdata,codeIndented:closer(function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:closer(function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e}),codeTextData:onexitdata,data:onexitdata,definition:closer(),definitionDestinationString:function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,W.d)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.title=e},emphasis:closer(),hardBreakEscape:closer(onexithardbreak),hardBreakTrailing:closer(onexithardbreak),htmlFlow:closer(function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e}),htmlFlowData:onexitdata,htmlText:closer(function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.value=e}),htmlTextData:onexitdata,image:closer(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){let t=e.children;n.children=t}else n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(eI,decode),n.identifier=(0,W.d)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){let t=n.children[n.children.length-1];t.position.end=mdast_util_from_markdown_lib_point(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(onenterdata.call(this,e),onexitdata.call(this,e))},link:closer(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:closer(),listOrdered:closer(),listUnordered:closer(),paragraph:closer(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,W.d)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.url=e},resourceTitleString:function(){let e=this.resume(),t=this.stack[this.stack.length-1];t.title=e},resource:function(){this.data.inReference=void 0},setextHeading:closer(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){let t=this.stack[this.stack.length-1];t.depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:closer(),thematicBreak:closer()}};(function configure(e,t){let n=-1;for(;++n<t.length;){let r=t[n];Array.isArray(r)?configure(e,r):function(e,t){let n;for(n in t)if(eb.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(e,r)}})(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},i={stack:[r],tokenStack:[],config:t,enter,exit,buffer,resume,data:n},o=[],a=-1;for(;++a<e.length;)if("listOrdered"===e[a][1].type||"listUnordered"===e[a][1].type){if("enter"===e[a][0])o.push(a);else{let t=o.pop();a=function(e,t,n){let r,i,o,a,s=t-1,l=-1,c=!1;for(;++s<=n;){let t=e[s];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?l++:l--,a=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||a||l||o||(o=s),a=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:a=void 0}if(!l&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===l&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let a=s;for(i=void 0;a--;){let t=e[a];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",i=a}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}o&&(!i||o<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||s,0,["exit",r,t[2]]),s++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(s,0,["enter",i,t[2]]),s++,n++,o=void 0,a=!0}}}return e[t][1]._spread=c,n}(e,t,a)}}for(a=-1;++a<e.length;){let n=t[e[a][0]];eb.call(n,e[a][1].type)&&n[e[a][1].type].call(Object.assign({sliceSerialize:e[a][2].sliceSerialize},i),e[a][1])}if(i.tokenStack.length>0){let e=i.tokenStack[i.tokenStack.length-1],t=e[1]||defaultOnError;t.call(i,void 0,e[0])}for(r.position={start:mdast_util_from_markdown_lib_point(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:mdast_util_from_markdown_lib_point(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},a=-1;++a<t.transforms.length;)r=t.transforms[a](r)||r;return r};function opener(e,t){return function(n){enter.call(this,e(n),n),t&&t.call(this,n)}}function buffer(){this.stack.push({type:"fragment",children:[]})}function enter(e,t,n){let r=this.stack[this.stack.length-1],i=r.children;i.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:mdast_util_from_markdown_lib_point(t.start),end:void 0}}function closer(e){return function(t){e&&e.call(this,t),exit.call(this,t)}}function exit(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r){if(r[0].type!==e.type){if(t)t.call(this,e,r[0]);else{let t=r[1]||defaultOnError;t.call(this,e,r[0])}}}else throw Error("Cannot close `"+e.type+"` ("+lib_stringifyPosition({start:e.start,end:e.end})+"): it’s not open");n.position.end=mdast_util_from_markdown_lib_point(e.end)}function resume(){return(0,I.B)(this.stack.pop())}function onenterdata(e){let t=this.stack[this.stack.length-1],n=t.children,r=n[n.length-1];r&&"text"===r.type||((r={type:"text",value:""}).position={start:mdast_util_from_markdown_lib_point(e.start),end:void 0},n.push(r)),this.stack.push(r)}function onexitdata(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=mdast_util_from_markdown_lib_point(e.end)}function onexithardbreak(){this.data.atHardBreak=!0}function onexitcharacterreferencemarker(e){this.data.characterReferenceType=e.type}function codeFlow(){return{type:"code",lang:null,meta:null,value:""}}function heading(){return{type:"heading",depth:0,children:[]}}function hardBreak(){return{type:"break"}}function html(){return{type:"html",value:""}}function link(){return{type:"link",title:null,url:"",children:[]}}function list(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(o)(function(e){for(;!subtokenize(e););return e}((function(e){let t=(0,O.W)([r,...(e||{}).extensions||[]]),n={defined:[],lazy:{},constructs:t,content:create(v),document:create(L),flow:create(H),string:create(B),text:create(U)};return n;function create(e){return function(t){return function(e,t,n){let r=Object.assign(n?Object.assign({},n):{line:1,column:1,offset:0},{_index:0,_bufferIndex:-1}),i={},o=[],a=[],s=[],l={consume:function(e){(0,R.Ch)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,accountForPotentialSkip()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===a[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=now(),c.events.push(["enter",n,c]),s.push(n),n},exit:function(e){let t=s.pop();return t.end=now(),c.events.push(["exit",t,c]),t},attempt:constructFactory(function(e,t){addResult(e,t.from)}),check:constructFactory(onsuccessfulcheck),interrupt:constructFactory(onsuccessfulcheck,{interrupt:!0})},c={previous:null,code:null,containerState:{},events:[],parser:e,sliceStream,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let o;let a=e[r];if("string"==typeof a)o=a;else switch(a){case -5:o="\r";break;case -4:o="\n";break;case -3:o="\r\n";break;case -2:o=t?" ":"	";break;case -1:if(!t&&n)continue;o=" ";break;default:o=String.fromCharCode(a)}n=-2===a,i.push(o)}return i.join("")}(sliceStream(e),t)},now,defineSkip:function(e){i[e.line]=e.column,accountForPotentialSkip()},write:function(e){return(a=(0,b.V)(a,e),function(){let e;for(;r._index<a.length;){var t;let n=a[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;)t=n.charCodeAt(r._bufferIndex),u=u(t);else u=u(n)}}(),null!==a[a.length-1])?[]:(addResult(t,0),c.events=(0,z.C)(o,c.events,c),c.events)}},u=t.tokenize.call(c,l);return t.resolveAll&&o.push(t),c;function sliceStream(e){return function(e,t){let n;let r=t.start._index,i=t.start._bufferIndex,o=t.end._index,a=t.end._bufferIndex;if(r===o)n=[e[r].slice(i,a)];else{if(n=e.slice(r,o),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}a>0&&n.push(e[o].slice(0,a))}return n}(a,e)}function now(){let{line:e,column:t,offset:n,_index:i,_bufferIndex:o}=r;return{line:e,column:t,offset:n,_index:i,_bufferIndex:o}}function onsuccessfulcheck(e,t){t.restore()}function constructFactory(e,t){return function(n,i,o){let a,u,h,p;return Array.isArray(n)?handleListOfConstructs(n):"tokenize"in n?handleListOfConstructs([n]):function(e){let t=null!==e&&n[e],r=null!==e&&n.null,i=[...Array.isArray(t)?t:t?[t]:[],...Array.isArray(r)?r:r?[r]:[]];return handleListOfConstructs(i)(e)};function handleListOfConstructs(e){return(a=e,u=0,0===e.length)?o:handleConstruct(e[u])}function handleConstruct(e){return function(n){return(p=function(){let e=now(),t=c.previous,n=c.currentConstruct,i=c.events.length,o=Array.from(s);return{restore:function(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=i,s=o,accountForPotentialSkip()},from:i}}(),h=e,e.partial||(c.currentConstruct=e),e.name&&c.parser.constructs.disable.null.includes(e.name))?nok(n):e.tokenize.call(t?Object.assign(Object.create(c),t):c,l,ok,nok)(n)}}function ok(t){return e(h,p),i}function nok(e){return(p.restore(),++u<a.length)?handleConstruct(a[u]):o}}}function addResult(e,t){e.resolveAll&&!o.includes(e)&&o.push(e),e.resolve&&(0,b.d)(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function accountForPotentialSkip(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(n,e,t)}}})(o).document().write((s=1,l="",c=!0,function(e,t,n){let r,i,o,u,h;let p=[];for(e=l+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),o=0,l="",c&&(65279===e.charCodeAt(0)&&o++,c=void 0);o<e.length;){if(eS.lastIndex=o,u=(r=eS.exec(e))&&void 0!==r.index?r.index:e.length,h=e.charCodeAt(u),!r){l=e.slice(o);break}if(10===h&&o===u&&a)p.push(-3),a=void 0;else switch(a&&(p.push(-5),a=void 0),o<u&&(p.push(e.slice(o,u)),s+=u-o),h){case 0:p.push(65533),s++;break;case 9:for(i=4*Math.ceil(s/4),p.push(-2);s++<i;)p.push(-1);break;case 10:p.push(-4),s=1;break;default:a=!0,s=1}o=u+1}return n&&(a&&p.push(-5),l&&p.push(l),p.push(null)),p})(n,i,!0))))}}let eO="object"==typeof self?self:globalThis,deserializer=(e,t)=>{let as=(t,n)=>(e.set(n,t),t),unpair=n=>{if(e.has(n))return e.get(n);let[r,i]=t[n];switch(r){case 0:case -1:return as(i,n);case 1:{let e=as([],n);for(let t of i)e.push(unpair(t));return e}case 2:{let e=as({},n);for(let[t,n]of i)e[unpair(t)]=unpair(n);return e}case 3:return as(new Date(i),n);case 4:{let{source:e,flags:t}=i;return as(new RegExp(e,t),n)}case 5:{let e=as(new Map,n);for(let[t,n]of i)e.set(unpair(t),unpair(n));return e}case 6:{let e=as(new Set,n);for(let t of i)e.add(unpair(t));return e}case 7:{let{name:e,message:t}=i;return as(new eO[e](t),n)}case 8:return as(BigInt(i),n);case"BigInt":return as(Object(BigInt(i)),n)}return as(new eO[r](i),n)};return unpair},deserialize=e=>deserializer(new Map,e)(0),{toString:ex}={},{keys:eR}=Object,typeOf=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=ex.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},shouldSkip=([e,t])=>0===e&&("function"===t||"symbol"===t),serializer=(e,t,n,r)=>{let as=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},pair=r=>{if(n.has(r))return n.get(r);let[i,o]=typeOf(r);switch(i){case 0:{let t=r;switch(o){case"bigint":i=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+o);t=null;break;case"undefined":return as([-1],r)}return as([i,t],r)}case 1:{if(o)return as([o,[...r]],r);let e=[],t=as([i,e],r);for(let t of r)e.push(pair(t));return t}case 2:{if(o)switch(o){case"BigInt":return as([o,r.toString()],r);case"Boolean":case"Number":case"String":return as([o,r.valueOf()],r)}if(t&&"toJSON"in r)return pair(r.toJSON());let n=[],a=as([i,n],r);for(let t of eR(r))(e||!shouldSkip(typeOf(r[t])))&&n.push([pair(t),pair(r[t])]);return a}case 3:return as([i,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return as([i,{source:e,flags:t}],r)}case 5:{let t=[],n=as([i,t],r);for(let[n,i]of r)(e||!(shouldSkip(typeOf(n))||shouldSkip(typeOf(i))))&&t.push([pair(n),pair(i)]);return n}case 6:{let t=[],n=as([i,t],r);for(let n of r)(e||!shouldSkip(typeOf(n)))&&t.push(pair(n));return n}}let{message:a}=r;return as([i,{name:o,message:a}],r)};return pair},serialize=(e,{json:t,lossy:n}={})=>{let r=[];return serializer(!(t||n),!!t,new Map,r)(e),r};var ev="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?deserialize(serialize(e,t)):structuredClone(e):(e,t)=>deserialize(serialize(e,t));function normalizeUri(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let o=e.charCodeAt(n),a="";if(37===o&&(0,R.H$)(e.charCodeAt(n+1))&&(0,R.H$)(e.charCodeAt(n+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(a=String.fromCharCode(o));else if(o>55295&&o<57344){let t=e.charCodeAt(n+1);o<56320&&t>56319&&t<57344?(a=String.fromCharCode(o,t),i=1):a="�"}else a=String.fromCharCode(o);a&&(t.push(e.slice(r,n),encodeURIComponent(a)),r=n+i+1,a=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function defaultFootnoteBackContent(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function defaultFootnoteBackLabel(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}let convert=function(e){if(null==e)return ok;if("function"==typeof e)return castFactory(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=convert(e[n]);return castFactory(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):castFactory(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return castFactory(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function castFactory(e){return function(t,n,r){var i;return!!(null!==(i=t)&&"object"==typeof i&&"type"in i&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function ok(){return!0}let eL=[],eP=unist_util_position_lib_point("end"),eM=unist_util_position_lib_point("start");function unist_util_position_lib_point(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function revert(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),o=i[0];o&&"text"===o.type?o.value="["+o.value:i.unshift({type:"text",value:"["});let a=i[i.length-1];return a&&"text"===a.type?a.value+=r:i.push({type:"text",value:r}),i}function listItemLoose(e){let t=e.spread;return null==t?e.children.length>1:t}function trimLine(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let eD={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n;let r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),o=normalizeUri(i.toLowerCase()),a=e.footnoteOrder.indexOf(i),s=e.footnoteCounts.get(i);void 0===s?(s=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=a+1,s+=1,e.footnoteCounts.set(i,s);let l={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+o,id:r+"fnref-"+o+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,l);let c={type:"element",tagName:"sup",properties:{},children:[l]};return e.patch(t,c),e.applyData(t,c)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return revert(e,t);let i={src:normalizeUri(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,o),e.applyData(t,o)},image:function(e,t){let n={src:normalizeUri(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return revert(e,t);let i={href:normalizeUri(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)},link:function(e,t){let n={href:normalizeUri(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=listItemLoose(n[r])}return t}(n):listItemLoose(t),o={},a=[];if("boolean"==typeof t.checked){let e;let n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let s=-1;for(;++s<r.length;){let e=r[s];(i||0!==s||"element"!==e.type||"p"!==e.tagName)&&a.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?a.push(e):a.push(...e.children)}let l=r[r.length-1];l&&(i||"element"!==l.type||"p"!==l.tagName)&&a.push({type:"text",value:"\n"});let c={type:"element",tagName:"li",properties:o,children:a};return e.patch(t,c),e.applyData(t,c)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let o={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,o),e.applyData(t,o)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=eM(t.children[1]),a=eP(t.children[t.children.length-1]);o&&a&&(r.position={start:o,end:a}),i.push(r)}let o={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,o),e.applyData(t,o)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=r?r.indexOf(t):1,o=0===i?"th":"td",a=n&&"table"===n.type?n.align:void 0,s=a?a.length:t.children.length,l=-1,c=[];for(;++l<s;){let n=t.children[l],r={},i=a?a[l]:void 0;i&&(r.align=i);let s={type:"element",tagName:o,properties:r,children:[]};n&&(s.children=e.all(n),e.patch(n,s),s=e.applyData(n,s)),c.push(s)}let u={type:"element",tagName:"tr",properties:{},children:e.wrap(c,!0)};return e.patch(t,u),e.applyData(t,u)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,o=[];for(;r;)o.push(trimLine(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return o.push(trimLine(t.slice(i),i>0,!1)),o.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:ignore,yaml:ignore,definition:ignore,footnoteDefinition:ignore};function ignore(){}let ew={}.hasOwnProperty,eH={};function patch(e,t){e.position&&(t.position=function(e){let t=eM(e),n=eP(e);if(t&&n)return{start:t,end:n}}(e))}function applyData(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;if("string"==typeof t){if("element"===n.type)n.tagName=t;else{let e="children"in n?n.children:[n];n={type:"element",tagName:t,properties:{},children:e}}}"element"===n.type&&i&&Object.assign(n.properties,ev(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function defaultUnknownHandler(e,t){let n=t.data||{},r="value"in t&&!(ew.call(n,"hProperties")||ew.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function wrap(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function trimMarkdownSpaceStart(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function toHast(e,t){let n=function(e,t){var n,r;let i,o,a;let s=t||eH,l=new Map,c=new Map,u=new Map,h={...eD,...s.handlers},p={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=p.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=trimMarkdownSpaceStart(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=trimMarkdownSpaceStart(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData,definitionById:l,footnoteById:c,footnoteCounts:u,footnoteOrder:[],handlers:h,one:function(e,t){let n=e.type,r=p.handlers[n];if(ew.call(p.handlers,n)&&r)return r(p,e,t);if(p.options.passThrough&&p.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=ev(n);return r.children=p.all(e),r}return ev(e)}let i=p.options.unknownHandler||defaultUnknownHandler;return i(p,e,t)},options:s,patch,wrap};return"function"==typeof(n=function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?l:c,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}})&&"function"!=typeof r?(o=void 0,a=n,i=r):(o=n,a=r,i=void 0),function(e,t,n,r){let i;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):i=t;let o=convert(i),a=r?-1:1;(function factory(e,i,s){let l=e&&"object"==typeof e?e:{};if("string"==typeof l.type){let t="string"==typeof l.tagName?l.tagName:"string"==typeof l.name?l.name:void 0;Object.defineProperty(visit,"name",{value:"node ("+e.type+(t?"<"+t+">":"")+")"})}return visit;function visit(){var l;let c,u,h,p=eL;if((!t||o(e,i,s[s.length-1]||void 0))&&!1===(p=Array.isArray(l=n(e,s))?l:"number"==typeof l?[!0,l]:null==l?eL:[l])[0])return p;if("children"in e&&e.children&&e.children&&"skip"!==p[0])for(u=(r?e.children.length:-1)+a,h=s.concat(e);u>-1&&u<e.children.length;){let t=e.children[u];if(!1===(c=factory(t,u,h)())[0])return c;u="number"==typeof c[1]?c[1]:u+a}return p}})(e,void 0,[])()}(e,o,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return a(e,r,n)},i),p}(e,t),r=n.one(e,void 0),o=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||defaultFootnoteBackContent,r=e.options.footnoteBackLabel||defaultFootnoteBackLabel,i=e.options.footnoteLabel||"Footnotes",o=e.options.footnoteLabelTagName||"h2",a=e.options.footnoteLabelProperties||{className:["sr-only"]},s=[],l=-1;for(;++l<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[l]);if(!i)continue;let o=e.all(i),a=String(i.identifier).toUpperCase(),c=normalizeUri(a.toLowerCase()),u=0,h=[],p=e.footnoteCounts.get(a);for(;void 0!==p&&++u<=p;){h.length>0&&h.push({type:"text",value:" "});let e="string"==typeof n?n:n(l,u);"string"==typeof e&&(e={type:"text",value:e}),h.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+c+(u>1?"-"+u:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(l,u),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let f=o[o.length-1];if(f&&"element"===f.type&&"p"===f.tagName){let e=f.children[f.children.length-1];e&&"text"===e.type?e.value+=" ":f.children.push({type:"text",value:" "}),f.children.push(...h)}else o.push(...h);let d={type:"element",tagName:"li",properties:{id:t+"fn-"+c},children:e.wrap(o,!0)};e.patch(i,d),s.push(d)}if(0!==s.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:o,properties:{...ev(a),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(s,!0)},{type:"text",value:"\n"}]}}(n),a=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&((0,i.ok)("children"in a),a.children.push({type:"text",value:"\n"},o)),a}function remarkRehype(e,t){return e&&"run"in e?async function(n,r){let i=toHast(n,{file:r,...t});await e.run(i,r)}:function(n,r){return toHast(n,{file:r,...e||t})}}function bail(e){if(e)throw e}var eF=n(5125);function isPlainObject(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function node_modules_unist_util_stringify_position_lib_point(e){return unist_util_stringify_position_lib_index(e&&e.line)+":"+unist_util_stringify_position_lib_index(e&&e.column)}function node_modules_unist_util_stringify_position_lib_position(e){return node_modules_unist_util_stringify_position_lib_point(e&&e.start)+"-"+node_modules_unist_util_stringify_position_lib_point(e&&e.end)}function unist_util_stringify_position_lib_index(e){return e&&"number"==typeof e?e:1}let lib_VFileMessage=class lib_VFileMessage extends Error{constructor(e,t,n){var r;super(),"string"==typeof t&&(n=t,t=void 0);let i="",o={},a=!1;if(t&&(o="line"in t&&"column"in t?{place:t}:"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?i=e:!o.cause&&e&&(a=!0,i=e.message,o.cause=e),!o.ruleId&&!o.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?o.ruleId=n:(o.source=n.slice(0,e),o.ruleId=n.slice(e+1))}if(!o.place&&o.ancestors&&o.ancestors){let e=o.ancestors[o.ancestors.length-1];e&&(o.place=e.position)}let s=o.place&&"start"in o.place?o.place.start:o.place;this.ancestors=o.ancestors||void 0,this.cause=o.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=s?s.line:void 0,this.name=((r=o.place)&&"object"==typeof r?"position"in r||"type"in r?node_modules_unist_util_stringify_position_lib_position(r.position):"start"in r||"end"in r?node_modules_unist_util_stringify_position_lib_position(r):"line"in r||"column"in r?node_modules_unist_util_stringify_position_lib_point(r):"":"")||"1:1",this.place=o.place||void 0,this.reason=this.message,this.ruleId=o.ruleId||void 0,this.source=o.source||void 0,this.stack=a&&o.cause&&"string"==typeof o.cause.stack?o.cause.stack:"",this.actual,this.expected,this.note,this.url}};lib_VFileMessage.prototype.file="",lib_VFileMessage.prototype.name="",lib_VFileMessage.prototype.reason="",lib_VFileMessage.prototype.message="",lib_VFileMessage.prototype.stack="",lib_VFileMessage.prototype.column=void 0,lib_VFileMessage.prototype.line=void 0,lib_VFileMessage.prototype.ancestors=void 0,lib_VFileMessage.prototype.cause=void 0,lib_VFileMessage.prototype.fatal=void 0,lib_VFileMessage.prototype.place=void 0,lib_VFileMessage.prototype.ruleId=void 0,lib_VFileMessage.prototype.source=void 0;let eB={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');assertPath(e);let r=0,i=-1,o=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else i<0&&(n=!0,i=o+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let a=-1,s=t.length-1;for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else a<0&&(n=!0,a=o+1),s>-1&&(e.codePointAt(o)===t.codePointAt(s--)?s<0&&(i=o):(s=-1,i=a));return r===i?i=a:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(assertPath(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;assertPath(e);let n=e.length,r=-1,i=0,o=-1,a=0;for(;n--;){let s=e.codePointAt(n);if(47===s){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===s?o<0?o=n:1!==a&&(a=1):o>-1&&(a=-1)}return o<0||r<0||0===a||1===a&&o===r-1&&o===i+1?"":e.slice(o,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)assertPath(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){assertPath(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",o=0,a=-1,s=0,l=-1;for(;++l<=e.length;){if(l<e.length)n=e.codePointAt(l);else if(47===n)break;else n=47;if(47===n){if(a===l-1||1===s);else if(a!==l-1&&2===s){if(i.length<2||2!==o||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",o=0):o=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),a=l,s=0;continue}}else if(i.length>0){i="",o=0,a=l,s=0;continue}}t&&(i=i.length>0?i+"/..":"..",o=2)}else i.length>0?i+="/"+e.slice(a+1,l):i=e.slice(a+1,l),o=l-a-1;a=l,s=0}else 46===n&&s>-1?s++:s=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function assertPath(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let eU={cwd:function(){return"/"}};function isUrl(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let ez=["history","path","basename","stem","extname","dirname"];let VFile=class VFile{constructor(e){let t,n;t=e?isUrl(e)?{path:e}:"string"==typeof e||e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e?{value:e}:e:{},this.cwd="cwd"in t?"":eU.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<ez.length;){let e=ez[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)ez.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?eB.basename(this.path):void 0}set basename(e){assertNonEmpty(e,"basename"),assertPart(e,"basename"),this.path=eB.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?eB.dirname(this.path):void 0}set dirname(e){lib_assertPath(this.basename,"dirname"),this.path=eB.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?eB.extname(this.path):void 0}set extname(e){if(assertPart(e,"extname"),lib_assertPath(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=eB.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){isUrl(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!isUrl(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),assertNonEmpty(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?eB.basename(this.path,this.extname):void 0}set stem(e){assertNonEmpty(e,"stem"),assertPart(e,"stem"),this.path=eB.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new lib_VFileMessage(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){if(void 0===this.value)return"";if("string"==typeof this.value)return this.value;let t=new TextDecoder(e||void 0);return t.decode(this.value)}};function assertPart(e,t){if(e&&e.includes(eB.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+eB.sep+"`")}function assertNonEmpty(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function lib_assertPath(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let CallableInstance=function(e){let t=this.constructor,n=t.prototype,r=n[e],apply=function(){return r.apply(apply,arguments)};return Object.setPrototypeOf(apply,n),apply},eG={}.hasOwnProperty;let Processor=class Processor extends CallableInstance{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);(function next(i,...o){let a=e[++n],s=-1;if(i){r(i);return}for(;++s<t.length;)(null===o[s]||void 0===o[s])&&(o[s]=t[s]);t=o,a?(function(e,t){let n;return function(...t){let r;let i=e.length>t.length;i&&t.push(done);try{r=e.apply(this,t)}catch(e){if(i&&n)throw e;return done(e)}i||(r instanceof Promise?r.then(then,done):r instanceof Error?done(r):then(r))};function done(e,...r){n||(n=!0,t(e,...r))}function then(e){done(null,e)}})(a,next)(...o):r(null,...o)})(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new Processor,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(eF(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(assertUnfrozen("data",this.frozen),this.namespace[e]=t,this):eG.call(this.namespace,e)&&this.namespace[e]||void 0:e?(assertUnfrozen("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=vfile(e),n=this.parser||this.Parser;return assertParser("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),assertParser("process",this.parser||this.Parser),assertCompiler("process",this.compiler||this.Compiler),t?executor(void 0,t):new Promise(executor);function executor(r,o){let a=vfile(e),s=n.parse(a);function realDone(e,n){e||!n?o(e):r?r(n):((0,i.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(s,a,function(e,t,r){if(e||!t||!r)return realDone(e);let i=n.stringify(t,r);"string"==typeof i||i&&"object"==typeof i&&"byteLength"in i&&"byteOffset"in i?r.value=i:r.result=i,realDone(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),assertParser("processSync",this.parser||this.Parser),assertCompiler("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,bail(e),t=r}),assertDone("processSync","process",n),(0,i.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){assertNode(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?executor(void 0,n):new Promise(executor);function executor(o,a){(0,i.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let s=vfile(t);r.run(e,s,function(t,r,s){let l=r||e;t?a(t):o?o(l):((0,i.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,l,s))})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){bail(e),n=t,r=!0}),assertDone("runSync","run",r),(0,i.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=vfile(t),r=this.compiler||this.Compiler;return assertCompiler("stringify",r),assertNode(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(assertUnfrozen("use",this.frozen),null==e);else if("function"==typeof e)addPlugin(e,t);else if("object"==typeof e)Array.isArray(e)?addList(e):addPreset(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function addPreset(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");addList(e.plugins),e.settings&&(r.settings=eF(!0,r.settings,e.settings))}function addList(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){let n=e[t];!function(e){if("function"==typeof e)addPlugin(e,[]);else if("object"==typeof e){if(Array.isArray(e)){let[t,...n]=e;addPlugin(t,n)}else addPreset(e)}else throw TypeError("Expected usable value, not `"+e+"`")}(n)}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function addPlugin(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...o]=t,a=n[i][1];isPlainObject(a)&&isPlainObject(r)&&(r=eF(!0,a,r)),n[i]=[e,r,...o]}}}};let eK=new Processor().freeze();function assertParser(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function assertCompiler(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function assertUnfrozen(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function assertNode(e){if(!isPlainObject(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function assertDone(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function vfile(e){return e&&"object"==typeof e&&"message"in e&&"messages"in e?e:new VFile(e)}let lib_convert=function(e){if(null==e)return lib_ok;if("function"==typeof e)return lib_castFactory(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=lib_convert(e[n]);return lib_castFactory(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):lib_castFactory(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return lib_castFactory(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function lib_castFactory(e){return function(t,n,r){var i;return!!(null!==(i=t)&&"object"==typeof i&&"type"in i&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function lib_ok(){return!0}let ej=[],eV=[],eY={allowDangerousHtml:!0},eW=/^(https?|ircs?|mailto|xmpp)$/i,eQ=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function Markdown(e){var t,n,r;let o,a,s;let l=e.allowedElements,c=e.allowElement,h=e.children||"",p=e.className,f=e.components,d=e.disallowedElements,m=e.rehypePlugins||eV,g=e.remarkPlugins||eV,E=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...eY}:eY,_=e.skipHtml,A=e.unwrapDisallowed,k=e.urlTransform||defaultUrlTransform,C=eK().use(remarkParse).use(g).use(remarkRehype,E).use(m),y=new VFile;for(let t of("string"==typeof h?y.value=h:(0,i.t1)("Unexpected value `"+h+"` for `children` prop, expected `string`"),l&&d&&(0,i.t1)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),eQ))Object.hasOwn(e,t.from)&&(0,i.t1)("Unexpected `"+t.from+"` prop, "+(t.to?"use `"+t.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+t.id+"> for more info)");let I=C.parse(y),b=C.runSync(I,y);return p&&(b={type:"element",tagName:"div",properties:{className:p},children:"root"===b.type?b.children:[b]}),t=b,"function"==typeof(n=function(e,t,n){if("raw"===e.type&&n&&"number"==typeof t)return _?n.children.splice(t,1):n.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in N)if(Object.hasOwn(N,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=N[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=k(String(n||""),t,e))}}if("element"===e.type){let r=l?!l.includes(e.tagName):!!d&&d.includes(e.tagName);if(!r&&c&&"number"==typeof t&&(r=!c(e,t,n)),r&&n&&"number"==typeof t)return A&&e.children?n.children.splice(t,1,...e.children):n.children.splice(t,1),t}})&&"function"!=typeof r?(a=void 0,s=n,o=r):(a=n,s=r,o=void 0),function(e,t,n,r){let i;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):i=t;let o=lib_convert(i),a=r?-1:1;(function factory(e,i,s){let l=e&&"object"==typeof e?e:{};if("string"==typeof l.type){let t="string"==typeof l.tagName?l.tagName:"string"==typeof l.name?l.name:void 0;Object.defineProperty(visit,"name",{value:"node ("+e.type+(t?"<"+t+">":"")+")"})}return visit;function visit(){var l;let c,u,h,p=ej;if((!t||o(e,i,s[s.length-1]||void 0))&&!1===(p=Array.isArray(l=n(e,s))?l:"number"==typeof l?[!0,l]:null==l?ej:[l])[0])return p;if("children"in e&&e.children&&e.children&&"skip"!==p[0])for(u=(r?e.children.length:-1)+a,h=s.concat(e);u>-1&&u<e.children.length;){let t=e.children[u];if(!1===(c=factory(t,u,h)())[0])return c;u="number"==typeof c[1]?c[1]:u+a}return p}})(e,void 0,[])()}(t,a,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return s(e,r,n)},o),function(e,t){var n,r,i;let o;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let a=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=t.jsxDEV,o=function(e,t,r,i){let o=Array.isArray(r.children),s=T(e);return n(t,r,i,o,{columnNumber:s?s.column-1:void 0,fileName:a,lineNumber:s?s.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");r=t.jsx,i=t.jsxs,o=function(e,t,n,o){let a=Array.isArray(n.children),s=a?i:r;return o?s(t,n,o):s(t,n)}}let s={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:o,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:a,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?u.YP:u.dy,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},l=one(s,e,void 0);return l&&"string"!=typeof l?l:s.create(e,s.Fragment,{children:l||void 0},void 0)}(b,{Fragment:S.Fragment,components:f,ignoreInvalidStyle:!0,jsx:S.jsx,jsxs:S.jsxs,passKeys:!0,passNode:!0})}function defaultUrlTransform(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return t<0||i>-1&&t>i||n>-1&&t>n||r>-1&&t>r||eW.test(e.slice(0,t))?e:""}},9663:function(e,t,n){"use strict";n.d(t,{Z:function(){return rehypeRaw}});var r=n(7045);let i=point("start"),o=point("end");function point(e){return function(t){let n=t&&t.position&&t.position[e]||{};return{line:n.line||null,column:n.column||null,offset:n.offset>-1?n.offset:null}}}let convert=function(e){if(null==e)return ok;if("string"==typeof e)return castFactory(function(t){return t&&t.type===e});if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=convert(e[n]);return castFactory(function(...e){let n=-1;for(;++n<t.length;)if(t[n].call(this,...e))return!0;return!1})}(e):castFactory(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("function"==typeof e)return castFactory(e);throw Error("Expected function, string, or object as test")};function castFactory(e){return function(...t){return!!e.call(this,...t)}}function ok(){return!0}let visitParents=function(e,t,n,r){"function"==typeof t&&"function"!=typeof n&&(r=n,n=t,t=null);let i=convert(t),o=r?-1:1;(function factory(e,a,s){let l;let c="object"==typeof e&&null!==e?e:{};return"string"==typeof c.type&&(l="string"==typeof c.tagName?c.tagName:"string"==typeof c.name?c.name:void 0,Object.defineProperty(visit,"name",{value:"node ("+c.type+(l?"<"+l+">":"")+")"})),visit;function visit(){var l;let c,u,h,p=[];if((!t||i(e,a,s[s.length-1]||null))&&!1===(p=Array.isArray(l=n(e,s))?l:"number"==typeof l?[!0,l]:[l])[0])return p;if(e.children&&"skip"!==p[0])for(u=(r?e.children.length:-1)+o,h=s.concat(e);u>-1&&u<e.children.length;){if(!1===(c=factory(e.children[u],u,h)())[0])return c;u="number"==typeof c[1]?c[1]:u+o}return p}})(e,null,[])()},visit=function(e,t,n,r){"function"==typeof t&&"function"!=typeof n&&(r=n,n=t,t=null),visitParents(e,t,function(e,t){let r=t[t.length-1];return n(e,r?r.children.indexOf(e):null,r)},r)};var a=n(1634),s=n(1588),l=n(3859),c=/[#.]/g;let parseSelector=function(e,t="div"){for(var n,r,i,o=e||"",a={},s=0;s<o.length;)c.lastIndex=s,i=c.exec(o),(n=o.slice(s,i?i.index:o.length))&&(r?"#"===r?a.id=n:Array.isArray(a.className)?a.className.push(n):a.className=[n]:t=n,s+=n.length),i&&(r=i[0],s++);return{type:"element",tagName:t,properties:a,children:[]}};var u=n(342),h=n(5668);let p=new Set(["menu","submit","reset","button"]),f={}.hasOwnProperty;function core(e,t,n){let r=n&&function(e){let t={},n=-1;for(;++n<e.length;)t[e[n].toLowerCase()]=e[n];return t}(n);return function(n,i,...o){let a,l=-1;if(null==n)a={type:"root",children:[]},o.unshift(i);else{var c;if((a=parseSelector(n,t)).tagName=a.tagName.toLowerCase(),r&&f.call(r,a.tagName)&&(a.tagName=r[a.tagName]),c=a.tagName,!(null==i||"object"!=typeof i||Array.isArray(i))&&("input"===c||!i.type||"string"!=typeof i.type||!("children"in i&&Array.isArray(i.children))&&("button"===c?p.has(i.type.toLowerCase()):!("value"in i)))){let t;for(t in i)f.call(i,t)&&function(e,t,n,r){let i;let o=(0,s.s)(e,n),a=-1;if(null!=r){if("number"==typeof r){if(Number.isNaN(r))return;i=r}else i="boolean"==typeof r?r:"string"==typeof r?o.spaceSeparated?(0,u.Q)(r):o.commaSeparated?(0,h.Q)(r):o.commaOrSpaceSeparated?(0,u.Q)((0,h.Q)(r).join(" ")):parsePrimitive(o,o.property,r):Array.isArray(r)?r.concat():"style"===o.property?function(e){let t;let n=[];for(t in e)f.call(e,t)&&n.push([t,e[t]].join(": "));return n.join("; ")}(r):String(r);if(Array.isArray(i)){let e=[];for(;++a<i.length;)e[a]=parsePrimitive(o,o.property,i[a]);i=e}"className"===o.property&&Array.isArray(t.className)&&(i=t.className.concat(i)),t[o.property]=i}}(e,a.properties,t,i[t])}else o.unshift(i)}for(;++l<o.length;)!function addChild(e,t){let n=-1;if(null==t);else if("string"==typeof t||"number"==typeof t)e.push({type:"text",value:String(t)});else if(Array.isArray(t))for(;++n<t.length;)addChild(e,t[n]);else if("object"==typeof t&&"type"in t)"root"===t.type?addChild(e,t.children):e.push(t);else throw Error("Expected node, nodes, or string, got `"+t+"`")}(a.children,o[l]);return"element"===a.type&&"template"===a.tagName&&(a.content={type:"root",children:a.children},a.children=[]),a}}function parsePrimitive(e,t,n){if("string"==typeof n){if(e.number&&n&&!Number.isNaN(Number(n)))return Number(n);if((e.boolean||e.overloadedBoolean)&&(""===n||(0,l.F)(n)===(0,l.F)(t)))return!0}return n}let d=core(a.YP,"g",["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","solidColor","textArea","textPath"]),m=core(a.dy,"div"),T={html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},g={}.hasOwnProperty,E={"#document":root,"#document-fragment":root,"#text":function(e,t){return{type:"text",value:t.value}},"#comment":function(e,t){return{type:"comment",value:t.data}},"#documentType":function(){return{type:"doctype"}}};function transform(e,t){let n;let r=e.schema,i=g.call(E,t.nodeName)?E[t.nodeName]:lib_element;"tagName"in t&&(e.schema=t.namespaceURI===T.svg?a.YP:a.dy),"childNodes"in t&&(n=function(e,t){let n=-1,r=[];for(;++n<t.length;)r[n]=transform(e,t[n]);return r}(e,t.childNodes));let o=i(e,t,n);if("sourceCodeLocation"in t&&t.sourceCodeLocation&&e.file){let n=function(e,t,n){let r=lib_position(n);if("element"===t.type){let i=t.children[t.children.length-1];if(r&&!n.endTag&&i&&i.position&&i.position.end&&(r.end=Object.assign({},i.position.end)),e.verbose){let r;let i={};for(r in n.attrs)g.call(n.attrs,r)&&(i[(0,s.s)(e.schema,r).property]=lib_position(n.attrs[r]));t.data={position:{opening:lib_position(n.startTag),closing:n.endTag?lib_position(n.endTag):null,properties:i}}}}return r}(e,o,t.sourceCodeLocation);n&&(e.location=!0,o.position=n)}return e.schema=r,o}function root(e,t,n){let r={type:"root",children:n,data:{quirksMode:"quirks"===t.mode||"limited-quirks"===t.mode}};if(e.file&&e.location){let t=String(e.file),n=function(e){for(var t=String(e),n=[],r=/\r?\n|\r/g;r.test(t);)n.push(r.lastIndex);return n.push(t.length+1),{toPoint:function(e){var t=-1;if(e>-1&&e<n[n.length-1]){for(;++t<n.length;)if(n[t]>e)return{line:t+1,column:e-(n[t-1]||0)+1,offset:e}}return{line:void 0,column:void 0,offset:void 0}},toOffset:function(e){var t,r=e&&e.line,i=e&&e.column;return"number"==typeof r&&"number"==typeof i&&!Number.isNaN(r)&&!Number.isNaN(i)&&r-1 in n&&(t=(n[r-2]||0)+i-1||0),t>-1&&t<n[n.length-1]?t:-1}}}(t);r.position={start:n.toPoint(0),end:n.toPoint(t.length)}}return r}function lib_element(e,t,n){let r="svg"===e.schema.space?d:m,i=-1,o={};for(;++i<t.attrs.length;){let e=t.attrs[i];o[(e.prefix?e.prefix+":":"")+e.name]=e.value}let a=r(t.tagName,o,n);if("template"===a.tagName&&"content"in t){let n=t.sourceCodeLocation,r=n&&n.startTag&&lib_position(n.startTag),i=n&&n.endTag&&lib_position(n.endTag),o=transform(e,t.content);r&&i&&e.file&&(o.position={start:r.end,end:i.start}),a.content=o}return a}function lib_position(e){let t=lib_point({line:e.startLine,column:e.startCol,offset:e.startOffset}),n=lib_point({line:e.endLine,column:e.endCol,offset:e.endOffset});return t||n?{start:t,end:n}:null}function lib_point(e){return e.line&&e.column?e:null}var _=n(3880),A=n(7848);let k=_.D,C={}.hasOwnProperty,y=convert("root"),N=convert("element"),S=convert("text");var I={}.hasOwnProperty;function zwitch(e,t){var n=t||{};function one(t){var n=one.invalid,r=one.handlers;if(t&&I.call(t,e)&&(n=I.call(r,t[e])?r[t[e]]:one.unknown),n)return n.apply(this,arguments)}return one.handlers=n.handlers||{},one.invalid=n.invalid,one.unknown=n.unknown,one}var b={}.hasOwnProperty,O=zwitch("type",{handlers:{root:function(e,t){var n={nodeName:"#document",mode:(e.data||{}).quirksMode?"quirks":"no-quirks",childNodes:[]};return n.childNodes=lib_all(e.children,n,t),patch(e,n)},element:function(e,t){var n=t.space;return function(e,t,n){let r,i;if("function"!=typeof e)throw TypeError("h is not a function");let o=function(e){let t=e("div",{});return!!(t&&("_owner"in t||"_store"in t)&&(void 0===t.key||null===t.key))}(e),l=function(e){let t=e("div",{});return!!(t&&t.context&&t.context._isVue)}(e),c=function(e){let t=e("div",{});return"VirtualNode"===t.type}(e);if("string"==typeof n||"boolean"==typeof n?(r=n,n={}):(n||(n={}),r=n.prefix),y(t))i=1===t.children.length&&N(t.children[0])?t.children[0]:{type:"element",tagName:"div",properties:{},children:t.children};else if(N(t))i=t;else throw Error("Expected root or element, not `"+(t&&t.type||t)+"`");return function hast_to_hyperscript_transform(e,t,n){let r;let i=n.schema,o=i,l=t.tagName,c={},p=[],f=-1;for(r in"html"===i.space&&"svg"===l.toLowerCase()&&(o=a.YP,n.schema=o),t.properties)t.properties&&C.call(t.properties,r)&&function(e,t,n,r,i){let o;let a=(0,s.s)(r.schema,t);null==n||"number"==typeof n&&Number.isNaN(n)||!1===n&&(r.vue||r.vdom||r.hyperscript)||!n&&a.boolean&&(r.vue||r.vdom||r.hyperscript)||(Array.isArray(n)&&(n=a.commaSeparated?(0,h.P)(n):(0,u.P)(n)),a.boolean&&r.hyperscript&&(n=""),"style"===a.property&&"string"==typeof n&&(r.react||r.vue||r.vdom)&&(n=function(e,t){let n={};try{A(e,(e,t)=>{"-ms-"===e.slice(0,4)&&(e="ms-"+e.slice(4)),n[e.replace(/-([a-z])/g,(e,t)=>t.toUpperCase())]=t})}catch(e){throw e.message=t+"[style]"+e.message.slice(9),e}return n}(n,i)),r.vue?"style"!==a.property&&(o="attrs"):!a.mustUseProperty&&(r.vdom?"style"!==a.property&&(o="attributes"):r.hyperscript&&(o="attrs")),o?e[o]=Object.assign(e[o]||{},{[a.attribute]:n}):a.space&&r.react?e[k[a.property]||a.property]=n:e[a.attribute]=n)}(c,r,t.properties[r],n,l);if(n.vdom&&("html"===o.space?l=l.toUpperCase():o.space&&(c.namespace=T[o.space])),n.prefix&&(n.key++,c.key=n.prefix+n.key),t.children)for(;++f<t.children.length;){let r=t.children[f];N(r)?p.push(hast_to_hyperscript_transform(e,r,n)):S(r)&&p.push(r.value)}return n.schema=i,p.length>0?e.call(t,l,c,p):e.call(t,l,c)}(e,i,{schema:"svg"===n.space?a.YP:a.dy,prefix:null==r?o||l||c?"h-":null:"string"==typeof r?r:r?"h-":null,key:0,react:o,vue:l,vdom:c,hyperscript:"context"in e&&"cleanup"in e})}(function(n,r){var i,o,l,c,u,h,p,f,d=[];for(h in r)b.call(r,h)&&!1!==r[h]&&(!(c=(0,s.s)(t,h)).boolean||r[h])&&(u={name:h,value:!0===r[h]?"":String(r[h])},c.space&&"html"!==c.space&&"svg"!==c.space&&((p=h.indexOf(":"))<0?u.prefix="":(u.name=h.slice(p+1),u.prefix=h.slice(0,p)),u.namespace=T[c.space]),d.push(u));return"html"===t.space&&"svg"===e.tagName&&(t=a.YP),(f=patch(e,{nodeName:n,tagName:n,attrs:d,namespaceURI:T[t.space],childNodes:[],parentNode:void 0})).childNodes=lib_all(e.children,f,t),"template"===n&&(f.content=(i=e.content,o=t,(l={nodeName:"#document-fragment",childNodes:[]}).childNodes=lib_all(i.children,l,o),patch(i,l))),f},Object.assign({},e,{children:[]}),{space:n})},text:function(e){return patch(e,{nodeName:"#text",value:e.value,parentNode:void 0})},comment:function(e){return patch(e,{nodeName:"#comment",data:e.value,parentNode:void 0})},doctype:function(e){return patch(e,{nodeName:"#documentType",name:"html",publicId:"",systemId:"",parentNode:void 0})}}});function lib_all(e,t,n){var r,i=-1,o=[];if(e)for(;++i<e.length;)(r=O(e[i],n)).parentNode=t,o.push(r);return o}function patch(e,t){var n=e.position;return n&&n.start&&n.end&&(t.sourceCodeLocation={startLine:n.start.line,startCol:n.start.column,startOffset:n.start.offset,endLine:n.end.line,endCol:n.end.column,endOffset:n.end.offset}),t}let x=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","isindex","keygen","link","menuitem","meta","nextid","param","source","track","wbr"],R={sourceCodeLocationInfo:!0,scriptingEnabled:!1},raw=function(e,t,n){var o;let s,l,c,u,h,p=-1,f=new r(R),d=zwitch("type",{handlers:{root:function(e){all(e.children)},element:function(e){resetTokenizer(),f._processToken(function(e){let t=Object.assign(createParse5Location(e));return t.startTag=Object.assign({},t),{type:"START_TAG_TOKEN",tagName:e.tagName,selfClosing:!1,attrs:O({tagName:e.tagName,type:"element",properties:e.properties,children:[]},a.dy).attrs,location:t}}(e),T.html),all(e.children),x.includes(e.tagName)||(resetTokenizer(),f._processToken(function(e){let t=Object.assign(createParse5Location(e));return t.startTag=Object.assign({},t),{type:"END_TAG_TOKEN",tagName:e.tagName,attrs:[],location:t}}(e)))},text:function(e){resetTokenizer(),f._processToken({type:"CHARACTER_TOKEN",chars:e.value,location:createParse5Location(e)})},comment,doctype:function(e){resetTokenizer(),f._processToken({type:"DOCTYPE_TOKEN",name:"html",forceQuirks:!1,publicId:"",systemId:"",location:createParse5Location(e)})},raw:function(e){let t=i(e),n=t.line||1,r=t.column||1,o=t.offset||0;if(!c)throw Error("Expected `preprocessor`");if(!l)throw Error("Expected `tokenizer`");if(!u)throw Error("Expected `posTracker`");if(!h)throw Error("Expected `locationTracker`");c.html=void 0,c.pos=-1,c.lastGapPos=-1,c.lastCharPos=-1,c.gapStack=[],c.skipNextNewLine=!1,c.lastChunkWritten=!1,c.endOfChunkHit=!1,u.isEol=!1,u.lineStartPos=-r+1,u.droppedBufferSize=o,u.offset=0,u.col=1,u.line=n,h.currentAttrLocation=void 0,h.ctLoc=createParse5Location(e),l.write(e.value),f._runParsingLoop(null),("NAMED_CHARACTER_REFERENCE_STATE"===l.state||"NUMERIC_CHARACTER_REFERENCE_END_STATE"===l.state)&&(c.lastChunkWritten=!0,l[l.state](l._consume()))}},unknown});if((o=t)&&!("message"in o&&"messages"in o)&&(n=t,t=void 0),n&&n.passThrough)for(;++p<n.passThrough.length;)d.handlers[n.passThrough[p]]=stitch;let m=function(e,t={}){let n,r;return"messages"in t?(r=t,n={}):(r=t.file,n=t),transform({schema:"svg"===n.space?a.YP:a.dy,file:r,verbose:n.verbose,location:!1},e)}(!function(e){let t="root"===e.type?e.children[0]:e;return!!(t&&("doctype"===t.type||"element"===t.type&&"html"===t.tagName))}(e)?function(){let t={nodeName:"template",tagName:"template",attrs:[],namespaceURI:T.html,childNodes:[]},n={nodeName:"documentmock",tagName:"documentmock",attrs:[],namespaceURI:T.html,childNodes:[]},r={nodeName:"#document-fragment",childNodes:[]};if(f._bootstrap(n,t),f._pushTmplInsertionMode("IN_TEMPLATE_MODE"),f._initTokenizerForFragmentParsing(),f._insertFakeRootElement(),f._resetInsertionMode(),f._findFormInFragmentContext(),!(l=f.tokenizer))throw Error("Expected `tokenizer`");return c=l.preprocessor,u=(h=l.__mixins[0]).posTracker,d(e),resetTokenizer(),f._adoptNodes(n.childNodes[0],r),r}():function(){let t=f.treeAdapter.createDocument();if(f._bootstrap(t,void 0),!(l=f.tokenizer))throw Error("Expected `tokenizer`");return c=l.preprocessor,u=(h=l.__mixins[0]).posTracker,d(e),resetTokenizer(),t}(),t);if(s&&visit(m,"comment",(e,t,n)=>{if(e.value.stitch&&null!==n&&null!==t)return n.children[t]=e.value.stitch,t}),"root"!==e.type&&"root"===m.type&&1===m.children.length)return m.children[0];return m;function all(e){let t=-1;if(e)for(;++t<e.length;)d(e[t])}function comment(e){resetTokenizer(),f._processToken({type:"COMMENT_TOKEN",data:e.value,location:createParse5Location(e)})}function stitch(e){s=!0,comment({type:"comment",value:{stitch:"children"in e?{...e,children:raw({type:"root",children:e.children},t,n).children}:{...e}}})}function resetTokenizer(){if(!l)throw Error("Expected `tokenizer`");if(!u)throw Error("Expected `posTracker`");let e=l.currentCharacterToken;e&&(e.location.endLine=u.line,e.location.endCol=u.col+1,e.location.endOffset=u.offset+1,f._processToken(e)),l.tokenQueue=[],l.state="DATA_STATE",l.returnState="",l.charRefCode=-1,l.tempBuff=[],l.lastStartTagName="",l.consumedAfterSnapshot=-1,l.active=!1,l.currentCharacterToken=void 0,l.currentToken=void 0,l.currentAttr=void 0}};function unknown(e){throw Error("Cannot compile `"+e.type+"` node")}function createParse5Location(e){let t=i(e),n=o(e);return{startLine:t.line,startCol:t.column,startOffset:t.offset,endLine:n.line,endCol:n.column,endOffset:n.offset}}function rehypeRaw(e={}){return(t,n)=>{let r=raw(t,n,e);return r}}},9562:function(e,t,n){"use strict";function ccount(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{Z:function(){return remarkGfm}});var r=n(4345),i=n(5459);let convert=function(e){if(null==e)return ok;if("function"==typeof e)return castFactory(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=convert(e[n]);return castFactory(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):castFactory(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return castFactory(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function castFactory(e){return function(t,n,r){var i;return!!(null!==(i=t)&&"object"==typeof i&&"type"in i&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function ok(){return!0}let o=[],a="phrasing",s=["autolink","link","image","label"];function enterLiteralAutolink(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function enterLiteralAutolinkValue(e){this.config.enter.autolinkProtocol.call(this,e)}function exitLiteralAutolinkHttp(e){this.config.exit.autolinkProtocol.call(this,e)}function exitLiteralAutolinkWww(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,r.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function exitLiteralAutolinkEmail(e){this.config.exit.autolinkEmail.call(this,e)}function exitLiteralAutolink(e){this.exit(e)}function transformGfmAutolinkLiterals(e){!function(e,t,n){let r=convert((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),a=-1;for(;++a<i.length;)!function(e,t,n,r){let i;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):i=t;let a=convert(i),s=r?-1:1;(function factory(e,i,l){let c=e&&"object"==typeof e?e:{};if("string"==typeof c.type){let t="string"==typeof c.tagName?c.tagName:"string"==typeof c.name?c.name:void 0;Object.defineProperty(visit,"name",{value:"node ("+e.type+(t?"<"+t+">":"")+")"})}return visit;function visit(){var c;let u,h,p,f=o;if((!t||a(e,i,l[l.length-1]||void 0))&&!1===(f=Array.isArray(c=n(e,l))?c:"number"==typeof c?[!0,c]:null==c?o:[c])[0])return f;if("children"in e&&e.children&&e.children&&"skip"!==f[0])for(h=(r?e.children.length:-1)+s,p=l.concat(e);h>-1&&h<e.children.length;){let t=e.children[h];if(!1===(u=factory(t,h,p)())[0])return u;h="number"==typeof u[1]?u[1]:h+s}return f}})(e,void 0,[])()}(e,"text",visitor);function visitor(e,t){let n,o=-1;for(;++o<t.length;){let e=t[o],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[a][0],o=i[a][1],s=0,l=n.children,c=l.indexOf(e),u=!1,h=[];r.lastIndex=0;let p=r.exec(e.value);for(;p;){let n=p.index,i={index:p.index,input:p.input,stack:[...t,e]},a=o(...p,i);if("string"==typeof a&&(a=a.length>0?{type:"text",value:a}:void 0),!1===a?r.lastIndex=n+1:(s!==n&&h.push({type:"text",value:e.value.slice(s,n)}),Array.isArray(a)?h.push(...a):a&&h.push(a),s=n+p[0].length,u=!0),!r.global)break;p=r.exec(e.value)}return u?(s<e.value.length&&h.push({type:"text",value:e.value.slice(s)}),n.children.splice(c,1,...h)):h=[e],c+h.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,findUrl],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,findEmail]],{ignore:["link","linkReference"]})}function findUrl(e,t,n,r,i){let o="";if(!previous(i)||(/^w/i.test(t)&&(n=t+n,t="",o="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let a=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")"),i=ccount(e,"("),o=ccount(e,")");for(;-1!==r&&i>o;)e+=n.slice(0,r+1),r=(n=n.slice(r+1)).indexOf(")"),o++;return[e,n]}(n+r);if(!a[0])return!1;let s={type:"link",title:null,url:o+t+a[0],children:[{type:"text",value:t+a[0]}]};return a[1]?[s,{type:"text",value:a[1]}]:s}function findEmail(e,t,n,r){return!(!previous(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function previous(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,i.B8)(n)||(0,i.Xh)(n))&&(!t||47!==n)}var l=n(1098);function enterFootnoteDefinition(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function enterFootnoteDefinitionLabelString(){this.buffer()}function exitFootnoteDefinitionLabelString(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,r.ok)("footnoteDefinition"===n.type),n.label=t,n.identifier=(0,l.d)(this.sliceSerialize(e)).toLowerCase()}function exitFootnoteDefinition(e){this.exit(e)}function enterFootnoteCall(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function enterFootnoteCallString(){this.buffer()}function exitFootnoteCallString(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,r.ok)("footnoteReference"===n.type),n.label=t,n.identifier=(0,l.d)(this.sliceSerialize(e)).toLowerCase()}function exitFootnoteCall(e){this.exit(e)}function footnoteReference(e,t,n,r){let i=n.createTracker(r),o=i.move("[^"),a=n.enter("footnoteReference"),s=n.enter("reference");return o+=i.move(n.safe(n.associationId(e),{...i.current(),before:o,after:"]"})),s(),a(),o+=i.move("]")}function footnoteDefinition(e,t,n,r){let i=n.createTracker(r),o=i.move("[^"),a=n.enter("footnoteDefinition"),s=n.enter("label");return o+=i.move(n.safe(n.associationId(e),{...i.current(),before:o,after:"]"})),s(),o+=i.move("]:"+(e.children&&e.children.length>0?" ":"")),i.shift(4),o+=i.move(n.indentLines(n.containerFlow(e,i.current()),map)),a(),o}function map(e,t,n){return 0===t?e:(n?"":"    ")+e}footnoteReference.peek=function(){return"["};let c=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function enterStrikethrough(e){this.enter({type:"delete",children:[]},e)}function exitStrikethrough(e){this.exit(e)}function handleDelete(e,t,n,r){let i=n.createTracker(r),o=n.enter("strikethrough"),a=i.move("~~");return a+=n.containerPhrasing(e,{...i.current(),before:a,after:"~"})+i.move("~~"),o(),a}function defaultStringLength(e){return e.length}function toAlignment(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}function blockquote_map(e,t,n){return">"+(n?"":" ")+e}function listInScope(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function hardBreak(e,t,n,r){let i=-1;for(;++i<n.unsafe.length;){var o,a;if("\n"===n.unsafe[i].character&&listInScope(o=n.stack,(a=n.unsafe[i]).inConstruct,!0)&&!listInScope(o,a.notInConstruct,!1))return/[ \t]/.test(r.before)?"":" "}return"\\\n"}function code_map(e,t,n){return(n?"":"    ")+e}function checkQuote(e){let t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function encodeCharacterReference(e){return"&#x"+e.toString(16).toUpperCase()+";"}handleDelete.peek=function(){return"~"};var u=n(2987);function encodeInfo(e,t,n){let r=(0,u.r)(e),i=(0,u.r)(t);return void 0===r?void 0===i?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function emphasis(e,t,n,r){let i=function(e){let t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),o=n.enter("emphasis"),a=n.createTracker(r),s=a.move(i),l=a.move(n.containerPhrasing(e,{after:i,before:s,...a.current()})),c=l.charCodeAt(0),u=encodeInfo(r.before.charCodeAt(r.before.length-1),c,i);u.inside&&(l=encodeCharacterReference(c)+l.slice(1));let h=l.charCodeAt(l.length-1),p=encodeInfo(r.after.charCodeAt(0),h,i);p.inside&&(l=l.slice(0,-1)+encodeCharacterReference(h));let f=a.move(i);return o(),n.attentionEncodeSurroundingInfo={after:p.outside,before:u.outside},s+l+f}emphasis.peek=function(e,t,n){return n.options.emphasis||"*"};let lib_convert=function(e){if(null==e)return lib_ok;if("function"==typeof e)return lib_castFactory(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=lib_convert(e[n]);return lib_castFactory(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):lib_castFactory(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return lib_castFactory(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function lib_castFactory(e){return function(t,n,r){var i;return!!(null!==(i=t)&&"object"==typeof i&&"type"in i&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function lib_ok(){return!0}let h=[];var p=n(7962);function html(e){return e.value||""}function image_image(e,t,n,r){let i=checkQuote(n),o='"'===i?"Quote":"Apostrophe",a=n.enter("image"),s=n.enter("label"),l=n.createTracker(r),c=l.move("![");return c+=l.move(n.safe(e.alt,{before:c,after:"]",...l.current()}))+l.move("]("),s(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()}))+l.move(">")):(s=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":")",...l.current()}))),s(),e.title&&(s=n.enter(`title${o}`),c+=l.move(" "+i),c+=l.move(n.safe(e.title,{before:c,after:i,...l.current()}))+l.move(i),s()),c+=l.move(")"),a(),c}function imageReference(e,t,n,r){let i=e.referenceType,o=n.enter("imageReference"),a=n.enter("label"),s=n.createTracker(r),l=s.move("!["),c=n.safe(e.alt,{before:l,after:"]",...s.current()});l+=s.move(c+"]["),a();let u=n.stack;n.stack=[],a=n.enter("reference");let h=n.safe(n.associationId(e),{before:l,after:"]",...s.current()});return a(),n.stack=u,o(),"full"!==i&&c&&c===h?"shortcut"===i?l=l.slice(0,-1):l+=s.move("]"):l+=s.move(h+"]"),l}function inlineCode(e,t,n){let r=e.value||"",i="`",o=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++o<n.unsafe.length;){let e;let t=n.unsafe[o],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}function formatLinkAsAutolink(e,t){let n=(0,p.B)(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function link_link(e,t,n,r){let i,o;let a=checkQuote(n),s='"'===a?"Quote":"Apostrophe",l=n.createTracker(r);if(formatLinkAsAutolink(e,n)){let t=n.stack;n.stack=[],i=n.enter("autolink");let r=l.move("<");return r+=l.move(n.containerPhrasing(e,{before:r,after:">",...l.current()}))+l.move(">"),i(),n.stack=t,r}i=n.enter("link"),o=n.enter("label");let c=l.move("[");return c+=l.move(n.containerPhrasing(e,{before:c,after:"](",...l.current()}))+l.move("]("),o(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(o=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()}))+l.move(">")):(o=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":")",...l.current()}))),o(),e.title&&(o=n.enter(`title${s}`),c+=l.move(" "+a),c+=l.move(n.safe(e.title,{before:c,after:a,...l.current()}))+l.move(a),o()),c+=l.move(")"),i(),c}function linkReference(e,t,n,r){let i=e.referenceType,o=n.enter("linkReference"),a=n.enter("label"),s=n.createTracker(r),l=s.move("["),c=n.containerPhrasing(e,{before:l,after:"]",...s.current()});l+=s.move(c+"]["),a();let u=n.stack;n.stack=[],a=n.enter("reference");let h=n.safe(n.associationId(e),{before:l,after:"]",...s.current()});return a(),n.stack=u,o(),"full"!==i&&c&&c===h?"shortcut"===i?l=l.slice(0,-1):l+=s.move("]"):l+=s.move(h+"]"),l}function checkBullet(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function checkRule(e){let t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}html.peek=function(){return"<"},image_image.peek=function(){return"!"},imageReference.peek=function(){return"!"},inlineCode.peek=function(){return"`"},link_link.peek=function(e,t,n){return formatLinkAsAutolink(e,n)?"<":"["},linkReference.peek=function(){return"["};let unist_util_is_lib_convert=function(e){if(null==e)return unist_util_is_lib_ok;if("function"==typeof e)return unist_util_is_lib_castFactory(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=unist_util_is_lib_convert(e[n]);return unist_util_is_lib_castFactory(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):unist_util_is_lib_castFactory(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return unist_util_is_lib_castFactory(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function unist_util_is_lib_castFactory(e){return function(t,n,r){var i;return!!(null!==(i=t)&&"object"==typeof i&&"type"in i&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function unist_util_is_lib_ok(){return!0}let f=unist_util_is_lib_convert(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function strong(e,t,n,r){let i=function(e){let t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),o=n.enter("strong"),a=n.createTracker(r),s=a.move(i+i),l=a.move(n.containerPhrasing(e,{after:i,before:s,...a.current()})),c=l.charCodeAt(0),u=encodeInfo(r.before.charCodeAt(r.before.length-1),c,i);u.inside&&(l=encodeCharacterReference(c)+l.slice(1));let h=l.charCodeAt(l.length-1),p=encodeInfo(r.after.charCodeAt(0),h,i);p.inside&&(l=l.slice(0,-1)+encodeCharacterReference(h));let f=a.move(i+i);return o(),n.attentionEncodeSurroundingInfo={after:p.outside,before:u.outside},s+l+f}strong.peek=function(e,t,n){return n.options.strong||"*"};let d={blockquote:function(e,t,n,r){let i=n.enter("blockquote"),o=n.createTracker(r);o.move("> "),o.shift(2);let a=n.indentLines(n.containerFlow(e,o.current()),blockquote_map);return i(),a},break:hardBreak,code:function(e,t,n,r){let i=function(e){let t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),o=e.value||"",a="`"===i?"GraveAccent":"Tilde";if(!1===n.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value)){let e=n.enter("codeIndented"),t=n.indentLines(o,code_map);return e(),t}let s=n.createTracker(r),l=i.repeat(Math.max(function(e,t){let n=String(e),r=n.indexOf(t),i=r,o=0,a=0;if("string"!=typeof t)throw TypeError("Expected substring");for(;-1!==r;)r===i?++o>a&&(a=o):o=1,i=r+t.length,r=n.indexOf(t,i);return a}(o,i)+1,3)),c=n.enter("codeFenced"),u=s.move(l);if(e.lang){let t=n.enter(`codeFencedLang${a}`);u+=s.move(n.safe(e.lang,{before:u,after:" ",encode:["`"],...s.current()})),t()}if(e.lang&&e.meta){let t=n.enter(`codeFencedMeta${a}`);u+=s.move(" "),u+=s.move(n.safe(e.meta,{before:u,after:"\n",encode:["`"],...s.current()})),t()}return u+=s.move("\n"),o&&(u+=s.move(o+"\n")),u+=s.move(l),c(),u},definition:function(e,t,n,r){let i=checkQuote(n),o='"'===i?"Quote":"Apostrophe",a=n.enter("definition"),s=n.enter("label"),l=n.createTracker(r),c=l.move("[");return c+=l.move(n.safe(n.associationId(e),{before:c,after:"]",...l.current()}))+l.move("]: "),s(),!e.url||/[\0- \u007F]/.test(e.url)?(s=n.enter("destinationLiteral"),c+=l.move("<"),c+=l.move(n.safe(e.url,{before:c,after:">",...l.current()}))+l.move(">")):(s=n.enter("destinationRaw"),c+=l.move(n.safe(e.url,{before:c,after:e.title?" ":"\n",...l.current()}))),s(),e.title&&(s=n.enter(`title${o}`),c+=l.move(" "+i),c+=l.move(n.safe(e.title,{before:c,after:i,...l.current()}))+l.move(i),s()),a(),c},emphasis:emphasis,hardBreak:hardBreak,heading:function(e,t,n,r){var i,o;let a,s,l,c;let u=Math.max(Math.min(6,e.depth||1),1),f=n.createTracker(r);if(a=!1,"function"==typeof(i=function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return a=!0,!1})&&"function"!=typeof o?(l=void 0,c=i,s=o):(l=i,c=o,s=void 0),function(e,t,n,r){let i;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):i=t;let o=lib_convert(i),a=r?-1:1;(function factory(e,i,s){let l=e&&"object"==typeof e?e:{};if("string"==typeof l.type){let t="string"==typeof l.tagName?l.tagName:"string"==typeof l.name?l.name:void 0;Object.defineProperty(visit,"name",{value:"node ("+e.type+(t?"<"+t+">":"")+")"})}return visit;function visit(){var l;let c,u,p,f=h;if((!t||o(e,i,s[s.length-1]||void 0))&&!1===(f=Array.isArray(l=n(e,s))?l:"number"==typeof l?[!0,l]:null==l?h:[l])[0])return f;if("children"in e&&e.children&&e.children&&"skip"!==f[0])for(u=(r?e.children.length:-1)+a,p=s.concat(e);u>-1&&u<e.children.length;){let t=e.children[u];if(!1===(c=factory(t,u,p)())[0])return c;u="number"==typeof c[1]?c[1]:u+a}return f}})(e,void 0,[])()}(e,l,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return c(e,r,n)},s),(!e.depth||e.depth<3)&&(0,p.B)(e)&&(n.options.setext||a)){let t=n.enter("headingSetext"),r=n.enter("phrasing"),i=n.containerPhrasing(e,{...f.current(),before:"\n",after:"\n"});return r(),t(),i+"\n"+(1===u?"=":"-").repeat(i.length-(Math.max(i.lastIndexOf("\r"),i.lastIndexOf("\n"))+1))}let d="#".repeat(u),m=n.enter("headingAtx"),T=n.enter("phrasing");f.move(d+" ");let g=n.containerPhrasing(e,{before:"# ",after:"\n",...f.current()});return/^[\t ]/.test(g)&&(g=encodeCharacterReference(g.charCodeAt(0))+g.slice(1)),g=g?d+" "+g:d,n.options.closeAtx&&(g+=" "+d),T(),m(),g},html:html,image:image_image,imageReference:imageReference,inlineCode:inlineCode,link:link_link,linkReference:linkReference,list:function(e,t,n,r){let i=n.enter("list"),o=n.bulletCurrent,a=e.ordered?function(e){let t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):checkBullet(n),s=e.ordered?"."===a?")":".":function(e){let t=checkBullet(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n),l=!!t&&!!n.bulletLastUsed&&a===n.bulletLastUsed;if(!e.ordered){let t=e.children?e.children[0]:void 0;if("*"!==a&&"-"!==a||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(l=!0),checkRule(n)===a&&t){let t=-1;for(;++t<e.children.length;){let n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){l=!0;break}}}}l&&(a=s),n.bulletCurrent=a;let c=n.containerFlow(e,r);return n.bulletLastUsed=a,n.bulletCurrent=o,i(),c},listItem:function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),o=n.bulletCurrent||checkBullet(n);t&&"list"===t.type&&t.ordered&&(o=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+o);let a=o.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(a=4*Math.ceil(a/4));let s=n.createTracker(r);s.move(o+" ".repeat(a-o.length)),s.shift(a);let l=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,s.current()),function(e,t,n){return t?(n?"":" ".repeat(a))+e:(n?o:o+" ".repeat(a-o.length))+e});return l(),c},paragraph:function(e,t,n,r){let i=n.enter("paragraph"),o=n.enter("phrasing"),a=n.containerPhrasing(e,r);return o(),i(),a},root:function(e,t,n,r){let i=e.children.some(function(e){return f(e)}),o=i?n.containerPhrasing:n.containerFlow;return o.call(n,e,r)},strong:strong,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){let r=(checkRule(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){let t=e.options.ruleRepetition||3;if(t<3)throw Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}};function enterTable(e){let t=e._align;(0,r.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function exitTable(e){this.exit(e),this.data.inTable=void 0}function enterRow(e){this.enter({type:"tableRow",children:[]},e)}function exit(e){this.exit(e)}function enterCell(e){this.enter({type:"tableCell",children:[]},e)}function exitCodeText(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,replace));let n=this.stack[this.stack.length-1];(0,r.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function replace(e,t){return"|"===t?t:e}function exitCheck(e){let t=this.stack[this.stack.length-2];(0,r.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function exitParagraphWithTaskListItem(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,r.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r;let i=t.children,o=-1;for(;++o<i.length;){let e=i[o];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function listItemWithTaskListItem(e,t,n,r){let i=e.children[0],o="boolean"==typeof e.checked&&i&&"paragraph"===i.type,a="["+(e.checked?"x":" ")+"] ",s=n.createTracker(r);o&&s.move(a);let l=d.listItem(e,t,n,{...r,...s.current()});return o&&(l=l.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+a})),l}var m=n(4663);let T={tokenize:function(e,t,n){let r=0;return function wwwPrefixInside(t){return(87===t||119===t)&&r<3?(r++,e.consume(t),wwwPrefixInside):46===t&&3===r?(e.consume(t),wwwPrefixAfter):n(t)};function wwwPrefixAfter(e){return null===e?n(e):t(e)}},partial:!0},g={tokenize:function(e,t,n){let r,o,a;return domainInside;function domainInside(t){return 46===t||95===t?e.check(_,domainAfter,domainAtPunctuation)(t):null===t||(0,i.z3)(t)||(0,i.B8)(t)||45!==t&&(0,i.Xh)(t)?domainAfter(t):(a=!0,e.consume(t),domainInside)}function domainAtPunctuation(t){return 95===t?r=!0:(o=r,r=void 0),e.consume(t),domainInside}function domainAfter(e){return o||r||!a?n(e):t(e)}},partial:!0},E={tokenize:function(e,t){let n=0,r=0;return pathInside;function pathInside(o){return 40===o?(n++,e.consume(o),pathInside):41===o&&r<n?pathAtPunctuation(o):33===o||34===o||38===o||39===o||41===o||42===o||44===o||46===o||58===o||59===o||60===o||63===o||93===o||95===o||126===o?e.check(_,t,pathAtPunctuation)(o):null===o||(0,i.z3)(o)||(0,i.B8)(o)?t(o):(e.consume(o),pathInside)}function pathAtPunctuation(t){return 41===t&&r++,e.consume(t),pathInside}},partial:!0},_={tokenize:function(e,t,n){return trail;function trail(r){return 33===r||34===r||39===r||41===r||42===r||44===r||46===r||58===r||59===r||63===r||95===r||126===r?(e.consume(r),trail):38===r?(e.consume(r),trailCharacterReferenceStart):93===r?(e.consume(r),trailBracketAfter):60===r||null===r||(0,i.z3)(r)||(0,i.B8)(r)?t(r):n(r)}function trailBracketAfter(e){return null===e||40===e||91===e||(0,i.z3)(e)||(0,i.B8)(e)?t(e):trail(e)}function trailCharacterReferenceStart(t){return(0,i.jv)(t)?function trailCharacterReferenceInside(t){return 59===t?(e.consume(t),trail):(0,i.jv)(t)?(e.consume(t),trailCharacterReferenceInside):n(t)}(t):n(t)}},partial:!0},A={tokenize:function(e,t,n){return function(t){return e.consume(t),after};function after(e){return(0,i.H$)(e)?n(e):t(e)}},partial:!0},k={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!previousWww.call(r,r.previous)||previousUnbalanced(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(T,e.attempt(g,e.attempt(E,wwwAfter),n),n)(t))};function wwwAfter(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:previousWww},C={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,o="",a=!1;return function(t){return(72===t||104===t)&&previousProtocol.call(r,r.previous)&&!previousUnbalanced(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),o+=String.fromCodePoint(t),e.consume(t),protocolPrefixInside):n(t)};function protocolPrefixInside(t){if((0,i.jv)(t)&&o.length<5)return o+=String.fromCodePoint(t),e.consume(t),protocolPrefixInside;if(58===t){let n=o.toLowerCase();if("http"===n||"https"===n)return e.consume(t),protocolSlashesInside}return n(t)}function protocolSlashesInside(t){return 47===t?(e.consume(t),a)?afterProtocol:(a=!0,protocolSlashesInside):n(t)}function afterProtocol(t){return null===t||(0,i.Av)(t)||(0,i.z3)(t)||(0,i.B8)(t)||(0,i.Xh)(t)?n(t):e.attempt(g,e.attempt(E,protocolAfter),n)(t)}function protocolAfter(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:previousProtocol},y={name:"emailAutolink",tokenize:function(e,t,n){let r,o;let a=this;return function(t){return!gfmAtext(t)||!previousEmail.call(a,a.previous)||previousUnbalanced(a.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function atext(t){return gfmAtext(t)?(e.consume(t),atext):64===t?(e.consume(t),emailDomain):n(t)}(t))};function emailDomain(t){return 46===t?e.check(A,emailDomainAfter,emailDomainDot)(t):45===t||95===t||(0,i.H$)(t)?(o=!0,e.consume(t),emailDomain):emailDomainAfter(t)}function emailDomainDot(t){return e.consume(t),r=!0,emailDomain}function emailDomainAfter(s){return o&&r&&(0,i.jv)(a.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(s)):n(s)}},previous:previousEmail},N={},S=48;for(;S<123;)N[S]=y,58==++S?S=65:91===S&&(S=97);function previousWww(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,i.z3)(e)}function previousProtocol(e){return!(0,i.jv)(e)}function previousEmail(e){return!(47===e||gfmAtext(e))}function gfmAtext(e){return 43===e||45===e||46===e||95===e||(0,i.H$)(e)}function previousUnbalanced(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}N[43]=y,N[45]=y,N[46]=y,N[95]=y,N[72]=[y,C],N[104]=[y,C],N[87]=[y,k],N[119]=[y,k];var I=n(3402),b=n(2761);let O={tokenize:function(e,t,n){let r=this;return(0,b.f)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function tokenizePotentialGfmFootnoteCall(e,t,n){let r;let i=this,o=i.events.length,a=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;o--;){let e=i.events[o][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(o){if(!r||!r._balanced)return n(o);let s=(0,l.d)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===s.codePointAt(0)&&a.includes(s.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),t(o)):n(o)}}function resolveToPotentialGfmFootnoteCall(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let o={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},a={type:"chunkString",contentType:"string",start:Object.assign({},o.start),end:Object.assign({},o.end)},s=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",o,t],["enter",a,t],["exit",a,t],["exit",o,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...s),e}function tokenizeGfmFootnoteCall(e,t,n){let r;let o=this,a=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]),s=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),callStart};function callStart(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",callData)}function callData(c){if(s>999||93===c&&!r||null===c||91===c||(0,i.z3)(c))return n(c);if(93===c){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return a.includes((0,l.d)(o.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(c),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(c)}return(0,i.z3)(c)||(r=!0),s++,e.consume(c),92===c?callEscape:callData}function callEscape(t){return 91===t||92===t||93===t?(e.consume(t),s++,callData):callData(t)}}function tokenizeDefinitionStart(e,t,n){let r,o;let a=this,s=a.parser.gfmFootnotes||(a.parser.gfmFootnotes=[]),c=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),labelAtMarker};function labelAtMarker(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",labelInside):n(t)}function labelInside(t){if(c>999||93===t&&!o||null===t||91===t||(0,i.z3)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,l.d)(a.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),labelAfter}return(0,i.z3)(t)||(o=!0),c++,e.consume(t),92===t?labelEscape:labelInside}function labelEscape(t){return 91===t||92===t||93===t?(e.consume(t),c++,labelInside):labelInside(t)}function labelAfter(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),s.includes(r)||s.push(r),(0,b.f)(e,whitespaceAfter,"gfmFootnoteDefinitionWhitespace")):n(t)}function whitespaceAfter(e){return t(e)}}function tokenizeDefinitionContinuation(e,t,n){return e.check(I.w,t,e.attempt(O,t,n))}function gfmFootnoteDefinitionEnd(e){e.exit("gfmFootnoteDefinition")}var x=n(2888),R=n(3233);let EditMap=class EditMap{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push([...e]),e.length=0;let r=n.pop();for(;r;)e.push(...r),r=n.pop();this.map.length=0}};function tokenizeTable(e,t,n){let r;let o=this,a=0,s=0;return function(e){let t=o.events.length-1;for(;t>-1;){let e=o.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?o.events[t][1].type:null,i="tableHead"===r||"tableRow"===r?bodyRowStart:headRowBefore;return i===bodyRowStart&&o.parser.lazy[o.now().line]?n(e):i(e)};function headRowBefore(t){return e.enter("tableHead"),e.enter("tableRow"),124===t||(r=!0,s+=1),headRowBreak(t)}function headRowBreak(t){return null===t?n(t):(0,i.Ch)(t)?s>1?(s=0,o.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),headDelimiterStart):n(t):(0,i.xz)(t)?(0,b.f)(e,headRowBreak,"whitespace")(t):(s+=1,r&&(r=!1,a+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,headRowBreak):(e.enter("data"),headRowData(t))}function headRowData(t){return null===t||124===t||(0,i.z3)(t)?(e.exit("data"),headRowBreak(t)):(e.consume(t),92===t?headRowEscape:headRowData)}function headRowEscape(t){return 92===t||124===t?(e.consume(t),headRowData):headRowData(t)}function headDelimiterStart(t){return(o.interrupt=!1,o.parser.lazy[o.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,(0,i.xz)(t))?(0,b.f)(e,headDelimiterBefore,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):headDelimiterBefore(t)}function headDelimiterBefore(t){return 45===t||58===t?headDelimiterValueBefore(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),headDelimiterCellBefore):n(t)}function headDelimiterCellBefore(t){return(0,i.xz)(t)?(0,b.f)(e,headDelimiterValueBefore,"whitespace")(t):headDelimiterValueBefore(t)}function headDelimiterValueBefore(t){return 58===t?(s+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),headDelimiterLeftAlignmentAfter):45===t?(s+=1,headDelimiterLeftAlignmentAfter(t)):null===t||(0,i.Ch)(t)?headDelimiterCellAfter(t):n(t)}function headDelimiterLeftAlignmentAfter(t){return 45===t?(e.enter("tableDelimiterFiller"),function headDelimiterFiller(t){return 45===t?(e.consume(t),headDelimiterFiller):58===t?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),headDelimiterRightAlignmentAfter):(e.exit("tableDelimiterFiller"),headDelimiterRightAlignmentAfter(t))}(t)):n(t)}function headDelimiterRightAlignmentAfter(t){return(0,i.xz)(t)?(0,b.f)(e,headDelimiterCellAfter,"whitespace")(t):headDelimiterCellAfter(t)}function headDelimiterCellAfter(o){return 124===o?headDelimiterBefore(o):null===o||(0,i.Ch)(o)?r&&a===s?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(o)):n(o):n(o)}function bodyRowStart(t){return e.enter("tableRow"),bodyRowBreak(t)}function bodyRowBreak(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),bodyRowBreak):null===n||(0,i.Ch)(n)?(e.exit("tableRow"),t(n)):(0,i.xz)(n)?(0,b.f)(e,bodyRowBreak,"whitespace")(n):(e.enter("data"),bodyRowData(n))}function bodyRowData(t){return null===t||124===t||(0,i.z3)(t)?(e.exit("data"),bodyRowBreak(t)):(e.consume(t),92===t?bodyRowEscape:bodyRowData)}function bodyRowEscape(t){return 92===t||124===t?(e.consume(t),bodyRowData):bodyRowData(t)}}function resolveTable(e,t){let n,r,i,o=-1,a=!0,s=0,l=[0,0,0,0],c=[0,0,0,0],u=!1,h=0,p=new EditMap;for(;++o<e.length;){let f=e[o],d=f[1];"enter"===f[0]?"tableHead"===d.type?(u=!1,0!==h&&(flushTableEnd(p,t,h,n,r),r=void 0,h=0),n={type:"table",start:Object.assign({},d.start),end:Object.assign({},d.end)},p.add(o,0,[["enter",n,t]])):"tableRow"===d.type||"tableDelimiterRow"===d.type?(a=!0,i=void 0,l=[0,0,0,0],c=[0,o+1,0,0],u&&(u=!1,r={type:"tableBody",start:Object.assign({},d.start),end:Object.assign({},d.end)},p.add(o,0,[["enter",r,t]])),s="tableDelimiterRow"===d.type?2:r?3:1):s&&("data"===d.type||"tableDelimiterMarker"===d.type||"tableDelimiterFiller"===d.type)?(a=!1,0===c[2]&&(0!==l[1]&&(c[0]=c[1],i=flushCell(p,t,l,s,void 0,i),l=[0,0,0,0]),c[2]=o)):"tableCellDivider"===d.type&&(a?a=!1:(0!==l[1]&&(c[0]=c[1],i=flushCell(p,t,l,s,void 0,i)),c=[(l=c)[1],o,0,0])):"tableHead"===d.type?(u=!0,h=o):"tableRow"===d.type||"tableDelimiterRow"===d.type?(h=o,0!==l[1]?(c[0]=c[1],i=flushCell(p,t,l,s,o,i)):0!==c[1]&&(i=flushCell(p,t,c,s,o,i)),s=0):s&&("data"===d.type||"tableDelimiterMarker"===d.type||"tableDelimiterFiller"===d.type)&&(c[3]=o)}for(0!==h&&flushTableEnd(p,t,h,n,r),p.consume(t.events),o=-1;++o<t.events.length;){let e=t.events[o];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,o))}return e}function flushCell(e,t,n,r,i,o){0!==n[0]&&(o.end=Object.assign({},getPoint(t.events,n[0])),e.add(n[0],0,[["exit",o,t]]));let a=getPoint(t.events,n[1]);if(o={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},a),end:Object.assign({},a)},e.add(n[1],0,[["enter",o,t]]),0!==n[2]){let i=getPoint(t.events,n[2]),o=getPoint(t.events,n[3]),a={type:"tableContent",start:Object.assign({},i),end:Object.assign({},o)};if(e.add(n[2],0,[["enter",a,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",a,t]])}return void 0!==i&&(o.end=Object.assign({},getPoint(t.events,i)),e.add(i,0,[["exit",o,t]]),o=void 0),o}function flushTableEnd(e,t,n,r,i){let o=[],a=getPoint(t.events,n);i&&(i.end=Object.assign({},a),o.push(["exit",i,t])),r.end=Object.assign({},a),o.push(["exit",r,t]),e.add(n+1,0,o)}function getPoint(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let v={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),inside):n(t)};function inside(t){return(0,i.z3)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),close):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),close):n(t)}function close(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),after):n(t)}function after(r){return(0,i.Ch)(r)?t(r):(0,i.xz)(r)?e.check({tokenize:spaceThenNonSpace},t,n)(r):n(r)}}};function spaceThenNonSpace(e,t,n){return(0,b.f)(e,function(e){return null===e?n(e):t(e)},"whitespace")}let L={};function remarkGfm(e){let t=e||L,n=this.data(),r=n.micromarkExtensions||(n.micromarkExtensions=[]),i=n.fromMarkdownExtensions||(n.fromMarkdownExtensions=[]),o=n.toMarkdownExtensions||(n.toMarkdownExtensions=[]);r.push((0,m.W)([{text:N},{document:{91:{name:"gfmFootnoteDefinition",tokenize:tokenizeDefinitionStart,continuation:{tokenize:tokenizeDefinitionContinuation},exit:gfmFootnoteDefinitionEnd}},text:{91:{name:"gfmFootnoteCall",tokenize:tokenizeGfmFootnoteCall},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:tokenizePotentialGfmFootnoteCall,resolveTo:resolveToPotentialGfmFootnoteCall}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let i=this.previous,o=this.events,a=0;return function(s){return 126===i&&"characterEscape"!==o[o.length-1][1].type?r(s):(e.enter("strikethroughSequenceTemporary"),function more(o){let s=(0,u.r)(i);if(126===o)return a>1?r(o):(e.consume(o),a++,more);if(a<2&&!t)return r(o);let l=e.exit("strikethroughSequenceTemporary"),c=(0,u.r)(o);return l._open=!c||2===c&&!!s,l._close=!s||2===s&&!!c,n(o)}(s))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},o={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},a=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",o,t]],s=t.parser.constructs.insideSpan.null;s&&(0,x.d)(a,a.length,0,(0,R.C)(s,e.slice(r+1,n),t)),(0,x.d)(a,a.length,0,[["exit",o,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,x.d)(e,r-1,n-r+3,a),n=r+a.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(t),{flow:{null:{name:"table",tokenize:tokenizeTable,resolveAll:resolveTable}}},{text:{91:v}}])),i.push([{transforms:[transformGfmAutolinkLiterals],enter:{literalAutolink:enterLiteralAutolink,literalAutolinkEmail:enterLiteralAutolinkValue,literalAutolinkHttp:enterLiteralAutolinkValue,literalAutolinkWww:enterLiteralAutolinkValue},exit:{literalAutolink:exitLiteralAutolink,literalAutolinkEmail:exitLiteralAutolinkEmail,literalAutolinkHttp:exitLiteralAutolinkHttp,literalAutolinkWww:exitLiteralAutolinkWww}},{enter:{gfmFootnoteDefinition:enterFootnoteDefinition,gfmFootnoteDefinitionLabelString:enterFootnoteDefinitionLabelString,gfmFootnoteCall:enterFootnoteCall,gfmFootnoteCallString:enterFootnoteCallString},exit:{gfmFootnoteDefinition:exitFootnoteDefinition,gfmFootnoteDefinitionLabelString:exitFootnoteDefinitionLabelString,gfmFootnoteCall:exitFootnoteCall,gfmFootnoteCallString:exitFootnoteCallString}},{canContainEols:["delete"],enter:{strikethrough:enterStrikethrough},exit:{strikethrough:exitStrikethrough}},{enter:{table:enterTable,tableData:enterCell,tableHeader:enterCell,tableRow:enterRow},exit:{codeText:exitCodeText,table:exitTable,tableData:exit,tableHeader:exit,tableRow:exit}},{exit:{taskListCheckValueChecked:exitCheck,taskListCheckValueUnchecked:exitCheck,paragraph:exitParagraphWithTaskListItem}}]),o.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:a,notInConstruct:s},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:a,notInConstruct:s},{character:":",before:"[ps]",after:"\\/",inConstruct:a,notInConstruct:s}]},{unsafe:[{character:"[",inConstruct:["phrasing","label","reference"]}],handlers:{footnoteDefinition,footnoteReference}},{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:c}],handlers:{delete:handleDelete}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,o=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=d.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return serializeData(function(e,t,n){let r=e.children,i=-1,o=[],a=t.enter("table");for(;++i<r.length;)o[i]=handleTableRowAsData(r[i],t,n);return a(),o}(e,n,r),e.align)},tableCell:handleTableCell,tableRow:function(e,t,n,r){let i=handleTableRowAsData(e,n,r),o=serializeData([i]);return o.slice(0,o.indexOf("\n"))}}};function handleTableCell(e,t,n,r){let i=n.enter("tableCell"),a=n.enter("phrasing"),s=n.containerPhrasing(e,{...r,before:o,after:o});return a(),i(),s}function serializeData(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),i=n.stringLength||defaultStringLength,o=[],a=[],s=[],l=[],c=0,u=-1;for(;++u<e.length;){let t=[],r=[],o=-1;for(e[u].length>c&&(c=e[u].length);++o<e[u].length;){var h;let a=null==(h=e[u][o])?"":String(h);if(!1!==n.alignDelimiters){let e=i(a);r[o]=e,(void 0===l[o]||e>l[o])&&(l[o]=e)}t.push(a)}a[u]=t,s[u]=r}let p=-1;if("object"==typeof r&&"length"in r)for(;++p<c;)o[p]=toAlignment(r[p]);else{let e=toAlignment(r);for(;++p<c;)o[p]=e}p=-1;let f=[],d=[];for(;++p<c;){let e=o[p],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,l[p]-t.length-r.length),a=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>l[p]&&(l[p]=i),d[p]=i),f[p]=a}a.splice(1,0,f),s.splice(1,0,d),u=-1;let m=[];for(;++u<a.length;){let e=a[u],t=s[u];p=-1;let r=[];for(;++p<c;){let i=e[p]||"",a="",s="";if(!1!==n.alignDelimiters){let e=l[p]-(t[p]||0),n=o[p];114===n?a=" ".repeat(e):99===n?e%2?(a=" ".repeat(e/2+.5),s=" ".repeat(e/2-.5)):s=a=" ".repeat(e/2):s=" ".repeat(e)}!1===n.delimiterStart||p||r.push("|"),!1!==n.padding&&!(!1===n.alignDelimiters&&""===i)&&(!1!==n.delimiterStart||p)&&r.push(" "),!1!==n.alignDelimiters&&r.push(a),r.push(i),!1!==n.alignDelimiters&&r.push(s),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||p!==c-1)&&r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function handleTableRowAsData(e,t,n){let r=e.children,i=-1,o=[],a=t.enter("tableRow");for(;++i<r.length;)o[i]=handleTableCell(r[i],e,t,n);return a(),o}}(t),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:listItemWithTaskListItem}}]})}},342:function(e,t,n){"use strict";function parse(e){let t=String(e||"").trim();return t?t.split(/[ \t\n\r\f]+/g):[]}function stringify(e){return e.join(" ").trim()}n.d(t,{P:function(){return stringify},Q:function(){return parse}})}}]);