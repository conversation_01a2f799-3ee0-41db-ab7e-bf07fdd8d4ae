/**
 * Script pour configurer Meilisearch v0.27.0 afin qu'il soit compatible avec le code existant
 * 
 * Pour exécuter ce script :
 * 1. Assurez-vous que Meilisearch est en cours d'exécution
 * 2. Exécutez la commande : node config-meilisearch-v0.27.0.js
 */

const fetch = require('node-fetch');

const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function configMeilisearch() {
  try {
    console.log('Configuration de Meilisearch v0.27.0 pour compatibilité avec le code existant...');
    
    // Vérifier si l'index existe
    console.log('Vérification de l\'existence de l\'index...');
    const indexesResponse = await fetch(`${MEILI_URL}/indexes`);
    const indexes = await indexesResponse.json();
    
    const indexExists = indexes.results && indexes.results.some(index => index.uid === INDEX_NAME);
    
    if (!indexExists) {
      console.log('L\'index n\'existe pas, création de l\'index...');
      await fetch(`${MEILI_URL}/indexes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uid: INDEX_NAME })
      });
      console.log('Index créé avec succès');
    } else {
      console.log('L\'index existe déjà');
    }
    
    // 1. Configurer les attributs filtrables
    console.log('Configuration des attributs filtrables...');
    const filterableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'title', 'topics', 'tags', 'author', 'type', 'blog', 'cs'
      ])
    });
    console.log(`Attributs filtrables configurés: ${filterableResponse.status}`);
    
    // 2. Configurer les attributs triables
    console.log('Configuration des attributs triables...');
    const sortableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['date'])
    });
    console.log(`Attributs triables configurés: ${sortableResponse.status}`);
    
    // 3. Configurer les attributs de recherche
    console.log('Configuration des attributs de recherche...');
    const searchableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['title', 'body', 'lead'])
    });
    console.log(`Attributs de recherche configurés: ${searchableResponse.status}`);
    
    // 4. Configurer les règles de classement
    console.log('Configuration des règles de classement...');
    const rankingResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/ranking-rules`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'words', 'typo', 'proximity', 'attribute', 'sort', 'exactness'
      ])
    });
    console.log(`Règles de classement configurées: ${rankingResponse.status}`);
    
    console.log('Configuration terminée !');
  } catch (error) {
    console.error('Erreur lors de la configuration :', error);
  }
}

configMeilisearch();
