import styled from "styled-components";

export default function ListTypes({ setFilter }) {
  return (
    <Wrapper>
      <ul>
        <li onClick={() => setFilter("type", "article")}>Article</li>
        <li onClick={() => setFilter("type", "formation")}>Formation</li>
        <li onClick={() => setFilter("type", "webinaire")}>Webinaire</li>
        <li onClick={() => setFilter("type", "parcours-email")}>Parcours e-mails</li>
        <li onClick={() => setFilter("type", "podcast")}>podcast</li>
      </ul>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  position: relative;
  font-weight: 400;
  width: calc(33.33% + var(--desktop-gap));
  font-size: 24px;
  color: var(--soft-white);

  ul {
    padding: 0;
    margin: 0;
    text-decoration: none;
  }

  li {
    padding: 16px 24px 8px 24px;
    display: inline-block;
    list-style: none;
    width: 100%;
    border-bottom: 1px solid #323232;

    &:hover {
      cursor: pointer;
      background-color: black;
    }
  }
`

