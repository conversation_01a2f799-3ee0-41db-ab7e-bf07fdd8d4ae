import { createContext, useState } from "react";

export const HeaderContext = createContext({});

export const HeaderProvider = ({ children }) => {

  const [headerState, setHeaderState] = useState({
    showMenu: false,
    dropDownOpen: false,
    dropDownKey: "blogs"
  });


  const onDropDownButtonClick = (key) => {
    if( headerState.dropDownKey !== key) {
      setHeaderState(prevState => ({
        ...prevState,
        dropDownOpen: true,
        dropDownKey: key,
      }))
    } else {
      setHeaderState(prevState => ({
        ...prevState,
        dropDownOpen: !headerState.dropDownOpen,
        dropDownKey: key,
      }))
    }
  }

  const onDropDownClickOutside = () => {
    setHeaderState(prevState => ({
      ...prevState,
      dropDownOpen: false,
    }))
    
  }

  const toggleMenu = (close = false) => {
    
    let body = document.body;

    body.classList.toggle("no-scroll", !headerState.showMenu);

    if(close) {
      setHeaderState({ ...headerState, showMenu: false });
      body.classList.toggle("no-scroll", false);
    } else {
      setHeaderState({ ...headerState, showMenu: !headerState.showMenu });
    }
  }

  const value = {
    headerState,
    toggleMenu,
    onDropDownButtonClick,
    onDropDownClickOutside,
  }

  return (
    <HeaderContext.Provider value={value}>
      {children}
    </HeaderContext.Provider>
  )
}