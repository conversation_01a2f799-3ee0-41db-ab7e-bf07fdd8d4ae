"use strict";exports.id=142,exports.ids=[142],exports.modules={2325:(e,t,i)=>{i.d(t,{Z:()=>HorizontalReversePostCard});var a=i(997),n=i(6382);i(1664);var o=i(7518),r=i.n(o),s=i(2558),l=i(7467),d=i(6453),c=i(1077),p=i(9280),h=i(5432),x=i(6035);i(635);var g=i(2570);function HorizontalReversePostCard({post:e,options:t}){let i,o,r;let f=!(0,x.a)({mediaQuery:l.U.tablet});if(!e.type)return a.jsx(a.Fragment,{});let j=(0,c.qt)(e);return t?.showDate&&(o=(o=e.date?(0,n.S$)(e.date):(0,n.S$)(e.published_at)).replace(".","")),t?.showAuthor&&e.author&&(r=e.author?.fullName?e.author.fullName:e.author),t?.showLead&&(i=(0,c.mj)(e)),(0,a.jsxs)(u,{children:[(0,a.jsxs)("div",{className:"post-info",children:[(0,a.jsxs)(d.My,{children:[o&&a.jsx("span",{children:o}),o&&r&&" - ",r&&a.jsx("span",{children:r})]}),(0,a.jsxs)(g.Z,{link:j,children:[a.jsx(d.kz,{children:e.title}),!f&&i&&a.jsx(d.X0,{children:i})]})]}),a.jsx("div",{className:"post-image-container",children:a.jsx(g.Z,{link:j,children:(0,a.jsxs)(m,{children:[a.jsx("div",{className:"post-image",children:a.jsx(s.Z,{imageData:e.image})}),t?.showAnimatedIcon&&a.jsx(p.YM,{type:e.type,colors:(0,h.Q)(e.type)})]})})})]})}let m=r().div.withConfig({componentId:"sc-355f4397-0"})`
  width: 100%;
  position: relative;
  .post-image {
    width: 100%;
    aspect-ratio: 1/1;
    position: relative;
  }
`,u=r().div.withConfig({componentId:"sc-355f4397-1"})`
  list-style: none;
  position: relative;
  width: 100%;
  list-style: none;

  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  &:after {
    content: "";
    display: block;
    width: 100%;
    margin: 24px 0px;
    background-color: #40444444;
    height: 1px;
  }

  .post-info {
    width: 65%;
    padding-right: 10px;
  }
  .post-image-container {
    width: 30%;
  }
  @media screen and (max-width: 320px) {
    /* Little screen only */
    flex-direction: column;
    .post-info {
      width: 100%;
    }
    .post-image-container {
      width: 40%;
    }
  }
  @media ${l.U.tablet} {
    flex-direction: row-reverse;
    &:after {
      content: "";
      display: block;
      width: 100%;
      margin: 37px 0px 30px 0px;

      background-color: #40444444;
      height: 1px;
    }
    .post-info {
      width: 75%;
    }
    .post-image-container {
      width: 20%;
    }
  }
`},465:(e,t,i)=>{i.d(t,{Z:()=>SSRPaginate});var a=i(997),n=i(7518),o=i.n(n),r=i(4092),s=i(1664),l=i.n(s),d=i(7467);function PaginationNumber({value:e,active:t,baseUrl:i}){return t?a.jsx(c,{active:!0,children:e}):a.jsx(l(),{href:i+e,children:a.jsx(c,{children:e})})}let c=o().div.withConfig({componentId:"sc-85464bad-0"})`
  display: inline-block;
  height: 42px;
  width: 42px;
  font-size: 26px;
  padding-top: 10px;
  font-family: Stelvio, sans-serif;
  border-radius: 100px;
  text-align: center;
  color: #080808;
  background-color: inherit;
  border: 1px solid ${e=>e.active?"black":"transparent"};

  &:hover {
    background-color: transparent;
    border: 1px solid black;
  }
  
  @media ${d.U.tablet} {
    margin: auto 10px;
  }
  @media ${d.U.desktop} {
    
  }
`;function SSRPaginate({nbHits:e,currentPage:t,baseUrl:i,options:n}){let o=(0,r.Z)(e,t||1,n.postPerPage||10,n.maxPages||5);return(0,a.jsxs)(p,{className:"no-select",children:[o.startPage>1&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(PaginationNumber,{value:1,active:1===t,baseUrl:i}),a.jsx(h,{children:"..."})]}),o.pages.map((e,t)=>a.jsx(PaginationNumber,{active:e===o.currentPage,value:e,baseUrl:i},t)),o.totalPages!==o.endPage&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(h,{children:"..."}),a.jsx(PaginationNumber,{baseUrl:i,active:o.currentPage===o.endPage,value:o.totalPages})]})]})}let p=o().div.withConfig({componentId:"sc-4412c46d-0"})`
  position: relative;
  width: 100%;
`,h=o().span.withConfig({componentId:"sc-4412c46d-1"})`
  color: #080808;
`}};