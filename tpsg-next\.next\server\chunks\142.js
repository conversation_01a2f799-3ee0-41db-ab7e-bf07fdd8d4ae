"use strict";exports.id=142,exports.ids=[142],exports.modules={2325:(e,t,i)=>{i.d(t,{Z:()=>HorizontalReversePostCard});var a=i(997),o=i(6382);i(1664);var n=i(7518),s=i.n(n),r=i(2558),l=i(7467),d=i(6453),c=i(1077),h=i(9280),p=i(5432),x=i(6035);i(635);var g=i(2570);function HorizontalReversePostCard({post:e,options:t}){let i,n,s;let j=!(0,x.a)({mediaQuery:l.U.tablet});if(!e.type)return a.jsx(a.Fragment,{});let b=(0,c.qt)(e);return t?.showDate&&(n=(n=e.date?(0,o.S$)(e.date):(0,o.S$)(e.published_at)).replace(".","")),t?.showAuthor&&(console.log("\uD83D\uDD0D DIAGNOSTIC AUTEUR:",{title:e.title,slug:e.slug,author:e.author,authorType:typeof e.author,authorKeys:e.author?Object.keys(e.author):"N/A",allPostKeys:Object.keys(e),showAuthor:t?.showAuthor}),e.author?console.log("✅ AUTEUR TROUV\xc9:",s=e.author?.fullName?e.author.fullName:e.author):console.log("❌ PAS D'AUTEUR pour:",e.title)),t?.showLead&&(i=(0,c.mj)(e)),(0,a.jsxs)(m,{children:[(0,a.jsxs)("div",{className:"post-info",children:[(0,a.jsxs)(d.My,{children:[n&&a.jsx("span",{children:n}),n&&s&&" - ",s&&a.jsx("span",{children:s})]}),(0,a.jsxs)(g.Z,{link:b,children:[a.jsx(d.kz,{children:e.title}),!j&&i&&a.jsx(d.X0,{children:i})]})]}),a.jsx("div",{className:"post-image-container",children:a.jsx(g.Z,{link:b,children:(0,a.jsxs)(u,{children:[a.jsx("div",{className:"post-image",children:a.jsx(r.Z,{imageData:e.image})}),t?.showAnimatedIcon&&a.jsx(h.YM,{type:e.type,colors:(0,p.Q)(e.type)})]})})})]})}let u=s().div.withConfig({componentId:"sc-c56e794b-0"})`
  width: 100%;
  position: relative;
  .post-image {
    width: 100%;
    aspect-ratio: 1/1;
    position: relative;
  }
`,m=s().div.withConfig({componentId:"sc-c56e794b-1"})`
  list-style: none;
  position: relative;
  width: 100%;
  list-style: none;

  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  &:after {
    content: "";
    display: block;
    width: 100%;
    margin: 24px 0px;
    background-color: #40444444;
    height: 1px;
  }

  .post-info {
    width: 65%;
    padding-right: 10px;
  }
  .post-image-container {
    width: 30%;
  }
  @media screen and (max-width: 320px) {
    // Little screen only
    flex-direction: column;
    .post-info {
      width: 100%;
    }
    .post-image-container {
      width: 40%;
    }
  }
  @media ${l.U.tablet} {
    flex-direction: row-reverse;
    &:after {
      content: "";
      display: block;
      width: 100%;
      margin: 37px 0px 30px 0px;

      background-color: #40444444;
      height: 1px;
    }
    .post-info {
      width: 75%;
    }
    .post-image-container {
      width: 20%;
    }
  }
`},465:(e,t,i)=>{i.d(t,{Z:()=>SSRPaginate});var a=i(997),o=i(7518),n=i.n(o),s=i(4092),r=i(1664),l=i.n(r),d=i(7467);function PaginationNumber({value:e,active:t,baseUrl:i}){return t?a.jsx(c,{active:!0,children:e}):a.jsx(l(),{href:i+e,children:a.jsx(c,{children:e})})}let c=n().div.withConfig({componentId:"sc-85464bad-0"})`
  display: inline-block;
  height: 42px;
  width: 42px;
  font-size: 26px;
  padding-top: 10px;
  font-family: Stelvio, sans-serif;
  border-radius: 100px;
  text-align: center;
  color: #080808;
  background-color: inherit;
  border: 1px solid ${e=>e.active?"black":"transparent"};

  &:hover {
    background-color: transparent;
    border: 1px solid black;
  }
  
  @media ${d.U.tablet} {
    margin: auto 10px;
  }
  @media ${d.U.desktop} {
    
  }
`;function SSRPaginate({nbHits:e,currentPage:t,baseUrl:i,options:o}){let n=(0,s.Z)(e,t||1,o.postPerPage||10,o.maxPages||5);return(0,a.jsxs)(h,{className:"no-select",children:[n.startPage>1&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(PaginationNumber,{value:1,active:1===t,baseUrl:i}),a.jsx(p,{children:"..."})]}),n.pages.map((e,t)=>a.jsx(PaginationNumber,{active:e===n.currentPage,value:e,baseUrl:i},t)),n.totalPages!==n.endPage&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(p,{children:"..."}),a.jsx(PaginationNumber,{baseUrl:i,active:n.currentPage===n.endPage,value:n.totalPages})]})]})}let h=n().div.withConfig({componentId:"sc-4412c46d-0"})`
  position: relative;
  width: 100%;
`,p=n().span.withConfig({componentId:"sc-4412c46d-1"})`
  color: #080808;
`}};