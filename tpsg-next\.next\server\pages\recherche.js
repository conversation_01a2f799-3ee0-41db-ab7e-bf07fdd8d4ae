"use strict";(()=>{var e={};e.id=7958,e.ids=[7958,2888],e.modules={1808:(e,t,i)=>{i.r(t),i.d(t,{config:()=>ei,default:()=>Y,getServerSideProps:()=>et,getStaticPaths:()=>ee,getStaticProps:()=>J,reportWebVitals:()=>er,routeModule:()=>ep,unstable_getServerProps:()=>es,unstable_getServerSideProps:()=>en,unstable_getStaticParams:()=>el,unstable_getStaticPaths:()=>eo,unstable_getStaticProps:()=>ea});var r={};i.r(r),i.d(r,{default:()=>SearchPage,getServerSideProps:()=>getServerSideProps});var a=i(7093),o=i(5244),l=i(1323),s=i(779),n=i(4033),p=i(997),c=i(6689),d=i(7518),h=i.n(d),u=i(723),f=i(1163);let g=["podcast","formation","article","webinaire","parcours-email"];var m=i(7467);let big_input=({listState:e,changeFilter:t,changeQueryTerms:i,autoFocus:r=!1})=>{let[a,o]=(0,c.useState)(""),[l,s]=(0,c.useState)(e.queryTerms),[n,d]=(0,c.useState)(""),[h,u]=(0,c.useState)(0),handleTextChange=t=>{if(s(t),t.length>=3&&!e.activeFilter){let e=function(e,t){let i=null;return(i=t.authors.filter(t=>t.fullName?.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").startsWith(e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"")))).length>0?{type:"author",value:i[0].fullName}:(i=t.topics.filter(t=>t.name?.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").startsWith(e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"")))).length>0?{type:"topics",value:i[0].name}:(i=g.filter(t=>t.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"").startsWith(e.toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g,"")))).length>0?{type:"type",value:i[0]}:i}(t);e.value&&(e.displayValue=t+e.value.slice(t.length)),o(e)}else o(null)},handleKeyDown=e=>{9===e.keyCode&&(e.preventDefault(),a&&addFilter()),8===e.keyCode&&0===l.length&&(t(null,null),i("")),"Enter"===e.key&&i(l)},addFilter=()=>{s(""),i(""),t(a.value,a.type,!0),o(null)},updateCaret=()=>{let e=document.getElementById("input");d(l.replaceAll(" ","!").slice(0,e.selectionStart))};return(0,c.useEffect)(()=>{updateCaret()},[l]),(0,c.useEffect)(()=>{let e=document.getElementById("ruler");e&&u(e.offsetWidth)},[n]),(0,p.jsxs)(C,{children:[a?.displayValue&&p.jsx("div",{className:"filter-label",onClick:()=>addFilter(),children:"AJOUTER: CLICK ou TAB"}),(0,p.jsxs)("p",{id:"ruler",children:[n," ",p.jsx(x,{width:h})]}),p.jsx("p",{children:a?.displayValue||""}),p.jsx("input",{id:"input",autoFocus:r,onKeyUp:e=>updateCaret(),onKeyDown:e=>handleKeyDown(e),onClick:()=>updateCaret(),onChange:e=>handleTextChange(e.target.value),placeholder:"Rechercher",value:l}),p.jsx(SearchSVG,{})]})},x=h().span.withConfig({componentId:"sc-939f8617-0"})`
  position: absolute;
  top: 8px;
  left: 0;
  display: none;
  width: ${e=>e.width}px;
  height: 80%;
  border-right-style: solid;
  border-right-color: #FF856A;
  border-right-width: ${e=>0===e.width?"3px":"36px"};
  transition: width 300ms cubic-bezier(.55, .77, .17, .97);

  @media ${m.U.tablet} {
    //display: inline-block;
  }
`,C=h().div.withConfig({componentId:"sc-939f8617-1"})`
  position: relative;
  margin-top: 16px;
  width: 100%;
  height: 48px;
  background-color: transparent;

  @media ${m.U.tablet} {
    height: 92px;
  }

  #ruler {
    position: absolute;
    font-family: Stelvio, Arial, sans-serif;
    font-weight: 500;
    font-size: 72px;
    height: 72px;
    color: transparent;
    padding-top: 8px;
  }

  input {
    position: absolute;
    top: 0;
    box-sizing: border-box;
    @media ${m.U.tablet} {
      //caret-color: transparent;
      font-size: 72px;
      height: auto;
    }
    padding-top: 0;
    margin-top: 0;
    padding-left: 0;
    width: 100%;
    font-family: Stelvio, Arial, sans-serif;
    font-weight: 500;
    font-size: 32px;
    //border: 1px solid green;
    border: none;
    caret-color: #F45D3C;
    background-color: transparent;
    z-index: 10;

    &:focus {
      outline: none;
      background-color: transparent;
    }

    &::placeholder {
      color: rgba(244, 93, 60, 0.25);
    }
  }

  // Permet de cacher le bas du caret 
  // (trop long avec la font Stelvio)
  &:after {
    content: '';
    display: inline-block;
    box-sizing: border-box;
    position: absolute;
    bottom: 0;
    left: 0;
    height: 12px;
    width: 100%;
    background-color: var(--soft-white);
    z-index: 20;

    @media ${m.U.tablet} {
      height: 20px;
    }
  }

  p {
    position: absolute;
    margin-top: 0;
    margin-bottom: 0;
    height: 48px;
    width: 100%; // à suprimer pour faire fonctionner le caret
    top: 0;
    left: 0;
    font-family: Stelvio, Arial, sans-serif;
    font-weight: 500;
    font-size: 32px;
    color: rgba(244, 93, 60, 0.5);
    z-index: 0;

    @media ${m.U.tablet} {
      font-size: 72px;
    }
  }

  .search-btn {
    position: absolute;
    top: 0;
    right: 4px;
    z-index: 100;

    svg {
      //background-color: rgba(138, 43, 226, 0.76);
      height: 24px;
      width: 24px;
    }

    @media ${m.U.tablet} {
      top: 12px;
      right: 10px;
      svg {
        height: 45px;
        width: 45px;
      }
    }
  }

  .filter-label {
    position: absolute;
    left: 0;
    padding: 4px 8px 4px 8px;
    color: #f4f4f4;
    background-color: black;
    top: -36px;
    font-size: 0.65rem;
    font-weight: 400;
    font-family: Arial, sans-serif;

    &:before {
      content: '';
      position: absolute;
      top: 100%;
      left: 0;
      transform: rotate(90deg);
      border-bottom: 12px solid transparent;
      border-left: 12px solid #121212;
    }

    @media ${m.U.tablet} {
      font-size: 0.75rem;
      top: -46px;
      padding: 5px 10px 5px 10px;
      &:before {
        border-bottom: 18px solid transparent;
        border-left: 18px solid #121212;
      }
    }
  }
`,SearchSVG=()=>p.jsx("div",{className:"search-btn",children:(0,p.jsxs)("svg",{width:"50",height:"50",viewBox:"0 0 50 49",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[p.jsx("path",{d:"M35.0234 36.1094L47.8259 48.912L49.2401 47.4977L36.4376 34.6952L35.0234 36.1094Z",fill:"black"}),p.jsx("circle",{cx:"21.6367",cy:"21.3087",r:"20.1035",stroke:"black",strokeWidth:"2"})]})}),b={type:"Dans",author:"Par",topics:"Dans"},filter_bar=({listState:e,changeList:t,changeFilter:i})=>{let[r,a]=(0,c.useState)(null),handleClick=e=>{let i=new Date().getTime();(!r||i-r>500)&&(a(i),t(e))};return p.jsx(v,{children:(0,p.jsxs)(w,{filterActive:e.activeFilter,className:e.filterOpen?"removing":"",children:[(0,p.jsxs)(y,{children:[(0,p.jsxs)(j,{active:"type"===e.activeList&&e.listOpen,onClick:()=>handleClick("type"),children:[p.jsx("p",{children:"Type"}),p.jsx(ChevArrow,{})]}),(0,p.jsxs)(j,{active:"author"===e.activeList&&e.listOpen,onClick:()=>handleClick("author"),children:[p.jsx("p",{children:"Auteur"}),p.jsx(ChevArrow,{})]}),(0,p.jsxs)(j,{active:"topic"===e.activeList&&e.listOpen,onClick:()=>handleClick("topics"),children:[p.jsx("p",{children:"Th\xe8me"}),p.jsx(ChevArrow,{})]})]}),e.activeFilter&&p.jsx(k,{onClick:()=>i(null,null),children:(0,p.jsxs)("p",{className:e.filterOpen?"":"erase",children:[(0,p.jsxs)("span",{children:[b[e.filterType],": "]}),e.activeFilter,p.jsx("span",{className:e.queryTerms.length?"eraser":"ready eraser",children:p.jsx(TabArrow,{})})]})})]})})},v=h().div.withConfig({componentId:"sc-f3b5b2aa-0"})`
  position: relative;
  box-sizing: content-box;
  height: 30px;
  width: 100%;
  overflow: hidden;
  border-top: 1.5px solid black;
  margin-top: -12px;

  .removing {
    transition-delay: 0ms;
  }

  .ready {
    transform: translateX(-12px);
  }

  z-index: 20;

  @media ${m.U.tablet} {
    height: 52px;
    margin-top: -16px;
    border-top: 2px solid black;
  }
`,w=h().div.withConfig({componentId:"sc-f3b5b2aa-1"})`
  position: absolute;
  width: 100%;
  top: ${e=>e.filterActive?"-30px":"0"};
  left: 0;
  transition: all 550ms cubic-bezier(1, 0.72, 0.15, 1.01);
  transition-delay: 250ms;

  @media ${m.U.tablet} {
    top: ${e=>e.filterActive?"-52px":"0"};
  }
`,y=h().div.withConfig({componentId:"sc-f3b5b2aa-2"})`
  position: relative;
  width: 100%;
  height: 30px;
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow: hidden;

  @media ${m.U.tablet} {
    height: 52px;
  }
`,j=h().div.withConfig({componentId:"sc-f3b5b2aa-3"})`
  position: relative;
  width: calc(33% + 32px);
  height: 100%;
  padding-left: 16px;
  margin-left: -16px;
  border-left: 1.5px solid black;
  cursor: pointer;

  @media ${m.U.tablet} {
    border-left: 2px solid black;
  }

  &:hover {
    svg {
      path {
        fill: orangered;
      }
    }
  }

  p {
    position: relative;
    margin-top: 8px;
    font-size: 16px;
    font-family: "Stelvio Grotesk", sans-serif;
    font-weight: 500;
  }

  svg {
    position: absolute;
    right: 26px;
    top: 10px;
    transform: rotateX(${e=>e.active?"180deg":0}) scale(0.65);
    transition: transform 350ms cubic-bezier(1, 0.72, 0.15, 1.01);
  }

  &:last-child {
    svg {
      right: 10px;
    }
  }

  @media ${m.U.tablet} {
    p {
      margin-top: 16px;
      font-size: 26px;
    }

    svg {
      top: 22px;
      right: 40px;
      transform: rotateX(${e=>e.active?"180deg":0}) scale(1);
    }

    &:last-child {
      svg {
        right: 10px;
      }
    }
  }
`,k=h().div.withConfig({componentId:"sc-f3b5b2aa-4"})`
  position: absolute;
  top: 30px;
  width: 100%;
  height: 30px;

  &:hover {
    .eraser {
      transform: translateX(-16px);
    }
  }

  .eraser {
    position: absolute;
    top: 2px;
    left: 100%;
    margin-left: 24px;
    display: inline-block;
    height: 100%;
    width: calc(100% + 300px);
    background-color: var(--soft-white);
    transition: transform 200ms ease-out;

    svg {
      margin-top: -5px;
      width: 30px;
    }
  }

  .erase {
    .eraser {
      transition: transform 550ms cubic-bezier(1, 0.72, 0.15, 1.01);
      transform: translateX(-100%);
    }
  }

  p {
    position: relative;
    display: inline-block;
    font-family: "Stelvio Grotesk", Arial, sans-serif;
    font-weight: 500;
    font-size: 16px;
    margin-top: 8px;
    cursor: pointer;

    span {
      font-weight: 700;
    }
  }

  @media ${m.U.tablet} {
    top: 52px;
    height: 52px;

    .eraser {
      svg {
        margin-top: 0;
        width: auto;
      }
    }

    p {
      margin-top: 16px;
      font-size: 26px;
    }
  }
`,TabArrow=()=>(0,p.jsxs)("svg",{width:"50",height:"20",viewBox:"0 0 50 21",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[p.jsx("path",{d:"M2.51012 10.8755L2.10624 9.96067L2.14632 11.807L2.51012 10.8755ZM49.1318 9.87549L2.51012 9.87549L2.51012 11.8755L49.1318 11.8755L49.1318 9.87549ZM2.51012 10.8755C2.14632 11.807 2.14611 11.8069 2.14591 11.8068C2.14586 11.8068 2.14567 11.8067 2.14557 11.8067C2.14535 11.8066 2.14518 11.8065 2.14505 11.8065C2.14478 11.8064 2.14467 11.8063 2.14471 11.8063C2.1448 11.8064 2.1455 11.8066 2.14681 11.8072C2.14945 11.8082 2.15452 11.8103 2.16198 11.8133C2.1769 11.8194 2.20136 11.8295 2.23488 11.8438C2.30193 11.8724 2.40521 11.9178 2.54093 11.9811C2.81243 12.1078 3.21343 12.3063 3.71381 12.5867C4.71494 13.1477 6.11131 14.0351 7.66351 15.3288L8.94401 13.7925C7.28483 12.4096 5.78437 11.4544 4.69153 10.842C4.14492 10.5357 3.69964 10.3147 3.38661 10.1687C3.23007 10.0956 3.10652 10.0413 3.01981 10.0043C2.97645 9.98578 2.94229 9.97162 2.91782 9.96163C2.90558 9.95663 2.89577 9.95268 2.88843 9.94975C2.88476 9.94829 2.88171 9.94708 2.87929 9.94612C2.87808 9.94564 2.87703 9.94523 2.87613 9.94488C2.87568 9.9447 2.87527 9.94454 2.87491 9.94439C2.87472 9.94432 2.87447 9.94423 2.87438 9.94419C2.87414 9.9441 2.87392 9.94401 2.51012 10.8755ZM7.66351 15.3288C9.13958 16.5591 9.99185 17.8891 10.4743 18.9018C10.7157 19.4087 10.864 19.835 10.9506 20.1274C10.9939 20.2734 11.0216 20.3855 11.0378 20.4569C11.0459 20.4925 11.0511 20.518 11.0539 20.5323C11.0553 20.5394 11.0561 20.5438 11.0564 20.5453C11.0565 20.5461 11.0565 20.5461 11.0564 20.5454C11.0564 20.545 11.0563 20.5445 11.0561 20.5438C11.0561 20.5434 11.056 20.543 11.0559 20.5425C11.0559 20.5423 11.0558 20.5419 11.0558 20.5418C11.0557 20.5414 11.0557 20.541 12.0419 20.3755C13.0281 20.21 13.028 20.2095 13.0279 20.2091C13.0279 20.2089 13.0278 20.2085 13.0278 20.2082C13.0277 20.2075 13.0275 20.2068 13.0274 20.2061C13.0272 20.2046 13.0269 20.2029 13.0266 20.201C13.0259 20.1973 13.0251 20.1928 13.0241 20.1875C13.0222 20.1769 13.0197 20.1634 13.0164 20.1469C13.01 20.1139 13.0007 20.0693 12.9881 20.0139C12.963 19.9032 12.9245 19.7492 12.8682 19.5592C12.7557 19.1795 12.5716 18.6541 12.2799 18.0417C11.6961 16.8162 10.6793 15.2388 8.94401 13.7925L7.66351 15.3288ZM2.51012 10.8755C2.91399 11.7903 2.91412 11.7902 2.91427 11.7902C2.91434 11.7901 2.91451 11.7901 2.91464 11.79C2.91491 11.7899 2.91524 11.7897 2.91562 11.7896C2.91639 11.7892 2.91737 11.7888 2.91857 11.7883C2.92098 11.7872 2.92425 11.7857 2.92838 11.7839C2.93663 11.7802 2.94828 11.775 2.96319 11.7683C2.993 11.7548 3.03582 11.7353 3.09041 11.7099C3.19957 11.6593 3.35598 11.5855 3.54965 11.4905C3.93656 11.3007 4.47456 11.0254 5.08316 10.681C6.28637 10.0002 7.82289 9.01787 9.00335 7.86213L7.60417 6.43304C6.60801 7.40835 5.24771 8.28995 4.09825 8.94034C3.53052 9.26158 3.02822 9.51863 2.66892 9.69486C2.48949 9.78287 2.34631 9.85043 2.24916 9.89547C2.2006 9.91799 2.16359 9.93485 2.13935 9.94581C2.12723 9.95129 2.11831 9.95529 2.11274 9.95778C2.10996 9.95902 2.10801 9.95989 2.10692 9.96037C2.10637 9.96061 2.10604 9.96076 2.10593 9.96081C2.10587 9.96083 2.10587 9.96084 2.10592 9.96081C2.10595 9.9608 2.10603 9.96076 2.10604 9.96076C2.10613 9.96072 2.10624 9.96067 2.51012 10.8755ZM9.00335 7.86213C10.1921 6.69825 11.1815 5.15892 11.8604 3.95298C12.2042 3.3424 12.4772 2.80121 12.6649 2.41154C12.7588 2.21647 12.8316 2.05871 12.8816 1.94846C12.9065 1.89332 12.9258 1.85 12.9391 1.81979C12.9457 1.80468 12.9509 1.79284 12.9545 1.78443C12.9564 1.78022 12.9578 1.77687 12.9589 1.7744C12.9594 1.77316 12.9598 1.77214 12.9602 1.77135C12.9604 1.77095 12.9605 1.77061 12.9606 1.77032C12.9607 1.77018 12.9608 1.77 12.9608 1.76993C12.9609 1.76977 12.9609 1.76963 12.0419 1.37549C11.1228 0.981341 11.1229 0.981223 11.1229 0.98112C11.1229 0.981102 11.123 0.981013 11.123 0.98098C11.123 0.980916 11.123 0.980904 11.123 0.980948C11.123 0.981036 11.1228 0.981343 11.1226 0.981865C11.1222 0.982909 11.1213 0.984814 11.1201 0.987563C11.1177 0.99306 11.1139 1.00193 11.1086 1.014C11.0979 1.03816 11.0815 1.07516 11.0595 1.12374C11.0155 1.22093 10.9494 1.36428 10.8629 1.54385C10.6897 1.90347 10.4364 2.40574 10.1177 2.9718C9.4717 4.11914 8.59202 5.46587 7.60417 6.43304L9.00335 7.86213Z",fill:"black"}),p.jsx("line",{x1:"1.2666",y1:"1.18481",x2:"1.2666",y2:"20.5662",stroke:"black",strokeWidth:"2"})]}),ChevArrow=()=>p.jsx("svg",{width:"20",height:"12",viewBox:"0 0 20 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:p.jsx("path",{d:"M10.2925 10.9345L9.37766 11.3384L11.224 11.2983L10.2925 10.9345ZM9.29248 9.23675L9.29248 10.9345L11.2925 10.9345L11.2925 9.23675L9.29248 9.23675ZM10.2925 10.9345C11.224 11.2983 11.2239 11.2985 11.2238 11.2987C11.2238 11.2988 11.2237 11.299 11.2237 11.2991C11.2236 11.2993 11.2235 11.2994 11.2235 11.2996C11.2234 11.2998 11.2233 11.3 11.2233 11.2999C11.2234 11.2998 11.2236 11.2991 11.2242 11.2978C11.2252 11.2952 11.2273 11.2901 11.2303 11.2826C11.2364 11.2677 11.2465 11.2433 11.2608 11.2097C11.2894 11.1427 11.3348 11.0394 11.3981 10.9037C11.5248 10.6322 11.7233 10.2312 12.0037 9.7308C12.5647 8.72967 13.4521 7.3333 14.7458 5.7811L13.2095 4.5006C11.8266 6.15978 10.8714 7.66024 10.259 8.75309C9.95265 9.2997 9.73174 9.74498 9.58568 10.058C9.51264 10.2146 9.45827 10.3381 9.42127 10.4248C9.40277 10.4682 9.38861 10.5023 9.37862 10.5268C9.37363 10.539 9.36967 10.5489 9.36674 10.5562C9.36528 10.5599 9.36407 10.5629 9.36311 10.5653C9.36264 10.5665 9.36222 10.5676 9.36187 10.5685C9.36169 10.5689 9.36153 10.5694 9.36139 10.5697C9.36132 10.5699 9.36122 10.5702 9.36118 10.5702C9.36109 10.5705 9.361 10.5707 10.2925 10.9345ZM14.7458 5.7811C15.9761 4.30503 17.3061 3.45276 18.3188 2.97033C18.8256 2.72891 19.252 2.58064 19.5444 2.49402C19.6904 2.45075 19.8025 2.42302 19.8739 2.40681C19.9095 2.39871 19.9349 2.39351 19.9493 2.3907C19.9564 2.38929 19.9608 2.38848 19.9623 2.38821C19.9631 2.38807 19.9631 2.38807 19.9624 2.3882C19.962 2.38826 19.9615 2.38835 19.9607 2.38848C19.9604 2.38854 19.96 2.38861 19.9595 2.38869C19.9593 2.38873 19.9589 2.38879 19.9588 2.38881C19.9584 2.38887 19.958 2.38894 19.7925 1.40274C19.627 0.416532 19.6265 0.416603 19.6261 0.416677C19.6259 0.416704 19.6255 0.41678 19.6252 0.416835C19.6245 0.416944 19.6238 0.417063 19.6231 0.41719C19.6216 0.417445 19.6199 0.417736 19.618 0.418064C19.6143 0.418719 19.6098 0.419522 19.6045 0.420481C19.5939 0.422398 19.5804 0.424941 19.5639 0.428177C19.5309 0.434647 19.4863 0.443896 19.4309 0.456473C19.3202 0.481619 19.1662 0.520122 18.9762 0.576414C18.5965 0.688915 18.0711 0.873037 17.4587 1.16472C16.2332 1.7485 14.6558 2.76529 13.2095 4.5006L14.7458 5.7811ZM10.2925 10.9345C11.2073 10.5306 11.2072 10.5305 11.2072 10.5303C11.2071 10.5303 11.2071 10.5301 11.207 10.53C11.2069 10.5297 11.2067 10.5294 11.2066 10.529C11.2062 10.5282 11.2058 10.5273 11.2053 10.526C11.2042 10.5236 11.2027 10.5204 11.2009 10.5162C11.1972 10.508 11.192 10.4963 11.1852 10.4814C11.1718 10.4516 11.1522 10.4088 11.1269 10.3542C11.0763 10.2451 11.0025 10.0886 10.9075 9.89497C10.7177 9.50806 10.4424 8.97006 10.098 8.36146C9.41721 7.15825 8.43486 5.62172 7.27912 4.44126L5.85003 5.84044C6.82534 6.8366 7.70694 8.1969 8.35733 9.34636C8.67857 9.9141 8.93562 10.4164 9.11185 10.7757C9.19986 10.9551 9.26743 11.0983 9.31247 11.1955C9.33498 11.244 9.35185 11.281 9.3628 11.3053C9.36828 11.3174 9.37228 11.3263 9.37477 11.3319C9.37601 11.3347 9.37688 11.3366 9.37736 11.3377C9.37761 11.3382 9.37775 11.3386 9.3778 11.3387C9.37783 11.3387 9.37783 11.3388 9.37781 11.3387C9.37779 11.3387 9.37776 11.3386 9.37775 11.3386C9.37771 11.3385 9.37766 11.3384 10.2925 10.9345ZM7.27912 4.44126C6.11524 3.25249 4.57592 2.26312 3.36998 1.58417C2.75939 1.24041 2.2182 0.967376 1.82853 0.779742C1.63346 0.685808 1.47571 0.612967 1.36545 0.563062C1.31031 0.538102 1.26699 0.518856 1.23678 0.505555C1.22167 0.498904 1.20983 0.493737 1.20142 0.490083C1.19721 0.488256 1.19386 0.486808 1.19139 0.48574C1.19016 0.485207 1.18914 0.484768 1.18834 0.484426C1.18794 0.484255 1.1876 0.484107 1.18731 0.483984C1.18717 0.483923 1.187 0.483848 1.18692 0.483817C1.18677 0.483749 1.18662 0.483687 0.792477 1.40274C0.398334 2.32179 0.398216 2.32173 0.398113 2.32169C0.398096 2.32168 0.398006 2.32164 0.397974 2.32163C0.397909 2.3216 0.397898 2.3216 0.397942 2.32162C0.398031 2.32165 0.398336 2.32179 0.398859 2.32201C0.399902 2.32246 0.401808 2.32329 0.404556 2.32448C0.410053 2.32687 0.418919 2.33073 0.430998 2.33605C0.455158 2.34669 0.492151 2.36311 0.540729 2.3851C0.63792 2.4291 0.781275 2.49525 0.960844 2.58172C1.32047 2.75488 1.82273 3.00826 2.38879 3.32695C3.53614 3.97291 4.88286 4.85259 5.85003 5.84044L7.27912 4.44126Z",fill:"black"})});var S=i(9667);let LetterSeparator=({word:e,prevWord:t})=>e.charAt(0)!==t.charAt(0)?p.jsx("li",{className:"letter-separator",children:e.charAt(0)},`s-${e.charAt(0)}`):null,filter_list=({data:e,fieldName:t,changeFilter:i,haveDisplayName:r,separator:a=!0})=>{let o=r?"displayName":t;return e.sort((0,S.IQ)(o)),p.jsx(L,{noCol:!a,children:e.map((r,l)=>(void 0==r.postCount||r?.postCount>0)&&(0,p.jsxs)(c.Fragment,{children:[0!==l&&a&&p.jsx(LetterSeparator,{word:r[o],prevWord:e[l-1][o]}),p.jsx("li",{onClick:()=>i(r[t]),children:p.jsx("span",{className:"list-filter",children:r[o]})},l)]},l))})},L=h().ul.withConfig({componentId:"sc-d0a18ee-0"})`
  position: relative;
  width: 100%;
  column-width: ${e=>e.noCol?"inherit":"320px"};
  padding: 20px;
  margin-top: -100vh;
  z-index: 999;

  li {
    position: relative;
    width: auto;
    margin-bottom: 8px;
    margin-top: 0;
    list-style: none;
    font-family: "Stelvio Grotesk", Arial, sans-serif;
    font-size: 16px;
    color: #262626;
    height: 40px;
  }

  .list-filter {
    position: relative;
    margin-left: -12px;
    padding: 8px 12px 0 12px;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 400;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1; /* number of lines to show */
    line-clamp: 1;
    -webkit-box-orient: vertical;
    color: #f4f4f4;

    &:hover {
      opacity: 0.7;
    }
  }

  .letter-separator {
    position: relative;
    margin-left: -4px;
    font-family: "Stelvio Grotesk", Arial, sans-serif;
    font-weight: 500;
    font-size: 32px;
    height: 88px;
    color: rgba(255, 255, 255, 0.29);
  }

  @media ${m.U.tablet} {
    padding: 60px;
    li {
      font-size: 26px;
    }

    .letter-separator {
      font-size: 72px;
      margin-top: 56px;
    }
  }
`;var N=i(6068);function getInitialState(e){return{activeList:e?.filter?.type?e.filter.type:"type",activeFilter:e?.filter?.value,filterType:e?.filter?.type,queryTerms:e?.terms?e.terms:"",listOpen:!1,filterOpen:!!e?.filter?.type,listVisible:!1}}let search_tool=({setQuery:e,initQuery:t,autoFocus:i=!1})=>{let r=(0,N.o)();r.types=[{name:"Podcast",displayName:"Podcasts"},{name:"Formation",displayName:"Formations"},{name:"Article",displayName:"Articles"},{name:"Webinaire",displayName:"Webinaires"},{name:"Parcours",displayName:"Parcours"}];let[a,o]=(0,c.useState)(getInitialState(t));return(0,c.useEffect)(()=>{a!==getInitialState(t)&&e({terms:a.queryTerms,filter:{value:a.activeFilter,type:a.filterType},page:0})},[a.activeFilter,a.queryTerms]),(0,c.useEffect)(()=>{setTimeout(()=>{o(e=>({...e,listVisible:a.listOpen}))},a.listOpen?350:0)},[a.listOpen]),(0,p.jsxs)(c.Fragment,{children:[p.jsx(big_input,{changeFilter:changeFilter,listState:a,changeQueryTerms:function(e){o(t=>({...t,queryTerms:e}))},autoFocus:i}),p.jsx(filter_bar,{setListState:o,listState:a,changeList:function(e){if(a.activeList===e&&a.listOpen){closeList();return}o(t=>({...t,activeList:e,listOpen:!0}))},changeFilter:changeFilter}),(0,p.jsxs)(F,{open:a.listOpen,children:[p.jsx(z,{open:a.listOpen}),(0,p.jsxs)("div",{className:a.listVisible?"lists visible":"lists",children:["author"===a.activeList&&a.listVisible&&p.jsx(filter_list,{separator:!0,changeFilter:changeFilter,haveDisplayName:!0,data:r.authors.map(e=>({displayName:e.lastName+" "+e.firstName,fullName:e.fullName})),fieldName:"fullName"}),"topics"===a.activeList&&a.listVisible&&p.jsx(filter_list,{separator:!0,changeFilter:changeFilter,data:r.topics,fieldName:"name"}),"type"===a.activeList&&a.listVisible&&p.jsx(filter_list,{separator:!1,changeFilter:changeFilter,data:r.types,haveDisplayName:!0,fieldName:"name"})]})]})]});function closeList(){window.scrollTo({top:0}),o(e=>({...e,listOpen:!1}))}function changeFilter(e,t=a.activeList,i=!1){e||o(e=>({...e,queryTerms:i||e.queryTerms,filterOpen:!1})),setTimeout(()=>{o(i=>({...i,activeFilter:e,filterType:t,filterOpen:!1!==e}))},e?0:250),closeList()}},F=h().div.withConfig({componentId:"sc-137cca13-0"})`
  position: relative;
  width: 100%;

  div {
    width: 100%;
    //background-color: #EF4523;
    //background-color: black;
  }
`,z=h().div.withConfig({componentId:"sc-137cca13-1"})`
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  height: ${e=>e.open?"100":"0"}vh;
  transform-origin: top;
  background-color: #161616;
  //background-color: #E9EDFB;
  //background-color: #F45D3C;
  transform: ${e=>e.open?"scaleY(1)":"scaleY(0)"};
  transition: all 650ms cubic-bezier(1, 0.72, 0.15, 1.01);
  z-index: 10;
`;var P=i(4092);let search_paginate=({nbHits:e,currentPage:t,changePage:i})=>{let r=(0,P.Z)(e,t,10,5);return(0,p.jsxs)($,{children:[r.startPage>1&&(0,p.jsxs)(p.Fragment,{children:[p.jsx(q,{onClick:()=>i(1),children:1}),p.jsx(A,{children:"..."})]}),r.pages.map((e,t)=>p.jsx(q,{onClick:()=>i(e),active:e===r.currentPage,children:e},t)),r.totalPages!==r.endPage&&(0,p.jsxs)(p.Fragment,{children:[p.jsx(A,{children:"..."}),p.jsx(q,{active:r.currentPage===r.endPage,onClick:()=>i(r.totalPages),children:r.totalPages})]})]})},$=h().div.withConfig({componentId:"sc-dbe3a1d-0"})`
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: end;
  height: 40px;
  width: 100%;
  color: white;
  font-size: 26px;
  margin: 72px auto;
  transform: scale(0.8);
  @media ${m.U.tablet} {
    transform: scale(1);
  }
`,A=h().span.withConfig({componentId:"sc-dbe3a1d-1"})`
  color: black;
`,q=h().span.withConfig({componentId:"sc-dbe3a1d-2"})`
  display: inline-block;
  height: 42px;
  width: 42px;
  margin: auto 10px;
  font-size: 26px;
  font-family: Stelvio, sans-serif;
  border-radius: 100px;
  padding-top: 10px;
  text-align: center;
  color: ${e=>e.active?"white":"black"};
  background-color: ${e=>e.active?"#080808":"none"};

  &:hover {
    background-color: transparent;
    cursor: pointer;
    border: 1.5px solid black;
  }
`;var _=i(2924),I=i(9296),T=i(9114),D=i(1385),U=i(2558),Z=i(3169);function AuthorCard({authorName:e}){let[t,i]=(0,c.useState)(!1),[r,a]=(0,c.useState)();return((0,c.useEffect)(()=>{(async function(){let t=await D.Z.query({query:W,variables:{authorName:e}}).then(e=>e.data.authors),r=await D.Z.query({query:B}).then(e=>e.data.blogs);a(t[0]);let o=r.map(e=>e.slug).indexOf(t[0]?.slug);-1!=o&&i(!0)})()},[e]),void 0===r)?p.jsx(p.Fragment,{}):(0,p.jsxs)(O,{children:[p.jsx(E,{children:p.jsx("div",{className:"author-picture",children:p.jsx(U.Z,{imageData:r?.picture})})}),p.jsx(M,{children:r?.fullName?.trim()}),p.jsx(V,{children:r?.about}),t&&p.jsx(Q,{children:p.jsx(Z.Z,{type:"soft-white",text:"Voir le blog",url:`/blog/${r?.slug}`})})]})}let O=h().div.withConfig({componentId:"sc-fbd4eae6-0"})`
  background-color: #111111;
  color: white;
  padding:39px;
  width: 100%;
`,E=h().div.withConfig({componentId:"sc-fbd4eae6-1"})`
    display: none;
    flex-direction: row;
    margin-right: 16px;
    margin-left: 10px;
    margin-bottom: 16px;
    .author-picture {
      position: relative;
      height: 81px;
      width: 81px;
      border-radius: 80px;
      margin-left: -10px;
      overflow: hidden;
      background-color: #161616;
    }
    
    @media ${m.U.tablet} {
      display: flex;
    }
`,M=h().h2.withConfig({componentId:"sc-fbd4eae6-2"})`
  margin-bottom: 16px;
  color: #F1F1F1;
  font-family: Stelvio, sans-serif;
  font-size: 1.25rem;
  font-size: clamp(1.25rem, 1.178635147190009rem + 0.35682426404995543vw, 1.5rem);
`,V=h().p.withConfig({componentId:"sc-fbd4eae6-3"})`
  margin-bottom: 16px;
  color: #7A7A7A;
  font-family: Stelvio, sans-serif;
  font-size: 1rem;
  font-size: clamp(1rem, 0.928635147190009rem + 0.35682426404995543vw, 1.25rem);
  
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;

  
  @media ${m.U.tablet} {
    -webkit-line-clamp: 4 !important;
  }
  @media ${m.U.desktop} {
    -webkit-line-clamp: 5 !important;
  }
`,Q=h().p.withConfig({componentId:"sc-fbd4eae6-4"})`
  padding-top: 16px;
`,W=T.gql`
  query QueryAuthor($authorName: String!){
    authors(where: { fullName: $authorName} ){
      lastName
    	firstName
      fullName
      about
      slug
      picture {
        url
        width
        height
        provider
        alternativeText
        alternativeText
      }
    }
  }
`,B=T.gql`
  query QueryBloggers {
    blogs {
      slug
    }
  }
`;i(2832);let G=["author","topics","type"];function SearchPage({initialQuery:e,initialResults:t,initialCornerStones:i}){let r=(0,f.useRouter)(),[a,o]=(0,c.useState)(e),[l,s]=(0,c.useState)(t),[n,d]=(0,c.useState)(i),updateUrl=e=>{let t="";t+=e.terms?`?terms=${e.terms}`:"",t+=e.filter.value&&e.filter.type?`${t.length?"&":"?"}${e.filter.type}=${e.filter.value}`:"",t+=e.page?`${t.length?"&":"?"}page=${e.page}`:"";let i=`/recherche${t}`;r.push(i,void 0,{shallow:!0})};return(0,c.useEffect)(()=>{async function fetchData(){updateUrl(a);let e=prepareQueryCornerStone(a),t=await u.V.search(e.terms,e.params);d(t);let i=prepareQuery(a),r=await u.V.searchHighlight(i.terms,i.params);r.hits=r?.hits?.filter(e=>!t?.hits?.find(t=>t.slug===e.slug)),s(r)}1>+a.page||+a.page>1&&0===l.totalPages?o(e=>({...e,page:1})):+a.page>l.totalPages&&l.totalPages,a!==e&&fetchData()},[a]),(0,p.jsxs)(H,{className:"site-padding",children:[p.jsx(search_tool,{setQuery:o,initQuery:a,autoFocus:!0}),p.jsx(X,{children:(0,p.jsxs)("div",{className:"center-results",children:[p.jsx(R,{children:(0,p.jsxs)("h2",{children:[l.totalHits," R\xe9sultat",l.totalHits>1&&"s"]})}),(0,p.jsxs)(K,{children:[(0,p.jsxs)("div",{className:"all-posts",children:[l?.hits.map((e,t)=>p.jsx(_.Z,{post:e,options:{showAuthor:!0,showDate:!0,showTopics:!0}},e.image?.src+""+t)),l?.hits?.length>0&&p.jsx(search_paginate,{nbHits:l.totalHits,currentPage:a.page||1,changePage:function(e){window.scrollTo({top:0}),o(t=>({...t,page:e}))}})]}),p.jsx("div",{className:"all-cs",children:(0,p.jsxs)("div",{className:"cornerstone-container",children:[a.filter?.type==="author"&&p.jsx(AuthorCard,{authorName:a.filter.value}),n?.hits?.map((e,t)=>(e.link=e?.cta?.url,p.jsx(I.Z,{post:e,options:{showAuthor:!0}},t)))]})})]})]})})]})}function prepareQuery(e){let t=[],i=+e.page;return e.filter?.type&&e.filter?.value&&(t=[""+e.filter.type+' ="'+e.filter.value.trim()+'"']),{terms:e.terms||null,params:{filter:t,page:i,sort:["date:desc"],hitsPerPage:10}}}function prepareQueryCornerStone(e){let t=[],i="",r=4;return e.filter?.type&&e.filter?.value?(t.push(""+e?.filter.type+' = "'+e?.filter.value.trim()+'"'),e?.filter?.type!=="author"&&e?.filter?.type!=="author"?i=e.terms||null:r=3):i=e.terms||null,t.push("cs=true"),{terms:i,params:{filter:t,sort:["date:desc"],limit:r}}}async function getServerSideProps({query:e}){let t=function(e){let t={terms:e.terms||null,filter:{value:null,type:null},page:1>+e.page?0:+e.page};for(let i in e)G.includes(i)&&(t.filter={value:e[i],type:i});return t}(e),i=prepareQuery(t),r=await u.V.searchHighlight(i.terms,i.params),a=prepareQueryCornerStone(t),o=await u.V.searchHighlight(a.terms,a.params);return r.hits=r?.hits?.filter(e=>!o?.hits?.find(t=>t.slug===e.slug)),{props:{initialQuery:t,initialResults:r,initialCornerStones:o}}}let H=h().div.withConfig({componentId:"sc-441d5efd-0"})`
  position: relative;
  width: 100%;
  padding-top: 48px;
`,R=h().div.withConfig({componentId:"sc-441d5efd-1"})`
  display: flex;
  flex-direction: row;
  margin-top: 72px;
  margin-bottom: 40px;
  width: 100%;

  h2 {
    margin-top: 0;
    font-size: 20px;
    font-family: "Switzer", sans-serif;
    margin-bottom: 0;
    font-weight: 600;
  }

  @media ${m.U.tablet} {
    //margin-bottom: 42px;
  }
`,X=h().div.withConfig({componentId:"sc-441d5efd-2"})`
  display: flex;
  justify-content: center;
  .center-results {
    width: 100%;
  }
  /* @media ${m.U.desktop} {
    .center-results{
      width: 85%;
    }
  } */
`,K=h().section.withConfig({componentId:"sc-441d5efd-3"})`
  position: relative;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .all-posts {
    width: 100%;
  }
  .all-cs {
    width: 100%;
  }
  .cornerstone-container {
    /* position: sticky;
    top: 20px; */
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    justify-content: center;
    margin-bottom: 40px;
  }
  @media ${m.U.desktop} {
    .all-posts {
      width: 65.23%;
      padding-right: 7.9%;
      //border-right: 2px solid #DEDCD8;
    }

    .all-cs {
      width: 30.16%;
    }
  }
`,Y=(0,l.l)(r,"default"),J=(0,l.l)(r,"getStaticProps"),ee=(0,l.l)(r,"getStaticPaths"),et=(0,l.l)(r,"getServerSideProps"),ei=(0,l.l)(r,"config"),er=(0,l.l)(r,"reportWebVitals"),ea=(0,l.l)(r,"unstable_getStaticProps"),eo=(0,l.l)(r,"unstable_getStaticPaths"),el=(0,l.l)(r,"unstable_getStaticParams"),es=(0,l.l)(r,"unstable_getServerProps"),en=(0,l.l)(r,"unstable_getServerSideProps"),ep=new a.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/recherche",pathname:"/recherche",bundlePath:"",filename:""},components:{App:n.default,Document:s.default},userland:r})},6035:(e,t,i)=>{i.d(t,{a:()=>useMediaQuery});var r=i(6689);let useMediaQuery=({width:e,mediaQuery:t})=>{let[i,a]=(0,r.useState)(!1),o=(0,r.useCallback)(e=>{e.matches?a(!0):a(!1)},[]);return(0,r.useEffect)(()=>{let i=window.matchMedia(e?`(max-width: ${e}px)`:t);return i.addEventListener("change",o),i.matches&&a(!0),()=>i.removeEventListener("change",o)},[]),i}},9114:e=>{e.exports=require("@apollo/client")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},9997:e=>{e.exports=require("meilisearch")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),i=t.X(0,[3181,5016,6859,8450,9755,4033,779,7113,6453,1077,7620,8672,2832,723,4092,9296,3169,2924],()=>__webpack_exec__(1808));module.exports=i})();