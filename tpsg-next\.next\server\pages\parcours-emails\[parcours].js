"use strict";(()=>{var e={};e.id=7512,e.ids=[7512,2888],e.modules={8630:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>h,default:()=>c,getServerSideProps:()=>x,getStaticPaths:()=>m,getStaticProps:()=>u,reportWebVitals:()=>g,routeModule:()=>j,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>f});var s=r(7093),i=r(5244),o=r(1323),n=r(779),l=r(4033),d=r(5468),p=e([d]);d=(p.then?(await p)():p)[0];let c=(0,o.l)(d,"default"),u=(0,o.l)(d,"getStaticProps"),m=(0,o.l)(d,"getStaticPaths"),x=(0,o.l)(d,"getServerSideProps"),h=(0,o.l)(d,"config"),g=(0,o.l)(d,"reportWebVitals"),f=(0,o.l)(d,"unstable_getStaticProps"),y=(0,o.l)(d,"unstable_getStaticPaths"),b=(0,o.l)(d,"unstable_getStaticParams"),S=(0,o.l)(d,"unstable_getServerProps"),P=(0,o.l)(d,"unstable_getServerSideProps"),j=new s.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/parcours-emails/[parcours]",pathname:"/parcours-emails/[parcours]",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:d});a()}catch(e){a(e)}})},5468:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>SingleParcours,getStaticPaths:()=>getStaticPaths,getStaticProps:()=>getStaticProps});var s=r(997),i=r(8657),o=r(9114),n=r(1385),l=r(7518),d=r.n(l),p=r(7672),c=r(1664),u=r.n(c),m=r(7467),x=r(635),h=r(4004),g=r(2350),f=r(6382),y=r(2558),b=r(8672),S=r(6453),P=r(3123),j=r(8102),q=r(6641),w=e([i]);function SingleParcours({post:e,relatedPosts:t}){if(!e)return null;let r=(0,x.fw)(e.modules),a=(0,f.S$)(e.published_at),o=(0,x.MS)(e.modules,"lead");return(0,s.jsxs)($,{children:[s.jsx(q.NextSeo,{title:r?.seo?.metaTitle||null,description:r?.seo?.metaDescription||null}),(0,s.jsxs)("div",{className:"site-padding",children:[s.jsx(h.Z,{hexLight:"#f5e3df",hexDark:"#62187d"}),(0,s.jsxs)("header",{children:[(0,s.jsxs)("div",{className:"header-text-container",children:[s.jsx(S.hQ,{children:s.jsx(u(),{href:"/parcours-emails",children:"PARCOURS E-MAILS"})}),s.jsx(S.DZ,{children:e.title}),s.jsx("div",{className:"post-topics",children:e.topics&&e.topics.map((e,t)=>s.jsx(b.Z,{text:e.name},t))})]}),s.jsx("div",{className:"header-img-container",children:s.jsx(y.Z,{imageData:e.image,priority:!0})})]}),(0,s.jsxs)(g.Z,{children:[s.jsx(g.Z.Text,{label:"Publi\xe9 le",content:a,addClass:""}),s.jsx(g.Z.Authors,{label:"Auteur(s)",authors:[e.author],addClass:""}),s.jsx(g.Z.Social,{url:`https://toutpoursagloire.com/parcours-emails/${e.slug}`,addClass:"mobile-hide_flex"})]}),(0,s.jsxs)(k,{children:[(0,s.jsxs)(Z,{children:[o?.content&&s.jsx("section",{children:s.jsx(i.oJ,{content:o.content})}),s.jsx("section",{children:s.jsx(i.oJ,{content:e.body})})]}),s.jsx(C,{children:r?.journey?.embedForm&&s.jsx(j.Z,{title:"Inscription",formString:r.journey.embedForm})})]})]}),s.jsx(P.Z,{items:t})]})}async function getStaticProps({params:e}){let t=await n.Z.query({query:_,variables:{slug:e.parcours}}).then(e=>e.data.posts[0]);if(!t)return{notFound:!0};let r=await n.Z.query({query:p.o$.QUERY_RELATED,variables:{id:t.id}}).then(e=>e.data.relatedPosts);return{props:{post:t,relatedPosts:r},revalidate:10}}async function getStaticPaths(){let{data:e}=await n.Z.query({query:v});return{paths:e.posts.map(e=>({params:{parcours:e.slug}})),fallback:!0}}i=(w.then?(await w)():w)[0];let v=o.gql`
  query JourneySlugs {
    posts(where: { type: "parcours" }, limit: 10) {
      slug
    }
  }
`,_=o.gql`
  ${p.Pq.CORE_POST_FIELDS}
  query Journey($slug: String!) {
    posts(where: { slug: $slug }) {
      ...CorePostFields
      modules {
        ... on ComponentModuleEmailJourney {
          __typename
          embedForm
        }
      }
    }
  }
`,k=d().main.withConfig({componentId:"sc-615ef80c-0"})`
  margin-top: 70px;
  padding-bottom: 70px;
  display: block;

  @media ${m.U.desktop} {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start; /* Pour que les éléments commencent au haut */
    min-height: 100vh; /* Pour s'assurer que la hauteur est suffisante pour l'effet sticky */
  }
`,Z=d().article.withConfig({componentId:"sc-615ef80c-1"})`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media ${m.U.desktop} {
    width: 50%;
  }
`,C=d().div.withConfig({componentId:"sc-615ef80c-2"})`
  width: 100%;

  @media ${m.U.desktop} {
    padding-left: 40px;
    border-left: 1px solid #dddddd;
    width: 40%;
    position: sticky;
    top: 20px; /* Distance depuis le haut de l'écran */
    align-self: flex-start; /* Important pour sticky dans un conteneur flex */
    max-height: calc(100vh - 40px); /* Hauteur maximale pour éviter de dépasser l'écran */
    // overflow-y: auto; /* Permet le défilement si le formulaire est trop grand */
  }
`,$=d().div.withConfig({componentId:"sc-615ef80c-3"})`
  header {
    display: flex;
    flex-direction: column;
    margin-bottom: 48px;
  }

  .card-label {
    display: none;
    margin: 8px 8px 0px 0px;
  }

  .header-img-container {
    position: relative;
    width: 100%;
    aspect-ratio: 16 / 10;
  }

  @media ${m.U.tablet} {
    .card-label {
      display: inline-block;
    }

    .header-img-container {
      margin-top: 48px;
    }
  }
  @media ${m.U.desktop} {
    header {
      margin-top: 90px;
      margin-bottom: 70px;
      flex-direction: row;
      justify-content: space-between;
    }

    .header-text-container {
      position: relative;
      width: 50%;
      aspect-ratio: 16 / 10;
      padding-top: 24px;
      padding-left: 16px;
    }

    .header-img-container {
      width: calc(50% - 32px);
      margin-top: 0px;
    }
  }
`;a()}catch(e){a(e)}})},9114:e=>{e.exports=require("@apollo/client")},9526:e=>{e.exports=require("dangerously-set-html-content")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},6641:e=>{e.exports=require("next-seo")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},6158:e=>{e.exports=require("react-share")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},3135:e=>{e.exports=import("react-markdown")},1871:e=>{e.exports=import("rehype-raw")},6809:e=>{e.exports=import("remark-gfm")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,9755,5152,4033,779,7113,6453,1077,7620,8672,2832,8657,4004,7482],()=>__webpack_exec__(8630));module.exports=r})();