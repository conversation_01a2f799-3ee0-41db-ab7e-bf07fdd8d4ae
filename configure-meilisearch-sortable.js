/**
 * Script pour configurer les attributs triables de Meilisearch
 * 
 * Ce script configure l'attribut 'date' comme étant triable dans Meilisearch
 * 
 * Pour exécuter ce script :
 * 1. Assurez-vous que Meilisearch est en cours d'exécution
 * 2. Exécutez la commande : node configure-meilisearch-sortable.js
 */

const fetch = require('node-fetch');

const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function configureMeilisearchSortable() {
  try {
    console.log('Configuration des attributs triables de Meilisearch...');

    // Vérifier si l'index existe
    console.log('Vérification de l\'existence de l\'index...');
    const indexesResponse = await fetch(`${MEILI_URL}/indexes`);
    const indexes = await indexesResponse.json();
    
    const indexExists = indexes.results && indexes.results.some(index => index.uid === INDEX_NAME);
    
    if (!indexExists) {
      console.log('L\'index n\'existe pas, création de l\'index...');
      await fetch(`${MEILI_URL}/indexes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uid: INDEX_NAME })
      });
      console.log('Index créé avec succès');
    } else {
      console.log('L\'index existe déjà');
    }

    // Configurer les attributs triables
    console.log('Configuration des attributs triables...');
    const sortableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['date'])
    });

    if (sortableResponse.ok) {
      console.log('Attributs triables configurés avec succès');
    } else {
      console.error('Erreur lors de la configuration des attributs triables:', await sortableResponse.text());
    }

    // Vérifier les attributs triables
    console.log('Vérification des attributs triables...');
    const settingsResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`);
    const settings = await settingsResponse.json();
    console.log('Attributs triables:', settings);

    console.log('Configuration terminée !');
  } catch (error) {
    console.error('Erreur lors de la configuration:', error);
  }
}

configureMeilisearchSortable();
