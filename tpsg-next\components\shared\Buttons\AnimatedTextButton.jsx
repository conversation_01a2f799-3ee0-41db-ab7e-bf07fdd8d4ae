import styled from "styled-components";
import CondLink from "components/shared/CondLink";

/**
 *
 * @param text
 * @param link (optionnel)
 * @param onClickFunction (optionnel)
 * @param theme "light" ou "dark"
 * @return {JSX.Element}
 * @constructor
 */
export default function AnimatedTextButton({ text, link, onClickFunction, theme }) {

  function Button(){
    return <ButtonWrapper
      className={"animated-text-button"}
      text={text}
      onClick={onClickFunction ? () => onClickFunction() : null}
      light={theme === "light"}>
      <p>{text}</p>
    </ButtonWrapper>
  }

  if(link) {
    return <CondLink link={link}><Button/></CondLink>
  } else {
    return <Button/>
  }
}


const ButtonWrapper = styled.button`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 24px 0 24px;
  background-color: transparent;
  border: 1px solid ${p => p.light ? "var(--c-soft-cream)" :  "black"};
  border-radius: 100px;
  font-family: Switzer, serif;
  font-size: 16px;
  cursor: pointer;
  overflow: hidden;
  isolation: isolate;
  line-height: 32px;

  p {
    color: ${p => p.light ? "var(--c-soft-cream)" :  "black"};
    margin: 0;
    height: 100%;
    transition: transform 350ms ease-out;
  }
  
  &:after {
    position: absolute;
    content: '${props => props.text}';
    color: ${p => p.light ? "#081921" :  "var(--c-soft-cream)"};
    background-color: ${p => p.light ? "var(--c-soft-cream)" :  "black"};
    border-radius: 100px;
    top: 100%;
    height: 100%;
    width: 100%;
    transition: transform 350ms ease-out;
  }
  
  &:hover {
    p {
      transform: translateY(-100%);
    }
    &:after {
      transform: translateY(-100%);
    }
  }
`;