'use strict';
const TurndownService = require('turndown');

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#lifecycle-hooks)
 * to customize this model
 */

module.exports = {
    lifecycles: {
        async beforeCreate(data){
            // TODO: supprimer cette partie une fois le transfert termniné.
            // Descritption de l'auteur html -> markdown
            let turndownService = new TurndownService({
              headingStyle: 'atx',
              hr: '---',
              bulletListMarker: '-',
            });
            turndownService.keep(['iframe', 'sup', 'sub', 'script']);
            data.about = turndownService.turndown(data.about);
            data.slug = slugify(data.fullName);
        },
    }
}

function slugify(str) {
    str = str.replace(/^\s+|\s+$/g, '');
  
    // Make the string lowercase
    str = str.toLowerCase();
  
    // Remove accents, swap ñ for n, etc
    var from = "ÁÄÂÀÃÅČÇĆĎÉĚËÈÊẼĔȆÍÌÎÏŇÑÓÖÒÔÕØŘŔŠŤÚŮÜÙÛÝŸŽáäâàãåčçćďéěëèêẽĕȇíìîïňñóöòôõøðřŕšťúůüùûýÿžþÞĐđßÆa·/_,:;";
    var to   = "AAAAAACCCDEEEEEEEEIIIINNOOOOOORRSTUUUUUYYZaaaaaacccdeeeeeeeeiiiinnooooooorrstuuuuuyyzbBDdBAa------";
    for (var i=0, l=from.length ; i<l ; i++) {
      str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
    }
  
    // Remove invalid chars
    str = str.replace(/[^a-z0-9 -]/g, '')
      // Collapse whitespace and replace by -
      .replace(/\s+/g, '-')
      // Collapse dashes
      .replace(/-+/g, '-');
  
    return str;
  }
