"use strict";(()=>{var e={};e.id=6633,e.ids=[6633,2888],e.modules={3600:(e,t,r)=>{r.r(t),r.d(t,{config:()=>U,default:()=>y,getServerSideProps:()=>C,getStaticPaths:()=>$,getStaticProps:()=>k,reportWebVitals:()=>I,routeModule:()=>V,unstable_getServerProps:()=>N,unstable_getServerSideProps:()=>R,unstable_getStaticParams:()=>H,unstable_getStaticPaths:()=>A,unstable_getStaticProps:()=>Z});var a={};r.r(a),r.d(a,{default:()=>PageFiltres,getServerSideProps:()=>getServerSideProps});var s=r(7093),i=r(5244),o=r(1323),l=r(779),p=r(4033),g=r(997),n=r(1385),d=r(9114),u=r(723),c=r(1163),b=r(2705),h=r(7994),m=r(2325),f=r(7518),x=r.n(f),v=r(465),P=r(7467),S=r(6689);function PageFiltres({blog:e,fallback:t}){let r;let a=t?.posts,s=(0,c.useRouter)(),{query:i}=s;(0,S.useEffect)(()=>{let t={};i.topic&&(t.topic=i.topic),1>+i.page?(t.page=1,s.replace({pathname:`/blog/${e.slug}/filtres`,query:t},void 0,{scroll:!1})):+i.page>a?.totalPages&&(t.page=a?.totalPages>0?a.totalPages:1,s.replace({pathname:`/blog/${e.slug}/filtres`,query:t},void 0,{scroll:!1}))},[e.slug,s.query.page]);let o={showLead:!0,showDate:!0,showAuthor:!0},l=i.topic;return g.jsx(q,{children:(0,g.jsxs)(_,{children:[g.jsx(h.Z,{data:e}),(0,g.jsxs)(w,{children:[g.jsx("h1",{children:l}),g.jsx("ul",{className:"list-container",children:a?.hits?.map((e,t)=>g.jsx("li",{className:"post-card-li",children:g.jsx(m.Z,{post:e,options:o})},`post-${t}`))}),g.jsx(v.Z,{nbHits:a?.totalHits,baseUrl:(r=i.topic?"topic":i.type?"type":i.tag?"tag":null,`/blog/${e.slug}/filtres?${r?r+"="+i[r]+"&":""}page=`),currentPage:a?.page,options:{postPerPage:15}})]})]})})}let q=x().div.withConfig({componentId:"sc-615abf4e-0"})`
  position: relative;
  background-color: var(--soft-white);
  @media ${P.U.desktop} {
    .blog-menu {
      
    }
  }
`,_=x().div.withConfig({componentId:"sc-615abf4e-1"})`
  position: relative;
  display: flex;
  
  @media ${P.U.desktop} {
    margin-left: var(--border-space);
    margin-right: var(--border-space);
  }
`,w=x().div.withConfig({componentId:"sc-615abf4e-2"})`
  width: 100%;
  margin-bottom: 64px;
  padding-top: 40px;
  padding-left: var(--mobile-gap);
  padding-right: var(--mobile-gap);
  .list-container {
    padding: 0;
    width: 100%;
  }
  .post-card-li {
    list-style: none;
    padding-right: 0;
  }
  @media ${P.U.tablet} {
    padding-left: var(--tablet-gap);
    padding-right: 0;
  }
  @media ${P.U.desktop} {
    padding-left: var(--desktop-gap);
    margin-bottom: 164px;
  }
`;async function getServerSideProps({query:e,params:t}){let r=await n.Z.query({query:j,variables:{slug:e.blog}}).then(e=>e.data.blogs[0]),a=await u.V.searchHighlight("",{...(0,b.Rj)(e,15),sort:["date:desc"]});return{props:{blog:r,fallback:{posts:a}}}}x().div.withConfig({componentId:"sc-615abf4e-3"})`
  position: relative;
  height: 526px;
`;let j=d.gql`
    query Blog($slug: String!){
        blogs(where: {slug: $slug}){
          id
          slug
          blogger {
              fullName
              picture{
                  formats
                  url
                  provider
              }
              about
          }
          menu {
              label
              value
              type
          }
        }
    }
`,y=(0,o.l)(a,"default"),k=(0,o.l)(a,"getStaticProps"),$=(0,o.l)(a,"getStaticPaths"),C=(0,o.l)(a,"getServerSideProps"),U=(0,o.l)(a,"config"),I=(0,o.l)(a,"reportWebVitals"),Z=(0,o.l)(a,"unstable_getStaticProps"),A=(0,o.l)(a,"unstable_getStaticPaths"),H=(0,o.l)(a,"unstable_getStaticParams"),N=(0,o.l)(a,"unstable_getServerProps"),R=(0,o.l)(a,"unstable_getServerSideProps"),V=new s.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/blog/[blog]/filtres",pathname:"/blog/[blog]/filtres",bundlePath:"",filename:""},components:{App:p.default,Document:l.default},userland:a})},9114:e=>{e.exports=require("@apollo/client")},1635:e=>{e.exports=require("dayjs")},7688:e=>{e.exports=require("dayjs/locale/fr")},9997:e=>{e.exports=require("meilisearch")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},5515:e=>{e.exports=require("react-cookie")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},7518:e=>{e.exports=require("styled-components")},7147:e=>{e.exports=require("fs")},1017:e=>{e.exports=require("path")},2781:e=>{e.exports=require("stream")},9796:e=>{e.exports=require("zlib")}};var t=require("../../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[3181,5016,6859,8450,9755,4033,779,7113,6453,1077,7620,723,5745,4092,142,7994],()=>__webpack_exec__(3600));module.exports=r})();