import { gql } from "@apollo/client";
import client from "/api/apollo-client";
import { PageTitle } from "styles/styled-typography";
import Stamp from "components/parcours-emails/Stamp";
import GridCardSection from "components/shared/section/GridCardSection";
import { modulesAsObj } from "utils/components.utils";
import GridCard from "components/shared/Card/GridCard";
import { removeMarkdown } from "utils/string.utils";

export default function Parcours({ pageContent }) {

  if (!pageContent) return null;

  const allPostSections = pageContent?.sections?.filter(
    (section) => section.__typename === "ComponentSectionPostSet"
  );

  const stampPost = allPostSections.filter(
    (section) => section.name === "featured"
  )[0]?.posts[0];

  const postSections = allPostSections.filter(section => section.name !== "featured");

  return (
    <>
      <PageTitle className={"site-padding"}>Parcours e-mails</PageTitle>

      { stampPost && <Stamp post={stampPost}/> }

      <div className={"site-padding"}>
        {postSections.map((postSection, index) => (
          <GridCardSection
            key={index} nameSection={postSection.name}>

            {postSection.posts.map((post, index) => {

              const modules = modulesAsObj(post.modules);
              let details = post?.author?.fullName ? post.author.fullName : "";

              return (
                <GridCard key={index} post={{
                  isExternal: false,
                  title: post.title,
                  image: post.image,
                  link: `/parcours-emails/${post.slug}`,
                  details: details,
                  lead: removeMarkdown(modules?.lead?.content || ""),
                  postType: "emailJourney"
                }}/>
              );
            })}
          </GridCardSection>
        ))}
      </div>
    </>
  );
}


export async function getStaticProps() {

  const { parcoursEmail } = await client
    .query({ query: QUERY_PAGE_PARCOURS })
    .then((response) => {
      return response.data;
    });

  if (!parcoursEmail) {
    return {
      notFound: true,
    };
  }

  return {
    props: {
      pageContent: parcoursEmail,
    },
    revalidate: 10,
  };
}

const QUERY_PAGE_PARCOURS = gql`
    query GetPageParcours {
        parcoursEmail {
            sections {
                ... on ComponentSectionPostSet {
                    __typename
                    name
                    posts {
                        title
                        type
                        slug
                        image {
                            url
                            alternativeText
                            caption
                            provider
                        }
                        author {
                            fullName
                        }
                        modules {
                            ... on ComponentModuleEmailJourney {
                                __typename
                                embedForm
                            }
                            ... on ComponentModuleLead {
                                __typename
                                content
                            }
                        }
                    }
                }
            }
        }
    }
`;
