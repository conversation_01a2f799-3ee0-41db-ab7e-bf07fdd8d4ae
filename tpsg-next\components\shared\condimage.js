import Image from "next/legacy/image";
import { withRealSrc } from "utils/image-utils";

export default function CondImage({
  imageData,
  preserveAspectRatio,
  addClass,
  priority = false,
  sizes = null
}) {

  const imageSrc = withRealSrc(imageData);

  if (!imageSrc) return null;

  if (preserveAspectRatio) {
    return (
      <Image
        src={imageSrc}
        layout={"intrinsic"}
        height={imageData.height}
        width={imageData.width}
        alt={imageData.alternativeText || ""}
        priority={priority}
        className={`cond-image ${addClass}`}
        sizes={sizes}
      />
    )
  }
  return (
    <Image
      className={`cond-image ${addClass}`}
      src={imageSrc}
      key={imageSrc}
      layout={"fill"}
      objectFit={"cover"}
      alt={imageData.alternativeText || ""}
      priority={priority}
      sizes={sizes}
    />
  )
}
