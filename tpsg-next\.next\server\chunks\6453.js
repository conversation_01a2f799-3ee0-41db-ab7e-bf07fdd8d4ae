"use strict";exports.id=6453,exports.ids=[6453],exports.modules={6453:(t,o,i)=>{i.d(o,{DZ:()=>x,GN:()=>s,My:()=>f,NZ:()=>c,V1:()=>m,X0:()=>g,bP:()=>r,hQ:()=>d,kz:()=>l});var e=i(7518),a=i.n(e),n=i(7467);let p={topSpace:.096,minBottomSpace:.2788,maxBottomSpace:.4711},m=a().h1.withConfig({componentId:"sc-a3af5335-0"})`
  position: relative;
  font-weight: 500;
  color: #161616;
  font-size: 48px;
  margin-top:  calc(var(--spacing-l) - 48px * ${p.topSpace});
  margin-bottom: calc(var(--spacing-l) - 48px * ${p.minBottomSpace});
  
  @media ${n.U.tablet} {
    font-size: 72px;
    margin-top:  calc(var(--spacing-l) - 72px * ${p.topSpace});
    margin-bottom: calc(var(--spacing-l) - 72px * ${p.minBottomSpace});
  }
  @media ${n.U.desktop} {
    font-size: 104px;
    margin-top:  calc(var(--spacing-l) - 104px * ${p.topSpace});
    margin-bottom: calc(var(--spacing-l) - 104px * ${p.minBottomSpace});
  }
`,r=a().h2.withConfig({componentId:"sc-a3af5335-1"})`
  box-sizing: content-box;
  color: #ececec;
  margin-top: 0;
  margin-bottom: 0;
  font-size: clamp(24px, 1.125rem + 1vw, 32px);
  font-weight: 400;
  max-width: 600px;
  line-height: 105%;
  &:before {
    display: block;
    margin-bottom: 8px;
    content: '${t=>t.label}';
    color: ${t=>t.color};
    font-size: 18px;
  }
  @media ${n.U.tablet} {
    font-size: 32px;
    margin-right: 24px;
  }
  @media ${n.U.desktop} {
    font-weight: 400;
    margin-right: 48px;
    font-size: clamp(32px, 3.5vw, 48px);
  }
`,c=a().h2.withConfig({componentId:"sc-a3af5335-2"})`
  
  --title-size: clamp(3.5rem, 0.03rem + 7.4vw, 6rem);
  
  font-family: Stelvio, "Helvetica Neue", Helvetica, sans-serif;
  font-size: var(--title-size);
  color: ${t=>t.light?"var(--c-soft-cream)":"var(--soft-dark)"};
  font-weight: 400;
  margin-top: 0;
  margin-bottom: calc( var(--title-size) * -${p.maxBottomSpace});
  
`,s=a().h2.withConfig({componentId:"sc-a3af5335-3"})`
  font-size: 24px;
  font-weight: 500;
  line-height: 110%;
  margin-top: 0;
  margin-bottom: 16px;
  @media ${n.U.tablet} {
    font-size: 48px;
    margin-bottom: 16px;
    margin-top: 16px;
  }
`,l=a().h4.withConfig({componentId:"sc-a3af5335-4"})`
  font-size: 20px;
  margin-top: 0;
  margin-bottom: 10px;
  @media ${n.U.tablet} {
    font-size: 30px;
  }
  span {
    position: relative;
    margin-left: 8px;
    display: inline-block;
    margin-bottom: -2px;
    height: 22px;
    width: 22px;
  }
`,g=a().p.withConfig({componentId:"sc-a3af5335-5"})`
  font-size: 16px;
  color: #888888;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 30px;
  margin-top: 0;
  margin-bottom: 10px;
  @media screen and (max-width:320px){ // Little screen only
    padding: 0;
  }
  @media ${n.U.tablet} {
    font-size:20px;
  }
`,f=a().p.withConfig({componentId:"sc-a3af5335-6"})`
  font-size: 14px;
  font-style: italic;
  font-family: "Lora", Charter, Times, "Times New Roman", serif;
  color: #7a7a7a;
  margin-top: 0;
  margin-bottom: 10px;
  @media ${n.U.tablet} {
    font-size: 17px;
  }
`,x=a().h1.withConfig({componentId:"sc-a3af5335-7"})`
  font-size: 38px;
  font-weight: 500;
  line-height: 110%;
  margin-top: 0;
  margin-bottom: 16px;
  @media ${n.U.tablet} {
    font-size: 64px;
    margin-bottom: 24px;
    margin-top: 24px;
  }
  @media ${n.U.desktop} {
    margin-bottom: 24px;
    margin-top: 24px;
  }
`,d=a().p.withConfig({componentId:"sc-a3af5335-8"})`
  color: #161616;
  letter-spacing: 0.04em;
  margin-top: 48px;
  text-transform: uppercase;
  @media ${n.U.desktop} {
    margin-top: 0;
    margin-bottom: 0;
    font-size: 22px;
    font-weight: 500;
    &:hover {
      color: var(--brand-color);
    }
  }
`;a().p.withConfig({componentId:"sc-a3af5335-9"})`
  font-weight: 400;
  font-size: 26px;
  margin: 0;
  color: #161616;
  @media ${n.U.tablet} {
    font-size: 32px;
  }
`}};