import styled from "styled-components";
import CondImage from "components/shared/condimage";
import { device } from "styles/device";

export default function SlideCard({ content, isDark }) {


  return (
    <Wrapper isDark={isDark}>
      <div className={"circle-image"}>
        <CondImage imageData={content.authorImage}/>
      </div>
      <p className={"card-author"}>{content.author}</p>
      <h3>{content.title}</h3>
      <div className={"thumbnail"}>
        <CondImage imageData={content.postImage}/>
      </div>
    </Wrapper>
  )
}

const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex: 0 0 1;
  justify-content: start;
  flex-direction: column;
  min-width: calc(80%);
  width: calc(100% - 48px);
  margin-right: 16px;
  background-color: ${p => p.isDark ? "#242424" : "#f4f4f4"};
  text-align: center;
  height: 364px;

  @media ${device.tablet} {
    min-width: 295px;
  }

  @media ${device.desktop} {
    width: 25%;
    margin-right: 32px
  }
  
  .circle-image {
    position: relative;
    flex: 0 1 auto;
    margin: -25px auto 0 auto;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    border-style: solid;
    border-width: 1px;
    border-color: ${p => p.isDark ? "#888888" : "white"};
    background-color: ${p => p.isDark ? "#242424" : "white"};
    z-index: 2;
    //overflow: hidden;
    img {
      border-radius: 50px;
    }
    
    &:before {
      content: "";
      position: absolute;
      top: -7px;
      left: -7px;
      width: 50px;
      height: 50px;
      border-radius: 50px;
      border: 6px solid ${p => p.isDark ? "#080808" : "white"};
    }
  }

  .card-author {
    flex: 0 1 auto;
    font-family: "Lora", serif;
    font-style: italic;
    font-size: 16px;
    margin: 27px 24px 0 24px;
    color: #888;
  }

  h3 {
    flex: 0 1 auto;
    margin: 12px 24px 0 24px;
    font-weight: 500;
    font-size: 21px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    color: ${p => p.isDark ? "#dcdcdc" : "#161616"};
  }
  
  .thumbnail {
    position: relative;
    flex: 1 1 auto;
    margin-top: 24px;
    width: 100%;
  }




`;
