const { MeiliSearch } = require("meilisearch");

// Connexion à Meilisearch
const config = {
  host: process.env.MEILI_HOST || 'http://localhost:7700'
};

// N'ajouter la clé API que si elle est définie
if (process.env.MEILI_ADMIN_KEY) {
  config.apiKey = process.env.MEILI_ADMIN_KEY;
}

const client = new MeiliSearch(config);

async function updateMeilisearchIndex() {
  try {
    console.log("Mise à jour de l'index Meilisearch...");

    // Vérifier si l'index existe
    const indexes = await client.getIndexes();

    if (!indexes || !indexes.results) {
      console.log("Aucun index trouvé ou format de réponse inattendu.");
      console.log("Réponse complète:", JSON.stringify(indexes, null, 2));
      console.log("Tentative de création de l'index 'post'...");
      await client.createIndex("post");
      console.log("Index 'post' créé avec succès !");
      return;
    }

    const postIndex = indexes.results.find(index => index.uid === "post");

    if (!postIndex) {
      console.log("L'index 'post' n'existe pas. Création de l'index...");
      await client.createIndex("post");
      console.log("Index 'post' créé avec succès !");
    }

    // Mettre à jour les paramètres de l'index
    console.log("Mise à jour des paramètres de l'index...");
    await client.index("post").updateSettings({
      "filterableAttributes": [
        "title",
        "topics",
        "tags",
        "author",
        "type",
        "blog",
        "cs",
      ],
      "sortableAttributes": ["date"],
      "pagination": {
        "maxTotalHits": 4000
      },
      "searchableAttributes": [
        "title",
        "body",
        "lead"
      ]
    });

    console.log("Paramètres de l'index mis à jour avec succès !");
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'index :", error);
  }
}

// Exécuter la fonction
updateMeilisearchIndex();
