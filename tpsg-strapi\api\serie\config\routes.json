{"routes": [{"method": "GET", "path": "/series", "handler": "serie.find", "config": {"policies": []}}, {"method": "GET", "path": "/series/count", "handler": "serie.count", "config": {"policies": []}}, {"method": "GET", "path": "/series/:id", "handler": "serie.findOne", "config": {"policies": []}}, {"method": "POST", "path": "/series", "handler": "serie.create", "config": {"policies": []}}, {"method": "PUT", "path": "/series/:id", "handler": "serie.update", "config": {"policies": []}}, {"method": "DELETE", "path": "/series/:id", "handler": "serie.delete", "config": {"policies": []}}]}