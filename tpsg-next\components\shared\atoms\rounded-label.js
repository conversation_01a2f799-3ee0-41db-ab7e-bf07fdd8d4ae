import styled from "styled-components";
import { slugify } from "utils/string.utils";
import Link from "next/link";

const RoundedLabel = ({ text }) => {

  let topicRoute = "/categories/" + slugify(text);

  return (
    <Link href={topicRoute} legacyBehavior={true}>
      <LabelWrapper className="card-label">
        {text}
      </LabelWrapper>
    </Link>
  )
}

export default RoundedLabel;

const LabelWrapper = styled.span`
  position: relative;
  font-family: <PERSON><PERSON><PERSON>, "Helvetica Neue", Helvetica, "Arial", sans-serif;
  padding: 5px 11px 5px 11px;
  font-size: 11px;
  letter-spacing: 0.2px;
  border-radius: 100px;
  text-transform: uppercase;
  white-space: nowrap;
  margin-bottom: 8px;

  border: 1px solid #464646;
  background-color: transparent;
  color: #464646;

  &:hover {
    background-color: #242424;
    color: #f4f4f4;
  }

  cursor: pointer;
  `
;
