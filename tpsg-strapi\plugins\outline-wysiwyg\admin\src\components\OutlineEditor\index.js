import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import Editor from "rich-markdown-editor";
import styled from "styled-components";
import MediaLib from "../MediaLib";

const OutlineEditor = ({onChange, name, value, fullScreen}) => {

  const [nightMode, setNightMode] = useState(false);
  const [isInit, setInit] = useState(false);
  const [initialValue, setInitialValue] = useState({text: ""});

  const [mediaLibOpen, setMediaLibOpen] = useState(false);

  const handleNightMode = () => {
    setNightMode(!nightMode);
  }

  const handleToggleMediaLib = () => {
    setMediaLibOpen(!mediaLibOpen);
  }

  useEffect(() => {
    if (!isInit && value !== null) {
      if (value.length > 2) {
        setInitialValue(Object.create({text: value}));
      }
      setInit(true);
    }
  }, [value])

  function changeBrokenImage(newSrc) {
    let arrImg = document.images;
    for (let i = 0; i < arrImg.length; i++) {
      if (arrImg[i].getAttribute("src") !== undefined) {
        arrImg[i].src = arrImg[i].getAttribute("src");
        if(arrImg[i].src.includes("www.replace.me")) {
          arrImg[i].src = newSrc;
        }
      }
    }
  }


  const handleChangeAssets = async (data) => {
    setMediaLibOpen(false);
    let newValue = value ? value : "";
    newValue = newValue.replace("![](www.replace.me)", `![](${data.url})`);
    onChange({
      target: {
        value: newValue,
        name: name,
        type: "textarea",
      },
    });
    changeBrokenImage(data.url);
  };

  return (
    <Wrapper fullScreen={fullScreen} nightMode={nightMode && fullScreen}>

      {fullScreen &&
        <DNButton>
          <div className="toggle-btn" id="_1st-toggle-btn">
            <input type="checkbox" value={nightMode} onClick={() => handleNightMode()}/>
            <span/>
          </div>
        </DNButton>
      }

      <MediaLib
        isOpen={mediaLibOpen}
        onChange={handleChangeAssets}
        onToggle={handleToggleMediaLib}
      />

      <EditorContainer
        fullScreen
        nightMode={nightMode && fullScreen}>
        {name && onChange &&
          <Editor
            name={"Wysiwyg"}
            value={initialValue.text}
            onChange={content => {
              onChange({
                target: {
                  value: content(),
                  name: name,
                  type: "textarea",
                },
              });
            }}
            uploadImage={async file => {
              setMediaLibOpen(true);
              return "www.replace.me";
            }}
          />
        }
      </EditorContainer>
    </Wrapper>
  );
};

Editor.propTypes = {
  onChange: PropTypes.func.isRequired,
  name: PropTypes.string.isRequired,
  value: PropTypes.string,
};

const Wrapper = styled.div`

  top: 0;
  left: 0;
  display: flex;
  align-content: center;
  justify-content: center;
  padding: 64px 7% 32px 5%;
  border: 1px solid #e3e9f3;
  border-radius: 2px;

  ${props => ( props.fullScreen &&
    "position: fixed;" +
    "z-index: 1050;" +
    "height: 100%;" +
    "width: 100%;" +
    "overflow-y: scroll;" +
    "border: none;"
  )};

  transition-duration: 0.65s;
  background-color: ${props => props.nightMode ? "#121212" : "white"};
`

const EditorContainer = styled.div`

  &:first-child:first-child {
    background-color: ${props => props.nightMode ? "#121212" : "white"};
    transition-duration: 0.65s;
  }


  .ProseMirror > p:last-of-type {
    background-color: ${props => props.nightMode ? "#121212" : "white"};
  }

  .ProseMirror {
    background-color: ${props => props.nightMode ? "#121212" : "white"};
    transition-duration: 0.65s;
  }

  max-width: 700px;
  margin-left: 32px;
  width: 100%;
  cursor: text;

  h1,
  h2,
  h3,
  h4 {
    span {
      cursor: text;
      color: ${props => props.nightMode ? "rgb(230,230,230)" : "rgb(42,42,42)"};
      font-family: "Manrope", sans-serif;
      text-rendering: optimizeLegibility;
    }
  }

  h1 {
    padding-top: 19px;

    span {
      font-size: 30px;
      font-weight: 700;
      line-height: 37.5px;
    }
  }

  h2 {
    span {
      font-size: 22px;
      font-weight: 800;
      line-height: 32px;
    }
  }

  p {
    font-family: "Lora", charter, Georgia, Cambria, "Times New Roman", Times,
    serif;
    font-size: 19px;
    line-height: 31px;
    margin-bottom: 29px;
    color: ${props => props.nightMode ? "rgb(210,210,210)" : "rgb(42,42,42)"};
    text-rendering: optimizeLegibility;
  }

  ul,
  ol {
    margin-bottom: 32px;
  }

  li {
    margin-bottom: 12px;
  }

  blockquote {
    font-family: "Lora", charter, Georgia, Cambria, "Times New Roman", Times,
    serif;
    margin: 1.2em 0 0 -28px;
    overflow: hidden;
    padding: 0px 0 0 28px;
    position: relative;
    text-rendering: optimizeLegibility;

    p {
      color: ${props => props.nightMode ? "rgb(200,200,200)" : "rgb(20,20,20)"};
      font-size: 19px;
      line-height: 31px;
      margin-bottom: 29px;
    }
  }

  blockquote:before {
    content: "";
    border-left: 3px solid ${props => props.nightMode ? "rgb(240,240,240)" : "rgb(0,0,0)"};;
    position: absolute;
    bottom: 22.9px;
    top: 3.6px;
    left: 16px;
  }

  strong,
  em,
  b {
    font-family: "Lora", charter, Georgia, Cambria, "Times New Roman", Times,
    serif;
    font-size: 19px;
    line-height: 29px;
    margin-bottom: 29px;
  }

  .caption {
    font-family: "Geomaniste", "Manrope", sans-serif;
    font-size: 16px;
    color: ${props => props.nightMode ? "rgb(200,200,200)" : "rgb(49,49,49)"};
  }

  a {
    cursor: pointer;
    color: ${props => props.nightMode ? "rgb(200,200,200)" : "rgb(42,42,42)"};
    font-family: "Manrope", sans-serif;
    text-rendering: optimizeLegibility;

    background-image: linear-gradient(#494949, #494949);
    background-position: 0% 100%;
    background-repeat: no-repeat;
    background-size: 100% 1px;
  }

  a:hover,
  a:focus {
    color: #f05637;
  }
`;

const DNButton = styled.div`

  transform: scale(0.95);

  position: fixed;
  z-index: 1060;
  top: 48px;
  right: 112px;

  .toggle-btn {
    position: relative;
    width: 60px;
    height: 26px;
    margin: 0 auto;
    border-radius: 30px;
  }

  input[type="checkbox"] {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    margin: 0px;
    cursor: pointer;
    opacity: 0;
    z-index: 2;
  }

  #_1st-toggle-btn span {
    position: absolute;
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
    overflow: hidden;
    opacity: 1;
    background-color: #000;
    border-radius: 40px;
    transition: 0.2s ease background-color, 0.2s ease opacity;
  }

  #_1st-toggle-btn span:before, #_1st-toggle-btn span:after {
    content: '';
    position: absolute;
    top: 4px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    transition: 0.5s ease transform, 0.2s ease background-color;
  }

  #_1st-toggle-btn span:before {
    background-color: #000;
    transform: translate(-30px, 0px);
    z-index: 1;
  }

  #_1st-toggle-btn span:after {
    background-color: #fff;
    transform: translate(5px, 0px);
    z-index: 0;
  }

  #_1st-toggle-btn input[type="checkbox"]:checked + span {
    background-color: #fff;
    box-shadow: none;
  }

  #_1st-toggle-btn input[type="checkbox"]:active + span {
    opacity: 0.5;
  }

  #_1st-toggle-btn input[type="checkbox"]:checked + span:before {
    background-color: #fff;
    transform: translate(30px, -8px);
  }

  #_1st-toggle-btn input[type="checkbox"]:checked + span:after {
    background-color: #000;
    transform: translate(36px, 0px);
    top: 3px;
    width: 20px;
    height: 20px;
  }
`;


export default OutlineEditor;