import client from "api/apollo-client";
import { queries } from "api/gql-queries";
import { gql } from "@apollo/client";
import styled from "styled-components";
import SSRPaginate from "components/shared/pagination/ssr-paginate";
import TopicHeader from "components/categories/TopicHeader";
import { topicsPostsfetcher } from "utils/fetcher";
import { FilterTopicsString } from "utils/filterSearchString";
import HorizontalReversePostCard from "components/shared/Card/HorizontalReversePostCard";
import { device } from "styles/device";
import { useEffect } from "react";
import { useRouter } from "next/router";
import { topicSort } from "utils/list.utils";

const postPerPage = 15; // Number items per pag

export default function PageTopicRessources({ topics, page, fallback }) {
  const nbHits = fallback?.posts?.totalHits;
  const posts = fallback?.posts?.hits;
  const router = useRouter();
  let sortTopics = topicSort(topics);

  useEffect(() => {
    if (+page === 1) {
      router.replace(
        {
          pathname: `/categories/${sortTopics[0].slug}`,
        },
        undefined,
        { scroll: false }
      );
    }else if (+page > Math.ceil(nbHits / postPerPage)) {
      router.replace(
        {
          pathname: `/categories/${sortTopics[0].slug}/ressources`,
          query: { page: Math.ceil(nbHits / postPerPage) }
        },
        undefined,
        { scroll: false }
      );
    }
  },[nbHits, page, router, sortTopics]);

  return (
    <PageWrapper className={"site-padding"}>

      {/*<header>*/}
      {/*   <Link href={`/categories/${topics[0].slug}`}>*/}
      {/*      HOME*/}
      {/*   </Link>*/}
      {/*   <h1>{topics[0].name}</h1>*/}
      {/*</header>*/}

      <TopicHeader topicName={topics[0].name} type={"THÈME /"}/>

      <SectionPosts>
        <p className="label-type">Dernières ressources</p>
        <div className="posts-container ">
          <LeftContent>
            <ul className={"list-container"}>
              {posts.map((post, key) => {
                return (
                  <HorizontalReversePostCard
                    post={post}
                    key={`post-${key}`}
                    options={{
                      showLead: true,
                      showDate: true,
                      showAuthor: true
                    }}
                  />
                );
              })}
            </ul>
          </LeftContent>
          
        </div>
      </SectionPosts>

      <SSRPaginate
        nbHits={nbHits}
        baseUrl={`/categories/${topics[0].slug}/ressources?page=`}
        currentPage={+page || 1}
        options={{
          postPerPage: postPerPage
        }}
      />

    </PageWrapper>
  )
}

const PageWrapper = styled.div`
  position: relative;
  padding-bottom: 50px;
`;

export async function getServerSideProps({ query, params }) {

  let { page } = query;

  // TOPICS QUERY
  // noinspection DuplicatedCode
  const mainTopic = await client.query({
    query: queries.QUERY_TOPIC,
    variables: { slug: params.topic }
  }).then(response => {
    return response.data.topics[0]
  })

  let topics = [mainTopic]

  // CHILDREN QUERY
  const childrenTopics = await client.query({
    query: QUERY_TOPIC_CHILDREN,
    variables: { id: mainTopic.id }
  }).then(response => {
    return response.data.topicChildren
  })

  topics.push(...childrenTopics)

  // Clear duplicate topics
  topics = topics.filter(
    (topic, index, self) =>
      self.findIndex((topic2) => topic2.id === topic.id) === index
  );

  //fetch posts
  const posts = await topicsPostsfetcher({ page: +page },FilterTopicsString(topics),postPerPage);
  if (posts?.hits?.length == 0) {
    return {
      notFound: true,
    }
  }
  
  return {
    props: {
      topics: topics,
      page: +page,
      fallback: {
        posts
      },
    }
  }
}


const QUERY_TOPIC_CHILDREN = gql`
    query TopicChildren($id: ID!){
        topicChildren(id: $id){
            name
            id
            slug,
            postCount
            parent{
                id
                slug
                name
            }
        }
    }
`;
const SectionPosts = styled.section`
  margin-top: 96px;
  .posts-container {
    display: block;

    @media ${device.desktop} {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
    }
  }
`;

const LeftContent = styled.article`
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .list-container {
    padding: 0;
    width: 100%;
  }
`;
