import styled from "styled-components";
import { withRealSrc } from "utils/image-utils";
import Image from "next/image";
import Link from "next/link";


/**
 *
 * @param {object}  author
 * @param {string}  date
 * @param {object}  options
 * @param {string}  options.direction [vertical,horizontal]
 * @param {string}  options.size      [s,m]
 * @param {string}  options.theme     [dark,light]
 * @return {JSX.Element}
 * @constructor
 */

export default function Author({
  date,
  author,
  options = {
    size: "m",
    theme: "light",
    direction: "row",
    showName: true,
    showDate: true,
    showPicture: true,
  }
}) {


  const name = author?.fullName;
  const pic = withRealSrc(author?.picture);

  const Dot =
      options.showDate &&
      options.showName &&
      options.direction === "row" ?
        <span className={"elt-author-dot"}>•</span>
        :
        null;

  return(
    <Wrapper size={options.size} theme={options.theme} direction={options.direction}>
      { options.showPicture &&
          <div className={"elt-author-picture"}>
            { pic && <Image
              src={pic}
              sizes={"28px"}
              fill
              style={{ objectFit: "cover" }}
              alt={""}/>
            }
          </div>
      }
      <div className={"elt-author-text"}>
        { options.showName && <span className={"elt-author-name"}>{name}</span> }
        { Dot }
        { options.showDate && <span className={"elt-author-date"}>{date}</span> }
      </div>
    </Wrapper>
  )
}


const Wrapper = styled.div`
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;

  .elt-author-picture {
    position: relative;
    aspect-ratio: 1/1;
    height: ${p => p.direction === "row" ?
    "28px" : "34px"};
    
    overflow: hidden;
    border-radius: 34px;
    background-color: var(--c-soft-cream);
  }
  
  .elt-author-text {
    position: relative;
    display: flex;
    flex-direction: ${p => p.direction};
    align-items: ${p => p.direction === "row" ? 
    "center" : "left"};

    color: ${p => p.theme === "dark" ?
    "var(--c-soft-cream)" : "#161616" };
    
    font-family: Switzer, sans-serif;
    font-size: 14px;
    font-weight: 400;
    
    span {
      margin-right: 8px;
    }
    
    .elt-author-date {
      color: ${p => p.direction === "row" ? "inherit" : "#848484"};
      text-transform: capitalize;
    }
  }
`;