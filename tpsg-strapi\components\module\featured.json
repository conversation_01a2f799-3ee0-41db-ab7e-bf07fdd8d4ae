{"collectionName": "components_module_featureds", "info": {"name": "featured", "icon": "star-of-life", "description": ""}, "options": {}, "attributes": {"title": {"type": "string", "required": true}, "description": {"type": "richtext"}, "image": {"model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": true, "pluginOptions": {}}, "inColumn": {"type": "boolean", "default": false}, "cta": {"type": "component", "repeatable": false, "component": "atom.button"}, "color": {"type": "component", "repeatable": false, "component": "atom.color"}, "type": {"type": "string"}, "cta2": {"type": "component", "repeatable": false, "component": "atom.button"}, "postRef": {"model": "post"}}}