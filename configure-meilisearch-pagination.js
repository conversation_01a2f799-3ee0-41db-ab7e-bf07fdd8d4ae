/**
 * Script pour configurer la pagination de Meilisearch
 * 
 * Ce script configure Meilisearch pour qu'il retourne plus de résultats par défaut
 * 
 * Pour exécuter ce script :
 * 1. Assurez-vous que Meilisearch est en cours d'exécution
 * 2. Exécutez la commande : node configure-meilisearch-pagination.js
 */

const fetch = require('node-fetch');

const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function configureMeilisearchPagination() {
  try {
    console.log('Configuration de la pagination de Meilisearch...');

    // Vérifier si l'index existe
    console.log('Vérification de l\'existence de l\'index...');
    const indexesResponse = await fetch(`${MEILI_URL}/indexes`);
    const indexes = await indexesResponse.json();
    
    const indexExists = indexes.results && indexes.results.some(index => index.uid === INDEX_NAME);
    
    if (!indexExists) {
      console.log('L\'index n\'existe pas, création de l\'index...');
      await fetch(`${MEILI_URL}/indexes`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uid: INDEX_NAME })
      });
      console.log('Index créé avec succès');
    } else {
      console.log('L\'index existe déjà');
    }

    // Configurer la pagination
    console.log('Configuration de la pagination...');
    const paginationResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/pagination`, {
      method: 'PATCH',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        maxTotalHits: 4000
      })
    });

    if (paginationResponse.ok) {
      console.log('Pagination configurée avec succès');
    } else {
      console.error('Erreur lors de la configuration de la pagination:', await paginationResponse.text());
    }

    // Vérifier les paramètres de pagination
    console.log('Vérification des paramètres de pagination...');
    const settingsResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/pagination`);
    const settings = await settingsResponse.json();
    console.log('Paramètres de pagination:', settings);

    console.log('Configuration terminée !');
  } catch (error) {
    console.error('Erreur lors de la configuration:', error);
  }
}

configureMeilisearchPagination();
