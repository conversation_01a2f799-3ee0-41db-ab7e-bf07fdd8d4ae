import client from "/api/apollo-client";
import { gql } from "@apollo/client";
import { Mei<PERSON><PERSON><PERSON> } from "/api/meili-client";
// import useSWR from "swr";
// import deepEqual from "fast-deep-equal"
import { useRouter } from "next/router";
import { getMeiliParamsPaginate } from "utils/query.utils";
import BlogMenu from "components/blog/menu";
// import SimplePagination from "components/shared/SimplePagination";
// import { CardPost } from "components/shared";
import HorizontalReversePostCard from "components/shared/Card/HorizontalReversePostCard";
import styled from "styled-components";
import SSRPaginate from "components/shared/pagination/ssr-paginate";
import { device } from "styles/device";
import { useEffect } from "react";

const postPerPage = 15; // Number items per page

export default function PageFiltres({ blog, fallback }) {

  const posts = fallback?.posts;
  const router = useRouter();
  const { query } = router;

  useEffect(() => {
    let newQuery = {}
    query.topic && (newQuery["topic"] = query.topic);
    if (+query.page < 1 ) {
      newQuery.page = 1
      router.replace(
        {
          pathname: `/blog/${blog.slug}/filtres`,
          query: newQuery
        },
        undefined,
        { scroll: false }
      );
    } else if ( +query.page > posts?.totalPages) {
      newQuery.page  = posts?.totalPages > 0 ? posts.totalPages : 1
      router.replace(
        {
          pathname: `/blog/${blog.slug}/filtres`,
          query: newQuery
        },
        undefined,
        { scroll: false }
      );
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[blog.slug, router.query.page]);

  const cardPostOptions = {
    showLead: true,
    showDate: true,
    showAuthor: true
  }

  const pageTitle = query.topic;


  // q = query
  const getQueryType = (q) => {
    if(q.topic) return "topic";
    if(q.type) return "type";
    if(q.tag) return "tag";
    return null;
  }

  const getBaseUrl = () => {
    let queryType = getQueryType(query);
    return `/blog/${blog.slug}/filtres?${queryType ? queryType + "=" + query[queryType] + "&" : ""}page=`
  }

  return (
    <PageWrapper>
      <SectionPosts>
        <BlogMenu data={blog}/>
        <PostsWrapper>
          <h1>{pageTitle}</h1>
          <ul className={"list-container"}>
            {posts?.hits?.map((post, key) => {
              return (
                <li key={`post-${key}`} className={"post-card-li"}>
                  <HorizontalReversePostCard
                    post={post}
                    options={cardPostOptions}
                  />
                </li>
              );
            })}
          </ul>
          <SSRPaginate
            nbHits={posts?.totalHits}
            baseUrl={getBaseUrl()}
            // baseUrl={`/blog/${blog.slug}/filtres?${query.topic ? (`topic=${query.topic}&`) : ""}page=`}
            currentPage={posts?.page}
            options={{
              postPerPage: postPerPage,
            }}
          />
        </PostsWrapper>
      </SectionPosts>

    </PageWrapper>
  )
}

const PageWrapper = styled.div`
  position: relative;
  background-color: var(--soft-white);
  @media ${device.desktop} {
    .blog-menu {
      
    }
  }
`;
const SectionPosts = styled.div`
  position: relative;
  display: flex;
  
  @media ${device.desktop} {
    margin-left: var(--border-space);
    margin-right: var(--border-space);
  }
`;
const PostsWrapper = styled.div`
  width: 100%;
  margin-bottom: 64px;
  padding-top: 40px;
  padding-left: var(--mobile-gap);
  padding-right: var(--mobile-gap);
  .list-container {
    padding: 0;
    width: 100%;
  }
  .post-card-li {
    list-style: none;
    padding-right: 0;
  }
  @media ${device.tablet} {
    padding-left: var(--tablet-gap);
    padding-right: 0;
  }
  @media ${device.desktop} {
    padding-left: var(--desktop-gap);
    margin-bottom: 164px;
  }
`;

const Spacer = styled.div`
  position: relative;
  height: 526px;
`


export async function getServerSideProps({ query, params }) {


  const blog = await client.query({
    query: QUERY_BLOG,
    variables: { slug: query.blog }
  }).then(response => {
    return response.data.blogs[0]
  })

  // fetch posts filter by blog and topics
  const posts = await MeiliApi.searchHighlight("", {
    ...getMeiliParamsPaginate( query, postPerPage),
    sort: ["date:desc"]
  });


  return {
    props: {
      blog: blog,
      fallback: {
        posts: posts,
      }
    }
  }
}

const QUERY_BLOG = gql`
    query Blog($slug: String!){
        blogs(where: {slug: $slug}){
          id
          slug
          blogger {
              fullName
              picture{
                  formats
                  url
                  provider
              }
              about
          }
          menu {
              label
              value
              type
          }
        }
    }
`
