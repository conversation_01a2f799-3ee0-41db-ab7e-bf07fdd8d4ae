(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[755],{763:function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return Image}});let n=i(8754),r=i(1757),a=r._(i(7294)),l=n._(i(46)),o=i(7599),d=i(5352),s=i(4955);i(9941);let u=i(2955);function normalizeSrc(e){return"/"===e[0]?e.slice(1):e}let c={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1},g=new Set,f="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",m=new Map([["default",function(e){let{config:t,src:i,width:n,quality:r}=e;return i.endsWith(".svg")&&!t.dangerouslyAllowSVG?i:(0,u.normalizePathTrailingSlash)(t.path)+"?url="+encodeURIComponent(i)+"&w="+n+"&q="+(r||75)}],["imgix",function(e){let{config:t,src:i,width:n,quality:r}=e,a=new URL(""+t.path+normalizeSrc(i)),l=a.searchParams;return l.set("auto",l.getAll("auto").join(",")||"format"),l.set("fit",l.get("fit")||"max"),l.set("w",l.get("w")||n.toString()),r&&l.set("q",r.toString()),a.href}],["cloudinary",function(e){let{config:t,src:i,width:n,quality:r}=e,a=["f_auto","c_limit","w_"+n,"q_"+(r||"auto")].join(",")+"/";return""+t.path+a+normalizeSrc(i)}],["akamai",function(e){let{config:t,src:i,width:n}=e;return""+t.path+normalizeSrc(i)+"?imwidth="+n}],["custom",function(e){let{src:t}=e;throw Error('Image with src "'+t+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}]]);function isStaticRequire(e){return void 0!==e.default}function generateImgAttrs(e){let{config:t,src:i,unoptimized:n,layout:r,width:a,quality:l,sizes:o,loader:d}=e;if(n)return{src:i,srcSet:void 0,sizes:void 0};let{widths:s,kind:u}=function(e,t,i,n){let{deviceSizes:r,allSizes:a}=e;if(n&&("fill"===i||"responsive"===i)){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(n);i)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:a.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:a,kind:"w"}}if("number"!=typeof t||"fill"===i||"responsive"===i)return{widths:r,kind:"w"};let l=[...new Set([t,2*t].map(e=>a.find(t=>t>=e)||a[a.length-1]))];return{widths:l,kind:"x"}}(t,a,r,o),c=s.length-1;return{sizes:o||"w"!==u?o:"100vw",srcSet:s.map((e,n)=>d({config:t,src:i,quality:l,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:d({config:t,src:i,quality:l,width:s[c]})}}function getInt(e){return"number"==typeof e?e:"string"==typeof e?parseInt(e,10):void 0}function defaultImageLoader(e){var t;let i=(null==(t=e.config)?void 0:t.loader)||"default",n=m.get(i);if(n)return n(e);throw Error('Unknown "loader" found in "next.config.js". Expected: '+o.VALID_LOADERS.join(", ")+". Received: "+i)}function handleLoading(e,t,i,n,r,a){if(!e||e.src===f||e["data-loaded-src"]===t)return;e["data-loaded-src"]=t;let l="decode"in e?e.decode():Promise.resolve();l.catch(()=>{}).then(()=>{if(e.parentNode&&(g.add(t),"blur"===n&&a(!0),null==r?void 0:r.current)){let{naturalWidth:t,naturalHeight:i}=e;r.current({naturalWidth:t,naturalHeight:i})}})}let ImageElement=e=>{let{imgAttributes:t,heightInt:i,widthInt:n,qualityInt:r,layout:l,className:o,imgStyle:d,blurStyle:s,isLazy:u,placeholder:c,loading:g,srcString:f,config:m,unoptimized:h,loader:p,onLoadingCompleteRef:b,setBlurComplete:y,setIntersection:w,onLoad:v,onError:A,isVisible:S,noscriptSizes:x,...k}=e;return g=u?"lazy":g,a.default.createElement(a.default.Fragment,null,a.default.createElement("img",{...k,...t,decoding:"async","data-nimg":l,className:o,style:{...d,...s},ref:(0,a.useCallback)(e=>{w(e),(null==e?void 0:e.complete)&&handleLoading(e,f,l,c,b,y)},[w,f,l,c,b,y]),onLoad:e=>{let t=e.currentTarget;handleLoading(t,f,l,c,b,y),v&&v(e)},onError:e=>{"blur"===c&&y(!0),A&&A(e)}}),(u||"blur"===c)&&a.default.createElement("noscript",null,a.default.createElement("img",{...k,loading:g,decoding:"async","data-nimg":l,style:d,className:o,...generateImgAttrs({config:m,src:f,unoptimized:h,layout:l,width:n,quality:r,sizes:x,loader:p})})))};function Image(e){var t;let i,{src:n,sizes:r,unoptimized:u=!1,priority:m=!1,loading:h,lazyRoot:p=null,lazyBoundary:b,className:y,quality:w,width:v,height:A,style:S,objectFit:x,objectPosition:k,onLoadingComplete:z,placeholder:E="empty",blurDataURL:I,..._}=e,R=(0,a.useContext)(s.ImageConfigContext),j=(0,a.useMemo)(()=>{let e=c||R||o.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),i=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:i}},[R]),L=r?"responsive":"intrinsic";"layout"in _&&(_.layout&&(L=_.layout),delete _.layout);let O=defaultImageLoader;if("loader"in _){if(_.loader){let e=_.loader;O=t=>{let{config:i,...n}=t;return e(n)}}delete _.loader}let P="";if("object"==typeof(t=n)&&(isStaticRequire(t)||void 0!==t.src)){let e=isStaticRequire(n)?n.default:n;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(I=I||e.blurDataURL,P=e.src,(!L||"fill"!==L)&&(A=A||e.height,v=v||e.width,!e.height||!e.width))throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e))}let N=!m&&("lazy"===h||void 0===h);((n="string"==typeof n?n:P).startsWith("data:")||n.startsWith("blob:"))&&(u=!0,N=!1),g.has(n)&&(N=!1),j.unoptimized&&(u=!0);let[q,C]=(0,a.useState)(!1),[W,M,D]=(0,d.useIntersection)({rootRef:p,rootMargin:b||"200px",disabled:!N}),B=!N||M,U={boxSizing:"border-box",display:"block",overflow:"hidden",width:"initial",height:"initial",background:"none",opacity:1,border:0,margin:0,padding:0},G={boxSizing:"border-box",display:"block",width:"initial",height:"initial",background:"none",opacity:1,border:0,margin:0,padding:0},H=!1,T=getInt(v),V=getInt(A),F=getInt(w),J=Object.assign({},S,{position:"absolute",top:0,left:0,bottom:0,right:0,boxSizing:"border-box",padding:0,border:"none",margin:"auto",display:"block",width:0,height:0,minWidth:"100%",maxWidth:"100%",minHeight:"100%",maxHeight:"100%",objectFit:x,objectPosition:k}),Q="blur"!==E||q?{}:{backgroundSize:x||"cover",backgroundPosition:k||"0% 0%",filter:"blur(20px)",backgroundImage:'url("'+I+'")'};if("fill"===L)U.display="block",U.position="absolute",U.top=0,U.left=0,U.bottom=0,U.right=0;else if(void 0!==T&&void 0!==V){let e=V/T,t=isNaN(e)?"100%":""+100*e+"%";"responsive"===L?(U.display="block",U.position="relative",H=!0,G.paddingTop=t):"intrinsic"===L?(U.display="inline-block",U.position="relative",U.maxWidth="100%",H=!0,G.maxWidth="100%",i="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%27"+T+"%27%20height=%27"+V+"%27/%3e"):"fixed"===L&&(U.display="inline-block",U.position="relative",U.width=T,U.height=V)}let K={src:f,srcSet:void 0,sizes:void 0};B&&(K=generateImgAttrs({config:j,src:n,unoptimized:u,layout:L,width:T,quality:F,sizes:r,loader:O}));let X=n,Y={imageSrcSet:K.srcSet,imageSizes:K.sizes,crossOrigin:_.crossOrigin,referrerPolicy:_.referrerPolicy},Z=a.default.useLayoutEffect,$=(0,a.useRef)(z),ee=(0,a.useRef)(n);(0,a.useEffect)(()=>{$.current=z},[z]),Z(()=>{ee.current!==n&&(D(),ee.current=n)},[D,n]);let et={isLazy:N,imgAttributes:K,heightInt:V,widthInt:T,qualityInt:F,layout:L,className:y,imgStyle:J,blurStyle:Q,loading:h,config:j,unoptimized:u,placeholder:E,loader:O,srcString:X,onLoadingCompleteRef:$,setBlurComplete:C,setIntersection:W,isVisible:B,noscriptSizes:r,..._};return a.default.createElement(a.default.Fragment,null,a.default.createElement("span",{style:U},H?a.default.createElement("span",{style:G},i?a.default.createElement("img",{style:{display:"block",maxWidth:"100%",width:"initial",height:"initial",background:"none",opacity:1,border:0,margin:0,padding:0},alt:"","aria-hidden":!0,src:i}):null):null,a.default.createElement(ImageElement,et)),m?a.default.createElement(l.default,null,a.default.createElement("link",{key:"__nimg-"+K.src+K.srcSet+K.sizes,rel:"preload",as:"image",href:K.srcSet?void 0:K.src,...Y})):null)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9755:function(e,t,i){e.exports=i(763)}}]);