"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[211],{211:function(n,e,t){t.d(e,{xr:function(){return CookieWall},g4:function(){return Featured},oJ:function(){return RenderMarkdown}});var o=t(2729),i=t(5893);t(1664);var r=t(9521);function _templateObject(){let n=(0,o._)(["\n    padding: 8px 16px;\n    cursor: pointer;\n    font-size: 12px;\n    background-color: black;\n    color: white;\n"]);return _templateObject=function(){return n},n}r.ZP.a.withConfig({componentId:"sc-555070bc-0"})(_templateObject());var a=t(5582),l=t(7294),p=t(9663),d=t(669),c=t(5158),s=t(708),u=t(4256);function CookieWall_templateObject(){let n=(0,o._)(["\n  position: relative;\n  width: 100%;\n  height: 100%;\n  background-color: var(--c-dark-green);\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  align-items: start;\n  padding: 40px;\n\n  .cw-text {\n    margin-top: 0;\n    font-family: Switzer, sans-serif;\n    font-size: 16px;\n    line-height: 24px;\n    color: #f4f4f4;\n  }\n"]);return CookieWall_templateObject=function(){return n},n}function CookieWall(n){let{style:e,children:t}=n,[o,r]=(0,s.Z)(["preferences"]),[a,p]=(0,l.useState)(void 0);return(0,l.useEffect)(()=>{var n;(null==o?void 0:null===(n=o.preferences)||void 0===n?void 0:n.medias)?p(!0):p(!1)},[o]),(0,i.jsx)(i.Fragment,{children:!0===a?t:(0,i.jsxs)(f,{style:{...e},children:[(0,i.jsx)("p",{className:"cw-text",children:"Nous n’avons pas d’autorisation de votre part pour l’utilisation de services tiers (YouTube, Spotify, SoundCloud, ConvertKit, …) depuis toutpoursagloire.com. Cette autorisation est n\xe9cessaire pour une exp\xe9rience compl\xe8te sur notre site. Vous pouvez les accepter en appuyant sur le bouton ci-dessous"}),(0,i.jsx)(u.Yz,{text:"Accepter",theme:"light",action:()=>void r("preferences",{...o.preferences,medias:!0},{sameSite:"strict",path:"/",maxAge:31536e3})})]})})}let f=r.ZP.div.withConfig({componentId:"sc-831a4ae6-0"})(CookieWall_templateObject());var g=t(7421),m=t(9562);function RenderMardown_templateObject(){let n=(0,o._)(['\n  font-family: "Lora", Charter, Times, "Times New Roman", serif;\n  font-size: 18px;\n  line-height: 28px;\n  font-weight: 400;\n\n  img {\n    max-width: 100%;\n  }\n\n  h5,\n  h6 {\n    font-size: 18px;\n    line-height: 28px;\n    font-weight: 500;\n    margin-bottom: 0;\n  }\n  h1,\n  h2,\n  h3,\n  h4 {\n    font-family: Stelvio, sans-serif;\n    font-weight: 600;\n    margin-bottom: 0;\n  }\n  h1,\n  h2 {\n    font-size: 28px;\n    line-height: 32px;\n    margin-top: 32px;\n    &:first-child {\n      margin-top: 0;\n    }\n  }\n  h3 {\n    font-size: 24px;\n    line-height: 28px;\n    margin-top: 12px;\n  }\n  p {\n    font-size: 18px;\n    line-height: 28px;\n    margin-top: 0;\n    margin-bottom: 24px;\n    color: #161616;\n    sup {\n      vertical-align: top;\n      position: relative;\n      top: -0.4em;\n      margin-left: 4px;\n      margin-right: 4px;\n    }\n  }\n  blockquote {\n    position: relative;\n    margin: 0;\n    padding: 8px 0 8px 36px;\n    &::before {\n      content: "“";\n      display: inline-block;\n      position: absolute;\n      font-size: 54px;\n      font-weight: bold;\n      color: #363636;\n      left: 0;\n      top: 18px;\n      width: 100px;\n    }\n  }\n  blockquote p {\n    font-size: 18px;\n    line-height: 28px;\n    font-style: italic;\n  }\n  em {\n  }\n\n  ul,\n  ol {\n    margin-top: 12px;\n    padding-left: 24px;\n    color: #161616;\n  }\n  li {\n    line-height: 170%;\n    margin: 0 0 8px 0;\n    color: #161616;\n  }\n  a {\n    color: #161616;\n    text-decoration: underline;\n  }\n  a:hover {\n    color: var(--brand-color);\n  }\n  hr {\n    border: none;\n    margin: 40px 0 40px 0;\n    &::before {\n      content: "***";\n      display: block;\n      letter-spacing: 10px;\n      text-align: center;\n      color: #161616;\n    }\n  }\n\n  cite {\n    &:before {\n      content: "– ";\n      font-weight: 600;\n    }\n  }\n\n  @media '," {\n    max-width: 720px;\n    margin: 80px 0 0 0;\n    h1,\n    h2 {\n      font-size: 32px;\n      line-height: 36px;\n      margin-top: 44px;\n      margin-bottom: 6px;\n    }\n    h3,\n    h4 {\n      margin-top: 32px;\n      margin-bottom: 0;\n      font-size: 26px;\n      line-height: 30px;\n    }\n    h4 {\n      opacity: 0.6;\n      font-weight: 500;\n    }\n    p {\n      color: #323232;\n      font-size: 20px;\n      line-height: 32px;\n      font-weight: 400;\n      margin-bottom: 24px;\n    }\n    ul,\n    ol {\n      margin-top: 12px;\n      padding-left: 24px;\n      margin-bottom: 24px;\n      font-size: 20px;\n      line-height: 32px;\n    }\n    blockquote p {\n      font-size: 20px;\n      line-height: 32px;\n      font-weight: 500;\n      font-style: italic;\n      margin-bottom: 0;\n    }\n    blockquote {\n      margin-bottom: 24px;\n    }\n\n    .post-content-button {\n      display: inline-block;\n      margin-top: 16px;\n      margin-bottom: 16px;\n      padding: 10px 32px;\n      border-radius: 40px;\n      text-align: center;\n      color: white;\n      text-decoration: none;\n      font-family: Switzer, sans-serif;\n      font-weight: 400;\n      background-color: var(--brand-color);\n    }\n  }\n  .table-container {\n    position: relative;\n    overflow-x: auto;\n    margin-bottom: 24px;\n    width: 100%;\n    background-image: linear-gradient(to right, white, white),\n      linear-gradient(to right, white, white),\n      linear-gradient(to right, rgba(0, 0, 20, 0.5), rgba(255, 255, 255, 0)),\n      linear-gradient(to left, rgba(0, 0, 20, 0.5), rgba(255, 255, 255, 0));\n    /* Shadows */\n    /* Shadow covers */\n    background-position: left center, right center, left center, right center;\n    background-repeat: no-repeat;\n    background-color: white;\n    background-size: 20px 100%, 20px 100%, 10px 100%, 16px 100%;\n    background-attachment: local, local, scroll, scroll;\n  }\n\n  table {\n    font-family: Switzer, sans-serif;\n    border: 1px solid #ccc;\n    border-collapse: collapse;\n    padding: 0;\n    width: 100%;\n    overflow-x: auto;\n  }\n\n  table caption {\n    font-size: 1.5em;\n    margin: 0.5em 0 0.75em;\n  }\n\n  table tr {\n    background-color: rgba(248, 248, 248, 0.9);\n    border: 1px solid #ddd;\n    padding: 0.35em;\n  }\n\n  table th,\n  table td {\n    font-size: 0.85em;\n    line-height: 1.4em;\n    padding: 0.625em;\n    text-align: left;\n    min-width: 240px;\n    color: rgba(0, 0, 0, 0.72);\n  }\n\n  table th {\n    font-weight: 600;\n    color: rgba(0, 0, 0, 9);\n  }\n\n  table th {\n    font-size: 0.85em;\n    //font-weight: 500;\n    //text-transform: uppercase;\n  }\n\n  @media screen and (max-width: 600px) {\n    .table-container {\n      background: none;\n    }\n    table {\n      border: 0;\n    }\n\n    table caption {\n      font-size: 1.3em;\n    }\n\n    table thead {\n      border: none;\n      clip: rect(0 0 0 0);\n      height: 1px;\n      margin: -1px;\n      overflow: hidden;\n      padding: 0;\n      position: absolute;\n      width: 1px;\n    }\n\n    table tr {\n      border-bottom: 3px solid #ddd;\n      display: block;\n      margin-bottom: 0.625em;\n    }\n\n    table td {\n      border-bottom: 1px solid #ddd;\n      display: block;\n      font-size: 0.8em;\n      text-align: left;\n    }\n\n    table td::before {\n      /*\n      * aria-label has no advantage, it won't be read inside a table\n      content: attr(aria-label);\n      */\n      content: attr(data-label);\n      float: left;\n      font-weight: bold;\n      text-transform: uppercase;\n    }\n\n    table td:last-child {\n      border-bottom: 0;\n    }\n  }\n"]);return RenderMardown_templateObject=function(){return n},n}let x={minHeight:300},codeComponent=n=>{let{children:e}=n;return(0,i.jsx)(CookieWall,{style:x,children:(0,i.jsx)(d.Z,{html:e})})},tableComponent=n=>{let{children:e}=n;return(0,i.jsx)("div",{className:"table-container",children:(0,i.jsx)("table",{children:e})})};function RenderMarkdown(n){let{content:e,className:t}=n;return e?(0,i.jsx)(l.Fragment,{children:(0,i.jsx)(h,{className:t,children:(0,i.jsx)(a.U,{rehypePlugins:[p.Z,m.Z],components:{code:codeComponent,table:tableComponent},children:(0,c.k5)(e)})})}):null}let h=r.ZP.div.withConfig({componentId:"sc-7da47e7e-0"})(RenderMardown_templateObject(),g.U.tablet);t(7915);var b=t(5675),w=t.n(b),v=t(3071),k=t(2053);function Featured_templateObject(){let n=(0,o._)(["\n  position: relative;\n  padding: 0 var(--border-space);\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  column-gap: 24px;\n  padding-bottom: 80px;\n  width: 100%;\n  min-height: 400px;\n  background-color: ",';\n  z-index: 100;\n\n  .fw-featured-author {\n    font-family: "Lora", sans-serif;\n    font-style: italic;\n    letter-spacing: 1px;\n    opacity: 0.4;\n  }\n\n  .fw-featured-image {\n    position: relative;\n    width: 100%;\n    aspect-ratio: 1/1;\n    grid-column: 1/5;\n\n    img {\n      object-fit: cover;\n    }\n  }\n\n  .text-content {\n    position: relative;\n    grid-column: 1/5;\n    color: ',";\n    background-color: ",";\n  }\n\n  .fw-featured-type {\n    font-family: Switzer, Arial, sans-serif;\n    opacity: 0.48;\n    margin-top: 48px;\n    margin-bottom: 56px;\n    font-size: 16px;\n    font-weight: 400;\n    text-transform: uppercase;\n    letter-spacing: 0.02em;\n  }\n\n  .fw-featured-title {\n    margin-top: 8px;\n    margin-bottom: 0;\n    font-size: 30px;\n    font-weight: 500;\n    line-height: 95%;\n  }\n\n  .fw-featured-lead {\n    margin-top: 8px;\n    font-size: 17px;\n    margin-right: 32px;\n  }\n\n  .fw-featured-buttons {\n    display: flex;\n    flex-direction: column;\n    gap: 24px;\n  }\n\n  @media "," {\n    flex-direction: row;\n    min-height: 400px;\n    padding-bottom: 96px;\n\n    .fw-featured-image {\n      position: relative;\n      grid-column: 1/3;\n    }\n\n    .text-content {\n      margin-top: 0;\n      margin-left: 32px;\n      grid-column: 3/5;\n      display: flex;\n      flex-direction: column;\n      justify-content: space-between;\n    }\n\n    .fw-featured-type {\n      font-size: 20px;\n      margin-top: 46px;\n    }\n\n    .fw-featured-title {\n      font-size: 46px;\n      margin-top: 24px;\n    }\n\n    .fw-featured-lead {\n      font-familly: Switzer, sans-serif;\n      font-size: 20px;\n      font-weight: 400;\n      opacity: 0.72;\n      //font-size: 18px;\n      margin-top: 24px;\n    }\n\n    .fw-featured-buttons {\n      display: flex;\n      flex-direction: row;\n      gap: 24px;\n    }\n  }\n"]);return Featured_templateObject=function(){return n},n}function Featured(n){let e,{content:t}=n,{image:o,title:r,description:a,cta:l=null,cta2:p=null,color:d,type:c,postRef:s,route:u,_formatted:f}=t,g=["article","podcast","video","webinar","livre","formation"],m=g.includes(null==c?void 0:c.toLowerCase()),getTypeFromRoute=n=>{var e;if(!n)return"article";let t=n.split("/").filter(Boolean),o=null===(e=t[0])||void 0===e?void 0:e.toLowerCase();return g.includes(o)?o:"article"};if(!m&&c){let n=getTypeFromRoute(u);e=n.toUpperCase()}else if(c)e=c.toUpperCase();else{let n=getTypeFromRoute(u);e=n.toUpperCase()}let x=(null==l?void 0:l.url)||((null==u?void 0:u.startsWith("/"))?u:"/"+u),h=(null==p?void 0:p.url)||null;return(0,i.jsxs)(y,{backgroundColor:null==d?void 0:d.background,children:[(0,i.jsx)("div",{className:"fw-featured-image",children:(0,i.jsx)(w(),{src:(0,v.k)(o),fill:!0,priority:!0,alt:"",sizes:"50vw"})}),(0,i.jsxs)("div",{className:"text-content",children:[(0,i.jsx)("p",{className:"fw-featured-type",children:e}),(0,i.jsxs)("div",{children:[(0,i.jsx)(PostAuthor,{post:s,content:t}),(0,i.jsx)("h3",{className:"fw-featured-title",children:r}),(0,i.jsx)("p",{className:"fw-featured-lead",children:a||(null==f?void 0:f.lead)||(null==f?void 0:f.body)}),(0,i.jsxs)("div",{className:"fw-featured-buttons",children:[x&&(0,i.jsx)(k.Z,{text:(null==l?void 0:l.name)||"D\xe9couvrir",link:x,outline:(null==l?void 0:l.outline)||!1,theme:"light"}),h&&(0,i.jsx)(k.Z,{text:(null==p?void 0:p.name)||"D\xe9couvrir",link:h,outline:(null==p?void 0:p.outline)||!1,theme:"light"})]})]})]})]})}let PostAuthor=n=>{var e;let{post:t,content:o}=n,r=null;return((null==t?void 0:null===(e=t.author)||void 0===e?void 0:e.fullName)?r=t.author.fullName:(null==t?void 0:t.author)?r=t.author:(null==o?void 0:o.author)&&(r=o.author),r)?(0,i.jsx)("div",{className:"fw-featured-author",children:r}):null},y=r.ZP.div.withConfig({componentId:"sc-f544c04b-0"})(Featured_templateObject(),n=>n.backgroundColor?n.backgroundColor:"var(--c-dark-green)",n=>n.color?n.color:"var(--c-soft-cream)",n=>n.backgroundColor?n.backgroundColor:"var(--c-dark-green)",g.U.tablet)}}]);