# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

data/coreData.json

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# IDE
/.idea
.vscode
http-request.http
schema.graphql
.graphqlconfig

# auto generated files
/data/coreData.json
/public/*.xml

last-build

.env