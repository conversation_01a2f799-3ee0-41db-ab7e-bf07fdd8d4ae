import { <PERSON><PERSON><PERSON><PERSON> } from "api/meili-client";
import { getMeiliParamsPaginate } from "utils/query.utils";

const nbPostPerPage = 15; // Number items per page
 
//query : {page,tag}
async function topicsPostsfetcher(urlParams, filterString = "", postPerPage=nbPostPerPage) {
  
  const response = await MeiliApi.search("", {
    ...getMeiliParamsPaginate(urlParams, postPerPage),
    attributesToRetrieve: [
      "title",
      "slug",
      "type",
      "published_at",
      "author",
      "image",
      "route",
      "date",
      "lead",
      "topics",
      "cs"
    ],
    filter: [filterString],
    sort: ["date:desc"],
    cropLength: 100,
  });
  return response;
};

export {
  topicsPostsfetcher
}