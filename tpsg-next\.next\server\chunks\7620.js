"use strict";exports.id=7620,exports.ids=[7620],exports.modules={9280:(t,o,e)=>{e.d(o,{YM:()=>animated_icon,P8:()=>BlurPlay});var i=e(997),r=e(7518),n=e.n(r);e(1664);var s=e(7467);n().div.withConfig({componentId:"sc-e3127c86-0"})`
  position: relative;
  font-family: "Stelvio", sans-serif;
  font-size: 17px;
  font-weight: 400;
  display: inline-block;
  width: 100%;
  color : #f4f4f4;
  border: 1px solid #080808;
  background-color: #080808;
  padding: 12px 18px 6px 18px;
  text-align: center;
  cursor: pointer;
  
  &:hover {
    opacity: 0.8;
  }
  
  &.soft {
    border: 1px solid #080808;
    background-color: transparent;
    color: #161616;
  }
  
  @media ${s.U.tablet} {
    width: auto;
    font-size: 17px;
  }
`,n().div.withConfig({componentId:"sc-2224e371-0"})`
  position: relative;
  width: 100%;
  height: 100%;

  &:hover {
    cursor: pointer;
    svg {
      transform: rotate(-45deg);
      width: 325%;
      height: 325%;
      left: -112%;
      bottom: -112%;
    }
    .background-dot {
      transform: translate3d(-66px, 66px, 0);
    }
    .color-dot {
      transform: translate3d(-16px, 16px, 0);
    }
  }
`,n().div.withConfig({componentId:"sc-2224e371-1"})`
  position: absolute;
  bottom: 0;
  left: 0;
  height: 200px;
  width: 200px;
  overflow: hidden;

  .background-dot {
    position: absolute;
    background-color: white;
    width: 66px;
    height: 66px;
    left: -33px;
    bottom: -33px;
    z-index: 10;
    border-radius: 100px;
    transition: all 450ms cubic-bezier(0.93, 0.03, 0.23, 0.84);
  }

  .color-dot {
    position: absolute;
    background-color: black;
    width: 16px;
    height: 16px;
    left: 0;
    bottom: 0;
    z-index: 10;
    border-radius: 16px;
    transition: all 450ms cubic-bezier(0.93, 0.03, 0.23, 0.84);
  }

  svg {
    position: absolute;
    left: -33px;
    bottom: -33px;
    z-index: 8;
    transition: all 450ms cubic-bezier(0.93, 0.03, 0.23, 0.84);
  }
`;let animated_icon=({type:t,colors:o})=>i.jsx(d,{children:(0,i.jsxs)(a,{colors:o,children:[i.jsx("div",{className:"background-dot"}),i.jsx("div",{className:"color-dot",children:i.jsx(Icon,{type:t})})]})}),d=n().div.withConfig({componentId:"sc-c95cb7b7-0"})`
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;

  &:hover {
    cursor: pointer;
    svg {
      opacity: 1;
      //transform: translate3d(25px,-25px, 0);
    }
    .background-dot {
      transform: translate3d(42px, -42px, 0);
    }
    .color-dot {
      width: 60px;
      height: 60px;
      transform: translate3d(12px, -12px, 0);
    }
  }
`,a=n().div.withConfig({componentId:"sc-c95cb7b7-1"})`
  position: absolute;
  bottom: 0;
  left: 0;
  height: 200px;
  width: 200px;
  overflow: hidden;

  .background-dot {
    position: absolute;
    background-color: ${t=>t.colors?.back?t.colors.back:"green"};
    width: 66px;
    height: 66px;
    left: -33px;
    bottom: -33px;
    z-index: 10;
    border-radius: 100px;
    transition: all 500ms cubic-bezier(0.6, -0.50, 0.24, 0.91);
  }

  .color-dot {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: ${t=>t.colors?.front||"#080808"};
    width: 16px;
    height: 16px;
    left: 0;
    bottom: 0;
    z-index: 10;
    border-radius: 60px;
    transition: all 500ms cubic-bezier(0.6, -0.50, 0.24, 0.91);
  }

  svg {
    opacity: 0;
    height: 50%;
    width: 50%;
    left: 25px;
    bottom: 0;
    z-index: 8;
    transition: all 500ms cubic-bezier(0.6, -0.50, 0.24, 0.91);
  }
`,Icon=({type:t})=>{switch(t){case"article":return(0,i.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[i.jsx("path",{d:"M25 22V8.00003C25.0017 7.6056 24.9252 7.21475 24.775 6.85002C24.6248 6.4853 24.4039 6.15392 24.125 5.87502C23.8461 5.59611 23.5147 5.3752 23.15 5.22502C22.7853 5.07484 22.3944 4.99837 22 5.00003H5",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),i.jsx("path",{d:"M13 13H21",stroke:"white",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"}),i.jsx("path",{d:"M13 17H21",stroke:"white",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"}),i.jsx("path",{d:"M2.20312 10.4995L2.70272 11.0588L3.82147 10.0596L3.32187 9.50026L2.20312 10.4995ZM8 7.99986H8.75V7.99946L8 7.99986ZM8 23.9999H7.25V24.0003L8 23.9999ZM13.2375 21.9999V21.2499H11.5621L12.6781 22.4995L13.2375 21.9999ZM27.2375 21.9999L27.7965 21.4999L27.5729 21.2499H27.2375V21.9999ZM25 26.9999L25.0004 26.2499H25V26.9999ZM11 26.2499C10.5858 26.2499 10.25 26.5856 10.25 26.9999C10.25 27.4141 10.5858 27.7499 11 27.7499V26.2499ZM3.32187 9.50026C3.079 9.22833 2.90585 8.90146 2.81733 8.54777L1.36221 8.91197C1.50976 9.50145 1.79834 10.0462 2.20312 10.4995L3.32187 9.50026ZM2.81733 8.54777C2.7288 8.19408 2.72756 7.82418 2.81369 7.4699L1.35616 7.11552C1.21259 7.70598 1.21467 8.32249 1.36221 8.91197L2.81733 8.54777ZM2.81369 7.4699C2.89983 7.11562 3.07077 6.78758 3.3118 6.51402L2.18634 5.52239C1.78462 5.97833 1.49972 6.52506 1.35616 7.11552L2.81369 7.4699ZM3.3118 6.51402C3.55283 6.24046 3.85673 6.02957 4.19734 5.8995L3.66224 4.4982C3.09455 4.71497 2.58805 5.06646 2.18634 5.52239L3.3118 6.51402ZM4.19734 5.8995C4.53795 5.76944 4.90507 5.72409 5.26709 5.76737L5.44515 4.27798C4.84178 4.20585 4.22992 4.28142 3.66224 4.4982L4.19734 5.8995ZM5.26709 5.76737C5.62911 5.81065 5.97519 5.94126 6.27554 6.14795L7.1259 4.91228C6.62531 4.56779 6.04851 4.35011 5.44515 4.27798L5.26709 5.76737ZM6.27554 6.14795C6.57589 6.35465 6.82151 6.63123 6.99126 6.9539L8.31877 6.25553C8.03585 5.71775 7.62648 5.25677 7.1259 4.91228L6.27554 6.14795ZM6.99126 6.9539C7.16101 7.27658 7.2498 7.63567 7.25 8.00026L8.75 7.99946C8.74967 7.3918 8.60168 6.79332 8.31877 6.25553L6.99126 6.9539ZM7.25 7.99986V23.9999H8.75V7.99986H7.25ZM7.25 24.0003C7.25032 24.6079 7.39832 25.2064 7.68123 25.7442L9.00874 25.0458C8.83899 24.7232 8.75019 24.3641 8.75 23.9995L7.25 24.0003ZM7.68123 25.7442C7.96415 26.282 8.37352 26.743 8.8741 27.0874L9.72446 25.8518C9.42411 25.6451 9.17849 25.3685 9.00874 25.0458L7.68123 25.7442ZM8.8741 27.0874C9.37469 27.4319 9.95149 27.6496 10.5549 27.7217L10.7329 26.2324C10.3709 26.1891 10.0248 26.0585 9.72446 25.8518L8.8741 27.0874ZM10.5549 27.7217C11.1582 27.7939 11.7701 27.7183 12.3378 27.5015L11.8027 26.1002C11.462 26.2303 11.0949 26.2756 10.7329 26.2324L10.5549 27.7217ZM12.3378 27.5015C12.9054 27.2848 13.4119 26.9333 13.8137 26.4773L12.6882 25.4857C12.4472 25.7593 12.1433 25.9702 11.8027 26.1002L12.3378 27.5015ZM13.8137 26.4773C14.2154 26.0214 14.5003 25.4747 14.6438 24.8842L13.1863 24.5298C13.1002 24.8841 12.9292 25.2121 12.6882 25.4857L13.8137 26.4773ZM14.6438 24.8842C14.7874 24.2937 14.7853 23.6772 14.6378 23.0878L13.1827 23.452C13.2712 23.8056 13.2724 24.1755 13.1863 24.5298L14.6438 24.8842ZM14.6378 23.0878C14.4902 22.4983 14.2017 21.9535 13.7969 21.5003L12.6781 22.4995C12.921 22.7714 13.0941 23.0983 13.1827 23.452L14.6378 23.0878ZM13.2375 22.7499H27.2375V21.2499H13.2375V22.7499ZM26.6785 22.4999C26.9681 22.8237 27.1578 23.2244 27.2246 23.6537L28.7068 23.4229C28.5954 22.7074 28.2792 22.0395 27.7965 21.4999L26.6785 22.4999ZM27.2246 23.6537C27.2915 24.0829 27.2326 24.5224 27.0552 24.9189L28.4243 25.5316C28.7201 24.8707 28.8182 24.1383 28.7068 23.4229L27.2246 23.6537ZM27.0552 24.9189C26.8777 25.3155 26.5893 25.6521 26.2246 25.8883L27.0401 27.1473C27.6478 26.7537 28.1286 26.1925 28.4243 25.5316L27.0552 24.9189ZM26.2246 25.8883C25.86 26.1245 25.4348 26.2501 25.0004 26.2499L24.9996 27.7499C25.7237 27.7502 26.4324 27.5409 27.0401 27.1473L26.2246 25.8883ZM25 26.2499H11V27.7499H25V26.2499Z",fill:"white"})]});case"podcast":return(0,i.jsxs)("svg",{width:"32",height:"32",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[i.jsx("path",{d:"M21 8C21 5.23858 18.7614 3 16 3C13.2386 3 11 5.23858 11 8V16C11 18.7614 13.2386 21 16 21C18.7614 21 21 18.7614 21 16V8Z",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),i.jsx("path",{d:"M16 25V29",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),i.jsx("path",{d:"M24.9498 17C24.6904 19.1915 23.6359 21.2116 21.9863 22.6774C20.3367 24.1433 18.2066 24.9529 15.9998 24.9529C13.793 24.9529 11.6629 24.1433 10.0133 22.6774C8.36371 21.2116 7.30925 19.1915 7.0498 17",stroke:"white",strokeWidth:"1.5",strokeLinecap:"square",strokeLinejoin:"round"})]});default:return null}};function BlurPlay({clickAction:t}){return i.jsx(l,{onClick:t,children:i.jsx(c,{children:i.jsx(p,{})})})}let l=n().div.withConfig({componentId:"sc-f3ad2613-0"})`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`,c=n().div.withConfig({componentId:"sc-f3ad2613-1"})`
  position: absolute;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 80px;
  width: 80px;
  border-radius: 60px;
  background-color: rgba(0, 0, 0, 0.8);

  &:hover {
    cursor: pointer;
    transform: scale(0.95);
  }

  transition: transform 350ms ease-in-out;

  @supports ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) {
    background-color: rgba(0, 0, 0, 0.50);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }
  
  @media ${s.U.desktop} {
    height: 104px;
    width: 104px;
  }
`,p=n().div.withConfig({componentId:"sc-f3ad2613-2"})`
  margin-left: 16px;
  width: 30px;
  height: 30px;
  background: white;
  clip-path: polygon(0 0, 0 100%, 80% 50%);
  @media ${s.U.desktop} {
    height: 36px;
    width: 36px;
  }
`},5432:(t,o,e)=>{function getDotColor(t){let o={formation:{front:"#262424",back:"#FFFFFF"},emailJourney:{front:"#AA2DD6",back:"#FFFFFF"},article:{front:"#000000",back:"#FFFFFF"},default:{front:"#000000",back:"#FFFFFF"}};return o[t]?o[t]:o.default}function hexToRgb(t){let o=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return o?{r:parseInt(o[1],16),g:parseInt(o[2],16),b:parseInt(o[3],16)}:null}e.d(o,{Q:()=>getDotColor,o:()=>hexToRgb})}};