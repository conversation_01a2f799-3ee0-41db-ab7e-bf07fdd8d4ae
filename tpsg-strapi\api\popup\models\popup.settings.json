{"kind": "collectionType", "collectionName": "popups", "info": {"name": "Popup", "description": ""}, "options": {"increments": true, "timestamps": true, "draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "body": {"type": "text", "maxLength": 255}, "image": {"model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}}, "endDate": {"type": "datetime"}, "startDate": {"type": "datetime"}, "button": {"type": "component", "repeatable": false, "component": "atom.button"}}}