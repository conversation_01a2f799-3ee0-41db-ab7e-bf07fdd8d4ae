'use strict';

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */

module.exports = {
    async setCount(ctx){
        let podcasts = await strapi.query('podcast').find({})
        for(const podcast of podcasts){
            let podcastEpisodes = await strapi.query('post').find({
                'tags.slug_eq': `channel-${podcast.slug}`,
                _limit: -1
            })
            strapi.query('podcast').update({id: podcast.id}, {count: podcastEpisodes.length});
        }
        return 'should be done'
    }
};
