import styled from "styled-components";
import Link from "next/link";
import Image from "next/image";
import { withRealSrc } from "utils/image-utils";
import { dateForHumans } from "utils/date.utils";


const Speakers = ({ speakers }) => {
  if (!speakers) return null;
  return (
    <>
      {speakers.map((x, key) => <span key={key}>{x.fullName}</span>)}
    </>
  )
}

const WepisodeCard = ({ post }) => {
  const { webinar } = post.modules;
  return (
    <Link href={`/webinaires/${post.slug}`}>
      <CardWrapper>
        <ImageWrapper>
          <Image src={withRealSrc(post.image)} layout={"fill"} objectFit={"cover"}/>
        </ImageWrapper>
        <TextWrapper>
          <p>{post.title}</p>
          <p>{dateForHumans(post.published_at)}</p>
          <Speakers speakers={webinar.speakers}/>
        </TextWrapper>
      </CardWrapper>
    </Link>
  )
}


export default WepisodeCard;

const CardWrapper = styled.div`
  display: flex;
  flex-direction: row;
  cursor: pointer;
`;

const ImageWrapper = styled.div`
  position: relative;
  width: 100px;
  height: 100px;
`;

const TextWrapper = styled.div`
  margin-left: 16px;
  span {
    color: green;
  }
`;
