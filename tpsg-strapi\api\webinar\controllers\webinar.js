'use strict';

const { default: createStrapi } = require("strapi");

/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */

module.exports = {
    async setCount(ctx){
        let webinars = await strapi.query('webinar').find({})
        let webinarEpisodes = [];
        for(const webinar of webinars){
            webinarEpisodes = await strapi.query('post').find({
                'tags.slug_eq': `channel-${webinar.slug}`
            })
            strapi.query('webinar').update({id: webinar.id}, {count: webinarEpisodes.length});
        }
        return 'should be done'
    }
};