import styled from "styled-components";
import { device } from "../../styles/device";
import Link from "next/link";

export default function Footer() {
  return (
    <Wrapper>
      <p className={"footer-mission"}>Nous existons pour vous aider à voir comme Dieu voit pour vivre comme Dieu
        veut</p>
      <div className={"footer-links"}>
        <Link href={"/contact"}>Contact</Link>
        <Link href={"/soutenir"}>Nous soutenir</Link>
        <hr/>
        <Link href={"/a-propos"}>À propos</Link>
        <Link href={"/ce-que-nous-croyons"}>Ce que nous croyons</Link>
        <Link href={"/equipe-tpsg"}>Équipe</Link>
        <hr/>
        <Link href={"/formations#evenements-tpsg"}>Camp TPSG</Link>
        <Link href={"https://toutpoursagloire.myspreadshop.fr/"}>Shop</Link>
        <hr/>
        <Link href={"/mentions-legales"}>Mentions légales</Link>
      </div>
    </Wrapper>
  )
}

const Wrapper = styled.footer`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 16px;

  padding: 40px var(--border-space) 120px var(--border-space);

  background-color: var(--c-dark-green);
  color: #E8E8E5;

  .footer-mission {
    grid-column: 1 / span 2;
    grid-row: 1;
    font-size: 36px;
    line-height: 36px;
    margin: 0 0 96px 0;
  }

  a {
    display: block;
    font-family: sans-serif, "Helvetica Neue", Helvetica, Arial;
    margin: 0 0 8px 0;
    line-height: 24px;
    opacity: 0.8;
    font-weight: 400;
  }

  a:hover {
    text-decoration: underline;
  }

  hr {
    border-color: transparent;
    height: 24px;
  }

  @media ${device.tablet} {
    grid-template-columns: repeat(8, 1fr);
    padding-top: 80px;
    padding-bottom: 80px;
    .footer-mission {
      grid-column: 1 / span 6;
      grid-row: 1;
      font-size: 48px;
      line-height: 48px;
      margin: 0;
    }

    .footer-links {
      grid-column: 11 / span 2;
    }
  }

  @media ${device.desktop} {
    grid-template-columns: repeat(12, 1fr);
    padding-top: 80px;
    padding-bottom: 80px;
    .footer-mission {
      grid-column: 1 / span 6;
      font-size: 48px;
      line-height: 48px;
    }

    .footer-links {
      grid-column: 11 / span 2;
    }
  }
`;
