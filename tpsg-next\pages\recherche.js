import { useEffect, useState } from "react";
import styled from "styled-components";
import { Mei<PERSON><PERSON><PERSON> } from "api/meili-client";
import { useRouter } from "next/router";
import SearchTool from "components/recherche/search-tool";
import SearchPaginate from "components/recherche/search-paginate";
import { device } from "styles/device";
import HorizontalPostCard from "components/shared/Card/HorizontalPostCard";
import CornerStoneCard from "components/shared/Card/CornerStoneCard";
import AuthorCard from "components/recherche/AuthorCard";
import { SquareVerticalFeatured } from "components/shared/Card";

const POST_PER_PAGE = 10;
const ACCEPTED_FILTER = ["author", "topics", "type"];

export default function SearchPage({
  initialQuery,
  initialResults,
  initialCornerStones,
}) {
  const router = useRouter();
  /**
   * Query Structure:
   * {
   *    terms: string
   *    filter: {
   *       value: string
   *       type: string
   *    }
   *    page: number
   * }
   */
  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState(initialResults);
  const [cornerStones, setCornerstones] = useState(initialCornerStones);

  // Met à jour l'URL suivant les paramètres de la recherche.
  const updateUrl = (query) => {
    let routeParams = "";
    routeParams += query.terms ? `?terms=${query.terms}` : "";
    routeParams +=
      query.filter.value && query.filter.type
        ? `${routeParams.length ? "&" : "?"}${query.filter.type}=${query.filter.value
        }`
        : "";
    routeParams += query.page
      ? `${routeParams.length ? "&" : "?"}page=${query.page}`
      : "";
    let routeUrl = `/recherche${routeParams}`;
    router.push(routeUrl, undefined, { shallow: true });
  };

  // On query change
  useEffect(() => {
    // Change query page if not correct
    // (if query page more than one when totalPages == 0, do infinite loop )
    if (+query.page < 1 || (+query.page > 1 && results.totalPages === 0)) {
      // set page min
      setQuery((prevState) => {
        return { ...prevState, page: 1 };
      });
    } else if (+query.page > results.totalPages && !results.totalPages === 0) {
      // set page max
      setQuery((prevState) => {
        return { ...prevState, page: results.totalPages };
      });
    }

    // Fetch data for new query
    async function fetchData() {
      updateUrl(query);
      let preparedQueryCornerStone = prepareQueryCornerStone(query);
      let dataCornerStone = await MeiliApi.search(
        preparedQueryCornerStone.terms,
        preparedQueryCornerStone.params
      );
      setCornerstones(dataCornerStone);
      let preparedQuery = prepareQuery(query);
      let data = await MeiliApi.searchHighlight(
        preparedQuery.terms,
        preparedQuery.params
      );
      // Supprime le post s'il est dans la liste des CS
      data.hits = data?.hits?.filter(
        (post) => !dataCornerStone?.hits?.find((cs) => cs.slug === post.slug)
      );
      setResults(data);
    }

    if (query !== initialQuery) {
      fetchData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query]);

  return (
    <PageWrapper className={"site-padding"}>
      {/* Ajout du focus automatique sur le champ de recherche - Ticket: "Move directly to the search field" */}
      <SearchTool setQuery={setQuery} initQuery={query} autoFocus={true} />
      <ResultsBody>
        <div className="center-results">
          <ResultsHead>
            <h2>
              {results.totalHits} Résultat{results.totalHits > 1 && "s"}
            </h2>
          </ResultsHead>
          <Results>
            <div className="all-posts">
              {results?.hits.map((post, key) => (
                <HorizontalPostCard
                  post={post}
                  key={post.image?.src + "" + key}
                  options={{
                    showAuthor: true,
                    showDate: true,
                    showTopics: true,
                  }}
                />
              ))}
              {results?.hits?.length > 0 && (
                <SearchPaginate
                  nbHits={results.totalHits}
                  currentPage={query.page || 1}
                  changePage={changePage}
                />
              )}
            </div>
            <div className="all-cs">
              <div className="cornerstone-container">
                {query.filter?.type === "author" && (
                  <AuthorCard authorName={query.filter.value} />
                )}
                {cornerStones?.hits?.map((cornerStone, key) => {
                  cornerStone.link = cornerStone?.cta?.url;
                  return (
                    <CornerStoneCard
                      key={key}
                      post={cornerStone}
                      options={{
                        showAuthor: true,
                      }}
                    />
                  );
                })}
              </div>
            </div>
          </Results>
        </div>
      </ResultsBody>
    </PageWrapper>
  );

  function changePage(page) {
    window.scrollTo({ top: 0 });
    setQuery((prevState) => {
      return { ...prevState, page: page };
    });
  }
}

function prepareQuery(query) {
  let filter = [];
  let page = +query.page;
  if (query.filter?.type && query.filter?.value) {
    filter = ["" + query.filter.type + " =\"" + query.filter.value.trim() + "\""];
  }

  return {
    terms: query.terms || null,
    params: {
      filter: filter,
      page: page,
      sort: ["date:desc"],
      hitsPerPage: POST_PER_PAGE,
    },
  };
}

function prepareQueryCornerStone(query) {
  let filter = [];
  let terms = "";
  let limit = 4;
  if (query.filter?.type && query.filter?.value) {
    filter.push(
      "" + query?.filter.type + " = \"" + query?.filter.value.trim() + "\""
    );
    if (query?.filter?.type !== "author" && query?.filter?.type !== "author") {
      terms = query.terms || null;
    } else {
      limit = 3;
    }
  } else {
    terms = query.terms || null;
  }
  filter.push("cs=true");
  return {
    terms: terms,
    params: {
      filter: filter,
      sort: ["date:desc"],
      limit: limit,
    },
  };
}

function formatInitialQuery(query) {
  let formatedQuery = {
    terms: query.terms || null,
    filter: { value: null, type: null },
    // 0 if not ok for detect it and reload page
    page: +query.page < 1 ? 0 : +query.page,
  };
  for (const param in query) {
    if (ACCEPTED_FILTER.includes(param)) {
      formatedQuery.filter = { value: query[param], type: param };
    }
  }
  return formatedQuery;
}

export async function getServerSideProps({ query }) {
  const initialQuery = formatInitialQuery(query);
  const preparedQuery = prepareQuery(initialQuery);
  const data = await MeiliApi.searchHighlight(
    preparedQuery.terms,
    preparedQuery.params
  );
  const preparedQueryCornerStone = prepareQueryCornerStone(initialQuery);
  const cornerStones = await MeiliApi.searchHighlight(
    preparedQueryCornerStone.terms,
    preparedQueryCornerStone.params
  );
  // Supprime les posts qui de la liste qui sont déjà présents dans les corner stones.
  data.hits = data?.hits?.filter(
    (post) => !cornerStones?.hits?.find((cs) => cs.slug === post.slug)
  );
  return {
    props: {
      initialQuery: initialQuery,
      initialResults: data,
      initialCornerStones: cornerStones,
    },
  };
}

const PageWrapper = styled.div`
  position: relative;
  width: 100%;
  padding-top: 48px;
`;

const ResultsHead = styled.div`
  display: flex;
  flex-direction: row;
  margin-top: 72px;
  margin-bottom: 40px;
  width: 100%;

  h2 {
    margin-top: 0;
    font-size: 20px;
    font-family: "Switzer", sans-serif;
    margin-bottom: 0;
    font-weight: 600;
  }

  @media ${device.tablet} {
    //margin-bottom: 42px;
  }
`;
const ResultsBody = styled.div`
  display: flex;
  justify-content: center;
  .center-results {
    width: 100%;
  }
  /* @media ${device.desktop} {
    .center-results{
      width: 85%;
    }
  } */
`;
const Results = styled.section`
  position: relative;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  .all-posts {
    width: 100%;
  }
  .all-cs {
    width: 100%;
  }
  .cornerstone-container {
    /* position: sticky;
    top: 20px; */
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    justify-content: center;
    margin-bottom: 40px;
  }
  @media ${device.desktop} {
    .all-posts {
      width: 65.23%;
      padding-right: 7.9%;
      //border-right: 2px solid #DEDCD8;
    }

    .all-cs {
      width: 30.16%;
    }
  }
`;
