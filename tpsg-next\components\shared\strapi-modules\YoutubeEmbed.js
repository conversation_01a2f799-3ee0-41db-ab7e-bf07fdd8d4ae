
import { getYouTubeVideoIdFromUrl } from "../../../utils/string.utils";

// TODO: Réutiliser ce composant dans les pages d'épisodes des podcasts et webinaires
// TODO: Déplacer la fonction getYoutubeVideoFromUrl ici.

/**
 *
 * @param video {string}
 * @param autoplay {boolean} True si la vidéo doit se lancer automatiquement après que le composant soit chargé
 * @constructor
 */
export default function YoutubeEmbed({ video, autoplay }) {
  let videoId = getYouTubeVideoIdFromUrl(video);
  let baseUrl = "https://www.youtube.com/embed/";
  let urlParams = `?&autoplay=${autoplay ? 1 : 0}&mute=1&enablejsapi=1`;
  let src = baseUrl + videoId + urlParams;
  return (
    <iframe src={src} width={"100%"} height={"100%"} frameBorder={"0"} allow="fullscreen"/>
  )
}