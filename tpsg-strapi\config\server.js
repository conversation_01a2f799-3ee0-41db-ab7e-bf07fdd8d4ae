module.exports = ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  url: env('STRAPI_URL'),
  admin: {
    url: env('STRAPI_ADMIN_URL'),
    auth: {
      secret: env('ADMIN_JWT_SECRET', 'b9d38dce2f6c1b89733882b2279e0ed4'),
    },
  },
  cron: {
    enabled: true,
  },
  // Configuration pour éviter les redémarrages fréquents en mode développement
  watchIgnoreFiles: [
    './meilisearch_update.json',
    'meilisearch_update.json',
  ],
});
