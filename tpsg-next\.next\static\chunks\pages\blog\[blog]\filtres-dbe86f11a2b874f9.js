(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[633],{3678:function(n,e,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/blog/[blog]/filtres",function(){return t(1452)}])},9487:function(n,e,t){"use strict";t.d(e,{Z:function(){return BlogMenu}});var i=t(2729),r=t(5893),l=t(785),o=t(1664),a=t.n(o),p=t(9521),c=t(7294),s=t(7421),d=t(1304);function _templateObject(){let n=(0,i._)(["\n  position: fixed;\n  bottom: -70px;\n  left: -70px;\n  height: calc(70vh + 140px);\n  width: calc(100vw + 140px);\n  padding: 48px 70px;\n  background-color: rgba(",");\n  backdrop-filter: blur(",");\n  transform: translate3d(",");\n  border-top-right-radius: 70px;\n  transition: all 450ms cubic-bezier(0.58, 0, 0.29, 0.91);\n  z-index: 1800;\n\n  @media "," {\n    grid-row: 1/6;\n    background-color: transparent;\n    position: sticky;\n    display: block;\n    backdrop-filter: inherit;\n    border-top-right-radius: 0;\n    transform: none;\n    top: 46px;\n    left: 0;\n    height: 100vh;\n    width: 240px;\n    padding: 40px 0 0 0;\n    z-index: 50;\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(['\n  display: flex;\n  flex-direction: row;\n  margin-bottom: 42px;\n  .menu-blogger-picture {\n    position: relative;\n    width: 55px;\n    height: 55px;\n    border-radius: 50px;\n    overflow: hidden;\n  }\n  .menu-blogger-name {\n    margin: 4px 0 0 16px;\n    font-family: "Stelvio", sans-serif;\n    font-size: 24px;\n    line-height: 105%;\n    color: #161616;\n    font-weight: 600;\n  }\n  .menu-blogger-label {\n    margin: 4px 0 0 16px;\n    font-family: "Novela", serif;\n    font-style: italic;\n    font-size: 20px;\n    color: #888888;\n    font-weight: 400;\n  }\n  \n  @media ',' {\n    flex-direction: column;\n    width: 50%;\n    .menu-blogger-name {\n      margin: 16px 0 0 0;\n      font-family: "Stelvio", sans-serif;\n      font-size: 32px;\n      line-height: 105%;\n      color: #161616;\n      font-weight: 600;\n    }\n    .menu-blogger-label {\n      margin: 4px 0 0 0;\n    }\n  }\n']);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  \n  padding-left: var(--mobile-gap);\n  padding-right: var(--mobile-gap);\n  \n  @media "," {\n    padding-left: var(--tablet-gap);\n    padding-right: var(tablet-gap);\n  }\n  @media "," {\n    transform: none;\n    padding-left: 0;\n    padding-right: 0;\n    top: 0;\n    left: 0;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(['\n  ul {\n    display: table;\n    padding: 0;\n  }\n  li {\n    font-weight: 400;\n    position: relative;\n    font-family: "Stelvio", sans-serif;\n    font-size: 20px;\n    margin-top: 8px;\n    list-style: none;\n  }\n  //transform: translate3d(',");\n  transition: all 800ms cubic-bezier(0.58, 0, 0.29, 0.91);\n\n  @media ",' {\n    a {\n      position: relative;\n      height: 32px;\n      display: table-row;\n      line-height: 24px;\n      border-radius: 32px;\n      z-index: 900;\n      color: black;\n\n      &:hover {\n        color: white;\n\n        &:after {\n          content: "";\n          background-color: black;\n          position: absolute;\n          height: 100%;\n          width: calc(100% + 24px);\n          left: -12px;\n          top: -7px;\n          border-radius: 32px;\n          z-index: -1;\n        }\n      }\n    }\n  }\n']);return _templateObject3=function(){return n},n}function _templateObject4(){let n=(0,i._)(["\n  display: flex;\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  align-items: center;\n  justify-content: center;\n  height: 70px;\n  width: 70px;\n  color: black;\n  border-radius: 70px;\n  p {\n    padding-top: 12px;\n    margin: 16px 16px 0 0;\n    font-size: 32px;\n    transform-origin: center;\n    transform: rotate(",");\n    transition: all 450ms cubic-bezier(0.58, -0.42, 0.29, 0.91);\n  }\n  z-index: 2000;\n  @media "," {\n    display: none;\n  }\n"]);return _templateObject4=function(){return n},n}function _templateObject5(){let n=(0,i._)(["\n  font-weight: 500;\n  letter-spacing: 0.08em;\n  text-transform: uppercase;\n  color: #888888;\n"]);return _templateObject5=function(){return n},n}function RenderGroup(n){let{group:e,blogPath:t}=n;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h,{children:e.name}),(0,r.jsx)("ul",{children:e.items.map((n,e)=>(0,r.jsx)(RenderLink,{link:n,blogPath:t},e))})]})}function RenderLink(n){let{link:e,blogPath:t}=n;switch(e.type){case"external":return(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:e.value,target:"_blank",rel:"noreferrer noopener",children:e.label})});case"internal":return(0,r.jsx)("li",{children:(0,r.jsx)(a(),{href:e.value,children:e.label})});case"filter":return(0,r.jsx)("li",{children:(0,r.jsx)(a(),{href:{pathname:"/blog/".concat(t,"/filtres"),query:e.value},children:e.label})});default:return(0,r.jsx)(r.Fragment,{})}}function BlogMenu(n){let{data:e}=n,t=(0,l.DG)(e.menu),i=e.slug,[o,p]=(0,c.useState)(!1);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g,{isOpen:o,className:"blog-menu",children:(0,r.jsxs)(f,{isOpen:o,children:[(0,r.jsxs)(u,{children:[(0,r.jsx)("div",{className:"menu-blogger-picture",children:(0,r.jsx)(d.Z,{imageData:e.blogger.picture})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(a(),{href:"/blog/".concat(i),children:(0,r.jsx)("p",{className:"menu-blogger-name primary-hover",children:e.blogger.fullName})}),(0,r.jsx)("p",{className:"menu-blogger-label",children:"Blog"})]})]}),(0,r.jsxs)(m,{children:[(null==t?void 0:t.groups)&&t.groups.map((n,e)=>(0,r.jsx)(RenderGroup,{group:n,blogPath:i},e)),(null==t?void 0:t.singles.length)>0&&t.singles.map((n,e)=>(0,r.jsx)(RenderLink,{link:n,blogPath:i},e))]})]})}),(0,r.jsx)(b,{isOpen:o,onClick:()=>p(!o),children:(0,r.jsx)("p",{children:"→"})})]})}let g=p.ZP.div.withConfig({componentId:"sc-f4ce5f7f-0"})(_templateObject(),n=>n.isOpen?"236, 236, 236, 0.8":"236, 236, 236, 0.5",n=>n.isOpen?"25px":"15px",n=>n.isOpen?"0,0,0":"-100vw,70vh,0",s.U.desktop),u=p.ZP.div.withConfig({componentId:"sc-f4ce5f7f-1"})(_templateObject1(),s.U.desktop),f=p.ZP.div.withConfig({componentId:"sc-f4ce5f7f-2"})(_templateObject2(),s.U.tablet,s.U.desktop),m=p.ZP.div.withConfig({componentId:"sc-f4ce5f7f-3"})(_templateObject3(),n=>n.isOpen?"0,0,0":"0, -100px, 0",s.U.desktop),b=p.ZP.div.withConfig({componentId:"sc-f4ce5f7f-4"})(_templateObject4(),n=>n.isOpen?"-225deg":"-45deg",s.U.desktop),h=p.ZP.label.withConfig({componentId:"sc-f4ce5f7f-5"})(_templateObject5())},1452:function(n,e,t){"use strict";t.r(e),t.d(e,{__N_SSP:function(){return g},default:function(){return PageFiltres}});var i=t(2729),r=t(5893),l=t(1163),o=t(9487),a=t(4724),p=t(9521),c=t(3500),s=t(7421),d=t(7294);function _templateObject(){let n=(0,i._)(["\n  position: relative;\n  background-color: var(--soft-white);\n  @media "," {\n    .blog-menu {\n      \n    }\n  }\n"]);return _templateObject=function(){return n},n}function _templateObject1(){let n=(0,i._)(["\n  position: relative;\n  display: flex;\n  \n  @media "," {\n    margin-left: var(--border-space);\n    margin-right: var(--border-space);\n  }\n"]);return _templateObject1=function(){return n},n}function _templateObject2(){let n=(0,i._)(["\n  width: 100%;\n  margin-bottom: 64px;\n  padding-top: 40px;\n  padding-left: var(--mobile-gap);\n  padding-right: var(--mobile-gap);\n  .list-container {\n    padding: 0;\n    width: 100%;\n  }\n  .post-card-li {\n    list-style: none;\n    padding-right: 0;\n  }\n  @media "," {\n    padding-left: var(--tablet-gap);\n    padding-right: 0;\n  }\n  @media "," {\n    padding-left: var(--desktop-gap);\n    margin-bottom: 164px;\n  }\n"]);return _templateObject2=function(){return n},n}function _templateObject3(){let n=(0,i._)(["\n  position: relative;\n  height: 526px;\n"]);return _templateObject3=function(){return n},n}var g=!0;function PageFiltres(n){var e;let t;let{blog:i,fallback:p}=n,s=null==p?void 0:p.posts,g=(0,l.useRouter)(),{query:b}=g;(0,d.useEffect)(()=>{let n={};b.topic&&(n.topic=b.topic),1>+b.page?(n.page=1,g.replace({pathname:"/blog/".concat(i.slug,"/filtres"),query:n},void 0,{scroll:!1})):+b.page>(null==s?void 0:s.totalPages)&&(n.page=(null==s?void 0:s.totalPages)>0?s.totalPages:1,g.replace({pathname:"/blog/".concat(i.slug,"/filtres"),query:n},void 0,{scroll:!1}))},[i.slug,g.query.page]);let h={showLead:!0,showDate:!0,showAuthor:!0},x=b.topic;return(0,r.jsx)(u,{children:(0,r.jsxs)(f,{children:[(0,r.jsx)(o.Z,{data:i}),(0,r.jsxs)(m,{children:[(0,r.jsx)("h1",{children:x}),(0,r.jsx)("ul",{className:"list-container",children:null==s?void 0:null===(e=s.hits)||void 0===e?void 0:e.map((n,e)=>(0,r.jsx)("li",{className:"post-card-li",children:(0,r.jsx)(a.Z,{post:n,options:h})},"post-".concat(e)))}),(0,r.jsx)(c.Z,{nbHits:null==s?void 0:s.totalHits,baseUrl:(t=b.topic?"topic":b.type?"type":b.tag?"tag":null,"/blog/".concat(i.slug,"/filtres?").concat(t?t+"="+b[t]+"&":"","page=")),currentPage:null==s?void 0:s.page,options:{postPerPage:15}})]})]})})}let u=p.ZP.div.withConfig({componentId:"sc-615abf4e-0"})(_templateObject(),s.U.desktop),f=p.ZP.div.withConfig({componentId:"sc-615abf4e-1"})(_templateObject1(),s.U.desktop),m=p.ZP.div.withConfig({componentId:"sc-615abf4e-2"})(_templateObject2(),s.U.tablet,s.U.desktop);p.ZP.div.withConfig({componentId:"sc-615abf4e-3"})(_templateObject3())}},function(n){n.O(0,[755,291,749,774,888,179],function(){return n(n.s=3678)}),_N_E=n.O()}]);