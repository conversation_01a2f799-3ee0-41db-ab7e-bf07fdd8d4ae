import dayjs from "dayjs";
import "dayjs/locale/fr";

dayjs.locale("fr");

function dateForHumans(date) {
  // const template = dayjs(date).year() !== dayjs().year() ? "DD MMM YYYY" : "DD MMM";
  const template = "DD MMM YYYY";
  return dayjs(date).locale("fr").format(template)
}

function hour(date) {
  return dayjs(date).locale("fr").format("HH:mm");
}

function dateDiffInDays(a, b) {
  const _MS_PER_DAY = 1000 * 60 * 60 * 24;
  // Discard the time and time-zone information.
  const utc1 = toUTC(a);
  const utc2 = toUTC(b);

  return Math.floor(Math.abs((utc2 - utc1)) / _MS_PER_DAY);
}

function toUTC(date) {
  return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(),
    date.getUTCDate(), date.getUTCHours(),
    date.getUTCMinutes(), date.getUTCSeconds());
}

export {
  dateForHumans,
  dateDiffInDays,
  hour
}
