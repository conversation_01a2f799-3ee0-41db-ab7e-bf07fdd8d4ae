"use strict";exports.id=2924,exports.ids=[2924],exports.modules={2924:(t,i,e)=>{e.d(i,{Z:()=>HorizontalPostCard});var a=e(997),s=e(6382),o=e(1664),r=e.n(o),n=e(7518),l=e.n(n),d=e(2558),p=e(7467),c=e(6453),h=e(1077),x=e(7199),m=e(8672),g=e(6035),w=e(5675),f=e.n(w),u=e(2570);function HorizontalPostCard({post:t,options:i}){let e=!(0,g.a)({mediaQuery:p.U.tablet}),o="",n="",l=(0,h.qt)(t),w=(0,x.tm)(l);return i?.showDate&&(o+=t.date?(0,s.S$)(t.date):(0,s.S$)(t.published_at)),i?.showAuthor&&t?.author&&(o+=(""!==o?" - ":"")+(t.author?.fullName?t.author.fullName:t.author)),i?.showLead&&(n=(0,h.mj)(t)),(0,a.jsxs)(j,{children:[(0,a.jsxs)("div",{className:"post-info",children:[a.jsx(c.My,{children:o}),(0,a.jsxs)(u.Z,{link:l,children:[(0,a.jsxs)(c.kz,{className:"primary-hover",children:[t.title,w&&a.jsx("span",{children:a.jsx(f(),{src:"/images/icons/external.svg",alt:"external",fill:!0})})]}),!e&&n&&a.jsx(c.X0,{children:n})]}),i.showTopics&&a.jsx("div",{className:"card-topics",children:t.topics&&t.topics.map((t,i)=>a.jsx(m.Z,{text:t},i))})]}),a.jsx(r(),{href:l,passHref:!0,className:"post-image-container",children:a.jsx("div",{className:"post-image",children:a.jsx(d.Z,{imageData:t.image})})})]})}let j=l().li.withConfig({componentId:"sc-c8777780-0"})`
  list-style: none;
  position: relative;
  width: 100%;

  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  &:after {
    content: "";
    display: block;
    width: 100vw;
    margin: 24px 0;
    background-color: #40444444;
    height: 1px;
  }

  .post-info {
    width: 65%;
    padding-right: 10px;
  }
  .post-image-container{
    width: 30%;
  }
  .post-image {
    width: 100%;
    aspect-ratio: 1/1;
    position: relative;
  }
  .card-topics{
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: start;
    justify-content: start;
    flex-wrap: wrap;
    margin-top: 16px;
    height: 32px;
    width: 100%;
    overflow: hidden;
    gap: 8px;
  }
  @media screen and (max-width: 320px) {
    // Little screen only
    .post-info {
      width: 100%;
    }
  }
  @media ${p.U.tablet} {
    &:after {
      content: "";
      display: block;
      width: 100%;
      margin: 37px 0 30px 0;

      background-color: #40444444;
      height: 1px;
    }
    .post-info {
      width: 75%;
      padding-right: 10px;
    }
    .post-image-container{
      width: 20%;
    }
  }
`}};