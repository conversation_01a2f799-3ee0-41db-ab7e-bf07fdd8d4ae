import styled from "styled-components";
import Link from "next/link";
import { useContext } from "react";
import { HeaderContext } from "context/HeaderContext";
import { device } from "styles/device";
import { dynamicSort } from "../../../../utils/list.utils";


export default function Blogs({ title, data }) {

  const { toggleMenu } = useContext(HeaderContext)

  return(
    <Wrapper aria-label={title}>
      { data.sort(dynamicSort("lastName")).map((item, key) =>
        <li key={key}>
          <Link href={item.route}>
            <p onClick={() => toggleMenu()}>
              {item.name}
            </p>
          </Link>
        </li>
      )}
    </Wrapper>
  )
}

const Wrapper = styled.ul`
  grid-row: 2;
  grid-column: 1/3;
  margin-bottom: 64px;
  padding: 0;

  :before {
    display: block;
    position: relative;
    content: attr(aria-label);
    font-family: "Switzer", serif;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 24px;
    color: var(--c-cream-A80);
  }

  li {
    list-style: none;
    margin-top: -1px;
    border-top: 1px solid #1C2E33;
    border-bottom: 1px solid #1C2E33;
  }

  p {
    font-family: "Switzer", serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    letter-spacing: 0.5px;
    color: var(--c-cream);

    &:before {
      margin-right: 24px;
      left: 0;
      top: 0;
      content: "";
      display: inline-block;
      width: 14px;
      height: 14px;
      background-color: var(--c-cream-A20);

      border-radius: 14px;
    }

    &:hover {
      cursor: pointer;

      &:before {
        background-color: #fa7050;
      }
    }
  }

  @media ${device.desktop} {
    width: 110%;
    li {
      width: 110%;
    }
  }
`