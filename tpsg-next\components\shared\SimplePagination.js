import Link from "next/link";
import { useRouter } from "next/router";
import styled from "styled-components";
import { removeUrlParams } from "utils/query.utils";


const PageLink = ({ path, query, page, children, shallow }) => {
  return (
    <Link
      href={{
        pathname: path,
        query: { ...query, page: page },
      }}
      passHref
      shallow={shallow}>
      {children}
    </Link> )
}

function getPaginationItems(path, query, currentPage, pageCount, shallow) {
  let items = []
  const start = Math.ceil(currentPage - currentPage % 5) || 1
  for (let page = start; page < start + 5; page++) {
    if (page <= pageCount) {
      items.push(
        <PageLink key={page} path={path} query={query} page={page} shallow={shallow}>
          <PaginationNumberButton active={page === currentPage}>{page}</PaginationNumberButton>
        </PageLink>
      )
    }
  }
  return items
}


export default function SimplePagination({ pageCount, noParam, shallow = false }) {

  const router = useRouter();
  if (pageCount <= 1) return <></>;

  let path = removeUrlParams(router.asPath)
  let { query } = router;

  if (noParam) delete query[noParam]
  let currentPage = router.query.page ? +router.query.page : 1

  return (
    <Wrapper>
      <br/>
      <PageLink path={path} query={query} page={currentPage - 1} shallow={shallow}>
        <PaginationTextButton disabled={currentPage <= 1}>Prev</PaginationTextButton>
      </PageLink>

      {getPaginationItems(path, query, currentPage, pageCount, shallow)}

      <PageLink path={path} query={query} page={currentPage + 1} shallow={shallow}>
        <PaginationTextButton disabled={currentPage >= pageCount}>Next</PaginationTextButton>
      </PageLink>

      {currentPage !== pageCount &&
        <PageLink path={path} query={query} page={pageCount} shallow={shallow}>
          <PaginationTextButton>Last</PaginationTextButton>
        </PageLink>
      }

    </Wrapper>
  )
}

const Wrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16px;
`

const PaginationTextButton = styled.button`
  padding: 8px;
  border-radius: 4px;
  margin: 4px;
  border: none;
  color: white;
  background-color: ${p => !p.disabled ? "#426bf3" : "#9AAEEEFF"};

  &:hover {
    cursor: ${p => !p.disabled ? "pointer" : "default"};
    background-color: ${p => !p.disabled ? "#426bf3" : "#9AAEEEFF"};
  }
`

const PaginationNumberButton = styled.button`
  border-radius: 4px;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
  border: none;
  color: ${p => p.active ? "white" : "black"};
  font-size: 18px;

  background-color: ${p => p.active ? "#426bf3" : "transparent"};

  &:hover {
    cursor: ${p => p.active ? "default" : "pointer"};
    background-color: ${p => p.active ? "#426bf3" : "transparent"};
  }

`;
