exports.id=9755,exports.ids=[9755],exports.modules={2732:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return Image}});let r=i(167),n=i(8760),a=n._(i(6689)),l=r._(i(6561)),o=i(5764),d=i(6730),s=i(6218);i(8565);let u=i(464);function normalizeSrc(e){return"/"===e[0]?e.slice(1):e}let c={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1},g=new Set,f="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";globalThis.__NEXT_IMAGE_IMPORTED=!0;let m=new Map([["default",function(e){let{config:t,src:i,width:r,quality:n}=e;return i.endsWith(".svg")&&!t.dangerouslyAllowSVG?i:(0,u.normalizePathTrailingSlash)(t.path)+"?url="+encodeURIComponent(i)+"&w="+r+"&q="+(n||75)}],["imgix",function(e){let{config:t,src:i,width:r,quality:n}=e,a=new URL(""+t.path+normalizeSrc(i)),l=a.searchParams;return l.set("auto",l.getAll("auto").join(",")||"format"),l.set("fit",l.get("fit")||"max"),l.set("w",l.get("w")||r.toString()),n&&l.set("q",n.toString()),a.href}],["cloudinary",function(e){let{config:t,src:i,width:r,quality:n}=e,a=["f_auto","c_limit","w_"+r,"q_"+(n||"auto")].join(",")+"/";return""+t.path+a+normalizeSrc(i)}],["akamai",function(e){let{config:t,src:i,width:r}=e;return""+t.path+normalizeSrc(i)+"?imwidth="+r}],["custom",function(e){let{src:t}=e;throw Error('Image with src "'+t+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}]]);function isStaticRequire(e){return void 0!==e.default}function generateImgAttrs(e){let{config:t,src:i,unoptimized:r,layout:n,width:a,quality:l,sizes:o,loader:d}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:s,kind:u}=function(e,t,i,r){let{deviceSizes:n,allSizes:a}=e;if(r&&("fill"===i||"responsive"===i)){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);i)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:a.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:a,kind:"w"}}if("number"!=typeof t||"fill"===i||"responsive"===i)return{widths:n,kind:"w"};let l=[...new Set([t,2*t].map(e=>a.find(t=>t>=e)||a[a.length-1]))];return{widths:l,kind:"x"}}(t,a,n,o),c=s.length-1;return{sizes:o||"w"!==u?o:"100vw",srcSet:s.map((e,r)=>d({config:t,src:i,quality:l,width:e})+" "+("w"===u?e:r+1)+u).join(", "),src:d({config:t,src:i,quality:l,width:s[c]})}}function getInt(e){return"number"==typeof e?e:"string"==typeof e?parseInt(e,10):void 0}function defaultImageLoader(e){var t;let i=(null==(t=e.config)?void 0:t.loader)||"default",r=m.get(i);if(r)return r(e);throw Error('Unknown "loader" found in "next.config.js". Expected: '+o.VALID_LOADERS.join(", ")+". Received: "+i)}function handleLoading(e,t,i,r,n,a){if(!e||e.src===f||e["data-loaded-src"]===t)return;e["data-loaded-src"]=t;let l="decode"in e?e.decode():Promise.resolve();l.catch(()=>{}).then(()=>{if(e.parentNode&&(g.add(t),"blur"===r&&a(!0),null==n?void 0:n.current)){let{naturalWidth:t,naturalHeight:i}=e;n.current({naturalWidth:t,naturalHeight:i})}})}let ImageElement=e=>{let{imgAttributes:t,heightInt:i,widthInt:r,qualityInt:n,layout:l,className:o,imgStyle:d,blurStyle:s,isLazy:u,placeholder:c,loading:g,srcString:f,config:m,unoptimized:h,loader:p,onLoadingCompleteRef:b,setBlurComplete:y,setIntersection:w,onLoad:v,onError:A,isVisible:S,noscriptSizes:x,...E}=e;return g=u?"lazy":g,a.default.createElement(a.default.Fragment,null,a.default.createElement("img",{...E,...t,decoding:"async","data-nimg":l,className:o,style:{...d,...s},ref:(0,a.useCallback)(e=>{w(e),(null==e?void 0:e.complete)&&handleLoading(e,f,l,c,b,y)},[w,f,l,c,b,y]),onLoad:e=>{let t=e.currentTarget;handleLoading(t,f,l,c,b,y),v&&v(e)},onError:e=>{"blur"===c&&y(!0),A&&A(e)}}),(u||"blur"===c)&&a.default.createElement("noscript",null,a.default.createElement("img",{...E,loading:g,decoding:"async","data-nimg":l,style:d,className:o,...generateImgAttrs({config:m,src:f,unoptimized:h,layout:l,width:r,quality:n,sizes:x,loader:p})})))};function Image(e){var t;let i,{src:r,sizes:n,unoptimized:u=!1,priority:g=!1,loading:m,lazyRoot:h=null,lazyBoundary:p,className:b,quality:y,width:w,height:v,style:A,objectFit:S,objectPosition:x,onLoadingComplete:E,placeholder:I="empty",blurDataURL:z,...k}=e,_=(0,a.useContext)(s.ImageConfigContext),R=(0,a.useMemo)(()=>{let e=c||_||o.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),i=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:i}},[_]),j=n?"responsive":"intrinsic";"layout"in k&&(k.layout&&(j=k.layout),delete k.layout);let O=defaultImageLoader;if("loader"in k){if(k.loader){let e=k.loader;O=t=>{let{config:i,...r}=t;return e(r)}}delete k.loader}let L="";if("object"==typeof(t=r)&&(isStaticRequire(t)||void 0!==t.src)){let e=isStaticRequire(r)?r.default:r;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(z=z||e.blurDataURL,L=e.src,(!j||"fill"!==j)&&(v=v||e.height,w=w||e.width,!e.height||!e.width))throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e))}let P=!g&&("lazy"===m||void 0===m);((r="string"==typeof r?r:L).startsWith("data:")||r.startsWith("blob:"))&&(u=!0,P=!1),R.unoptimized&&(u=!0);let[M,q]=(0,a.useState)(!1),[N,W,C]=(0,d.useIntersection)({rootRef:h,rootMargin:p||"200px",disabled:!P}),D=!P||W,T={boxSizing:"border-box",display:"block",overflow:"hidden",width:"initial",height:"initial",background:"none",opacity:1,border:0,margin:0,padding:0},B={boxSizing:"border-box",display:"block",width:"initial",height:"initial",background:"none",opacity:1,border:0,margin:0,padding:0},G=!1,U=getInt(w),H=getInt(v),V=getInt(y),F=Object.assign({},A,{position:"absolute",top:0,left:0,bottom:0,right:0,boxSizing:"border-box",padding:0,border:"none",margin:"auto",display:"block",width:0,height:0,minWidth:"100%",maxWidth:"100%",minHeight:"100%",maxHeight:"100%",objectFit:S,objectPosition:x}),J="blur"!==I||M?{}:{backgroundSize:S||"cover",backgroundPosition:x||"0% 0%",filter:"blur(20px)",backgroundImage:'url("'+z+'")'};if("fill"===j)T.display="block",T.position="absolute",T.top=0,T.left=0,T.bottom=0,T.right=0;else if(void 0!==U&&void 0!==H){let e=H/U,t=isNaN(e)?"100%":""+100*e+"%";"responsive"===j?(T.display="block",T.position="relative",G=!0,B.paddingTop=t):"intrinsic"===j?(T.display="inline-block",T.position="relative",T.maxWidth="100%",G=!0,B.maxWidth="100%",i="data:image/svg+xml,%3csvg%20xmlns=%27http://www.w3.org/2000/svg%27%20version=%271.1%27%20width=%27"+U+"%27%20height=%27"+H+"%27/%3e"):"fixed"===j&&(T.display="inline-block",T.position="relative",T.width=U,T.height=H)}let Q={src:f,srcSet:void 0,sizes:void 0};D&&(Q=generateImgAttrs({config:R,src:r,unoptimized:u,layout:j,width:U,quality:V,sizes:n,loader:O}));let X=r,K={imageSrcSet:Q.srcSet,imageSizes:Q.sizes,crossOrigin:k.crossOrigin,referrerPolicy:k.referrerPolicy},Y=a.default.useEffect,Z=(0,a.useRef)(E),$=(0,a.useRef)(r);(0,a.useEffect)(()=>{Z.current=E},[E]),Y(()=>{$.current!==r&&(C(),$.current=r)},[C,r]);let ee={isLazy:P,imgAttributes:Q,heightInt:H,widthInt:U,qualityInt:V,layout:j,className:b,imgStyle:F,blurStyle:J,loading:m,config:R,unoptimized:u,placeholder:I,loader:O,srcString:X,onLoadingCompleteRef:Z,setBlurComplete:q,setIntersection:N,isVisible:D,noscriptSizes:n,...k};return a.default.createElement(a.default.Fragment,null,a.default.createElement("span",{style:T},G?a.default.createElement("span",{style:B},i?a.default.createElement("img",{style:{display:"block",maxWidth:"100%",width:"initial",height:"initial",background:"none",opacity:1,border:0,margin:0,padding:0},alt:"","aria-hidden":!0,src:i}):null):null,a.default.createElement(ImageElement,ee)),g?a.default.createElement(l.default,null,a.default.createElement("link",{key:"__nimg-"+Q.src+Q.srcSet+Q.sizes,rel:"preload",as:"image",href:Q.srcSet?void 0:Q.src,...K})):null)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9755:(e,t,i)=>{e.exports=i(2732)}};