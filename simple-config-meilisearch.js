/**
 * Script simple pour configurer Meilisearch v0.27.0
 * Ce script configure uniquement les attributs essentiels pour que Meilisearch fonctionne
 */

const fetch = require('node-fetch');

// Configuration
const MEILI_URL = 'http://127.0.0.1:7700';
const INDEX_NAME = 'post';

async function configMeilisearch() {
  console.log('Configuration simple de Meilisearch v0.27.0...');

  try {
    // 1. Configurer les attributs filtrables
    console.log('Configuration des attributs filtrables...');
    const filterableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/filterable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify([
        'title', 'topics', 'tags', 'author', 'type', 'blog', 'cs', 'series'
      ])
    });
    console.log(`Attributs filtrables configurés: ${filterableResponse.status}`);

    // 2. Configurer les attributs triables
    console.log('Configuration des attributs triables...');
    const sortableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/sortable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['date'])
    });
    console.log(`Attributs triables configurés: ${sortableResponse.status}`);

    // 3. Configurer les attributs de recherche
    console.log('Configuration des attributs de recherche...');
    const searchableResponse = await fetch(`${MEILI_URL}/indexes/${INDEX_NAME}/settings/searchable-attributes`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(['title', 'body', 'lead'])
    });
    console.log(`Attributs de recherche configurés: ${searchableResponse.status}`);

    console.log('\nConfiguration terminée !');

  } catch (error) {
    console.error('Erreur lors de la configuration :', error);
  }
}

configMeilisearch();
