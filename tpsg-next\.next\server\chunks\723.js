"use strict";exports.id=723,exports.ids=[723],exports.modules={723:(t,e,a)=>{a.d(e,{V:()=>o});var i=a(9997);let s=new i.MeiliSearch({host:"http://127.0.0.1:7700",apiKey:""}),r=s.index("post"),instantSearch=async t=>await r.search(t,{attributesToCrop:["body"],cropLength:200,limit:5}),search=async(t="",e)=>await r.search(t,{attributesToRetrieve:["title","author","type","image","route","slug","date","cs","lead"],attributesToHighlight:["title"],...e}),searchHighlight=async(t="",e)=>await r.search(t,{attributesToRetrieve:["title","author","type","image","route","slug","date","body","lead","topics","cs"],attributesToHighlight:["title","body","lead"],attributesToCrop:["body","lead"],cropLength:100,...e}),urls=async()=>await r.getDocuments({attributesToRetrieve:["route","date"],limit:9999}),o={instantSearch,search,searchHighlight,urls}}};