{"name": "tpsg-strapi", "private": true, "version": "0.1.0", "description": "Installation Strapi pour le site toutpoursagloire.com", "telemetryDisabled": true, "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"axios": "^1.8.4", "knex": "0.21.18", "markdown-it": "^14.1.0", "meilisearch": "^0.27.0", "mysql": "^2.18.1", "node-fetch": "^2.7.0", "pg": "8.5.1", "rich-markdown-editor": "git+https://github.com/Darvey/rich-markdown-editor", "sharp": "^0.30.7", "showdown": "^2.1.0", "strapi": "3.6.8", "strapi-admin": "3.6.8", "strapi-connector-bookshelf": "3.6.8", "strapi-plugin-content-manager": "3.6.8", "strapi-plugin-content-type-builder": "3.6.8", "strapi-plugin-email": "3.6.8", "strapi-plugin-graphql": "^3.6.8", "strapi-plugin-i18n": "3.6.8", "strapi-plugin-upload": "3.6.8", "strapi-plugin-users-permissions": "3.6.8", "strapi-provider-upload-aws-s3-enhanced": "^3.5.5", "strapi-utils": "3.6.8", "turndown": "^7.1.1"}, "author": {"name": "<PERSON>"}, "strapi": {"uuid": "aaf6efbf-ea36-4569-8611-6fe1263ce7c4"}, "engines": {"node": ">=10.16.0 <=14.x.x", "npm": "^6.0.0"}, "license": "MIT"}