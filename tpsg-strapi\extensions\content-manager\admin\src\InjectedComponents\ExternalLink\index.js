import React from "react";
import styled from "styled-components";
import { useContentManagerEditViewDataManager } from "strapi-helper-plugin";
import EyeIcon from "./view.svg";


const StyledExternalLink = styled.a`
  display: block;
  color: #333740;
  width: 100%;
  text-decoration: none;

  span,
  i,
  svg {
    color: #333740;
    width: 13px;
    height: 12px;
    margin-right: 10px;
    vertical-align: 0;
  }

  span {
    font-size: 13px;
  }

  i {
    display: inline-block;
    background-image: url(${EyeIcon});
    background-size: contain;
  }

  &:hover {
    text-decoration: none;

    span,
    i,
    svg {
      color: #007eff;
    }
  }
`;

const ExternalLink = () => {


  const {modifiedData, layout} = useContentManagerEditViewDataManager();

  if (layout.apiID !== "post") {
    return null;
  }

  if (!modifiedData.slug) {
    return null;
  }

  if (!front_env.CLIENT_URL || !front_env.CLIENT_PREVIEW_SECRET) {
    return null;
  }

  let linkState = {};


  if(modifiedData.published_at ) {
    linkState.label = 'Voir sur le site';
    let route = getRoute(modifiedData);

    // Aucune route valide n'a pu être reconstituée.
    if(!route) {
      return null
    }

    linkState.url = `${front_env.CLIENT_URL}/${route}`;

  } else {
    linkState = {
      url: `${front_env.CLIENT_URL}/preview?secret=${front_env.CLIENT_PREVIEW_SECRET}&slug=${modifiedData.slug}`,
      label: 'Preview'
    }
  }

  return (
    <li>
      <StyledExternalLink
        href={linkState.url}
        target="_blank"
        rel="noopener noreferrer"
        title="page preview">
        <i/>
        {linkState.label}
      </StyledExternalLink>
    </li>
  );
};

function getRoute(post) {
  if(post.type === 'article') {
    return `/article/${post.slug}`
  }
  if(post.type === 'webinaire') {
    return `/webinaires/${post.slug}`
  }
  if(post.type === 'podcast') {
    let module = post.modules.find(function (module) {
      return module.__component === "module.podcast";
    });
    if(module.podcast?.slug) {
      return `/podcasts/${module.podcast.slug}/${post.slug}`
    }
  }
  return null
};

export default ExternalLink;
