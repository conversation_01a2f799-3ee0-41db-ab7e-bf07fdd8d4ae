"use strict";exports.id=3646,exports.ids=[3646],exports.modules={3646:(e,i,t)=>{t.a(e,async(e,o)=>{try{t.d(i,{Z:()=>AuthorBox});var r=t(997),a=t(7518),n=t.n(a),l=t(3135),s=t(1871),d=t(2558),x=t(7467);t(1077),t(1664);var h=e([l,s]);function AuthorBox({author:e,blog:i}){if(!e)return null;e.about=e.about?.replace(/\\/g,"")||"";let t=i?`/blog/${i.blogger?.slug}`:`/recherche?author=${e.fullName}`,o=`${e.firstName||""} ${e.lastName||""}`.trim();return(0,r.jsxs)(p,{className:"author-box",children:[r.jsx(c,{children:r.jsx("a",{href:t,children:r.jsx(d.Z,{imageData:e.picture})})}),(0,r.jsxs)(u,{children:[r.jsx("p",{className:"abox-about__name",children:r.jsx("a",{href:t,children:o})}),r.jsx(l.default,{rehypePlugins:[s.default],children:e.about})]})]})}[l,s]=h.then?(await h)():h;let p=n().div.withConfig({componentId:"sc-9bb0274f-0"})`
  position: relative;
  display: flex;
  flex-direction: column;
  border-top: 1px solid rgba(0, 0, 0, 0.4);
  padding-top: 24px;
  
  @media ${x.U.tablet} {
    flex-direction: row;
    padding: 32px 0 0 0;
  }
`,c=n().div.withConfig({componentId:"sc-9bb0274f-1"})`
  position: relative;
  flex-shrink: 0;
  margin: 0 32px 12px 0;
  height: 60px;
  width: 60px;
  border-radius: 60px;
  overflow: hidden;
  background-color: white;
`,u=n().div.withConfig({componentId:"sc-9bb0274f-2"})`
  .abox-about__name {
    font-size: 26px;
    font-weight: 600;
  }
  p {
    font-size: 22px;
    line-height: 28px;
    font-weight: 400;
    margin: 12px 0 0 0;
  }
`;o()}catch(e){o(e)}})}};