import Layout from "/components/layout"
import "../styles/globals.css"
import Head from "next/head";
import Script from "next/script";
import { CookiesProvider, useCookies } from "react-cookie";
import { useRouter } from "next/router";

function MyApp({ Component, pageProps }) {
  const getParams = (query, paramsToInclude) => {
    let params = paramsToInclude.reduce((params, param) => {
      // don"t include empty params or page=1
      if (query[param] && query[param] !== "" && (param === "page" && Number(query.page) !== 1)) {
        params.push(`${param}=${query[param]}`)
      }
      return params
    }, [])
    return params.length ? `?${params.join("&")}` : ""
  }

  const createCanonicalUrl = () => {
    const { pathname, query } = useRouter()

    let url = ""
    switch (pathname) {
    // article
    case "/article/[article]":
      url = `/article/${query.article}`
      break

    // blog
    case "/blog/[blog]":
      url = `/blog/${query.blog}`
      break

    case "/blog/[blog]/filtres":
      url = `/blog/${query.blog}/filtres${getParams(query, ["topic", "type", "page"])}`
      break

    // categories
    case "/categories":
      url = "/categories"
      break

    case "/categories/[topic]":
      url = `/categories/${query.topic}`
      break

    case "/categories/[topic]/ressources":
      url = `/categories/${query.topic}/ressources${getParams(query, ["page"])}`
      break

    case "/categories/ministere/[ministry]":
      url = `/categories/ministere/${query.ministry}`
      break

    case "/categories/ministere/[ministry]/ressources":
      url = `/categories/ministere/${query.ministry}/ressources${getParams(query, ["page"])}`
      break

    case "/categories/vocation/[vocation]":
      url = `/categories/vocation/${query.vocation}`
      break

    case "/categories/vocation/[vocation]/ressources":
      url = `/categories/vocation/${query.vocation}/ressources${getParams(query, ["page"])}`
      break

    // formation
    case "/formations":
      url = "/formations"
      break

    case "/formations/[formation]":
      url = `/formations/${query.formation}`
      break

    // parcours email
    case "/parcours-emails":
      url = "/parcours-emails"
      break

    case "/parcours-emails/[parcours]":
      url = `/parcours-emails/${query.parcours}`
      break

    // podcasts
    case "/podcasts":
      url = "/podcasts"
      break

    case "/podcasts/[podcast]":
      url = `/podcasts/${query.podcast}${getParams(query, ["page"])}`
      break

    case "/podcasts/[podcast]/[episode]":
      url = `/podcasts/${query.podcast}/${query.episode}`
      break

    // webinaires
    case "/webinaires":
      url = `/webinaires${getParams(query, ["page"])}`
      break

    case "/webinaires/[episode]":
      url = `/webinaires/${query.episode}${getParams(query, ["page"])}`
      break

    case "/[page]":
      url = `/${query.page}`
      break

    // default
    default:
      url = ""
    }

    return new URL(url, "https://toutpoursagloire.com").href // or use process.env.NEXT_PUBLIC_URL?
  }

  return (
    <CookiesProvider>
      <Layout>
        <Head>
          <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0" />
          <link rel="canonical" href={createCanonicalUrl()} />
          <link rel="icon" href="/favicon.png" />
        </Head>
        <Component {...pageProps} />
        {CookieScripts()}
      </Layout>
    </CookiesProvider>
  )
}

function CookieScripts() {

  let gaTagId = process.env.NEXT_PUBLIC_GATAG_ID;

  const [cookie, setCookie] = useCookies(["preferences"]);

  return (
    <>
      {cookie["preferences"]?.analytics &&
        <>
          <Script async src={`https://www.googletagmanager.com/gtag/js?id=${gaTagId}`} id={"ga-url-script"}>
          </Script >
          <Script id={"ga-analytics-script"}>
            {`window.dataLayer = window.dataLayer || [];
              function gtag(){ dataLayer.push(arguments); }
              gtag("js", new Date());
              gtag("config", "${gaTagId}");
            `}
          </Script>
        </>
      }
      {
        cookie["preferences"]?.medias &&
        <>
          <Script id={"marker-io-config"}>
            {
              `window.markerConfig = {
                project: "647f03e6a572cb7307800759",
                source: "snippet" };`
            }
          </Script>
          {/*<Script id={"marker-io-function"}>*/}
          {/*  {"!function(e,r,a){if(!e.__Marker){e.__Marker={};var t=[],n={__cs:t};[\"show\",\"hide\",\"isVisible\",\"capture\",\"cancelCapture\",\"unload\",\"reload\",\"isExtensionInstalled\",\"setReporter\",\"setCustomData\",\"on\",\"off\"].forEach(function(e){n[e]=function(){var r=Array.prototype.slice.call(arguments);r.unshift(e),t.push(r)}}),e.Marker=n;var s=r.createElement(\"script\");s.async=1,s.src=\"https://edge.marker.io/latest/shim.js\";var i=r.getElementsByTagName(\"script\")[0];i.parentNode.insertBefore(s,i)}}(window,document);"}*/}
          {/*</Script>*/}
        </>
      }
    </>
  )
}

function GoogleAnalytics() {

  let gaTagId = process.env.NEXT_PUBLIC_GATAG_ID;

  const [cookie, setCookie] = useCookies(["preferences"]);

  if (!cookie["preferences"]?.analytics) {
    return;
  }

  return (
    <>
      <Script async src={`https://www.googletagmanager.com/gtag/js?id=${gaTagId}`} id={"ga-url-script"}>
      </Script>
      <Script id={"ga-analytics-script"}>
        {`window.dataLayer = window.dataLayer || [];
        function gtag(){ dataLayer.push(arguments); }
        gtag("js", new Date());
        gtag("config", "${gaTagId}");
         `}
      </Script>
    </>
  )
}


export default MyApp

// <script async src="https://www.googletagmanager.com/gtag/js?id=${gaTagId}%22%3E"/>
//         <script>
//           window.dataLayer = window.dataLayer || [];
//           function gtag(){dataLayer.push(arguments);}
//           gtag("js", new Date());
//           gtag("config", "${gaTagId}");
//         </script>
