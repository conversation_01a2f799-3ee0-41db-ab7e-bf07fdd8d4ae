exports.id=3169,exports.ids=[3169],exports.modules={769:t=>{t.exports={"soft-white":"ButtonLink_soft-white__ep2DH","glow-white":"ButtonLink_glow-white__46aUW","soft-dark":"ButtonLink_soft-dark__NLNNQ","glow-dark":"ButtonLink_glow-dark__p2EZ7","rounded-glow-white":"ButtonLink_rounded-glow-white__XCQ_D ButtonLink_glow-white__46aUW","rounded-soft-white":"ButtonLink_rounded-soft-white__W4BQi ButtonLink_soft-white__ep2DH","rounded-glow-dark":"ButtonLink_rounded-glow-dark__D1hGV ButtonLink_glow-dark__p2EZ7","rounded-soft-dark":"ButtonLink_rounded-soft-dark__6gKMz ButtonLink_soft-dark__NLNNQ","orange-register":"ButtonLink_orange-register__aIov2"}},3169:(t,o,n)=>{"use strict";n.d(o,{Z:()=>ButtonLink});var e=n(997),r=n(1664),i=n.n(r),_=n(7518),d=n.n(_),s=n(7199),a=n(769),u=n.n(a);function ButtonLink({text:t,url:o,type:n}){return(0,s.tm)(o)?e.jsx(k,{href:o,className:`button-link ${u()[n]}`,target:"_blank",rel:"noopener noreferrer",children:t}):e.jsx(i(),{href:o,children:e.jsx(k,{className:`button-link ${u()[n]}`,children:t})})}let k=d().a.withConfig({componentId:"sc-e62ceebf-0"})`
  margin: 0 16px 0 0;
  padding: 12px 18px 6px 18px;
  font-family: Stelvio,sans-serif;
  font-size: 18px;
  white-space: nowrap;
  cursor: pointer;
  
  &:hover {
    opacity: 0.72;
  }
`}};